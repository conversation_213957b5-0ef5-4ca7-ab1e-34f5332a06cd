{"version": 2, "buildCommand": "npm run build", "outputDirectory": "build", "installCommand": "npm install", "framework": "create-react-app", "routes": [{"src": "/static/(.*)", "dest": "/static/$1"}, {"src": "/(.*\\.(js|css|ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot))", "dest": "/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}]}