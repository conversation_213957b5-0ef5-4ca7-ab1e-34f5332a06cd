.dashboard {
  padding: 2rem;
}

.dashboard-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  margin-top: 2rem;
}

.user-info,
.quick-actions {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.quick-actions button {
  padding: 1rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s;
}

.quick-actions button:hover {
  background-color: #0056b3;
}

h1 {
  color: #333;
  margin-bottom: 1.5rem;
}

h2 {
  color: #444;
  margin-bottom: 1rem;
}

.dashboard-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 280px;
  background-color: #E34234; /* India Post brand color */
  color: white;
  padding: 2rem;
}

.company-logo {
  width: 120px;
  height: auto;
  margin-bottom: 2rem;
  display: block;
}

.profile-section {
  text-align: center;
  margin-bottom: 2rem;
}

.avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  margin: 1rem 0;
  border: 3px solid white;
}

.nav-menu {
  margin-top: 2rem;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  color: white;
  text-decoration: none;
  margin-bottom: 0.5rem;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.nav-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.nav-item svg {
  margin-right: 1rem;
}

.main-content {
  flex: 1;
  padding: 30px;
  background-color: #f5f5f5;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin: 30px 0;
}

.stat-card {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-card.primary {
  background-color: #1B3A53;
  color: white;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  margin: 10px 0 0;
}

.charts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.chart-box {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.chart-box:hover {
  transform: translateY(-5px);
}

.nav-item.logout {
  margin-top: auto;
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-item.logout:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.progress-circle {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  background-color: #1B3A53;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px auto;
}

.percentage {
  color: white;
  font-size: 2rem;
  font-weight: bold;
}

.nav-item.back {
  margin-bottom: 1rem;
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-item.back:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}