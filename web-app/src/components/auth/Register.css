.register-container {
  max-width: 500px;
  margin: 4rem auto;
  padding: 2.5rem;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.register-container h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #1a1a1a;
  font-size: 2rem;
  font-weight: 600;
}

.error-message {
  color: #dc3545;
  background-color: #fff5f5;
  border: 1px solid #dc3545;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  text-align: center;
  font-size: 0.9rem;
}

.register-form .form-group {
  margin-bottom: 1.5rem;
}

.register-form label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #4a5568;
  font-size: 0.95rem;
}

.register-form input[type="text"],
.register-form input[type="email"],
.register-form input[type="password"],
.register-form select {
  width: 100%;
  padding: 0.875rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  box-sizing: border-box;
  font-size: 1rem;
  transition: all 0.2s ease;
  background-color: #f8fafc;
}

.register-form input:focus,
.register-form select:focus {
  border-color: #4299e1;
  background-color: white;
  outline: none;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
}

.submit-button,
.google-button {
  width: 100%;
  padding: 1rem;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.1s ease, background-color 0.2s ease;
}

.submit-button:active,
.google-button:active {
  transform: scale(0.98);
}

.submit-button {
  background-color: #4299e1;
  margin-top: 1.5rem;
}

.submit-button:hover {
  background-color: #3182ce;
}

.google-button {
  background-color: #dc3545;
  margin-top: 1rem;
}

.google-button:hover {
  background-color: #c82333;
}

.login-link-container {
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.login-link {
  color: #4299e1;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.login-link:hover {
  color: #3182ce;
  text-decoration: underline;
}


.logo-container {
  text-align: center;
  margin-bottom: 2rem;
}

.logo-container img {
  max-width: 150px;
  height: auto;
}

.input-error {
  color: #dc3545;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.form-group.error input {
  border-color: #dc3545;
  background-color: #fff8f8;
}

.form-group.error input:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.15);
}