{"version": 3, "file": "masking.cjs", "sources": ["utils.js", "maskDefinition.js", "maskFragment.js", "maskOperation.js"], "sourcesContent": ["import { Slot } from \"optimism\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport { canUseWeakMap, canUseWeakSet } from \"../utilities/index.js\";\nexport var MapImpl = canUseWeakMap ? WeakMap : Map;\nexport var SetImpl = canUseWeakSet ? WeakSet : Set;\n// Contextual slot that allows us to disable accessor warnings on fields when in\n// migrate mode.\n/** @internal */\nexport var disableWarningsSlot = new Slot();\nvar issuedWarning = false;\nexport function warnOnImproperCacheImplementation() {\n    if (!issuedWarning) {\n        issuedWarning = true;\n        globalThis.__DEV__ !== false && invariant.warn(52);\n    }\n}\n//# sourceMappingURL=utils.js.map", "import { Kind } from \"graphql\";\nimport { getFragmentMaskMode, maybeDeep<PERSON>reeze, resultKeyNameFromField, } from \"../utilities/index.js\";\nimport { disableWarningsSlot } from \"./utils.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\nexport function maskDefinition(data, selectionSet, context) {\n    return disableWarningsSlot.withValue(true, function () {\n        var masked = maskSelectionSet(data, selectionSet, context, false);\n        if (Object.isFrozen(data)) {\n            maybeDeepFreeze(masked);\n        }\n        return masked;\n    });\n}\nfunction getMutableTarget(data, mutableTargets) {\n    if (mutableTargets.has(data)) {\n        return mutableTargets.get(data);\n    }\n    var mutableTarget = Array.isArray(data) ? [] : Object.create(null);\n    mutableTargets.set(data, mutableTarget);\n    return mutableTarget;\n}\nfunction maskSelectionSet(data, selectionSet, context, migration, path) {\n    var _a;\n    var knownChanged = context.knownChanged;\n    var memo = getMutableTarget(data, context.mutableTargets);\n    if (Array.isArray(data)) {\n        for (var _i = 0, _b = Array.from(data.entries()); _i < _b.length; _i++) {\n            var _c = _b[_i], index = _c[0], item = _c[1];\n            if (item === null) {\n                memo[index] = null;\n                continue;\n            }\n            var masked = maskSelectionSet(item, selectionSet, context, migration, globalThis.__DEV__ !== false ? \"\".concat(path || \"\", \"[\").concat(index, \"]\") : void 0);\n            if (knownChanged.has(masked)) {\n                knownChanged.add(memo);\n            }\n            memo[index] = masked;\n        }\n        return knownChanged.has(memo) ? memo : data;\n    }\n    for (var _d = 0, _e = selectionSet.selections; _d < _e.length; _d++) {\n        var selection = _e[_d];\n        var value = void 0;\n        // we later want to add acessor warnings to the final result\n        // so we need a new object to add the accessor warning to\n        if (migration) {\n            knownChanged.add(memo);\n        }\n        if (selection.kind === Kind.FIELD) {\n            var keyName = resultKeyNameFromField(selection);\n            var childSelectionSet = selection.selectionSet;\n            value = memo[keyName] || data[keyName];\n            if (value === void 0) {\n                continue;\n            }\n            if (childSelectionSet && value !== null) {\n                var masked = maskSelectionSet(data[keyName], childSelectionSet, context, migration, globalThis.__DEV__ !== false ? \"\".concat(path || \"\", \".\").concat(keyName) : void 0);\n                if (knownChanged.has(masked)) {\n                    value = masked;\n                }\n            }\n            if (!(globalThis.__DEV__ !== false)) {\n                memo[keyName] = value;\n            }\n            if (globalThis.__DEV__ !== false) {\n                if (migration &&\n                    keyName !== \"__typename\" &&\n                    // either the field is not present in the memo object\n                    // or it has a `get` descriptor, not a `value` descriptor\n                    // => it is a warning accessor and we can overwrite it\n                    // with another accessor\n                    !((_a = Object.getOwnPropertyDescriptor(memo, keyName)) === null || _a === void 0 ? void 0 : _a.value)) {\n                    Object.defineProperty(memo, keyName, getAccessorWarningDescriptor(keyName, value, path || \"\", context.operationName, context.operationType));\n                }\n                else {\n                    delete memo[keyName];\n                    memo[keyName] = value;\n                }\n            }\n        }\n        if (selection.kind === Kind.INLINE_FRAGMENT &&\n            (!selection.typeCondition ||\n                context.cache.fragmentMatches(selection, data.__typename))) {\n            value = maskSelectionSet(data, selection.selectionSet, context, migration, path);\n        }\n        if (selection.kind === Kind.FRAGMENT_SPREAD) {\n            var fragmentName = selection.name.value;\n            var fragment = context.fragmentMap[fragmentName] ||\n                (context.fragmentMap[fragmentName] =\n                    context.cache.lookupFragment(fragmentName));\n            invariant(fragment, 47, fragmentName);\n            var mode = getFragmentMaskMode(selection);\n            if (mode !== \"mask\") {\n                value = maskSelectionSet(data, fragment.selectionSet, context, mode === \"migrate\", path);\n            }\n        }\n        if (knownChanged.has(value)) {\n            knownChanged.add(memo);\n        }\n    }\n    if (\"__typename\" in data && !(\"__typename\" in memo)) {\n        memo.__typename = data.__typename;\n    }\n    // This check prevents cases where masked fields may accidentally be\n    // returned as part of this object when the fragment also selects\n    // additional fields from the same child selection.\n    if (Object.keys(memo).length !== Object.keys(data).length) {\n        knownChanged.add(memo);\n    }\n    return knownChanged.has(memo) ? memo : data;\n}\nfunction getAccessorWarningDescriptor(fieldName, value, path, operationName, operationType) {\n    var getValue = function () {\n        if (disableWarningsSlot.getValue()) {\n            return value;\n        }\n        globalThis.__DEV__ !== false && invariant.warn(48, operationName ?\n            \"\".concat(operationType, \" '\").concat(operationName, \"'\")\n            : \"anonymous \".concat(operationType), \"\".concat(path, \".\").concat(fieldName).replace(/^\\./, \"\"));\n        getValue = function () { return value; };\n        return value;\n    };\n    return {\n        get: function () {\n            return getValue();\n        },\n        set: function (newValue) {\n            getValue = function () { return newValue; };\n        },\n        enumerable: true,\n        configurable: true,\n    };\n}\n//# sourceMappingURL=maskDefinition.js.map", "import { Kind } from \"graphql\";\nimport { MapImpl, SetImpl, warnOnImproperCacheImplementation, } from \"./utils.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport equal from \"@wry/equality\";\nimport { maskDefinition } from \"./maskDefinition.js\";\nimport { createFragmentMap, getFragmentDefinitions, } from \"../utilities/index.js\";\n/** @internal */\nexport function maskFragment(data, document, cache, fragmentName) {\n    if (!cache.fragmentMatches) {\n        if (globalThis.__DEV__ !== false) {\n            warnOnImproperCacheImplementation();\n        }\n        return data;\n    }\n    var fragments = document.definitions.filter(function (node) {\n        return node.kind === Kind.FRAGMENT_DEFINITION;\n    });\n    if (typeof fragmentName === \"undefined\") {\n        invariant(fragments.length === 1, 49, fragments.length);\n        fragmentName = fragments[0].name.value;\n    }\n    var fragment = fragments.find(function (fragment) { return fragment.name.value === fragmentName; });\n    invariant(!!fragment, 50, fragmentName);\n    if (data == null) {\n        // Maintain the original `null` or `undefined` value\n        return data;\n    }\n    if (equal(data, {})) {\n        // Return early and skip the masking algorithm if we don't have any data\n        // yet. This can happen when cache.diff returns an empty object which is\n        // used from watchFragment.\n        return data;\n    }\n    return maskDefinition(data, fragment.selectionSet, {\n        operationType: \"fragment\",\n        operationName: fragment.name.value,\n        fragmentMap: createFragmentMap(getFragmentDefinitions(document)),\n        cache: cache,\n        mutableTargets: new MapImpl(),\n        knownChanged: new SetImpl(),\n    });\n}\n//# sourceMappingURL=maskFragment.js.map", "import { invariant } from \"../utilities/globals/index.js\";\nimport { createFragmentMap, getFragmentDefinitions, getOperationDefinition, } from \"../utilities/index.js\";\nimport { maskDefinition } from \"./maskDefinition.js\";\nimport { MapImpl, SetImpl, warnOnImproperCacheImplementation, } from \"./utils.js\";\n/** @internal */\nexport function maskOperation(data, document, cache) {\n    var _a;\n    if (!cache.fragmentMatches) {\n        if (globalThis.__DEV__ !== false) {\n            warnOnImproperCacheImplementation();\n        }\n        return data;\n    }\n    var definition = getOperationDefinition(document);\n    invariant(definition, 51);\n    if (data == null) {\n        // Maintain the original `null` or `undefined` value\n        return data;\n    }\n    return maskDefinition(data, definition.selectionSet, {\n        operationType: definition.operation,\n        operationName: (_a = definition.name) === null || _a === void 0 ? void 0 : _a.value,\n        fragmentMap: createFragmentMap(getFragmentDefinitions(document)),\n        cache: cache,\n        mutableTargets: new MapImpl(),\n        knownChanged: new SetImpl(),\n    });\n}\n//# sourceMappingURL=maskOperation.js.map"], "names": ["canUseWeakMap", "canUseWeakSet", "Slot", "invariant", "maybeDeepFreeze", "Kind", "resultKeyNameFromField", "getFragmentMaskMode", "equal", "createFragmentMap", "getFragmentDefinitions", "getOperationDefinition"], "mappings": ";;;;;;;;;;;;;;AAGO,IAAI,OAAO,GAAGA,uBAAa,GAAG,OAAO,GAAG,GAAG,CAAC;AAC5C,IAAI,OAAO,GAAGC,uBAAa,GAAG,OAAO,GAAG,GAAG,CAAC;AAIzC,IAAC,mBAAmB,GAAG,IAAIC,aAAI,GAAG;AAC5C,IAAI,aAAa,GAAG,KAAK,CAAC;AACnB,SAAS,iCAAiC,GAAG;AACpD,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,QAAQ,aAAa,GAAG,IAAI,CAAC;AAC7B,QAAQ,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIC,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3D,KAAK;AACL;;ACXO,SAAS,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE;AAC5D,IAAI,OAAO,mBAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY;AAC3D,QAAQ,IAAI,MAAM,GAAG,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC1E,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACnC,YAAYC,yBAAe,CAAC,MAAM,CAAC,CAAC;AACpC,SAAS;AACT,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,CAAC,CAAC;AACP,CAAC;AACD,SAAS,gBAAgB,CAAC,IAAI,EAAE,cAAc,EAAE;AAChD,IAAI,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAClC,QAAQ,OAAO,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACvE,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AAC5C,IAAI,OAAO,aAAa,CAAC;AACzB,CAAC;AACD,SAAS,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE;AACxE,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;AAC5C,IAAI,IAAI,IAAI,GAAG,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;AAC9D,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC7B,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAChF,YAAY,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACzD,YAAY,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/B,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AACnC,gBAAgB,SAAS;AACzB,aAAa;AACb,YAAY,IAAI,MAAM,GAAG,gBAAgB,CAAC,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,OAAO,KAAK,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AACzK,YAAY,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AAC1C,gBAAgB,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACvC,aAAa;AACb,YAAY,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;AACjC,SAAS;AACT,QAAQ,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;AACpD,KAAK;AACL,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,YAAY,CAAC,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACzE,QAAQ,IAAI,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/B,QAAQ,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC;AAG3B,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACnC,SAAS;AACT,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAKC,YAAI,CAAC,KAAK,EAAE;AAC3C,YAAY,IAAI,OAAO,GAAGC,gCAAsB,CAAC,SAAS,CAAC,CAAC;AAC5D,YAAY,IAAI,iBAAiB,GAAG,SAAS,CAAC,YAAY,CAAC;AAC3D,YAAY,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AACnD,YAAY,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE;AAClC,gBAAgB,SAAS;AACzB,aAAa;AACb,YAAY,IAAI,iBAAiB,IAAI,KAAK,KAAK,IAAI,EAAE;AACrD,gBAAgB,IAAI,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,iBAAiB,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,OAAO,KAAK,KAAK,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AACxL,gBAAgB,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AAC9C,oBAAoB,KAAK,GAAG,MAAM,CAAC;AACnC,iBAAiB;AACjB,aAAa;AACb,YAAY,IAAI,EAAE,UAAU,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE;AACjD,gBAAgB,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AACtC,aAAa;AACb,YAAY,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAC9C,gBAAgB,IAAI,SAAS;AAC7B,oBAAoB,OAAO,KAAK,YAAY;AAK5C,oBAAoB,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,EAAE;AAC5H,oBAAoB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,4BAA4B,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC,aAAa,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;AACjK,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;AACzC,oBAAoB,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC1C,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAKD,YAAI,CAAC,eAAe;AACnD,aAAa,CAAC,SAAS,CAAC,aAAa;AACrC,gBAAgB,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE;AAC5E,YAAY,KAAK,GAAG,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AAC7F,SAAS;AACT,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAKA,YAAI,CAAC,eAAe,EAAE;AACrD,YAAY,IAAI,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;AACpD,YAAY,IAAI,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC;AAC5D,iBAAiB,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC;AAClD,oBAAoB,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,CAAC;AAChE,YAAYF,iBAAS,CAAC,QAAQ,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;AAClD,YAAY,IAAI,IAAI,GAAGI,6BAAmB,CAAC,SAAS,CAAC,CAAC;AACtD,YAAY,IAAI,IAAI,KAAK,MAAM,EAAE;AACjC,gBAAgB,KAAK,GAAG,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,KAAK,SAAS,EAAE,IAAI,CAAC,CAAC;AACzG,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACrC,YAAY,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACnC,SAAS;AACT,KAAK;AACL,IAAI,IAAI,YAAY,IAAI,IAAI,IAAI,EAAE,YAAY,IAAI,IAAI,CAAC,EAAE;AACzD,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAC1C,KAAK;AAIL,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;AAC/D,QAAQ,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/B,KAAK;AACL,IAAI,OAAO,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;AAChD,CAAC;AACD,SAAS,4BAA4B,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,EAAE,aAAa,EAAE;AAC5F,IAAI,IAAI,QAAQ,GAAG,YAAY;AAC/B,QAAQ,IAAI,mBAAmB,CAAC,QAAQ,EAAE,EAAE;AAC5C,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIJ,iBAAS,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa;AACxE,YAAY,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,aAAa,EAAE,GAAG,CAAC;AACrE,cAAc,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;AAC7G,QAAQ,QAAQ,GAAG,YAAY,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC;AACjD,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,OAAO;AACX,QAAQ,GAAG,EAAE,YAAY;AACzB,YAAY,OAAO,QAAQ,EAAE,CAAC;AAC9B,SAAS;AACT,QAAQ,GAAG,EAAE,UAAU,QAAQ,EAAE;AACjC,YAAY,QAAQ,GAAG,YAAY,EAAE,OAAO,QAAQ,CAAC,EAAE,CAAC;AACxD,SAAS;AACT,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,YAAY,EAAE,IAAI;AAC1B,KAAK,CAAC;AACN;;AC7HO,SAAS,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE;AAClE,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;AAChC,QAAQ,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAC1C,YAAY,iCAAiC,EAAE,CAAC;AAChD,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,IAAI,EAAE;AAChE,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAKE,YAAI,CAAC,mBAAmB,CAAC;AACtD,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AAC7C,QAAQF,iBAAS,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAChE,QAAQ,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AAC/C,KAAK;AACL,IAAI,IAAI,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC,EAAE,CAAC,CAAC;AACxG,IAAIA,iBAAS,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;AAC5C,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAEtB,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,IAAIK,cAAK,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;AAIzB,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,YAAY,EAAE;AACvD,QAAQ,aAAa,EAAE,UAAU;AACjC,QAAQ,aAAa,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;AAC1C,QAAQ,WAAW,EAAEC,2BAAiB,CAACC,gCAAsB,CAAC,QAAQ,CAAC,CAAC;AACxE,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,cAAc,EAAE,IAAI,OAAO,EAAE;AACrC,QAAQ,YAAY,EAAE,IAAI,OAAO,EAAE;AACnC,KAAK,CAAC,CAAC;AACP;;ACpCO,SAAS,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE;AACrD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE;AAChC,QAAQ,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAC1C,YAAY,iCAAiC,EAAE,CAAC;AAChD,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,UAAU,GAAGC,gCAAsB,CAAC,QAAQ,CAAC,CAAC;AACtD,IAAIR,iBAAS,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AAC9B,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;AAEtB,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,EAAE;AACzD,QAAQ,aAAa,EAAE,UAAU,CAAC,SAAS;AAC3C,QAAQ,aAAa,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK;AAC3F,QAAQ,WAAW,EAAEM,2BAAiB,CAACC,gCAAsB,CAAC,QAAQ,CAAC,CAAC;AACxE,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,cAAc,EAAE,IAAI,OAAO,EAAE;AACrC,QAAQ,YAAY,EAAE,IAAI,OAAO,EAAE;AACnC,KAAK,CAAC,CAAC;AACP;;;;;;"}