{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/masking/internal/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type {\n  Prettify,\n  Primitive,\n  RemoveIndexSignature,\n} from \"../../utilities/index.js\";\n\nexport type IsAny<T> = 0 extends 1 & T ? true : false;\n\nexport type UnwrapFragmentRefs<TData> =\n  true extends IsAny<TData> ? TData\n  : TData extends any ?\n    // Ensure tagged/branded types are left alone (i.e. type UUID = string & { ... })\n    TData extends Primitive ? TData\n    : // Leave TData alone if it is Record<string, any> and not a specific shape\n    string extends keyof TData ? TData\n    : // short-circuit on empty object\n    keyof TData extends never ? TData\n    : TData extends { \" $fragmentRefs\"?: infer FragmentRefs } ?\n      UnwrapFragmentRefs<\n        CombineIntersection<\n          | Omit<TData, \" $fragmentRefs\">\n          | RemoveFragmentName<\n              NonNullable<\n                NonNullable<FragmentRefs>[keyof NonNullable<FragmentRefs>]\n              >\n            >\n        >\n      >\n    : TData extends object ?\n      {\n        [K in keyof TData]: UnwrapFragmentRefs<TData[K]>;\n      }\n    : TData\n  : never;\n\n/**\n```ts\nCombineIntersection<\n  | { foo: string }\n  | { __typename: \"A\"; a: string }\n  | { __typename: \"B\"; b1: number }\n  | { __typename: \"B\"; b2: string }\n> =>\n  | { foo: string }\n  | CombineByTypeName<\n    | { __typename: \"A\"; a: string }\n    | { __typename: \"B\"; b1: number }\n    | { __typename: \"B\"; b2: string }\n  >\n```\n */\ntype CombineIntersection<T> =\n  | Exclude<T, { __typename?: string }>\n  | CombineByTypeName<Extract<T, { __typename?: string }>>;\n/**\n```ts\n  CombineByTypeName<\n    | { __typename: \"A\"; a: string }\n    | { __typename: \"B\"; b1: number }\n    | { __typename: \"B\"; b2: string }\n  > =>\n  | CombineWithArrays<\n    | { __typename: \"A\"; a: string }\n  >\n  | CombineWithArrays<\n    | { __typename: \"B\"; b1: number }\n    | { __typename: \"B\"; b2: number }\n  >\n```\n */\ntype CombineByTypeName<T extends { __typename?: string }> = {\n  [TypeName in NonNullable<T[\"__typename\"]>]: Prettify<\n    MergeUnions<ExtractByMatchingTypeNames<T, TypeName>>\n  >;\n}[NonNullable<T[\"__typename\"]>];\n\n/**\n```ts\nCombineByTypeName<\n  | {\n      __typename: \"Person\" | \"Animatronic\" | \"CartoonCharacter\";\n      id: number;\n      name: string;\n    }\n  | {\n      __typename: \"Person\";\n      birthdate: string;\n    }\n  | {\n      __typename: \"Animatronic\";\n      manufacturer: string;\n      warrantyEndDate: string;\n    }\n  | {\n      __typename: \"CartoonCharacter\";\n      animator: string;\n      voiceActor: string;\n    }\n>\n    =>\n{\n    id: number;\n    name: string;\n    __typename: \"Person\";\n    birthdate: string;\n} | {\n    id: number;\n    name: string;\n    __typename: \"Animatronic\";\n    manufacturer: string;\n    warrantyEndDate: string;\n} | {\n    id: number;\n    name: string;\n    __typename: \"CartoonCharacter\";\n    animator: string;\n    voiceActor: string;\n}\n```\n */\ntype ExtractByMatchingTypeNames<\n  Union extends { __typename?: string },\n  TypeName extends string,\n> = Union extends any ?\n  TypeName extends NonNullable<Union[\"__typename\"]> ?\n    Omit<Union, \"__typename\"> & {\n      // preserve `?`, which `& { __typename: TypeName }` would not do\n      [K in keyof Union as K extends \"__typename\" ? K : never]: TypeName;\n    }\n  : never\n: never;\n\ntype MergeUnions<TUnion> = MergeUnionsAcc<\n  TUnion,\n  takeOneFromUnion<TUnion>,\n  never\n>;\n\ntype DistributedRequiredExclude<T, U> =\n  T extends any ?\n    Required<T> extends Required<U> ?\n      Required<U> extends Required<T> ?\n        never\n      : T\n    : T\n  : T;\n\ntype MergeUnionsAcc<TUnion, Curr, Merged> =\n  [Curr] extends [never] ? Merged\n  : MergeUnionsAcc<\n      DistributedRequiredExclude<TUnion, Curr>,\n      takeOneFromUnion<DistributedRequiredExclude<TUnion, Curr>>,\n      [Merged] extends [never] ? Curr : MergeObjects<Curr, Merged>\n    >;\ntype unionToIntersection<T> =\n  (T extends unknown ? (x: T) => unknown : never) extends (\n    (x: infer U) => unknown\n  ) ?\n    U\n  : never;\n\ntype takeOneFromUnion<T> =\n  unionToIntersection<T extends T ? (x: T) => 0 : never> extends (\n    (x: infer U) => 0\n  ) ?\n    U\n  : never;\n\ntype MergeObjects<T, U> = Prettify<\n  {\n    [k in keyof T]: k extends keyof U ?\n      [NonNullable<T[k]>, NonNullable<U[k]>] extends (\n        [infer TK extends object, infer UK extends object]\n      ) ?\n        TK extends unknown[] ?\n          UK extends unknown[] ?\n            | CombineIntersection<TK[number] | UK[number]>[]\n            | Extract<T[k] | U[k], undefined | null>\n          : T[k]\n        : CombineIntersection<TK | UK> | Extract<T[k] | U[k], undefined | null>\n      : T[k]\n    : T[k];\n  } & Pick<U, Exclude<keyof U, keyof T>>\n>;\n\nexport type RemoveMaskedMarker<T> = Omit<T, \"__masked\">;\n// force distrubution when T is a union with | undefined\nexport type RemoveFragmentName<T> =\n  T extends any ? Omit<T, \" $fragmentName\"> : T;\n\ntype Exact<in out T> = (x: T) => T;\nexport type ContainsFragmentsRefs<TData, Seen = never> = true extends (\n  IsAny<TData>\n) ?\n  false\n: TData extends object ?\n  Exact<TData> extends Seen ? false\n  : \" $fragmentRefs\" extends keyof RemoveIndexSignature<TData> ? true\n  : ContainsFragmentsRefs<TData[keyof TData], Seen | Exact<TData>>\n: false;\n"]}