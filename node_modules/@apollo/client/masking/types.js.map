{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/masking/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\nimport type {\n  ContainsFragmentsRefs,\n  IsAny,\n  RemoveFragmentName,\n  RemoveMaskedMarker,\n  UnwrapFragmentRefs,\n} from \"./internal/types.js\";\nimport type { Prettify } from \"../utilities/index.js\";\n\nexport interface DataMasking {}\n\n/**\n * Marks a type as masked. This is used by `MaybeMasked` when determining\n * whether to use the masked or unmasked type.\n */\nexport type Masked<TData> = TData & {\n  __masked?: true;\n};\n\n/**\n * Marks a type as masked. This is a shortcut for\n * `TypedDocumentNode<Masked<TData>, TVariables>`\n */\nexport type MaskedDocumentNode<\n  TData = { [key: string]: any },\n  TVariables = { [key: string]: any },\n> = TypedDocumentNode<Masked<TData>, TVariables>;\n\nexport type FragmentType<TData> =\n  [TData] extends [{ \" $fragmentName\"?: infer TKey }] ?\n    TKey extends string ?\n      { \" $fragmentRefs\"?: { [key in TKey]: TData } }\n    : never\n  : never;\n\n/**\n * Returns TData as either masked or unmasked depending on whether masking is\n * enabled.\n */\nexport type MaybeMasked<TData> =\n  DataMasking extends { mode: \"unmask\" } ?\n    // distribute TData - in case of a union, do the next steps for each member\n    TData extends any ?\n      // prevent \"Type instantiation is excessively deep and possibly infinite.\"\n      true extends IsAny<TData> ? TData\n      : TData extends { __masked?: true } ? Prettify<RemoveMaskedMarker<TData>>\n      : Unmasked<TData>\n    : never\n  : DataMasking extends { mode: \"preserveTypes\" } ? TData\n  : TData;\n\n/**\n * Unmasks a type to provide its full result.\n */\nexport type Unmasked<TData> =\n  true extends IsAny<TData> ? TData\n  : TData extends object ?\n    true extends ContainsFragmentsRefs<TData> ?\n      UnwrapFragmentRefs<RemoveMaskedMarker<RemoveFragmentName<TData>>>\n    : TData\n  : TData;\n"]}