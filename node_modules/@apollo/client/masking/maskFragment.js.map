{"version": 3, "file": "maskFragment.js", "sourceRoot": "", "sources": ["../../src/masking/maskFragment.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAO/B,OAAO,EACL,OAAO,EACP,OAAO,EACP,iCAAiC,GAClC,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,KAAK,MAAM,eAAe,CAAC;AAClC,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EACL,iBAAiB,EACjB,sBAAsB,GACvB,MAAM,uBAAuB,CAAC;AAE/B,gBAAgB;AAChB,MAAM,UAAU,YAAY,CAC1B,IAAW,EACX,QAAiD,EACjD,KAA2B,EAC3B,YAAqB;IAErB,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;QAC3B,IAAI,OAAO,EAAE,CAAC;YACZ,iCAAiC,EAAE,CAAC;QACtC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAC3C,UAAC,IAAI;QACH,OAAA,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,mBAAmB;IAAtC,CAAsC,CACzC,CAAC;IAEF,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;QACxC,SAAS,CACP,SAAS,CAAC,MAAM,KAAK,CAAC,EACtB,2FAA6F,EAC7F,SAAS,CAAC,MAAM,CACjB,CAAC;QACF,YAAY,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IACzC,CAAC;IAED,IAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAC7B,UAAC,QAAQ,IAAK,OAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY,EAApC,CAAoC,CACnD,CAAC;IAEF,SAAS,CACP,CAAC,CAAC,QAAQ,EACV,2CAAyC,EACzC,YAAY,CACb,CAAC;IAEF,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,oDAAoD;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC;QACpB,wEAAwE;QACxE,wEAAwE;QACxE,2BAA2B;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,YAAY,EAAE;QACjD,aAAa,EAAE,UAAU;QACzB,aAAa,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK;QAClC,WAAW,EAAE,iBAAiB,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAChE,KAAK,OAAA;QACL,cAAc,EAAE,IAAI,OAAO,EAAE;QAC7B,YAAY,EAAE,IAAI,OAAO,EAAE;KAC5B,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { Kind } from \"graphql\";\nimport type { FragmentDefinitionNode } from \"graphql\";\nimport type {\n  ApolloCache,\n  DocumentNode,\n  TypedDocumentNode,\n} from \"../core/index.js\";\nimport {\n  MapImpl,\n  SetImpl,\n  warnOnImproperCacheImplementation,\n} from \"./utils.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport equal from \"@wry/equality\";\nimport { maskDefinition } from \"./maskDefinition.js\";\nimport {\n  createFragmentMap,\n  getFragmentDefinitions,\n} from \"../utilities/index.js\";\n\n/** @internal */\nexport function maskFragment<TData = unknown>(\n  data: TData,\n  document: TypedDocumentNode<TData> | DocumentNode,\n  cache: ApolloCache<unknown>,\n  fragmentName?: string\n): TData {\n  if (!cache.fragmentMatches) {\n    if (__DEV__) {\n      warnOnImproperCacheImplementation();\n    }\n\n    return data;\n  }\n\n  const fragments = document.definitions.filter(\n    (node): node is FragmentDefinitionNode =>\n      node.kind === Kind.FRAGMENT_DEFINITION\n  );\n\n  if (typeof fragmentName === \"undefined\") {\n    invariant(\n      fragments.length === 1,\n      `Found %s fragments. \\`fragmentName\\` must be provided when there is not exactly 1 fragment.`,\n      fragments.length\n    );\n    fragmentName = fragments[0].name.value;\n  }\n\n  const fragment = fragments.find(\n    (fragment) => fragment.name.value === fragmentName\n  );\n\n  invariant(\n    !!fragment,\n    `Could not find fragment with name \"%s\".`,\n    fragmentName\n  );\n\n  if (data == null) {\n    // Maintain the original `null` or `undefined` value\n    return data;\n  }\n\n  if (equal(data, {})) {\n    // Return early and skip the masking algorithm if we don't have any data\n    // yet. This can happen when cache.diff returns an empty object which is\n    // used from watchFragment.\n    return data;\n  }\n\n  return maskDefinition(data, fragment.selectionSet, {\n    operationType: \"fragment\",\n    operationName: fragment.name.value,\n    fragmentMap: createFragmentMap(getFragmentDefinitions(document)),\n    cache,\n    mutableTargets: new MapImpl(),\n    knownChanged: new SetImpl(),\n  });\n}\n"]}