{"version": 3, "file": "maskOperation.js", "sourceRoot": "", "sources": ["../../src/masking/maskOperation.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAC1D,OAAO,EACL,iBAAiB,EACjB,sBAAsB,EACtB,sBAAsB,GACvB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EACL,OAAO,EACP,OAAO,EACP,iCAAiC,GAClC,MAAM,YAAY,CAAC;AAEpB,gBAAgB;AAChB,MAAM,UAAU,aAAa,CAC3B,IAAW,EACX,QAAiD,EACjD,KAA2B;;IAE3B,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,CAAC;QAC3B,IAAI,OAAO,EAAE,CAAC;YACZ,iCAAiC,EAAE,CAAC;QACtC,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAM,UAAU,GAAG,sBAAsB,CAAC,QAAQ,CAAC,CAAC;IAEpD,SAAS,CACP,UAAU,EACV,6EAA6E,CAC9E,CAAC;IAEF,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,oDAAoD;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,YAAY,EAAE;QACnD,aAAa,EAAE,UAAU,CAAC,SAAS;QACnC,aAAa,EAAE,MAAA,UAAU,CAAC,IAAI,0CAAE,KAAK;QACrC,WAAW,EAAE,iBAAiB,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAChE,KAAK,OAAA;QACL,cAAc,EAAE,IAAI,OAAO,EAAE;QAC7B,YAAY,EAAE,IAAI,OAAO,EAAE;KAC5B,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import type {\n  ApolloCache,\n  DocumentNode,\n  TypedDocumentNode,\n} from \"../core/index.js\";\nimport { invariant } from \"../utilities/globals/index.js\";\nimport {\n  createFragmentMap,\n  getFragmentDefinitions,\n  getOperationDefinition,\n} from \"../utilities/index.js\";\nimport { maskDefinition } from \"./maskDefinition.js\";\nimport {\n  MapImpl,\n  SetImpl,\n  warnOnImproperCacheImplementation,\n} from \"./utils.js\";\n\n/** @internal */\nexport function maskOperation<TData = unknown>(\n  data: TData,\n  document: DocumentNode | TypedDocumentNode<TData>,\n  cache: ApolloCache<unknown>\n): TData {\n  if (!cache.fragmentMatches) {\n    if (__DEV__) {\n      warnOnImproperCacheImplementation();\n    }\n\n    return data;\n  }\n\n  const definition = getOperationDefinition(document);\n\n  invariant(\n    definition,\n    \"Expected a parsed GraphQL document with a query, mutation, or subscription.\"\n  );\n\n  if (data == null) {\n    // Maintain the original `null` or `undefined` value\n    return data;\n  }\n\n  return maskDefinition(data, definition.selectionSet, {\n    operationType: definition.operation,\n    operationName: definition.name?.value,\n    fragmentMap: createFragmentMap(getFragmentDefinitions(document)),\n    cache,\n    mutableTargets: new MapImpl(),\n    knownChanged: new SetImpl(),\n  });\n}\n"]}