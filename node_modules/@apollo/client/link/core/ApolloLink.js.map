{"version": 3, "file": "ApolloLink.js", "sourceRoot": "", "sources": ["../../../src/link/core/ApolloLink.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAGhF,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAQtD,OAAO,EACL,iBAAiB,EACjB,eAAe,EACf,kBAAkB,GACnB,MAAM,mBAAmB,CAAC;AAE3B,SAAS,WAAW,CAAC,EAAa,EAAE,OAAiB;IACnD,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,EAAE,EAAE,CAA4B,CAAC;AAC9E,CAAC;AAED,SAAS,MAAM,CAAC,OAAoC;IAClD,OAAO,OAAO,OAAO,KAAK,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AAC3E,CAAC;AAED,SAAS,aAAa,CAAC,IAAgB;IACrC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,CAAC;AAClC,CAAC;AAED;IAoFE,oBAAY,OAAwB;QAClC,IAAI,OAAO;YAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACtC,CAAC;IArFa,gBAAK,GAAnB;QACE,OAAO,IAAI,UAAU,CAAC,cAAM,OAAA,UAAU,CAAC,EAAE,EAAE,EAAf,CAAe,CAAC,CAAC;IAC/C,CAAC;IAEa,eAAI,GAAlB,UAAmB,KAAsC;QACvD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,UAAU,CAAC,KAAK,EAAE,CAAC;QAClD,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAX,CAAW,CAAe,CAAC;IACvE,CAAC;IAEa,gBAAK,GAAnB,UACE,IAAgC,EAChC,IAAiC,EACjC,KAAmC;QAEnC,IAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;QAC9B,IAAM,SAAS,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC;QAE/D,IAAI,GAAe,CAAC;QACpB,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;YACxD,GAAG,GAAG,IAAI,UAAU,CAAC,UAAC,SAAS;gBAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;oBACpB,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE;oBAChD,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,GAAG,GAAG,IAAI,UAAU,CAAC,UAAC,SAAS,EAAE,OAAO;gBACtC,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;oBACpB,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE;oBACzD,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,CAAC;YAC/D,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;IAClE,CAAC;IAEa,kBAAO,GAArB,UACE,IAAgB,EAChB,SAAyB;QAEzB,OAAO,CACL,IAAI,CAAC,OAAO,CACV,eAAe,CACb,SAAS,CAAC,OAAO,EACjB,kBAAkB,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,CACjD,CACF,IAAI,UAAU,CAAC,EAAE,EAAE,CACrB,CAAC;IACJ,CAAC;IAEa,iBAAM,GAApB,UACE,KAAkC,EAClC,MAAmC;QAEnC,IAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7B,SAAS,CAAC,IAAI,CACZ,4EAA4E,EAC5E,SAAS,CACV,CAAC;YACF,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;QAEhC,IAAI,GAAe,CAAC;QACpB,IAAI,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,GAAG,GAAG,IAAI,UAAU,CAClB,UAAC,SAAS;gBACR,OAAA,SAAS,CAAC,OAAO,CACf,SAAS,EACT,UAAC,EAAE,IAAK,OAAA,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,EAAvC,CAAuC,CAChD,IAAI,UAAU,CAAC,EAAE,EAAE;YAHpB,CAGoB,CACvB,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,GAAG,GAAG,IAAI,UAAU,CAAC,UAAC,SAAS,EAAE,OAAO;gBACtC,OAAO,CACL,SAAS,CAAC,OAAO,CAAC,SAAS,EAAE,UAAC,EAAE;oBAC9B,OAAO,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,CAAC;gBAC1D,CAAC,CAAC,IAAI,UAAU,CAAC,EAAE,EAAE,CACtB,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;IAClE,CAAC;IAMM,0BAAK,GAAZ,UACE,IAAgC,EAChC,IAAiC,EACjC,KAAmC;QAEnC,OAAO,IAAI,CAAC,MAAM,CAChB,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC,CACnE,CAAC;IACJ,CAAC;IAEM,2BAAM,GAAb,UAAc,IAAiC;QAC7C,OAAO,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IAEM,4BAAO,GAAd,UACE,SAAoB,EACpB,OAAkB;QAElB,MAAM,iBAAiB,CAAC,4BAA4B,CAAC,CAAC;IACxD,CAAC;IAES,4BAAO,GAAjB,UACE,KAAU,EACV,QAAgC;QAEhC,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC/B,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACtB,oEAAoE;YACpE,oEAAoE;YACpE,kEAAkE;YAClE,kEAAkE;YAClE,8DAA8D;YAC9D,+BAA+B;YAC/B,OAAO,KAAK,CAAC;QACf,CAAC;QACD,iDAAiD;QACjD,MAAM,KAAK,CAAC;IACd,CAAC;IAEM,+BAAU,GAAjB,UAAkB,EAAyB;QACzC,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAkBH,iBAAC;AAAD,CAAC,AApJD,IAoJC", "sourcesContent": ["import { newInvariantError, invariant } from \"../../utilities/globals/index.js\";\n\nimport type { Observer } from \"../../utilities/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport type {\n  NextLink,\n  Operation,\n  RequestHandler,\n  FetchResult,\n  GraphQLRequest,\n} from \"./types.js\";\nimport {\n  validateOperation,\n  createOperation,\n  transformOperation,\n} from \"../utils/index.js\";\n\nfunction passthrough(op: Operation, forward: NextLink) {\n  return (forward ? forward(op) : Observable.of()) as Observable<FetchResult>;\n}\n\nfunction toLink(handler: RequestHandler | ApolloLink) {\n  return typeof handler === \"function\" ? new ApolloLink(handler) : handler;\n}\n\nfunction isTerminating(link: ApolloLink): boolean {\n  return link.request.length <= 1;\n}\n\nexport class ApolloLink {\n  public static empty(): ApolloLink {\n    return new ApolloLink(() => Observable.of());\n  }\n\n  public static from(links: (ApolloLink | RequestHandler)[]): ApolloLink {\n    if (links.length === 0) return ApolloLink.empty();\n    return links.map(toLink).reduce((x, y) => x.concat(y)) as ApolloLink;\n  }\n\n  public static split(\n    test: (op: Operation) => boolean,\n    left: ApolloLink | RequestHandler,\n    right?: ApolloLink | RequestHandler\n  ): ApolloLink {\n    const leftLink = toLink(left);\n    const rightLink = toLink(right || new ApolloLink(passthrough));\n\n    let ret: ApolloLink;\n    if (isTerminating(leftLink) && isTerminating(rightLink)) {\n      ret = new ApolloLink((operation) => {\n        return test(operation) ?\n            leftLink.request(operation) || Observable.of()\n          : rightLink.request(operation) || Observable.of();\n      });\n    } else {\n      ret = new ApolloLink((operation, forward) => {\n        return test(operation) ?\n            leftLink.request(operation, forward) || Observable.of()\n          : rightLink.request(operation, forward) || Observable.of();\n      });\n    }\n    return Object.assign(ret, { left: leftLink, right: rightLink });\n  }\n\n  public static execute(\n    link: ApolloLink,\n    operation: GraphQLRequest\n  ): Observable<FetchResult> {\n    return (\n      link.request(\n        createOperation(\n          operation.context,\n          transformOperation(validateOperation(operation))\n        )\n      ) || Observable.of()\n    );\n  }\n\n  public static concat(\n    first: ApolloLink | RequestHandler,\n    second: ApolloLink | RequestHandler\n  ) {\n    const firstLink = toLink(first);\n    if (isTerminating(firstLink)) {\n      invariant.warn(\n        `You are calling concat on a terminating link, which will have no effect %o`,\n        firstLink\n      );\n      return firstLink;\n    }\n    const nextLink = toLink(second);\n\n    let ret: ApolloLink;\n    if (isTerminating(nextLink)) {\n      ret = new ApolloLink(\n        (operation) =>\n          firstLink.request(\n            operation,\n            (op) => nextLink.request(op) || Observable.of()\n          ) || Observable.of()\n      );\n    } else {\n      ret = new ApolloLink((operation, forward) => {\n        return (\n          firstLink.request(operation, (op) => {\n            return nextLink.request(op, forward) || Observable.of();\n          }) || Observable.of()\n        );\n      });\n    }\n    return Object.assign(ret, { left: firstLink, right: nextLink });\n  }\n\n  constructor(request?: RequestHandler) {\n    if (request) this.request = request;\n  }\n\n  public split(\n    test: (op: Operation) => boolean,\n    left: ApolloLink | RequestHandler,\n    right?: ApolloLink | RequestHandler\n  ): ApolloLink {\n    return this.concat(\n      ApolloLink.split(test, left, right || new ApolloLink(passthrough))\n    );\n  }\n\n  public concat(next: ApolloLink | RequestHandler): ApolloLink {\n    return ApolloLink.concat(this, next);\n  }\n\n  public request(\n    operation: Operation,\n    forward?: NextLink\n  ): Observable<FetchResult> | null {\n    throw newInvariantError(\"request is not implemented\");\n  }\n\n  protected onError(\n    error: any,\n    observer?: Observer<FetchResult>\n  ): false | void {\n    if (observer && observer.error) {\n      observer.error(error);\n      // Returning false indicates that observer.error does not need to be\n      // called again, since it was already called (on the previous line).\n      // Calling observer.error again would not cause any real problems,\n      // since only the first call matters, but custom onError functions\n      // might have other reasons for wanting to prevent the default\n      // behavior by returning false.\n      return false;\n    }\n    // Throw errors will be passed to observer.error.\n    throw error;\n  }\n\n  public setOnError(fn: ApolloLink[\"onError\"]): this {\n    this.onError = fn;\n    return this;\n  }\n\n  /**\n   * @internal\n   * Used to iterate through all links that are concatenations or `split` links.\n   */\n  readonly left?: ApolloLink;\n  /**\n   * @internal\n   * Used to iterate through all links that are concatenations or `split` links.\n   */\n  readonly right?: ApolloLink;\n\n  /**\n   * @internal\n   * Can be provided by a link that has an internal cache to report it's memory details.\n   */\n  getMemoryInternals?: () => unknown;\n}\n"]}