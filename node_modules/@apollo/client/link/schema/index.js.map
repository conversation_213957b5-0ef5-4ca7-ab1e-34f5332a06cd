{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/link/schema/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAG5C,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAgCtD;IAAgC,8BAAU;IAMxC,oBAAY,OAA2B;QACrC,YAAA,MAAK,WAAE,SAAC;QACR,KAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,KAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACnC,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,KAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;;IACrC,CAAC;IAEM,4BAAO,GAAd,UAAe,SAAoB;QAAnC,iBAsCC;QArCC,OAAO,IAAI,UAAU,CAAc,UAAC,QAAQ;YAC1C,IAAI,OAAO,CAA6B,UAAC,OAAO;gBAC9C,OAAA,OAAO,CACL,OAAO,KAAI,CAAC,OAAO,KAAK,UAAU,CAAC,CAAC;oBAClC,KAAI,CAAC,OAAO,CAAC,SAAS,CAAC;oBACzB,CAAC,CAAC,KAAI,CAAC,OAAO,CACf;YAJD,CAIC,CACF;iBACE,IAAI,CAAC,UAAC,OAAO;gBACZ,IAAI,KAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,IAAM,gBAAgB,GAAG,QAAQ,CAAC,KAAI,CAAC,MAAM,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;oBAChE,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAChC,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC;oBACtC,CAAC;gBACH,CAAC;gBAED,OAAO,OAAO,CAAC;oBACb,MAAM,EAAE,KAAI,CAAC,MAAM;oBACnB,QAAQ,EAAE,SAAS,CAAC,KAAK;oBACzB,SAAS,EAAE,KAAI,CAAC,SAAS;oBACzB,YAAY,EAAE,OAAO;oBACrB,cAAc,EAAE,SAAS,CAAC,SAAS;oBACnC,aAAa,EAAE,SAAS,CAAC,aAAa;iBACvC,CAAC,CAAC;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,UAAC,IAAI;gBACT,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;oBACrB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACpB,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACtB,CAAC;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,UAAC,KAAK;gBACX,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;oBACrB,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC;IACH,iBAAC;AAAD,CAAC,AArDD,CAAgC,UAAU,GAqDzC", "sourcesContent": ["import type { GraphQLSchema } from \"graphql\";\nimport { validate, execute } from \"graphql\";\n\nimport type { Operation, FetchResult } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\n\nexport namespace SchemaLink {\n  export type ResolverContext = Record<string, any>;\n  export type ResolverContextFunction = (\n    operation: Operation\n  ) => ResolverContext | PromiseLike<ResolverContext>;\n\n  export interface Options {\n    /**\n     * The schema to generate responses from.\n     */\n    schema: GraphQLSchema;\n\n    /**\n     * The root value to use when generating responses.\n     */\n    rootValue?: any;\n\n    /**\n     * A context to provide to resolvers declared within the schema.\n     */\n    context?: ResolverContext | ResolverContextFunction;\n\n    /**\n     * Validate incoming queries against the given schema, returning\n     * validation errors as a GraphQL server would.\n     */\n    validate?: boolean;\n  }\n}\n\nexport class SchemaLink extends ApolloLink {\n  public schema: SchemaLink.Options[\"schema\"];\n  public rootValue: SchemaLink.Options[\"rootValue\"];\n  public context: SchemaLink.Options[\"context\"];\n  public validate: boolean;\n\n  constructor(options: SchemaLink.Options) {\n    super();\n    this.schema = options.schema;\n    this.rootValue = options.rootValue;\n    this.context = options.context;\n    this.validate = !!options.validate;\n  }\n\n  public request(operation: Operation): Observable<FetchResult> {\n    return new Observable<FetchResult>((observer) => {\n      new Promise<SchemaLink.ResolverContext>((resolve) =>\n        resolve(\n          typeof this.context === \"function\" ?\n            this.context(operation)\n          : this.context\n        )\n      )\n        .then((context) => {\n          if (this.validate) {\n            const validationErrors = validate(this.schema, operation.query);\n            if (validationErrors.length > 0) {\n              return { errors: validationErrors };\n            }\n          }\n\n          return execute({\n            schema: this.schema,\n            document: operation.query,\n            rootValue: this.rootValue,\n            contextValue: context,\n            variableValues: operation.variables,\n            operationName: operation.operationName,\n          });\n        })\n        .then((data) => {\n          if (!observer.closed) {\n            observer.next(data);\n            observer.complete();\n          }\n        })\n        .catch((error) => {\n          if (!observer.closed) {\n            observer.error(error);\n          }\n        });\n    });\n  }\n}\n"]}