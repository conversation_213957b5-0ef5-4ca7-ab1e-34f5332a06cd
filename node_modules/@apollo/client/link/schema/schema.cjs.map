{"version": 3, "file": "schema.cjs", "sources": ["index.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { validate, execute } from \"graphql\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nvar SchemaLink = /** @class */ (function (_super) {\n    __extends(SchemaLink, _super);\n    function SchemaLink(options) {\n        var _this = _super.call(this) || this;\n        _this.schema = options.schema;\n        _this.rootValue = options.rootValue;\n        _this.context = options.context;\n        _this.validate = !!options.validate;\n        return _this;\n    }\n    SchemaLink.prototype.request = function (operation) {\n        var _this = this;\n        return new Observable(function (observer) {\n            new Promise(function (resolve) {\n                return resolve(typeof _this.context === \"function\" ?\n                    _this.context(operation)\n                    : _this.context);\n            })\n                .then(function (context) {\n                if (_this.validate) {\n                    var validationErrors = validate(_this.schema, operation.query);\n                    if (validationErrors.length > 0) {\n                        return { errors: validationErrors };\n                    }\n                }\n                return execute({\n                    schema: _this.schema,\n                    document: operation.query,\n                    rootValue: _this.rootValue,\n                    contextValue: context,\n                    variableValues: operation.variables,\n                    operationName: operation.operationName,\n                });\n            })\n                .then(function (data) {\n                if (!observer.closed) {\n                    observer.next(data);\n                    observer.complete();\n                }\n            })\n                .catch(function (error) {\n                if (!observer.closed) {\n                    observer.error(error);\n                }\n            });\n        });\n    };\n    return SchemaLink;\n}(ApolloLink));\nexport { SchemaLink };\n//# sourceMappingURL=index.js.map"], "names": ["__extends", "Observable", "validate", "execute", "ApolloLink"], "mappings": ";;;;;;;;;AAIG,IAAC,UAAU,KAAkB,UAAU,MAAM,EAAE;AAClD,IAAIA,eAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AAClC,IAAI,SAAS,UAAU,CAAC,OAAO,EAAE;AACjC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,QAAQ,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AACtC,QAAQ,KAAK,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AAC5C,QAAQ,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AACxC,QAAQ,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC5C,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,UAAU,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE;AACxD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAO,IAAIC,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAClD,YAAY,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE;AAC3C,gBAAgB,OAAO,OAAO,CAAC,OAAO,KAAK,CAAC,OAAO,KAAK,UAAU;AAClE,oBAAoB,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;AAC5C,sBAAsB,KAAK,CAAC,OAAO,CAAC,CAAC;AACrC,aAAa,CAAC;AACd,iBAAiB,IAAI,CAAC,UAAU,OAAO,EAAE;AACzC,gBAAgB,IAAI,KAAK,CAAC,QAAQ,EAAE;AACpC,oBAAoB,IAAI,gBAAgB,GAAGC,gBAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;AACnF,oBAAoB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;AACrD,wBAAwB,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC;AAC5D,qBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,OAAOC,eAAO,CAAC;AAC/B,oBAAoB,MAAM,EAAE,KAAK,CAAC,MAAM;AACxC,oBAAoB,QAAQ,EAAE,SAAS,CAAC,KAAK;AAC7C,oBAAoB,SAAS,EAAE,KAAK,CAAC,SAAS;AAC9C,oBAAoB,YAAY,EAAE,OAAO;AACzC,oBAAoB,cAAc,EAAE,SAAS,CAAC,SAAS;AACvD,oBAAoB,aAAa,EAAE,SAAS,CAAC,aAAa;AAC1D,iBAAiB,CAAC,CAAC;AACnB,aAAa,CAAC;AACd,iBAAiB,IAAI,CAAC,UAAU,IAAI,EAAE;AACtC,gBAAgB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACtC,oBAAoB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxC,oBAAoB,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACxC,iBAAiB;AACjB,aAAa,CAAC;AACd,iBAAiB,KAAK,CAAC,UAAU,KAAK,EAAE;AACxC,gBAAgB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AACtC,oBAAoB,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC1C,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,OAAO,UAAU,CAAC;AACtB,CAAC,CAACC,eAAU,CAAC;;;;"}