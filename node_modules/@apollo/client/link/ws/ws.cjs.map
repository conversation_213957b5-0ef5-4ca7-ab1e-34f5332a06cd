{"version": 3, "file": "ws.cjs", "sources": ["index.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { SubscriptionClient } from \"subscriptions-transport-ws\";\nimport { ApolloLink } from \"../core/index.js\";\nvar WebSocketLink = /** @class */ (function (_super) {\n    __extends(WebSocketLink, _super);\n    function WebSocketLink(paramsOrClient) {\n        var _this = _super.call(this) || this;\n        if (paramsOrClient instanceof SubscriptionClient) {\n            _this.subscriptionClient = paramsOrClient;\n        }\n        else {\n            _this.subscriptionClient = new SubscriptionClient(paramsOrClient.uri, paramsOrClient.options, paramsOrClient.webSocketImpl);\n        }\n        return _this;\n    }\n    WebSocketLink.prototype.request = function (operation) {\n        return this.subscriptionClient.request(operation);\n    };\n    return WebSocketLink;\n}(ApolloLink));\nexport { WebSocketLink };\n//# sourceMappingURL=index.js.map"], "names": ["__extends", "SubscriptionClient", "ApolloLink"], "mappings": ";;;;;;;;AAGG,IAAC,aAAa,KAAkB,UAAU,MAAM,EAAE;AACrD,IAAIA,eAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACrC,IAAI,SAAS,aAAa,CAAC,cAAc,EAAE;AAC3C,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,QAAQ,IAAI,cAAc,YAAYC,2CAAkB,EAAE;AAC1D,YAAY,KAAK,CAAC,kBAAkB,GAAG,cAAc,CAAC;AACtD,SAAS;AACT,aAAa;AACb,YAAY,KAAK,CAAC,kBAAkB,GAAG,IAAIA,2CAAkB,CAAC,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,OAAO,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AACxI,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,aAAa,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE;AAC3D,QAAQ,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC1D,KAAK,CAAC;AACN,IAAI,OAAO,aAAa,CAAC;AACzB,CAAC,CAACC,eAAU,CAAC;;;;"}