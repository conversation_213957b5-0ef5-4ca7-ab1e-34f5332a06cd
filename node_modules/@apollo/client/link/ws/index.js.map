{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/link/ws/index.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,kBAAkB,EAAE,MAAM,4BAA4B,CAAC;AAGhE,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AA4B9C;IAAmC,iCAAU;IAG3C,uBACE,cAAgE;QAEhE,YAAA,MAAK,WAAE,SAAC;QAER,IAAI,cAAc,YAAY,kBAAkB,EAAE,CAAC;YACjD,KAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC;QAC3C,CAAC;aAAM,CAAC;YACN,KAAI,CAAC,kBAAkB,GAAG,IAAI,kBAAkB,CAC9C,cAAc,CAAC,GAAG,EAClB,cAAc,CAAC,OAAO,EACtB,cAAc,CAAC,aAAa,CAC7B,CAAC;QACJ,CAAC;;IACH,CAAC;IAEM,+BAAO,GAAd,UAAe,SAAoB;QACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CACpC,SAAS,CACiB,CAAC;IAC/B,CAAC;IACH,oBAAC;AAAD,CAAC,AAxBD,CAAmC,UAAU,GAwB5C", "sourcesContent": ["import type { ClientOptions } from \"subscriptions-transport-ws\";\nimport { SubscriptionClient } from \"subscriptions-transport-ws\";\n\nimport type { Operation, FetchResult } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport type { Observable } from \"../../utilities/index.js\";\n\nexport namespace WebSocketLink {\n  /**\n   * Configuration to use when constructing the subscription client (subscriptions-transport-ws).\n   */\n  export interface Configuration {\n    /**\n     * The endpoint to connect to.\n     */\n    uri: string;\n\n    /**\n     * Options to pass when constructing the subscription client.\n     */\n    options?: ClientOptions;\n\n    /**\n     * A custom WebSocket implementation to use.\n     */\n    webSocketImpl?: any;\n  }\n}\n\n// For backwards compatibility.\nexport import WebSocketParams = WebSocketLink.Configuration;\n\nexport class WebSocketLink extends ApolloLink {\n  private subscriptionClient: SubscriptionClient;\n\n  constructor(\n    paramsOrClient: WebSocketLink.Configuration | SubscriptionClient\n  ) {\n    super();\n\n    if (paramsOrClient instanceof SubscriptionClient) {\n      this.subscriptionClient = paramsOrClient;\n    } else {\n      this.subscriptionClient = new SubscriptionClient(\n        paramsOrClient.uri,\n        paramsOrClient.options,\n        paramsOrClient.webSocketImpl\n      );\n    }\n  }\n\n  public request(operation: Operation): Observable<FetchResult> | null {\n    return this.subscriptionClient.request(\n      operation\n    ) as Observable<FetchResult>;\n  }\n}\n"]}