{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/link/subscriptions/index.ts"], "names": [], "mappings": "AAAA,wDAAwD;AACxD,0CAA0C;AAC1C,EAAE;AACF,+EAA+E;AAC/E,0CAA0C;AAC1C,EAAE;AACF,2CAA2C;AAC3C,EAAE;AACF,wBAAwB;AACxB,EAAE;AACF,yCAAyC;AACzC,EAAE;AACF,+EAA+E;AAC/E,gFAAgF;AAChF,+EAA+E;AAC/E,4EAA4E;AAC5E,wEAAwE;AACxE,2DAA2D;AAC3D,EAAE;AACF,6EAA6E;AAC7E,sDAAsD;AACtD,EAAE;AACF,6EAA6E;AAC7E,2EAA2E;AAC3E,8EAA8E;AAC9E,yEAAyE;AACzE,gFAAgF;AAChF,4EAA4E;AAC5E,gBAAgB;;AAEhB,OAAO,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAIjD,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AACvE,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AAGpD,yEAAyE;AACzE,SAAS,gBAAgB,CAAC,GAAY;IACpC,OAAO,eAAe,CAAC,GAAG,CAAC,IAAI,MAAM,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,CAAC;AAClE,CAAC;AAED,yEAAyE;AACzE,SAAS,gBAAgB,CAAC,GAAY;;IACpC,OAAO,eAAe,CAAC,GAAG,CAAC,IAAI,CAAA,MAAA,GAAG,CAAC,MAAM,0CAAE,UAAU,MAAK,SAAS,CAAC,MAAM,CAAC;AAC7E,CAAC;AAED;IAAmC,iCAAU;IAC3C,uBAA4B,MAAc;QACxC,YAAA,MAAK,WAAE,SAAC;QADkB,YAAM,GAAN,MAAM,CAAQ;;IAE1C,CAAC;IAEM,+BAAO,GAAd,UAAe,SAAoB;QAAnC,iBAiCC;QAhCC,OAAO,IAAI,UAAU,CAAC,UAAC,QAAQ;YAC7B,OAAO,KAAI,CAAC,MAAM,CAAC,SAAS,uBACrB,SAAS,KAAE,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAC7C;gBACE,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAClC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;gBAC1C,KAAK,EAAE,UAAC,GAAG;oBACT,IAAI,GAAG,YAAY,KAAK,EAAE,CAAC;wBACzB,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAC7B,CAAC;oBACD,IAAM,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;oBACxC,IAAI,SAAS,IAAI,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC;wBACvC,OAAO,QAAQ,CAAC,KAAK;wBACnB,2CAA2C;wBAC3C,IAAI,KAAK,CACP,uBAAgB,SAAS,CAAC,CAAC,CAAC,sBAAe,GAAG,CAAC,IAAI,CAAE,CAAC,CAAC,CAAC,EAAE,SACxD,SAAS,CAAC,CAAC,CAAC,WAAI,GAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC,EAAE,CACjC,CACH,CACF,CAAC;oBACJ,CAAC;oBAED,OAAO,QAAQ,CAAC,KAAK,CACnB,IAAI,WAAW,CAAC;wBACd,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;qBAChD,CAAC,CACH,CAAC;gBACJ,CAAC;gBACD,+FAA+F;aAChD,CAClD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IACH,oBAAC;AAAD,CAAC,AAvCD,CAAmC,UAAU,GAuC5C", "sourcesContent": ["// This file is adapted from the graphql-ws npm package:\n// https://github.com/enisdenjo/graphql-ws\n//\n// Most of the file comes from that package's README; some other parts (such as\n// isLikeCloseEvent) come from its source.\n//\n// Here's the license of the original code:\n//\n// The MIT License (MIT)\n//\n// Copyright (c) 2020-2021 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nimport { print } from \"../../utilities/index.js\";\nimport type { Client, Sink } from \"graphql-ws\";\n\nimport type { Operation, FetchResult } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { isNonNullObject, Observable } from \"../../utilities/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\nimport type { FormattedExecutionResult } from \"graphql\";\n\n// https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/close_event\nfunction isLikeCloseEvent(val: unknown): val is CloseEvent {\n  return isNonNullObject(val) && \"code\" in val && \"reason\" in val;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/error_event\nfunction isLikeErrorEvent(err: unknown): err is Event {\n  return isNonNullObject(err) && err.target?.readyState === WebSocket.CLOSED;\n}\n\nexport class GraphQLWsLink extends ApolloLink {\n  constructor(public readonly client: Client) {\n    super();\n  }\n\n  public request(operation: Operation): Observable<FetchResult> {\n    return new Observable((observer) => {\n      return this.client.subscribe<FetchResult>(\n        { ...operation, query: print(operation.query) },\n        {\n          next: observer.next.bind(observer),\n          complete: observer.complete.bind(observer),\n          error: (err) => {\n            if (err instanceof Error) {\n              return observer.error(err);\n            }\n            const likeClose = isLikeCloseEvent(err);\n            if (likeClose || isLikeErrorEvent(err)) {\n              return observer.error(\n                // reason will be available on clean closes\n                new Error(\n                  `Socket closed${likeClose ? ` with event ${err.code}` : \"\"}${\n                    likeClose ? ` ${err.reason}` : \"\"\n                  }`\n                )\n              );\n            }\n\n            return observer.error(\n              new ApolloError({\n                graphQLErrors: Array.isArray(err) ? err : [err],\n              })\n            );\n          },\n          // casting around a wrong type in graphql-ws, which incorrectly expects `Sink<ExecutionResult>`\n        } satisfies Sink<FormattedExecutionResult> as any\n      );\n    });\n  }\n}\n"]}