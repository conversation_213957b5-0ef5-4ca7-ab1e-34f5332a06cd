{"version": 3, "file": "error.cjs", "sources": ["index.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { graphQLResultHasProtocolErrors, PROTOCOL_ERRORS_SYMBOL, } from \"../../errors/index.js\";\nimport { Observable } from \"../../utilities/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nexport function onError(errorHandler) {\n    return new ApolloLink(function (operation, forward) {\n        return new Observable(function (observer) {\n            var sub;\n            var retriedSub;\n            var retriedResult;\n            try {\n                sub = forward(operation).subscribe({\n                    next: function (result) {\n                        if (result.errors) {\n                            retriedResult = errorHandler({\n                                graphQLErrors: result.errors,\n                                response: result,\n                                operation: operation,\n                                forward: forward,\n                            });\n                        }\n                        else if (graphQLResultHasProtocolErrors(result)) {\n                            retriedResult = errorHandler({\n                                protocolErrors: result.extensions[PROTOCOL_ERRORS_SYMBOL],\n                                response: result,\n                                operation: operation,\n                                forward: forward,\n                            });\n                        }\n                        if (retriedResult) {\n                            retriedSub = retriedResult.subscribe({\n                                next: observer.next.bind(observer),\n                                error: observer.error.bind(observer),\n                                complete: observer.complete.bind(observer),\n                            });\n                            return;\n                        }\n                        observer.next(result);\n                    },\n                    error: function (networkError) {\n                        retriedResult = errorHandler({\n                            operation: operation,\n                            networkError: networkError,\n                            //Network errors can return GraphQL errors on for example a 403\n                            graphQLErrors: (networkError &&\n                                networkError.result &&\n                                networkError.result.errors) ||\n                                void 0,\n                            forward: forward,\n                        });\n                        if (retriedResult) {\n                            retriedSub = retriedResult.subscribe({\n                                next: observer.next.bind(observer),\n                                error: observer.error.bind(observer),\n                                complete: observer.complete.bind(observer),\n                            });\n                            return;\n                        }\n                        observer.error(networkError);\n                    },\n                    complete: function () {\n                        // disable the previous sub from calling complete on observable\n                        // if retry is in flight.\n                        if (!retriedResult) {\n                            observer.complete.bind(observer)();\n                        }\n                    },\n                });\n            }\n            catch (e) {\n                errorHandler({ networkError: e, operation: operation, forward: forward });\n                observer.error(e);\n            }\n            return function () {\n                if (sub)\n                    sub.unsubscribe();\n                if (retriedSub)\n                    sub.unsubscribe();\n            };\n        });\n    });\n}\nvar ErrorLink = /** @class */ (function (_super) {\n    __extends(ErrorLink, _super);\n    function ErrorLink(errorHandler) {\n        var _this = _super.call(this) || this;\n        _this.link = onError(errorHandler);\n        return _this;\n    }\n    ErrorLink.prototype.request = function (operation, forward) {\n        return this.link.request(operation, forward);\n    };\n    return ErrorLink;\n}(ApolloLink));\nexport { ErrorLink };\n//# sourceMappingURL=index.js.map"], "names": ["ApolloLink", "Observable", "graphQLResultHasProtocolErrors", "PROTOCOL_ERRORS_SYMBOL", "__extends"], "mappings": ";;;;;;;;;AAIO,SAAS,OAAO,CAAC,YAAY,EAAE;AACtC,IAAI,OAAO,IAAIA,eAAU,CAAC,UAAU,SAAS,EAAE,OAAO,EAAE;AACxD,QAAQ,OAAO,IAAIC,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAClD,YAAY,IAAI,GAAG,CAAC;AACpB,YAAY,IAAI,UAAU,CAAC;AAC3B,YAAY,IAAI,aAAa,CAAC;AAC9B,YAAY,IAAI;AAChB,gBAAgB,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC;AACnD,oBAAoB,IAAI,EAAE,UAAU,MAAM,EAAE;AAC5C,wBAAwB,IAAI,MAAM,CAAC,MAAM,EAAE;AAC3C,4BAA4B,aAAa,GAAG,YAAY,CAAC;AACzD,gCAAgC,aAAa,EAAE,MAAM,CAAC,MAAM;AAC5D,gCAAgC,QAAQ,EAAE,MAAM;AAChD,gCAAgC,SAAS,EAAE,SAAS;AACpD,gCAAgC,OAAO,EAAE,OAAO;AAChD,6BAA6B,CAAC,CAAC;AAC/B,yBAAyB;AACzB,6BAA6B,IAAIC,qCAA8B,CAAC,MAAM,CAAC,EAAE;AACzE,4BAA4B,aAAa,GAAG,YAAY,CAAC;AACzD,gCAAgC,cAAc,EAAE,MAAM,CAAC,UAAU,CAACC,6BAAsB,CAAC;AACzF,gCAAgC,QAAQ,EAAE,MAAM;AAChD,gCAAgC,SAAS,EAAE,SAAS;AACpD,gCAAgC,OAAO,EAAE,OAAO;AAChD,6BAA6B,CAAC,CAAC;AAC/B,yBAAyB;AACzB,wBAAwB,IAAI,aAAa,EAAE;AAC3C,4BAA4B,UAAU,GAAG,aAAa,CAAC,SAAS,CAAC;AACjE,gCAAgC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AAClE,gCAAgC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;AACpE,gCAAgC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC1E,6BAA6B,CAAC,CAAC;AAC/B,4BAA4B,OAAO;AACnC,yBAAyB;AACzB,wBAAwB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9C,qBAAqB;AACrB,oBAAoB,KAAK,EAAE,UAAU,YAAY,EAAE;AACnD,wBAAwB,aAAa,GAAG,YAAY,CAAC;AACrD,4BAA4B,SAAS,EAAE,SAAS;AAChD,4BAA4B,YAAY,EAAE,YAAY;AAEtD,4BAA4B,aAAa,EAAE,CAAC,YAAY;AACxD,gCAAgC,YAAY,CAAC,MAAM;AACnD,gCAAgC,YAAY,CAAC,MAAM,CAAC,MAAM;AAC1D,gCAAgC,KAAK,CAAC;AACtC,4BAA4B,OAAO,EAAE,OAAO;AAC5C,yBAAyB,CAAC,CAAC;AAC3B,wBAAwB,IAAI,aAAa,EAAE;AAC3C,4BAA4B,UAAU,GAAG,aAAa,CAAC,SAAS,CAAC;AACjE,gCAAgC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AAClE,gCAAgC,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;AACpE,gCAAgC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC1E,6BAA6B,CAAC,CAAC;AAC/B,4BAA4B,OAAO;AACnC,yBAAyB;AACzB,wBAAwB,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AACrD,qBAAqB;AACrB,oBAAoB,QAAQ,EAAE,YAAY;AAG1C,wBAAwB,IAAI,CAAC,aAAa,EAAE;AAC5C,4BAA4B,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;AAC/D,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,YAAY,OAAO,CAAC,EAAE;AACtB,gBAAgB,YAAY,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;AAC1F,gBAAgB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,aAAa;AACb,YAAY,OAAO,YAAY;AAC/B,gBAAgB,IAAI,GAAG;AACvB,oBAAoB,GAAG,CAAC,WAAW,EAAE,CAAC;AACtC,gBAAgB,IAAI,UAAU;AAC9B,oBAAoB,GAAG,CAAC,WAAW,EAAE,CAAC;AACtC,aAAa,CAAC;AACd,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,CAAC;AACE,IAAC,SAAS,KAAkB,UAAU,MAAM,EAAE;AACjD,IAAIC,eAAS,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AACjC,IAAI,SAAS,SAAS,CAAC,YAAY,EAAE;AACrC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,QAAQ,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AAC3C,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE,OAAO,EAAE;AAChE,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AACrD,KAAK,CAAC;AACN,IAAI,OAAO,SAAS,CAAC;AACrB,CAAC,CAACJ,eAAU,CAAC;;;;;"}