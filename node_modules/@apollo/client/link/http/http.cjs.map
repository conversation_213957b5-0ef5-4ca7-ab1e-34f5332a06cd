{"version": 3, "file": "http.cjs", "sources": ["iterators/async.js", "iterators/nodeStream.js", "iterators/promise.js", "iterators/reader.js", "responseIterator.js", "../../utilities/common/objects.js", "../../utilities/common/incrementalResult.js", "parseAndCheckHttpResponse.js", "serializeFetchParameter.js", "selectHttpOptionsAndBody.js", "checkFetcher.js", "createSignalIfSupported.js", "selectURI.js", "rewriteURIForGET.js", "createHttpLink.js", "HttpLink.js"], "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/async.ts\n */\nexport default function asyncIterator(source) {\n    var _a;\n    var iterator = source[Symbol.asyncIterator]();\n    return _a = {\n            next: function () {\n                return iterator.next();\n            }\n        },\n        _a[Symbol.asyncIterator] = function () {\n            return this;\n        },\n        _a;\n}\n//# sourceMappingURL=async.js.map", "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/nodeStream.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function nodeStreamIterator(stream) {\n    var cleanup = null;\n    var error = null;\n    var done = false;\n    var data = [];\n    var waiting = [];\n    function onData(chunk) {\n        if (error)\n            return;\n        if (waiting.length) {\n            var shiftedArr = waiting.shift();\n            if (Array.isArray(shiftedArr) && shiftedArr[0]) {\n                return shiftedArr[0]({ value: chunk, done: false });\n            }\n        }\n        data.push(chunk);\n    }\n    function onError(err) {\n        error = err;\n        var all = waiting.slice();\n        all.forEach(function (pair) {\n            pair[1](err);\n        });\n        !cleanup || cleanup();\n    }\n    function onEnd() {\n        done = true;\n        var all = waiting.slice();\n        all.forEach(function (pair) {\n            pair[0]({ value: undefined, done: true });\n        });\n        !cleanup || cleanup();\n    }\n    cleanup = function () {\n        cleanup = null;\n        stream.removeListener(\"data\", onData);\n        stream.removeListener(\"error\", onError);\n        stream.removeListener(\"end\", onEnd);\n        stream.removeListener(\"finish\", onEnd);\n        stream.removeListener(\"close\", onEnd);\n    };\n    stream.on(\"data\", onData);\n    stream.on(\"error\", onError);\n    stream.on(\"end\", onEnd);\n    stream.on(\"finish\", onEnd);\n    stream.on(\"close\", onEnd);\n    function getNext() {\n        return new Promise(function (resolve, reject) {\n            if (error)\n                return reject(error);\n            if (data.length)\n                return resolve({ value: data.shift(), done: false });\n            if (done)\n                return resolve({ value: undefined, done: true });\n            waiting.push([resolve, reject]);\n        });\n    }\n    var iterator = {\n        next: function () {\n            return getNext();\n        },\n    };\n    if (canUseAsyncIteratorSymbol) {\n        iterator[Symbol.asyncIterator] = function () {\n            return this;\n        };\n    }\n    return iterator;\n}\n//# sourceMappingURL=nodeStream.js.map", "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/promise.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function promiseIterator(promise) {\n    var resolved = false;\n    var iterator = {\n        next: function () {\n            if (resolved)\n                return Promise.resolve({\n                    value: undefined,\n                    done: true,\n                });\n            resolved = true;\n            return new Promise(function (resolve, reject) {\n                promise\n                    .then(function (value) {\n                    resolve({ value: value, done: false });\n                })\n                    .catch(reject);\n            });\n        },\n    };\n    if (canUseAsyncIteratorSymbol) {\n        iterator[Symbol.asyncIterator] = function () {\n            return this;\n        };\n    }\n    return iterator;\n}\n//# sourceMappingURL=promise.js.map", "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/reader.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function readerIterator(reader) {\n    var iterator = {\n        next: function () {\n            return reader.read();\n        },\n    };\n    if (canUseAsyncIteratorSymbol) {\n        iterator[Symbol.asyncIterator] = function () {\n            return this;\n        };\n    }\n    return iterator;\n}\n//# sourceMappingURL=reader.js.map", "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/index.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../utilities/index.js\";\nimport asyncIterator from \"./iterators/async.js\";\nimport nodeStreamIterator from \"./iterators/nodeStream.js\";\nimport promiseIterator from \"./iterators/promise.js\";\nimport readerIterator from \"./iterators/reader.js\";\nfunction isNodeResponse(value) {\n    return !!value.body;\n}\nfunction isReadableStream(value) {\n    return !!value.getReader;\n}\nfunction isAsyncIterableIterator(value) {\n    return !!(canUseAsyncIteratorSymbol &&\n        value[Symbol.asyncIterator]);\n}\nfunction isStreamableBlob(value) {\n    return !!value.stream;\n}\nfunction isBlob(value) {\n    return !!value.arrayBuffer;\n}\nfunction isNodeReadableStream(value) {\n    return !!value.pipe;\n}\nexport function responseIterator(response) {\n    var body = response;\n    if (isNodeResponse(response))\n        body = response.body;\n    if (isAsyncIterableIterator(body))\n        return asyncIterator(body);\n    if (isReadableStream(body))\n        return readerIterator(body.getReader());\n    // this errors without casting to ReadableStream<T>\n    // because Blob.stream() returns a NodeJS ReadableStream\n    if (isStreamableBlob(body)) {\n        return readerIterator(body.stream().getReader());\n    }\n    if (isBlob(body))\n        return promiseIterator(body.arrayBuffer());\n    if (isNodeReadableStream(body))\n        return nodeStreamIterator(body);\n    throw new Error(\"Unknown body type for responseIterator. Please pass a streamable response.\");\n}\n//# sourceMappingURL=responseIterator.js.map", "export function isNonNullObject(obj) {\n    return obj !== null && typeof obj === \"object\";\n}\nexport function isPlainObject(obj) {\n    return (obj !== null &&\n        typeof obj === \"object\" &&\n        (Object.getPrototypeOf(obj) === Object.prototype ||\n            Object.getPrototypeOf(obj) === null));\n}\n//# sourceMappingURL=objects.js.map", "import { isNonNullObject } from \"./objects.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { DeepMerger } from \"./mergeDeep.js\";\nexport function isExecutionPatchIncrementalResult(value) {\n    return \"incremental\" in value;\n}\nexport function isExecutionPatchInitialResult(value) {\n    return \"hasNext\" in value && \"data\" in value;\n}\nexport function isExecutionPatchResult(value) {\n    return (isExecutionPatchIncrementalResult(value) ||\n        isExecutionPatchInitialResult(value));\n}\n// This function detects an Apollo payload result before it is transformed\n// into a FetchResult via HttpLink; it cannot detect an ApolloPayloadResult\n// once it leaves the link chain.\nexport function isApolloPayloadResult(value) {\n    return isNonNullObject(value) && \"payload\" in value;\n}\nexport function mergeIncrementalData(prevResult, result) {\n    var mergedData = prevResult;\n    var merger = new DeepMerger();\n    if (isExecutionPatchIncrementalResult(result) &&\n        isNonEmptyArray(result.incremental)) {\n        result.incremental.forEach(function (_a) {\n            var data = _a.data, path = _a.path;\n            for (var i = path.length - 1; i >= 0; --i) {\n                var key = path[i];\n                var isNumericKey = !isNaN(+key);\n                var parent_1 = isNumericKey ? [] : {};\n                parent_1[key] = data;\n                data = parent_1;\n            }\n            mergedData = merger.merge(mergedData, data);\n        });\n    }\n    return mergedData;\n}\n//# sourceMappingURL=incrementalResult.js.map", "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { responseIterator } from \"./responseIterator.js\";\nimport { throwServerError } from \"../utils/index.js\";\nimport { PROTOCOL_ERRORS_SYMBOL } from \"../../errors/index.js\";\nimport { isApolloPayloadResult } from \"../../utilities/common/incrementalResult.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function readMultipartBody(response, nextValue) {\n    return __awaiter(this, void 0, void 0, function () {\n        var decoder, contentType, delimiter, boundaryVal, boundary, buffer, iterator, running, _a, value, done, chunk, searchFrom, bi, message, i, headers, contentType_1, body, result, next;\n        var _b, _c;\n        var _d;\n        return __generator(this, function (_e) {\n            switch (_e.label) {\n                case 0:\n                    if (TextDecoder === undefined) {\n                        throw new Error(\"TextDecoder must be defined in the environment: please import a polyfill.\");\n                    }\n                    decoder = new TextDecoder(\"utf-8\");\n                    contentType = (_d = response.headers) === null || _d === void 0 ? void 0 : _d.get(\"content-type\");\n                    delimiter = \"boundary=\";\n                    boundaryVal = (contentType === null || contentType === void 0 ? void 0 : contentType.includes(delimiter)) ?\n                        contentType === null || contentType === void 0 ? void 0 : contentType.substring((contentType === null || contentType === void 0 ? void 0 : contentType.indexOf(delimiter)) + delimiter.length).replace(/['\"]/g, \"\").replace(/\\;(.*)/gm, \"\").trim()\n                        : \"-\";\n                    boundary = \"\\r\\n--\".concat(boundaryVal);\n                    buffer = \"\";\n                    iterator = responseIterator(response);\n                    running = true;\n                    _e.label = 1;\n                case 1:\n                    if (!running) return [3 /*break*/, 3];\n                    return [4 /*yield*/, iterator.next()];\n                case 2:\n                    _a = _e.sent(), value = _a.value, done = _a.done;\n                    chunk = typeof value === \"string\" ? value : decoder.decode(value);\n                    searchFrom = buffer.length - boundary.length + 1;\n                    running = !done;\n                    buffer += chunk;\n                    bi = buffer.indexOf(boundary, searchFrom);\n                    while (bi > -1) {\n                        message = void 0;\n                        _b = [\n                            buffer.slice(0, bi),\n                            buffer.slice(bi + boundary.length),\n                        ], message = _b[0], buffer = _b[1];\n                        i = message.indexOf(\"\\r\\n\\r\\n\");\n                        headers = parseHeaders(message.slice(0, i));\n                        contentType_1 = headers[\"content-type\"];\n                        if (contentType_1 &&\n                            contentType_1.toLowerCase().indexOf(\"application/json\") === -1) {\n                            throw new Error(\"Unsupported patch content type: application/json is required.\");\n                        }\n                        body = message.slice(i);\n                        if (body) {\n                            result = parseJsonBody(response, body);\n                            if (Object.keys(result).length > 1 ||\n                                \"data\" in result ||\n                                \"incremental\" in result ||\n                                \"errors\" in result ||\n                                \"payload\" in result) {\n                                if (isApolloPayloadResult(result)) {\n                                    next = {};\n                                    if (\"payload\" in result) {\n                                        if (Object.keys(result).length === 1 && result.payload === null) {\n                                            return [2 /*return*/];\n                                        }\n                                        next = __assign({}, result.payload);\n                                    }\n                                    if (\"errors\" in result) {\n                                        next = __assign(__assign({}, next), { extensions: __assign(__assign({}, (\"extensions\" in next ? next.extensions : null)), (_c = {}, _c[PROTOCOL_ERRORS_SYMBOL] = result.errors, _c)) });\n                                    }\n                                    nextValue(next);\n                                }\n                                else {\n                                    // for the last chunk with only `hasNext: false`\n                                    // we don't need to call observer.next as there is no data/errors\n                                    nextValue(result);\n                                }\n                            }\n                            else if (\n                            // If the chunk contains only a \"hasNext: false\", we can call\n                            // observer.complete() immediately.\n                            Object.keys(result).length === 1 &&\n                                \"hasNext\" in result &&\n                                !result.hasNext) {\n                                return [2 /*return*/];\n                            }\n                        }\n                        bi = buffer.indexOf(boundary);\n                    }\n                    return [3 /*break*/, 1];\n                case 3: return [2 /*return*/];\n            }\n        });\n    });\n}\nexport function parseHeaders(headerText) {\n    var headersInit = {};\n    headerText.split(\"\\n\").forEach(function (line) {\n        var i = line.indexOf(\":\");\n        if (i > -1) {\n            // normalize headers to lowercase\n            var name_1 = line.slice(0, i).trim().toLowerCase();\n            var value = line.slice(i + 1).trim();\n            headersInit[name_1] = value;\n        }\n    });\n    return headersInit;\n}\nexport function parseJsonBody(response, bodyText) {\n    if (response.status >= 300) {\n        // Network error\n        var getResult = function () {\n            try {\n                return JSON.parse(bodyText);\n            }\n            catch (err) {\n                return bodyText;\n            }\n        };\n        throwServerError(response, getResult(), \"Response not successful: Received status code \".concat(response.status));\n    }\n    try {\n        return JSON.parse(bodyText);\n    }\n    catch (err) {\n        var parseError = err;\n        parseError.name = \"ServerParseError\";\n        parseError.response = response;\n        parseError.statusCode = response.status;\n        parseError.bodyText = bodyText;\n        throw parseError;\n    }\n}\nexport function handleError(err, observer) {\n    // if it is a network error, BUT there is graphql result info fire\n    // the next observer before calling error this gives apollo-client\n    // (and react-apollo) the `graphqlErrors` and `networkErrors` to\n    // pass to UI this should only happen if we *also* have data as\n    // part of the response key per the spec\n    if (err.result && err.result.errors && err.result.data) {\n        // if we don't call next, the UI can only show networkError\n        // because AC didn't get any graphqlErrors this is graphql\n        // execution result info (i.e errors and possibly data) this is\n        // because there is no formal spec how errors should translate to\n        // http status codes. So an auth error (401) could have both data\n        // from a public field, errors from a private field, and a status\n        // of 401\n        // {\n        //  user { // this will have errors\n        //    firstName\n        //  }\n        //  products { // this is public so will have data\n        //    cost\n        //  }\n        // }\n        //\n        // the result of above *could* look like this:\n        // {\n        //   data: { products: [{ cost: \"$10\" }] },\n        //   errors: [{\n        //      message: 'your session has timed out',\n        //      path: []\n        //   }]\n        // }\n        // status code of above would be a 401\n        // in the UI you want to show data where you can, errors as data where you can\n        // and use correct http status codes\n        observer.next(err.result);\n    }\n    observer.error(err);\n}\nexport function parseAndCheckHttpResponse(operations) {\n    return function (response) {\n        return response\n            .text()\n            .then(function (bodyText) { return parseJsonBody(response, bodyText); })\n            .then(function (result) {\n            if (!Array.isArray(result) &&\n                !hasOwnProperty.call(result, \"data\") &&\n                !hasOwnProperty.call(result, \"errors\")) {\n                // Data error\n                throwServerError(response, result, \"Server response was missing for query '\".concat(Array.isArray(operations) ?\n                    operations.map(function (op) { return op.operationName; })\n                    : operations.operationName, \"'.\"));\n            }\n            return result;\n        });\n    };\n}\n//# sourceMappingURL=parseAndCheckHttpResponse.js.map", "import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport var serializeFetchParameter = function (p, label) {\n    var serialized;\n    try {\n        serialized = JSON.stringify(p);\n    }\n    catch (e) {\n        var parseError = newInvariantError(42, label, e.message);\n        parseError.parseError = e;\n        throw parseError;\n    }\n    return serialized;\n};\n//# sourceMappingURL=serializeFetchParameter.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport { print } from \"../../utilities/index.js\";\nvar defaultHttpOptions = {\n    includeQuery: true,\n    includeExtensions: false,\n    preserveHeaderCase: false,\n};\nvar defaultHeaders = {\n    // headers are case insensitive (https://stackoverflow.com/a/5259004)\n    accept: \"*/*\",\n    // The content-type header describes the type of the body of the request, and\n    // so it typically only is sent with requests that actually have bodies. One\n    // could imagine that Apollo Client would remove this header when constructing\n    // a GET request (which has no body), but we historically have not done that.\n    // This means that browsers will preflight all Apollo Client requests (even\n    // GET requests). Apollo Server's CSRF prevention feature (introduced in\n    // AS3.7) takes advantage of this fact and does not block requests with this\n    // header. If you want to drop this header from GET requests, then you should\n    // probably replace it with a `apollo-require-preflight` header, or servers\n    // with CSRF prevention enabled might block your GET request. See\n    // https://www.apollographql.com/docs/apollo-server/security/cors/#preventing-cross-site-request-forgery-csrf\n    // for more details.\n    \"content-type\": \"application/json\",\n};\nvar defaultOptions = {\n    method: \"POST\",\n};\nexport var fallbackHttpConfig = {\n    http: defaultHttpOptions,\n    headers: defaultHeaders,\n    options: defaultOptions,\n};\nexport var defaultPrinter = function (ast, printer) { return printer(ast); };\nexport function selectHttpOptionsAndBody(operation, fallbackConfig) {\n    var configs = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        configs[_i - 2] = arguments[_i];\n    }\n    configs.unshift(fallbackConfig);\n    return selectHttpOptionsAndBodyInternal.apply(void 0, __spreadArray([operation,\n        defaultPrinter], configs, false));\n}\nexport function selectHttpOptionsAndBodyInternal(operation, printer) {\n    var configs = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        configs[_i - 2] = arguments[_i];\n    }\n    var options = {};\n    var http = {};\n    configs.forEach(function (config) {\n        options = __assign(__assign(__assign({}, options), config.options), { headers: __assign(__assign({}, options.headers), config.headers) });\n        if (config.credentials) {\n            options.credentials = config.credentials;\n        }\n        http = __assign(__assign({}, http), config.http);\n    });\n    if (options.headers) {\n        options.headers = removeDuplicateHeaders(options.headers, http.preserveHeaderCase);\n    }\n    //The body depends on the http options\n    var operationName = operation.operationName, extensions = operation.extensions, variables = operation.variables, query = operation.query;\n    var body = { operationName: operationName, variables: variables };\n    if (http.includeExtensions)\n        body.extensions = extensions;\n    // not sending the query (i.e persisted queries)\n    if (http.includeQuery)\n        body.query = printer(query, print);\n    return {\n        options: options,\n        body: body,\n    };\n}\n// Remove potential duplicate header names, preserving last (by insertion order).\n// This is done to prevent unintentionally duplicating a header instead of\n// overwriting it (See #8447 and #8449).\nfunction removeDuplicateHeaders(headers, preserveHeaderCase) {\n    // If we're not preserving the case, just remove duplicates w/ normalization.\n    if (!preserveHeaderCase) {\n        var normalizedHeaders_1 = {};\n        Object.keys(Object(headers)).forEach(function (name) {\n            normalizedHeaders_1[name.toLowerCase()] = headers[name];\n        });\n        return normalizedHeaders_1;\n    }\n    // If we are preserving the case, remove duplicates w/ normalization,\n    // preserving the original name.\n    // This allows for non-http-spec-compliant servers that expect intentionally\n    // capitalized header names (See #6741).\n    var headerData = {};\n    Object.keys(Object(headers)).forEach(function (name) {\n        headerData[name.toLowerCase()] = {\n            originalName: name,\n            value: headers[name],\n        };\n    });\n    var normalizedHeaders = {};\n    Object.keys(headerData).forEach(function (name) {\n        normalizedHeaders[headerData[name].originalName] = headerData[name].value;\n    });\n    return normalizedHeaders;\n}\n//# sourceMappingURL=selectHttpOptionsAndBody.js.map", "import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport var checkFetcher = function (fetcher) {\n    if (!fetcher && typeof fetch === \"undefined\") {\n        throw newInvariantError(40);\n    }\n};\n//# sourceMappingURL=checkFetcher.js.map", "/**\n * @deprecated\n * This is not used internally any more and will be removed in\n * the next major version of Apollo Client.\n */\nexport var createSignalIfSupported = function () {\n    if (typeof AbortController === \"undefined\")\n        return { controller: false, signal: false };\n    var controller = new AbortController();\n    var signal = controller.signal;\n    return { controller: controller, signal: signal };\n};\n//# sourceMappingURL=createSignalIfSupported.js.map", "export var selectURI = function (operation, fallbackURI) {\n    var context = operation.getContext();\n    var contextURI = context.uri;\n    if (contextURI) {\n        return contextURI;\n    }\n    else if (typeof fallbackURI === \"function\") {\n        return fallbackURI(operation);\n    }\n    else {\n        return fallbackURI || \"/graphql\";\n    }\n};\n//# sourceMappingURL=selectURI.js.map", "import { serializeFetchParameter } from \"./serializeFetchParameter.js\";\n// For GET operations, returns the given URI rewritten with parameters, or a\n// parse error.\nexport function rewriteURIForGET(chosenURI, body) {\n    // Implement the standard HTTP GET serialization, plus 'extensions'. Note\n    // the extra level of JSON serialization!\n    var queryParams = [];\n    var addQueryParam = function (key, value) {\n        queryParams.push(\"\".concat(key, \"=\").concat(encodeURIComponent(value)));\n    };\n    if (\"query\" in body) {\n        addQueryParam(\"query\", body.query);\n    }\n    if (body.operationName) {\n        addQueryParam(\"operationName\", body.operationName);\n    }\n    if (body.variables) {\n        var serializedVariables = void 0;\n        try {\n            serializedVariables = serializeFetchParameter(body.variables, \"Variables map\");\n        }\n        catch (parseError) {\n            return { parseError: parseError };\n        }\n        addQueryParam(\"variables\", serializedVariables);\n    }\n    if (body.extensions) {\n        var serializedExtensions = void 0;\n        try {\n            serializedExtensions = serializeFetchParameter(body.extensions, \"Extensions map\");\n        }\n        catch (parseError) {\n            return { parseError: parseError };\n        }\n        addQueryParam(\"extensions\", serializedExtensions);\n    }\n    // Reconstruct the URI with added query params.\n    // XXX This assumes that the URI is well-formed and that it doesn't\n    //     already contain any of these query params. We could instead use the\n    //     URL API and take a polyfill (whatwg-url@6) for older browsers that\n    //     don't support URLSearchParams. Note that some browsers (and\n    //     versions of whatwg-url) support URL but not URLSearchParams!\n    var fragment = \"\", preFragment = chosenURI;\n    var fragmentStart = chosenURI.indexOf(\"#\");\n    if (fragmentStart !== -1) {\n        fragment = chosenURI.substr(fragmentStart);\n        preFragment = chosenURI.substr(0, fragmentStart);\n    }\n    var queryParamsPrefix = preFragment.indexOf(\"?\") === -1 ? \"?\" : \"&\";\n    var newURI = preFragment + queryParamsPrefix + queryParams.join(\"&\") + fragment;\n    return { newURI: newURI };\n}\n//# sourceMappingURL=rewriteURIForGET.js.map", "import { __assign, __rest } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable, hasDirectives } from \"../../utilities/index.js\";\nimport { serializeFetchParameter } from \"./serializeFetchParameter.js\";\nimport { selectURI } from \"./selectURI.js\";\nimport { handleError, readMultipartBody, parseAndCheckHttpResponse, } from \"./parseAndCheckHttpResponse.js\";\nimport { checkFetcher } from \"./checkFetcher.js\";\nimport { selectHttpOptionsAndBodyInternal, defaultPrinter, fallbackHttpConfig, } from \"./selectHttpOptionsAndBody.js\";\nimport { rewriteURIForGET } from \"./rewriteURIForGET.js\";\nimport { fromError, filterOperationVariables } from \"../utils/index.js\";\nimport { maybe, getMainDefinition, removeClientSetsFromDocument, } from \"../../utilities/index.js\";\nvar backupFetch = maybe(function () { return fetch; });\nexport var createHttpLink = function (linkOptions) {\n    if (linkOptions === void 0) { linkOptions = {}; }\n    var _a = linkOptions.uri, uri = _a === void 0 ? \"/graphql\" : _a, \n    // use default global fetch if nothing passed in\n    preferredFetch = linkOptions.fetch, _b = linkOptions.print, print = _b === void 0 ? defaultPrinter : _b, includeExtensions = linkOptions.includeExtensions, preserveHeaderCase = linkOptions.preserveHeaderCase, useGETForQueries = linkOptions.useGETForQueries, _c = linkOptions.includeUnusedVariables, includeUnusedVariables = _c === void 0 ? false : _c, requestOptions = __rest(linkOptions, [\"uri\", \"fetch\", \"print\", \"includeExtensions\", \"preserveHeaderCase\", \"useGETForQueries\", \"includeUnusedVariables\"]);\n    if (globalThis.__DEV__ !== false) {\n        // Make sure at least one of preferredFetch, window.fetch, or backupFetch is\n        // defined, so requests won't fail at runtime.\n        checkFetcher(preferredFetch || backupFetch);\n    }\n    var linkConfig = {\n        http: { includeExtensions: includeExtensions, preserveHeaderCase: preserveHeaderCase },\n        options: requestOptions.fetchOptions,\n        credentials: requestOptions.credentials,\n        headers: requestOptions.headers,\n    };\n    return new ApolloLink(function (operation) {\n        var chosenURI = selectURI(operation, uri);\n        var context = operation.getContext();\n        // `apollographql-client-*` headers are automatically set if a\n        // `clientAwareness` object is found in the context. These headers are\n        // set first, followed by the rest of the headers pulled from\n        // `context.headers`. If desired, `apollographql-client-*` headers set by\n        // the `clientAwareness` object can be overridden by\n        // `apollographql-client-*` headers set in `context.headers`.\n        var clientAwarenessHeaders = {};\n        if (context.clientAwareness) {\n            var _a = context.clientAwareness, name_1 = _a.name, version = _a.version;\n            if (name_1) {\n                clientAwarenessHeaders[\"apollographql-client-name\"] = name_1;\n            }\n            if (version) {\n                clientAwarenessHeaders[\"apollographql-client-version\"] = version;\n            }\n        }\n        var contextHeaders = __assign(__assign({}, clientAwarenessHeaders), context.headers);\n        var contextConfig = {\n            http: context.http,\n            options: context.fetchOptions,\n            credentials: context.credentials,\n            headers: contextHeaders,\n        };\n        if (hasDirectives([\"client\"], operation.query)) {\n            var transformedQuery = removeClientSetsFromDocument(operation.query);\n            if (!transformedQuery) {\n                return fromError(new Error(\"HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`.\"));\n            }\n            operation.query = transformedQuery;\n        }\n        //uses fallback, link, and then context to build options\n        var _b = selectHttpOptionsAndBodyInternal(operation, print, fallbackHttpConfig, linkConfig, contextConfig), options = _b.options, body = _b.body;\n        if (body.variables && !includeUnusedVariables) {\n            body.variables = filterOperationVariables(body.variables, operation.query);\n        }\n        var controller;\n        if (!options.signal && typeof AbortController !== \"undefined\") {\n            controller = new AbortController();\n            options.signal = controller.signal;\n        }\n        // If requested, set method to GET if there are no mutations.\n        var definitionIsMutation = function (d) {\n            return d.kind === \"OperationDefinition\" && d.operation === \"mutation\";\n        };\n        var definitionIsSubscription = function (d) {\n            return d.kind === \"OperationDefinition\" && d.operation === \"subscription\";\n        };\n        var isSubscription = definitionIsSubscription(getMainDefinition(operation.query));\n        // does not match custom directives beginning with @defer\n        var hasDefer = hasDirectives([\"defer\"], operation.query);\n        if (useGETForQueries &&\n            !operation.query.definitions.some(definitionIsMutation)) {\n            options.method = \"GET\";\n        }\n        if (hasDefer || isSubscription) {\n            options.headers = options.headers || {};\n            var acceptHeader = \"multipart/mixed;\";\n            // Omit defer-specific headers if the user attempts to defer a selection\n            // set on a subscription and log a warning.\n            if (isSubscription && hasDefer) {\n                globalThis.__DEV__ !== false && invariant.warn(41);\n            }\n            if (isSubscription) {\n                acceptHeader +=\n                    \"boundary=graphql;subscriptionSpec=1.0,application/json\";\n            }\n            else if (hasDefer) {\n                acceptHeader += \"deferSpec=20220824,application/json\";\n            }\n            options.headers.accept = acceptHeader;\n        }\n        if (options.method === \"GET\") {\n            var _c = rewriteURIForGET(chosenURI, body), newURI = _c.newURI, parseError = _c.parseError;\n            if (parseError) {\n                return fromError(parseError);\n            }\n            chosenURI = newURI;\n        }\n        else {\n            try {\n                options.body = serializeFetchParameter(body, \"Payload\");\n            }\n            catch (parseError) {\n                return fromError(parseError);\n            }\n        }\n        return new Observable(function (observer) {\n            // Prefer linkOptions.fetch (preferredFetch) if provided, and otherwise\n            // fall back to the *current* global window.fetch function (see issue\n            // #7832), or (if all else fails) the backupFetch function we saved when\n            // this module was first evaluated. This last option protects against the\n            // removal of window.fetch, which is unlikely but not impossible.\n            var currentFetch = preferredFetch || maybe(function () { return fetch; }) || backupFetch;\n            var observerNext = observer.next.bind(observer);\n            currentFetch(chosenURI, options)\n                .then(function (response) {\n                var _a;\n                operation.setContext({ response: response });\n                var ctype = (_a = response.headers) === null || _a === void 0 ? void 0 : _a.get(\"content-type\");\n                if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n                    return readMultipartBody(response, observerNext);\n                }\n                else {\n                    return parseAndCheckHttpResponse(operation)(response).then(observerNext);\n                }\n            })\n                .then(function () {\n                controller = undefined;\n                observer.complete();\n            })\n                .catch(function (err) {\n                controller = undefined;\n                handleError(err, observer);\n            });\n            return function () {\n                // XXX support canceling this request\n                // https://developers.google.com/web/updates/2017/09/abortable-fetch\n                if (controller)\n                    controller.abort();\n            };\n        });\n    });\n};\n//# sourceMappingURL=createHttpLink.js.map", "import { __extends } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { createHttpLink } from \"./createHttpLink.js\";\nvar HttpLink = /** @class */ (function (_super) {\n    __extends(HttpLink, _super);\n    function HttpLink(options) {\n        if (options === void 0) { options = {}; }\n        var _this = _super.call(this, createHttpLink(options).request) || this;\n        _this.options = options;\n        return _this;\n    }\n    return HttpLink;\n}(ApolloLink));\nexport { HttpLink };\n//# sourceMappingURL=HttpLink.js.map"], "names": ["canUseAsyncIteratorSymbol", "__awaiter", "__generator", "__assign", "PROTOCOL_ERRORS_SYMBOL", "throwServerError", "newInvariantError", "__spread<PERSON><PERSON>y", "print", "maybe", "__rest", "ApolloLink", "hasDirectives", "removeClientSetsFromDocument", "fromError", "filterOperationVariables", "getMainDefinition", "invariant", "Observable", "__extends"], "mappings": ";;;;;;;;;;;AAIe,SAAS,aAAa,CAAC,MAAM,EAAE;AAC9C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;AAClD,IAAI,OAAO,EAAE,GAAG;AAChB,YAAY,IAAI,EAAE,YAAY;AAC9B,gBAAgB,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AACvC,aAAa;AACb,SAAS;AACT,QAAQ,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY;AAC/C,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,EAAE,CAAC;AACX;;ACXe,SAAS,kBAAkB,CAAC,MAAM,EAAE;AACnD,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC;AACrB,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE;AAC3B,QAAQ,IAAI,KAAK;AACjB,YAAY,OAAO;AACnB,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AAC5B,YAAY,IAAI,UAAU,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;AAC7C,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;AAC5D,gBAAgB,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AACpE,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,SAAS,OAAO,CAAC,GAAG,EAAE;AAC1B,QAAQ,KAAK,GAAG,GAAG,CAAC;AACpB,QAAQ,IAAI,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;AAClC,QAAQ,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACpC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACzB,SAAS,CAAC,CAAC;AACX,QAAQ,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AAC9B,KAAK;AACL,IAAI,SAAS,KAAK,GAAG;AACrB,QAAQ,IAAI,GAAG,IAAI,CAAC;AACpB,QAAQ,IAAI,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;AAClC,QAAQ,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACpC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACtD,SAAS,CAAC,CAAC;AACX,QAAQ,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AAC9B,KAAK;AACL,IAAI,OAAO,GAAG,YAAY;AAC1B,QAAQ,OAAO,GAAG,IAAI,CAAC;AACvB,QAAQ,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC9C,QAAQ,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAChD,QAAQ,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC5C,QAAQ,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC/C,QAAQ,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC9C,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC9B,IAAI,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAChC,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC5B,IAAI,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC/B,IAAI,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC9B,IAAI,SAAS,OAAO,GAAG;AACvB,QAAQ,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AACtD,YAAY,IAAI,KAAK;AACrB,gBAAgB,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACrC,YAAY,IAAI,IAAI,CAAC,MAAM;AAC3B,gBAAgB,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AACrE,YAAY,IAAI,IAAI;AACpB,gBAAgB,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACjE,YAAY,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AAC5C,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,IAAI,QAAQ,GAAG;AACnB,QAAQ,IAAI,EAAE,YAAY;AAC1B,YAAY,OAAO,OAAO,EAAE,CAAC;AAC7B,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAIA,mCAAyB,EAAE;AACnC,QAAQ,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY;AACrD,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB;;ACpEe,SAAS,eAAe,CAAC,OAAO,EAAE;AACjD,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,QAAQ,GAAG;AACnB,QAAQ,IAAI,EAAE,YAAY;AAC1B,YAAY,IAAI,QAAQ;AACxB,gBAAgB,OAAO,OAAO,CAAC,OAAO,CAAC;AACvC,oBAAoB,KAAK,EAAE,SAAS;AACpC,oBAAoB,IAAI,EAAE,IAAI;AAC9B,iBAAiB,CAAC,CAAC;AACnB,YAAY,QAAQ,GAAG,IAAI,CAAC;AAC5B,YAAY,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AAC1D,gBAAgB,OAAO;AACvB,qBAAqB,IAAI,CAAC,UAAU,KAAK,EAAE;AAC3C,oBAAoB,OAAO,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AAC3D,iBAAiB,CAAC;AAClB,qBAAqB,KAAK,CAAC,MAAM,CAAC,CAAC;AACnC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAIA,mCAAyB,EAAE;AACnC,QAAQ,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY;AACrD,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB;;ACzBe,SAAS,cAAc,CAAC,MAAM,EAAE;AAC/C,IAAI,IAAI,QAAQ,GAAG;AACnB,QAAQ,IAAI,EAAE,YAAY;AAC1B,YAAY,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;AACjC,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAIA,mCAAyB,EAAE;AACnC,QAAQ,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY;AACrD,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB;;ACRA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;AACxB,CAAC;AACD,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;AAC7B,CAAC;AACD,SAAS,uBAAuB,CAAC,KAAK,EAAE;AACxC,IAAI,OAAO,CAAC,EAAEA,mCAAyB;AACvC,QAAQ,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;AACrC,CAAC;AACD,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;AAC1B,CAAC;AACD,SAAS,MAAM,CAAC,KAAK,EAAE;AACvB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC;AAC/B,CAAC;AACD,SAAS,oBAAoB,CAAC,KAAK,EAAE;AACrC,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;AACxB,CAAC;AACM,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AAC3C,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC;AACxB,IAAI,IAAI,cAAc,CAAC,QAAQ,CAAC;AAChC,QAAQ,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC7B,IAAI,IAAI,uBAAuB,CAAC,IAAI,CAAC;AACrC,QAAQ,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;AACnC,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC;AAC9B,QAAQ,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;AAGhD,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAChC,QAAQ,OAAO,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC;AACzD,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC;AACpB,QAAQ,OAAO,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACnD,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC;AAClC,QAAQ,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;AACxC,IAAI,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;AAClG;;AC9CO,SAAS,eAAe,CAAC,GAAG,EAAE;AACrC,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC;AACnD;;ACcO,SAAS,qBAAqB,CAAC,KAAK,EAAE;AAC7C,IAAI,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC;AACxD;;ACbA,IAAI,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AAC9C,SAAS,iBAAiB,CAAC,QAAQ,EAAE,SAAS,EAAE;AACvD,IAAI,OAAOC,eAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AACvD,QAAQ,IAAI,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;AAC9L,QAAQ,IAAI,EAAE,EAAE,EAAE,CAAC;AACnB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AAC/C,YAAY,QAAQ,EAAE,CAAC,KAAK;AAC5B,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,IAAI,WAAW,KAAK,SAAS,EAAE;AACnD,wBAAwB,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;AACrH,qBAAqB;AACrB,oBAAoB,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;AACvD,oBAAoB,WAAW,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACtH,oBAAoB,SAAS,GAAG,WAAW,CAAC;AAC5C,oBAAoB,WAAW,GAAG,CAAC,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC;AAC5H,wBAAwB,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE;AAC1Q,0BAA0B,GAAG,CAAC;AAC9B,oBAAoB,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAC5D,oBAAoB,MAAM,GAAG,EAAE,CAAC;AAChC,oBAAoB,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAC1D,oBAAoB,OAAO,GAAG,IAAI,CAAC;AACnC,oBAAoB,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;AACjC,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,GAAY,CAAC,CAAC,CAAC;AAC1D,oBAAoB,OAAO,CAAC,CAAC,GAAY,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;AAC1D,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACrE,oBAAoB,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACtF,oBAAoB,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;AACrE,oBAAoB,OAAO,GAAG,CAAC,IAAI,CAAC;AACpC,oBAAoB,MAAM,IAAI,KAAK,CAAC;AACpC,oBAAoB,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC9D,oBAAoB,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE;AACpC,wBAAwB,OAAO,GAAG,KAAK,CAAC,CAAC;AACzC,wBAAwB,EAAE,GAAG;AAC7B,4BAA4B,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;AAC/C,4BAA4B,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC9D,yBAAyB,EAAE,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3D,wBAAwB,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACxD,wBAAwB,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACpE,wBAAwB,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAChE,wBAAwB,IAAI,aAAa;AACzC,4BAA4B,aAAa,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;AAC5F,4BAA4B,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;AAC7G,yBAAyB;AACzB,wBAAwB,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChD,wBAAwB,IAAI,IAAI,EAAE;AAClC,4BAA4B,MAAM,GAAG,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACnE,4BAA4B,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;AAC9D,gCAAgC,MAAM,IAAI,MAAM;AAChD,gCAAgC,aAAa,IAAI,MAAM;AACvD,gCAAgC,QAAQ,IAAI,MAAM;AAClD,gCAAgC,SAAS,IAAI,MAAM,EAAE;AACrD,gCAAgC,IAAI,qBAAqB,CAAC,MAAM,CAAC,EAAE;AACnE,oCAAoC,IAAI,GAAG,EAAE,CAAC;AAC9C,oCAAoC,IAAI,SAAS,IAAI,MAAM,EAAE;AAC7D,wCAAwC,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE;AACzG,4CAA4C,OAAO,CAAC,CAAC,EAAY,CAAC;AAClE,yCAAyC;AACzC,wCAAwC,IAAI,GAAGC,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AAC5E,qCAAqC;AACrC,oCAAoC,IAAI,QAAQ,IAAI,MAAM,EAAE;AAC5D,wCAAwC,IAAI,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAEA,cAAQ,CAACA,cAAQ,CAAC,EAAE,GAAG,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAACC,6BAAsB,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAChO,qCAAqC;AACrC,oCAAoC,SAAS,CAAC,IAAI,CAAC,CAAC;AACpD,iCAAiC;AACjC,qCAAqC;AAGrC,oCAAoC,SAAS,CAAC,MAAM,CAAC,CAAC;AACtD,iCAAiC;AACjC,6BAA6B;AAC7B,iCAAiC;AAGjC,4BAA4B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;AAC5D,gCAAgC,SAAS,IAAI,MAAM;AACnD,gCAAgC,CAAC,MAAM,CAAC,OAAO,EAAE;AACjD,gCAAgC,OAAO,CAAC,CAAC,EAAY,CAAC;AACtD,6BAA6B;AAC7B,yBAAyB;AACzB,wBAAwB,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACtD,qBAAqB;AACrB,oBAAoB,OAAO,CAAC,CAAC,GAAY,CAAC,CAAC,CAAC;AAC5C,gBAAgB,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,EAAY,CAAC;AAC9C,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,CAAC;AACM,SAAS,YAAY,CAAC,UAAU,EAAE;AACzC,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC;AACzB,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACnD,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;AAEpB,YAAY,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;AAC/D,YAAY,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACjD,YAAY,WAAW,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;AACxC,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC;AACM,SAAS,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE;AAClD,IAAI,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE;AAEhC,QAAQ,IAAI,SAAS,GAAG,YAAY;AACpC,YAAY,IAAI;AAChB,gBAAgB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC5C,aAAa;AACb,YAAY,OAAO,GAAG,EAAE;AACxB,gBAAgB,OAAO,QAAQ,CAAC;AAChC,aAAa;AACb,SAAS,CAAC;AACV,QAAQC,sBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,gDAAgD,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC1H,KAAK;AACL,IAAI,IAAI;AACR,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,OAAO,GAAG,EAAE;AAChB,QAAQ,IAAI,UAAU,GAAG,GAAG,CAAC;AAC7B,QAAQ,UAAU,CAAC,IAAI,GAAG,kBAAkB,CAAC;AAC7C,QAAQ,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACvC,QAAQ,UAAU,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;AAChD,QAAQ,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACvC,QAAQ,MAAM,UAAU,CAAC;AACzB,KAAK;AACL,CAAC;AACM,SAAS,WAAW,CAAC,GAAG,EAAE,QAAQ,EAAE;AAM3C,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;AA4B5D,QAAQ,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClC,KAAK;AACL,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC;AACM,SAAS,yBAAyB,CAAC,UAAU,EAAE;AACtD,IAAI,OAAO,UAAU,QAAQ,EAAE;AAC/B,QAAQ,OAAO,QAAQ;AACvB,aAAa,IAAI,EAAE;AACnB,aAAa,IAAI,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC;AACpF,aAAa,IAAI,CAAC,UAAU,MAAM,EAAE;AACpC,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AACtC,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;AACpD,gBAAgB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;AAExD,gBAAgBA,sBAAgB,CAAC,QAAQ,EAAE,MAAM,EAAE,yCAAyC,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC;AAC7H,oBAAoB,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,aAAa,CAAC,EAAE,CAAC;AAC9E,sBAAsB,UAAU,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC,CAAC;AACvD,aAAa;AACb,YAAY,OAAO,MAAM,CAAC;AAC1B,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN;;AC3LU,IAAC,uBAAuB,GAAG,UAAU,CAAC,EAAE,KAAK,EAAE;AACzD,IAAI,IAAI,UAAU,CAAC;AACnB,IAAI,IAAI;AACR,QAAQ,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACvC,KAAK;AACL,IAAI,OAAO,CAAC,EAAE;AACd,QAAQ,IAAI,UAAU,GAAGC,yBAAiB,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;AACjE,QAAQ,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC;AAClC,QAAQ,MAAM,UAAU,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,UAAU,CAAC;AACtB;;ACVA,IAAI,kBAAkB,GAAG;AACzB,IAAI,YAAY,EAAE,IAAI;AACtB,IAAI,iBAAiB,EAAE,KAAK;AAC5B,IAAI,kBAAkB,EAAE,KAAK;AAC7B,CAAC,CAAC;AACF,IAAI,cAAc,GAAG;AAErB,IAAI,MAAM,EAAE,KAAK;AAajB,IAAI,cAAc,EAAE,kBAAkB;AACtC,CAAC,CAAC;AACF,IAAI,cAAc,GAAG;AACrB,IAAI,MAAM,EAAE,MAAM;AAClB,CAAC,CAAC;AACQ,IAAC,kBAAkB,GAAG;AAChC,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,OAAO,EAAE,cAAc;AAC3B,IAAI,OAAO,EAAE,cAAc;AAC3B,EAAE;AACQ,IAAC,cAAc,GAAG,UAAU,GAAG,EAAE,OAAO,EAAE,EAAE,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG;AACtE,SAAS,wBAAwB,CAAC,SAAS,EAAE,cAAc,EAAE;AACpE,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACpC,IAAI,OAAO,gCAAgC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAEC,mBAAa,CAAC,CAAC,SAAS;AAClF,QAAQ,cAAc,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AAC1C,CAAC;AACM,SAAS,gCAAgC,CAAC,SAAS,EAAE,OAAO,EAAE;AACrE,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;AACtC,QAAQ,OAAO,GAAGJ,cAAQ,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,EAAEA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AAClJ,QAAQ,IAAI,MAAM,CAAC,WAAW,EAAE;AAChC,YAAY,OAAO,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;AACrD,SAAS;AACT,QAAQ,IAAI,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;AACzD,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE;AACzB,QAAQ,OAAO,CAAC,OAAO,GAAG,sBAAsB,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC3F,KAAK;AAEL,IAAI,IAAI,aAAa,GAAG,SAAS,CAAC,aAAa,EAAE,UAAU,GAAG,SAAS,CAAC,UAAU,EAAE,SAAS,GAAG,SAAS,CAAC,SAAS,EAAE,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;AAC7I,IAAI,IAAI,IAAI,GAAG,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;AACtE,IAAI,IAAI,IAAI,CAAC,iBAAiB;AAC9B,QAAQ,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAErC,IAAI,IAAI,IAAI,CAAC,YAAY;AACzB,QAAQ,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,EAAEK,eAAK,CAAC,CAAC;AAC3C,IAAI,OAAO;AACX,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,IAAI,EAAE,IAAI;AAClB,KAAK,CAAC;AACN,CAAC;AAID,SAAS,sBAAsB,CAAC,OAAO,EAAE,kBAAkB,EAAE;AAE7D,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC7B,QAAQ,IAAI,mBAAmB,GAAG,EAAE,CAAC;AACrC,QAAQ,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AAC7D,YAAY,mBAAmB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACpE,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,mBAAmB,CAAC;AACnC,KAAK;AAKL,IAAI,IAAI,UAAU,GAAG,EAAE,CAAC;AACxB,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACzD,QAAQ,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG;AACzC,YAAY,YAAY,EAAE,IAAI;AAC9B,YAAY,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC;AAChC,SAAS,CAAC;AACV,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,iBAAiB,GAAG,EAAE,CAAC;AAC/B,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACpD,QAAQ,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;AAClF,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,iBAAiB,CAAC;AAC7B;;ACnGU,IAAC,YAAY,GAAG,UAAU,OAAO,EAAE;AAC7C,IAAI,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE;AAClD,QAAQ,MAAMF,yBAAiB,CAAC,EAAE,CAAC,CAAC;AACpC,KAAK;AACL;;ACAU,IAAC,uBAAuB,GAAG,YAAY;AACjD,IAAI,IAAI,OAAO,eAAe,KAAK,WAAW;AAC9C,QAAQ,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AACpD,IAAI,IAAI,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;AAC3C,IAAI,IAAI,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AACnC,IAAI,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AACtD;;ACXU,IAAC,SAAS,GAAG,UAAU,SAAS,EAAE,WAAW,EAAE;AACzD,IAAI,IAAI,OAAO,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AACzC,IAAI,IAAI,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC;AACjC,IAAI,IAAI,UAAU,EAAE;AACpB,QAAQ,OAAO,UAAU,CAAC;AAC1B,KAAK;AACL,SAAS,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;AAChD,QAAQ,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;AACtC,KAAK;AACL,SAAS;AACT,QAAQ,OAAO,WAAW,IAAI,UAAU,CAAC;AACzC,KAAK;AACL;;ACTO,SAAS,gBAAgB,CAAC,SAAS,EAAE,IAAI,EAAE;AAGlD,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC;AACzB,IAAI,IAAI,aAAa,GAAG,UAAU,GAAG,EAAE,KAAK,EAAE;AAC9C,QAAQ,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChF,KAAK,CAAC;AACN,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE;AACzB,QAAQ,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5B,QAAQ,aAAa,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACxB,QAAQ,IAAI,mBAAmB,GAAG,KAAK,CAAC,CAAC;AACzC,QAAQ,IAAI;AACZ,YAAY,mBAAmB,GAAG,uBAAuB,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;AAC3F,SAAS;AACT,QAAQ,OAAO,UAAU,EAAE;AAC3B,YAAY,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;AAC9C,SAAS;AACT,QAAQ,aAAa,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;AACxD,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;AACzB,QAAQ,IAAI,oBAAoB,GAAG,KAAK,CAAC,CAAC;AAC1C,QAAQ,IAAI;AACZ,YAAY,oBAAoB,GAAG,uBAAuB,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;AAC9F,SAAS;AACT,QAAQ,OAAO,UAAU,EAAE;AAC3B,YAAY,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC;AAC9C,SAAS;AACT,QAAQ,aAAa,CAAC,YAAY,EAAE,oBAAoB,CAAC,CAAC;AAC1D,KAAK;AAOL,IAAI,IAAI,QAAQ,GAAG,EAAE,EAAE,WAAW,GAAG,SAAS,CAAC;AAC/C,IAAI,IAAI,aAAa,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/C,IAAI,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE;AAC9B,QAAQ,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACnD,QAAQ,WAAW,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;AACzD,KAAK;AACL,IAAI,IAAI,iBAAiB,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;AACxE,IAAI,IAAI,MAAM,GAAG,WAAW,GAAG,iBAAiB,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;AACpF,IAAI,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC9B;;ACvCA,IAAI,WAAW,GAAGG,eAAK,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC,CAAC;AAC7C,IAAC,cAAc,GAAG,UAAU,WAAW,EAAE;AACnD,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,EAAE;AACrD,IAAI,IAAI,EAAE,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,GAAG,EAAE;AAEnE,IAAI,cAAc,GAAG,WAAW,CAAC,KAAK,EAAE,EAAE,GAAG,WAAW,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,cAAc,GAAG,EAAE,EAAE,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,EAAE,kBAAkB,GAAG,WAAW,CAAC,kBAAkB,EAAE,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,EAAE,EAAE,GAAG,WAAW,CAAC,sBAAsB,EAAE,sBAAsB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,EAAE,cAAc,GAAGC,YAAM,CAAC,WAAW,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,wBAAwB,CAAC,CAAC,CAAC;AAC7f,IAAI,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAGtC,QAAQ,YAAY,CAAC,cAAc,IAAI,WAAW,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,IAAI,UAAU,GAAG;AACrB,QAAQ,IAAI,EAAE,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE;AAC9F,QAAQ,OAAO,EAAE,cAAc,CAAC,YAAY;AAC5C,QAAQ,WAAW,EAAE,cAAc,CAAC,WAAW;AAC/C,QAAQ,OAAO,EAAE,cAAc,CAAC,OAAO;AACvC,KAAK,CAAC;AACN,IAAI,OAAO,IAAIC,eAAU,CAAC,UAAU,SAAS,EAAE;AAC/C,QAAQ,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;AAClD,QAAQ,IAAI,OAAO,GAAG,SAAS,CAAC,UAAU,EAAE,CAAC;AAO7C,QAAQ,IAAI,sBAAsB,GAAG,EAAE,CAAC;AACxC,QAAQ,IAAI,OAAO,CAAC,eAAe,EAAE;AACrC,YAAY,IAAI,EAAE,GAAG,OAAO,CAAC,eAAe,EAAE,MAAM,GAAG,EAAE,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AACrF,YAAY,IAAI,MAAM,EAAE;AACxB,gBAAgB,sBAAsB,CAAC,2BAA2B,CAAC,GAAG,MAAM,CAAC;AAC7E,aAAa;AACb,YAAY,IAAI,OAAO,EAAE;AACzB,gBAAgB,sBAAsB,CAAC,8BAA8B,CAAC,GAAG,OAAO,CAAC;AACjF,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,cAAc,GAAGR,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,sBAAsB,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;AAC7F,QAAQ,IAAI,aAAa,GAAG;AAC5B,YAAY,IAAI,EAAE,OAAO,CAAC,IAAI;AAC9B,YAAY,OAAO,EAAE,OAAO,CAAC,YAAY;AACzC,YAAY,WAAW,EAAE,OAAO,CAAC,WAAW;AAC5C,YAAY,OAAO,EAAE,cAAc;AACnC,SAAS,CAAC;AACV,QAAQ,IAAIS,uBAAa,CAAC,CAAC,QAAQ,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,EAAE;AACxD,YAAY,IAAI,gBAAgB,GAAGC,sCAA4B,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACjF,YAAY,IAAI,CAAC,gBAAgB,EAAE;AACnC,gBAAgB,OAAOC,eAAS,CAAC,IAAI,KAAK,CAAC,uMAAuM,CAAC,CAAC,CAAC;AACrP,aAAa;AACb,YAAY,SAAS,CAAC,KAAK,GAAG,gBAAgB,CAAC;AAC/C,SAAS;AAET,QAAQ,IAAI,EAAE,GAAG,gCAAgC,CAAC,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,aAAa,CAAC,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACzJ,QAAQ,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,sBAAsB,EAAE;AACvD,YAAY,IAAI,CAAC,SAAS,GAAGC,8BAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;AACvF,SAAS;AACT,QAAQ,IAAI,UAAU,CAAC;AACvB,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,eAAe,KAAK,WAAW,EAAE;AACvE,YAAY,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;AAC/C,YAAY,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AAC/C,SAAS;AAET,QAAQ,IAAI,oBAAoB,GAAG,UAAU,CAAC,EAAE;AAChD,YAAY,OAAO,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,UAAU,CAAC;AAClF,SAAS,CAAC;AACV,QAAQ,IAAI,wBAAwB,GAAG,UAAU,CAAC,EAAE;AACpD,YAAY,OAAO,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,cAAc,CAAC;AACtF,SAAS,CAAC;AACV,QAAQ,IAAI,cAAc,GAAG,wBAAwB,CAACC,2BAAiB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AAE1F,QAAQ,IAAI,QAAQ,GAAGJ,uBAAa,CAAC,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;AACjE,QAAQ,IAAI,gBAAgB;AAC5B,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;AACrE,YAAY,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;AACnC,SAAS;AACT,QAAQ,IAAI,QAAQ,IAAI,cAAc,EAAE;AACxC,YAAY,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC;AACpD,YAAY,IAAI,YAAY,GAAG,kBAAkB,CAAC;AAGlD,YAAY,IAAI,cAAc,IAAI,QAAQ,EAAE;AAC5C,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIK,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnE,aAAa;AACb,YAAY,IAAI,cAAc,EAAE;AAChC,gBAAgB,YAAY;AAC5B,oBAAoB,wDAAwD,CAAC;AAC7E,aAAa;AACb,iBAAiB,IAAI,QAAQ,EAAE;AAC/B,gBAAgB,YAAY,IAAI,qCAAqC,CAAC;AACtE,aAAa;AACb,YAAY,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,YAAY,CAAC;AAClD,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;AACtC,YAAY,IAAI,EAAE,GAAG,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC;AACvG,YAAY,IAAI,UAAU,EAAE;AAC5B,gBAAgB,OAAOH,eAAS,CAAC,UAAU,CAAC,CAAC;AAC7C,aAAa;AACb,YAAY,SAAS,GAAG,MAAM,CAAC;AAC/B,SAAS;AACT,aAAa;AACb,YAAY,IAAI;AAChB,gBAAgB,OAAO,CAAC,IAAI,GAAG,uBAAuB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACxE,aAAa;AACb,YAAY,OAAO,UAAU,EAAE;AAC/B,gBAAgB,OAAOA,eAAS,CAAC,UAAU,CAAC,CAAC;AAC7C,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAII,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAMlD,YAAY,IAAI,YAAY,GAAG,cAAc,IAAIT,eAAK,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI,WAAW,CAAC;AACrG,YAAY,IAAI,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5D,YAAY,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC;AAC5C,iBAAiB,IAAI,CAAC,UAAU,QAAQ,EAAE;AAC1C,gBAAgB,IAAI,EAAE,CAAC;AACvB,gBAAgB,SAAS,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC7D,gBAAgB,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAChH,gBAAgB,IAAI,KAAK,KAAK,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACxE,oBAAoB,OAAO,iBAAiB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACrE,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,OAAO,yBAAyB,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC7F,iBAAiB;AACjB,aAAa,CAAC;AACd,iBAAiB,IAAI,CAAC,YAAY;AAClC,gBAAgB,UAAU,GAAG,SAAS,CAAC;AACvC,gBAAgB,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACpC,aAAa,CAAC;AACd,iBAAiB,KAAK,CAAC,UAAU,GAAG,EAAE;AACtC,gBAAgB,UAAU,GAAG,SAAS,CAAC;AACvC,gBAAgB,WAAW,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC3C,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,YAAY;AAG/B,gBAAgB,IAAI,UAAU;AAC9B,oBAAoB,UAAU,CAAC,KAAK,EAAE,CAAC;AACvC,aAAa,CAAC;AACd,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP;;ACvJG,IAAC,QAAQ,KAAkB,UAAU,MAAM,EAAE;AAChD,IAAIU,eAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAChC,IAAI,SAAS,QAAQ,CAAC,OAAO,EAAE;AAC/B,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AACjD,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AAC/E,QAAQ,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC,CAACR,eAAU,CAAC;;;;;;;;;;;;;;;"}