{"version": 3, "file": "serializeFetchParameter.js", "sourceRoot": "", "sources": ["../../../src/link/http/serializeFetchParameter.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AAOrE,MAAM,CAAC,IAAM,uBAAuB,GAAG,UAAC,CAAM,EAAE,KAAa;IAC3D,IAAI,UAAU,CAAC;IACf,IAAI,CAAC;QACH,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,IAAM,UAAU,GAAG,iBAAiB,CAClC,oDAAoD,EACpD,KAAK,EACL,CAAC,CAAC,OAAO,CACU,CAAC;QACtB,UAAU,CAAC,UAAU,GAAG,CAAC,CAAC;QAC1B,MAAM,UAAU,CAAC;IACnB,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC", "sourcesContent": ["import { newInvariantError } from \"../../utilities/globals/index.js\";\nimport type { InvariantError } from \"../../utilities/globals/index.js\";\n\nexport type ClientParseError = InvariantError & {\n  parseError: Error;\n};\n\nexport const serializeFetchParameter = (p: any, label: string) => {\n  let serialized;\n  try {\n    serialized = JSON.stringify(p);\n  } catch (e: any) {\n    const parseError = newInvariantError(\n      `Network request failed. %s is not serializable: %s`,\n      label,\n      e.message\n    ) as ClientParseError;\n    parseError.parseError = e;\n    throw parseError;\n  }\n  return serialized;\n};\n"]}