{"version": 3, "file": "parseAndCheckHttpResponse.js", "sourceRoot": "", "sources": ["../../../src/link/http/parseAndCheckHttpResponse.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAEzD,OAAO,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAC;AACrD,OAAO,EAAE,sBAAsB,EAAE,MAAM,uBAAuB,CAAC;AAC/D,OAAO,EAAE,qBAAqB,EAAE,MAAM,6CAA6C,CAAC;AAG5E,IAAA,cAAc,GAAK,MAAM,CAAC,SAAS,eAArB,CAAsB;AAQ5C,MAAM,UAAgB,iBAAiB,CAErC,QAAkB,EAAE,SAA6B;;;;;;;;oBACjD,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;wBAC9B,MAAM,IAAI,KAAK,CACb,2EAA2E,CAC5E,CAAC;oBACJ,CAAC;oBACK,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;oBACnC,WAAW,GAAG,MAAA,QAAQ,CAAC,OAAO,0CAAE,GAAG,CAAC,cAAc,CAAC,CAAC;oBACpD,SAAS,GAAG,WAAW,CAAC;oBAMxB,WAAW,GACf,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ,CAAC,SAAS,CAAC,EAAC,CAAC;wBAChC,WAAW,aAAX,WAAW,uBAAX,WAAW,CACP,SAAS,CAAC,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,OAAO,CAAC,SAAS,CAAC,IAAG,SAAS,CAAC,MAAM,EAC7D,OAAO,CAAC,OAAO,EAAE,EAAE,EACnB,OAAO,CAAC,UAAU,EAAE,EAAE,EACtB,IAAI,EAAE;wBACX,CAAC,CAAC,GAAG,CAAC;oBAEF,QAAQ,GAAG,gBAAS,WAAW,CAAE,CAAC;oBACpC,MAAM,GAAG,EAAE,CAAC;oBACV,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;oBACxC,OAAO,GAAG,IAAI,CAAC;;;yBAEZ,OAAO;oBACY,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;oBAAvC,KAAkB,SAAqB,EAArC,KAAK,WAAA,EAAE,IAAI,UAAA;oBACb,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAClE,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;oBACvD,OAAO,GAAG,CAAC,IAAI,CAAC;oBAChB,MAAM,IAAI,KAAK,CAAC;oBACZ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;oBAE9C,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;wBACX,OAAO,SAAQ,CAAC;wBACpB,KAAoB;4BAClB,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;4BACnB,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC;yBACnC,EAHA,OAAO,QAAA,EAAE,MAAM,QAAA,CAGd;wBACI,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;wBAChC,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;wBAC5C,gBAAc,OAAO,CAAC,cAAc,CAAC,CAAC;wBAC5C,IACE,aAAW;4BACX,aAAW,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAC5D,CAAC;4BACD,MAAM,IAAI,KAAK,CACb,+DAA+D,CAChE,CAAC;wBACJ,CAAC;wBAGK,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAE9B,IAAI,IAAI,EAAE,CAAC;4BACH,MAAM,GAAG,aAAa,CAAI,QAAQ,EAAE,IAAI,CAAC,CAAC;4BAChD,IACE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;gCAC9B,MAAM,IAAI,MAAM;gCAChB,aAAa,IAAI,MAAM;gCACvB,QAAQ,IAAI,MAAM;gCAClB,SAAS,IAAI,MAAM,EACnB,CAAC;gCACD,IAAI,qBAAqB,CAAC,MAAM,CAAC,EAAE,CAAC;oCAC9B,IAAI,GAAG,EAAE,CAAC;oCACd,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;wCACxB,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;4CAChE,sBAAO;wCACT,CAAC;wCACD,IAAI,gBAAQ,MAAM,CAAC,OAAO,CAAE,CAAC;oCAC/B,CAAC;oCACD,IAAI,QAAQ,IAAI,MAAM,EAAE,CAAC;wCACvB,IAAI,yBACC,IAAI,KACP,UAAU,wBACL,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAE,IAAY,CAAC,gBAC1D,sBAAsB,IAAG,MAAM,CAAC,MAAM,SAE1C,CAAC;oCACJ,CAAC;oCACD,SAAS,CAAC,IAAS,CAAC,CAAC;gCACvB,CAAC;qCAAM,CAAC;oCACN,gDAAgD;oCAChD,iEAAiE;oCACjE,SAAS,CAAC,MAAM,CAAC,CAAC;gCACpB,CAAC;4BACH,CAAC;iCAAM;4BACL,6DAA6D;4BAC7D,mCAAmC;4BACnC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;gCAChC,SAAS,IAAI,MAAM;gCACnB,CAAC,MAAM,CAAC,OAAO,EACf,CAAC;gCACD,sBAAO;4BACT,CAAC;wBACH,CAAC;wBACD,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAChC,CAAC;;;;;;CAEJ;AAED,MAAM,UAAU,YAAY,CAAC,UAAkB;IAC7C,IAAM,WAAW,GAA2B,EAAE,CAAC;IAC/C,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;QAClC,IAAM,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;YACX,iCAAiC;YACjC,IAAM,MAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;YACnD,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YACvC,WAAW,CAAC,MAAI,CAAC,GAAG,KAAK,CAAC;QAC5B,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,MAAM,UAAU,aAAa,CAAI,QAAkB,EAAE,QAAgB;IACnE,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;QAC3B,gBAAgB;QAChB,IAAM,SAAS,GAAG;YAChB,IAAI,CAAC;gBACH,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YAC9B,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC,CAAC;QACF,gBAAgB,CACd,QAAQ,EACR,SAAS,EAAE,EACX,wDAAiD,QAAQ,CAAC,MAAM,CAAE,CACnE,CAAC;IACJ,CAAC;IAED,IAAI,CAAC;QACH,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAM,CAAC;IACnC,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAM,UAAU,GAAG,GAAuB,CAAC;QAC3C,UAAU,CAAC,IAAI,GAAG,kBAAkB,CAAC;QACrC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC/B,UAAU,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;QACxC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC/B,MAAM,UAAU,CAAC;IACnB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,GAAQ,EAAE,QAAmC;IACvE,kEAAkE;IAClE,kEAAkE;IAClE,gEAAgE;IAChE,+DAA+D;IAC/D,wCAAwC;IACxC,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACvD,2DAA2D;QAC3D,0DAA0D;QAC1D,+DAA+D;QAC/D,iEAAiE;QACjE,iEAAiE;QACjE,iEAAiE;QACjE,SAAS;QACT,IAAI;QACJ,mCAAmC;QACnC,eAAe;QACf,KAAK;QACL,kDAAkD;QAClD,UAAU;QACV,KAAK;QACL,IAAI;QACJ,EAAE;QACF,8CAA8C;QAC9C,IAAI;QACJ,2CAA2C;QAC3C,eAAe;QACf,8CAA8C;QAC9C,gBAAgB;QAChB,OAAO;QACP,IAAI;QACJ,sCAAsC;QACtC,8EAA8E;QAC9E,oCAAoC;QACpC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC5B,CAAC;IAED,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACtB,CAAC;AAED,MAAM,UAAU,yBAAyB,CAAC,UAAmC;IAC3E,OAAO,UAAC,QAAkB;QACxB,OAAA,QAAQ;aACL,IAAI,EAAE;aACN,IAAI,CAAC,UAAC,QAAQ,IAAK,OAAA,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAjC,CAAiC,CAAC;aACrD,IAAI,CAAC,UAAC,MAAW;YAChB,IACE,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACtB,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;gBACpC,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,EACtC,CAAC;gBACD,aAAa;gBACb,gBAAgB,CACd,QAAQ,EACR,MAAM,EACN,iDACE,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;oBACzB,UAAU,CAAC,GAAG,CAAC,UAAC,EAAE,IAAK,OAAA,EAAE,CAAC,aAAa,EAAhB,CAAgB,CAAC;oBAC1C,CAAC,CAAC,UAAU,CAAC,aAAa,OACxB,CACL,CAAC;YACJ,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC;IArBJ,CAqBI,CAAC;AACT,CAAC", "sourcesContent": ["import { responseIterator } from \"./responseIterator.js\";\nimport type { Operation } from \"../core/index.js\";\nimport { throwServerError } from \"../utils/index.js\";\nimport { PROTOCOL_ERRORS_SYMBOL } from \"../../errors/index.js\";\nimport { isApolloPayloadResult } from \"../../utilities/common/incrementalResult.js\";\nimport type { SubscriptionObserver } from \"zen-observable-ts\";\n\nconst { hasOwnProperty } = Object.prototype;\n\nexport type ServerParseError = Error & {\n  response: Response;\n  statusCode: number;\n  bodyText: string;\n};\n\nexport async function readMultipartBody<\n  T extends object = Record<string, unknown>,\n>(response: Response, nextValue: (value: T) => void) {\n  if (TextDecoder === undefined) {\n    throw new Error(\n      \"TextDecoder must be defined in the environment: please import a polyfill.\"\n    );\n  }\n  const decoder = new TextDecoder(\"utf-8\");\n  const contentType = response.headers?.get(\"content-type\");\n  const delimiter = \"boundary=\";\n\n  // parse boundary value and ignore any subsequent name/value pairs after ;\n  // https://www.rfc-editor.org/rfc/rfc9110.html#name-parameters\n  // e.g. multipart/mixed;boundary=\"graphql\";deferSpec=20220824\n  // if no boundary is specified, default to -\n  const boundaryVal =\n    contentType?.includes(delimiter) ?\n      contentType\n        ?.substring(contentType?.indexOf(delimiter) + delimiter.length)\n        .replace(/['\"]/g, \"\")\n        .replace(/\\;(.*)/gm, \"\")\n        .trim()\n    : \"-\";\n\n  const boundary = `\\r\\n--${boundaryVal}`;\n  let buffer = \"\";\n  const iterator = responseIterator(response);\n  let running = true;\n\n  while (running) {\n    const { value, done } = await iterator.next();\n    const chunk = typeof value === \"string\" ? value : decoder.decode(value);\n    const searchFrom = buffer.length - boundary.length + 1;\n    running = !done;\n    buffer += chunk;\n    let bi = buffer.indexOf(boundary, searchFrom);\n\n    while (bi > -1) {\n      let message: string;\n      [message, buffer] = [\n        buffer.slice(0, bi),\n        buffer.slice(bi + boundary.length),\n      ];\n      const i = message.indexOf(\"\\r\\n\\r\\n\");\n      const headers = parseHeaders(message.slice(0, i));\n      const contentType = headers[\"content-type\"];\n      if (\n        contentType &&\n        contentType.toLowerCase().indexOf(\"application/json\") === -1\n      ) {\n        throw new Error(\n          \"Unsupported patch content type: application/json is required.\"\n        );\n      }\n      // nb: Technically you'd want to slice off the beginning \"\\r\\n\" but since\n      // this is going to be `JSON.parse`d there is no need.\n      const body = message.slice(i);\n\n      if (body) {\n        const result = parseJsonBody<T>(response, body);\n        if (\n          Object.keys(result).length > 1 ||\n          \"data\" in result ||\n          \"incremental\" in result ||\n          \"errors\" in result ||\n          \"payload\" in result\n        ) {\n          if (isApolloPayloadResult(result)) {\n            let next = {};\n            if (\"payload\" in result) {\n              if (Object.keys(result).length === 1 && result.payload === null) {\n                return;\n              }\n              next = { ...result.payload };\n            }\n            if (\"errors\" in result) {\n              next = {\n                ...next,\n                extensions: {\n                  ...(\"extensions\" in next ? next.extensions : (null as any)),\n                  [PROTOCOL_ERRORS_SYMBOL]: result.errors,\n                },\n              };\n            }\n            nextValue(next as T);\n          } else {\n            // for the last chunk with only `hasNext: false`\n            // we don't need to call observer.next as there is no data/errors\n            nextValue(result);\n          }\n        } else if (\n          // If the chunk contains only a \"hasNext: false\", we can call\n          // observer.complete() immediately.\n          Object.keys(result).length === 1 &&\n          \"hasNext\" in result &&\n          !result.hasNext\n        ) {\n          return;\n        }\n      }\n      bi = buffer.indexOf(boundary);\n    }\n  }\n}\n\nexport function parseHeaders(headerText: string): Record<string, string> {\n  const headersInit: Record<string, string> = {};\n  headerText.split(\"\\n\").forEach((line) => {\n    const i = line.indexOf(\":\");\n    if (i > -1) {\n      // normalize headers to lowercase\n      const name = line.slice(0, i).trim().toLowerCase();\n      const value = line.slice(i + 1).trim();\n      headersInit[name] = value;\n    }\n  });\n  return headersInit;\n}\n\nexport function parseJsonBody<T>(response: Response, bodyText: string): T {\n  if (response.status >= 300) {\n    // Network error\n    const getResult = (): Record<string, unknown> | string => {\n      try {\n        return JSON.parse(bodyText);\n      } catch (err) {\n        return bodyText;\n      }\n    };\n    throwServerError(\n      response,\n      getResult(),\n      `Response not successful: Received status code ${response.status}`\n    );\n  }\n\n  try {\n    return JSON.parse(bodyText) as T;\n  } catch (err) {\n    const parseError = err as ServerParseError;\n    parseError.name = \"ServerParseError\";\n    parseError.response = response;\n    parseError.statusCode = response.status;\n    parseError.bodyText = bodyText;\n    throw parseError;\n  }\n}\n\nexport function handleError(err: any, observer: SubscriptionObserver<any>) {\n  // if it is a network error, BUT there is graphql result info fire\n  // the next observer before calling error this gives apollo-client\n  // (and react-apollo) the `graphqlErrors` and `networkErrors` to\n  // pass to UI this should only happen if we *also* have data as\n  // part of the response key per the spec\n  if (err.result && err.result.errors && err.result.data) {\n    // if we don't call next, the UI can only show networkError\n    // because AC didn't get any graphqlErrors this is graphql\n    // execution result info (i.e errors and possibly data) this is\n    // because there is no formal spec how errors should translate to\n    // http status codes. So an auth error (401) could have both data\n    // from a public field, errors from a private field, and a status\n    // of 401\n    // {\n    //  user { // this will have errors\n    //    firstName\n    //  }\n    //  products { // this is public so will have data\n    //    cost\n    //  }\n    // }\n    //\n    // the result of above *could* look like this:\n    // {\n    //   data: { products: [{ cost: \"$10\" }] },\n    //   errors: [{\n    //      message: 'your session has timed out',\n    //      path: []\n    //   }]\n    // }\n    // status code of above would be a 401\n    // in the UI you want to show data where you can, errors as data where you can\n    // and use correct http status codes\n    observer.next(err.result);\n  }\n\n  observer.error(err);\n}\n\nexport function parseAndCheckHttpResponse(operations: Operation | Operation[]) {\n  return (response: Response) =>\n    response\n      .text()\n      .then((bodyText) => parseJsonBody(response, bodyText))\n      .then((result: any) => {\n        if (\n          !Array.isArray(result) &&\n          !hasOwnProperty.call(result, \"data\") &&\n          !hasOwnProperty.call(result, \"errors\")\n        ) {\n          // Data error\n          throwServerError(\n            response,\n            result,\n            `Server response was missing for query '${\n              Array.isArray(operations) ?\n                operations.map((op) => op.operationName)\n              : operations.operationName\n            }'.`\n          );\n        }\n        return result;\n      });\n}\n"]}