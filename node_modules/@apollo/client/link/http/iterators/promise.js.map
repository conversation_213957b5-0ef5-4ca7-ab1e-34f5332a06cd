{"version": 3, "file": "promise.js", "sourceRoot": "", "sources": ["../../../../src/link/http/iterators/promise.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAOxE,MAAM,CAAC,OAAO,UAAU,eAAe,CACrC,OAA6B;IAE7B,IAAI,QAAQ,GAAG,KAAK,CAAC;IAErB,IAAM,QAAQ,GAAuB;QACnC,IAAI;YACF,IAAI,QAAQ;gBACV,OAAO,OAAO,CAAC,OAAO,CAAC;oBACrB,KAAK,EAAE,SAAS;oBAChB,IAAI,EAAE,IAAI;iBACX,CAAC,CAAC;YACL,QAAQ,GAAG,IAAI,CAAC;YAChB,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM;gBAC1C,OAAO;qBACJ,IAAI,CAAC,UAAU,KAAK;oBACnB,OAAO,CAAC,EAAE,KAAK,EAAE,KAAqB,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;gBACzD,CAAC,CAAC;qBACD,KAAK,CAAC,MAAM,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;QACL,CAAC;KACF,CAAC;IAEF,IAAI,yBAAyB,EAAE,CAAC;QAC9B,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG;YAC/B,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,QAAoC,CAAC;AAC9C,CAAC", "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/promise.ts\n */\n\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\n\ninterface PromiseIterator<T> {\n  next(): Promise<IteratorResult<T, ArrayBuffer | undefined>>;\n  [Symbol.asyncIterator]?(): AsyncIterator<T>;\n}\n\nexport default function promiseIterator<T = ArrayBuffer>(\n  promise: Promise<ArrayBuffer>\n): AsyncIterableIterator<T> {\n  let resolved = false;\n\n  const iterator: PromiseIterator<T> = {\n    next(): Promise<IteratorResult<T, ArrayBuffer | undefined>> {\n      if (resolved)\n        return Promise.resolve({\n          value: undefined,\n          done: true,\n        });\n      resolved = true;\n      return new Promise(function (resolve, reject) {\n        promise\n          .then(function (value) {\n            resolve({ value: value as unknown as T, done: false });\n          })\n          .catch(reject);\n      });\n    },\n  };\n\n  if (canUseAsyncIteratorSymbol) {\n    iterator[Symbol.asyncIterator] = function (): AsyncIterator<T> {\n      return this;\n    };\n  }\n\n  return iterator as AsyncIterableIterator<T>;\n}\n"]}