{"version": 3, "file": "async.js", "sourceRoot": "", "sources": ["../../../../src/link/http/iterators/async.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,MAAM,CAAC,OAAO,UAAU,aAAa,CACnC,MAAgC;;IAEhC,IAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;IAChD;YACE,IAAI;gBACF,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzB,CAAC;;QACD,GAAC,MAAM,CAAC,aAAa,IAArB;YACE,OAAO,IAAI,CAAC;QACd,CAAC;WACD;AACJ,CAAC", "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/async.ts\n */\n\nexport default function asyncIterator<T>(\n  source: AsyncIterableIterator<T>\n): AsyncIterableIterator<T> {\n  const iterator = source[Symbol.asyncIterator]();\n  return {\n    next(): Promise<IteratorResult<T, boolean>> {\n      return iterator.next();\n    },\n    [Symbol.asyncIterator](): AsyncIterableIterator<T> {\n      return this;\n    },\n  };\n}\n"]}