{"version": 3, "file": "batch.cjs", "sources": ["batching.js", "batchLink.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { Observable } from \"../../utilities/index.js\";\n// QueryBatcher doesn't fire requests immediately. Requests that were enqueued within\n// a certain amount of time (configurable through `batchInterval`) will be batched together\n// into one query.\nvar OperationBatcher = /** @class */ (function () {\n    function OperationBatcher(_a) {\n        var batchDebounce = _a.batchDebounce, batchInterval = _a.batchInterval, batchMax = _a.batchMax, batchHandler = _a.batchHandler, batchKey = _a.batchKey;\n        // Queue on which the QueryBatcher will operate on a per-tick basis.\n        this.batchesByKey = new Map();\n        this.scheduledBatchTimerByKey = new Map();\n        this.batchDebounce = batchDebounce;\n        this.batchInterval = batchInterval;\n        this.batchMax = batchMax || 0;\n        this.batchHandler = batchHandler;\n        this.batchKey = batchKey || (function () { return \"\"; });\n    }\n    OperationBatcher.prototype.enqueueRequest = function (request) {\n        var _this = this;\n        var requestCopy = __assign(__assign({}, request), { next: [], error: [], complete: [], subscribers: new Set() });\n        var key = this.batchKey(request.operation);\n        if (!requestCopy.observable) {\n            requestCopy.observable = new Observable(function (observer) {\n                var batch = _this.batchesByKey.get(key);\n                if (!batch)\n                    _this.batchesByKey.set(key, (batch = new Set()));\n                // These booleans seem to me (@benjamn) like they might always be the\n                // same (and thus we could do with only one of them), but I'm not 100%\n                // sure about that.\n                var isFirstEnqueuedRequest = batch.size === 0;\n                var isFirstSubscriber = requestCopy.subscribers.size === 0;\n                requestCopy.subscribers.add(observer);\n                if (isFirstSubscriber) {\n                    batch.add(requestCopy);\n                }\n                // called for each subscriber, so need to save all listeners (next, error, complete)\n                if (observer.next) {\n                    requestCopy.next.push(observer.next.bind(observer));\n                }\n                if (observer.error) {\n                    requestCopy.error.push(observer.error.bind(observer));\n                }\n                if (observer.complete) {\n                    requestCopy.complete.push(observer.complete.bind(observer));\n                }\n                // The first enqueued request triggers the queue consumption after `batchInterval` milliseconds.\n                if (isFirstEnqueuedRequest || _this.batchDebounce) {\n                    _this.scheduleQueueConsumption(key);\n                }\n                // When amount of requests reaches `batchMax`, trigger the queue consumption without waiting on the `batchInterval`.\n                if (batch.size === _this.batchMax) {\n                    _this.consumeQueue(key);\n                }\n                return function () {\n                    var _a;\n                    // If this is last subscriber for this request, remove request from queue\n                    if (requestCopy.subscribers.delete(observer) &&\n                        requestCopy.subscribers.size < 1) {\n                        // If this is last request from queue, remove queue entirely\n                        if (batch.delete(requestCopy) && batch.size < 1) {\n                            _this.consumeQueue(key);\n                            // If queue was in flight, cancel it\n                            (_a = batch.subscription) === null || _a === void 0 ? void 0 : _a.unsubscribe();\n                        }\n                    }\n                };\n            });\n        }\n        return requestCopy.observable;\n    };\n    // Consumes the queue.\n    // Returns a list of promises (one for each query).\n    OperationBatcher.prototype.consumeQueue = function (key) {\n        if (key === void 0) { key = \"\"; }\n        var batch = this.batchesByKey.get(key);\n        // Delete this batch and process it below.\n        this.batchesByKey.delete(key);\n        if (!batch || !batch.size) {\n            // No requests to be processed.\n            return;\n        }\n        var operations = [];\n        var forwards = [];\n        var observables = [];\n        var nexts = [];\n        var errors = [];\n        var completes = [];\n        // Even though batch is a Set, it preserves the order of first insertion\n        // when iterating (per ECMAScript specification), so these requests will be\n        // handled in the order they were enqueued (minus any deleted ones).\n        batch.forEach(function (request) {\n            operations.push(request.operation);\n            forwards.push(request.forward);\n            observables.push(request.observable);\n            nexts.push(request.next);\n            errors.push(request.error);\n            completes.push(request.complete);\n        });\n        var batchedObservable = this.batchHandler(operations, forwards) || Observable.of();\n        var onError = function (error) {\n            //each callback list in batch\n            errors.forEach(function (rejecters) {\n                if (rejecters) {\n                    //each subscriber to request\n                    rejecters.forEach(function (e) { return e(error); });\n                }\n            });\n        };\n        batch.subscription = batchedObservable.subscribe({\n            next: function (results) {\n                if (!Array.isArray(results)) {\n                    results = [results];\n                }\n                if (nexts.length !== results.length) {\n                    var error = new Error(\"server returned results with length \".concat(results.length, \", expected length of \").concat(nexts.length));\n                    error.result = results;\n                    return onError(error);\n                }\n                results.forEach(function (result, index) {\n                    if (nexts[index]) {\n                        nexts[index].forEach(function (next) { return next(result); });\n                    }\n                });\n            },\n            error: onError,\n            complete: function () {\n                completes.forEach(function (complete) {\n                    if (complete) {\n                        //each subscriber to request\n                        complete.forEach(function (c) { return c(); });\n                    }\n                });\n            },\n        });\n        return observables;\n    };\n    OperationBatcher.prototype.scheduleQueueConsumption = function (key) {\n        var _this = this;\n        clearTimeout(this.scheduledBatchTimerByKey.get(key));\n        this.scheduledBatchTimerByKey.set(key, setTimeout(function () {\n            _this.consumeQueue(key);\n            _this.scheduledBatchTimerByKey.delete(key);\n        }, this.batchInterval));\n    };\n    return OperationBatcher;\n}());\nexport { OperationBatcher };\n//# sourceMappingURL=batching.js.map", "import { __extends } from \"tslib\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { OperationBatcher } from \"./batching.js\";\nexport { OperationBatcher } from \"./batching.js\";\nvar BatchLink = /** @class */ (function (_super) {\n    __extends(BatchLink, _super);\n    function BatchLink(fetchParams) {\n        var _this = _super.call(this) || this;\n        var _a = fetchParams || {}, batchDebounce = _a.batchDebounce, _b = _a.batchInterval, batchInterval = _b === void 0 ? 10 : _b, _c = _a.batchMax, batchMax = _c === void 0 ? 0 : _c, _d = _a.batchHandler, batchHandler = _d === void 0 ? function () { return null; } : _d, _e = _a.batchKey, batchKey = _e === void 0 ? function () { return \"\"; } : _e;\n        _this.batcher = new OperationBatcher({\n            batchDebounce: batchDebounce,\n            batchInterval: batchInterval,\n            batchMax: batchMax,\n            batchHandler: batch<PERSON><PERSON><PERSON>,\n            batchKey: batchKey,\n        });\n        //make this link terminating\n        if (fetchParams.batchHandler.length <= 1) {\n            _this.request = function (operation) { return _this.batcher.enqueueRequest({ operation: operation }); };\n        }\n        return _this;\n    }\n    BatchLink.prototype.request = function (operation, forward) {\n        return this.batcher.enqueueRequest({\n            operation: operation,\n            forward: forward,\n        });\n    };\n    return BatchLink;\n}(ApolloLink));\nexport { BatchLink };\n//# sourceMappingURL=batchLink.js.map"], "names": ["__assign", "Observable", "__extends", "ApolloLink"], "mappings": ";;;;;;;;AAKG,IAAC,gBAAgB,KAAkB,YAAY;AAClD,IAAI,SAAS,gBAAgB,CAAC,EAAE,EAAE;AAClC,QAAQ,IAAI,aAAa,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,GAAG,EAAE,CAAC,aAAa,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC;AAE/J,QAAQ,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;AACtC,QAAQ,IAAI,CAAC,wBAAwB,GAAG,IAAI,GAAG,EAAE,CAAC;AAClD,QAAQ,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AAC3C,QAAQ,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AAC3C,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,CAAC,CAAC;AACtC,QAAQ,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACzC,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,KAAK,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;AACjE,KAAK;AACL,IAAI,gBAAgB,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,OAAO,EAAE;AACnE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,WAAW,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC;AACzH,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACnD,QAAQ,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE;AACrC,YAAY,WAAW,CAAC,UAAU,GAAG,IAAIC,oBAAU,CAAC,UAAU,QAAQ,EAAE;AACxE,gBAAgB,IAAI,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxD,gBAAgB,IAAI,CAAC,KAAK;AAC1B,oBAAoB,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG,IAAI,GAAG,EAAE,EAAE,CAAC;AAIrE,gBAAgB,IAAI,sBAAsB,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;AAC9D,gBAAgB,IAAI,iBAAiB,GAAG,WAAW,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,CAAC;AAC3E,gBAAgB,WAAW,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACtD,gBAAgB,IAAI,iBAAiB,EAAE;AACvC,oBAAoB,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC3C,iBAAiB;AAEjB,gBAAgB,IAAI,QAAQ,CAAC,IAAI,EAAE;AACnC,oBAAoB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AACxE,iBAAiB;AACjB,gBAAgB,IAAI,QAAQ,CAAC,KAAK,EAAE;AACpC,oBAAoB,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC1E,iBAAiB;AACjB,gBAAgB,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACvC,oBAAoB,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AAChF,iBAAiB;AAEjB,gBAAgB,IAAI,sBAAsB,IAAI,KAAK,CAAC,aAAa,EAAE;AACnE,oBAAoB,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;AACxD,iBAAiB;AAEjB,gBAAgB,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,QAAQ,EAAE;AACnD,oBAAoB,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC5C,iBAAiB;AACjB,gBAAgB,OAAO,YAAY;AACnC,oBAAoB,IAAI,EAAE,CAAC;AAE3B,oBAAoB,IAAI,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;AAChE,wBAAwB,WAAW,CAAC,WAAW,CAAC,IAAI,GAAG,CAAC,EAAE;AAE1D,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE;AACzE,4BAA4B,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAEpD,4BAA4B,CAAC,EAAE,GAAG,KAAK,CAAC,YAAY,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;AAC5G,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB,CAAC;AAClB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,WAAW,CAAC,UAAU,CAAC;AACtC,KAAK,CAAC;AAGN,IAAI,gBAAgB,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,GAAG,EAAE;AAC7D,QAAQ,IAAI,GAAG,KAAK,KAAK,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE;AACzC,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAE/C,QAAQ,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACtC,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;AAEnC,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,IAAI,UAAU,GAAG,EAAE,CAAC;AAC5B,QAAQ,IAAI,QAAQ,GAAG,EAAE,CAAC;AAC1B,QAAQ,IAAI,WAAW,GAAG,EAAE,CAAC;AAC7B,QAAQ,IAAI,KAAK,GAAG,EAAE,CAAC;AACvB,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC;AACxB,QAAQ,IAAI,SAAS,GAAG,EAAE,CAAC;AAI3B,QAAQ,KAAK,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE;AACzC,YAAY,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC/C,YAAY,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC3C,YAAY,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACjD,YAAY,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACrC,YAAY,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACvC,YAAY,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC7C,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAIA,oBAAU,CAAC,EAAE,EAAE,CAAC;AAC3F,QAAQ,IAAI,OAAO,GAAG,UAAU,KAAK,EAAE;AAEvC,YAAY,MAAM,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAChD,gBAAgB,IAAI,SAAS,EAAE;AAE/B,oBAAoB,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACzE,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC;AACV,QAAQ,KAAK,CAAC,YAAY,GAAG,iBAAiB,CAAC,SAAS,CAAC;AACzD,YAAY,IAAI,EAAE,UAAU,OAAO,EAAE;AACrC,gBAAgB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAC7C,oBAAoB,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;AACxC,iBAAiB;AACjB,gBAAgB,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE;AACrD,oBAAoB,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,sCAAsC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,uBAAuB,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AACvJ,oBAAoB,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC;AAC3C,oBAAoB,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC;AAC1C,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,KAAK,EAAE;AACzD,oBAAoB,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;AACtC,wBAAwB,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AACvF,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,YAAY,KAAK,EAAE,OAAO;AAC1B,YAAY,QAAQ,EAAE,YAAY;AAClC,gBAAgB,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;AACtD,oBAAoB,IAAI,QAAQ,EAAE;AAElC,wBAAwB,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACvE,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,WAAW,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,gBAAgB,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,GAAG,EAAE;AACzE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,YAAY,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7D,QAAQ,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,YAAY;AACtE,YAAY,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACpC,YAAY,KAAK,CAAC,wBAAwB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACvD,SAAS,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AAChC,KAAK,CAAC;AACN,IAAI,OAAO,gBAAgB,CAAC;AAC5B,CAAC,EAAE;;AC7IA,IAAC,SAAS,KAAkB,UAAU,MAAM,EAAE;AACjD,IAAIC,eAAS,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AACjC,IAAI,SAAS,SAAS,CAAC,WAAW,EAAE;AACpC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,QAAQ,IAAI,EAAE,GAAG,WAAW,IAAI,EAAE,EAAE,aAAa,GAAG,EAAE,CAAC,aAAa,EAAE,EAAE,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,YAAY,EAAE,YAAY,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,YAAY,EAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;AAChW,QAAQ,KAAK,CAAC,OAAO,GAAG,IAAI,gBAAgB,CAAC;AAC7C,YAAY,aAAa,EAAE,aAAa;AACxC,YAAY,aAAa,EAAE,aAAa;AACxC,YAAY,QAAQ,EAAE,QAAQ;AAC9B,YAAY,YAAY,EAAE,YAAY;AACtC,YAAY,QAAQ,EAAE,QAAQ;AAC9B,SAAS,CAAC,CAAC;AAEX,QAAQ,IAAI,WAAW,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,EAAE;AAClD,YAAY,KAAK,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;AACpH,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE,OAAO,EAAE;AAChE,QAAQ,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC;AAC3C,YAAY,SAAS,EAAE,SAAS;AAChC,YAAY,OAAO,EAAE,OAAO;AAC5B,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,OAAO,SAAS,CAAC;AACrB,CAAC,CAACC,eAAU,CAAC;;;;;"}