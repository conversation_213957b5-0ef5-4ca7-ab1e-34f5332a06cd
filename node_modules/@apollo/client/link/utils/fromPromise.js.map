{"version": 3, "file": "fromPromise.js", "sourceRoot": "", "sources": ["../../../src/link/utils/fromPromise.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAC;AAEtD,MAAM,UAAU,WAAW,CAAI,OAAmB;IAChD,OAAO,IAAI,UAAU,CAAI,UAAC,QAAQ;QAChC,OAAO;aACJ,IAAI,CAAC,UAAC,KAAQ;YACb,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,QAAQ,CAAC,QAAQ,EAAE,CAAC;QACtB,CAAC,CAAC;aACD,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC1C,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { Observable } from \"../../utilities/index.js\";\n\nexport function fromPromise<T>(promise: Promise<T>): Observable<T> {\n  return new Observable<T>((observer) => {\n    promise\n      .then((value: T) => {\n        observer.next(value);\n        observer.complete();\n      })\n      .catch(observer.error.bind(observer));\n  });\n}\n"]}