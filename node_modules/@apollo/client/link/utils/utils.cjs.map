{"version": 3, "file": "utils.cjs", "sources": ["fromError.js", "toPromise.js", "fromPromise.js", "throwServerError.js", "validateOperation.js", "createOperation.js", "transformOperation.js", "filterOperationVariables.js"], "sourcesContent": ["import { Observable } from \"../../utilities/index.js\";\nexport function fromError(errorValue) {\n    return new Observable(function (observer) {\n        observer.error(errorValue);\n    });\n}\n//# sourceMappingURL=fromError.js.map", "import { invariant } from \"../../utilities/globals/index.js\";\nexport function toPromise(observable) {\n    var completed = false;\n    return new Promise(function (resolve, reject) {\n        observable.subscribe({\n            next: function (data) {\n                if (completed) {\n                    globalThis.__DEV__ !== false && invariant.warn(45);\n                }\n                else {\n                    completed = true;\n                    resolve(data);\n                }\n            },\n            error: reject,\n        });\n    });\n}\n//# sourceMappingURL=toPromise.js.map", "import { Observable } from \"../../utilities/index.js\";\nexport function fromPromise(promise) {\n    return new Observable(function (observer) {\n        promise\n            .then(function (value) {\n            observer.next(value);\n            observer.complete();\n        })\n            .catch(observer.error.bind(observer));\n    });\n}\n//# sourceMappingURL=fromPromise.js.map", "export var throwServerError = function (response, result, message) {\n    var error = new Error(message);\n    error.name = \"ServerError\";\n    error.response = response;\n    error.statusCode = response.status;\n    error.result = result;\n    throw error;\n};\n//# sourceMappingURL=throwServerError.js.map", "import { newInvariantError } from \"../../utilities/globals/index.js\";\nexport function validateOperation(operation) {\n    var OPERATION_FIELDS = [\n        \"query\",\n        \"operationName\",\n        \"variables\",\n        \"extensions\",\n        \"context\",\n    ];\n    for (var _i = 0, _a = Object.keys(operation); _i < _a.length; _i++) {\n        var key = _a[_i];\n        if (OPERATION_FIELDS.indexOf(key) < 0) {\n            throw newInvariantError(46, key);\n        }\n    }\n    return operation;\n}\n//# sourceMappingURL=validateOperation.js.map", "import { __assign } from \"tslib\";\nexport function createOperation(starting, operation) {\n    var context = __assign({}, starting);\n    var setContext = function (next) {\n        if (typeof next === \"function\") {\n            context = __assign(__assign({}, context), next(context));\n        }\n        else {\n            context = __assign(__assign({}, context), next);\n        }\n    };\n    var getContext = function () { return (__assign({}, context)); };\n    Object.defineProperty(operation, \"setContext\", {\n        enumerable: false,\n        value: setContext,\n    });\n    Object.defineProperty(operation, \"getContext\", {\n        enumerable: false,\n        value: getContext,\n    });\n    return operation;\n}\n//# sourceMappingURL=createOperation.js.map", "import { getOperationName } from \"../../utilities/index.js\";\nexport function transformOperation(operation) {\n    var transformedOperation = {\n        variables: operation.variables || {},\n        extensions: operation.extensions || {},\n        operationName: operation.operationName,\n        query: operation.query,\n    };\n    // Best guess at an operation name\n    if (!transformedOperation.operationName) {\n        transformedOperation.operationName =\n            typeof transformedOperation.query !== \"string\" ?\n                getOperationName(transformedOperation.query) || undefined\n                : \"\";\n    }\n    return transformedOperation;\n}\n//# sourceMappingURL=transformOperation.js.map", "import { __assign } from \"tslib\";\nimport { visit } from \"graphql\";\nexport function filterOperationVariables(variables, query) {\n    var result = __assign({}, variables);\n    var unusedNames = new Set(Object.keys(variables));\n    visit(query, {\n        Variable: function (node, _key, parent) {\n            // A variable type definition at the top level of a query is not\n            // enough to silence server-side errors about the variable being\n            // unused, so variable definitions do not count as usage.\n            // https://spec.graphql.org/draft/#sec-All-Variables-Used\n            if (parent &&\n                parent.kind !== \"VariableDefinition\") {\n                unusedNames.delete(node.name.value);\n            }\n        },\n    });\n    unusedNames.forEach(function (name) {\n        delete result[name];\n    });\n    return result;\n}\n//# sourceMappingURL=filterOperationVariables.js.map"], "names": ["Observable", "invariant", "newInvariantError", "__assign", "getOperationName", "visit"], "mappings": ";;;;;;;;;AACO,SAAS,SAAS,CAAC,UAAU,EAAE;AACtC,IAAI,OAAO,IAAIA,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAC9C,QAAQ,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AACnC,KAAK,CAAC,CAAC;AACP;;ACJO,SAAS,SAAS,CAAC,UAAU,EAAE;AACtC,IAAI,IAAI,SAAS,GAAG,KAAK,CAAC;AAC1B,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AAClD,QAAQ,UAAU,CAAC,SAAS,CAAC;AAC7B,YAAY,IAAI,EAAE,UAAU,IAAI,EAAE;AAClC,gBAAgB,IAAI,SAAS,EAAE;AAC/B,oBAAoB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIC,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvE,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,SAAS,GAAG,IAAI,CAAC;AACrC,oBAAoB,OAAO,CAAC,IAAI,CAAC,CAAC;AAClC,iBAAiB;AACjB,aAAa;AACb,YAAY,KAAK,EAAE,MAAM;AACzB,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP;;AChBO,SAAS,WAAW,CAAC,OAAO,EAAE;AACrC,IAAI,OAAO,IAAID,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAC9C,QAAQ,OAAO;AACf,aAAa,IAAI,CAAC,UAAU,KAAK,EAAE;AACnC,YAAY,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjC,YAAY,QAAQ,CAAC,QAAQ,EAAE,CAAC;AAChC,SAAS,CAAC;AACV,aAAa,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AAClD,KAAK,CAAC,CAAC;AACP;;ACVU,IAAC,gBAAgB,GAAG,UAAU,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE;AACnE,IAAI,IAAI,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AACnC,IAAI,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC;AAC/B,IAAI,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC9B,IAAI,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;AACvC,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAC1B,IAAI,MAAM,KAAK,CAAC;AAChB;;ACNO,SAAS,iBAAiB,CAAC,SAAS,EAAE;AAC7C,IAAI,IAAI,gBAAgB,GAAG;AAC3B,QAAQ,OAAO;AACf,QAAQ,eAAe;AACvB,QAAQ,WAAW;AACnB,QAAQ,YAAY;AACpB,QAAQ,SAAS;AACjB,KAAK,CAAC;AACN,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACxE,QAAQ,IAAI,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACzB,QAAQ,IAAI,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC/C,YAAY,MAAME,yBAAiB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;AAC7C,SAAS;AACT,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB;;ACfO,SAAS,eAAe,CAAC,QAAQ,EAAE,SAAS,EAAE;AACrD,IAAI,IAAI,OAAO,GAAGC,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AACzC,IAAI,IAAI,UAAU,GAAG,UAAU,IAAI,EAAE;AACrC,QAAQ,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;AACxC,YAAY,OAAO,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;AACrE,SAAS;AACT,aAAa;AACb,YAAY,OAAO,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,CAAC;AAC5D,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAI,UAAU,GAAG,YAAY,EAAE,QAAQA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC;AACrE,IAAI,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,YAAY,EAAE;AACnD,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,KAAK,EAAE,UAAU;AACzB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,YAAY,EAAE;AACnD,QAAQ,UAAU,EAAE,KAAK;AACzB,QAAQ,KAAK,EAAE,UAAU;AACzB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,SAAS,CAAC;AACrB;;ACpBO,SAAS,kBAAkB,CAAC,SAAS,EAAE;AAC9C,IAAI,IAAI,oBAAoB,GAAG;AAC/B,QAAQ,SAAS,EAAE,SAAS,CAAC,SAAS,IAAI,EAAE;AAC5C,QAAQ,UAAU,EAAE,SAAS,CAAC,UAAU,IAAI,EAAE;AAC9C,QAAQ,aAAa,EAAE,SAAS,CAAC,aAAa;AAC9C,QAAQ,KAAK,EAAE,SAAS,CAAC,KAAK;AAC9B,KAAK,CAAC;AAEN,IAAI,IAAI,CAAC,oBAAoB,CAAC,aAAa,EAAE;AAC7C,QAAQ,oBAAoB,CAAC,aAAa;AAC1C,YAAY,OAAO,oBAAoB,CAAC,KAAK,KAAK,QAAQ;AAC1D,gBAAgBC,0BAAgB,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,SAAS;AACzE,kBAAkB,EAAE,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,oBAAoB,CAAC;AAChC;;ACdO,SAAS,wBAAwB,CAAC,SAAS,EAAE,KAAK,EAAE;AAC3D,IAAI,IAAI,MAAM,GAAGD,cAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;AACzC,IAAI,IAAI,WAAW,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AACtD,IAAIE,aAAK,CAAC,KAAK,EAAE;AACjB,QAAQ,QAAQ,EAAE,UAAU,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAKhD,YAAY,IAAI,MAAM;AACtB,gBAAgB,MAAM,CAAC,IAAI,KAAK,oBAAoB,EAAE;AACtD,gBAAgB,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpD,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACxC,QAAQ,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5B,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC;AAClB;;;;;;;;;;;"}