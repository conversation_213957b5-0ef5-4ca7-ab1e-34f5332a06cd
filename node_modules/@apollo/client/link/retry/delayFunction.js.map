{"version": 3, "file": "delayFunction.js", "sourceRoot": "", "sources": ["../../../src/link/retry/delayFunction.ts"], "names": [], "mappings": "AA2CA,MAAM,UAAU,kBAAkB,CAChC,YAAmC;IAE7B,IAAA,KAAmD,YAAY,IAAI,EAAE,EAAnE,eAAa,EAAb,OAAO,mBAAG,GAAG,KAAA,EAAE,cAAa,EAAb,MAAM,mBAAG,IAAI,KAAA,EAAE,WAAc,EAAd,GAAG,mBAAG,QAAQ,KAAuB,CAAC;IAC5E,sEAAsE;IACtE,6DAA6D;IAC7D,qEAAqE;IACrE,qDAAqD;IACrD,IAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC;IAEjD,OAAO,SAAS,aAAa,CAAC,KAAa;QACzC,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,GAAG,SAAA,CAAC,EAAI,KAAK,CAAA,CAAC,CAAC;QAClD,IAAI,MAAM,EAAE,CAAC;YACX,uEAAuE;YACvE,oEAAoE;YACpE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC;QAChC,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import type { Operation } from \"../core/index.js\";\n\n/**\n * Advanced mode: a function that implements the strategy for calculating delays\n * for particular responses.\n */\nexport interface DelayFunction {\n  (count: number, operation: Operation, error: any): number;\n}\n\nexport interface DelayFunctionOptions {\n  /**\n   * The number of milliseconds to wait before attempting the first retry.\n   *\n   * Delays will increase exponentially for each attempt.  E.g. if this is\n   * set to 100, subsequent retries will be delayed by 200, 400, 800, etc,\n   * until they reach maxDelay.\n   *\n   * Note that if jittering is enabled, this is the _average_ delay.\n   *\n   * Defaults to 300.\n   */\n  initial?: number;\n\n  /**\n   * The maximum number of milliseconds that the link should wait for any\n   * retry.\n   *\n   * Defaults to Infinity.\n   */\n  max?: number;\n\n  /**\n   * Whether delays between attempts should be randomized.\n   *\n   * This helps avoid thundering herd type situations by better distributing\n   * load during major outages.\n   *\n   * Defaults to true.\n   */\n  jitter?: boolean;\n}\n\nexport function buildDelayFunction(\n  delayOptions?: DelayFunctionOptions\n): DelayFunction {\n  const { initial = 300, jitter = true, max = Infinity } = delayOptions || {};\n  // If we're jittering, baseDelay is half of the maximum delay for that\n  // attempt (and is, on average, the delay we will encounter).\n  // If we're not jittering, adjust baseDelay so that the first attempt\n  // lines up with initialDelay, for everyone's sanity.\n  const baseDelay = jitter ? initial : initial / 2;\n\n  return function delayFunction(count: number) {\n    let delay = Math.min(max, baseDelay * 2 ** count);\n    if (jitter) {\n      // We opt for a full jitter approach for a mostly uniform distribution,\n      // but bound it within initialDelay and delay for everyone's sanity.\n      delay = Math.random() * delay;\n    }\n\n    return delay;\n  };\n}\n"]}