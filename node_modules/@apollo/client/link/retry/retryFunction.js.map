{"version": 3, "file": "retryFunction.js", "sourceRoot": "", "sources": ["../../../src/link/retry/retryFunction.ts"], "names": [], "mappings": "AAgCA,MAAM,UAAU,kBAAkB,CAChC,YAAmC;IAE7B,IAAA,KAAuB,YAAY,IAAK,EAA2B,EAAjE,OAAO,aAAA,EAAE,WAAO,EAAP,GAAG,mBAAG,CAAC,KAAiD,CAAC;IAC1E,OAAO,SAAS,aAAa,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK;QACnD,IAAI,KAAK,IAAI,GAAG;YAAE,OAAO,KAAK,CAAC;QAC/B,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IACvD,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import type { Operation } from \"../core/index.js\";\n\n/**\n * Advanced mode: a function that determines both whether a particular\n * response should be retried.\n */\nexport interface RetryFunction {\n  (count: number, operation: Operation, error: any): boolean | Promise<boolean>;\n}\n\nexport interface RetryFunctionOptions {\n  /**\n   * The max number of times to try a single operation before giving up.\n   *\n   * Note that this INCLUDES the initial request as part of the count.\n   * E.g. maxTries of 1 indicates no retrying should occur.\n   *\n   * Defaults to 5.  Pass Infinity for infinite retries.\n   */\n  max?: number;\n\n  /**\n   * Predicate function that determines whether a particular error should\n   * trigger a retry.\n   *\n   * For example, you may want to not retry 4xx class HTTP errors.\n   *\n   * By default, all errors are retried.\n   */\n  retryIf?: (error: any, operation: Operation) => boolean | Promise<boolean>;\n}\n\nexport function buildRetryFunction(\n  retryOptions?: RetryFunctionOptions\n): RetryFunction {\n  const { retryIf, max = 5 } = retryOptions || ({} as RetryFunctionOptions);\n  return function retryFunction(count, operation, error) {\n    if (count >= max) return false;\n    return retryIf ? retryIf(error, operation) : !!error;\n  };\n}\n"]}