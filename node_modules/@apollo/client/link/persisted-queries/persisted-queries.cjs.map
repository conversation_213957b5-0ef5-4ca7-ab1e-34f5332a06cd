{"version": 3, "file": "persisted-queries.cjs", "sources": ["index.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { print } from \"../../utilities/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport { Observable, compact, isNonEmptyArray } from \"../../utilities/index.js\";\nimport { cacheSizes, AutoCleanedWeakCache, } from \"../../utilities/index.js\";\nexport var VERSION = 1;\nfunction processErrors(graphQLErrors) {\n    var byMessage = Object.create(null), byCode = Object.create(null);\n    if (isNonEmptyArray(graphQLErrors)) {\n        graphQLErrors.forEach(function (error) {\n            var _a;\n            byMessage[error.message] = error;\n            if (typeof ((_a = error.extensions) === null || _a === void 0 ? void 0 : _a.code) == \"string\")\n                byCode[error.extensions.code] = error;\n        });\n    }\n    return {\n        persistedQueryNotSupported: !!(byMessage.PersistedQueryNotSupported ||\n            byCode.PERSISTED_QUERY_NOT_SUPPORTED),\n        persistedQueryNotFound: !!(byMessage.PersistedQueryNotFound || byCode.PERSISTED_QUERY_NOT_FOUND),\n    };\n}\nvar defaultOptions = {\n    disable: function (_a) {\n        var meta = _a.meta;\n        return meta.persistedQueryNotSupported;\n    },\n    retry: function (_a) {\n        var meta = _a.meta;\n        return meta.persistedQueryNotSupported || meta.persistedQueryNotFound;\n    },\n    useGETForHashedQueries: false,\n};\nfunction operationDefinesMutation(operation) {\n    return operation.query.definitions.some(function (d) { return d.kind === \"OperationDefinition\" && d.operation === \"mutation\"; });\n}\nexport var createPersistedQueryLink = function (options) {\n    var hashesByQuery;\n    function resetHashCache() {\n        hashesByQuery = undefined;\n    }\n    // Ensure a SHA-256 hash function is provided, if a custom hash\n    // generation function is not provided. We don't supply a SHA-256 hash\n    // function by default, to avoid forcing one as a dependency. Developers\n    // should pick the most appropriate SHA-256 function (sync or async) for\n    // their needs/environment, or provide a fully custom hash generation\n    // function (via the `generateHash` option) if they want to handle\n    // hashing with something other than SHA-256.\n    invariant(options &&\n        (typeof options.sha256 === \"function\" ||\n            typeof options.generateHash === \"function\"), 43);\n    var _a = compact(defaultOptions, options), sha256 = _a.sha256, \n    // If both a `sha256` and `generateHash` option are provided, the\n    // `sha256` option will be ignored. Developers can configure and\n    // use any hashing approach they want in a custom `generateHash`\n    // function; they aren't limited to SHA-256.\n    _b = _a.generateHash, \n    // If both a `sha256` and `generateHash` option are provided, the\n    // `sha256` option will be ignored. Developers can configure and\n    // use any hashing approach they want in a custom `generateHash`\n    // function; they aren't limited to SHA-256.\n    generateHash = _b === void 0 ? function (query) {\n        return Promise.resolve(sha256(print(query)));\n    } : _b, disable = _a.disable, retry = _a.retry, useGETForHashedQueries = _a.useGETForHashedQueries;\n    var supportsPersistedQueries = true;\n    var getHashPromise = function (query) {\n        return new Promise(function (resolve) { return resolve(generateHash(query)); });\n    };\n    function getQueryHash(query) {\n        if (!query || typeof query !== \"object\") {\n            // If the query is not an object, we won't be able to store its hash as\n            // a property of query[hashesKey], so we let generateHash(query) decide\n            // what to do with the bogus query.\n            return getHashPromise(query);\n        }\n        if (!hashesByQuery) {\n            hashesByQuery = new AutoCleanedWeakCache(cacheSizes[\"PersistedQueryLink.persistedQueryHashes\"] ||\n                2000 /* defaultCacheSizes[\"PersistedQueryLink.persistedQueryHashes\"] */);\n        }\n        var hash = hashesByQuery.get(query);\n        if (!hash)\n            hashesByQuery.set(query, (hash = getHashPromise(query)));\n        return hash;\n    }\n    return Object.assign(new ApolloLink(function (operation, forward) {\n        invariant(forward, 44);\n        var query = operation.query;\n        return new Observable(function (observer) {\n            var subscription;\n            var retried = false;\n            var originalFetchOptions;\n            var setFetchOptions = false;\n            var maybeRetry = function (_a, cb) {\n                var response = _a.response, networkError = _a.networkError;\n                if (!retried && ((response && response.errors) || networkError)) {\n                    retried = true;\n                    var graphQLErrors = [];\n                    var responseErrors = response && response.errors;\n                    if (isNonEmptyArray(responseErrors)) {\n                        graphQLErrors.push.apply(graphQLErrors, responseErrors);\n                    }\n                    // Network errors can return GraphQL errors on for example a 403\n                    var networkErrors = void 0;\n                    if (typeof (networkError === null || networkError === void 0 ? void 0 : networkError.result) !== \"string\") {\n                        networkErrors =\n                            networkError &&\n                                networkError.result &&\n                                networkError.result.errors;\n                    }\n                    if (isNonEmptyArray(networkErrors)) {\n                        graphQLErrors.push.apply(graphQLErrors, networkErrors);\n                    }\n                    var disablePayload = {\n                        response: response,\n                        networkError: networkError,\n                        operation: operation,\n                        graphQLErrors: isNonEmptyArray(graphQLErrors) ? graphQLErrors : void 0,\n                        meta: processErrors(graphQLErrors),\n                    };\n                    // if the server doesn't support persisted queries, don't try anymore\n                    supportsPersistedQueries = !disable(disablePayload);\n                    if (!supportsPersistedQueries) {\n                        // clear hashes from cache, we don't need them anymore\n                        resetHashCache();\n                    }\n                    // if its not found, we can try it again, otherwise just report the error\n                    if (retry(disablePayload)) {\n                        // need to recall the link chain\n                        if (subscription)\n                            subscription.unsubscribe();\n                        // actually send the query this time\n                        operation.setContext({\n                            http: {\n                                includeQuery: true,\n                                includeExtensions: supportsPersistedQueries,\n                            },\n                            fetchOptions: {\n                                // Since we're including the full query, which may be\n                                // large, we should send it in the body of a POST request.\n                                // See issue #7456.\n                                method: \"POST\",\n                            },\n                        });\n                        if (setFetchOptions) {\n                            operation.setContext({ fetchOptions: originalFetchOptions });\n                        }\n                        subscription = forward(operation).subscribe(handler);\n                        return;\n                    }\n                }\n                cb();\n            };\n            var handler = {\n                next: function (response) {\n                    maybeRetry({ response: response }, function () { return observer.next(response); });\n                },\n                error: function (networkError) {\n                    maybeRetry({ networkError: networkError }, function () { return observer.error(networkError); });\n                },\n                complete: observer.complete.bind(observer),\n            };\n            // don't send the query the first time\n            operation.setContext({\n                http: {\n                    includeQuery: !supportsPersistedQueries,\n                    includeExtensions: supportsPersistedQueries,\n                },\n            });\n            // If requested, set method to GET if there are no mutations. Remember the\n            // original fetchOptions so we can restore them if we fall back to a\n            // non-hashed request.\n            if (useGETForHashedQueries &&\n                supportsPersistedQueries &&\n                !operationDefinesMutation(operation)) {\n                operation.setContext(function (_a) {\n                    var _b = _a.fetchOptions, fetchOptions = _b === void 0 ? {} : _b;\n                    originalFetchOptions = fetchOptions;\n                    return {\n                        fetchOptions: __assign(__assign({}, fetchOptions), { method: \"GET\" }),\n                    };\n                });\n                setFetchOptions = true;\n            }\n            if (supportsPersistedQueries) {\n                getQueryHash(query)\n                    .then(function (sha256Hash) {\n                    operation.extensions.persistedQuery = {\n                        version: VERSION,\n                        sha256Hash: sha256Hash,\n                    };\n                    subscription = forward(operation).subscribe(handler);\n                })\n                    .catch(observer.error.bind(observer));\n            }\n            else {\n                subscription = forward(operation).subscribe(handler);\n            }\n            return function () {\n                if (subscription)\n                    subscription.unsubscribe();\n            };\n        });\n    }), {\n        resetHashCache: resetHashCache,\n    }, globalThis.__DEV__ !== false ?\n        {\n            getMemoryInternals: function () {\n                var _a;\n                return {\n                    PersistedQueryLink: {\n                        persistedQueryHashes: (_a = hashesByQuery === null || hashesByQuery === void 0 ? void 0 : hashesByQuery.size) !== null && _a !== void 0 ? _a : 0,\n                    },\n                };\n            },\n        }\n        : {});\n};\n//# sourceMappingURL=index.js.map"], "names": ["isNonEmptyArray", "invariant", "compact", "print", "AutoCleanedWeakCache", "cacheSizes", "ApolloLink", "Observable", "__assign"], "mappings": ";;;;;;;;;AAMU,IAAC,OAAO,GAAG,EAAE;AACvB,SAAS,aAAa,CAAC,aAAa,EAAE;AACtC,IAAI,IAAI,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtE,IAAI,IAAIA,yBAAe,CAAC,aAAa,CAAC,EAAE;AACxC,QAAQ,aAAa,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;AAC/C,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;AAC7C,YAAY,IAAI,QAAQ,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,QAAQ;AACzG,gBAAgB,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AACtD,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO;AACX,QAAQ,0BAA0B,EAAE,CAAC,EAAE,SAAS,CAAC,0BAA0B;AAC3E,YAAY,MAAM,CAAC,6BAA6B,CAAC;AACjD,QAAQ,sBAAsB,EAAE,CAAC,EAAE,SAAS,CAAC,sBAAsB,IAAI,MAAM,CAAC,yBAAyB,CAAC;AACxG,KAAK,CAAC;AACN,CAAC;AACD,IAAI,cAAc,GAAG;AACrB,IAAI,OAAO,EAAE,UAAU,EAAE,EAAE;AAC3B,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC3B,QAAQ,OAAO,IAAI,CAAC,0BAA0B,CAAC;AAC/C,KAAK;AACL,IAAI,KAAK,EAAE,UAAU,EAAE,EAAE;AACzB,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC3B,QAAQ,OAAO,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,sBAAsB,CAAC;AAC9E,KAAK;AACL,IAAI,sBAAsB,EAAE,KAAK;AACjC,CAAC,CAAC;AACF,SAAS,wBAAwB,CAAC,SAAS,EAAE;AAC7C,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,UAAU,CAAC,EAAE,CAAC,CAAC;AACrI,CAAC;AACS,IAAC,wBAAwB,GAAG,UAAU,OAAO,EAAE;AACzD,IAAI,IAAI,aAAa,CAAC;AACtB,IAAI,SAAS,cAAc,GAAG;AAC9B,QAAQ,aAAa,GAAG,SAAS,CAAC;AAClC,KAAK;AAQL,IAAIC,iBAAS,CAAC,OAAO;AACrB,SAAS,OAAO,OAAO,CAAC,MAAM,KAAK,UAAU;AAC7C,YAAY,OAAO,OAAO,CAAC,YAAY,KAAK,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;AAC7D,IAAI,IAAI,EAAE,GAAGC,iBAAO,CAAC,cAAc,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM;AAKjE,IAAI,EAAE,GAAG,EAAE,CAAC,YAAY;AAKxB,IAAI,YAAY,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,KAAK,EAAE;AACpD,QAAQ,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAACC,eAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrD,KAAK,GAAG,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,sBAAsB,GAAG,EAAE,CAAC,sBAAsB,CAAC;AACvG,IAAI,IAAI,wBAAwB,GAAG,IAAI,CAAC;AACxC,IAAI,IAAI,cAAc,GAAG,UAAU,KAAK,EAAE;AAC1C,QAAQ,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AACxF,KAAK,CAAC;AACN,IAAI,SAAS,YAAY,CAAC,KAAK,EAAE;AACjC,QAAQ,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAIjD,YAAY,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;AACzC,SAAS;AACT,QAAQ,IAAI,CAAC,aAAa,EAAE;AAC5B,YAAY,aAAa,GAAG,IAAIC,8BAAoB,CAACC,oBAAU,CAAC,yCAAyC,CAAC;AAC1G,gBAAgB,IAAI,EAAoE,CAAC;AACzF,SAAS;AACT,QAAQ,IAAI,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5C,QAAQ,IAAI,CAAC,IAAI;AACjB,YAAY,aAAa,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,GAAG,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;AACrE,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,IAAIC,eAAU,CAAC,UAAU,SAAS,EAAE,OAAO,EAAE;AACtE,QAAQL,iBAAS,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC/B,QAAQ,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;AACpC,QAAQ,OAAO,IAAIM,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAClD,YAAY,IAAI,YAAY,CAAC;AAC7B,YAAY,IAAI,OAAO,GAAG,KAAK,CAAC;AAChC,YAAY,IAAI,oBAAoB,CAAC;AACrC,YAAY,IAAI,eAAe,GAAG,KAAK,CAAC;AACxC,YAAY,IAAI,UAAU,GAAG,UAAU,EAAE,EAAE,EAAE,EAAE;AAC/C,gBAAgB,IAAI,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,CAAC;AAC3E,gBAAgB,IAAI,CAAC,OAAO,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,YAAY,CAAC,EAAE;AACjF,oBAAoB,OAAO,GAAG,IAAI,CAAC;AACnC,oBAAoB,IAAI,aAAa,GAAG,EAAE,CAAC;AAC3C,oBAAoB,IAAI,cAAc,GAAG,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC;AACrE,oBAAoB,IAAIP,yBAAe,CAAC,cAAc,CAAC,EAAE;AACzD,wBAAwB,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;AAChF,qBAAqB;AAErB,oBAAoB,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC;AAC/C,oBAAoB,IAAI,QAAQ,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE;AAC/H,wBAAwB,aAAa;AACrC,4BAA4B,YAAY;AACxC,gCAAgC,YAAY,CAAC,MAAM;AACnD,gCAAgC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;AAC3D,qBAAqB;AACrB,oBAAoB,IAAIA,yBAAe,CAAC,aAAa,CAAC,EAAE;AACxD,wBAAwB,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAC/E,qBAAqB;AACrB,oBAAoB,IAAI,cAAc,GAAG;AACzC,wBAAwB,QAAQ,EAAE,QAAQ;AAC1C,wBAAwB,YAAY,EAAE,YAAY;AAClD,wBAAwB,SAAS,EAAE,SAAS;AAC5C,wBAAwB,aAAa,EAAEA,yBAAe,CAAC,aAAa,CAAC,GAAG,aAAa,GAAG,KAAK,CAAC;AAC9F,wBAAwB,IAAI,EAAE,aAAa,CAAC,aAAa,CAAC;AAC1D,qBAAqB,CAAC;AAEtB,oBAAoB,wBAAwB,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACxE,oBAAoB,IAAI,CAAC,wBAAwB,EAAE;AAEnD,wBAAwB,cAAc,EAAE,CAAC;AACzC,qBAAqB;AAErB,oBAAoB,IAAI,KAAK,CAAC,cAAc,CAAC,EAAE;AAE/C,wBAAwB,IAAI,YAAY;AACxC,4BAA4B,YAAY,CAAC,WAAW,EAAE,CAAC;AAEvD,wBAAwB,SAAS,CAAC,UAAU,CAAC;AAC7C,4BAA4B,IAAI,EAAE;AAClC,gCAAgC,YAAY,EAAE,IAAI;AAClD,gCAAgC,iBAAiB,EAAE,wBAAwB;AAC3E,6BAA6B;AAC7B,4BAA4B,YAAY,EAAE;AAI1C,gCAAgC,MAAM,EAAE,MAAM;AAC9C,6BAA6B;AAC7B,yBAAyB,CAAC,CAAC;AAC3B,wBAAwB,IAAI,eAAe,EAAE;AAC7C,4BAA4B,SAAS,CAAC,UAAU,CAAC,EAAE,YAAY,EAAE,oBAAoB,EAAE,CAAC,CAAC;AACzF,yBAAyB;AACzB,wBAAwB,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC7E,wBAAwB,OAAO;AAC/B,qBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,EAAE,EAAE,CAAC;AACrB,aAAa,CAAC;AACd,YAAY,IAAI,OAAO,GAAG;AAC1B,gBAAgB,IAAI,EAAE,UAAU,QAAQ,EAAE;AAC1C,oBAAoB,UAAU,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,EAAE,YAAY,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;AACxG,iBAAiB;AACjB,gBAAgB,KAAK,EAAE,UAAU,YAAY,EAAE;AAC/C,oBAAoB,UAAU,CAAC,EAAE,YAAY,EAAE,YAAY,EAAE,EAAE,YAAY,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;AACrH,iBAAiB;AACjB,gBAAgB,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC1D,aAAa,CAAC;AAEd,YAAY,SAAS,CAAC,UAAU,CAAC;AACjC,gBAAgB,IAAI,EAAE;AACtB,oBAAoB,YAAY,EAAE,CAAC,wBAAwB;AAC3D,oBAAoB,iBAAiB,EAAE,wBAAwB;AAC/D,iBAAiB;AACjB,aAAa,CAAC,CAAC;AAIf,YAAY,IAAI,sBAAsB;AACtC,gBAAgB,wBAAwB;AACxC,gBAAgB,CAAC,wBAAwB,CAAC,SAAS,CAAC,EAAE;AACtD,gBAAgB,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE;AACnD,oBAAoB,IAAI,EAAE,GAAG,EAAE,CAAC,YAAY,EAAE,YAAY,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACrF,oBAAoB,oBAAoB,GAAG,YAAY,CAAC;AACxD,oBAAoB,OAAO;AAC3B,wBAAwB,YAAY,EAAEQ,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC7F,qBAAqB,CAAC;AACtB,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,eAAe,GAAG,IAAI,CAAC;AACvC,aAAa;AACb,YAAY,IAAI,wBAAwB,EAAE;AAC1C,gBAAgB,YAAY,CAAC,KAAK,CAAC;AACnC,qBAAqB,IAAI,CAAC,UAAU,UAAU,EAAE;AAChD,oBAAoB,SAAS,CAAC,UAAU,CAAC,cAAc,GAAG;AAC1D,wBAAwB,OAAO,EAAE,OAAO;AACxC,wBAAwB,UAAU,EAAE,UAAU;AAC9C,qBAAqB,CAAC;AACtB,oBAAoB,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACzE,iBAAiB,CAAC;AAClB,qBAAqB,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC1D,aAAa;AACb,iBAAiB;AACjB,gBAAgB,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACrE,aAAa;AACb,YAAY,OAAO,YAAY;AAC/B,gBAAgB,IAAI,YAAY;AAChC,oBAAoB,YAAY,CAAC,WAAW,EAAE,CAAC;AAC/C,aAAa,CAAC;AACd,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,EAAE;AACR,QAAQ,cAAc,EAAE,cAAc;AACtC,KAAK,EAAE,UAAU,CAAC,OAAO,KAAK,KAAK;AACnC,QAAQ;AACR,YAAY,kBAAkB,EAAE,YAAY;AAC5C,gBAAgB,IAAI,EAAE,CAAC;AACvB,gBAAgB,OAAO;AACvB,oBAAoB,kBAAkB,EAAE;AACxC,wBAAwB,oBAAoB,EAAE,CAAC,EAAE,GAAG,aAAa,KAAK,IAAI,IAAI,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,IAAI,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC;AACxK,qBAAqB;AACrB,iBAAiB,CAAC;AAClB,aAAa;AACb,SAAS;AACT,UAAU,EAAE,CAAC,CAAC;AACd;;;;;"}