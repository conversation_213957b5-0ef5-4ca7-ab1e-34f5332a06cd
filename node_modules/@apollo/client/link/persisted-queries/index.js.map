{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/link/persisted-queries/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAE7D,OAAO,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAQjD,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAK9C,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,0BAA0B,CAAC;AAGhF,OAAO,EACL,UAAU,EACV,oBAAoB,GAErB,MAAM,0BAA0B,CAAC;AAElC,MAAM,CAAC,IAAM,OAAO,GAAG,CAAC,CAAC;AAwCzB,SAAS,aAAa,CACpB,aAGa;IAEb,IAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EACnC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAE/B,IAAI,eAAe,CAAC,aAAa,CAAC,EAAE,CAAC;QACnC,aAAa,CAAC,OAAO,CAAC,UAAC,KAAK;;YAC1B,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,KAAK,CAAC;YACjC,IAAI,OAAO,CAAA,MAAA,KAAK,CAAC,UAAU,0CAAE,IAAI,CAAA,IAAI,QAAQ;gBAC3C,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC;IACD,OAAO;QACL,0BAA0B,EAAE,CAAC,CAAC,CAC5B,SAAS,CAAC,0BAA0B;YACpC,MAAM,CAAC,6BAA6B,CACrC;QACD,sBAAsB,EAAE,CAAC,CAAC,CACxB,SAAS,CAAC,sBAAsB,IAAI,MAAM,CAAC,yBAAyB,CACrE;KACF,CAAC;AACJ,CAAC;AAED,IAAM,cAAc,GAA0B;IAC5C,OAAO,EAAE,UAAC,EAAQ;YAAN,IAAI,UAAA;QAAO,OAAA,IAAI,CAAC,0BAA0B;IAA/B,CAA+B;IACtD,KAAK,EAAE,UAAC,EAAQ;YAAN,IAAI,UAAA;QACZ,OAAA,IAAI,CAAC,0BAA0B,IAAI,IAAI,CAAC,sBAAsB;IAA9D,CAA8D;IAChE,sBAAsB,EAAE,KAAK;CAC9B,CAAC;AAEF,SAAS,wBAAwB,CAAC,SAAoB;IACpD,OAAO,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CACrC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,SAAS,KAAK,UAAU,EAA9D,CAA8D,CACtE,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,IAAM,wBAAwB,GAAG,UACtC,OAAmC;IAEnC,IAAI,aAES,CAAC;IACd,SAAS,cAAc;QACrB,aAAa,GAAG,SAAS,CAAC;IAC5B,CAAC;IACD,+DAA+D;IAC/D,sEAAsE;IACtE,wEAAwE;IACxE,wEAAwE;IACxE,qEAAqE;IACrE,kEAAkE;IAClE,6CAA6C;IAC7C,SAAS,CACP,OAAO;QACL,CAAC,OAAO,OAAO,CAAC,MAAM,KAAK,UAAU;YACnC,OAAO,OAAO,CAAC,YAAY,KAAK,UAAU,CAAC,EAC/C,8DAA8D;QAC5D,sEAAsE;QACtE,YAAY,CACf,CAAC;IAEI,IAAA,KAWF,OAAO,CAAC,cAAc,EAAE,OAAO,CAAC,EAVlC,MAAM,YAAA;IACN,iEAAiE;IACjE,gEAAgE;IAChE,gEAAgE;IAChE,4CAA4C;IAC5C,oBACgD;IALhD,iEAAiE;IACjE,gEAAgE;IAChE,gEAAgE;IAChE,4CAA4C;IAC5C,YAAY,mBAAG,UAAC,KAAmB;QACjC,OAAA,OAAO,CAAC,OAAO,CAAS,MAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IAA9C,CAA8C,KAAA,EAChD,OAAO,aAAA,EACP,KAAK,WAAA,EACL,sBAAsB,4BACY,CAAC;IAErC,IAAI,wBAAwB,GAAG,IAAI,CAAC;IAEpC,IAAM,cAAc,GAAG,UAAC,KAAmB;QACzC,OAAA,IAAI,OAAO,CAAS,UAAC,OAAO,IAAK,OAAA,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAA5B,CAA4B,CAAC;IAA9D,CAA8D,CAAC;IAEjE,SAAS,YAAY,CAAC,KAAmB;QACvC,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACxC,uEAAuE;YACvE,uEAAuE;YACvE,mCAAmC;YACnC,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;QACD,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,aAAa,GAAG,IAAI,oBAAoB,CACtC,UAAU,CAAC,yCAAyC,CAAC;uFACS,CAC/D,CAAC;QACJ,CAAC;QACD,IAAI,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI;YAAE,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,IAAI,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,MAAM,CAAC,MAAM,CAClB,IAAI,UAAU,CAAC,UAAC,SAAS,EAAE,OAAO;QAChC,SAAS,CACP,OAAO,EACP,0DAA0D,CAC3D,CAAC;QAEM,IAAA,KAAK,GAAK,SAAS,MAAd,CAAe;QAE5B,OAAO,IAAI,UAAU,CAAC,UAAC,QAA4C;YACjE,IAAI,YAAoC,CAAC;YACzC,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,oBAAyB,CAAC;YAC9B,IAAI,eAAe,GAAG,KAAK,CAAC;YAC5B,IAAM,UAAU,GAAG,UACjB,EAMC,EACD,EAAc;oBANZ,QAAQ,cAAA,EACR,YAAY,kBAAA;gBAOd,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,EAAE,CAAC;oBAChE,OAAO,GAAG,IAAI,CAAC;oBAEf,IAAM,aAAa,GAA4B,EAAE,CAAC;oBAElD,IAAM,cAAc,GAAG,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC;oBACnD,IAAI,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC;wBACpC,aAAa,CAAC,IAAI,OAAlB,aAAa,EAAS,cAAc,EAAE;oBACxC,CAAC;oBAED,gEAAgE;oBAChE,IAAI,aAAa,SAAA,CAAC;oBAClB,IAAI,OAAO,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,MAAM,CAAA,KAAK,QAAQ,EAAE,CAAC;wBAC7C,aAAa;4BACX,YAAY;gCACZ,YAAY,CAAC,MAAM;gCAClB,YAAY,CAAC,MAAM,CAAC,MAAkC,CAAC;oBAC5D,CAAC;oBACD,IAAI,eAAe,CAAC,aAAa,CAAC,EAAE,CAAC;wBACnC,aAAa,CAAC,IAAI,OAAlB,aAAa,EAAS,aAAa,EAAE;oBACvC,CAAC;oBAED,IAAM,cAAc,GAAkB;wBACpC,QAAQ,UAAA;wBACR,YAAY,cAAA;wBACZ,SAAS,WAAA;wBACT,aAAa,EACX,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC;wBACzD,IAAI,EAAE,aAAa,CAAC,aAAa,CAAC;qBACnC,CAAC;oBAEF,qEAAqE;oBACrE,wBAAwB,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;oBACpD,IAAI,CAAC,wBAAwB,EAAE,CAAC;wBAC9B,sDAAsD;wBACtD,cAAc,EAAE,CAAC;oBACnB,CAAC;oBAED,yEAAyE;oBACzE,IAAI,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;wBAC1B,gCAAgC;wBAChC,IAAI,YAAY;4BAAE,YAAY,CAAC,WAAW,EAAE,CAAC;wBAC7C,oCAAoC;wBACpC,SAAS,CAAC,UAAU,CAAC;4BACnB,IAAI,EAAE;gCACJ,YAAY,EAAE,IAAI;gCAClB,iBAAiB,EAAE,wBAAwB;6BAC5C;4BACD,YAAY,EAAE;gCACZ,qDAAqD;gCACrD,0DAA0D;gCAC1D,mBAAmB;gCACnB,MAAM,EAAE,MAAM;6BACf;yBACF,CAAC,CAAC;wBACH,IAAI,eAAe,EAAE,CAAC;4BACpB,SAAS,CAAC,UAAU,CAAC,EAAE,YAAY,EAAE,oBAAoB,EAAE,CAAC,CAAC;wBAC/D,CAAC;wBACD,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;wBAErD,OAAO;oBACT,CAAC;gBACH,CAAC;gBACD,EAAE,EAAE,CAAC;YACP,CAAC,CAAC;YACF,IAAM,OAAO,GAAG;gBACd,IAAI,EAAE,UAAC,QAAkC;oBACvC,UAAU,CAAC,EAAE,QAAQ,UAAA,EAAE,EAAE,cAAM,OAAA,QAAQ,CAAC,IAAK,CAAC,QAAQ,CAAC,EAAxB,CAAwB,CAAC,CAAC;gBAC3D,CAAC;gBACD,KAAK,EAAE,UAAC,YAAyB;oBAC/B,UAAU,CAAC,EAAE,YAAY,cAAA,EAAE,EAAE,cAAM,OAAA,QAAQ,CAAC,KAAM,CAAC,YAAY,CAAC,EAA7B,CAA6B,CAAC,CAAC;gBACpE,CAAC;gBACD,QAAQ,EAAE,QAAQ,CAAC,QAAS,CAAC,IAAI,CAAC,QAAQ,CAAC;aAC5C,CAAC;YAEF,sCAAsC;YACtC,SAAS,CAAC,UAAU,CAAC;gBACnB,IAAI,EAAE;oBACJ,YAAY,EAAE,CAAC,wBAAwB;oBACvC,iBAAiB,EAAE,wBAAwB;iBAC5C;aACF,CAAC,CAAC;YAEH,0EAA0E;YAC1E,oEAAoE;YACpE,sBAAsB;YACtB,IACE,sBAAsB;gBACtB,wBAAwB;gBACxB,CAAC,wBAAwB,CAAC,SAAS,CAAC,EACpC,CAAC;gBACD,SAAS,CAAC,UAAU,CAClB,UAAC,EAA4D;wBAA1D,oBAAiB,EAAjB,YAAY,mBAAG,EAAE,KAAA;oBAClB,oBAAoB,GAAG,YAAY,CAAC;oBACpC,OAAO;wBACL,YAAY,wBACP,YAAY,KACf,MAAM,EAAE,KAAK,GACd;qBACF,CAAC;gBACJ,CAAC,CACF,CAAC;gBACF,eAAe,GAAG,IAAI,CAAC;YACzB,CAAC;YAED,IAAI,wBAAwB,EAAE,CAAC;gBAC7B,YAAY,CAAC,KAAK,CAAC;qBAChB,IAAI,CAAC,UAAC,UAAU;oBACf,SAAS,CAAC,UAAU,CAAC,cAAc,GAAG;wBACpC,OAAO,EAAE,OAAO;wBAChB,UAAU,YAAA;qBACX,CAAC;oBACF,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACvD,CAAC,CAAC;qBACD,KAAK,CAAC,QAAQ,CAAC,KAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACvD,CAAC;YAED,OAAO;gBACL,IAAI,YAAY;oBAAE,YAAY,CAAC,WAAW,EAAE,CAAC;YAC/C,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,EACF;QACE,cAAc,gBAAA;KACf,EACD,OAAO,CAAC,CAAC;QACP;YACE,kBAAkB;;gBAChB,OAAO;oBACL,kBAAkB,EAAE;wBAClB,oBAAoB,EAAE,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAE,IAAI,mCAAI,CAAC;qBAC/C;iBACF,CAAC;YACJ,CAAC;SACF;QACH,CAAC,CAAC,EAAE,CACL,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\n\nimport { print } from \"../../utilities/index.js\";\nimport type {\n  DocumentNode,\n  FormattedExecutionResult,\n  GraphQLFormattedError,\n} from \"graphql\";\n\nimport type { Operation } from \"../core/index.js\";\nimport { ApolloLink } from \"../core/index.js\";\nimport type {\n  Observer,\n  ObservableSubscription,\n} from \"../../utilities/index.js\";\nimport { Observable, compact, isNonEmptyArray } from \"../../utilities/index.js\";\nimport type { NetworkError } from \"../../errors/index.js\";\nimport type { ServerError } from \"../utils/index.js\";\nimport {\n  cacheSizes,\n  AutoCleanedWeakCache,\n  defaultCacheSizes,\n} from \"../../utilities/index.js\";\n\nexport const VERSION = 1;\n\nexport interface ErrorResponse {\n  graphQLErrors?: ReadonlyArray<GraphQLFormattedError>;\n  networkError?: NetworkError;\n  response?: FormattedExecutionResult;\n  operation: Operation;\n  meta: ErrorMeta;\n}\n\ntype ErrorMeta = {\n  persistedQueryNotSupported: boolean;\n  persistedQueryNotFound: boolean;\n};\n\ntype SHA256Function = (...args: any[]) => string | PromiseLike<string>;\ntype GenerateHashFunction = (\n  document: DocumentNode\n) => string | PromiseLike<string>;\n\ninterface BaseOptions {\n  disable?: (error: ErrorResponse) => boolean;\n  retry?: (error: ErrorResponse) => boolean;\n  useGETForHashedQueries?: boolean;\n}\n\nexport namespace PersistedQueryLink {\n  interface SHA256Options extends BaseOptions {\n    sha256: SHA256Function;\n    generateHash?: never;\n  }\n\n  interface GenerateHashOptions extends BaseOptions {\n    sha256?: never;\n    generateHash: GenerateHashFunction;\n  }\n\n  export type Options = SHA256Options | GenerateHashOptions;\n}\n\nfunction processErrors(\n  graphQLErrors:\n    | GraphQLFormattedError[]\n    | ReadonlyArray<GraphQLFormattedError>\n    | undefined\n): ErrorMeta {\n  const byMessage = Object.create(null),\n    byCode = Object.create(null);\n\n  if (isNonEmptyArray(graphQLErrors)) {\n    graphQLErrors.forEach((error) => {\n      byMessage[error.message] = error;\n      if (typeof error.extensions?.code == \"string\")\n        byCode[error.extensions.code] = error;\n    });\n  }\n  return {\n    persistedQueryNotSupported: !!(\n      byMessage.PersistedQueryNotSupported ||\n      byCode.PERSISTED_QUERY_NOT_SUPPORTED\n    ),\n    persistedQueryNotFound: !!(\n      byMessage.PersistedQueryNotFound || byCode.PERSISTED_QUERY_NOT_FOUND\n    ),\n  };\n}\n\nconst defaultOptions: Required<BaseOptions> = {\n  disable: ({ meta }) => meta.persistedQueryNotSupported,\n  retry: ({ meta }) =>\n    meta.persistedQueryNotSupported || meta.persistedQueryNotFound,\n  useGETForHashedQueries: false,\n};\n\nfunction operationDefinesMutation(operation: Operation) {\n  return operation.query.definitions.some(\n    (d) => d.kind === \"OperationDefinition\" && d.operation === \"mutation\"\n  );\n}\n\nexport const createPersistedQueryLink = (\n  options: PersistedQueryLink.Options\n) => {\n  let hashesByQuery:\n    | AutoCleanedWeakCache<DocumentNode, Promise<string>>\n    | undefined;\n  function resetHashCache() {\n    hashesByQuery = undefined;\n  }\n  // Ensure a SHA-256 hash function is provided, if a custom hash\n  // generation function is not provided. We don't supply a SHA-256 hash\n  // function by default, to avoid forcing one as a dependency. Developers\n  // should pick the most appropriate SHA-256 function (sync or async) for\n  // their needs/environment, or provide a fully custom hash generation\n  // function (via the `generateHash` option) if they want to handle\n  // hashing with something other than SHA-256.\n  invariant(\n    options &&\n      (typeof options.sha256 === \"function\" ||\n        typeof options.generateHash === \"function\"),\n    'Missing/invalid \"sha256\" or \"generateHash\" function. Please ' +\n      'configure one using the \"createPersistedQueryLink(options)\" options ' +\n      \"parameter.\"\n  );\n\n  const {\n    sha256,\n    // If both a `sha256` and `generateHash` option are provided, the\n    // `sha256` option will be ignored. Developers can configure and\n    // use any hashing approach they want in a custom `generateHash`\n    // function; they aren't limited to SHA-256.\n    generateHash = (query: DocumentNode) =>\n      Promise.resolve<string>(sha256!(print(query))),\n    disable,\n    retry,\n    useGETForHashedQueries,\n  } = compact(defaultOptions, options);\n\n  let supportsPersistedQueries = true;\n\n  const getHashPromise = (query: DocumentNode) =>\n    new Promise<string>((resolve) => resolve(generateHash(query)));\n\n  function getQueryHash(query: DocumentNode): Promise<string> {\n    if (!query || typeof query !== \"object\") {\n      // If the query is not an object, we won't be able to store its hash as\n      // a property of query[hashesKey], so we let generateHash(query) decide\n      // what to do with the bogus query.\n      return getHashPromise(query);\n    }\n    if (!hashesByQuery) {\n      hashesByQuery = new AutoCleanedWeakCache(\n        cacheSizes[\"PersistedQueryLink.persistedQueryHashes\"] ||\n          defaultCacheSizes[\"PersistedQueryLink.persistedQueryHashes\"]\n      );\n    }\n    let hash = hashesByQuery.get(query);\n    if (!hash) hashesByQuery.set(query, (hash = getHashPromise(query)));\n    return hash;\n  }\n\n  return Object.assign(\n    new ApolloLink((operation, forward) => {\n      invariant(\n        forward,\n        \"PersistedQueryLink cannot be the last link in the chain.\"\n      );\n\n      const { query } = operation;\n\n      return new Observable((observer: Observer<FormattedExecutionResult>) => {\n        let subscription: ObservableSubscription;\n        let retried = false;\n        let originalFetchOptions: any;\n        let setFetchOptions = false;\n        const maybeRetry = (\n          {\n            response,\n            networkError,\n          }: {\n            response?: FormattedExecutionResult;\n            networkError?: ServerError;\n          },\n          cb: () => void\n        ) => {\n          if (!retried && ((response && response.errors) || networkError)) {\n            retried = true;\n\n            const graphQLErrors: GraphQLFormattedError[] = [];\n\n            const responseErrors = response && response.errors;\n            if (isNonEmptyArray(responseErrors)) {\n              graphQLErrors.push(...responseErrors);\n            }\n\n            // Network errors can return GraphQL errors on for example a 403\n            let networkErrors;\n            if (typeof networkError?.result !== \"string\") {\n              networkErrors =\n                networkError &&\n                networkError.result &&\n                (networkError.result.errors as GraphQLFormattedError[]);\n            }\n            if (isNonEmptyArray(networkErrors)) {\n              graphQLErrors.push(...networkErrors);\n            }\n\n            const disablePayload: ErrorResponse = {\n              response,\n              networkError,\n              operation,\n              graphQLErrors:\n                isNonEmptyArray(graphQLErrors) ? graphQLErrors : void 0,\n              meta: processErrors(graphQLErrors),\n            };\n\n            // if the server doesn't support persisted queries, don't try anymore\n            supportsPersistedQueries = !disable(disablePayload);\n            if (!supportsPersistedQueries) {\n              // clear hashes from cache, we don't need them anymore\n              resetHashCache();\n            }\n\n            // if its not found, we can try it again, otherwise just report the error\n            if (retry(disablePayload)) {\n              // need to recall the link chain\n              if (subscription) subscription.unsubscribe();\n              // actually send the query this time\n              operation.setContext({\n                http: {\n                  includeQuery: true,\n                  includeExtensions: supportsPersistedQueries,\n                },\n                fetchOptions: {\n                  // Since we're including the full query, which may be\n                  // large, we should send it in the body of a POST request.\n                  // See issue #7456.\n                  method: \"POST\",\n                },\n              });\n              if (setFetchOptions) {\n                operation.setContext({ fetchOptions: originalFetchOptions });\n              }\n              subscription = forward(operation).subscribe(handler);\n\n              return;\n            }\n          }\n          cb();\n        };\n        const handler = {\n          next: (response: FormattedExecutionResult) => {\n            maybeRetry({ response }, () => observer.next!(response));\n          },\n          error: (networkError: ServerError) => {\n            maybeRetry({ networkError }, () => observer.error!(networkError));\n          },\n          complete: observer.complete!.bind(observer),\n        };\n\n        // don't send the query the first time\n        operation.setContext({\n          http: {\n            includeQuery: !supportsPersistedQueries,\n            includeExtensions: supportsPersistedQueries,\n          },\n        });\n\n        // If requested, set method to GET if there are no mutations. Remember the\n        // original fetchOptions so we can restore them if we fall back to a\n        // non-hashed request.\n        if (\n          useGETForHashedQueries &&\n          supportsPersistedQueries &&\n          !operationDefinesMutation(operation)\n        ) {\n          operation.setContext(\n            ({ fetchOptions = {} }: { fetchOptions: Record<string, any> }) => {\n              originalFetchOptions = fetchOptions;\n              return {\n                fetchOptions: {\n                  ...fetchOptions,\n                  method: \"GET\",\n                },\n              };\n            }\n          );\n          setFetchOptions = true;\n        }\n\n        if (supportsPersistedQueries) {\n          getQueryHash(query)\n            .then((sha256Hash) => {\n              operation.extensions.persistedQuery = {\n                version: VERSION,\n                sha256Hash,\n              };\n              subscription = forward(operation).subscribe(handler);\n            })\n            .catch(observer.error!.bind(observer));\n        } else {\n          subscription = forward(operation).subscribe(handler);\n        }\n\n        return () => {\n          if (subscription) subscription.unsubscribe();\n        };\n      });\n    }),\n    {\n      resetHashCache,\n    },\n    __DEV__ ?\n      {\n        getMemoryInternals() {\n          return {\n            PersistedQueryLink: {\n              persistedQueryHashes: hashesByQuery?.size ?? 0,\n            },\n          };\n        },\n      }\n    : {}\n  );\n};\n"]}