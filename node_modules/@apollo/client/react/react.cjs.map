{"version": 3, "file": "react.cjs", "sources": ["hooks/internal/wrapHook.js", "query-preloader/createQueryPreloader.js"], "sourcesContent": ["var wrapperSymbol = Symbol.for(\"apollo.hook.wrappers\");\n/**\n * @internal\n *\n * Makes an Apollo Client hook \"wrappable\".\n * That means that the Apollo Client instance can expose a \"wrapper\" that will be\n * used to wrap the original hook implementation with additional logic.\n * @example\n * ```tsx\n * // this is already done in `@apollo/client` for all wrappable hooks (see `WrappableHooks`)\n * // following this pattern\n * function useQuery() {\n *   return wrapHook('useQuery', _useQuery, options.client)(query, options);\n * }\n * function _useQuery(query, options) {\n *   // original implementation\n * }\n *\n * // this is what a library like `@apollo/client-react-streaming` would do\n * class ApolloClientWithStreaming extends ApolloClient {\n *   constructor(options) {\n *     super(options);\n *     this.queryManager[Symbol.for(\"apollo.hook.wrappers\")] = {\n *       useQuery: (original) => (query, options) => {\n *         console.log(\"useQuery was called with options\", options);\n *         return original(query, options);\n *       }\n *     }\n *   }\n * }\n *\n * // this will now log the options and then call the original `useQuery`\n * const client = new ApolloClientWithStreaming({ ... });\n * useQuery(query, { client });\n * ```\n */\nexport function wrapHook(hookName, useHook, clientOrObsQuery) {\n    var queryManager = clientOrObsQuery[\"queryManager\"];\n    var wrappers = queryManager && queryManager[wrapperSymbol];\n    var wrapper = wrappers && wrappers[hookName];\n    return wrapper ? wrapper(useHook) : useHook;\n}\n//# sourceMappingURL=wrapHook.js.map", "import { __assign } from \"tslib\";\nimport { InternalQueryReference, wrapQueryRef } from \"../internal/index.js\";\nimport { wrapHook } from \"../hooks/internal/index.js\";\n/**\n * A higher order function that returns a `preloadQuery` function which\n * can be used to begin loading a query with the given `client`. This is useful\n * when you want to start loading a query as early as possible outside of a\n * React component.\n *\n * > Refer to the [Suspense - Initiating queries outside React](https://www.apollographql.com/docs/react/data/suspense#initiating-queries-outside-react) section for a more in-depth overview.\n *\n * @param client - The `ApolloClient` instance that will be used to load queries\n * from the returned `preloadQuery` function.\n * @returns The `preloadQuery` function.\n *\n * @example\n * ```js\n * const preloadQuery = createQueryPreloader(client);\n * ```\n * @since 3.9.0\n */\nexport function createQueryPreloader(client) {\n    return wrapHook(\"createQueryPreloader\", _createQueryPreloader, client)(client);\n}\nvar _createQueryPreloader = function (client) {\n    return function preloadQuery(query, options) {\n        var _a, _b;\n        if (options === void 0) { options = Object.create(null); }\n        var queryRef = new InternalQueryReference(client.watchQuery(__assign(__assign({}, options), { query: query })), {\n            autoDisposeTimeoutMs: (_b = (_a = client.defaultOptions.react) === null || _a === void 0 ? void 0 : _a.suspense) === null || _b === void 0 ? void 0 : _b.autoDisposeTimeoutMs,\n        });\n        return wrapQueryRef(queryRef);\n    };\n};\n//# sourceMappingURL=createQueryPreloader.js.map"], "names": ["InternalQueryReference", "__assign", "wrapQueryRef"], "mappings": ";;;;;;;;;;;AAAA,IAAI,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;AAoChD,SAAS,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE;AAC9D,IAAI,IAAI,YAAY,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AACxD,IAAI,IAAI,QAAQ,GAAG,YAAY,IAAI,YAAY,CAAC,aAAa,CAAC,CAAC;AAC/D,IAAI,IAAI,OAAO,GAAG,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACjD,IAAI,OAAO,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AAChD;;ACpBO,SAAS,oBAAoB,CAAC,MAAM,EAAE;AAC7C,IAAI,OAAO,QAAQ,CAAC,sBAAsB,EAAE,qBAAqB,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC;AACnF,CAAC;AACD,IAAI,qBAAqB,GAAG,UAAU,MAAM,EAAE;AAC9C,IAAI,OAAO,SAAS,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE;AACjD,QAAQ,IAAI,EAAE,EAAE,EAAE,CAAC;AACnB,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAClE,QAAQ,IAAI,QAAQ,GAAG,IAAIA,+BAAsB,CAAC,MAAM,CAAC,UAAU,CAACC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE;AACxH,YAAY,oBAAoB,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,oBAAoB;AACzL,SAAS,CAAC,CAAC;AACX,QAAQ,OAAOC,qBAAY,CAAC,QAAQ,CAAC,CAAC;AACtC,KAAK,CAAC;AACN,CAAC;;;;;;;;;;;;;;"}