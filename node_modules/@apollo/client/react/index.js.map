{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/react/index.ts"], "names": [], "mappings": "AAAA,OAAO,+BAA+B,CAAC;AAGvC,OAAO,EACL,cAAc,EACd,cAAc,EACd,gBAAgB,EAChB,kBAAkB,GACnB,MAAM,oBAAoB,CAAC;AAE5B,cAAc,kBAAkB,CAAC;AAGjC,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAOxE,OAAO,EAAE,oBAAoB,EAAE,MAAM,2CAA2C,CAAC", "sourcesContent": ["import \"../utilities/globals/index.js\";\n\nexport type { ApolloContextValue } from \"./context/index.js\";\nexport {\n  ApolloProvider,\n  ApolloConsumer,\n  getApolloContext,\n  resetApolloContext,\n} from \"./context/index.js\";\n\nexport * from \"./hooks/index.js\";\n\nexport type { IDocumentDefinition } from \"./parser/index.js\";\nexport { DocumentType, operationName, parser } from \"./parser/index.js\";\n\nexport type {\n  PreloadQueryOptions,\n  PreloadQueryFetchPolicy,\n  PreloadQueryFunction,\n} from \"./query-preloader/createQueryPreloader.js\";\nexport { createQueryPreloader } from \"./query-preloader/createQueryPreloader.js\";\n\nexport type * from \"./types/types.js\";\n"]}