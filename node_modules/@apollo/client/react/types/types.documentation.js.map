{"version": 3, "file": "types.documentation.js", "sourceRoot": "", "sources": ["../../../src/react/types/types.documentation.ts"], "names": [], "mappings": "", "sourcesContent": ["export interface QueryOptionsDocumentation {\n  /**\n   * A GraphQL query string parsed into an AST with the gql template literal.\n   *\n   * @docGroup 1. Operation options\n   */\n  query: unknown;\n\n  /**\n   * An object containing all of the GraphQL variables your query requires to execute.\n   *\n   * Each key in the object corresponds to a variable name, and that key's value corresponds to the variable value.\n   *\n   * @docGroup 1. Operation options\n   */\n  variables: unknown;\n\n  /**\n   * Specifies how the query handles a response that returns both GraphQL errors and partial results.\n   *\n   * For details, see [GraphQL error policies](https://www.apollographql.com/docs/react/data/error-handling/#graphql-error-policies).\n   *\n   * The default value is `none`, meaning that the query result includes error details but not partial results.\n   *\n   * @docGroup 1. Operation options\n   */\n  errorPolicy: unknown;\n\n  /**\n   * If you're using [Apollo Link](https://www.apollographql.com/docs/react/api/link/introduction/), this object is the initial value of the `context` object that's passed along your link chain.\n   *\n   * @docGroup 2. Networking options\n   */\n  context: unknown;\n\n  /**\n   * Specifies how the query interacts with the Apollo Client cache during execution (for example, whether it checks the cache for results before sending a request to the server).\n   *\n   * For details, see [Setting a fetch policy](https://www.apollographql.com/docs/react/data/queries/#setting-a-fetch-policy).\n   *\n   * The default value is `cache-first`.\n   *\n   * @docGroup 3. Caching options\n   */\n  fetchPolicy: unknown;\n\n  /**\n   * Specifies the `FetchPolicy` to be used after this query has completed.\n   *\n   * @docGroup 3. Caching options\n   */\n  nextFetchPolicy: unknown;\n\n  /**\n   * Defaults to the initial value of options.fetchPolicy, but can be explicitly\n   * configured to specify the WatchQueryFetchPolicy to revert back to whenever\n   * variables change (unless nextFetchPolicy intervenes).\n   *\n   * @docGroup 3. Caching options\n   */\n  initialFetchPolicy: unknown;\n\n  /**\n   * Specifies the interval (in milliseconds) at which the query polls for updated results.\n   *\n   * The default value is `0` (no polling).\n   *\n   * @docGroup 2. Networking options\n   */\n  pollInterval: unknown;\n\n  /**\n   * If `true`, the in-progress query's associated component re-renders whenever the network status changes or a network error occurs.\n   *\n   * The default value is `false`.\n   *\n   * @docGroup 2. Networking options\n   */\n  notifyOnNetworkStatusChange: unknown;\n\n  /**\n   * If `true`, the query can return partial results from the cache if the cache doesn't contain results for all queried fields.\n   *\n   * The default value is `false`.\n   *\n   * @docGroup 3. Caching options\n   */\n  returnPartialData: unknown;\n\n  /**\n   * Specifies whether a `NetworkStatus.refetch` operation should merge\n   * incoming field data with existing data, or overwrite the existing data.\n   * Overwriting is probably preferable, but merging is currently the default\n   * behavior, for backwards compatibility with Apollo Client 3.x.\n   *\n   * @docGroup 3. Caching options\n   */\n  refetchWritePolicy: unknown;\n\n  /**\n   * Watched queries must opt into overwriting existing data on refetch, by passing refetchWritePolicy: \"overwrite\" in their WatchQueryOptions.\n   *\n   * The default value is \"overwrite\".\n   *\n   * @docGroup 3. Caching options\n   */\n  refetchWritePolicy_suspense: unknown;\n\n  /**\n   * If `true`, causes a query refetch if the query result is detected as partial.\n   *\n   * The default value is `false`.\n   *\n   * @deprecated\n   * Setting this option is unnecessary in Apollo Client 3, thanks to a more consistent application of fetch policies. It might be removed in a future release.\n   */\n  partialRefetch: unknown;\n\n  /**\n   * Whether to canonize cache results before returning them. Canonization\n   * takes some extra time, but it speeds up future deep equality comparisons.\n   * Defaults to false.\n   *\n   * @deprecated\n   * Using `canonizeResults` can result in memory leaks so we generally do not\n   * recommend using this option anymore.\n   * A future version of Apollo Client will contain a similar feature without\n   * the risk of memory leaks.\n   */\n  canonizeResults: unknown;\n\n  /**\n   * If true, the query is not executed.\n   *\n   * The default value is `false`.\n   *\n   * @docGroup 1. Operation options\n   */\n  skip: unknown;\n\n  /**\n   * If `true`, the query is not executed. The default value is `false`.\n   *\n   * @deprecated We recommend using `skipToken` in place of the `skip` option as\n   * it is more type-safe.\n   *\n   * This option is deprecated and only supported to ease the migration from useQuery. It will be removed in a future release.\n   *\n   * @docGroup 1. Operation options\n   */\n  skip_deprecated: unknown;\n\n  /**\n   * A callback function that's called when your query successfully completes with zero errors (or if `errorPolicy` is `ignore` and partial data is returned).\n   *\n   * This function is passed the query's result `data`.\n   *\n   * @docGroup 1. Operation options\n   */\n  onCompleted: unknown;\n  /**\n   * A callback function that's called when the query encounters one or more errors (unless `errorPolicy` is `ignore`).\n   *\n   * This function is passed an `ApolloError` object that contains either a `networkError` object or a `graphQLErrors` array, depending on the error(s) that occurred.\n   *\n   * @docGroup 1. Operation options\n   */\n  onError: unknown;\n\n  /**\n   * The instance of `ApolloClient` to use to execute the query.\n   *\n   * By default, the instance that's passed down via context is used, but you\n   * can provide a different instance here.\n   *\n   * @docGroup 1. Operation options\n   */\n  client: unknown;\n\n  /**\n   * A unique identifier for the query. Each item in the array must be a stable\n   * identifier to prevent infinite fetches.\n   *\n   * This is useful when using the same query and variables combination in more\n   * than one component, otherwise the components may clobber each other. This\n   * can also be used to force the query to re-evaluate fresh.\n   *\n   * @docGroup 1. Operation options\n   */\n  queryKey: unknown;\n\n  /**\n   * Pass `false` to skip executing the query during [server-side rendering](https://www.apollographql.com/docs/react/performance/server-side-rendering/).\n   *\n   * @docGroup 2. Networking options\n   */\n  ssr: unknown;\n\n  /**\n   * A callback function that's called whenever a refetch attempt occurs\n   * while polling. If the function returns `true`, the refetch is\n   * skipped and not reattempted until the next poll interval.\n   *\n   * @docGroup 2. Networking options\n   */\n  skipPollAttempt: unknown;\n}\n\nexport interface QueryResultDocumentation {\n  /**\n   * The instance of Apollo Client that executed the query.\n   * Can be useful for manually executing followup queries or writing data to the cache.\n   *\n   * @docGroup 2. Network info\n   */\n  client: unknown;\n  /**\n   * A reference to the internal `ObservableQuery` used by the hook.\n   */\n  observable: unknown;\n  /**\n   * An object containing the result of your GraphQL query after it completes.\n   *\n   * This value might be `undefined` if a query results in one or more errors (depending on the query's `errorPolicy`).\n   *\n   * @docGroup 1. Operation data\n   */\n  data: unknown;\n  /**\n   * An object containing the result from the most recent _previous_ execution of this query.\n   *\n   * This value is `undefined` if this is the query's first execution.\n   *\n   * @docGroup 1. Operation data\n   */\n  previousData: unknown;\n  /**\n   * If the query produces one or more errors, this object contains either an array of `graphQLErrors` or a single `networkError`. Otherwise, this value is `undefined`.\n   *\n   * For more information, see [Handling operation errors](https://www.apollographql.com/docs/react/data/error-handling/).\n   *\n   * @docGroup 1. Operation data\n   */\n  error: unknown;\n  /**\n   * If `true`, the query is still in flight and results have not yet been returned.\n   *\n   * @docGroup 2. Network info\n   */\n  loading: unknown;\n  /**\n   * A number indicating the current network state of the query's associated request. [See possible values.](https://github.com/apollographql/apollo-client/blob/d96f4578f89b933c281bb775a39503f6cdb59ee8/src/core/networkStatus.ts#L4)\n   *\n   * Used in conjunction with the [`notifyOnNetworkStatusChange`](#notifyonnetworkstatuschange) option.\n   *\n   * @docGroup 2. Network info\n   */\n  networkStatus: unknown;\n  /**\n   * If `true`, the associated lazy query has been executed.\n   *\n   * This field is only present on the result object returned by [`useLazyQuery`](/react/data/queries/#executing-queries-manually).\n   *\n   * @docGroup 2. Network info\n   */\n  called: unknown;\n  /**\n   * An object containing the variables that were provided for the query.\n   *\n   * @docGroup 1. Operation data\n   */\n  variables: unknown;\n\n  /**\n   * A function that enables you to re-execute the query, optionally passing in new `variables`.\n   *\n   * To guarantee that the refetch performs a network request, its `fetchPolicy` is set to `network-only` (unless the original query's `fetchPolicy` is `no-cache` or `cache-and-network`, which also guarantee a network request).\n   *\n   * See also [Refetching](https://www.apollographql.com/docs/react/data/queries/#refetching).\n   *\n   *   @docGroup 3. Helper functions\n   */\n  refetch: unknown;\n  /**\n   * {@inheritDoc @apollo/client!ObservableQuery#fetchMore:member(1)}\n   *\n   * @docGroup 3. Helper functions\n   */\n  fetchMore: unknown;\n  /**\n   * {@inheritDoc @apollo/client!ObservableQuery#startPolling:member(1)}\n   *\n   * @docGroup 3. Helper functions\n   */\n  startPolling: unknown;\n  /**\n   * {@inheritDoc @apollo/client!ObservableQuery#stopPolling:member(1)}\n   *\n   * @docGroup 3. Helper functions\n   */\n  stopPolling: unknown;\n  /**\n   * {@inheritDoc @apollo/client!ObservableQuery#subscribeToMore:member(1)}\n   *\n   * @docGroup 3. Helper functions\n   */\n  subscribeToMore: unknown;\n  /**\n   * {@inheritDoc @apollo/client!ObservableQuery#updateQuery:member(1)}\n   *\n   * @docGroup 3. Helper functions\n   */\n  updateQuery: unknown;\n}\n\nexport interface MutationOptionsDocumentation {\n  /**\n   * A GraphQL document, often created with `gql` from the `graphql-tag`\n   * package, that contains a single mutation inside of it.\n   *\n   * @docGroup 1. Operation options\n   */\n  mutation: unknown;\n\n  /**\n   * Provide `no-cache` if the mutation's result should _not_ be written to the Apollo Client cache.\n   *\n   * The default value is `network-only` (which means the result _is_ written to the cache).\n   *\n   * Unlike queries, mutations _do not_ support [fetch policies](https://www.apollographql.com/docs/react/data/queries/#setting-a-fetch-policy) besides `network-only` and `no-cache`.\n   *\n   * @docGroup 3. Caching options\n   */\n  fetchPolicy: unknown;\n\n  /**\n   * To avoid retaining sensitive information from mutation root field\n   * arguments, Apollo Client v3.4+ automatically clears any `ROOT_MUTATION`\n   * fields from the cache after each mutation finishes. If you need this\n   * information to remain in the cache, you can prevent the removal by passing\n   * `keepRootFields: true` to the mutation. `ROOT_MUTATION` result data are\n   * also passed to the mutation `update` function, so we recommend obtaining\n   * the results that way, rather than using this option, if possible.\n   */\n  keepRootFields: unknown;\n\n  /**\n   * By providing either an object or a callback function that, when invoked after\n   * a mutation, allows you to return optimistic data and optionally skip updates\n   * via the `IGNORE` sentinel object, Apollo Client caches this temporary\n   * (and potentially incorrect) response until the mutation completes, enabling\n   * more responsive UI updates.\n   *\n   * For more information, see [Optimistic mutation results](https://www.apollographql.com/docs/react/performance/optimistic-ui/).\n   *\n   * @docGroup 3. Caching options\n   */\n  optimisticResponse: unknown;\n\n  /**\n   * A `MutationQueryReducersMap`, which is map from query names to\n   * mutation query reducers. Briefly, this map defines how to incorporate the\n   * results of the mutation into the results of queries that are currently\n   * being watched by your application.\n   */\n  updateQueries: unknown;\n\n  /**\n   * An array (or a function that _returns_ an array) that specifies which queries you want to refetch after the mutation occurs.\n   *\n   * Each array value can be either:\n   *\n   * - An object containing the `query` to execute, along with any `variables`\n   *\n   * - A string indicating the operation name of the query to refetch\n   *\n   * @docGroup 1. Operation options\n   */\n  refetchQueries: unknown;\n\n  /**\n   * If `true`, makes sure all queries included in `refetchQueries` are completed before the mutation is considered complete.\n   *\n   * The default value is `false` (queries are refetched asynchronously).\n   *\n   * @docGroup 1. Operation options\n   */\n  awaitRefetchQueries: unknown;\n\n  /**\n   * A function used to update the Apollo Client cache after the mutation completes.\n   *\n   * For more information, see [Updating the cache after a mutation](https://www.apollographql.com/docs/react/data/mutations#updating-the-cache-after-a-mutation).\n   *\n   * @docGroup 3. Caching options\n   */\n  update: unknown;\n\n  /**\n   * Optional callback for intercepting queries whose cache data has been updated by the mutation, as well as any queries specified in the `refetchQueries: [...]` list passed to `client.mutate`.\n   *\n   * Returning a `Promise` from `onQueryUpdated` will cause the final mutation `Promise` to await the returned `Promise`. Returning `false` causes the query to be ignored.\n   *\n   * @docGroup 1. Operation options\n   */\n  onQueryUpdated: unknown;\n\n  /**\n   * Specifies how the mutation handles a response that returns both GraphQL errors and partial results.\n   *\n   * For details, see [GraphQL error policies](https://www.apollographql.com/docs/react/data/error-handling/#graphql-error-policies).\n   *\n   * The default value is `none`, meaning that the mutation result includes error details but _not_ partial results.\n   *\n   * @docGroup 1. Operation options\n   */\n  errorPolicy: unknown;\n\n  /**\n   * An object containing all of the GraphQL variables your mutation requires to execute.\n   *\n   * Each key in the object corresponds to a variable name, and that key's value corresponds to the variable value.\n   *\n   * @docGroup 1. Operation options\n   */\n  variables: unknown;\n\n  /**\n   * If you're using [Apollo Link](https://www.apollographql.com/docs/react/api/link/introduction/), this object is the initial value of the `context` object that's passed along your link chain.\n   *\n   * @docGroup 2. Networking options\n   */\n  context: unknown;\n\n  /**\n   * The instance of `ApolloClient` to use to execute the mutation.\n   *\n   * By default, the instance that's passed down via context is used, but you can provide a different instance here.\n   *\n   * @docGroup 2. Networking options\n   */\n  client: unknown;\n  /**\n   * If `true`, the in-progress mutation's associated component re-renders whenever the network status changes or a network error occurs.\n   *\n   * The default value is `false`.\n   *\n   * @docGroup 2. Networking options\n   */\n  notifyOnNetworkStatusChange: unknown;\n  /**\n   * A callback function that's called when your mutation successfully completes with zero errors (or if `errorPolicy` is `ignore` and partial data is returned).\n   *\n   * This function is passed the mutation's result `data` and any options passed to the mutation.\n   *\n   * @docGroup 1. Operation options\n   */\n  onCompleted: unknown;\n  /**\n   * A callback function that's called when the mutation encounters one or more errors (unless `errorPolicy` is `ignore`).\n   *\n   * This function is passed an [`ApolloError`](https://github.com/apollographql/apollo-client/blob/d96f4578f89b933c281bb775a39503f6cdb59ee8/src/errors/index.ts#L36-L39) object that contains either a `networkError` object or a `graphQLErrors` array, depending on the error(s) that occurred, as well as any options passed the mutation.\n   *\n   * @docGroup 1. Operation options\n   */\n  onError: unknown;\n  /**\n   * If `true`:\n   *\n   * - The initial state update (setting loading to true) is skipped\n   * - The success state update (setting data and setting loading to false) is skipped\n   * - Error updates will still occur\n   *\n   * The default value is `false`.\n   *\n   * This option is useful when you want to execute a mutation but don't need to track its progress or result in the UI, potentially improving performance by reducing re-renders.\n   *\n   * @docGroup 1. Operation options\n   */\n  ignoreResults: unknown;\n}\n\nexport interface MutationResultDocumentation {\n  /**\n   * The data returned from your mutation. Can be `undefined` if `ignoreResults` is `true`.\n   */\n  data: unknown;\n  /**\n   * If the mutation produces one or more errors, this object contains either an array of `graphQLErrors` or a single `networkError`. Otherwise, this value is `undefined`.\n   *\n   * For more information, see [Handling operation errors](https://www.apollographql.com/docs/react/data/error-handling/).\n   */\n  error: unknown;\n  /**\n   * If `true`, the mutation is currently in flight.\n   */\n  loading: unknown;\n  /**\n   * If `true`, the mutation's mutate function has been called.\n   */\n  called: unknown;\n  /**\n   * The instance of Apollo Client that executed the mutation.\n   *\n   * Can be useful for manually executing followup operations or writing data to the cache.\n   */\n  client: unknown;\n  /**\n   * A function that you can call to reset the mutation's result to its initial, uncalled state.\n   */\n  reset: unknown;\n}\n\nexport interface SubscriptionOptionsDocumentation {\n  /**\n   * A GraphQL document, often created with `gql` from the `graphql-tag`\n   * package, that contains a single subscription inside of it.\n   */\n  query: unknown;\n  /**\n   * An object containing all of the variables your subscription needs to execute\n   */\n  variables: unknown;\n\n  /**\n   * Specifies the `ErrorPolicy` to be used for this operation\n   */\n  errorPolicy: unknown;\n\n  /**\n   * How you want your component to interact with the Apollo cache. For details, see [Setting a fetch policy](https://www.apollographql.com/docs/react/data/queries/#setting-a-fetch-policy).\n   */\n  fetchPolicy: unknown;\n\n  /**\n   * Determines if your subscription should be unsubscribed and subscribed again when an input to the hook (such as `subscription` or `variables`) changes.\n   */\n  shouldResubscribe: unknown;\n\n  /**\n   * If `true`, the hook will not cause the component to rerender. This is useful when you want to control the rendering of your component yourself with logic in the `onData` and `onError` callbacks.\n   *\n   * Changing this to `true` when the hook already has `data` will reset the `data` to `undefined`.\n   */\n  ignoreResults: unknown;\n  /**\n   * An `ApolloClient` instance. By default `useSubscription` / `Subscription` uses the client passed down via context, but a different client can be passed in.\n   */\n  client: unknown;\n\n  /**\n   * Determines if the current subscription should be skipped. Useful if, for example, variables depend on previous queries and are not ready yet.\n   */\n  skip: unknown;\n\n  /**\n   * Shared context between your component and your network interface (Apollo Link).\n   */\n  context: unknown;\n\n  /**\n   * Shared context between your component and your network interface (Apollo Link).\n   */\n  extensions: unknown;\n\n  /**\n   * Allows the registration of a callback function that will be triggered each time the `useSubscription` Hook / `Subscription` component completes the subscription.\n   *\n   * @since 3.7.0\n   */\n  onComplete: unknown;\n\n  /**\n   * Allows the registration of a callback function that will be triggered each time the `useSubscription` Hook / `Subscription` component receives data. The callback `options` object param consists of the current Apollo Client instance in `client`, and the received subscription data in `data`.\n   *\n   * @since 3.7.0\n   */\n  onData: unknown;\n\n  /**\n   * Allows the registration of a callback function that will be triggered each time the `useSubscription` Hook / `Subscription` component receives data. The callback `options` object param consists of the current Apollo Client instance in `client`, and the received subscription data in `subscriptionData`.\n   *\n   * @deprecated Use `onData` instead\n   */\n  onSubscriptionData: unknown;\n\n  /**\n   * Allows the registration of a callback function that will be triggered each time the `useSubscription` Hook / `Subscription` component receives an error.\n   *\n   * @since 3.7.0\n   */\n  onError: unknown;\n\n  /**\n   * Allows the registration of a callback function that will be triggered when the `useSubscription` Hook / `Subscription` component completes the subscription.\n   *\n   * @deprecated Use `onComplete` instead\n   */\n  onSubscriptionComplete: unknown;\n}\n\nexport interface SubscriptionResultDocumentation {\n  /**\n   * A boolean that indicates whether any initial data has been returned\n   */\n  loading: unknown;\n  /**\n   * An object containing the result of your GraphQL subscription. Defaults to an empty object.\n   */\n  data: unknown;\n  /**\n   * A runtime error with `graphQLErrors` and `networkError` properties\n   */\n  error: unknown;\n}\n"]}