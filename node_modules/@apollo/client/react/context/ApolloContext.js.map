{"version": 3, "file": "ApolloContext.js", "sourceRoot": "", "sources": ["../../../src/react/context/ApolloContext.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAGjC,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AAExD,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAO7D,wEAAwE;AACxE,0EAA0E;AAC1E,wEAAwE;AACxE,4EAA4E;AAC5E,IAAM,UAAU,GACd,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC;AAEzE,MAAM,UAAU,gBAAgB;IAC9B,SAAS,CACP,eAAe,IAAI,KAAK,EACxB,+FAA+F;QAC7F,uGAAuG;QACvG,iEAAiE;QACjE,4GAA4G;QAC5G,sGAAsG,CACzG,CAAC;IAEF,IAAI,OAAO,GAAI,KAAK,CAAC,aAAqB,CACxC,UAAU,CAC0B,CAAC;IACvC,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,aAAa,EAAE,UAAU,EAAE;YACrD,KAAK,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC,aAAa,CAAqB,EAAE,CAAC,CAAC;YAC9D,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QACH,OAAO,CAAC,WAAW,GAAG,eAAe,CAAC;IACxC,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,IAAM,kBAAkB,GAAG,gBAAgB,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport type * as ReactTypes from \"react\";\nimport type { ApolloClient } from \"../../core/index.js\";\nimport { canUseSymbol } from \"../../utilities/index.js\";\nimport type { RenderPromises } from \"../ssr/index.js\";\nimport { invariant } from \"../../utilities/globals/index.js\";\n\nexport interface ApolloContextValue {\n  client?: ApolloClient<object>;\n  renderPromises?: RenderPromises;\n}\n\n// To make sure Apollo Client doesn't create more than one React context\n// (which can lead to problems like having an Apollo Client instance added\n// in one context, then attempting to retrieve it from another different\n// context), a single Apollo context is created and tracked in global state.\nconst contextKey =\n  canUseSymbol ? Symbol.for(\"__APOLLO_CONTEXT__\") : \"__APOLLO_CONTEXT__\";\n\nexport function getApolloContext(): ReactTypes.Context<ApolloContextValue> {\n  invariant(\n    \"createContext\" in React,\n    \"Invoking `getApolloContext` in an environment where `React.createContext` is not available.\\n\" +\n      \"The Apollo Client functionality you are trying to use is only available in React Client Components.\\n\" +\n      'Please make sure to add \"use client\" at the top of your file.\\n' +\n      // TODO: change to React documentation once React documentation contains information about Client Components\n      \"For more information, see https://nextjs.org/docs/getting-started/react-essentials#client-components\"\n  );\n\n  let context = (React.createContext as any)[\n    contextKey\n  ] as React.Context<ApolloContextValue>;\n  if (!context) {\n    Object.defineProperty(React.createContext, contextKey, {\n      value: (context = React.createContext<ApolloContextValue>({})),\n      enumerable: false,\n      writable: false,\n      configurable: true,\n    });\n    context.displayName = \"ApolloContext\";\n  }\n  return context;\n}\n\n/**\n * @deprecated This function has no \"resetting\" effect since Apollo Client 3.4.12,\n * and will be removed in the next major version of Apollo Client.\n * If you want to get the Apollo Context, use `getApolloContext` instead.\n */\nexport const resetApolloContext = getApolloContext;\n"]}