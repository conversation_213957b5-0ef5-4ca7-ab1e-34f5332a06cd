{"version": 3, "file": "ApolloProvider.js", "sourceRoot": "", "sources": ["../../../src/react/context/ApolloProvider.tsx"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAE7D,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAIjC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAOtD,MAAM,CAAC,IAAM,cAAc,GAA4C,UAAC,EAGvE;QAFC,MAAM,YAAA,EACN,QAAQ,cAAA;IAER,IAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;IACzC,IAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;IAEtD,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;QAC5B,6BACK,aAAa,KAChB,MAAM,EAAE,MAAM,IAAI,aAAa,CAAC,MAAM,IACtC;IACJ,CAAC,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;IAE5B,SAAS,CACP,OAAO,CAAC,MAAM,EACd,wDAAwD;QACtD,qDAAqD,CACxD,CAAC;IAEF,OAAO,CACL,oBAAC,aAAa,CAAC,QAAQ,IAAC,KAAK,EAAE,OAAO,IAAG,QAAQ,CAA0B,CAC5E,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\n\nimport * as React from \"rehackt\";\nimport type * as ReactTypes from \"react\";\n\nimport type { ApolloClient } from \"../../core/index.js\";\nimport { getApolloContext } from \"./ApolloContext.js\";\n\nexport interface ApolloProviderProps<TCache> {\n  client: ApolloClient<TCache>;\n  children: ReactTypes.ReactNode | ReactTypes.ReactNode[] | null;\n}\n\nexport const ApolloProvider: ReactTypes.FC<ApolloProviderProps<any>> = ({\n  client,\n  children,\n}) => {\n  const ApolloContext = getApolloContext();\n  const parentContext = React.useContext(ApolloContext);\n\n  const context = React.useMemo(() => {\n    return {\n      ...parentContext,\n      client: client || parentContext.client,\n    };\n  }, [parentContext, client]);\n\n  invariant(\n    context.client,\n    \"ApolloProvider was not passed a client instance. Make \" +\n      'sure you pass in your client via the \"client\" prop.'\n  );\n\n  return (\n    <ApolloContext.Provider value={context}>{children}</ApolloContext.Provider>\n  );\n};\n"]}