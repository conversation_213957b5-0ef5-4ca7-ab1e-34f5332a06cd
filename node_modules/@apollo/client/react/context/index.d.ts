import "../../utilities/globals/index.js";
export type { ApolloConsumerProps } from "./ApolloConsumer.js";
export { ApolloConsumer } from "./ApolloConsumer.js";
export type { ApolloContextValue } from "./ApolloContext.js";
export { getApolloContext, resetApolloContext } from "./ApolloContext.js";
export type { ApolloProviderProps } from "./ApolloProvider.js";
export { ApolloProvider } from "./ApolloProvider.js";
//# sourceMappingURL=index.d.ts.map