{"version": 3, "file": "context.cjs", "sources": ["ApolloContext.js", "ApolloConsumer.js", "ApolloProvider.js"], "sourcesContent": ["import * as React from \"rehackt\";\nimport { canUseSymbol } from \"../../utilities/index.js\";\nimport { invariant } from \"../../utilities/globals/index.js\";\n// To make sure Apollo Client doesn't create more than one React context\n// (which can lead to problems like having an Apollo Client instance added\n// in one context, then attempting to retrieve it from another different\n// context), a single Apollo context is created and tracked in global state.\nvar contextKey = canUseSymbol ? Symbol.for(\"__APOLLO_CONTEXT__\") : \"__APOLLO_CONTEXT__\";\nexport function getApolloContext() {\n    invariant(\"createContext\" in React, 54);\n    var context = React.createContext[contextKey];\n    if (!context) {\n        Object.defineProperty(React.createContext, contextKey, {\n            value: (context = React.createContext({})),\n            enumerable: false,\n            writable: false,\n            configurable: true,\n        });\n        context.displayName = \"ApolloContext\";\n    }\n    return context;\n}\n/**\n * @deprecated This function has no \"resetting\" effect since Apollo Client 3.4.12,\n * and will be removed in the next major version of Apollo Client.\n * If you want to get the Apollo Context, use `getApolloContext` instead.\n */\nexport var resetApolloContext = getApolloContext;\n//# sourceMappingURL=ApolloContext.js.map", "import { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport { getApolloContext } from \"./ApolloContext.js\";\nexport var ApolloConsumer = function (props) {\n    var ApolloContext = getApolloContext();\n    return (React.createElement(ApolloContext.Consumer, null, function (context) {\n        invariant(context && context.client, 53);\n        return props.children(context.client);\n    }));\n};\n//# sourceMappingURL=ApolloConsumer.js.map", "import { __assign } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport { getApolloContext } from \"./ApolloContext.js\";\nexport var ApolloProvider = function (_a) {\n    var client = _a.client, children = _a.children;\n    var ApolloContext = getApolloContext();\n    var parentContext = React.useContext(ApolloContext);\n    var context = React.useMemo(function () {\n        return __assign(__assign({}, parentContext), { client: client || parentContext.client });\n    }, [parentContext, client]);\n    invariant(context.client, 55);\n    return (React.createElement(ApolloContext.Provider, { value: context }, children));\n};\n//# sourceMappingURL=ApolloProvider.js.map"], "names": ["canUseSymbol", "invariant", "React", "__assign"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAI,UAAU,GAAGA,sBAAY,GAAG,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,oBAAoB,CAAC;AACjF,SAAS,gBAAgB,GAAG;AACnC,IAAIC,iBAAS,CAAC,eAAe,IAAIC,gBAAK,EAAE,EAAE,CAAC,CAAC;AAC5C,IAAI,IAAI,OAAO,GAAGA,gBAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,QAAQ,MAAM,CAAC,cAAc,CAACA,gBAAK,CAAC,aAAa,EAAE,UAAU,EAAE;AAC/D,YAAY,KAAK,GAAG,OAAO,GAAGA,gBAAK,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AACtD,YAAY,UAAU,EAAE,KAAK;AAC7B,YAAY,QAAQ,EAAE,KAAK;AAC3B,YAAY,YAAY,EAAE,IAAI;AAC9B,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,CAAC,WAAW,GAAG,eAAe,CAAC;AAC9C,KAAK;AACL,IAAI,OAAO,OAAO,CAAC;AACnB,CAAC;AAMS,IAAC,kBAAkB,GAAG;;ACxBtB,IAAC,cAAc,GAAG,UAAU,KAAK,EAAE;AAC7C,IAAI,IAAI,aAAa,GAAG,gBAAgB,EAAE,CAAC;AAC3C,IAAI,QAAQA,gBAAK,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,OAAO,EAAE;AACjF,QAAQD,iBAAS,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AACjD,QAAQ,OAAO,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9C,KAAK,CAAC,EAAE;AACR;;ACLU,IAAC,cAAc,GAAG,UAAU,EAAE,EAAE;AAC1C,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC;AACnD,IAAI,IAAI,aAAa,GAAG,gBAAgB,EAAE,CAAC;AAC3C,IAAI,IAAI,aAAa,GAAGC,gBAAK,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;AACxD,IAAI,IAAI,OAAO,GAAGA,gBAAK,CAAC,OAAO,CAAC,YAAY;AAC5C,QAAQ,OAAOC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;AACjG,KAAK,EAAE,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;AAChC,IAAIF,iBAAS,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAClC,IAAI,QAAQC,gBAAK,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,QAAQ,CAAC,EAAE;AACvF;;;;;;;"}