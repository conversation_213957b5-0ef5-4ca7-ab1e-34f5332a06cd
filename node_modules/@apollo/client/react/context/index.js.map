{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/react/context/index.ts"], "names": [], "mappings": "AAAA,OAAO,kCAAkC,CAAC;AAG1C,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAErD,OAAO,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AAE1E,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC", "sourcesContent": ["import \"../../utilities/globals/index.js\";\n\nexport type { ApolloConsumerProps } from \"./ApolloConsumer.js\";\nexport { ApolloConsumer } from \"./ApolloConsumer.js\";\nexport type { ApolloContextValue } from \"./ApolloContext.js\";\nexport { getApolloContext, resetApolloContext } from \"./ApolloContext.js\";\nexport type { ApolloProviderProps } from \"./ApolloProvider.js\";\nexport { ApolloProvider } from \"./ApolloProvider.js\";\n"]}