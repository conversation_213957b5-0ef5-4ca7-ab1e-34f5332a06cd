{"version": 3, "file": "ApolloConsumer.js", "sourceRoot": "", "sources": ["../../../src/react/context/ApolloConsumer.tsx"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAE7D,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAIjC,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAMtD,MAAM,CAAC,IAAM,cAAc,GAAuC,UAAC,KAAK;IACtE,IAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;IACzC,OAAO,CACL,oBAAC,aAAa,CAAC,QAAQ,QACpB,UAAC,OAAY;QACZ,SAAS,CACP,OAAO,IAAI,OAAO,CAAC,MAAM,EACzB,4DAA4D;YAC1D,iDAAiD,CACpD,CAAC;QACF,OAAO,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC,CACsB,CAC1B,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\n\nimport * as React from \"rehackt\";\nimport type * as ReactTypes from \"react\";\n\nimport type { ApolloClient } from \"../../core/index.js\";\nimport { getApolloContext } from \"./ApolloContext.js\";\n\nexport interface ApolloConsumerProps {\n  children: (client: ApolloClient<object>) => ReactTypes.ReactNode;\n}\n\nexport const ApolloConsumer: ReactTypes.FC<ApolloConsumerProps> = (props) => {\n  const ApolloContext = getApolloContext();\n  return (\n    <ApolloContext.Consumer>\n      {(context: any) => {\n        invariant(\n          context && context.client,\n          'Could not find \"client\" in the context of ApolloConsumer. ' +\n            \"Wrap the root component in an <ApolloProvider>.\"\n        );\n        return props.children(context.client);\n      }}\n    </ApolloContext.Consumer>\n  );\n};\n"]}