{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/react/parser/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAQ7D,OAAO,EACL,oBAAoB,EACpB,UAAU,GAEX,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,mBAAmB,EAAE,MAAM,+CAA+C,CAAC;AAEpF,MAAM,CAAN,IAAY,YAIX;AAJD,WAAY,YAAY;IACtB,iDAAK,CAAA;IACL,uDAAQ,CAAA;IACR,+DAAY,CAAA;AACd,CAAC,EAJW,YAAY,KAAZ,YAAY,QAIvB;AAQD,IAAI,KASC,CAAC;AAEN,MAAM,UAAU,aAAa,CAAC,IAAkB;IAC9C,IAAI,IAAI,CAAC;IACT,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,YAAY,CAAC,KAAK;YACrB,IAAI,GAAG,OAAO,CAAC;YACf,MAAM;QACR,KAAK,YAAY,CAAC,QAAQ;YACxB,IAAI,GAAG,UAAU,CAAC;YAClB,MAAM;QACR,KAAK,YAAY,CAAC,YAAY;YAC5B,IAAI,GAAG,cAAc,CAAC;YACtB,MAAM;IACV,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,iEAAiE;AACjE,MAAM,UAAU,MAAM,CAAC,QAAsB;IAC3C,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,KAAK,GAAG,IAAI,oBAAoB,CAC9B,UAAU,CAAC,MAAM,uCAA4B,CAC9C,CAAC;IACJ,CAAC;IACD,IAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACnC,IAAI,MAAM;QAAE,OAAO,MAAM,CAAC;IAE1B,IAAI,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;IAE1B,SAAS,CACP,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,EAC7B,0DAA0D;QACxD,oEAAoE;QACpE,2CAA2C,EAC7C,QAAQ,CACT,CAAC;IAEF,IAAM,SAAS,GAAqB,EAAE,CAAC;IACvC,IAAM,OAAO,GAAqB,EAAE,CAAC;IACrC,IAAM,SAAS,GAAqB,EAAE,CAAC;IACvC,IAAM,aAAa,GAAqB,EAAE,CAAC;IAE3C,KAAgB,UAAoB,EAApB,KAAA,QAAQ,CAAC,WAAW,EAApB,cAAoB,EAApB,IAAoB,EAAE,CAAC;QAAlC,IAAM,CAAC,SAAA;QACV,IAAI,CAAC,CAAC,IAAI,KAAK,oBAAoB,EAAE,CAAC;YACpC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,SAAS;QACX,CAAC;QAED,IAAI,CAAC,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;YACrC,QAAQ,CAAC,CAAC,SAAS,EAAE,CAAC;gBACpB,KAAK,OAAO;oBACV,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAChB,MAAM;gBACR,KAAK,UAAU;oBACb,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBAClB,MAAM;gBACR,KAAK,cAAc;oBACjB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACtB,MAAM;YACV,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS,CACP,CAAC,SAAS,CAAC,MAAM;QACf,OAAO,CAAC,MAAM;QACd,SAAS,CAAC,MAAM;QAChB,aAAa,CAAC,MAAM,EACtB,6DAA6D;QAC3D,4DAA4D,CAC/D,CAAC;IAEF,SAAS,CACP,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,IAAI,CAAC,EAC7D,2EAA2E;QACzE,wBAAwB;QACxB,kCAAkC;QAClC,uEAAuE,EACzE,QAAQ,EACR,OAAO,CAAC,MAAM,EACd,aAAa,CAAC,MAAM,EACpB,SAAS,CAAC,MAAM,CACjB,CAAC;IAEF,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC;IACnE,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM;QAAE,IAAI,GAAG,YAAY,CAAC,YAAY,CAAC;IAE3E,IAAM,WAAW,GACf,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;QACxB,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YAC9B,CAAC,CAAC,aAAa,CAAC;IAElB,SAAS,CACP,WAAW,CAAC,MAAM,KAAK,CAAC,EACxB,4DAA4D;QAC1D,kBAAkB;QAClB,uEAAuE,EACzE,QAAQ,EACR,WAAW,CAAC,MAAM,CACnB,CAAC;IAEF,IAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAA4B,CAAC;IAC7D,SAAS,GAAG,UAAU,CAAC,mBAAmB,IAAI,EAAE,CAAC;IAEjD,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;QACvD,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;IAC/B,CAAC;SAAM,CAAC;QACN,IAAI,GAAG,MAAM,CAAC,CAAC,oCAAoC;IACrD,CAAC;IAED,IAAM,OAAO,GAAG,EAAE,IAAI,MAAA,EAAE,IAAI,MAAA,EAAE,SAAS,WAAA,EAAE,CAAC;IAC1C,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC7B,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,CAAC,UAAU,GAAG;IAClB,KAAK,GAAG,SAAS,CAAC;AACpB,CAAC,CAAC;AAEF,IAAI,OAAO,EAAE,CAAC;IACZ,mBAAmB,CAAC,QAAQ,EAAE,cAAM,OAAA,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAxB,CAAwB,CAAC,CAAC;AAChE,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,QAAsB,EAAE,IAAkB;IAC3E,IAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IACnC,IAAM,qBAAqB,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;IAClD,IAAM,iBAAiB,GAAG,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IACxD,SAAS,CACP,SAAS,CAAC,IAAI,KAAK,IAAI,EACvB,kCAAkC,GAAG,gCAAgC,EACrE,qBAAqB,EACrB,qBAAqB,EACrB,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\n\nimport type {\n  DocumentNode,\n  DefinitionNode,\n  VariableDefinitionNode,\n  OperationDefinitionNode,\n} from \"graphql\";\nimport {\n  AutoCleanedWeakCache,\n  cacheSizes,\n  defaultCacheSizes,\n} from \"../../utilities/index.js\";\nimport { registerGlobalCache } from \"../../utilities/caching/getMemoryInternals.js\";\n\nexport enum DocumentType {\n  Query,\n  Mutation,\n  Subscription,\n}\n\nexport interface IDocumentDefinition {\n  type: DocumentType;\n  name: string;\n  variables: ReadonlyArray<VariableDefinitionNode>;\n}\n\nlet cache:\n  | undefined\n  | AutoCleanedWeakCache<\n      DocumentNode,\n      {\n        name: string;\n        type: DocumentType;\n        variables: readonly VariableDefinitionNode[];\n      }\n    >;\n\nexport function operationName(type: DocumentType) {\n  let name;\n  switch (type) {\n    case DocumentType.Query:\n      name = \"Query\";\n      break;\n    case DocumentType.Mutation:\n      name = \"Mutation\";\n      break;\n    case DocumentType.Subscription:\n      name = \"Subscription\";\n      break;\n  }\n  return name;\n}\n\n// This parser is mostly used to safety check incoming documents.\nexport function parser(document: DocumentNode): IDocumentDefinition {\n  if (!cache) {\n    cache = new AutoCleanedWeakCache(\n      cacheSizes.parser || defaultCacheSizes.parser\n    );\n  }\n  const cached = cache.get(document);\n  if (cached) return cached;\n\n  let variables, type, name;\n\n  invariant(\n    !!document && !!document.kind,\n    `Argument of %s passed to parser was not a valid GraphQL ` +\n      `DocumentNode. You may need to use 'graphql-tag' or another method ` +\n      `to convert your operation into a document`,\n    document\n  );\n\n  const fragments: DefinitionNode[] = [];\n  const queries: DefinitionNode[] = [];\n  const mutations: DefinitionNode[] = [];\n  const subscriptions: DefinitionNode[] = [];\n\n  for (const x of document.definitions) {\n    if (x.kind === \"FragmentDefinition\") {\n      fragments.push(x);\n      continue;\n    }\n\n    if (x.kind === \"OperationDefinition\") {\n      switch (x.operation) {\n        case \"query\":\n          queries.push(x);\n          break;\n        case \"mutation\":\n          mutations.push(x);\n          break;\n        case \"subscription\":\n          subscriptions.push(x);\n          break;\n      }\n    }\n  }\n\n  invariant(\n    !fragments.length ||\n      queries.length ||\n      mutations.length ||\n      subscriptions.length,\n    `Passing only a fragment to 'graphql' is not yet supported. ` +\n      `You must include a query, subscription or mutation as well`\n  );\n\n  invariant(\n    queries.length + mutations.length + subscriptions.length <= 1,\n    `react-apollo only supports a query, subscription, or a mutation per HOC. ` +\n      `%s had %s queries, %s ` +\n      `subscriptions and %s mutations. ` +\n      `You can use 'compose' to join multiple operation types to a component`,\n    document,\n    queries.length,\n    subscriptions.length,\n    mutations.length\n  );\n\n  type = queries.length ? DocumentType.Query : DocumentType.Mutation;\n  if (!queries.length && !mutations.length) type = DocumentType.Subscription;\n\n  const definitions =\n    queries.length ? queries\n    : mutations.length ? mutations\n    : subscriptions;\n\n  invariant(\n    definitions.length === 1,\n    `react-apollo only supports one definition per HOC. %s had ` +\n      `%s definitions. ` +\n      `You can use 'compose' to join multiple operation types to a component`,\n    document,\n    definitions.length\n  );\n\n  const definition = definitions[0] as OperationDefinitionNode;\n  variables = definition.variableDefinitions || [];\n\n  if (definition.name && definition.name.kind === \"Name\") {\n    name = definition.name.value;\n  } else {\n    name = \"data\"; // fallback to using data if no name\n  }\n\n  const payload = { name, type, variables };\n  cache.set(document, payload);\n  return payload;\n}\n\nparser.resetCache = () => {\n  cache = undefined;\n};\n\nif (__DEV__) {\n  registerGlobalCache(\"parser\", () => (cache ? cache.size : 0));\n}\n\nexport function verifyDocumentType(document: DocumentNode, type: DocumentType) {\n  const operation = parser(document);\n  const requiredOperationName = operationName(type);\n  const usedOperationName = operationName(operation.type);\n  invariant(\n    operation.type === type,\n    `Running a %s requires a graphql ` + `%s, but a %s was used instead.`,\n    requiredOperationName,\n    requiredOperationName,\n    usedOperationName\n  );\n}\n"]}