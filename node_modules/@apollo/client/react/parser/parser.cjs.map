{"version": 3, "file": "parser.cjs", "sources": ["../../utilities/caching/getMemoryInternals.js", "index.js"], "sourcesContent": ["import { __assign, __spreadArray } from \"tslib\";\nimport { cacheSizes } from \"./sizes.js\";\nvar globalCaches = {};\nexport function registerGlobalCache(name, getSize) {\n    globalCaches[name] = getSize;\n}\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getApolloClientMemoryInternals = globalThis.__DEV__ !== false ?\n    _getApolloClientMemoryInternals\n    : undefined;\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getInMemoryCacheMemoryInternals = globalThis.__DEV__ !== false ?\n    _getInMemoryCacheMemoryInternals\n    : undefined;\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getApolloCacheMemoryInternals = globalThis.__DEV__ !== false ?\n    _getApolloCacheMemoryInternals\n    : undefined;\nfunction getCurrentCacheSizes() {\n    // `defaultCacheSizes` is a `const enum` that will be inlined during build, so we have to reconstruct it's shape here\n    var defaults = {\n        parser: 1000 /* defaultCacheSizes[\"parser\"] */,\n        canonicalStringify: 1000 /* defaultCacheSizes[\"canonicalStringify\"] */,\n        print: 2000 /* defaultCacheSizes[\"print\"] */,\n        \"documentTransform.cache\": 2000 /* defaultCacheSizes[\"documentTransform.cache\"] */,\n        \"queryManager.getDocumentInfo\": 2000 /* defaultCacheSizes[\"queryManager.getDocumentInfo\"] */,\n        \"PersistedQueryLink.persistedQueryHashes\": 2000 /* defaultCacheSizes[\"PersistedQueryLink.persistedQueryHashes\"] */,\n        \"fragmentRegistry.transform\": 2000 /* defaultCacheSizes[\"fragmentRegistry.transform\"] */,\n        \"fragmentRegistry.lookup\": 1000 /* defaultCacheSizes[\"fragmentRegistry.lookup\"] */,\n        \"fragmentRegistry.findFragmentSpreads\": 4000 /* defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"] */,\n        \"cache.fragmentQueryDocuments\": 1000 /* defaultCacheSizes[\"cache.fragmentQueryDocuments\"] */,\n        \"removeTypenameFromVariables.getVariableDefinitions\": 2000 /* defaultCacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"] */,\n        \"inMemoryCache.maybeBroadcastWatch\": 5000 /* defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] */,\n        \"inMemoryCache.executeSelectionSet\": 50000 /* defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"] */,\n        \"inMemoryCache.executeSubSelectedArray\": 10000 /* defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"] */,\n    };\n    return Object.fromEntries(Object.entries(defaults).map(function (_a) {\n        var k = _a[0], v = _a[1];\n        return [\n            k,\n            cacheSizes[k] || v,\n        ];\n    }));\n}\nfunction _getApolloClientMemoryInternals() {\n    var _a, _b, _c, _d, _e;\n    if (!(globalThis.__DEV__ !== false))\n        throw new Error(\"only supported in development mode\");\n    return {\n        limits: getCurrentCacheSizes(),\n        sizes: __assign({ print: (_a = globalCaches.print) === null || _a === void 0 ? void 0 : _a.call(globalCaches), parser: (_b = globalCaches.parser) === null || _b === void 0 ? void 0 : _b.call(globalCaches), canonicalStringify: (_c = globalCaches.canonicalStringify) === null || _c === void 0 ? void 0 : _c.call(globalCaches), links: linkInfo(this.link), queryManager: {\n                getDocumentInfo: this[\"queryManager\"][\"transformCache\"].size,\n                documentTransforms: transformInfo(this[\"queryManager\"].documentTransform),\n            } }, (_e = (_d = this.cache).getMemoryInternals) === null || _e === void 0 ? void 0 : _e.call(_d)),\n    };\n}\nfunction _getApolloCacheMemoryInternals() {\n    return {\n        cache: {\n            fragmentQueryDocuments: getWrapperInformation(this[\"getFragmentDoc\"]),\n        },\n    };\n}\nfunction _getInMemoryCacheMemoryInternals() {\n    var fragments = this.config.fragments;\n    return __assign(__assign({}, _getApolloCacheMemoryInternals.apply(this)), { addTypenameDocumentTransform: transformInfo(this[\"addTypenameTransform\"]), inMemoryCache: {\n            executeSelectionSet: getWrapperInformation(this[\"storeReader\"][\"executeSelectionSet\"]),\n            executeSubSelectedArray: getWrapperInformation(this[\"storeReader\"][\"executeSubSelectedArray\"]),\n            maybeBroadcastWatch: getWrapperInformation(this[\"maybeBroadcastWatch\"]),\n        }, fragmentRegistry: {\n            findFragmentSpreads: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.findFragmentSpreads),\n            lookup: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.lookup),\n            transform: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.transform),\n        } });\n}\nfunction isWrapper(f) {\n    return !!f && \"dirtyKey\" in f;\n}\nfunction getWrapperInformation(f) {\n    return isWrapper(f) ? f.size : undefined;\n}\nfunction isDefined(value) {\n    return value != null;\n}\nfunction transformInfo(transform) {\n    return recurseTransformInfo(transform).map(function (cache) { return ({ cache: cache }); });\n}\nfunction recurseTransformInfo(transform) {\n    return transform ?\n        __spreadArray(__spreadArray([\n            getWrapperInformation(transform === null || transform === void 0 ? void 0 : transform[\"performWork\"])\n        ], recurseTransformInfo(transform === null || transform === void 0 ? void 0 : transform[\"left\"]), true), recurseTransformInfo(transform === null || transform === void 0 ? void 0 : transform[\"right\"]), true).filter(isDefined)\n        : [];\n}\nfunction linkInfo(link) {\n    var _a;\n    return link ?\n        __spreadArray(__spreadArray([\n            (_a = link === null || link === void 0 ? void 0 : link.getMemoryInternals) === null || _a === void 0 ? void 0 : _a.call(link)\n        ], linkInfo(link === null || link === void 0 ? void 0 : link.left), true), linkInfo(link === null || link === void 0 ? void 0 : link.right), true).filter(isDefined)\n        : [];\n}\n//# sourceMappingURL=getMemoryInternals.js.map", "import { invariant } from \"../../utilities/globals/index.js\";\nimport { AutoCleanedWeakCache, cacheSizes, } from \"../../utilities/index.js\";\nimport { registerGlobalCache } from \"../../utilities/caching/getMemoryInternals.js\";\nexport var DocumentType;\n(function (DocumentType) {\n    DocumentType[DocumentType[\"Query\"] = 0] = \"Query\";\n    DocumentType[DocumentType[\"Mutation\"] = 1] = \"Mutation\";\n    DocumentType[DocumentType[\"Subscription\"] = 2] = \"Subscription\";\n})(DocumentType || (DocumentType = {}));\nvar cache;\nexport function operationName(type) {\n    var name;\n    switch (type) {\n        case DocumentType.Query:\n            name = \"Query\";\n            break;\n        case DocumentType.Mutation:\n            name = \"Mutation\";\n            break;\n        case DocumentType.Subscription:\n            name = \"Subscription\";\n            break;\n    }\n    return name;\n}\n// This parser is mostly used to safety check incoming documents.\nexport function parser(document) {\n    if (!cache) {\n        cache = new AutoCleanedWeakCache(cacheSizes.parser || 1000 /* defaultCacheSizes.parser */);\n    }\n    var cached = cache.get(document);\n    if (cached)\n        return cached;\n    var variables, type, name;\n    invariant(!!document && !!document.kind, 70, document);\n    var fragments = [];\n    var queries = [];\n    var mutations = [];\n    var subscriptions = [];\n    for (var _i = 0, _a = document.definitions; _i < _a.length; _i++) {\n        var x = _a[_i];\n        if (x.kind === \"FragmentDefinition\") {\n            fragments.push(x);\n            continue;\n        }\n        if (x.kind === \"OperationDefinition\") {\n            switch (x.operation) {\n                case \"query\":\n                    queries.push(x);\n                    break;\n                case \"mutation\":\n                    mutations.push(x);\n                    break;\n                case \"subscription\":\n                    subscriptions.push(x);\n                    break;\n            }\n        }\n    }\n    invariant(!fragments.length ||\n        queries.length ||\n        mutations.length ||\n        subscriptions.length, 71);\n    invariant(\n        queries.length + mutations.length + subscriptions.length <= 1,\n        72,\n        document,\n        queries.length,\n        subscriptions.length,\n        mutations.length\n    );\n    type = queries.length ? DocumentType.Query : DocumentType.Mutation;\n    if (!queries.length && !mutations.length)\n        type = DocumentType.Subscription;\n    var definitions = queries.length ? queries\n        : mutations.length ? mutations\n            : subscriptions;\n    invariant(definitions.length === 1, 73, document, definitions.length);\n    var definition = definitions[0];\n    variables = definition.variableDefinitions || [];\n    if (definition.name && definition.name.kind === \"Name\") {\n        name = definition.name.value;\n    }\n    else {\n        name = \"data\"; // fallback to using data if no name\n    }\n    var payload = { name: name, type: type, variables: variables };\n    cache.set(document, payload);\n    return payload;\n}\nparser.resetCache = function () {\n    cache = undefined;\n};\nif (globalThis.__DEV__ !== false) {\n    registerGlobalCache(\"parser\", function () { return (cache ? cache.size : 0); });\n}\nexport function verifyDocumentType(document, type) {\n    var operation = parser(document);\n    var requiredOperationName = operationName(type);\n    var usedOperationName = operationName(operation.type);\n    invariant(\n        operation.type === type,\n        74,\n        requiredOperationName,\n        requiredOperationName,\n        usedOperationName\n    );\n}\n//# sourceMappingURL=index.js.map"], "names": ["DocumentType", "AutoCleanedWeakCache", "cacheSizes", "invariant"], "mappings": ";;;;;;;;AAEA,IAAI,YAAY,GAAG,EAAE,CAAC;AACf,SAAS,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE;AACnD,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;AACjC;;ACFWA,8BAAa;AACxB,CAAC,UAAU,YAAY,EAAE;AACzB,IAAI,YAAY,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,CAAC;AACtD,IAAI,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC;AAC5D,IAAI,YAAY,CAAC,YAAY,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,cAAc,CAAC;AACpE,CAAC,EAAEA,oBAAY,KAAKA,oBAAY,GAAG,EAAE,CAAC,CAAC,CAAC;AACxC,IAAI,KAAK,CAAC;AACH,SAAS,aAAa,CAAC,IAAI,EAAE;AACpC,IAAI,IAAI,IAAI,CAAC;AACb,IAAI,QAAQ,IAAI;AAChB,QAAQ,KAAKA,oBAAY,CAAC,KAAK;AAC/B,YAAY,IAAI,GAAG,OAAO,CAAC;AAC3B,YAAY,MAAM;AAClB,QAAQ,KAAKA,oBAAY,CAAC,QAAQ;AAClC,YAAY,IAAI,GAAG,UAAU,CAAC;AAC9B,YAAY,MAAM;AAClB,QAAQ,KAAKA,oBAAY,CAAC,YAAY;AACtC,YAAY,IAAI,GAAG,cAAc,CAAC;AAClC,YAAY,MAAM;AAClB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AAEM,SAAS,MAAM,CAAC,QAAQ,EAAE;AACjC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,QAAQ,KAAK,GAAG,IAAIC,8BAAoB,CAACC,oBAAU,CAAC,MAAM,IAAI,IAAI,EAAgC,CAAC;AACnG,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI,IAAI,MAAM;AACd,QAAQ,OAAO,MAAM,CAAC;AACtB,IAAI,IAAI,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC;AAC9B,IAAIC,iBAAS,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,EAAE,QAAQ,CAAC,CAAC;AAC3D,IAAI,IAAI,SAAS,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,SAAS,GAAG,EAAE,CAAC;AACvB,IAAI,IAAI,aAAa,GAAG,EAAE,CAAC;AAC3B,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,QAAQ,CAAC,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtE,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,oBAAoB,EAAE;AAC7C,YAAY,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9B,YAAY,SAAS;AACrB,SAAS;AACT,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,qBAAqB,EAAE;AAC9C,YAAY,QAAQ,CAAC,CAAC,SAAS;AAC/B,gBAAgB,KAAK,OAAO;AAC5B,oBAAoB,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACpC,oBAAoB,MAAM;AAC1B,gBAAgB,KAAK,UAAU;AAC/B,oBAAoB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtC,oBAAoB,MAAM;AAC1B,gBAAgB,KAAK,cAAc;AACnC,oBAAoB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C,oBAAoB,MAAM;AAC1B,aAAa;AACb,SAAS;AACT,KAAK;AACL,IAAIA,iBAAS,CAAC,CAAC,SAAS,CAAC,MAAM;AAC/B,QAAQ,OAAO,CAAC,MAAM;AACtB,QAAQ,SAAS,CAAC,MAAM;AACxB,QAAQ,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAClC,IAAIA,iBAAS;AACb,QAAQ,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,IAAI,CAAC;AACrE,QAAQ,EAAE;AACV,QAAQ,QAAQ;AAChB,QAAQ,OAAO,CAAC,MAAM;AACtB,QAAQ,aAAa,CAAC,MAAM;AAC5B,QAAQ,SAAS,CAAC,MAAM;AACxB,KAAK,CAAC;AACN,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,GAAGH,oBAAY,CAAC,KAAK,GAAGA,oBAAY,CAAC,QAAQ,CAAC;AACvE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM;AAC5C,QAAQ,IAAI,GAAGA,oBAAY,CAAC,YAAY,CAAC;AACzC,IAAI,IAAI,WAAW,GAAG,OAAO,CAAC,MAAM,GAAG,OAAO;AAC9C,UAAU,SAAS,CAAC,MAAM,GAAG,SAAS;AACtC,cAAc,aAAa,CAAC;AAC5B,IAAIG,iBAAS,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,WAAW,CAAC,MAAM,CAAC,CAAC;AAC1E,IAAI,IAAI,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACpC,IAAI,SAAS,GAAG,UAAU,CAAC,mBAAmB,IAAI,EAAE,CAAC;AACrD,IAAI,IAAI,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;AAC5D,QAAQ,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;AACrC,KAAK;AACL,SAAS;AACT,QAAQ,IAAI,GAAG,MAAM,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC;AACnE,IAAI,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACjC,IAAI,OAAO,OAAO,CAAC;AACnB,CAAC;AACD,MAAM,CAAC,UAAU,GAAG,YAAY;AAChC,IAAI,KAAK,GAAG,SAAS,CAAC;AACtB,CAAC,CAAC;AACF,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAClC,IAAI,mBAAmB,CAAC,QAAQ,EAAE,YAAY,EAAE,QAAQ,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;AACpF,CAAC;AACM,SAAS,kBAAkB,CAAC,QAAQ,EAAE,IAAI,EAAE;AACnD,IAAI,IAAI,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrC,IAAI,IAAI,qBAAqB,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;AACpD,IAAI,IAAI,iBAAiB,GAAG,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC1D,IAAIA,iBAAS;AACb,QAAQ,SAAS,CAAC,IAAI,KAAK,IAAI;AAC/B,QAAQ,EAAE;AACV,QAAQ,qBAAqB;AAC7B,QAAQ,qBAAqB;AAC7B,QAAQ,iBAAiB;AACzB,KAAK,CAAC;AACN;;;;;;"}