{"version": 3, "file": "ssr.cjs", "sources": ["RenderPromises.js", "getDataFromTree.js", "renderToStringWithData.js"], "sourcesContent": ["import { Trie } from \"@wry/trie\";\nimport { canonicalStringify } from \"../../cache/index.js\";\nfunction makeQueryInfoTrie() {\n    // these Tries are very short-lived, so we don't need to worry about making it\n    // \"weak\" - it's easier to test and debug as a strong Trie.\n    return new Trie(false, function () { return ({\n        seen: false,\n        observable: null,\n    }); });\n}\nvar RenderPromises = /** @class */ (function () {\n    function RenderPromises() {\n        // Map from Query component instances to pending fetchData promises.\n        this.queryPromises = new Map();\n        // Two-layered map from (query document, stringified variables) to QueryInfo\n        // objects. These QueryInfo objects are intended to survive through the whole\n        // getMarkupFromTree process, whereas specific Query instances do not survive\n        // beyond a single call to renderToStaticMarkup.\n        this.queryInfoTrie = makeQueryInfoTrie();\n        this.stopped = false;\n    }\n    RenderPromises.prototype.stop = function () {\n        if (!this.stopped) {\n            this.queryPromises.clear();\n            this.queryInfoTrie = makeQueryInfoTrie();\n            this.stopped = true;\n        }\n    };\n    // Registers the server side rendered observable.\n    RenderPromises.prototype.registerSSRObservable = function (observable) {\n        if (this.stopped)\n            return;\n        this.lookupQueryInfo(observable.options).observable = observable;\n    };\n    // Get's the cached observable that matches the SSR Query instances query and variables.\n    RenderPromises.prototype.getSSRObservable = function (props) {\n        return this.lookupQueryInfo(props).observable;\n    };\n    RenderPromises.prototype.addQueryPromise = function (queryInstance, finish) {\n        if (!this.stopped) {\n            var info = this.lookupQueryInfo(queryInstance.getOptions());\n            if (!info.seen) {\n                this.queryPromises.set(queryInstance.getOptions(), new Promise(function (resolve) {\n                    resolve(queryInstance.fetchData());\n                }));\n                // Render null to abandon this subtree for this rendering, so that we\n                // can wait for the data to arrive.\n                return null;\n            }\n        }\n        return finish ? finish() : null;\n    };\n    RenderPromises.prototype.addObservableQueryPromise = function (obsQuery) {\n        return this.addQueryPromise({\n            // The only options which seem to actually be used by the\n            // RenderPromises class are query and variables.\n            getOptions: function () { return obsQuery.options; },\n            fetchData: function () {\n                return new Promise(function (resolve) {\n                    var sub = obsQuery.subscribe({\n                        next: function (result) {\n                            if (!result.loading) {\n                                resolve();\n                                sub.unsubscribe();\n                            }\n                        },\n                        error: function () {\n                            resolve();\n                            sub.unsubscribe();\n                        },\n                        complete: function () {\n                            resolve();\n                        },\n                    });\n                });\n            },\n        });\n    };\n    RenderPromises.prototype.hasPromises = function () {\n        return this.queryPromises.size > 0;\n    };\n    RenderPromises.prototype.consumeAndAwaitPromises = function () {\n        var _this = this;\n        var promises = [];\n        this.queryPromises.forEach(function (promise, queryInstance) {\n            // Make sure we never try to call fetchData for this query document and\n            // these variables again. Since the queryInstance objects change with\n            // every rendering, deduplicating them by query and variables is the\n            // best we can do. If a different Query component happens to have the\n            // same query document and variables, it will be immediately rendered\n            // by calling finish() in addQueryPromise, which could result in the\n            // rendering of an unwanted loading state, but that's not nearly as bad\n            // as getting stuck in an infinite rendering loop because we kept calling\n            // queryInstance.fetchData for the same Query component indefinitely.\n            _this.lookupQueryInfo(queryInstance).seen = true;\n            promises.push(promise);\n        });\n        this.queryPromises.clear();\n        return Promise.all(promises);\n    };\n    RenderPromises.prototype.lookupQueryInfo = function (props) {\n        return this.queryInfoTrie.lookup(props.query, canonicalStringify(props.variables));\n    };\n    return RenderPromises;\n}());\nexport { RenderPromises };\n//# sourceMappingURL=RenderPromises.js.map", "import { __assign } from \"tslib\";\nimport * as React from \"rehackt\";\nimport { getApolloContext } from \"../context/index.js\";\nimport { RenderPromises } from \"./RenderPromises.js\";\nimport { renderToStaticMarkup } from \"react-dom/server\";\nexport function getDataFromTree(tree, context) {\n    if (context === void 0) { context = {}; }\n    return getMarkupFromTree({\n        tree: tree,\n        context: context,\n        // If you need to configure this renderFunction, call getMarkupFromTree\n        // directly instead of getDataFromTree.\n        renderFunction: renderToStaticMarkup,\n    });\n}\nexport function getMarkupFromTree(_a) {\n    var tree = _a.tree, _b = _a.context, context = _b === void 0 ? {} : _b, \n    // The rendering function is configurable! We use renderToStaticMarkup as\n    // the default, because it's a little less expensive than renderToString,\n    // and legacy usage of getDataFromTree ignores the return value anyway.\n    _c = _a.renderFunction, \n    // The rendering function is configurable! We use renderToStaticMarkup as\n    // the default, because it's a little less expensive than renderToString,\n    // and legacy usage of getDataFromTree ignores the return value anyway.\n    renderFunction = _c === void 0 ? renderToStaticMarkup : _c;\n    var renderPromises = new RenderPromises();\n    function process() {\n        // Always re-render from the rootElement, even though it might seem\n        // better to render the children of the component responsible for the\n        // promise, because it is not possible to reconstruct the full context\n        // of the original rendering (including all unknown context provider\n        // elements) for a subtree of the original component tree.\n        var ApolloContext = getApolloContext();\n        return new Promise(function (resolve) {\n            var element = React.createElement(ApolloContext.Provider, { value: __assign(__assign({}, context), { renderPromises: renderPromises }) }, tree);\n            resolve(renderFunction(element));\n        })\n            .then(function (html) {\n            return renderPromises.hasPromises() ?\n                renderPromises.consumeAndAwaitPromises().then(process)\n                : html;\n        })\n            .finally(function () {\n            renderPromises.stop();\n        });\n    }\n    return Promise.resolve().then(process);\n}\n//# sourceMappingURL=getDataFromTree.js.map", "import { getMarkupFromTree } from \"./getDataFromTree.js\";\nimport { renderToString } from \"react-dom/server\";\nexport function renderToStringWithData(component) {\n    return getMarkupFromTree({\n        tree: component,\n        renderFunction: renderToString,\n    });\n}\n//# sourceMappingURL=renderToStringWithData.js.map"], "names": ["<PERSON><PERSON>", "canonicalStringify", "renderToStaticMarkup", "context", "getApolloContext", "React", "__assign", "renderToString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAAS,iBAAiB,GAAG;AAG7B,IAAI,OAAO,IAAIA,SAAI,CAAC,KAAK,EAAE,YAAY,EAAE,QAAQ;AACjD,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,UAAU,EAAE,IAAI;AACxB,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,CAAC;AACE,IAAC,cAAc,KAAkB,YAAY;AAChD,IAAI,SAAS,cAAc,GAAG;AAE9B,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;AAKvC,QAAQ,IAAI,CAAC,aAAa,GAAG,iBAAiB,EAAE,CAAC;AACjD,QAAQ,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AAC7B,KAAK;AACL,IAAI,cAAc,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;AAChD,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC3B,YAAY,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACvC,YAAY,IAAI,CAAC,aAAa,GAAG,iBAAiB,EAAE,CAAC;AACrD,YAAY,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AAChC,SAAS;AACT,KAAK,CAAC;AAEN,IAAI,cAAc,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,UAAU,EAAE;AAC3E,QAAQ,IAAI,IAAI,CAAC,OAAO;AACxB,YAAY,OAAO;AACnB,QAAQ,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,UAAU,GAAG,UAAU,CAAC;AACzE,KAAK,CAAC;AAEN,IAAI,cAAc,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,KAAK,EAAE;AACjE,QAAQ,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC;AACtD,KAAK,CAAC;AACN,IAAI,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,aAAa,EAAE,MAAM,EAAE;AAChF,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC3B,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;AACxE,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AAC5B,gBAAgB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,UAAU,EAAE,EAAE,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE;AAClG,oBAAoB,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC;AACvD,iBAAiB,CAAC,CAAC,CAAC;AAGpB,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,MAAM,GAAG,MAAM,EAAE,GAAG,IAAI,CAAC;AACxC,KAAK,CAAC;AACN,IAAI,cAAc,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,QAAQ,EAAE;AAC7E,QAAQ,OAAO,IAAI,CAAC,eAAe,CAAC;AAGpC,YAAY,UAAU,EAAE,YAAY,EAAE,OAAO,QAAQ,CAAC,OAAO,CAAC,EAAE;AAChE,YAAY,SAAS,EAAE,YAAY;AACnC,gBAAgB,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE;AACtD,oBAAoB,IAAI,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC;AACjD,wBAAwB,IAAI,EAAE,UAAU,MAAM,EAAE;AAChD,4BAA4B,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AACjD,gCAAgC,OAAO,EAAE,CAAC;AAC1C,gCAAgC,GAAG,CAAC,WAAW,EAAE,CAAC;AAClD,6BAA6B;AAC7B,yBAAyB;AACzB,wBAAwB,KAAK,EAAE,YAAY;AAC3C,4BAA4B,OAAO,EAAE,CAAC;AACtC,4BAA4B,GAAG,CAAC,WAAW,EAAE,CAAC;AAC9C,yBAAyB;AACzB,wBAAwB,QAAQ,EAAE,YAAY;AAC9C,4BAA4B,OAAO,EAAE,CAAC;AACtC,yBAAyB;AACzB,qBAAqB,CAAC,CAAC;AACvB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,cAAc,CAAC,SAAS,CAAC,WAAW,GAAG,YAAY;AACvD,QAAQ,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;AAC3C,KAAK,CAAC;AACN,IAAI,cAAc,CAAC,SAAS,CAAC,uBAAuB,GAAG,YAAY;AACnE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,QAAQ,GAAG,EAAE,CAAC;AAC1B,QAAQ,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,aAAa,EAAE;AAUrE,YAAY,KAAK,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7D,YAAY,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACnC,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;AACnC,QAAQ,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACrC,KAAK,CAAC;AACN,IAAI,cAAc,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,KAAK,EAAE;AAChE,QAAQ,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAEC,wBAAkB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AAC3F,KAAK,CAAC;AACN,IAAI,OAAO,cAAc,CAAC;AAC1B,CAAC,EAAE;;ACnGI,SAAS,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE;AAC/C,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AAC7C,IAAI,OAAO,iBAAiB,CAAC;AAC7B,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,OAAO,EAAE,OAAO;AAGxB,QAAQ,cAAc,EAAEC,2BAAoB;AAC5C,KAAK,CAAC,CAAC;AACP,CAAC;AACM,SAAS,iBAAiB,CAAC,EAAE,EAAE;AACtC,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,EAAEC,SAAO,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;AAI1E,IAAI,EAAE,GAAG,EAAE,CAAC,cAAc;AAI1B,IAAI,cAAc,GAAG,EAAE,KAAK,KAAK,CAAC,GAAGD,2BAAoB,GAAG,EAAE,CAAC;AAC/D,IAAI,IAAI,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;AAC9C,IAAI,SAAS,OAAO,GAAG;AAMvB,QAAQ,IAAI,aAAa,GAAGE,wBAAgB,EAAE,CAAC;AAC/C,QAAQ,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE;AAC9C,YAAY,IAAI,OAAO,GAAGC,gBAAK,CAAC,aAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAEC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAEH,SAAO,CAAC,EAAE,EAAE,cAAc,EAAE,cAAc,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AAC5J,YAAY,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7C,SAAS,CAAC;AACV,aAAa,IAAI,CAAC,UAAU,IAAI,EAAE;AAClC,YAAY,OAAO,cAAc,CAAC,WAAW,EAAE;AAC/C,gBAAgB,cAAc,CAAC,uBAAuB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;AACtE,kBAAkB,IAAI,CAAC;AACvB,SAAS,CAAC;AACV,aAAa,OAAO,CAAC,YAAY;AACjC,YAAY,cAAc,CAAC,IAAI,EAAE,CAAC;AAClC,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3C;;AC7CO,SAAS,sBAAsB,CAAC,SAAS,EAAE;AAClD,IAAI,OAAO,iBAAiB,CAAC;AAC7B,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,cAAc,EAAEI,qBAAc;AACtC,KAAK,CAAC,CAAC;AACP;;;;;;;"}