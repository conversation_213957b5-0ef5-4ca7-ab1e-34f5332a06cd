{"version": 3, "file": "renderToStringWithData.js", "sourceRoot": "", "sources": ["../../../src/react/ssr/renderToStringWithData.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAC;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,MAAM,UAAU,sBAAsB,CACpC,SAAuC;IAEvC,OAAO,iBAAiB,CAAC;QACvB,IAAI,EAAE,SAAS;QACf,cAAc,EAAE,cAAc;KAC/B,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import type * as ReactTypes from \"react\";\nimport { getMarkupFromTree } from \"./getDataFromTree.js\";\nimport { renderToString } from \"react-dom/server\";\n\nexport function renderToStringWithData(\n  component: ReactTypes.ReactElement<any>\n): Promise<string> {\n  return getMarkupFromTree({\n    tree: component,\n    renderFunction: renderToString,\n  });\n}\n"]}