{"version": 3, "file": "RenderPromises.js", "sourceRoot": "", "sources": ["../../../src/react/ssr/RenderPromises.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AACjC,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAc1D,SAAS,iBAAiB;IACxB,8EAA8E;IAC9E,2DAA2D;IAC3D,OAAO,IAAI,IAAI,CAAY,KAAK,EAAE,cAAM,OAAA,CAAC;QACvC,IAAI,EAAE,KAAK;QACX,UAAU,EAAE,IAAI;KACjB,CAAC,EAHsC,CAGtC,CAAC,CAAC;AACN,CAAC;AAED;IAAA;QACE,oEAAoE;QAC5D,kBAAa,GAAG,IAAI,GAAG,EAA4C,CAAC;QAE5E,4EAA4E;QAC5E,6EAA6E;QAC7E,6EAA6E;QAC7E,gDAAgD;QACxC,kBAAa,GAAG,iBAAiB,EAAE,CAAC;QAEpC,YAAO,GAAG,KAAK,CAAC;IAyG1B,CAAC;IAxGQ,6BAAI,GAAX;QACE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YAC3B,IAAI,CAAC,aAAa,GAAG,iBAAiB,EAAE,CAAC;YACzC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;IACH,CAAC;IAED,iDAAiD;IAC1C,8CAAqB,GAA5B,UACE,UAA4C;QAE5C,IAAI,IAAI,CAAC,OAAO;YAAE,OAAO;QACzB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,UAAU,GAAG,UAAU,CAAC;IACnE,CAAC;IAED,wFAAwF;IACjF,yCAAgB,GAAvB,UACE,KAA0C;QAE1C,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC;IAChD,CAAC;IAEM,wCAAe,GAAtB,UACE,aAAwB,EACxB,MAAmC;QAEnC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;YAC9D,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACf,IAAI,CAAC,aAAa,CAAC,GAAG,CACpB,aAAa,CAAC,UAAU,EAAE,EAC1B,IAAI,OAAO,CAAC,UAAC,OAAO;oBAClB,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC;gBACrC,CAAC,CAAC,CACH,CAAC;gBACF,qEAAqE;gBACrE,mCAAmC;gBACnC,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAClC,CAAC;IAEM,kDAAyB,GAAhC,UAGE,QAA4C;QAC5C,OAAO,IAAI,CAAC,eAAe,CAAC;YAC1B,yDAAyD;YACzD,gDAAgD;YAChD,UAAU,EAAE,cAAM,OAAA,QAAQ,CAAC,OAAO,EAAhB,CAAgB;YAClC,SAAS,EAAE;gBACT,OAAA,IAAI,OAAO,CAAO,UAAC,OAAO;oBACxB,IAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC;wBAC7B,IAAI,YAAC,MAAM;4BACT,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gCACpB,OAAO,EAAE,CAAC;gCACV,GAAG,CAAC,WAAW,EAAE,CAAC;4BACpB,CAAC;wBACH,CAAC;wBACD,KAAK;4BACH,OAAO,EAAE,CAAC;4BACV,GAAG,CAAC,WAAW,EAAE,CAAC;wBACpB,CAAC;wBACD,QAAQ;4BACN,OAAO,EAAE,CAAC;wBACZ,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC,CAAC;YAhBF,CAgBE;SACL,CAAC,CAAC;IACL,CAAC;IAEM,oCAAW,GAAlB;QACE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,CAAC,CAAC;IACrC,CAAC;IAEM,gDAAuB,GAA9B;QAAA,iBAiBC;QAhBC,IAAM,QAAQ,GAAmB,EAAE,CAAC;QACpC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,aAAa;YAChD,uEAAuE;YACvE,qEAAqE;YACrE,oEAAoE;YACpE,qEAAqE;YACrE,qEAAqE;YACrE,oEAAoE;YACpE,uEAAuE;YACvE,yEAAyE;YACzE,qEAAqE;YACrE,KAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;YAChD,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAEO,wCAAe,GAAvB,UACE,KAA0C;QAE1C,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAC9B,KAAK,CAAC,KAAK,EACX,kBAAkB,CAAC,KAAK,CAAC,SAAS,CAAC,CACpC,CAAC;IACJ,CAAC;IACH,qBAAC;AAAD,CAAC,AAnHD,IAmHC", "sourcesContent": ["import type * as ReactTypes from \"react\";\n\nimport type { ObservableQuery, OperationVariables } from \"../../core/index.js\";\nimport type { QueryDataOptions } from \"../types/types.js\";\nimport { Trie } from \"@wry/trie\";\nimport { canonicalStringify } from \"../../cache/index.js\";\n\n// TODO: A vestigial interface from when hooks were implemented with utility\n// classes, which should be deleted in the future.\ninterface QueryData {\n  getOptions(): any;\n  fetchData(): Promise<void>;\n}\n\ntype QueryInfo = {\n  seen: boolean;\n  observable: ObservableQuery<any, any> | null;\n};\n\nfunction makeQueryInfoTrie() {\n  // these Tries are very short-lived, so we don't need to worry about making it\n  // \"weak\" - it's easier to test and debug as a strong Trie.\n  return new Trie<QueryInfo>(false, () => ({\n    seen: false,\n    observable: null,\n  }));\n}\n\nexport class RenderPromises {\n  // Map from Query component instances to pending fetchData promises.\n  private queryPromises = new Map<QueryDataOptions<any, any>, Promise<any>>();\n\n  // Two-layered map from (query document, stringified variables) to QueryInfo\n  // objects. These QueryInfo objects are intended to survive through the whole\n  // getMarkupFromTree process, whereas specific Query instances do not survive\n  // beyond a single call to renderToStaticMarkup.\n  private queryInfoTrie = makeQueryInfoTrie();\n\n  private stopped = false;\n  public stop() {\n    if (!this.stopped) {\n      this.queryPromises.clear();\n      this.queryInfoTrie = makeQueryInfoTrie();\n      this.stopped = true;\n    }\n  }\n\n  // Registers the server side rendered observable.\n  public registerSSRObservable<TData, TVariables extends OperationVariables>(\n    observable: ObservableQuery<any, TVariables>\n  ) {\n    if (this.stopped) return;\n    this.lookupQueryInfo(observable.options).observable = observable;\n  }\n\n  // Get's the cached observable that matches the SSR Query instances query and variables.\n  public getSSRObservable<TData, TVariables extends OperationVariables>(\n    props: QueryDataOptions<TData, TVariables>\n  ): ObservableQuery<any, TVariables> | null {\n    return this.lookupQueryInfo(props).observable;\n  }\n\n  public addQueryPromise(\n    queryInstance: QueryData,\n    finish?: () => ReactTypes.ReactNode\n  ): ReactTypes.ReactNode {\n    if (!this.stopped) {\n      const info = this.lookupQueryInfo(queryInstance.getOptions());\n      if (!info.seen) {\n        this.queryPromises.set(\n          queryInstance.getOptions(),\n          new Promise((resolve) => {\n            resolve(queryInstance.fetchData());\n          })\n        );\n        // Render null to abandon this subtree for this rendering, so that we\n        // can wait for the data to arrive.\n        return null;\n      }\n    }\n    return finish ? finish() : null;\n  }\n\n  public addObservableQueryPromise<\n    TData,\n    TVariables extends OperationVariables,\n  >(obsQuery: ObservableQuery<TData, TVariables>) {\n    return this.addQueryPromise({\n      // The only options which seem to actually be used by the\n      // RenderPromises class are query and variables.\n      getOptions: () => obsQuery.options,\n      fetchData: () =>\n        new Promise<void>((resolve) => {\n          const sub = obsQuery.subscribe({\n            next(result) {\n              if (!result.loading) {\n                resolve();\n                sub.unsubscribe();\n              }\n            },\n            error() {\n              resolve();\n              sub.unsubscribe();\n            },\n            complete() {\n              resolve();\n            },\n          });\n        }),\n    });\n  }\n\n  public hasPromises() {\n    return this.queryPromises.size > 0;\n  }\n\n  public consumeAndAwaitPromises() {\n    const promises: Promise<any>[] = [];\n    this.queryPromises.forEach((promise, queryInstance) => {\n      // Make sure we never try to call fetchData for this query document and\n      // these variables again. Since the queryInstance objects change with\n      // every rendering, deduplicating them by query and variables is the\n      // best we can do. If a different Query component happens to have the\n      // same query document and variables, it will be immediately rendered\n      // by calling finish() in addQueryPromise, which could result in the\n      // rendering of an unwanted loading state, but that's not nearly as bad\n      // as getting stuck in an infinite rendering loop because we kept calling\n      // queryInstance.fetchData for the same Query component indefinitely.\n      this.lookupQueryInfo(queryInstance).seen = true;\n      promises.push(promise);\n    });\n    this.queryPromises.clear();\n    return Promise.all(promises);\n  }\n\n  private lookupQueryInfo<TData, TVariables extends OperationVariables>(\n    props: QueryDataOptions<TData, TVariables>\n  ): QueryInfo {\n    return this.queryInfoTrie.lookup(\n      props.query,\n      canonicalStringify(props.variables)\n    );\n  }\n}\n"]}