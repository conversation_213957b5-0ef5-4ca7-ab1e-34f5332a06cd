{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/react/ssr/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAC1E,OAAO,EAAE,sBAAsB,EAAE,MAAM,6BAA6B,CAAC;AACrE,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC", "sourcesContent": ["export { getMarkupFromTree, getDataFromTree } from \"./getDataFromTree.js\";\nexport { renderToStringWithData } from \"./renderToStringWithData.js\";\nexport { RenderPromises } from \"./RenderPromises.js\";\n"]}