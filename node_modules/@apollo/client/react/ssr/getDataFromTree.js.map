{"version": 3, "file": "getDataFromTree.js", "sourceRoot": "", "sources": ["../../../src/react/ssr/getDataFromTree.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAEjC,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAExD,MAAM,UAAU,eAAe,CAC7B,IAA0B,EAC1B,OAAoC;IAApC,wBAAA,EAAA,YAAoC;IAEpC,OAAO,iBAAiB,CAAC;QACvB,IAAI,MAAA;QACJ,OAAO,SAAA;QACP,uEAAuE;QACvE,uCAAuC;QACvC,cAAc,EAAE,oBAAoB;KACrC,CAAC,CAAC;AACL,CAAC;AAUD,MAAM,UAAU,iBAAiB,CAAC,EAOP;QANzB,IAAI,UAAA,EACJ,eAAY,EAAZ,OAAO,mBAAG,EAAE,KAAA;IACZ,yEAAyE;IACzE,yEAAyE;IACzE,uEAAuE;IACvE,sBAAqC;IAHrC,yEAAyE;IACzE,yEAAyE;IACzE,uEAAuE;IACvE,cAAc,mBAAG,oBAAoB,KAAA;IAErC,IAAM,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;IAE5C,SAAS,OAAO;QACd,mEAAmE;QACnE,qEAAqE;QACrE,sEAAsE;QACtE,oEAAoE;QACpE,0DAA0D;QAC1D,IAAM,aAAa,GAAG,gBAAgB,EAAE,CAAC;QAEzC,OAAO,IAAI,OAAO,CAAS,UAAC,OAAO;YACjC,IAAM,OAAO,GAAG,KAAK,CAAC,aAAa,CACjC,aAAa,CAAC,QAAQ,EACtB,EAAE,KAAK,wBAAO,OAAO,KAAE,cAAc,gBAAA,GAAE,EAAE,EACzC,IAAI,CACL,CAAC;YACF,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;QACnC,CAAC,CAAC;aACC,IAAI,CAAC,UAAC,IAAI;YACT,OAAO,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;gBACjC,cAAc,CAAC,uBAAuB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;gBACxD,CAAC,CAAC,IAAI,CAAC;QACX,CAAC,CAAC;aACD,OAAO,CAAC;YACP,cAAc,CAAC,IAAI,EAAE,CAAC;QACxB,CAAC,CAAC,CAAC;IACP,CAAC;IAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzC,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport type * as ReactTypes from \"react\";\nimport { getApolloContext } from \"../context/index.js\";\nimport { RenderPromises } from \"./RenderPromises.js\";\nimport { renderToStaticMarkup } from \"react-dom/server\";\n\nexport function getDataFromTree(\n  tree: ReactTypes.ReactNode,\n  context: { [key: string]: any } = {}\n) {\n  return getMarkupFromTree({\n    tree,\n    context,\n    // If you need to configure this renderFunction, call getMarkupFromTree\n    // directly instead of getDataFromTree.\n    renderFunction: renderToStaticMarkup,\n  });\n}\n\nexport type GetMarkupFromTreeOptions = {\n  tree: ReactTypes.ReactNode;\n  context?: { [key: string]: any };\n  renderFunction?: (\n    tree: ReactTypes.ReactElement<any>\n  ) => string | PromiseLike<string>;\n};\n\nexport function getMarkupFromTree({\n  tree,\n  context = {},\n  // The rendering function is configurable! We use renderToStaticMarkup as\n  // the default, because it's a little less expensive than renderToString,\n  // and legacy usage of getDataFromTree ignores the return value anyway.\n  renderFunction = renderToStaticMarkup,\n}: GetMarkupFromTreeOptions): Promise<string> {\n  const renderPromises = new RenderPromises();\n\n  function process(): Promise<string> {\n    // Always re-render from the rootElement, even though it might seem\n    // better to render the children of the component responsible for the\n    // promise, because it is not possible to reconstruct the full context\n    // of the original rendering (including all unknown context provider\n    // elements) for a subtree of the original component tree.\n    const ApolloContext = getApolloContext();\n\n    return new Promise<string>((resolve) => {\n      const element = React.createElement(\n        ApolloContext.Provider,\n        { value: { ...context, renderPromises } },\n        tree\n      );\n      resolve(renderFunction(element));\n    })\n      .then((html) => {\n        return renderPromises.hasPromises() ?\n            renderPromises.consumeAndAwaitPromises().then(process)\n          : html;\n      })\n      .finally(() => {\n        renderPromises.stop();\n      });\n  }\n\n  return Promise.resolve().then(process);\n}\n"]}