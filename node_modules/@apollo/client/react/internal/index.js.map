{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/react/internal/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AAO/D,OAAO,EACL,sBAAsB,EACtB,iBAAiB,EACjB,cAAc,EACd,qBAAqB,EACrB,YAAY,EACZ,qBAAqB,GACtB,MAAM,2BAA2B,CAAC", "sourcesContent": ["export { getSuspenseCache } from \"./cache/getSuspenseCache.js\";\nexport type { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON> } from \"./cache/types.js\";\nexport type {\n  QueryReference,\n  QueryRef,\n  PreloadedQueryRef,\n} from \"./cache/QueryReference.js\";\nexport {\n  InternalQueryReference,\n  getWrappedPromise,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n  wrapQueryRef,\n  assertWrappedQueryRef,\n} from \"./cache/QueryReference.js\";\nexport type { SuspenseCacheOptions } from \"./cache/SuspenseCache.js\";\nexport type { HookWrappers } from \"../hooks/internal/wrapHook.js\";\n"]}