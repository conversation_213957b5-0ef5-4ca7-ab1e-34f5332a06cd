{"version": 3, "file": "getSuspenseCache.js", "sourceRoot": "", "sources": ["../../../../src/react/internal/cache/getSuspenseCache.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAWnD,IAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;AAE/D,MAAM,UAAU,gBAAgB,CAC9B,MAEC;;IAED,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE,CAAC;QACjC,MAAM,CAAC,mBAAmB,CAAC,GAAG,IAAI,aAAa,CAC7C,MAAA,MAAM,CAAC,cAAc,CAAC,KAAK,0CAAE,QAAQ,CACtC,CAAC;IACJ,CAAC;IAED,OAAO,MAAM,CAAC,mBAAmB,CAAC,CAAC;AACrC,CAAC", "sourcesContent": ["import type { SuspenseCacheOptions } from \"../index.js\";\nimport { SuspenseCache } from \"./SuspenseCache.js\";\nimport type { ApolloClient } from \"../../../core/ApolloClient.js\";\n\ndeclare module \"../../../core/ApolloClient.js\" {\n  interface DefaultOptions {\n    react?: {\n      suspense?: Readonly<SuspenseCacheOptions>;\n    };\n  }\n}\n\nconst suspenseCacheSymbol = Symbol.for(\"apollo.suspenseCache\");\n\nexport function getSuspenseCache(\n  client: ApolloClient<object> & {\n    [suspenseCacheSymbol]?: SuspenseCache;\n  }\n) {\n  if (!client[suspenseCacheSymbol]) {\n    client[suspenseCacheSymbol] = new SuspenseCache(\n      client.defaultOptions.react?.suspense\n    );\n  }\n\n  return client[suspenseCacheSymbol];\n}\n"]}