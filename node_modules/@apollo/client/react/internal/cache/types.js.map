{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../../src/react/internal/cache/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { DocumentNode } from \"graphql\";\n\nexport type CacheKey = [\n  query: DocumentNode,\n  stringifiedVariables: string,\n  ...queryKey: any[],\n];\n\nexport type FragmentCacheKey = [\n  cacheId: string,\n  fragment: DocumentNode,\n  stringifiedVariables: string,\n];\n\nexport interface QueryKey {\n  __queryKey?: string;\n}\n\nexport interface FragmentKey {\n  __fragmentKey?: string;\n}\n"]}