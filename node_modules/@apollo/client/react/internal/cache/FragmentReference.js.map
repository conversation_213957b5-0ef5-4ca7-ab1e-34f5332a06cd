{"version": 3, "file": "FragmentReference.js", "sourceRoot": "", "sources": ["../../../../src/react/internal/cache/FragmentReference.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAOtC,OAAO,EACL,sBAAsB,EACtB,oBAAoB,GACrB,MAAM,6BAA6B,CAAC;AAgBrC;IAiBE,2BACE,MAAyB,EACzB,oBAEC,EACD,OAAiC;QALnC,iBAuCC;QAnDe,QAAG,GAAgB,EAAE,CAAC;QAO9B,cAAS,GAAG,IAAI,GAAG,EAAgC,CAAC;QAGpD,eAAU,GAAG,CAAC,CAAC;QASrB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAE/C,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;QAE7D,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QACrC,CAAC;QAED,IAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,oBAAoB,CAAC,CAAC;QAExD,oEAAoE;QACpE,2EAA2E;QAC3E,uEAAuE;QACvE,8BAA8B;QAC9B,IAAM,iBAAiB,GAAG;;YACxB,IAAI,CAAC,KAAI,CAAC,UAAU,EAAE,CAAC;gBACrB,KAAI,CAAC,oBAAoB,GAAG,UAAU,CACpC,KAAI,CAAC,OAAO,EACZ,MAAA,OAAO,CAAC,oBAAoB,mCAAI,KAAM,CACvC,CAAC;YACJ,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC,OAAO;YACV,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACb,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC;gBACrC,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;IAC1D,CAAC;IAED,kCAAM,GAAN,UAAO,QAAsC;QAA7C,iBAMC;QALC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE7B,OAAO;YACL,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,CAAC;IACJ,CAAC;IAED,kCAAM,GAAN;QAAA,iBAmBC;QAlBC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,YAAY,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACxC,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,OAAO;YACL,IAAI,QAAQ,EAAE,CAAC;gBACb,OAAO;YACT,CAAC;YAED,QAAQ,GAAG,IAAI,CAAC;YAChB,KAAI,CAAC,UAAU,EAAE,CAAC;YAElB,UAAU,CAAC;gBACT,IAAI,CAAC,KAAI,CAAC,UAAU,EAAE,CAAC;oBACrB,KAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC;IAEO,mCAAO,GAAf;QACE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;QAChC,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEO,qCAAS,GAAjB;QACE,+BAA+B;IACjC,CAAC;IAEO,+CAAmB,GAA3B;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAC1B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAC5B,CAAC;IACJ,CAAC;IAEO,sCAAU,GAAlB,UAAmB,MAAkC;;QACnD,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAC5B,KAAK,SAAS,CAAC,CAAC,CAAC;gBACf,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;oBACpB,OAAO,MAAA,IAAI,CAAC,OAAO,qDAAG,MAAM,CAAC,IAAI,CAAC,CAAC;gBACrC,CAAC;gBAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC3B,MAAM;YACR,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,wEAAwE;gBACxE,wEAAwE;gBACxE,uEAAuE;gBACvE,oCAAoC;gBACpC,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3C,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,OAAO;oBACV,MAAM,CAAC,QAAQ,CAAC,CAAC;wBACf,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC;wBACrC,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAEhC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC;IACH,CAAC;IAEO,uCAAW,GAAnB,UAAoB,KAAc;;QAChC,MAAA,IAAI,CAAC,MAAM,qDAAG,KAAK,CAAC,CAAC;IACvB,CAAC;IAEO,mCAAO,GAAf,UAAgB,OAA+C;QAC7D,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,QAAQ,IAAK,OAAA,QAAQ,CAAC,OAAO,CAAC,EAAjB,CAAiB,CAAC,CAAC;IAC1D,CAAC;IAEO,gDAAoB,GAA5B;QAAA,iBAOC;QANC,OAAO,oBAAoB,CACzB,IAAI,OAAO,CAAqB,UAAC,OAAO,EAAE,MAAM;YAC9C,KAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAEO,mCAAO,GAAf,UACE,MAAyB,EACzB,OAAmE;QAE3D,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;QACjB,IAAA,IAAI,GAA6B,OAAO,KAApC,EAAE,QAAQ,GAAmB,OAAO,SAA1B,EAAE,YAAY,GAAK,OAAO,aAAZ,CAAa;QAEjD,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,uBAClB,OAAO,KACV,KAAK,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE,YAAY,CAAC,EACtD,iBAAiB,EAAE,IAAI,EACvB,EAAE,EAAE,IAAI,EACR,UAAU,EAAE,IAAI,IAChB,CAAC;QAEH,6BACK,IAAI,KACP,MAAM,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC;gBAC1C,QAAQ,UAAA;gBACR,YAAY,cAAA;gBACZ,IAAI,EAAE,IAAI,CAAC,MAAM;aAClB,CAAuB,IACxB;IACJ,CAAC;IACH,wBAAC;AAAD,CAAC,AA7KD,IA6KC", "sourcesContent": ["import { equal } from \"@wry/equality\";\nimport type {\n  WatchFragmentOptions,\n  WatchFragmentResult,\n} from \"../../../cache/index.js\";\nimport type { ApolloClient } from \"../../../core/ApolloClient.js\";\nimport type { MaybeMasked } from \"../../../masking/index.js\";\nimport {\n  createFulfilledPromise,\n  wrapPromiseWithState,\n} from \"../../../utilities/index.js\";\nimport type {\n  Observable,\n  ObservableSubscription,\n  PromiseWithState,\n} from \"../../../utilities/index.js\";\nimport type { FragmentKey } from \"./types.js\";\n\ntype FragmentRefPromise<TData> = PromiseWithState<TData>;\ntype Listener<TData> = (promise: FragmentRefPromise<TData>) => void;\n\ninterface FragmentReferenceOptions {\n  autoDisposeTimeoutMs?: number;\n  onDispose?: () => void;\n}\n\nexport class FragmentReference<\n  TData = unknown,\n  TVariables = Record<string, unknown>,\n> {\n  public readonly observable: Observable<WatchFragmentResult<TData>>;\n  public readonly key: FragmentKey = {};\n  public promise!: FragmentRefPromise<MaybeMasked<TData>>;\n\n  private resolve: ((result: MaybeMasked<TData>) => void) | undefined;\n  private reject: ((error: unknown) => void) | undefined;\n\n  private subscription!: ObservableSubscription;\n  private listeners = new Set<Listener<MaybeMasked<TData>>>();\n  private autoDisposeTimeoutId?: NodeJS.Timeout;\n\n  private references = 0;\n\n  constructor(\n    client: ApolloClient<any>,\n    watchFragmentOptions: WatchFragmentOptions<TData, TVariables> & {\n      from: string;\n    },\n    options: FragmentReferenceOptions\n  ) {\n    this.dispose = this.dispose.bind(this);\n    this.handleNext = this.handleNext.bind(this);\n    this.handleError = this.handleError.bind(this);\n\n    this.observable = client.watchFragment(watchFragmentOptions);\n\n    if (options.onDispose) {\n      this.onDispose = options.onDispose;\n    }\n\n    const diff = this.getDiff(client, watchFragmentOptions);\n\n    // Start a timer that will automatically dispose of the query if the\n    // suspended resource does not use this fragmentRef in the given time. This\n    // helps prevent memory leaks when a component has unmounted before the\n    // query has finished loading.\n    const startDisposeTimer = () => {\n      if (!this.references) {\n        this.autoDisposeTimeoutId = setTimeout(\n          this.dispose,\n          options.autoDisposeTimeoutMs ?? 30_000\n        );\n      }\n    };\n\n    this.promise =\n      diff.complete ?\n        createFulfilledPromise(diff.result)\n      : this.createPendingPromise();\n    this.subscribeToFragment();\n\n    this.promise.then(startDisposeTimer, startDisposeTimer);\n  }\n\n  listen(listener: Listener<MaybeMasked<TData>>) {\n    this.listeners.add(listener);\n\n    return () => {\n      this.listeners.delete(listener);\n    };\n  }\n\n  retain() {\n    this.references++;\n    clearTimeout(this.autoDisposeTimeoutId);\n    let disposed = false;\n\n    return () => {\n      if (disposed) {\n        return;\n      }\n\n      disposed = true;\n      this.references--;\n\n      setTimeout(() => {\n        if (!this.references) {\n          this.dispose();\n        }\n      });\n    };\n  }\n\n  private dispose() {\n    this.subscription.unsubscribe();\n    this.onDispose();\n  }\n\n  private onDispose() {\n    // noop. overridable by options\n  }\n\n  private subscribeToFragment() {\n    this.subscription = this.observable.subscribe(\n      this.handleNext.bind(this),\n      this.handleError.bind(this)\n    );\n  }\n\n  private handleNext(result: WatchFragmentResult<TData>) {\n    switch (this.promise.status) {\n      case \"pending\": {\n        if (result.complete) {\n          return this.resolve?.(result.data);\n        }\n\n        this.deliver(this.promise);\n        break;\n      }\n      case \"fulfilled\": {\n        // This can occur when we already have a result written to the cache and\n        // we subscribe for the first time. We create a fulfilled promise in the\n        // constructor with a value that is the same as the first emitted value\n        // so we want to skip delivering it.\n        if (equal(this.promise.value, result.data)) {\n          return;\n        }\n\n        this.promise =\n          result.complete ?\n            createFulfilledPromise(result.data)\n          : this.createPendingPromise();\n\n        this.deliver(this.promise);\n      }\n    }\n  }\n\n  private handleError(error: unknown) {\n    this.reject?.(error);\n  }\n\n  private deliver(promise: FragmentRefPromise<MaybeMasked<TData>>) {\n    this.listeners.forEach((listener) => listener(promise));\n  }\n\n  private createPendingPromise() {\n    return wrapPromiseWithState(\n      new Promise<MaybeMasked<TData>>((resolve, reject) => {\n        this.resolve = resolve;\n        this.reject = reject;\n      })\n    );\n  }\n\n  private getDiff<TData, TVariables>(\n    client: ApolloClient<any>,\n    options: WatchFragmentOptions<TData, TVariables> & { from: string }\n  ) {\n    const { cache } = client;\n    const { from, fragment, fragmentName } = options;\n\n    const diff = cache.diff({\n      ...options,\n      query: cache[\"getFragmentDoc\"](fragment, fragmentName),\n      returnPartialData: true,\n      id: from,\n      optimistic: true,\n    });\n\n    return {\n      ...diff,\n      result: client[\"queryManager\"].maskFragment({\n        fragment,\n        fragmentName,\n        data: diff.result,\n      }) as MaybeMasked<TData>,\n    };\n  }\n}\n"]}