{"version": 3, "file": "SuspenseCache.js", "sourceRoot": "", "sources": ["../../../../src/react/internal/cache/SuspenseCache.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAMjC,OAAO,EAAE,aAAa,EAAE,MAAM,6BAA6B,CAAC;AAC5D,OAAO,EAAE,sBAAsB,EAAE,MAAM,qBAAqB,CAAC;AAE7D,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAgB3D;IAUE,uBAAY,OAAmD;QAAnD,wBAAA,EAAA,UAAgC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QATvD,cAAS,GAAG,IAAI,IAAI,CAC1B,aAAa,CACd,CAAC;QACM,iBAAY,GAAG,IAAI,IAAI,CAC7B,aAAa,CACd,CAAC;QAKA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,mCAAW,GAAX,UACE,QAAkB,EAClB,gBAA8C;QAE9C,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAE9C,CAAC;QAEF,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACjB,GAAG,CAAC,OAAO,GAAG,IAAI,sBAAsB,CAAC,gBAAgB,EAAE,EAAE;gBAC3D,oBAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB;gBACvD,SAAS,EAAE;oBACT,OAAO,GAAG,CAAC,OAAO,CAAC;gBACrB,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,GAAG,CAAC,OAAO,CAAC;IACrB,CAAC;IAED,sCAAc,GAAd,UACE,QAA0B,EAC1B,MAAyB,EACzB,OAAmE;QAEnE,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,CAEjD,CAAC;QAEF,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACjB,GAAG,CAAC,OAAO,GAAG,IAAI,iBAAiB,CAAC,MAAM,EAAE,OAAO,EAAE;gBACnD,oBAAoB,EAAE,IAAI,CAAC,OAAO,CAAC,oBAAoB;gBACvD,SAAS,EAAE;oBACT,OAAO,GAAG,CAAC,OAAO,CAAC;gBACrB,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,GAAG,CAAC,OAAO,CAAC;IACrB,CAAC;IAED,2BAAG,GAAH,UAAI,QAAkB,EAAE,QAAyC;QAC/D,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QACjD,GAAG,CAAC,OAAO,GAAG,QAAQ,CAAC;IACzB,CAAC;IACH,oBAAC;AAAD,CAAC,AA3DD,IA2DC", "sourcesContent": ["import { Trie } from \"@wry/trie\";\nimport type {\n  ApolloClient,\n  ObservableQuery,\n  WatchFragmentOptions,\n} from \"../../../core/index.js\";\nimport { canUseWeakMap } from \"../../../utilities/index.js\";\nimport { InternalQueryReference } from \"./QueryReference.js\";\nimport type { Cache<PERSON>ey, FragmentCacheKey } from \"./types.js\";\nimport { FragmentReference } from \"./FragmentReference.js\";\n\nexport interface SuspenseCacheOptions {\n  /**\n   * Specifies the amount of time, in milliseconds, the suspense cache will wait\n   * for a suspended component to read from the suspense cache before it\n   * automatically disposes of the query. This prevents memory leaks when a\n   * component unmounts before a suspended resource finishes loading. Increase\n   * the timeout if your queries take longer than than the specified time to\n   * prevent your queries from suspending over and over.\n   *\n   * Defaults to 30 seconds.\n   */\n  autoDisposeTimeoutMs?: number;\n}\n\nexport class SuspenseCache {\n  private queryRefs = new Trie<{ current?: InternalQueryReference }>(\n    canUseWeakMap\n  );\n  private fragmentRefs = new Trie<{ current?: FragmentReference }>(\n    canUseWeakMap\n  );\n\n  private options: SuspenseCacheOptions;\n\n  constructor(options: SuspenseCacheOptions = Object.create(null)) {\n    this.options = options;\n  }\n\n  getQueryRef<TData = any>(\n    cacheKey: CacheKey,\n    createObservable: () => ObservableQuery<TData>\n  ) {\n    const ref = this.queryRefs.lookupArray(cacheKey) as {\n      current?: InternalQueryReference<TData>;\n    };\n\n    if (!ref.current) {\n      ref.current = new InternalQueryReference(createObservable(), {\n        autoDisposeTimeoutMs: this.options.autoDisposeTimeoutMs,\n        onDispose: () => {\n          delete ref.current;\n        },\n      });\n    }\n\n    return ref.current;\n  }\n\n  getFragmentRef<TData, TVariables>(\n    cacheKey: FragmentCacheKey,\n    client: ApolloClient<any>,\n    options: WatchFragmentOptions<TData, TVariables> & { from: string }\n  ) {\n    const ref = this.fragmentRefs.lookupArray(cacheKey) as {\n      current?: FragmentReference<TData, TVariables>;\n    };\n\n    if (!ref.current) {\n      ref.current = new FragmentReference(client, options, {\n        autoDisposeTimeoutMs: this.options.autoDisposeTimeoutMs,\n        onDispose: () => {\n          delete ref.current;\n        },\n      });\n    }\n\n    return ref.current;\n  }\n\n  add(cacheKey: CacheKey, queryRef: InternalQueryReference<unknown>) {\n    const ref = this.queryRefs.lookupArray(cacheKey);\n    ref.current = queryRef;\n  }\n}\n"]}