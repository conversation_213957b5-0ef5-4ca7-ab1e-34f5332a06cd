{"version": 3, "file": "hoc.cjs", "sources": ["hoc-utils.js", "query-hoc.js", "mutation-hoc.js", "subscription-hoc.js", "graphql.js", "withApollo.js"], "sourcesContent": ["import { __extends } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nexport var defaultMapPropsToOptions = function () { return ({}); };\nexport var defaultMapResultToProps = function (props) { return props; };\nexport var defaultMapPropsToSkip = function () { return false; };\nexport function getDisplayName(WrappedComponent) {\n    return WrappedComponent.displayName || WrappedComponent.name || \"Component\";\n}\nexport function calculateVariablesFromProps(operation, props) {\n    var variables = {};\n    for (var _i = 0, _a = operation.variables; _i < _a.length; _i++) {\n        var _b = _a[_i], variable = _b.variable, type = _b.type;\n        if (!variable.name || !variable.name.value)\n            continue;\n        var variableName = variable.name.value;\n        var variableProp = props[variableName];\n        if (typeof variableProp !== \"undefined\") {\n            variables[variableName] = variableProp;\n            continue;\n        }\n        // Allow optional props\n        if (type.kind !== \"NonNullType\") {\n            variables[variableName] = undefined;\n        }\n    }\n    return variables;\n}\n// base class for hocs to easily manage refs\nvar GraphQLBase = /** @class */ (function (_super) {\n    __extends(GraphQLBase, _super);\n    function GraphQLBase(props) {\n        var _this = _super.call(this, props) || this;\n        _this.withRef = false;\n        _this.setWrappedInstance = _this.setWrappedInstance.bind(_this);\n        return _this;\n    }\n    GraphQLBase.prototype.getWrappedInstance = function () {\n        invariant(this.withRef, 56);\n        return this.wrappedInstance;\n    };\n    GraphQLBase.prototype.setWrappedInstance = function (ref) {\n        this.wrappedInstance = ref;\n    };\n    return GraphQLBase;\n}(React.Component));\nexport { GraphQLBase };\n//# sourceMappingURL=hoc-utils.js.map", "import { __assign, __extends, __rest } from \"tslib\";\nimport * as React from \"rehackt\";\nimport hoistNonReactStatics from \"hoist-non-react-statics\";\nimport { parser } from \"../parser/index.js\";\nimport { Query } from \"../components/index.js\";\nimport { getDisplayName, GraphQLBase, calculateVariablesFromProps, defaultMapPropsToOptions, defaultMapPropsToSkip, } from \"./hoc-utils.js\";\n/**\n * @deprecated\n * Official support for React Apollo higher order components ended in March 2020.\n * This library is still included in the `@apollo/client` package, but it no longer receives feature updates or bug fixes.\n */\nexport function withQuery(document, operationOptions) {\n    if (operationOptions === void 0) { operationOptions = {}; }\n    // this is memoized so if coming from `graphql` there is nearly no extra cost\n    var operation = parser(document);\n    // extract options\n    var _a = operationOptions.options, options = _a === void 0 ? defaultMapPropsToOptions : _a, _b = operationOptions.skip, skip = _b === void 0 ? defaultMapPropsToSkip : _b, _c = operationOptions.alias, alias = _c === void 0 ? \"Apollo\" : _c;\n    var mapPropsToOptions = options;\n    if (typeof mapPropsToOptions !== \"function\") {\n        mapPropsToOptions = function () { return options; };\n    }\n    var mapPropsToSkip = skip;\n    if (typeof mapPropsToSkip !== \"function\") {\n        mapPropsToSkip = function () { return skip; };\n    }\n    // allow for advanced referential equality checks\n    var lastResultProps;\n    return function (WrappedComponent) {\n        var graphQLDisplayName = \"\".concat(alias, \"(\").concat(getDisplayName(WrappedComponent), \")\");\n        var GraphQL = /** @class */ (function (_super) {\n            __extends(GraphQL, _super);\n            function GraphQL() {\n                return _super !== null && _super.apply(this, arguments) || this;\n            }\n            GraphQL.prototype.render = function () {\n                var _this = this;\n                var props = this.props;\n                var shouldSkip = mapPropsToSkip(props);\n                var opts = shouldSkip ? Object.create(null) : __assign({}, mapPropsToOptions(props));\n                if (!shouldSkip && !opts.variables && operation.variables.length > 0) {\n                    opts.variables = calculateVariablesFromProps(operation, props);\n                }\n                return (React.createElement(Query, __assign({}, opts, { displayName: graphQLDisplayName, skip: shouldSkip, query: document }), function (_a) {\n                    var _b, _c;\n                    var _ = _a.client, data = _a.data, r = __rest(_a, [\"client\", \"data\"]);\n                    if (operationOptions.withRef) {\n                        _this.withRef = true;\n                        props = Object.assign({}, props, {\n                            ref: _this.setWrappedInstance,\n                        });\n                    }\n                    // if we have skipped, no reason to manage any reshaping\n                    if (shouldSkip) {\n                        return (React.createElement(WrappedComponent, __assign({}, props, {})));\n                    }\n                    // the HOC's historically hoisted the data from the execution result\n                    // up onto the result since it was passed as a nested prop\n                    // we massage the Query components shape here to replicate that\n                    var result = Object.assign(r, data || {});\n                    var name = operationOptions.name || \"data\";\n                    var childProps = (_b = {}, _b[name] = result, _b);\n                    if (operationOptions.props) {\n                        var newResult = (_c = {},\n                            _c[name] = result,\n                            _c.ownProps = props,\n                            _c);\n                        lastResultProps = operationOptions.props(newResult, lastResultProps);\n                        childProps = lastResultProps;\n                    }\n                    return (React.createElement(WrappedComponent, __assign({}, props, childProps)));\n                }));\n            };\n            GraphQL.displayName = graphQLDisplayName;\n            GraphQL.WrappedComponent = WrappedComponent;\n            return GraphQL;\n        }(GraphQLBase));\n        // Make sure we preserve any custom statics on the original component.\n        return hoistNonReactStatics(GraphQL, WrappedComponent, {});\n    };\n}\n//# sourceMappingURL=query-hoc.js.map", "import { __assign, __extends, __rest } from \"tslib\";\nimport * as React from \"rehackt\";\nimport hoistNonReactStatics from \"hoist-non-react-statics\";\nimport { parser } from \"../parser/index.js\";\nimport { Mutation } from \"../components/index.js\";\nimport { defaultMapPropsToOptions, getDisplayName, calculateVariablesFromProps, GraphQLBase, } from \"./hoc-utils.js\";\n/**\n * @deprecated\n * Official support for React Apollo higher order components ended in March 2020.\n * This library is still included in the `@apollo/client` package, but it no longer receives feature updates or bug fixes.\n */\nexport function withMutation(document, operationOptions) {\n    if (operationOptions === void 0) { operationOptions = {}; }\n    // this is memoized so if coming from `graphql` there is nearly no extra cost\n    var operation = parser(document);\n    // extract options\n    var _a = operationOptions.options, options = _a === void 0 ? defaultMapPropsToOptions : _a, _b = operationOptions.alias, alias = _b === void 0 ? \"Apollo\" : _b;\n    var mapPropsToOptions = options;\n    if (typeof mapPropsToOptions !== \"function\")\n        mapPropsToOptions = function () {\n            return options;\n        };\n    return function (WrappedComponent) {\n        var graphQLDisplayName = \"\".concat(alias, \"(\").concat(getDisplayName(WrappedComponent), \")\");\n        var GraphQL = /** @class */ (function (_super) {\n            __extends(GraphQL, _super);\n            function GraphQL() {\n                return _super !== null && _super.apply(this, arguments) || this;\n            }\n            GraphQL.prototype.render = function () {\n                var props = this.props;\n                var opts = mapPropsToOptions(props);\n                if (operationOptions.withRef) {\n                    this.withRef = true;\n                    props = Object.assign({}, props, {\n                        ref: this.setWrappedInstance,\n                    });\n                }\n                if (!opts.variables && operation.variables.length > 0) {\n                    opts.variables = calculateVariablesFromProps(operation, props);\n                }\n                return (\n                    // @ts-expect-error\n                    (React.createElement(\n                        Mutation,\n                        __assign({ ignoreResults: true }, opts, { mutation: document }),\n                        function (mutate, _a) {\n                            // the HOC's historically hoisted the data from the execution result\n                            // up onto the result since it was passed as a nested prop\n                            // we massage the Mutation component's shape here to replicate that\n                            // this matches the query HoC\n                            var _b, _c;\n                            var data = _a.data, r = __rest(_a, [\"data\"]);\n                            // @ts-ignore Type instantiation is excessively deep and possibly infinite.\n                            var result = Object.assign(r, data || {});\n                            var name = operationOptions.name || \"mutate\";\n                            var resultName = operationOptions.name ? \"\".concat(name, \"Result\") : \"result\";\n                            var childProps = (_b = {},\n                                _b[name] = mutate,\n                                _b[resultName] = result,\n                                _b);\n                            if (operationOptions.props) {\n                                var newResult = (_c = {},\n                                    _c[name] = mutate,\n                                    _c[resultName] = result,\n                                    _c.ownProps = props,\n                                    _c);\n                                childProps = operationOptions.props(newResult);\n                            }\n                            return React.createElement(WrappedComponent, __assign({}, props, childProps));\n                        }\n                    ))\n                );\n            };\n            GraphQL.displayName = graphQLDisplayName;\n            GraphQL.WrappedComponent = WrappedComponent;\n            return GraphQL;\n        }(GraphQLBase));\n        // Make sure we preserve any custom statics on the original component.\n        return hoistNonReactStatics(GraphQL, WrappedComponent, {});\n    };\n}\n//# sourceMappingURL=mutation-hoc.js.map", "import { __assign, __extends, __rest } from \"tslib\";\nimport * as React from \"rehackt\";\nimport hoistNonReactStatics from \"hoist-non-react-statics\";\nimport { parser } from \"../parser/index.js\";\nimport { Subscription } from \"../components/index.js\";\nimport { getDisplayName, GraphQLBase, calculateVariablesFromProps, defaultMapPropsToOptions, defaultMapPropsToSkip, } from \"./hoc-utils.js\";\n/**\n * @deprecated\n * Official support for React Apollo higher order components ended in March 2020.\n * This library is still included in the `@apollo/client` package, but it no longer receives feature updates or bug fixes.\n */\nexport function withSubscription(document, operationOptions) {\n    if (operationOptions === void 0) { operationOptions = {}; }\n    // this is memoized so if coming from `graphql` there is nearly no extra cost\n    var operation = parser(document);\n    // extract options\n    var _a = operationOptions.options, options = _a === void 0 ? defaultMapPropsToOptions : _a, _b = operationOptions.skip, skip = _b === void 0 ? defaultMapPropsToSkip : _b, _c = operationOptions.alias, alias = _c === void 0 ? \"Apollo\" : _c, shouldResubscribe = operationOptions.shouldResubscribe;\n    var mapPropsToOptions = options;\n    if (typeof mapPropsToOptions !== \"function\")\n        mapPropsToOptions = function () { return options; };\n    var mapPropsToSkip = skip;\n    if (typeof mapPropsToSkip !== \"function\")\n        mapPropsToSkip = function () { return skip; };\n    // allow for advanced referential equality checks\n    var lastResultProps;\n    return function (WrappedComponent) {\n        var graphQLDisplayName = \"\".concat(alias, \"(\").concat(getDisplayName(WrappedComponent), \")\");\n        var GraphQL = /** @class */ (function (_super) {\n            __extends(GraphQL, _super);\n            function GraphQL(props) {\n                var _this = _super.call(this, props) || this;\n                _this.state = { resubscribe: false };\n                return _this;\n            }\n            GraphQL.prototype.updateResubscribe = function (resubscribe) {\n                this.setState({ resubscribe: resubscribe });\n            };\n            GraphQL.prototype.componentDidUpdate = function (prevProps) {\n                var resubscribe = !!(shouldResubscribe && shouldResubscribe(prevProps, this.props));\n                if (this.state.resubscribe !== resubscribe) {\n                    this.updateResubscribe(resubscribe);\n                }\n            };\n            GraphQL.prototype.render = function () {\n                var _this = this;\n                var props = this.props;\n                var shouldSkip = mapPropsToSkip(props);\n                var opts = shouldSkip ? Object.create(null) : mapPropsToOptions(props);\n                if (!shouldSkip && !opts.variables && operation.variables.length > 0) {\n                    opts.variables = calculateVariablesFromProps(operation, props);\n                }\n                return (React.createElement(Subscription, __assign({}, opts, { displayName: graphQLDisplayName, skip: shouldSkip, subscription: document, shouldResubscribe: this.state.resubscribe }), function (_a) {\n                    var _b, _c;\n                    var data = _a.data, r = __rest(_a, [\"data\"]);\n                    if (operationOptions.withRef) {\n                        _this.withRef = true;\n                        props = Object.assign({}, props, {\n                            ref: _this.setWrappedInstance,\n                        });\n                    }\n                    // if we have skipped, no reason to manage any reshaping\n                    if (shouldSkip) {\n                        return (React.createElement(WrappedComponent, __assign({}, props, {})));\n                    }\n                    // the HOC's historically hoisted the data from the execution result\n                    // up onto the result since it was passed as a nested prop\n                    // we massage the Query components shape here to replicate that\n                    var result = Object.assign(r, data || {});\n                    var name = operationOptions.name || \"data\";\n                    var childProps = (_b = {}, _b[name] = result, _b);\n                    if (operationOptions.props) {\n                        var newResult = (_c = {},\n                            _c[name] = result,\n                            _c.ownProps = props,\n                            _c);\n                        lastResultProps = operationOptions.props(newResult, lastResultProps);\n                        childProps = lastResultProps;\n                    }\n                    return (React.createElement(WrappedComponent, __assign({}, props, childProps)));\n                }));\n            };\n            GraphQL.displayName = graphQLDisplayName;\n            GraphQL.WrappedComponent = WrappedComponent;\n            return GraphQL;\n        }(GraphQLBase));\n        // Make sure we preserve any custom statics on the original component.\n        return hoistNonReactStatics(GraphQL, WrappedComponent, {});\n    };\n}\n//# sourceMappingURL=subscription-hoc.js.map", "import { parser, DocumentType } from \"../parser/index.js\";\nimport { withQuery } from \"./query-hoc.js\";\nimport { withMutation } from \"./mutation-hoc.js\";\nimport { withSubscription } from \"./subscription-hoc.js\";\n/**\n * @deprecated\n * Official support for React Apollo higher order components ended in March 2020.\n * This library is still included in the `@apollo/client` package, but it no longer receives feature updates or bug fixes.\n */\nexport function graphql(document, operationOptions) {\n    if (operationOptions === void 0) { operationOptions = {}; }\n    switch (parser(document).type) {\n        case DocumentType.Mutation:\n            return withMutation(document, operationOptions);\n        case DocumentType.Subscription:\n            return withSubscription(document, operationOptions);\n        case DocumentType.Query:\n        default:\n            return withQuery(document, operationOptions);\n    }\n}\n//# sourceMappingURL=graphql.js.map", "import { __assign, __extends } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport hoistNonReactStatics from \"hoist-non-react-statics\";\nimport { ApolloConsumer } from \"../context/index.js\";\nfunction getDisplayName(WrappedComponent) {\n    return WrappedComponent.displayName || WrappedComponent.name || \"Component\";\n}\n/**\n * @deprecated\n * Official support for React Apollo higher order components ended in March 2020.\n * This library is still included in the `@apollo/client` package, but it no longer receives feature updates or bug fixes.\n */\nexport function withApollo(WrappedComponent, operationOptions) {\n    if (operationOptions === void 0) { operationOptions = {}; }\n    var withDisplayName = \"withApollo(\".concat(getDisplayName(WrappedComponent), \")\");\n    var WithApollo = /** @class */ (function (_super) {\n        __extends(WithApollo, _super);\n        function WithApollo(props) {\n            var _this = _super.call(this, props) || this;\n            _this.setWrappedInstance = _this.setWrappedInstance.bind(_this);\n            return _this;\n        }\n        WithApollo.prototype.getWrappedInstance = function () {\n            invariant(operationOptions.withRef, 57);\n            return this.wrappedInstance;\n        };\n        WithApollo.prototype.setWrappedInstance = function (ref) {\n            this.wrappedInstance = ref;\n        };\n        WithApollo.prototype.render = function () {\n            var _this = this;\n            return (React.createElement(ApolloConsumer, null, function (client) {\n                var props = Object.assign({}, _this.props, {\n                    client: client,\n                    ref: operationOptions.withRef ? _this.setWrappedInstance : undefined,\n                });\n                return React.createElement(WrappedComponent, __assign({}, props));\n            }));\n        };\n        WithApollo.displayName = withDisplayName;\n        WithApollo.WrappedComponent = WrappedComponent;\n        return WithApollo;\n    }(React.Component));\n    // Make sure we preserve any custom statics on the original component.\n    return hoistNonReactStatics(WithApollo, WrappedComponent, {});\n}\n//# sourceMappingURL=withApollo.js.map"], "names": ["getDisplayName", "__extends", "invariant", "React", "parser", "__assign", "Query", "__rest", "hoistNonReactStatics", "Mutation", "Subscription", "DocumentType", "ApolloConsumer"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,IAAI,wBAAwB,GAAG,YAAY,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;AAE5D,IAAI,qBAAqB,GAAG,YAAY,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC;AAC1D,SAASA,gBAAc,CAAC,gBAAgB,EAAE;AACjD,IAAI,OAAO,gBAAgB,CAAC,WAAW,IAAI,gBAAgB,CAAC,IAAI,IAAI,WAAW,CAAC;AAChF,CAAC;AACM,SAAS,2BAA2B,CAAC,SAAS,EAAE,KAAK,EAAE;AAC9D,IAAI,IAAI,SAAS,GAAG,EAAE,CAAC;AACvB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACrE,QAAQ,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAChE,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK;AAClD,YAAY,SAAS;AACrB,QAAQ,IAAI,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;AAC/C,QAAQ,IAAI,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AAC/C,QAAQ,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AACjD,YAAY,SAAS,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC;AACnD,YAAY,SAAS;AACrB,SAAS;AAET,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE;AACzC,YAAY,SAAS,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC;AAChD,SAAS;AACT,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB,CAAC;AAED,IAAI,WAAW,KAAkB,UAAU,MAAM,EAAE;AACnD,IAAIC,eAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AACnC,IAAI,SAAS,WAAW,CAAC,KAAK,EAAE;AAChC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;AACrD,QAAQ,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;AAC9B,QAAQ,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxE,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,WAAW,CAAC,SAAS,CAAC,kBAAkB,GAAG,YAAY;AAC3D,QAAQC,iBAAS,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AACpC,QAAQ,OAAO,IAAI,CAAC,eAAe,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,GAAG,EAAE;AAC9D,QAAQ,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;AACnC,KAAK,CAAC;AACN,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC,CAACC,gBAAK,CAAC,SAAS,CAAC,CAAC;;AClCZ,SAAS,SAAS,CAAC,QAAQ,EAAE,gBAAgB,EAAE;AACtD,IAAI,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE,EAAE,gBAAgB,GAAG,EAAE,CAAC,EAAE;AAE/D,IAAI,IAAI,SAAS,GAAGC,aAAM,CAAC,QAAQ,CAAC,CAAC;AAErC,IAAI,IAAI,EAAE,GAAG,gBAAgB,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,wBAAwB,GAAG,EAAE,EAAE,EAAE,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,qBAAqB,GAAG,EAAE,EAAE,EAAE,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,CAAC;AAClP,IAAI,IAAI,iBAAiB,GAAG,OAAO,CAAC;AACpC,IAAI,IAAI,OAAO,iBAAiB,KAAK,UAAU,EAAE;AACjD,QAAQ,iBAAiB,GAAG,YAAY,EAAE,OAAO,OAAO,CAAC,EAAE,CAAC;AAC5D,KAAK;AACL,IAAI,IAAI,cAAc,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;AAC9C,QAAQ,cAAc,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;AACtD,KAAK;AAEL,IAAI,IAAI,eAAe,CAAC;AACxB,IAAI,OAAO,UAAU,gBAAgB,EAAE;AACvC,QAAQ,IAAI,kBAAkB,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM,CAACJ,gBAAc,CAAC,gBAAgB,CAAC,EAAE,GAAG,CAAC,CAAC;AACrG,QAAQ,IAAI,OAAO,KAAkB,UAAU,MAAM,EAAE;AACvD,YAAYC,eAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACvC,YAAY,SAAS,OAAO,GAAG;AAC/B,gBAAgB,OAAO,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC;AAChF,aAAa;AACb,YAAY,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AACnD,gBAAgB,IAAI,KAAK,GAAG,IAAI,CAAC;AACjC,gBAAgB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACvC,gBAAgB,IAAI,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AACvD,gBAAgB,IAAI,IAAI,GAAG,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAGI,cAAQ,CAAC,EAAE,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;AACrG,gBAAgB,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACtF,oBAAoB,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACnF,iBAAiB;AACjB,gBAAgB,QAAQF,gBAAK,CAAC,aAAa,CAACG,gBAAK,EAAED,cAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,WAAW,EAAE,kBAAkB,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE;AAC7J,oBAAoB,IAAI,EAAE,EAAE,EAAE,CAAC;AAC/B,oBAA4B,EAAE,CAAC,MAAM,CAAC,KAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,GAAGE,YAAM,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE;AAC1F,oBAAoB,IAAI,gBAAgB,CAAC,OAAO,EAAE;AAClD,wBAAwB,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;AAC7C,wBAAwB,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE;AACzD,4BAA4B,GAAG,EAAE,KAAK,CAAC,kBAAkB;AACzD,yBAAyB,CAAC,CAAC;AAC3B,qBAAqB;AAErB,oBAAoB,IAAI,UAAU,EAAE;AACpC,wBAAwB,QAAQJ,gBAAK,CAAC,aAAa,CAAC,gBAAgB,EAAEE,cAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;AAChG,qBAAqB;AAIrB,oBAAoB,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;AAC9D,oBAAoB,IAAI,IAAI,GAAG,gBAAgB,CAAC,IAAI,IAAI,MAAM,CAAC;AAC/D,oBAAoB,IAAI,UAAU,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,CAAC;AACtE,oBAAoB,IAAI,gBAAgB,CAAC,KAAK,EAAE;AAChD,wBAAwB,IAAI,SAAS,IAAI,EAAE,GAAG,EAAE;AAChD,4BAA4B,EAAE,CAAC,IAAI,CAAC,GAAG,MAAM;AAC7C,4BAA4B,EAAE,CAAC,QAAQ,GAAG,KAAK;AAC/C,4BAA4B,EAAE,CAAC,CAAC;AAChC,wBAAwB,eAAe,GAAG,gBAAgB,CAAC,KAAK,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;AAC7F,wBAAwB,UAAU,GAAG,eAAe,CAAC;AACrD,qBAAqB;AACrB,oBAAoB,QAAQF,gBAAK,CAAC,aAAa,CAAC,gBAAgB,EAAEE,cAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE;AACpG,iBAAiB,CAAC,EAAE;AACpB,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,WAAW,GAAG,kBAAkB,CAAC;AACrD,YAAY,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACxD,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;AAExB,QAAQ,OAAOG,6BAAoB,CAAC,OAAO,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;AACnE,KAAK,CAAC;AACN;;ACpEO,SAAS,YAAY,CAAC,QAAQ,EAAE,gBAAgB,EAAE;AACzD,IAAI,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE,EAAE,gBAAgB,GAAG,EAAE,CAAC,EAAE;AAE/D,IAAI,IAAI,SAAS,GAAGJ,aAAM,CAAC,QAAQ,CAAC,CAAC;AAErC,IAAI,IAAI,EAAE,GAAG,gBAAgB,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,wBAAwB,GAAG,EAAE,EAAE,EAAE,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,CAAC;AACnK,IAAI,IAAI,iBAAiB,GAAG,OAAO,CAAC;AACpC,IAAI,IAAI,OAAO,iBAAiB,KAAK,UAAU;AAC/C,QAAQ,iBAAiB,GAAG,YAAY;AACxC,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS,CAAC;AACV,IAAI,OAAO,UAAU,gBAAgB,EAAE;AACvC,QAAQ,IAAI,kBAAkB,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM,CAACJ,gBAAc,CAAC,gBAAgB,CAAC,EAAE,GAAG,CAAC,CAAC;AACrG,QAAQ,IAAI,OAAO,KAAkB,UAAU,MAAM,EAAE;AACvD,YAAYC,eAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACvC,YAAY,SAAS,OAAO,GAAG;AAC/B,gBAAgB,OAAO,MAAM,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI,CAAC;AAChF,aAAa;AACb,YAAY,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AACnD,gBAAgB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACvC,gBAAgB,IAAI,IAAI,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACpD,gBAAgB,IAAI,gBAAgB,CAAC,OAAO,EAAE;AAC9C,oBAAoB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxC,oBAAoB,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE;AACrD,wBAAwB,GAAG,EAAE,IAAI,CAAC,kBAAkB;AACpD,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACvE,oBAAoB,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACnF,iBAAiB;AACjB,gBAAgB;AAEhB,qBAAqBE,gBAAK,CAAC,aAAa;AACxC,wBAAwBM,mBAAQ;AAChC,wBAAwBJ,cAAQ,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC;AACvF,wBAAwB,UAAU,MAAM,EAAE,EAAE,EAAE;AAK9C,4BAA4B,IAAI,EAAE,EAAE,EAAE,CAAC;AACvC,4BAA4B,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAGE,YAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAEzE,4BAA4B,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;AACtE,4BAA4B,IAAI,IAAI,GAAG,gBAAgB,CAAC,IAAI,IAAI,QAAQ,CAAC;AACzE,4BAA4B,IAAI,UAAU,GAAG,gBAAgB,CAAC,IAAI,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAC;AAC1G,4BAA4B,IAAI,UAAU,IAAI,EAAE,GAAG,EAAE;AACrD,gCAAgC,EAAE,CAAC,IAAI,CAAC,GAAG,MAAM;AACjD,gCAAgC,EAAE,CAAC,UAAU,CAAC,GAAG,MAAM;AACvD,gCAAgC,EAAE,CAAC,CAAC;AACpC,4BAA4B,IAAI,gBAAgB,CAAC,KAAK,EAAE;AACxD,gCAAgC,IAAI,SAAS,IAAI,EAAE,GAAG,EAAE;AACxD,oCAAoC,EAAE,CAAC,IAAI,CAAC,GAAG,MAAM;AACrD,oCAAoC,EAAE,CAAC,UAAU,CAAC,GAAG,MAAM;AAC3D,oCAAoC,EAAE,CAAC,QAAQ,GAAG,KAAK;AACvD,oCAAoC,EAAE,CAAC,CAAC;AACxC,gCAAgC,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC/E,6BAA6B;AAC7B,4BAA4B,OAAOJ,gBAAK,CAAC,aAAa,CAAC,gBAAgB,EAAEE,cAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC;AAC1G,yBAAyB;AACzB,qBAAqB;AACrB,kBAAkB;AAClB,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,WAAW,GAAG,kBAAkB,CAAC;AACrD,YAAY,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACxD,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;AAExB,QAAQ,OAAOG,6BAAoB,CAAC,OAAO,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;AACnE,KAAK,CAAC;AACN;;ACtEO,SAAS,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,EAAE;AAC7D,IAAI,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE,EAAE,gBAAgB,GAAG,EAAE,CAAC,EAAE;AAE/D,IAAI,IAAI,SAAS,GAAGJ,aAAM,CAAC,QAAQ,CAAC,CAAC;AAErC,IAAI,IAAI,EAAE,GAAG,gBAAgB,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,wBAAwB,GAAG,EAAE,EAAE,EAAE,GAAG,gBAAgB,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,qBAAqB,GAAG,EAAE,EAAE,EAAE,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAE,iBAAiB,GAAG,gBAAgB,CAAC,iBAAiB,CAAC;AAC1S,IAAI,IAAI,iBAAiB,GAAG,OAAO,CAAC;AACpC,IAAI,IAAI,OAAO,iBAAiB,KAAK,UAAU;AAC/C,QAAQ,iBAAiB,GAAG,YAAY,EAAE,OAAO,OAAO,CAAC,EAAE,CAAC;AAC5D,IAAI,IAAI,cAAc,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,OAAO,cAAc,KAAK,UAAU;AAC5C,QAAQ,cAAc,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;AAEtD,IAAI,IAAI,eAAe,CAAC;AACxB,IAAI,OAAO,UAAU,gBAAgB,EAAE;AACvC,QAAQ,IAAI,kBAAkB,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM,CAACJ,gBAAc,CAAC,gBAAgB,CAAC,EAAE,GAAG,CAAC,CAAC;AACrG,QAAQ,IAAI,OAAO,KAAkB,UAAU,MAAM,EAAE;AACvD,YAAYC,eAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACvC,YAAY,SAAS,OAAO,CAAC,KAAK,EAAE;AACpC,gBAAgB,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;AAC7D,gBAAgB,KAAK,CAAC,KAAK,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;AACrD,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa;AACb,YAAY,OAAO,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,WAAW,EAAE;AACzE,gBAAgB,IAAI,CAAC,QAAQ,CAAC,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,CAAC;AAC5D,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,SAAS,EAAE;AACxE,gBAAgB,IAAI,WAAW,GAAG,CAAC,EAAE,iBAAiB,IAAI,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACpG,gBAAgB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,WAAW,EAAE;AAC5D,oBAAoB,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;AACxD,iBAAiB;AACjB,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AACnD,gBAAgB,IAAI,KAAK,GAAG,IAAI,CAAC;AACjC,gBAAgB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AACvC,gBAAgB,IAAI,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;AACvD,gBAAgB,IAAI,IAAI,GAAG,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACvF,gBAAgB,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACtF,oBAAoB,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACnF,iBAAiB;AACjB,gBAAgB,QAAQE,gBAAK,CAAC,aAAa,CAACO,uBAAY,EAAEL,cAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,WAAW,EAAE,kBAAkB,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,EAAE,UAAU,EAAE,EAAE;AACtN,oBAAoB,IAAI,EAAE,EAAE,EAAE,CAAC;AAC/B,oBAAoB,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC,GAAGE,YAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACjE,oBAAoB,IAAI,gBAAgB,CAAC,OAAO,EAAE;AAClD,wBAAwB,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;AAC7C,wBAAwB,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE;AACzD,4BAA4B,GAAG,EAAE,KAAK,CAAC,kBAAkB;AACzD,yBAAyB,CAAC,CAAC;AAC3B,qBAAqB;AAErB,oBAAoB,IAAI,UAAU,EAAE;AACpC,wBAAwB,QAAQJ,gBAAK,CAAC,aAAa,CAAC,gBAAgB,EAAEE,cAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE;AAChG,qBAAqB;AAIrB,oBAAoB,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;AAC9D,oBAAoB,IAAI,IAAI,GAAG,gBAAgB,CAAC,IAAI,IAAI,MAAM,CAAC;AAC/D,oBAAoB,IAAI,UAAU,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,EAAE,CAAC,CAAC;AACtE,oBAAoB,IAAI,gBAAgB,CAAC,KAAK,EAAE;AAChD,wBAAwB,IAAI,SAAS,IAAI,EAAE,GAAG,EAAE;AAChD,4BAA4B,EAAE,CAAC,IAAI,CAAC,GAAG,MAAM;AAC7C,4BAA4B,EAAE,CAAC,QAAQ,GAAG,KAAK;AAC/C,4BAA4B,EAAE,CAAC,CAAC;AAChC,wBAAwB,eAAe,GAAG,gBAAgB,CAAC,KAAK,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;AAC7F,wBAAwB,UAAU,GAAG,eAAe,CAAC;AACrD,qBAAqB;AACrB,oBAAoB,QAAQF,gBAAK,CAAC,aAAa,CAAC,gBAAgB,EAAEE,cAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,EAAE;AACpG,iBAAiB,CAAC,EAAE;AACpB,aAAa,CAAC;AACd,YAAY,OAAO,CAAC,WAAW,GAAG,kBAAkB,CAAC;AACrD,YAAY,OAAO,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACxD,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;AAExB,QAAQ,OAAOG,6BAAoB,CAAC,OAAO,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;AACnE,KAAK,CAAC;AACN;;AC/EO,SAAS,OAAO,CAAC,QAAQ,EAAE,gBAAgB,EAAE;AACpD,IAAI,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE,EAAE,gBAAgB,GAAG,EAAE,CAAC,EAAE;AAC/D,IAAI,QAAQJ,aAAM,CAAC,QAAQ,CAAC,CAAC,IAAI;AACjC,QAAQ,KAAKO,mBAAY,CAAC,QAAQ;AAClC,YAAY,OAAO,YAAY,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;AAC5D,QAAQ,KAAKA,mBAAY,CAAC,YAAY;AACtC,YAAY,OAAO,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;AAChE,QAAQ,KAAKA,mBAAY,CAAC,KAAK,CAAC;AAChC,QAAQ;AACR,YAAY,OAAO,SAAS,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;AACzD,KAAK;AACL;;ACfA,SAAS,cAAc,CAAC,gBAAgB,EAAE;AAC1C,IAAI,OAAO,gBAAgB,CAAC,WAAW,IAAI,gBAAgB,CAAC,IAAI,IAAI,WAAW,CAAC;AAChF,CAAC;AAMM,SAAS,UAAU,CAAC,gBAAgB,EAAE,gBAAgB,EAAE;AAC/D,IAAI,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE,EAAE,gBAAgB,GAAG,EAAE,CAAC,EAAE;AAC/D,IAAI,IAAI,eAAe,GAAG,aAAa,CAAC,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE,GAAG,CAAC,CAAC;AACtF,IAAI,IAAI,UAAU,KAAkB,UAAU,MAAM,EAAE;AACtD,QAAQV,eAAS,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACtC,QAAQ,SAAS,UAAU,CAAC,KAAK,EAAE;AACnC,YAAY,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;AACzD,YAAY,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5E,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,UAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,YAAY;AAC9D,YAAYC,iBAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AACpD,YAAY,OAAO,IAAI,CAAC,eAAe,CAAC;AACxC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,GAAG,EAAE;AACjE,YAAY,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;AACvC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AAClD,YAAY,IAAI,KAAK,GAAG,IAAI,CAAC;AAC7B,YAAY,QAAQC,gBAAK,CAAC,aAAa,CAACS,sBAAc,EAAE,IAAI,EAAE,UAAU,MAAM,EAAE;AAChF,gBAAgB,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK,EAAE;AAC3D,oBAAoB,MAAM,EAAE,MAAM;AAClC,oBAAoB,GAAG,EAAE,gBAAgB,CAAC,OAAO,GAAG,KAAK,CAAC,kBAAkB,GAAG,SAAS;AACxF,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,OAAOT,gBAAK,CAAC,aAAa,CAAC,gBAAgB,EAAEE,cAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;AAClF,aAAa,CAAC,EAAE;AAChB,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,WAAW,GAAG,eAAe,CAAC;AACjD,QAAQ,UAAU,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACvD,QAAQ,OAAO,UAAU,CAAC;AAC1B,KAAK,CAACF,gBAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AAExB,IAAI,OAAOK,6BAAoB,CAAC,UAAU,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;AAClE;;;;;;;;"}