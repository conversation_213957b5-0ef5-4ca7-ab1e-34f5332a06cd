{"version": 3, "file": "graphql.js", "sourceRoot": "", "sources": ["../../../src/react/hoc/graphql.tsx"], "names": [], "mappings": "AAGA,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAC1D,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAIzD;;;;GAIG;AACH,MAAM,UAAU,OAAO,CAOrB,QAAsB,EACtB,gBAKM;IALN,iCAAA,EAAA,qBAKM;IAIN,QAAQ,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;QAC9B,KAAK,YAAY,CAAC,QAAQ;YACxB,OAAO,YAAY,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QAClD,KAAK,YAAY,CAAC,YAAY;YAC5B,OAAO,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;QACtD,KAAK,YAAY,CAAC,KAAK,CAAC;QACxB;YACE,OAAO,SAAS,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;IACjD,CAAC;AACH,CAAC", "sourcesContent": ["import type { DocumentNode } from \"graphql\";\nimport type * as ReactTypes from \"react\";\n\nimport { parser, DocumentType } from \"../parser/index.js\";\nimport { withQuery } from \"./query-hoc.js\";\nimport { withMutation } from \"./mutation-hoc.js\";\nimport { withSubscription } from \"./subscription-hoc.js\";\nimport type { OperationOption, DataProps, MutateProps } from \"./types.js\";\nimport type { OperationVariables } from \"../../core/index.js\";\n\n/**\n * @deprecated\n * Official support for React Apollo higher order components ended in March 2020.\n * This library is still included in the `@apollo/client` package, but it no longer receives feature updates or bug fixes.\n */\nexport function graphql<\n  TProps extends TGraphQLVariables | {} = {},\n  TData extends object = {},\n  TGraphQLVariables extends OperationVariables = {},\n  TChildProps extends object = Partial<DataProps<TData, TGraphQLVariables>> &\n    Partial<MutateProps<TData, TGraphQLVariables>>,\n>(\n  document: DocumentNode,\n  operationOptions: OperationOption<\n    TProps,\n    TData,\n    TGraphQLVariables,\n    TChildProps\n  > = {}\n): (\n  WrappedComponent: ReactTypes.ComponentType<TProps & TChildProps>\n) => ReactTypes.ComponentClass<TProps> {\n  switch (parser(document).type) {\n    case DocumentType.Mutation:\n      return withMutation(document, operationOptions);\n    case DocumentType.Subscription:\n      return withSubscription(document, operationOptions);\n    case DocumentType.Query:\n    default:\n      return withQuery(document, operationOptions);\n  }\n}\n"]}