{"version": 3, "file": "subscription-hoc.js", "sourceRoot": "", "sources": ["../../../src/react/hoc/subscription-hoc.tsx"], "names": [], "mappings": ";AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAGjC,OAAO,oBAAoB,MAAM,yBAAyB,CAAC;AAE3D,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AACtD,OAAO,EACL,cAAc,EACd,WAAW,EACX,2BAA2B,EAC3B,wBAAwB,EACxB,qBAAqB,GACtB,MAAM,gBAAgB,CAAC;AAGxB;;;;GAIG;AACH,MAAM,UAAU,gBAAgB,CAM9B,QAAsB,EACtB,gBAKM;IALN,iCAAA,EAAA,qBAKM;IAEN,6EAA6E;IAC7E,IAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IACnC,kBAAkB;IAEhB,IAAA,KAIE,gBAAgB,QAJgB,EAAlC,OAAO,mBAAG,wBAAwB,KAAA,EAClC,KAGE,gBAAgB,KAHU,EAA5B,IAAI,mBAAG,qBAAqB,KAAA,EAC5B,KAEE,gBAAgB,MAFF,EAAhB,KAAK,mBAAG,QAAQ,KAAA,EAChB,iBAAiB,GACf,gBAAgB,kBADD,CACE;IAErB,IAAI,iBAAiB,GAAG,OAA2C,CAAC;IACpE,IAAI,OAAO,iBAAiB,KAAK,UAAU;QACzC,iBAAiB,GAAG,cAAM,OAAA,OAA2B,EAA3B,CAA2B,CAAC;IAExD,IAAI,cAAc,GAAG,IAA+B,CAAC;IACrD,IAAI,OAAO,cAAc,KAAK,UAAU;QAAE,cAAc,GAAG,cAAM,OAAA,IAAW,EAAX,CAAW,CAAC;IAE7E,iDAAiD;IACjD,IAAI,eAAmC,CAAC;IACxC,OAAO,UACL,gBAAgE;QAEhE,IAAM,kBAAkB,GAAG,UAAG,KAAK,cAAI,cAAc,CAAC,gBAAgB,CAAC,MAAG,CAAC;QAC3E;YAAsB,2BAIrB;YAGC,iBAAY,KAAa;gBACvB,YAAA,MAAK,YAAC,KAAK,CAAC,SAAC;gBACb,KAAI,CAAC,KAAK,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;;YACtC,CAAC;YAED,mCAAiB,GAAjB,UAAkB,WAAoB;gBACpC,IAAI,CAAC,QAAQ,CAAC,EAAE,WAAW,aAAA,EAAE,CAAC,CAAC;YACjC,CAAC;YAED,oCAAkB,GAAlB,UAAmB,SAAiB;gBAClC,IAAM,WAAW,GAAG,CAAC,CAAC,CACpB,iBAAiB,IAAI,iBAAiB,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAC9D,CAAC;gBACF,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,KAAK,WAAW,EAAE,CAAC;oBAC3C,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;YAED,wBAAM,GAAN;gBAAA,iBA8DC;gBA7DC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;gBACvB,IAAM,UAAU,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;gBACzC,IAAM,IAAI,GACR,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAE9D,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACrE,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;gBACjE,CAAC;gBACD,OAAO,CACL,oBAAC,YAAY,eACP,IAAI,IACR,WAAW,EAAE,kBAAkB,EAC/B,IAAI,EAAE,UAAU,EAChB,YAAY,EAAE,QAAQ,EACtB,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,KAExC,UAAC,EAAmB;;oBAAjB,IAAA,IAAI,UAAA,EAAK,CAAC,cAAZ,QAAc,CAAF;oBACZ,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;wBAC7B,KAAI,CAAC,OAAO,GAAG,IAAI,CAAC;wBACpB,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE;4BAC/B,GAAG,EAAE,KAAI,CAAC,kBAAkB;yBAC7B,CAAC,CAAC;oBACL,CAAC;oBACD,wDAAwD;oBACxD,IAAI,UAAU,EAAE,CAAC;wBACf,OAAO,CACL,oBAAC,gBAAgB,eACV,KAAgB,EAChB,EAAkB,EACvB,CACH,CAAC;oBACJ,CAAC;oBAED,oEAAoE;oBACpE,0DAA0D;oBAC1D,+DAA+D;oBAC/D,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;oBAC5C,IAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,IAAI,MAAM,CAAC;oBAC7C,IAAI,UAAU,aAAK,GAAC,IAAI,IAAG,MAAM,KAAE,CAAC;oBACpC,IAAI,gBAAgB,CAAC,KAAK,EAAE,CAAC;wBAC3B,IAAM,SAAS;4BAEX,GAAC,IAAI,IAAG,MAAM;4BACd,WAAQ,GAAE,KAAe;+BAC1B,CAAC;wBACJ,eAAe,GAAG,gBAAgB,CAAC,KAAK,CACtC,SAAS,EACT,eAAe,CAChB,CAAC;wBACF,UAAU,GAAG,eAAe,CAAC;oBAC/B,CAAC;oBAED,OAAO,CACL,oBAAC,gBAAgB,eACV,KAAgB,EAChB,UAA0B,EAC/B,CACH,CAAC;gBACJ,CAAC,CACY,CAChB,CAAC;YACJ,CAAC;YAlFM,mBAAW,GAAG,kBAAkB,CAAC;YACjC,wBAAgB,GAAG,gBAAgB,CAAC;YAkF7C,cAAC;SAAA,AAxFD,CAAsB,WAAW,GAwFhC;QAED,sEAAsE;QACtE,OAAO,oBAAoB,CAAC,OAAO,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;IAC7D,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport type * as ReactTypes from \"react\";\nimport type { DocumentNode } from \"graphql\";\nimport hoistNonReactStatics from \"hoist-non-react-statics\";\n\nimport { parser } from \"../parser/index.js\";\nimport type { BaseQueryOptions } from \"../types/types.js\";\nimport { Subscription } from \"../components/index.js\";\nimport {\n  getDisplayName,\n  GraphQLBase,\n  calculateVariablesFromProps,\n  defaultMapPropsToOptions,\n  defaultMapPropsToSkip,\n} from \"./hoc-utils.js\";\nimport type { OperationOption, OptionProps, DataProps } from \"./types.js\";\n\n/**\n * @deprecated\n * Official support for React Apollo higher order components ended in March 2020.\n * This library is still included in the `@apollo/client` package, but it no longer receives feature updates or bug fixes.\n */\nexport function withSubscription<\n  TProps extends TGraphQLVariables | {} = {},\n  TData extends object = {},\n  TGraphQLVariables extends object = {},\n  TChildProps extends object = DataProps<TData, TGraphQLVariables>,\n>(\n  document: DocumentNode,\n  operationOptions: OperationOption<\n    TProps,\n    TData,\n    TGraphQLVariables,\n    TChildProps\n  > = {}\n) {\n  // this is memoized so if coming from `graphql` there is nearly no extra cost\n  const operation = parser(document);\n  // extract options\n  const {\n    options = defaultMapPropsToOptions,\n    skip = defaultMapPropsToSkip,\n    alias = \"Apollo\",\n    shouldResubscribe,\n  } = operationOptions;\n\n  let mapPropsToOptions = options as (props: any) => BaseQueryOptions;\n  if (typeof mapPropsToOptions !== \"function\")\n    mapPropsToOptions = () => options as BaseQueryOptions;\n\n  let mapPropsToSkip = skip as (props: any) => boolean;\n  if (typeof mapPropsToSkip !== \"function\") mapPropsToSkip = () => skip as any;\n\n  // allow for advanced referential equality checks\n  let lastResultProps: TChildProps | void;\n  return (\n    WrappedComponent: ReactTypes.ComponentType<TProps & TChildProps>\n  ): ReactTypes.ComponentClass<TProps> => {\n    const graphQLDisplayName = `${alias}(${getDisplayName(WrappedComponent)})`;\n    class GraphQL extends GraphQLBase<\n      TProps,\n      TChildProps,\n      { resubscribe: boolean }\n    > {\n      static displayName = graphQLDisplayName;\n      static WrappedComponent = WrappedComponent;\n      constructor(props: TProps) {\n        super(props);\n        this.state = { resubscribe: false };\n      }\n\n      updateResubscribe(resubscribe: boolean) {\n        this.setState({ resubscribe });\n      }\n\n      componentDidUpdate(prevProps: TProps) {\n        const resubscribe = !!(\n          shouldResubscribe && shouldResubscribe(prevProps, this.props)\n        );\n        if (this.state.resubscribe !== resubscribe) {\n          this.updateResubscribe(resubscribe);\n        }\n      }\n\n      render() {\n        let props = this.props;\n        const shouldSkip = mapPropsToSkip(props);\n        const opts =\n          shouldSkip ? Object.create(null) : mapPropsToOptions(props);\n\n        if (!shouldSkip && !opts.variables && operation.variables.length > 0) {\n          opts.variables = calculateVariablesFromProps(operation, props);\n        }\n        return (\n          <Subscription\n            {...opts}\n            displayName={graphQLDisplayName}\n            skip={shouldSkip}\n            subscription={document}\n            shouldResubscribe={this.state.resubscribe}\n          >\n            {({ data, ...r }: any) => {\n              if (operationOptions.withRef) {\n                this.withRef = true;\n                props = Object.assign({}, props, {\n                  ref: this.setWrappedInstance,\n                });\n              }\n              // if we have skipped, no reason to manage any reshaping\n              if (shouldSkip) {\n                return (\n                  <WrappedComponent\n                    {...(props as TProps)}\n                    {...({} as TChildProps)}\n                  />\n                );\n              }\n\n              // the HOC's historically hoisted the data from the execution result\n              // up onto the result since it was passed as a nested prop\n              // we massage the Query components shape here to replicate that\n              const result = Object.assign(r, data || {});\n              const name = operationOptions.name || \"data\";\n              let childProps = { [name]: result };\n              if (operationOptions.props) {\n                const newResult: OptionProps<TProps, TData, TGraphQLVariables> =\n                  {\n                    [name]: result,\n                    ownProps: props as TProps,\n                  };\n                lastResultProps = operationOptions.props(\n                  newResult,\n                  lastResultProps\n                );\n                childProps = lastResultProps;\n              }\n\n              return (\n                <WrappedComponent\n                  {...(props as TProps)}\n                  {...(childProps as TChildProps)}\n                />\n              );\n            }}\n          </Subscription>\n        );\n      }\n    }\n\n    // Make sure we preserve any custom statics on the original component.\n    return hoistNonReactStatics(GraphQL, WrappedComponent, {});\n  };\n}\n"]}