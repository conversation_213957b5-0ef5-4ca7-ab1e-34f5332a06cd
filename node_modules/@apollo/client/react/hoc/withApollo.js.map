{"version": 3, "file": "withApollo.js", "sourceRoot": "", "sources": ["../../../src/react/hoc/withApollo.tsx"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAC7D,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAEjC,OAAO,oBAAoB,MAAM,yBAAyB,CAAC;AAE3D,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AAGrD,SAAS,cAAc,CAAI,gBAA6C;IACtE,OAAO,gBAAgB,CAAC,WAAW,IAAI,gBAAgB,CAAC,IAAI,IAAI,WAAW,CAAC;AAC9E,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,UAAU,CACxB,gBAEC,EACD,gBAAuD;IAAvD,iCAAA,EAAA,qBAAuD;IAEvD,IAAM,eAAe,GAAG,qBAAc,cAAc,CAAC,gBAAgB,CAAC,MAAG,CAAC;IAE1E;QAAyB,8BAAuC;QAO9D,oBAAY,KAA6B;YACvC,YAAA,MAAK,YAAC,KAAK,CAAC,SAAC;YACb,KAAI,CAAC,kBAAkB,GAAG,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;;QAC/D,CAAC;QAED,uCAAkB,GAAlB;YACE,SAAS,CACP,gBAAgB,CAAC,OAAO,EACxB,sDAAsD;gBACpD,kCAAkC,CACrC,CAAC;YAEF,OAAO,IAAI,CAAC,eAAe,CAAC;QAC9B,CAAC;QAED,uCAAkB,GAAlB,UACE,GAAuD;YAEvD,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;QAC7B,CAAC;QAED,2BAAM,GAAN;YAAA,iBAaC;YAZC,OAAO,CACL,oBAAC,cAAc,QACZ,UAAC,MAAM;gBACN,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAI,CAAC,KAAK,EAAE;oBAC1C,MAAM,QAAA;oBACN,GAAG,EACD,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,SAAS;iBACjE,CAAC,CAAC;gBACH,OAAO,oBAAC,gBAAgB,eAAK,KAAK,EAAI,CAAC;YACzC,CAAC,CACc,CAClB,CAAC;QACJ,CAAC;QAxCM,sBAAW,GAAG,eAAe,CAAC;QAC9B,2BAAgB,GAAG,gBAAgB,CAAC;QAwC7C,iBAAC;KAAA,AA1CD,CAAyB,KAAK,CAAC,SAAS,GA0CvC;IAED,sEAAsE;IACtE,OAAO,oBAAoB,CAAC,UAAU,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;AAChE,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport type * as ReactTypes from \"react\";\nimport hoistNonReactStatics from \"hoist-non-react-statics\";\n\nimport { ApolloConsumer } from \"../context/index.js\";\nimport type { OperationOption, WithApolloClient } from \"./types.js\";\n\nfunction getDisplayName<P>(WrappedComponent: ReactTypes.ComponentType<P>) {\n  return WrappedComponent.displayName || WrappedComponent.name || \"Component\";\n}\n\n/**\n * @deprecated\n * Official support for React Apollo higher order components ended in March 2020.\n * This library is still included in the `@apollo/client` package, but it no longer receives feature updates or bug fixes.\n */\nexport function withApollo<TProps, TResult = any>(\n  WrappedComponent: ReactTypes.ComponentType<\n    WithApolloClient<Omit<TProps, \"client\">>\n  >,\n  operationOptions: OperationOption<TProps, TResult> = {}\n): ReactTypes.ComponentClass<Omit<TProps, \"client\">> {\n  const withDisplayName = `withApollo(${getDisplayName(WrappedComponent)})`;\n\n  class WithApollo extends React.Component<Omit<TProps, \"client\">> {\n    static displayName = withDisplayName;\n    static WrappedComponent = WrappedComponent;\n\n    // wrapped instance\n    private wrappedInstance: any;\n\n    constructor(props: Omit<TProps, \"client\">) {\n      super(props);\n      this.setWrappedInstance = this.setWrappedInstance.bind(this);\n    }\n\n    getWrappedInstance() {\n      invariant(\n        operationOptions.withRef,\n        `To access the wrapped instance, you need to specify ` +\n          `{ withRef: true } in the options`\n      );\n\n      return this.wrappedInstance;\n    }\n\n    setWrappedInstance(\n      ref: ReactTypes.ComponentType<WithApolloClient<TProps>>\n    ) {\n      this.wrappedInstance = ref;\n    }\n\n    render() {\n      return (\n        <ApolloConsumer>\n          {(client) => {\n            const props = Object.assign({}, this.props, {\n              client,\n              ref:\n                operationOptions.withRef ? this.setWrappedInstance : undefined,\n            });\n            return <WrappedComponent {...props} />;\n          }}\n        </ApolloConsumer>\n      );\n    }\n  }\n\n  // Make sure we preserve any custom statics on the original component.\n  return hoistNonReactStatics(WithApollo, WrappedComponent, {});\n}\n"]}