{"version": 3, "file": "mutation-hoc.js", "sourceRoot": "", "sources": ["../../../src/react/hoc/mutation-hoc.tsx"], "names": [], "mappings": ";AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAGjC,OAAO,oBAAoB,MAAM,yBAAyB,CAAC;AAE3D,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAG5C,OAAO,EAAE,QAAQ,EAAE,MAAM,wBAAwB,CAAC;AAElD,OAAO,EACL,wBAAwB,EACxB,cAAc,EACd,2BAA2B,EAC3B,WAAW,GACZ,MAAM,gBAAgB,CAAC;AAIxB;;;;GAIG;AACH,MAAM,UAAU,YAAY,CAQ1B,QAAsB,EACtB,gBAKM;IALN,iCAAA,EAAA,qBAKM;IAEN,6EAA6E;IAC7E,IAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;IACnC,kBAAkB;IAEV,IAAA,KACN,gBAAgB,QADwB,EAAlC,OAAO,mBAAG,wBAAwB,KAAA,EAAE,KAC1C,gBAAgB,MAD0C,EAAhB,KAAK,mBAAG,QAAQ,KAAA,CACzC;IAEnB,IAAI,iBAAiB,GAAG,OAE4C,CAAC;IACrE,IAAI,OAAO,iBAAiB,KAAK,UAAU;QACzC,iBAAiB,GAAG;YAClB,OAAA,OAKC;QALD,CAKC,CAAC;IAEN,OAAO,UACL,gBAAgE;QAEhE,IAAM,kBAAkB,GAAG,UAAG,KAAK,cAAI,cAAc,CAAC,gBAAgB,CAAC,MAAG,CAAC;QAC3E;YAAsB,2BAAgC;YAAtD;;YA0DA,CAAC;YAvDC,wBAAM,GAAN;gBACE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAe,CAAC;gBACjC,IAAM,IAAI,GAAG,iBAAiB,CAAC,KAAK,CAKnC,CAAC;gBAEF,IAAI,gBAAgB,CAAC,OAAO,EAAE,CAAC;oBAC7B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;oBACpB,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE;wBAC/B,GAAG,EAAE,IAAI,CAAC,kBAAkB;qBAC7B,CAAC,CAAC;gBACL,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtD,IAAI,CAAC,SAAS,GAAG,2BAA2B,CAC1C,SAAS,EACT,KAAK,CACe,CAAC;gBACzB,CAAC;gBAED,OAAO;gBACL,mBAAmB;gBACnB,oBAAC,QAAQ,aAAC,aAAa,UAAK,IAAI,IAAE,QAAQ,EAAE,QAAQ,KACjD,UAAC,MAAM,EAAE,EAAc;oBACtB,oEAAoE;oBACpE,0DAA0D;oBAC1D,mEAAmE;oBACnE,6BAA6B;;oBAJnB,IAAA,IAAI,UAAA,EAAK,CAAC,cAAZ,QAAc,CAAF;oBAMpB,2EAA2E;oBAC3E,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;oBAC5C,IAAM,IAAI,GAAG,gBAAgB,CAAC,IAAI,IAAI,QAAQ,CAAC;oBAC/C,IAAM,UAAU,GACd,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,UAAG,IAAI,WAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC;oBACrD,IAAI,UAAU,GAAG,CAAA;wBACf,GAAC,IAAI,IAAG,MAAM;wBACd,GAAC,UAAU,IAAG,MAAM;0BACC,CAAA,CAAC;oBACxB,IAAI,gBAAgB,CAAC,KAAK,EAAE,CAAC;wBAC3B,IAAM,SAAS;4BAEX,GAAC,IAAI,IAAG,MAAM;4BACd,GAAC,UAAU,IAAG,MAAM;4BACpB,WAAQ,GAAE,KAAK;+BAChB,CAAC;wBACJ,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,SAAS,CAAQ,CAAC;oBACxD,CAAC;oBAED,OAAO,oBAAC,gBAAgB,eAAK,KAAK,EAAM,UAAU,EAAI,CAAC;gBACzD,CAAC,CACQ,CACZ,CAAC;YACJ,CAAC;YAxDM,mBAAW,GAAG,kBAAkB,CAAC;YACjC,wBAAgB,GAAG,gBAAgB,CAAC;YAwD7C,cAAC;SAAA,AA1DD,CAAsB,WAAW,GA0DhC;QAED,sEAAsE;QACtE,OAAO,oBAAoB,CAAC,OAAO,EAAE,gBAAgB,EAAE,EAAE,CAAC,CAAC;IAC7D,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport type * as ReactTypes from \"react\";\nimport type { DocumentNode } from \"graphql\";\nimport hoistNonReactStatics from \"hoist-non-react-statics\";\n\nimport { parser } from \"../parser/index.js\";\nimport type { DefaultContext, OperationVariables } from \"../../core/types.js\";\nimport type { BaseMutationOptions } from \"../types/types.js\";\nimport { Mutation } from \"../components/index.js\";\n\nimport {\n  defaultMapPropsToOptions,\n  getDisplayName,\n  calculateVariablesFromProps,\n  GraphQLBase,\n} from \"./hoc-utils.js\";\nimport type { OperationOption, OptionProps, MutateProps } from \"./types.js\";\nimport type { ApolloCache } from \"../../core/index.js\";\n\n/**\n * @deprecated\n * Official support for React Apollo higher order components ended in March 2020.\n * This library is still included in the `@apollo/client` package, but it no longer receives feature updates or bug fixes.\n */\nexport function withMutation<\n  TProps extends TGraphQLVariables | {} = {},\n  TData extends Record<string, any> = {},\n  TGraphQLVariables extends OperationVariables = {},\n  TChildProps = MutateProps<TData, TGraphQLVariables>,\n  TContext extends Record<string, any> = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n>(\n  document: DocumentNode,\n  operationOptions: OperationOption<\n    TProps,\n    TData,\n    TGraphQLVariables,\n    TChildProps\n  > = {}\n) {\n  // this is memoized so if coming from `graphql` there is nearly no extra cost\n  const operation = parser(document);\n  // extract options\n\n  const { options = defaultMapPropsToOptions, alias = \"Apollo\" } =\n    operationOptions;\n\n  let mapPropsToOptions = options as (\n    props: any\n  ) => BaseMutationOptions<TData, TGraphQLVariables, TContext, TCache>;\n  if (typeof mapPropsToOptions !== \"function\")\n    mapPropsToOptions = () =>\n      options as BaseMutationOptions<\n        TData,\n        TGraphQLVariables,\n        TContext,\n        TCache\n      >;\n\n  return (\n    WrappedComponent: ReactTypes.ComponentType<TProps & TChildProps>\n  ): ReactTypes.ComponentClass<TProps> => {\n    const graphQLDisplayName = `${alias}(${getDisplayName(WrappedComponent)})`;\n    class GraphQL extends GraphQLBase<TProps, TChildProps> {\n      static displayName = graphQLDisplayName;\n      static WrappedComponent = WrappedComponent;\n      render() {\n        let props = this.props as TProps;\n        const opts = mapPropsToOptions(props) as BaseMutationOptions<\n          TData,\n          TGraphQLVariables,\n          TContext,\n          TCache\n        >;\n\n        if (operationOptions.withRef) {\n          this.withRef = true;\n          props = Object.assign({}, props, {\n            ref: this.setWrappedInstance,\n          });\n        }\n        if (!opts.variables && operation.variables.length > 0) {\n          opts.variables = calculateVariablesFromProps(\n            operation,\n            props\n          ) as TGraphQLVariables;\n        }\n\n        return (\n          // @ts-expect-error\n          <Mutation ignoreResults {...opts} mutation={document}>\n            {(mutate, { data, ...r }) => {\n              // the HOC's historically hoisted the data from the execution result\n              // up onto the result since it was passed as a nested prop\n              // we massage the Mutation component's shape here to replicate that\n              // this matches the query HoC\n\n              // @ts-ignore Type instantiation is excessively deep and possibly infinite.\n              const result = Object.assign(r, data || {});\n              const name = operationOptions.name || \"mutate\";\n              const resultName =\n                operationOptions.name ? `${name}Result` : \"result\";\n              let childProps = {\n                [name]: mutate,\n                [resultName]: result,\n              } as any as TChildProps;\n              if (operationOptions.props) {\n                const newResult: OptionProps<TProps, TData, TGraphQLVariables> =\n                  {\n                    [name]: mutate,\n                    [resultName]: result,\n                    ownProps: props,\n                  };\n                childProps = operationOptions.props(newResult) as any;\n              }\n\n              return <WrappedComponent {...props} {...childProps} />;\n            }}\n          </Mutation>\n        );\n      }\n    }\n\n    // Make sure we preserve any custom statics on the original component.\n    return hoistNonReactStatics(GraphQL, WrappedComponent, {});\n  };\n}\n"]}