{"version": 3, "file": "hoc-utils.js", "sourceRoot": "", "sources": ["../../../src/react/hoc/hoc-utils.tsx"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAC7D,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAIjC,MAAM,CAAC,IAAM,wBAAwB,GAAG,cAAM,OAAA,CAAC,EAAE,CAAC,EAAJ,CAAI,CAAC;AACnD,MAAM,CAAC,IAAM,uBAAuB,GAAuB,UAAC,KAAK,IAAK,OAAA,KAAK,EAAL,CAAK,CAAC;AAC5E,MAAM,CAAC,IAAM,qBAAqB,GAAG,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC;AAEjD,MAAM,UAAU,cAAc,CAAI,gBAAwC;IACxE,OAAO,gBAAgB,CAAC,WAAW,IAAI,gBAAgB,CAAC,IAAI,IAAI,WAAW,CAAC;AAC9E,CAAC;AAED,MAAM,UAAU,2BAA2B,CACzC,SAA8B,EAC9B,KAAa;IAEb,IAAI,SAAS,GAAuB,EAAE,CAAC;IACvC,KAA+B,UAAmB,EAAnB,KAAA,SAAS,CAAC,SAAS,EAAnB,cAAmB,EAAnB,IAAmB,EAAE,CAAC;QAA5C,IAAA,WAAkB,EAAhB,QAAQ,cAAA,EAAE,IAAI,UAAA;QACvB,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK;YAAE,SAAS;QAErD,IAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;QACzC,IAAM,YAAY,GAAI,KAAa,CAAC,YAAY,CAAC,CAAC;QAElD,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE,CAAC;YACxC,SAAS,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC;YACvC,SAAS;QACX,CAAC;QAED,uBAAuB;QACvB,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAChC,SAAS,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC;QACtC,CAAC;IACH,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAMD,4CAA4C;AAC5C;IAIU,+BAA+B;IAKvC,qBAAY,KAAa;QACvB,YAAA,MAAK,YAAC,KAAK,CAAC,SAAC;QALR,aAAO,GAAY,KAAK,CAAC;QAM9B,KAAI,CAAC,kBAAkB,GAAG,KAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;;IAC/D,CAAC;IAED,wCAAkB,GAAlB;QACE,SAAS,CACP,IAAI,CAAC,OAAO,EACZ,sDAAsD;YACpD,kCAAkC,CACrC,CAAC;QAEF,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,wCAAkB,GAAlB,UAAmB,GAAsC;QACvD,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC;IAC7B,CAAC;IACH,kBAAC;AAAD,CAAC,AA3BD,CAIU,KAAK,CAAC,SAAS,GAuBxB", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport type { OperationVariables } from \"../../core/index.js\";\nimport type { IDocumentDefinition } from \"../parser/index.js\";\n\nexport const defaultMapPropsToOptions = () => ({});\nexport const defaultMapResultToProps: <P>(props: P) => P = (props) => props;\nexport const defaultMapPropsToSkip = () => false;\n\nexport function getDisplayName<P>(WrappedComponent: React.ComponentType<P>) {\n  return WrappedComponent.displayName || WrappedComponent.name || \"Component\";\n}\n\nexport function calculateVariablesFromProps<TProps>(\n  operation: IDocumentDefinition,\n  props: TProps\n) {\n  let variables: OperationVariables = {};\n  for (let { variable, type } of operation.variables) {\n    if (!variable.name || !variable.name.value) continue;\n\n    const variableName = variable.name.value;\n    const variableProp = (props as any)[variableName];\n\n    if (typeof variableProp !== \"undefined\") {\n      variables[variableName] = variableProp;\n      continue;\n    }\n\n    // Allow optional props\n    if (type.kind !== \"NonNullType\") {\n      variables[variableName] = undefined;\n    }\n  }\n  return variables;\n}\n\nexport type RefSetter<TChildProps> = (\n  ref: React.ComponentClass<TChildProps>\n) => void | void;\n\n// base class for hocs to easily manage refs\nexport class GraphQLBase<\n  TProps,\n  TChildProps,\n  TState = any,\n> extends React.Component<TProps, TState> {\n  public withRef: boolean = false;\n  // wrapped instance\n  private wrappedInstance?: React.ComponentClass<TChildProps>;\n\n  constructor(props: TProps) {\n    super(props);\n    this.setWrappedInstance = this.setWrappedInstance.bind(this);\n  }\n\n  getWrappedInstance() {\n    invariant(\n      this.withRef,\n      `To access the wrapped instance, you need to specify ` +\n        `{ withRef: true } in the options`\n    );\n\n    return this.wrappedInstance;\n  }\n\n  setWrappedInstance(ref: React.ComponentClass<TChildProps>) {\n    this.wrappedInstance = ref;\n  }\n}\n"]}