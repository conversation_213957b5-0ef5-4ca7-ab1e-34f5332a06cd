{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/react/hoc/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ApolloError } from \"../../errors/index.js\";\nimport type {\n  ApolloCache,\n  ApolloClient,\n  ApolloQueryResult,\n  DefaultContext,\n  FetchMoreOptions,\n  FetchMoreQueryOptions,\n  OperationVariables,\n  SubscribeToMoreOptions,\n  UpdateQueryMapFn,\n} from \"../../core/index.js\";\nimport type {\n  MutationFunction,\n  BaseQueryOptions,\n  BaseMutationOptions,\n  MutationResult,\n} from \"../types/types.js\";\n\nexport interface QueryControls<\n  TData = any,\n  TGraphQLVariables = OperationVariables,\n> {\n  error?: ApolloError;\n  networkStatus: number;\n  loading: boolean;\n  variables: TGraphQLVariables;\n  fetchMore: (\n    fetchMoreOptions: FetchMoreQueryOptions<TGraphQLVariables, TData> &\n      FetchMoreOptions<TData, TGraphQLVariables>\n  ) => Promise<ApolloQueryResult<TData>>;\n  refetch: (variables?: TGraphQLVariables) => Promise<ApolloQueryResult<TData>>;\n  startPolling: (pollInterval: number) => void;\n  stopPolling: () => void;\n  subscribeToMore: (options: SubscribeToMoreOptions) => () => void;\n  updateQuery: (mapFn: UpdateQueryMapFn<TData, TGraphQLVariables>) => void;\n}\n\nexport type DataValue<\n  TData,\n  TGraphQLVariables = OperationVariables,\n> = QueryControls<TData, TGraphQLVariables> &\n  // data may not yet be loaded\n  Partial<TData>;\n\nexport interface DataProps<TData, TGraphQLVariables = OperationVariables> {\n  data: DataValue<TData, TGraphQLVariables>;\n}\n\nexport interface MutateProps<\n  TData = any,\n  TGraphQLVariables = OperationVariables,\n> {\n  mutate: MutationFunction<TData, TGraphQLVariables>;\n  result: MutationResult<TData>;\n}\n\nexport type ChildProps<\n  TProps = {},\n  TData = {},\n  TGraphQLVariables = OperationVariables,\n> = TProps &\n  Partial<DataProps<TData, TGraphQLVariables>> &\n  Partial<MutateProps<TData, TGraphQLVariables>>;\n\nexport type ChildDataProps<\n  TProps = {},\n  TData = {},\n  TGraphQLVariables = OperationVariables,\n> = TProps & DataProps<TData, TGraphQLVariables>;\n\nexport type ChildMutateProps<\n  TProps = {},\n  TData = {},\n  TGraphQLVariables = OperationVariables,\n> = TProps & MutateProps<TData, TGraphQLVariables>;\n\nexport interface OptionProps<\n  TProps = any,\n  TData = any,\n  TGraphQLVariables = OperationVariables,\n> extends Partial<DataProps<TData, TGraphQLVariables>>,\n    Partial<MutateProps<TData, TGraphQLVariables>> {\n  ownProps: TProps;\n}\n\nexport interface OperationOption<\n  TProps,\n  TData,\n  TGraphQLVariables extends OperationVariables = OperationVariables,\n  TChildProps = ChildProps<TProps, TData, TGraphQLVariables>,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> {\n  options?:\n    | BaseQueryOptions<TGraphQLVariables>\n    | BaseMutationOptions<TData, TGraphQLVariables, TContext, TCache>\n    | ((\n        props: TProps\n      ) =>\n        | BaseQueryOptions<TGraphQLVariables>\n        | BaseMutationOptions<TData, TGraphQLVariables, TContext, TCache>);\n  props?: (\n    props: OptionProps<TProps, TData, TGraphQLVariables>,\n    lastProps?: TChildProps | void\n  ) => TChildProps;\n  skip?: boolean | ((props: TProps) => boolean);\n  name?: string;\n  withRef?: boolean;\n  shouldResubscribe?: (props: TProps, nextProps: TProps) => boolean;\n  alias?: string;\n}\n\nexport type WithApolloClient<P> = P & { client?: ApolloClient<any> };\n"]}