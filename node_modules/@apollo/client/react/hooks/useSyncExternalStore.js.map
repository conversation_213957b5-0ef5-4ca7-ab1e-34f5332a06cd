{"version": 3, "file": "useSyncExternalStore.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useSyncExternalStore.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAC7D,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAEjC,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAE9D,IAAI,0BAA0B,GAAG,KAAK,CAAC;AASvC,sEAAsE;AACtE,8EAA8E;AAC9E,6DAA6D;AAC7D,IAAM,OAAO,GAAG,sBAA4C,CAAC;AAC7D,IAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAmC,CAAC;AAElE,2EAA2E;AAC3E,sEAAsE;AAEtE,uEAAuE;AACvE,MAAM,CAAC,IAAM,oBAAoB,GAC/B,QAAQ;IACR,CAAC,UAAC,SAAS,EAAE,WAAW,EAAE,iBAAiB;QACzC,wEAAwE;QACxE,qEAAqE;QACrE,4DAA4D;QAC5D,sBAAsB;QACtB,IAAM,KAAK,GAAG,WAAW,EAAE,CAAC;QAC5B;QACE,2BAA2B;QAC3B,OAAO;YACP,CAAC,0BAA0B;YAC3B,0EAA0E;YAC1E,4DAA4D;YAC5D,KAAK,KAAK,WAAW,EAAE,EACvB,CAAC;YACD,0BAA0B,GAAG,IAAI,CAAC;YAClC,sEAAsE;YACtE,SAAS,CAAC,KAAK,CACb,sEAAsE,CACvE,CAAC;QACJ,CAAC;QAED,2EAA2E;QAC3E,sEAAsE;QACtE,4EAA4E;QAC5E,qBAAqB;QACrB,EAAE;QACF,4EAA4E;QAC5E,gEAAgE;QAChE,EAAE;QACF,4EAA4E;QAC5E,8EAA8E;QAC9E,6BAA6B;QAC7B,EAAE;QACF,4EAA4E;QAC5E,6CAA6C;QACvC,IAAA,KAA0B,KAAK,CAAC,QAAQ,CAAC;YAC7C,IAAI,EAAE,EAAE,KAAK,OAAA,EAAE,WAAW,aAAA,EAAE;SAC7B,CAAC,EAFO,IAAI,aAAA,EAAI,WAAW,QAE1B,CAAC;QAEH,6EAA6E;QAC7E,wEAAwE;QACxE,wBAAwB;QACxB,IAAI,kBAAkB,EAAE,CAAC;YACvB,wEAAwE;YACxE,yEAAyE;YACzE,uEAAuE;YACvE,YAAY;YACZ,KAAK,CAAC,eAAe,CAAC;gBACpB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,KAAK,OAAA,EAAE,WAAW,aAAA,EAAE,CAAC,CAAC;gBAC5C,qEAAqE;gBACrE,wEAAwE;gBACxE,yEAAyE;gBACzE,qCAAqC;gBACrC,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;oBACjC,qBAAqB;oBACrB,WAAW,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;gBACxB,CAAC;gBACD,uHAAuH;gBACvH,uDAAuD;YACzD,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;QACtC,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,KAAK,OAAA,EAAE,WAAW,aAAA,EAAE,CAAC,CAAC;QAC9C,CAAC;QAED,KAAK,CAAC,SAAS,CAAC;YACd,yEAAyE;YACzE,wCAAwC;YACxC,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,qBAAqB;gBACrB,WAAW,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;YACxB,CAAC;YAED,yDAAyD;YACzD,OAAO,SAAS,CAAC,SAAS,iBAAiB;gBACzC,0EAA0E;gBAC1E,sEAAsE;gBACtE,wEAAwE;gBACxE,+CAA+C;gBAE/C,0EAA0E;gBAC1E,uBAAuB;gBACvB,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC;oBACjC,qBAAqB;oBACrB,WAAW,CAAC,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC,CAAC,CAAC;YACH,iHAAiH;YACjH,uDAAuD;QACzD,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;QAEhB,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;AAEL,SAAS,sBAAsB,CAAW,EAMzC;QALC,KAAK,WAAA,EACL,WAAW,iBAAA;IAKX,IAAI,CAAC;QACH,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;IACjC,CAAC;IAAC,WAAM,CAAC;QACP,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\n\nimport { canUseLayoutEffect } from \"../../utilities/index.js\";\n\nlet didWarnUncachedGetSnapshot = false;\n\ntype RealUseSESHookType =\n  // This import depends only on the @types/use-sync-external-store package, not\n  // the actual use-sync-external-store package, which is not installed. It\n  // might be nice to get this type from React 18, but it still needs to work\n  // when only React 17 or earlier is installed.\n  typeof import(\"use-sync-external-store\").useSyncExternalStore;\n\n// Prevent webpack from complaining about our feature detection of the\n// useSyncExternalStore property of the React namespace, which is expected not\n// to exist when using React 17 and earlier, and that's fine.\nconst uSESKey = \"useSyncExternalStore\" as keyof typeof React;\nconst realHook = React[uSESKey] as RealUseSESHookType | undefined;\n\n// Adapted from https://www.npmjs.com/package/use-sync-external-store, with\n// Apollo Client deviations called out by \"// DEVIATION ...\" comments.\n\n// When/if React.useSyncExternalStore is defined, delegate fully to it.\nexport const useSyncExternalStore: RealUseSESHookType =\n  realHook ||\n  ((subscribe, getSnapshot, getServerSnapshot) => {\n    // Read the current snapshot from the store on every render. Again, this\n    // breaks the rules of React, and only works here because of specific\n    // implementation details, most importantly that updates are\n    // always synchronous.\n    const value = getSnapshot();\n    if (\n      // DEVIATION: Using __DEV__\n      __DEV__ &&\n      !didWarnUncachedGetSnapshot &&\n      // DEVIATION: Not using Object.is because we know our snapshots will never\n      // be exotic primitive values like NaN, which is !== itself.\n      value !== getSnapshot()\n    ) {\n      didWarnUncachedGetSnapshot = true;\n      // DEVIATION: Using invariant.error instead of console.error directly.\n      invariant.error(\n        \"The result of getSnapshot should be cached to avoid an infinite loop\"\n      );\n    }\n\n    // Because updates are synchronous, we don't queue them. Instead we force a\n    // re-render whenever the subscribed state changes by updating an some\n    // arbitrary useState hook. Then, during render, we call getSnapshot to read\n    // the current value.\n    //\n    // Because we don't actually use the state returned by the useState hook, we\n    // can save a bit of memory by storing other stuff in that slot.\n    //\n    // To implement the early bailout, we need to track some things on a mutable\n    // object. Usually, we would put that in a useRef hook, but we can stash it in\n    // our useState hook instead.\n    //\n    // To force a re-render, we call forceUpdate({inst}). That works because the\n    // new object always fails an equality check.\n    const [{ inst }, forceUpdate] = React.useState({\n      inst: { value, getSnapshot },\n    });\n\n    // Track the latest getSnapshot function with a ref. This needs to be updated\n    // in the layout phase so we can access it during the tearing check that\n    // happens on subscribe.\n    if (canUseLayoutEffect) {\n      // DEVIATION: We avoid calling useLayoutEffect when !canUseLayoutEffect,\n      // which may seem like a conditional hook, but this code ends up behaving\n      // unconditionally (one way or the other) because canUseLayoutEffect is\n      // constant.\n      React.useLayoutEffect(() => {\n        Object.assign(inst, { value, getSnapshot });\n        // Whenever getSnapshot or subscribe changes, we need to check in the\n        // commit phase if there was an interleaved mutation. In concurrent mode\n        // this can happen all the time, but even in synchronous mode, an earlier\n        // effect may have mutated the store.\n        if (checkIfSnapshotChanged(inst)) {\n          // Force a re-render.\n          forceUpdate({ inst });\n        }\n        // React Hook React.useLayoutEffect has a missing dependency: 'inst'. Either include it or remove the dependency array.\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n      }, [subscribe, value, getSnapshot]);\n    } else {\n      Object.assign(inst, { value, getSnapshot });\n    }\n\n    React.useEffect(() => {\n      // Check for changes right before subscribing. Subsequent changes will be\n      // detected in the subscription handler.\n      if (checkIfSnapshotChanged(inst)) {\n        // Force a re-render.\n        forceUpdate({ inst });\n      }\n\n      // Subscribe to the store and return a clean-up function.\n      return subscribe(function handleStoreChange() {\n        // TODO: Because there is no cross-renderer API for batching updates, it's\n        // up to the consumer of this library to wrap their subscription event\n        // with unstable_batchedUpdates. Should we try to detect when this isn't\n        // the case and print a warning in development?\n\n        // The store changed. Check if the snapshot changed since the last time we\n        // read from the store.\n        if (checkIfSnapshotChanged(inst)) {\n          // Force a re-render.\n          forceUpdate({ inst });\n        }\n      });\n      // React Hook React.useEffect has a missing dependency: 'inst'. Either include it or remove the dependency array.\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [subscribe]);\n\n    return value;\n  });\n\nfunction checkIfSnapshotChanged<Snapshot>({\n  value,\n  getSnapshot,\n}: {\n  value: Snapshot;\n  getSnapshot: () => Snapshot;\n}): boolean {\n  try {\n    return value !== getSnapshot();\n  } catch {\n    return true;\n  }\n}\n"]}