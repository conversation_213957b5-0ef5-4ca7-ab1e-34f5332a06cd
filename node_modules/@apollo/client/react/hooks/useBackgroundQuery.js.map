{"version": 3, "file": "useBackgroundQuery.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useBackgroundQuery.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AASjC,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EACL,gBAAgB,EAChB,cAAc,EACd,qBAAqB,EACrB,YAAY,GACb,MAAM,sBAAsB,CAAC;AAG9B,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAC/C,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAE7D,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAyJ1D,MAAM,UAAU,kBAAkB,CAIhC,KAA0D,EAC1D,OAG8E;IAH9E,wBAAA,EAAA,UAG2D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAK9E,OAAO,QAAQ,CACb,oBAAoB;IACpB,yDAAyD;IACzD,mBAAmB,EACnB,eAAe,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAC1E,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACpB,CAAC;AAED,SAAS,mBAAmB,CAI1B,KAA0D,EAC1D,OAGwD;IAKxD,IAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,IAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC/C,IAAM,iBAAiB,GAAG,oBAAoB,CAAC,EAAE,MAAM,QAAA,EAAE,KAAK,OAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC;IACnE,IAAA,WAAW,GAAgB,iBAAiB,YAAjC,EAAE,SAAS,GAAK,iBAAiB,UAAtB,CAAuB;IAC7C,IAAA,KAAkB,OAAO,SAAZ,EAAb,QAAQ,mBAAG,EAAE,KAAA,CAAa;IAElC,yEAAyE;IACzE,iEAAiE;IACjE,wEAAwE;IACxE,wEAAwE;IACxE,6EAA6E;IAC7E,uBAAuB;IACvB,IAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC;IAC/D,cAAc,CAAC,OAAO,KAAtB,cAAc,CAAC,OAAO,GAAK,WAAW,KAAK,SAAS,EAAC;IAErD,IAAM,QAAQ;QACZ,KAAK;QACL,kBAAkB,CAAC,SAAS,CAAC;OACzB,EAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,OAClC,CAAC;IAEF,IAAM,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAC,QAAQ,EAAE;QACnD,OAAA,MAAM,CAAC,UAAU,CAAC,iBAAgD,CAAC;IAAnE,CAAmE,CACpE,CAAC;IAEI,IAAA,KAAwC,KAAK,CAAC,QAAQ,CAC1D,YAAY,CAAC,QAAQ,CAAC,CACvB,EAFM,eAAe,QAAA,EAAE,kBAAkB,QAEzC,CAAC;IACF,IAAI,cAAc,CAAC,eAAe,CAAC,KAAK,QAAQ,EAAE,CAAC;QACjD,kBAAkB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC7C,CAAC;IACD,IAAI,QAAQ,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACjD,IAAM,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;QACzD,qBAAqB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED,sEAAsE;IACtE,2EAA2E;IAC3E,0EAA0E;IAC1E,6EAA6E;IAC7E,yDAAyD;IACzD,KAAK,CAAC,SAAS,CAAC;QACd,2EAA2E;QAC3E,iEAAiE;QACjE,IAAM,EAAE,GAAG,UAAU,CAAC;YACpB,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACtB,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YACxC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,cAAM,OAAA,YAAY,CAAC,EAAE,CAAC,EAAhB,CAAgB,CAAC;QAC9B,uEAAuE;QACvE,kEAAkE;IACpE,CAAC,CAAC,CAAC;IAEH,IAAM,SAAS,GAAyC,KAAK,CAAC,WAAW,CACvE,UAAC,OAAO;QACN,IAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAqC,CAAC,CAAC;QAE1E,kBAAkB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE3C,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,IAAM,OAAO,GAAuC,KAAK,CAAC,WAAW,CACnE,UAAC,SAAS;QACR,IAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAE5C,kBAAkB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;QAE3C,OAAO,OAAO,CAAC;IACjB,CAAC,EACD,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,KAAK,CAAC,SAAS,CAAC,cAAM,OAAA,QAAQ,CAAC,UAAU,EAAE,EAArB,CAAqB,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEzD,OAAO;QACL,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC;QACjD;YACE,SAAS,WAAA;YACT,OAAO,SAAA;YACP,kGAAkG;YAClG,eAAe,EAAE,QAAQ,CAAC,UAAU;iBACjC,eAA6D;SACjE;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport type {\n  DocumentNode,\n  FetchMoreQueryOptions,\n  OperationVariables,\n  TypedDocumentNode,\n  WatchQueryOptions,\n} from \"../../core/index.js\";\nimport type { SubscribeToMoreFunction } from \"../../core/watchQueryOptions.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport {\n  getSuspenseCache,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n  wrapQueryRef,\n} from \"../internal/index.js\";\nimport type { CacheKey, QueryRef } from \"../internal/index.js\";\nimport type { BackgroundQueryHookOptions, NoInfer } from \"../types/types.js\";\nimport { wrapHook } from \"./internal/index.js\";\nimport { useWatchQueryOptions } from \"./useSuspenseQuery.js\";\nimport type { FetchMoreFunction, RefetchFunction } from \"./useSuspenseQuery.js\";\nimport { canonicalStringify } from \"../../cache/index.js\";\nimport type { DeepPartial } from \"../../utilities/index.js\";\nimport type { SkipToken } from \"./constants.js\";\n\nexport type UseBackgroundQueryResult<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n> = {\n  /** {@inheritDoc @apollo/client!ObservableQuery#subscribeToMore:member(1)} */\n  subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;\n  /** {@inheritDoc @apollo/client!ObservableQuery#fetchMore:member(1)} */\n  fetchMore: FetchMoreFunction<TData, TVariables>;\n  /** {@inheritDoc @apollo/client!ObservableQuery#refetch:member(1)} */\n  refetch: RefetchFunction<TData, TVariables>;\n};\n\ntype BackgroundQueryHookOptionsNoInfer<\n  TData,\n  TVariables extends OperationVariables,\n> = BackgroundQueryHookOptions<NoInfer<TData>, NoInfer<TVariables>>;\n\nexport function useBackgroundQuery<\n  TData,\n  TVariables extends OperationVariables,\n  TOptions extends Omit<BackgroundQueryHookOptions<TData>, \"variables\">,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: BackgroundQueryHookOptionsNoInfer<TData, TVariables> & TOptions\n): [\n  (\n    | QueryRef<\n        TOptions[\"errorPolicy\"] extends \"ignore\" | \"all\" ?\n          TOptions[\"returnPartialData\"] extends true ?\n            DeepPartial<TData> | undefined\n          : TData | undefined\n        : TOptions[\"returnPartialData\"] extends true ? DeepPartial<TData>\n        : TData,\n        TVariables\n      >\n    | (TOptions[\"skip\"] extends boolean ? undefined : never)\n  ),\n  UseBackgroundQueryResult<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: BackgroundQueryHookOptionsNoInfer<TData, TVariables> & {\n    returnPartialData: true;\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): [\n  QueryRef<DeepPartial<TData> | undefined, TVariables>,\n  UseBackgroundQueryResult<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: BackgroundQueryHookOptionsNoInfer<TData, TVariables> & {\n    errorPolicy: \"ignore\" | \"all\";\n  }\n): [\n  QueryRef<TData | undefined, TVariables>,\n  UseBackgroundQueryResult<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: BackgroundQueryHookOptionsNoInfer<TData, TVariables> & {\n    skip: boolean;\n    returnPartialData: true;\n  }\n): [\n  QueryRef<DeepPartial<TData>, TVariables> | undefined,\n  UseBackgroundQueryResult<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: BackgroundQueryHookOptionsNoInfer<TData, TVariables> & {\n    returnPartialData: true;\n  }\n): [\n  QueryRef<DeepPartial<TData>, TVariables>,\n  UseBackgroundQueryResult<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: BackgroundQueryHookOptionsNoInfer<TData, TVariables> & {\n    skip: boolean;\n  }\n): [\n  QueryRef<TData, TVariables> | undefined,\n  UseBackgroundQueryResult<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: BackgroundQueryHookOptionsNoInfer<TData, TVariables>\n): [QueryRef<TData, TVariables>, UseBackgroundQueryResult<TData, TVariables>];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SkipToken\n): [undefined, UseBackgroundQueryResult<TData, TVariables>];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | SkipToken\n    | (BackgroundQueryHookOptionsNoInfer<TData, TVariables> & {\n        returnPartialData: true;\n      })\n): [\n  QueryRef<DeepPartial<TData>, TVariables> | undefined,\n  UseBackgroundQueryResult<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: SkipToken | BackgroundQueryHookOptionsNoInfer<TData, TVariables>\n): [\n  QueryRef<TData, TVariables> | undefined,\n  UseBackgroundQueryResult<TData, TVariables>,\n];\n\nexport function useBackgroundQuery<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | (SkipToken &\n        Partial<BackgroundQueryHookOptionsNoInfer<TData, TVariables>>)\n    | BackgroundQueryHookOptionsNoInfer<TData, TVariables> = Object.create(null)\n): [\n  QueryRef<TData, TVariables> | undefined,\n  UseBackgroundQueryResult<TData, TVariables>,\n] {\n  return wrapHook(\n    \"useBackgroundQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useBackgroundQuery_,\n    useApolloClient(typeof options === \"object\" ? options.client : undefined)\n  )(query, options);\n}\n\nfunction useBackgroundQuery_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options:\n    | (SkipToken &\n        Partial<BackgroundQueryHookOptionsNoInfer<TData, TVariables>>)\n    | BackgroundQueryHookOptionsNoInfer<TData, TVariables>\n): [\n  QueryRef<TData, TVariables> | undefined,\n  UseBackgroundQueryResult<TData, TVariables>,\n] {\n  const client = useApolloClient(options.client);\n  const suspenseCache = getSuspenseCache(client);\n  const watchQueryOptions = useWatchQueryOptions({ client, query, options });\n  const { fetchPolicy, variables } = watchQueryOptions;\n  const { queryKey = [] } = options;\n\n  // This ref tracks the first time query execution is enabled to determine\n  // whether to return a query ref or `undefined`. When initialized\n  // in a skipped state (either via `skip: true` or `skipToken`) we return\n  // `undefined` for the `queryRef` until the query has been enabled. Once\n  // enabled, a query ref is always returned regardless of whether the query is\n  // skipped again later.\n  const didFetchResult = React.useRef(fetchPolicy !== \"standby\");\n  didFetchResult.current ||= fetchPolicy !== \"standby\";\n\n  const cacheKey: CacheKey = [\n    query,\n    canonicalStringify(variables),\n    ...([] as any[]).concat(queryKey),\n  ];\n\n  const queryRef = suspenseCache.getQueryRef(cacheKey, () =>\n    client.watchQuery(watchQueryOptions as WatchQueryOptions<any, any>)\n  );\n\n  const [wrappedQueryRef, setWrappedQueryRef] = React.useState(\n    wrapQueryRef(queryRef)\n  );\n  if (unwrapQueryRef(wrappedQueryRef) !== queryRef) {\n    setWrappedQueryRef(wrapQueryRef(queryRef));\n  }\n  if (queryRef.didChangeOptions(watchQueryOptions)) {\n    const promise = queryRef.applyOptions(watchQueryOptions);\n    updateWrappedQueryRef(wrappedQueryRef, promise);\n  }\n\n  // This prevents issues where rerendering useBackgroundQuery after the\n  // queryRef has been disposed would cause the hook to return a new queryRef\n  // instance since disposal also removes it from the suspense cache. We add\n  // the queryRef back in the suspense cache so that the next render will reuse\n  // this queryRef rather than initializing a new instance.\n  React.useEffect(() => {\n    // Since the queryRef is disposed async via `setTimeout`, we have to wait a\n    // tick before checking it and adding back to the suspense cache.\n    const id = setTimeout(() => {\n      if (queryRef.disposed) {\n        suspenseCache.add(cacheKey, queryRef);\n      }\n    });\n\n    return () => clearTimeout(id);\n    // Omitting the deps is intentional. This avoids stale closures and the\n    // conditional ensures we aren't running the logic on each render.\n  });\n\n  const fetchMore: FetchMoreFunction<TData, TVariables> = React.useCallback(\n    (options) => {\n      const promise = queryRef.fetchMore(options as FetchMoreQueryOptions<any>);\n\n      setWrappedQueryRef(wrapQueryRef(queryRef));\n\n      return promise;\n    },\n    [queryRef]\n  );\n\n  const refetch: RefetchFunction<TData, TVariables> = React.useCallback(\n    (variables) => {\n      const promise = queryRef.refetch(variables);\n\n      setWrappedQueryRef(wrapQueryRef(queryRef));\n\n      return promise;\n    },\n    [queryRef]\n  );\n\n  React.useEffect(() => queryRef.softRetain(), [queryRef]);\n\n  return [\n    didFetchResult.current ? wrappedQueryRef : void 0,\n    {\n      fetchMore,\n      refetch,\n      // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n      subscribeToMore: queryRef.observable\n        .subscribeToMore as SubscribeToMoreFunction<TData, TVariables>,\n    },\n  ];\n}\n"]}