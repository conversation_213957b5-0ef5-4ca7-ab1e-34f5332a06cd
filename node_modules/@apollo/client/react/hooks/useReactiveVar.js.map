{"version": 3, "file": "useReactiveVar.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useReactiveVar.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAEjC,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAEjE;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,UAAU,cAAc,CAAI,EAAkB;IAClD,OAAO,oBAAoB,CACzB,KAAK,CAAC,WAAW,CACf,UAAC,MAAM;QACL,4DAA4D;QAC5D,2EAA2E;QAC3E,0EAA0E;QAC1E,6DAA6D;QAC7D,sDAAsD;QACtD,OAAO,EAAE,CAAC,YAAY,CAAC,SAAS,MAAM;YACpC,MAAM,EAAE,CAAC;YACT,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC,EACD,CAAC,EAAE,CAAC,CACL,EACD,EAAE,EACF,EAAE,CACH,CAAC;AACJ,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport type { ReactiveVar } from \"../../core/index.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\n\n/**\n * Reads the value of a [reactive variable](https://www.apollographql.com/docs/react/local-state/reactive-variables/) and re-renders the containing component whenever that variable's value changes. This enables a reactive variable to trigger changes _without_ relying on the `useQuery` hook.\n *\n * @example\n * ```jsx\n * import { makeVar, useReactiveVar } from \"@apollo/client\";\n * export const cartItemsVar = makeVar([]);\n *\n * export function Cart() {\n *   const cartItems = useReactiveVar(cartItemsVar);\n *   // ...\n * }\n * ```\n * @since 3.2.0\n * @param rv - A reactive variable.\n * @returns The current value of the reactive variable.\n */\nexport function useReactiveVar<T>(rv: ReactiveVar<T>): T {\n  return useSyncExternalStore(\n    React.useCallback(\n      (update) => {\n        // By reusing the same onNext function in the nested call to\n        // rv.onNextChange(onNext), we can keep using the initial clean-up function\n        // returned by rv.onNextChange(function onNext(v){...}), without having to\n        // register the new clean-up function (returned by the nested\n        // rv.onNextChange(onNext)) with yet another callback.\n        return rv.onNextChange(function onNext() {\n          update();\n          rv.onNextChange(onNext);\n        });\n      },\n      [rv]\n    ),\n    rv,\n    rv\n  );\n}\n"]}