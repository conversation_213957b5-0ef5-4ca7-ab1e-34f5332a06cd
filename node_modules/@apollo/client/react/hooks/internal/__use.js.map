{"version": 3, "file": "__use.js", "sourceRoot": "", "sources": ["../../../../src/react/hooks/internal/__use.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAC;AACnE,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAGjC,sEAAsE;AACtE,6DAA6D;AAC7D,gEAAgE;AAChE,IAAM,MAAM,GAAG,KAA2B,CAAC;AAC3C,IAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAoB,CAAC;AAElD,kFAAkF;AAClF,4CAA4C;AAC5C,MAAM,CAAC,IAAM,KAAK,GAChB,QAAQ;IACR,SAAS,KAAK,CAAS,OAAwB;QAC7C,IAAM,eAAe,GAAG,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAEtD,QAAQ,eAAe,CAAC,MAAM,EAAE,CAAC;YAC/B,KAAK,SAAS;gBACZ,MAAM,eAAe,CAAC;YACxB,KAAK,UAAU;gBACb,MAAM,eAAe,CAAC,MAAM,CAAC;YAC/B,KAAK,WAAW;gBACd,OAAO,eAAe,CAAC,KAAK,CAAC;QACjC,CAAC;IACH,CAAC,CAAC", "sourcesContent": ["import { wrapPromiseWithState } from \"../../../utilities/index.js\";\nimport * as React from \"rehackt\";\n\ntype Use = <T>(promise: Promise<T>) => T;\n// Prevent webpack from complaining about our feature detection of the\n// use property of the React namespace, which is expected not\n// to exist when using current stable versions, and that's fine.\nconst useKey = \"use\" as keyof typeof React;\nconst realHook = React[useKey] as Use | undefined;\n\n// This is named with two underscores to allow this hook to evade typical rules of\n// hooks (i.e. it can be used conditionally)\nexport const __use =\n  realHook ||\n  function __use<TValue>(promise: Promise<TValue>) {\n    const statefulPromise = wrapPromiseWithState(promise);\n\n    switch (statefulPromise.status) {\n      case \"pending\":\n        throw statefulPromise;\n      case \"rejected\":\n        throw statefulPromise.reason;\n      case \"fulfilled\":\n        return statefulPromise.value;\n    }\n  };\n"]}