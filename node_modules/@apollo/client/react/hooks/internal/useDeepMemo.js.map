{"version": 3, "file": "useDeepMemo.js", "sourceRoot": "", "sources": ["../../../../src/react/hooks/internal/useDeepMemo.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AACjC,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,MAAM,UAAU,WAAW,CACzB,MAAoB,EACpB,IAAoB;IAEpB,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAA0C,KAAK,CAAC,CAAC,CAAC;IAE1E,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;QACnD,yDAAyD;QACzD,GAAG,CAAC,OAAO,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,IAAI,MAAA,EAAE,CAAC;IAC1C,CAAC;IAED,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;AAC3B,CAAC", "sourcesContent": ["import type { DependencyList } from \"react\";\nimport * as React from \"rehackt\";\nimport { equal } from \"@wry/equality\";\n\nexport function useDeepMemo<TValue>(\n  memoFn: () => TValue,\n  deps: DependencyList\n) {\n  const ref = React.useRef<{ deps: DependencyList; value: TValue }>(void 0);\n\n  if (!ref.current || !equal(ref.current.deps, deps)) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    ref.current = { value: memoFn(), deps };\n  }\n\n  return ref.current.value;\n}\n"]}