import type { DocumentNode, OperationVariables, TypedDocumentNode } from "../../core/index.js";
import type { SubscribeToMoreFunction } from "../../core/watchQueryOptions.js";
import type { QueryRef } from "../internal/index.js";
import type { LoadableQueryHookOptions } from "../types/types.js";
import type { FetchMoreFunction, RefetchFunction } from "./useSuspenseQuery.js";
import type { DeepPartial, OnlyRequiredProperties } from "../../utilities/index.js";
export type LoadQueryFunction<TVariables extends OperationVariables> = (...args: [TVariables] extends [never] ? [] : {} extends OnlyRequiredProperties<TVariables> ? [variables?: TVariables] : [variables: TVariables]) => void;
type ResetFunction = () => void;
export type UseLoadableQueryResult<TData = unknown, TVariables extends OperationVariables = OperationVariables> = [
    loadQuery: LoadQueryFunction<TVariables>,
    queryRef: QueryRef<TData, TVariables> | null,
    handlers: {
        /**
         * A function that helps you fetch the next set of results for a [paginated list field](https://www.apollographql.com/docs/react/pagination/core-api/).
         * 
         * 
         * @docGroup
         * 
         * 3. Helper functions
         */
        fetchMore: FetchMoreFunction<TData, TVariables>;
        /**
         * A function that enables you to re-execute the query, optionally passing in new `variables`.
         * 
         * To guarantee that the refetch performs a network request, its `fetchPolicy` is set to `network-only` (unless the original query's `fetchPolicy` is `no-cache` or `cache-and-network`, which also guarantee a network request).
         * 
         * See also [Refetching](https://www.apollographql.com/docs/react/data/queries/#refetching).
         * 
         * @docGroup
         * 
         * 3. Helper functions
         */
        refetch: RefetchFunction<TData, TVariables>;
        /**
         * A function that enables you to execute a [subscription](https://www.apollographql.com/docs/react/data/subscriptions/), usually to subscribe to specific fields that were included in the query.
         * 
         * This function returns _another_ function that you can call to terminate the subscription.
         */
        subscribeToMore: SubscribeToMoreFunction<TData, TVariables>;
        /**
         * A function that resets the `queryRef` back to `null`.
         */
        reset: ResetFunction;
    }
];
export declare function useLoadableQuery<TData, TVariables extends OperationVariables, TOptions extends LoadableQueryHookOptions>(query: DocumentNode | TypedDocumentNode<TData, TVariables>, options?: LoadableQueryHookOptions & TOptions): UseLoadableQueryResult<TOptions["errorPolicy"] extends "ignore" | "all" ? TOptions["returnPartialData"] extends true ? DeepPartial<TData> | undefined : TData | undefined : TOptions["returnPartialData"] extends true ? DeepPartial<TData> : TData, TVariables>;
export declare function useLoadableQuery<TData = unknown, TVariables extends OperationVariables = OperationVariables>(query: DocumentNode | TypedDocumentNode<TData, TVariables>, options: LoadableQueryHookOptions & {
    returnPartialData: true;
    errorPolicy: "ignore" | "all";
}): UseLoadableQueryResult<DeepPartial<TData> | undefined, TVariables>;
export declare function useLoadableQuery<TData = unknown, TVariables extends OperationVariables = OperationVariables>(query: DocumentNode | TypedDocumentNode<TData, TVariables>, options: LoadableQueryHookOptions & {
    errorPolicy: "ignore" | "all";
}): UseLoadableQueryResult<TData | undefined, TVariables>;
export declare function useLoadableQuery<TData = unknown, TVariables extends OperationVariables = OperationVariables>(query: DocumentNode | TypedDocumentNode<TData, TVariables>, options: LoadableQueryHookOptions & {
    returnPartialData: true;
}): UseLoadableQueryResult<DeepPartial<TData>, TVariables>;
/**
 * A hook for imperatively loading a query, such as responding to a user
 * interaction.
 *
 * > Refer to the [Suspense - Fetching in response to user interaction](https://www.apollographql.com/docs/react/data/suspense#fetching-in-response-to-user-interaction) section for a more in-depth overview of `useLoadableQuery`.
 *
 * @example
 * ```jsx
 * import { gql, useLoadableQuery } from "@apollo/client";
 *
 * const GET_GREETING = gql`
 *   query GetGreeting($language: String!) {
 *     greeting(language: $language) {
 *       message
 *     }
 *   }
 * `;
 *
 * function App() {
 *   const [loadGreeting, queryRef] = useLoadableQuery(GET_GREETING);
 *
 *   return (
 *     <>
 *       <button onClick={() => loadGreeting({ language: "english" })}>
 *         Load greeting
 *       </button>
 *       <Suspense fallback={<div>Loading...</div>}>
 *         {queryRef && <Hello queryRef={queryRef} />}
 *       </Suspense>
 *     </>
 *   );
 * }
 *
 * function Hello({ queryRef }) {
 *   const { data } = useReadQuery(queryRef);
 *
 *   return <div>{data.greeting.message}</div>;
 * }
 * ```
 *
 * @since 3.9.0
 * @param query - A GraphQL query document parsed into an AST by `gql`.
 * @param options - Options to control how the query is executed.
 * @returns A tuple in the form of `[loadQuery, queryRef, handlers]`
 */
export declare function useLoadableQuery<TData = unknown, TVariables extends OperationVariables = OperationVariables>(query: DocumentNode | TypedDocumentNode<TData, TVariables>, options?: LoadableQueryHookOptions): UseLoadableQueryResult<TData, TVariables>;
export {};
//# sourceMappingURL=useLoadableQuery.d.ts.map