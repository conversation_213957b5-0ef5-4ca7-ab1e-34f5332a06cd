{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/index.ts"], "names": [], "mappings": "AAAA,OAAO,kCAAkC,CAAC;AAE1C,cAAc,sBAAsB,CAAC;AACrC,cAAc,mBAAmB,CAAC;AAClC,cAAc,kBAAkB,CAAC;AACjC,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,cAAc,sBAAsB,CAAC;AACrC,cAAc,qBAAqB,CAAC;AACpC,cAAc,kBAAkB,CAAC;AAEjC,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAEzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yBAAyB,CAAC;AAK7D,OAAO,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAK/D,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AAEzD,OAAO,EAAE,mBAAmB,EAAE,MAAM,0BAA0B,CAAC;AAE/D,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC", "sourcesContent": ["import \"../../utilities/globals/index.js\";\n\nexport * from \"./useApolloClient.js\";\nexport * from \"./useLazyQuery.js\";\nexport * from \"./useMutation.js\";\nexport { useQuery } from \"./useQuery.js\";\nexport * from \"./useSubscription.js\";\nexport * from \"./useReactiveVar.js\";\nexport * from \"./useFragment.js\";\nexport type { UseSuspenseQueryResult } from \"./useSuspenseQuery.js\";\nexport { useSuspenseQuery } from \"./useSuspenseQuery.js\";\nexport type { UseBackgroundQueryResult } from \"./useBackgroundQuery.js\";\nexport { useBackgroundQuery } from \"./useBackgroundQuery.js\";\nexport type {\n  UseSuspenseFragmentResult,\n  UseSuspenseFragmentOptions,\n} from \"./useSuspenseFragment.js\";\nexport { useSuspenseFragment } from \"./useSuspenseFragment.js\";\nexport type {\n  LoadQueryFunction,\n  UseLoadableQueryResult,\n} from \"./useLoadableQuery.js\";\nexport { useLoadableQuery } from \"./useLoadableQuery.js\";\nexport type { UseQueryRefHandlersResult } from \"./useQueryRefHandlers.js\";\nexport { useQueryRefHandlers } from \"./useQueryRefHandlers.js\";\nexport type { UseReadQueryResult } from \"./useReadQuery.js\";\nexport { useReadQuery } from \"./useReadQuery.js\";\nexport { skipToken } from \"./constants.js\";\nexport type { SkipToken } from \"./constants.js\";\n"]}