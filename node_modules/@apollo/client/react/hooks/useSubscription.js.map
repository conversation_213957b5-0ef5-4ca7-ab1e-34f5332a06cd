{"version": 3, "file": "useSubscription.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useSubscription.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAC7D,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAGjC,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AActE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAC9D,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACxD,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AACjE,OAAO,EAAE,aAAa,EAAE,MAAM,eAAe,CAAC;AAC9C,OAAO,EAAE,yBAAyB,EAAE,MAAM,yCAAyC,CAAC;AAGpF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgFG;AACH,MAAM,UAAU,eAAe,CAI7B,YAAiE,EACjE,OAGuB;IAHvB,wBAAA,EAAA,UAGI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;IAEvB,IAAM,8BAA8B,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IAC3D,IAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IAC/C,kBAAkB,CAAC,YAAY,EAAE,YAAY,CAAC,YAAY,CAAC,CAAC;IAE5D,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE,CAAC;QAC5C,yDAAyD;QACzD,8BAA8B,CAAC,OAAO,GAAG,IAAI,CAAC;QAE9C,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;YAC/B,SAAS,CAAC,IAAI,CACZ,OAAO,CAAC,MAAM,CAAC,CAAC;gBACd,mIAAmI;gBACrI,CAAC,CAAC,2HAA2H,CAC9H,CAAC;QACJ,CAAC;QAED,IAAI,OAAO,CAAC,sBAAsB,EAAE,CAAC;YACnC,SAAS,CAAC,IAAI,CACZ,OAAO,CAAC,UAAU,CAAC,CAAC;gBAClB,+IAA+I;gBACjJ,CAAC,CAAC,mIAAmI,CACtI,CAAC;QACJ,CAAC;IACH,CAAC;IAGC,IAAA,IAAI,GAOF,OAAO,KAPL,EACJ,WAAW,GAMT,OAAO,YANE,EACX,WAAW,GAKT,OAAO,YALE,EACX,iBAAiB,GAIf,OAAO,kBAJQ,EACjB,OAAO,GAGL,OAAO,QAHF,EACP,UAAU,GAER,OAAO,WAFC,EACV,aAAa,GACX,OAAO,cADI,CACH;IACZ,IAAM,SAAS,GAAG,WAAW,CAAC,cAAM,OAAA,OAAO,CAAC,SAAS,EAAjB,CAAiB,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;IAE5E,IAAM,QAAQ,GAAG;QACf,OAAA,kBAAkB,CAChB,MAAM,EACN,YAAY,EACZ,SAAS,EACT,WAAW,EACX,WAAW,EACX,OAAO,EACP,UAAU,CACX;IARD,CAQC,CAAC;IAEA,IAAA,KAA8B,KAAK,CAAC,QAAQ,CAC9C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAC/B,EAFI,UAAU,QAAA,EAAE,aAAa,QAE7B,CAAC;IAEF,IAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAC3C,yBAAyB,CAAC;QACxB,WAAW,CAAC,OAAO,GAAG,QAAQ,CAAC;IACjC,CAAC,CAAC,CAAC;IAEH,IAAI,IAAI,EAAE,CAAC;QACT,IAAI,UAAU,EAAE,CAAC;YACf,aAAa,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;SAAM,IACL,CAAC,UAAU;QACX,CAAC,CAAC,MAAM,KAAK,UAAU,CAAC,EAAE,CAAC,MAAM;YAC/B,YAAY,KAAK,UAAU,CAAC,EAAE,CAAC,KAAK;YACpC,WAAW,KAAK,UAAU,CAAC,EAAE,CAAC,WAAW;YACzC,WAAW,KAAK,UAAU,CAAC,EAAE,CAAC,WAAW;YACzC,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC,OAAO,iBAAiB,KAAK,UAAU,CAAC,CAAC;gBACxC,CAAC,CAAC,iBAAiB,CAAC,OAAQ,CAAC;gBAC/B,CAAC,CAAC,iBAAiB,CAAC,KAAK,KAAK,CAAC,EACjC,CAAC;QACD,aAAa,CAAC,CAAC,UAAU,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED,IAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACzC,KAAK,CAAC,SAAS,CAAC;QACd,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAC/B,CAAC,CAAC,CAAC;IAEH,IAAM,eAAe,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC;IAChD,IAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAClC,cAAM,OAAA,CAAC;QACL,OAAO,EAAE,eAAe;QACxB,KAAK,EAAE,KAAK,CAAC;QACb,IAAI,EAAE,KAAK,CAAC;QACZ,SAAS,WAAA;KACV,CAAC,EALI,CAKJ,EACF,CAAC,eAAe,EAAE,SAAS,CAAC,CAC7B,CAAC;IAEF,IAAM,gBAAgB,GAAG,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACrD,yBAAyB,CAAC;QACxB,mEAAmE;QACnE,2EAA2E;QAC3E,6DAA6D;QAC7D,yEAAyE;QACzE,2EAA2E;QAC3E,yEAAyE;QACzE,6EAA6E;QAC7E,gCAAgC;QAChC,gBAAgB,CAAC,OAAO,GAAG,aAAa,CAAC;IAC3C,CAAC,CAAC,CAAC;IAEH,IAAM,GAAG,GAAG,oBAAoB,CAC9B,KAAK,CAAC,WAAW,CACf,UAAC,MAAM;QACL,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,cAAO,CAAC,CAAC;QAClB,CAAC;QAED,IAAI,mBAAmB,GAAG,KAAK,CAAC;QAChC,IAAM,SAAS,GAAG,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;QAC1C,IAAM,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC,MAAM,CAAC;QACpC,IAAM,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC;YACxC,IAAI,YAAC,WAAW;;gBACd,IAAI,mBAAmB,EAAE,CAAC;oBACxB,OAAO;gBACT,CAAC;gBAED,IAAM,MAAM,GAAG;oBACb,OAAO,EAAE,KAAK;oBACd,iEAAiE;oBACjE,iCAAiC;oBACjC,IAAI,EAAE,WAAW,CAAC,IAAK;oBACvB,KAAK,EAAE,aAAa,CAAC,WAAW,CAAC;oBACjC,SAAS,WAAA;iBACV,CAAC;gBACF,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;gBAChC,IAAI,CAAC,gBAAgB,CAAC,OAAO;oBAAE,MAAM,EAAE,CAAC;gBAExC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBACjB,MAAA,MAAA,UAAU,CAAC,OAAO,EAAC,OAAO,mDAAG,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC7C,CAAC;qBAAM,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;oBACrC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;wBACxB,MAAM,QAAA;wBACN,IAAI,EAAE,MAAM;qBACb,CAAC,CAAC;gBACL,CAAC;qBAAM,IAAI,UAAU,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;oBACjD,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC;wBACpC,MAAM,QAAA;wBACN,gBAAgB,EAAE,MAAM;qBACzB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,KAAK,YAAC,KAAK;;gBACT,KAAK;oBACH,KAAK,YAAY,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CACrC,IAAI,WAAW,CAAC,EAAE,cAAc,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAC7C,CAAC;gBACJ,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBACzB,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;wBACtB,OAAO,EAAE,KAAK;wBACd,IAAI,EAAE,KAAK,CAAC;wBACZ,KAAK,OAAA;wBACL,SAAS,WAAA;qBACV,CAAC,CAAC;oBACH,IAAI,CAAC,gBAAgB,CAAC,OAAO;wBAAE,MAAM,EAAE,CAAC;oBACxC,MAAA,MAAA,UAAU,CAAC,OAAO,EAAC,OAAO,mDAAG,KAAK,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;YACD,QAAQ;gBACN,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBACzB,IAAI,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;wBAClC,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;oBAClC,CAAC;yBAAM,IAAI,UAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC;wBACrD,UAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC;oBAC9C,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC,CAAC;QAEH,OAAO;YACL,yEAAyE;YACzE,oEAAoE;YACpE,mEAAmE;YACnE,mBAAmB,GAAG,IAAI,CAAC;YAC3B,UAAU,CAAC;gBACT,YAAY,CAAC,WAAW,EAAE,CAAC;YAC7B,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;IACJ,CAAC,EACD,CAAC,UAAU,CAAC,CACb,EACD;QACE,OAAA,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC;YACrC,UAAU,CAAC,EAAE,CAAC,MAAM;YACtB,CAAC,CAAC,cAAc;IAFhB,CAEgB,EAClB,cAAM,OAAA,cAAc,EAAd,CAAc,CACrB,CAAC;IAEF,IAAM,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC;QAChC,SAAS,CACP,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EACxB,qDAAqD,CACtD,CAAC;QACF,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;IACvC,CAAC,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;IAE9B,OAAO,KAAK,CAAC,OAAO,CAAC,cAAM,OAAA,uBAAM,GAAG,KAAE,OAAO,SAAA,IAAG,EAArB,CAAqB,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AACpE,CAAC;AAED,SAAS,kBAAkB,CAIzB,MAAyB,EACzB,KAA2C,EAC3C,SAAiC,EACjC,WAAoC,EACpC,WAAoC,EACpC,OAAmC,EACnC,UAA2C;IAE3C,IAAM,OAAO,GAAG;QACd,KAAK,OAAA;QACL,SAAS,WAAA;QACT,WAAW,aAAA;QACX,WAAW,aAAA;QACX,OAAO,SAAA;QACP,UAAU,YAAA;KACX,CAAC;IACF,IAAM,EAAE,yBACH,OAAO,KACV,MAAM,QAAA,EACN,MAAM,EAAE;YACN,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,KAAK,CAAC;YACZ,KAAK,EAAE,KAAK,CAAC;YACb,SAAS,WAAA;SAC+B,EAC1C,SAAS,YAAC,MAA6C;YACrD,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,CAAC,GACF,CAAC;IAEF,IAAI,UAAU,GAAuD,IAAI,CAAC;IAC1E,OAAO,MAAM,CAAC,MAAM,CAClB,IAAI,UAAU,CAAkC,UAAC,QAAQ;QACvD,mEAAmE;QACnE,4BAA4B;QAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QACD,IAAM,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAC3C,OAAO,cAAM,OAAA,GAAG,CAAC,WAAW,EAAE,EAAjB,CAAiB,CAAC;IACjC,CAAC,CAAC,EACF;QACE;;WAEG;QACH,EAAE,IAAA;KACH,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport type { DocumentNode } from \"graphql\";\nimport type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\nimport { equal } from \"@wry/equality\";\n\nimport { DocumentType, verifyDocumentType } from \"../parser/index.js\";\nimport type {\n  NoInfer,\n  SubscriptionHookOptions,\n  SubscriptionResult,\n} from \"../types/types.js\";\nimport type {\n  ApolloClient,\n  DefaultContext,\n  ErrorPolicy,\n  FetchPolicy,\n  FetchResult,\n  OperationVariables,\n} from \"../../core/index.js\";\nimport { ApolloError, Observable } from \"../../core/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useDeepMemo } from \"./internal/useDeepMemo.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nimport { toApolloError } from \"./useQuery.js\";\nimport { useIsomorphicLayoutEffect } from \"./internal/useIsomorphicLayoutEffect.js\";\nimport type { MaybeMasked } from \"../../masking/index.js\";\n\n/**\n * > Refer to the [Subscriptions](https://www.apollographql.com/docs/react/data/subscriptions/) section for a more in-depth overview of `useSubscription`.\n *\n * @example\n * ```jsx\n * const COMMENTS_SUBSCRIPTION = gql`\n *   subscription OnCommentAdded($repoFullName: String!) {\n *     commentAdded(repoFullName: $repoFullName) {\n *       id\n *       content\n *     }\n *   }\n * `;\n *\n * function DontReadTheComments({ repoFullName }) {\n *   const {\n *     data: { commentAdded },\n *     loading,\n *   } = useSubscription(COMMENTS_SUBSCRIPTION, { variables: { repoFullName } });\n *   return <h4>New comment: {!loading && commentAdded.content}</h4>;\n * }\n * ```\n * @remarks\n * #### Consider using `onData` instead of `useEffect`\n *\n * If you want to react to incoming data, please use the `onData` option instead of `useEffect`.\n * State updates you make inside a `useEffect` hook might cause additional rerenders, and `useEffect` is mostly meant for side effects of rendering, not as an event handler.\n * State updates made in an event handler like `onData` might - depending on the React version - be batched and cause only a single rerender.\n *\n * Consider the following component:\n *\n * ```jsx\n * export function Subscriptions() {\n *   const { data, error, loading } = useSubscription(query);\n *   const [accumulatedData, setAccumulatedData] = useState([]);\n *\n *   useEffect(() => {\n *     setAccumulatedData((prev) => [...prev, data]);\n *   }, [data]);\n *\n *   return (\n *     <>\n *       {loading && <p>Loading...</p>}\n *       {JSON.stringify(accumulatedData, undefined, 2)}\n *     </>\n *   );\n * }\n * ```\n *\n * Instead of using `useEffect` here, we can re-write this component to use the `onData` callback function accepted in `useSubscription`'s `options` object:\n *\n * ```jsx\n * export function Subscriptions() {\n *   const [accumulatedData, setAccumulatedData] = useState([]);\n *   const { data, error, loading } = useSubscription(\n *     query,\n *     {\n *       onData({ data }) {\n *         setAccumulatedData((prev) => [...prev, data])\n *       }\n *     }\n *   );\n *\n *   return (\n *     <>\n *       {loading && <p>Loading...</p>}\n *       {JSON.stringify(accumulatedData, undefined, 2)}\n *     </>\n *   );\n * }\n * ```\n *\n * > ⚠️ **Note:** The `useSubscription` option `onData` is available in Apollo Client >= 3.7. In previous versions, the equivalent option is named `onSubscriptionData`.\n *\n * Now, the first message will be added to the `accumulatedData` array since `onData` is called _before_ the component re-renders. React 18 automatic batching is still in effect and results in a single re-render, but with `onData` we can guarantee each message received after the component mounts is added to `accumulatedData`.\n *\n * @since 3.0.0\n * @param subscription - A GraphQL subscription document parsed into an AST by `gql`.\n * @param options - Options to control how the subscription is executed.\n * @returns Query result object\n */\nexport function useSubscription<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  subscription: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: SubscriptionHookOptions<\n    NoInfer<TData>,\n    NoInfer<TVariables>\n  > = Object.create(null)\n) {\n  const hasIssuedDeprecationWarningRef = React.useRef(false);\n  const client = useApolloClient(options.client);\n  verifyDocumentType(subscription, DocumentType.Subscription);\n\n  if (!hasIssuedDeprecationWarningRef.current) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    hasIssuedDeprecationWarningRef.current = true;\n\n    if (options.onSubscriptionData) {\n      invariant.warn(\n        options.onData ?\n          \"'useSubscription' supports only the 'onSubscriptionData' or 'onData' option, but not both. Only the 'onData' option will be used.\"\n        : \"'onSubscriptionData' is deprecated and will be removed in a future major version. Please use the 'onData' option instead.\"\n      );\n    }\n\n    if (options.onSubscriptionComplete) {\n      invariant.warn(\n        options.onComplete ?\n          \"'useSubscription' supports only the 'onSubscriptionComplete' or 'onComplete' option, but not both. Only the 'onComplete' option will be used.\"\n        : \"'onSubscriptionComplete' is deprecated and will be removed in a future major version. Please use the 'onComplete' option instead.\"\n      );\n    }\n  }\n\n  const {\n    skip,\n    fetchPolicy,\n    errorPolicy,\n    shouldResubscribe,\n    context,\n    extensions,\n    ignoreResults,\n  } = options;\n  const variables = useDeepMemo(() => options.variables, [options.variables]);\n\n  const recreate = () =>\n    createSubscription(\n      client,\n      subscription,\n      variables,\n      fetchPolicy,\n      errorPolicy,\n      context,\n      extensions\n    );\n\n  let [observable, setObservable] = React.useState(\n    options.skip ? null : recreate\n  );\n\n  const recreateRef = React.useRef(recreate);\n  useIsomorphicLayoutEffect(() => {\n    recreateRef.current = recreate;\n  });\n\n  if (skip) {\n    if (observable) {\n      setObservable((observable = null));\n    }\n  } else if (\n    !observable ||\n    ((client !== observable.__.client ||\n      subscription !== observable.__.query ||\n      fetchPolicy !== observable.__.fetchPolicy ||\n      errorPolicy !== observable.__.errorPolicy ||\n      !equal(variables, observable.__.variables)) &&\n      (typeof shouldResubscribe === \"function\" ?\n        !!shouldResubscribe(options!)\n      : shouldResubscribe) !== false)\n  ) {\n    setObservable((observable = recreate()));\n  }\n\n  const optionsRef = React.useRef(options);\n  React.useEffect(() => {\n    optionsRef.current = options;\n  });\n\n  const fallbackLoading = !skip && !ignoreResults;\n  const fallbackResult = React.useMemo<SubscriptionResult<TData, TVariables>>(\n    () => ({\n      loading: fallbackLoading,\n      error: void 0,\n      data: void 0,\n      variables,\n    }),\n    [fallbackLoading, variables]\n  );\n\n  const ignoreResultsRef = React.useRef(ignoreResults);\n  useIsomorphicLayoutEffect(() => {\n    // We cannot reference `ignoreResults` directly in the effect below\n    // it would add a dependency to the `useEffect` deps array, which means the\n    // subscription would be recreated if `ignoreResults` changes\n    // As a result, on resubscription, the last result would be re-delivered,\n    // rendering the component one additional time, and re-triggering `onData`.\n    // The same applies to `fetchPolicy`, which results in a new `observable`\n    // being created. We cannot really avoid it in that case, but we can at least\n    // avoid it for `ignoreResults`.\n    ignoreResultsRef.current = ignoreResults;\n  });\n\n  const ret = useSyncExternalStore<SubscriptionResult<TData, TVariables>>(\n    React.useCallback(\n      (update) => {\n        if (!observable) {\n          return () => {};\n        }\n\n        let subscriptionStopped = false;\n        const variables = observable.__.variables;\n        const client = observable.__.client;\n        const subscription = observable.subscribe({\n          next(fetchResult) {\n            if (subscriptionStopped) {\n              return;\n            }\n\n            const result = {\n              loading: false,\n              // TODO: fetchResult.data can be null but SubscriptionResult.data\n              // expects TData | undefined only\n              data: fetchResult.data!,\n              error: toApolloError(fetchResult),\n              variables,\n            };\n            observable.__.setResult(result);\n            if (!ignoreResultsRef.current) update();\n\n            if (result.error) {\n              optionsRef.current.onError?.(result.error);\n            } else if (optionsRef.current.onData) {\n              optionsRef.current.onData({\n                client,\n                data: result,\n              });\n            } else if (optionsRef.current.onSubscriptionData) {\n              optionsRef.current.onSubscriptionData({\n                client,\n                subscriptionData: result,\n              });\n            }\n          },\n          error(error) {\n            error =\n              error instanceof ApolloError ? error : (\n                new ApolloError({ protocolErrors: [error] })\n              );\n            if (!subscriptionStopped) {\n              observable.__.setResult({\n                loading: false,\n                data: void 0,\n                error,\n                variables,\n              });\n              if (!ignoreResultsRef.current) update();\n              optionsRef.current.onError?.(error);\n            }\n          },\n          complete() {\n            if (!subscriptionStopped) {\n              if (optionsRef.current.onComplete) {\n                optionsRef.current.onComplete();\n              } else if (optionsRef.current.onSubscriptionComplete) {\n                optionsRef.current.onSubscriptionComplete();\n              }\n            }\n          },\n        });\n\n        return () => {\n          // immediately stop receiving subscription values, but do not unsubscribe\n          // until after a short delay in case another useSubscription hook is\n          // reusing the same underlying observable and is about to subscribe\n          subscriptionStopped = true;\n          setTimeout(() => {\n            subscription.unsubscribe();\n          });\n        };\n      },\n      [observable]\n    ),\n    () =>\n      observable && !skip && !ignoreResults ?\n        observable.__.result\n      : fallbackResult,\n    () => fallbackResult\n  );\n\n  const restart = React.useCallback(() => {\n    invariant(\n      !optionsRef.current.skip,\n      \"A subscription that is skipped cannot be restarted.\"\n    );\n    setObservable(recreateRef.current());\n  }, [optionsRef, recreateRef]);\n\n  return React.useMemo(() => ({ ...ret, restart }), [ret, restart]);\n}\n\nfunction createSubscription<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  client: ApolloClient<any>,\n  query: TypedDocumentNode<TData, TVariables>,\n  variables: TVariables | undefined,\n  fetchPolicy: FetchPolicy | undefined,\n  errorPolicy: ErrorPolicy | undefined,\n  context: DefaultContext | undefined,\n  extensions: Record<string, any> | undefined\n) {\n  const options = {\n    query,\n    variables,\n    fetchPolicy,\n    errorPolicy,\n    context,\n    extensions,\n  };\n  const __ = {\n    ...options,\n    client,\n    result: {\n      loading: true,\n      data: void 0,\n      error: void 0,\n      variables,\n    } as SubscriptionResult<TData, TVariables>,\n    setResult(result: SubscriptionResult<TData, TVariables>) {\n      __.result = result;\n    },\n  };\n\n  let observable: Observable<FetchResult<MaybeMasked<TData>>> | null = null;\n  return Object.assign(\n    new Observable<FetchResult<MaybeMasked<TData>>>((observer) => {\n      // lazily start the subscription when the first observer subscribes\n      // to get around strict mode\n      if (!observable) {\n        observable = client.subscribe(options);\n      }\n      const sub = observable.subscribe(observer);\n      return () => sub.unsubscribe();\n    }),\n    {\n      /**\n       * A tracking object to store details about the observable and the latest result of the subscription.\n       */\n      __,\n    }\n  );\n}\n"]}