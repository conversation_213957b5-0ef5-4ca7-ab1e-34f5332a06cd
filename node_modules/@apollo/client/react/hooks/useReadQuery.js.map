{"version": 3, "file": "useReadQuery.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useReadQuery.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AACjC,OAAO,EACL,qBAAqB,EACrB,iBAAiB,EACjB,cAAc,EACd,qBAAqB,GACtB,MAAM,sBAAsB,CAAC;AAE9B,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AACtD,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAOjE,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AA4BvD,MAAM,UAAU,YAAY,CAC1B,QAAyB;IAEzB,IAAM,SAAS,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;IAC3C,IAAM,gBAAgB,GAAG,eAAe,CACtC,SAAS,CAAC,CAAC;QACT,0EAA0E;QAC1E,iFAAiF;QAChF,SAAS,CAAC,YAAY,CAAS;QAClC,CAAC,CAAC,SAAS,CACkC,CAAC;IAEhD,OAAO,QAAQ,CACb,cAAc;IACd,yDAAyD;IACzD,aAAa,EACb,gBAAgB,CACjB,CAAC,QAAQ,CAAC,CAAC;AACd,CAAC;AAED,SAAS,aAAa,CACpB,QAAyB;IAEzB,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAChC,IAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CACpC,cAAM,OAAA,cAAc,CAAC,QAAQ,CAAC,EAAxB,CAAwB,EAC9B,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,IAAM,UAAU,GAAG,KAAK,CAAC,WAAW,CAClC,cAAM,OAAA,iBAAiB,CAAC,QAAQ,CAAC,EAA3B,CAA2B,EACjC,CAAC,QAAQ,CAAC,CACX,CAAC;IAEF,IAAI,gBAAgB,CAAC,QAAQ,EAAE,CAAC;QAC9B,gBAAgB,CAAC,YAAY,EAAE,CAAC;QAChC,qBAAqB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC5D,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,cAAM,OAAA,gBAAgB,CAAC,MAAM,EAAE,EAAzB,CAAyB,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAErE,IAAM,OAAO,GAAG,oBAAoB,CAClC,KAAK,CAAC,WAAW,CACf,UAAC,WAAW;QACV,OAAO,gBAAgB,CAAC,MAAM,CAAC,UAAC,OAAO;YACrC,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACzC,WAAW,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,EACD,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAC7B,EACD,UAAU,EACV,UAAU,CACX,CAAC;IAEF,IAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;IAE9B,OAAO,KAAK,CAAC,OAAO,CAAC;QACnB,OAAO;YACL,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,KAAK,EAAE,aAAa,CAAC,MAAM,CAAC;SAC7B,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACf,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport {\n  assertWrappedQueryRef,\n  getWrappedPromise,\n  unwrapQueryRef,\n  updateWrappedQueryRef,\n} from \"../internal/index.js\";\nimport type { QueryRef } from \"../internal/index.js\";\nimport { __use, wrapHook } from \"./internal/index.js\";\nimport { toApolloError } from \"./useSuspenseQuery.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nimport type { ApolloError } from \"../../errors/index.js\";\nimport type {\n  ApolloClient,\n  NetworkStatus,\n  ObservableQuery,\n} from \"../../core/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport type { MaybeMasked } from \"../../masking/index.js\";\n\nexport interface UseReadQueryResult<TData = unknown> {\n  /**\n   * An object containing the result of your GraphQL query after it completes.\n   *\n   * This value might be `undefined` if a query results in one or more errors\n   * (depending on the query's `errorPolicy`).\n   */\n  data: MaybeMasked<TData>;\n  /**\n   * If the query produces one or more errors, this object contains either an\n   * array of `graphQLErrors` or a single `networkError`. Otherwise, this value\n   * is `undefined`.\n   *\n   * This property can be ignored when using the default `errorPolicy` or an\n   * `errorPolicy` of `none`. The hook will throw the error instead of setting\n   * this property.\n   */\n  error: ApolloError | undefined;\n  /**\n   * A number indicating the current network state of the query's associated\n   * request. {@link https://github.com/apollographql/apollo-client/blob/d96f4578f89b933c281bb775a39503f6cdb59ee8/src/core/networkStatus.ts#L4 | See possible values}.\n   */\n  networkStatus: NetworkStatus;\n}\n\nexport function useReadQuery<TData>(\n  queryRef: QueryRef<TData>\n): UseReadQueryResult<TData> {\n  const unwrapped = unwrapQueryRef(queryRef);\n  const clientOrObsQuery = useApolloClient(\n    unwrapped ?\n      // passing an `ObservableQuery` is not supported by the types, but it will\n      // return any truthy value that is passed in as an override so we cast the result\n      (unwrapped[\"observable\"] as any)\n    : undefined\n  ) as ApolloClient<any> | ObservableQuery<TData>;\n\n  return wrapHook(\n    \"useReadQuery\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useReadQuery_,\n    clientOrObsQuery\n  )(queryRef);\n}\n\nfunction useReadQuery_<TData>(\n  queryRef: QueryRef<TData>\n): UseReadQueryResult<TData> {\n  assertWrappedQueryRef(queryRef);\n  const internalQueryRef = React.useMemo(\n    () => unwrapQueryRef(queryRef),\n    [queryRef]\n  );\n\n  const getPromise = React.useCallback(\n    () => getWrappedPromise(queryRef),\n    [queryRef]\n  );\n\n  if (internalQueryRef.disposed) {\n    internalQueryRef.reinitialize();\n    updateWrappedQueryRef(queryRef, internalQueryRef.promise);\n  }\n\n  React.useEffect(() => internalQueryRef.retain(), [internalQueryRef]);\n\n  const promise = useSyncExternalStore(\n    React.useCallback(\n      (forceUpdate) => {\n        return internalQueryRef.listen((promise) => {\n          updateWrappedQueryRef(queryRef, promise);\n          forceUpdate();\n        });\n      },\n      [internalQueryRef, queryRef]\n    ),\n    getPromise,\n    getPromise\n  );\n\n  const result = __use(promise);\n\n  return React.useMemo(() => {\n    return {\n      data: result.data,\n      networkStatus: result.networkStatus,\n      error: toApolloError(result),\n    };\n  }, [result]);\n}\n"]}