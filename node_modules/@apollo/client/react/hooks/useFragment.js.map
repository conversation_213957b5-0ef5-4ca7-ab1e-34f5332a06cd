{"version": 3, "file": "useFragment.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useFragment.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAEjC,OAAO,EAAE,cAAc,EAAE,MAAM,0BAA0B,CAAC;AAQ1D,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAGjE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AAC5D,OAAO,KAAK,MAAM,eAAe,CAAC;AAsClC,MAAM,UAAU,WAAW,CACzB,OAAyC;IAEzC,OAAO,QAAQ,CACb,aAAa;IACb,yDAAyD;IACzD,YAAY,EACZ,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAChC,CAAC,OAAO,CAAC,CAAC;AACb,CAAC;AAED,SAAS,YAAY,CACnB,OAAyC;IAEzC,IAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACvC,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IACjB,IAAA,IAAI,GAAc,OAAO,KAArB,EAAK,IAAI,UAAK,OAAO,EAA3B,QAAiB,CAAF,CAAa;IAElC,6EAA6E;IAC7E,kEAAkE;IAClE,0EAA0E;IAC1E,8EAA8E;IAC9E,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,CACtB;QACE,OAAA,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI;YAC/B,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI;gBACtB,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;IAFtB,CAEsB,EACxB,CAAC,KAAK,EAAE,IAAI,CAAC,CACd,CAAC;IAEF,IAAM,aAAa,GAAG,WAAW,CAAC,cAAM,OAAA,uBAAM,IAAI,KAAE,IAAI,EAAE,EAAG,IAAG,EAAxB,CAAwB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;IAE9E,qDAAqD;IACrD,gEAAgE;IAChE,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC;QACjB,IAAA,QAAQ,GAA4C,aAAa,SAAzD,EAAE,YAAY,GAA8B,aAAa,aAA3C,EAAE,IAAI,GAAwB,aAAa,KAArC,EAAE,KAAsB,aAAa,WAAlB,EAAjB,UAAU,mBAAG,IAAI,KAAA,CAAmB;QAE1E,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YAClB,OAAO;gBACL,MAAM,EAAE,YAAY,CAAC;oBACnB,MAAM,EAAE,EAAW;oBACnB,QAAQ,EAAE,KAAK;iBAChB,CAAC;aACH,CAAC;QACJ,CAAC;QAEO,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;QACzB,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,uBAClB,aAAa,KAChB,iBAAiB,EAAE,IAAI,EACvB,EAAE,EAAE,IAAI,EACR,KAAK,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE,YAAY,CAAC,EACtD,UAAU,YAAA,IACV,CAAC;QAEH,OAAO;YACL,MAAM,EAAE,YAAY,uBACf,IAAI,KACP,MAAM,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC;oBAC1C,QAAQ,UAAA;oBACR,YAAY,cAAA;oBACZ,IAAI,EAAE,IAAI,CAAC,MAAM;iBAClB,CAAC,IACF;SACH,CAAC;IACJ,CAAC,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;IAE5B,kDAAkD;IAClD,IAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,cAAM,OAAA,IAAI,CAAC,MAAM,EAAX,CAAW,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;IAEjE,OAAO,oBAAoB,CACzB,KAAK,CAAC,WAAW,CACf,UAAC,WAAW;QACV,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,IAAM,YAAY,GAChB,aAAa,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;YAC3B,IAAI;YACN,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC;gBAC5C,IAAI,EAAE,UAAC,MAAM;oBACX,mEAAmE;oBACnE,6DAA6D;oBAC7D,sDAAsD;oBACtD,iBAAiB;oBACjB,IAAI,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC;wBAAE,OAAO;oBACvC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;oBACrB,iEAAiE;oBACjE,kEAAkE;oBAClE,6DAA6D;oBAC7D,qEAAqE;oBACrE,YAAY,CAAC,WAAW,CAAC,CAAC;oBAC1B,WAAW,GAAG,UAAU,CAAC,WAAW,CAAQ,CAAC;gBAC/C,CAAC;aACF,CAAC,CAAC;QACP,OAAO;YACL,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,WAAW,EAAE,CAAC;YAC5B,YAAY,CAAC,WAAW,CAAC,CAAC;QAC5B,CAAC,CAAC;IACJ,CAAC,EACD,CAAC,MAAM,EAAE,aAAa,EAAE,IAAI,CAAC,CAC9B,EACD,WAAW,EACX,WAAW,CACZ,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CACnB,IAA6B;IAE7B,IAAM,MAAM,GAAG;QACb,IAAI,EAAE,IAAI,CAAC,MAAO;QAClB,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;KACE,CAAC;IAE9B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,MAAM,CAAC,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,OAAO,EAAb,CAAa,CAAC,CAAC,CAAC;IAC9E,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport type { DeepPartial } from \"../../utilities/index.js\";\nimport { mergeDeepArray } from \"../../utilities/index.js\";\nimport type {\n  Cache,\n  Reference,\n  StoreObject,\n  MissingTree,\n} from \"../../cache/index.js\";\n\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nimport type { ApolloClient, OperationVariables } from \"../../core/index.js\";\nimport type { NoInfer } from \"../types/types.js\";\nimport { useDeepMemo, wrapHook } from \"./internal/index.js\";\nimport equal from \"@wry/equality\";\nimport type { FragmentType, MaybeMasked } from \"../../masking/index.js\";\n\nexport interface UseFragmentOptions<TData, TVars>\n  extends Omit<\n      Cache.DiffOptions<NoInfer<TData>, NoInfer<TVars>>,\n      \"id\" | \"query\" | \"optimistic\" | \"previousResult\" | \"returnPartialData\"\n    >,\n    Omit<\n      Cache.ReadFragmentOptions<TData, TVars>,\n      \"id\" | \"variables\" | \"returnPartialData\"\n    > {\n  from: StoreObject | Reference | FragmentType<NoInfer<TData>> | string | null;\n  // Override this field to make it optional (default: true).\n  optimistic?: boolean;\n  /**\n   * The instance of `ApolloClient` to use to look up the fragment.\n   *\n   * By default, the instance that's passed down via context is used, but you\n   * can provide a different instance here.\n   *\n   * @docGroup 1. Operation options\n   */\n  client?: ApolloClient<any>;\n}\n\nexport type UseFragmentResult<TData> =\n  | {\n      data: MaybeMasked<TData>;\n      complete: true;\n      missing?: never;\n    }\n  | {\n      data: DeepPartial<MaybeMasked<TData>>;\n      complete: false;\n      missing?: MissingTree;\n    };\n\nexport function useFragment<TData = any, TVars = OperationVariables>(\n  options: UseFragmentOptions<TData, TVars>\n): UseFragmentResult<TData> {\n  return wrapHook(\n    \"useFragment\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useFragment_,\n    useApolloClient(options.client)\n  )(options);\n}\n\nfunction useFragment_<TData = any, TVars = OperationVariables>(\n  options: UseFragmentOptions<TData, TVars>\n): UseFragmentResult<TData> {\n  const client = useApolloClient(options.client);\n  const { cache } = client;\n  const { from, ...rest } = options;\n\n  // We calculate the cache id seperately from `stableOptions` because we don't\n  // want changes to non key fields in the `from` property to affect\n  // `stableOptions` and retrigger our subscription. If the cache identifier\n  // stays the same between renders, we want to reuse the existing subscription.\n  const id = React.useMemo(\n    () =>\n      typeof from === \"string\" ? from\n      : from === null ? null\n      : cache.identify(from),\n    [cache, from]\n  );\n\n  const stableOptions = useDeepMemo(() => ({ ...rest, from: id! }), [rest, id]);\n\n  // Since .next is async, we need to make sure that we\n  // get the correct diff on the next render given new diffOptions\n  const diff = React.useMemo(() => {\n    const { fragment, fragmentName, from, optimistic = true } = stableOptions;\n\n    if (from === null) {\n      return {\n        result: diffToResult({\n          result: {} as TData,\n          complete: false,\n        }),\n      };\n    }\n\n    const { cache } = client;\n    const diff = cache.diff<TData>({\n      ...stableOptions,\n      returnPartialData: true,\n      id: from,\n      query: cache[\"getFragmentDoc\"](fragment, fragmentName),\n      optimistic,\n    });\n\n    return {\n      result: diffToResult({\n        ...diff,\n        result: client[\"queryManager\"].maskFragment({\n          fragment,\n          fragmentName,\n          data: diff.result,\n        }),\n      }),\n    };\n  }, [client, stableOptions]);\n\n  // Used for both getSnapshot and getServerSnapshot\n  const getSnapshot = React.useCallback(() => diff.result, [diff]);\n\n  return useSyncExternalStore(\n    React.useCallback(\n      (forceUpdate) => {\n        let lastTimeout = 0;\n\n        const subscription =\n          stableOptions.from === null ?\n            null\n          : client.watchFragment(stableOptions).subscribe({\n              next: (result) => {\n                // Since `next` is called async by zen-observable, we want to avoid\n                // unnecessarily rerendering this hook for the initial result\n                // emitted from watchFragment which should be equal to\n                // `diff.result`.\n                if (equal(result, diff.result)) return;\n                diff.result = result;\n                // If we get another update before we've re-rendered, bail out of\n                // the update and try again. This ensures that the relative timing\n                // between useQuery and useFragment stays roughly the same as\n                // fixed in https://github.com/apollographql/apollo-client/pull/11083\n                clearTimeout(lastTimeout);\n                lastTimeout = setTimeout(forceUpdate) as any;\n              },\n            });\n        return () => {\n          subscription?.unsubscribe();\n          clearTimeout(lastTimeout);\n        };\n      },\n      [client, stableOptions, diff]\n    ),\n    getSnapshot,\n    getSnapshot\n  );\n}\n\nfunction diffToResult<TData>(\n  diff: Cache.DiffResult<TData>\n): UseFragmentResult<TData> {\n  const result = {\n    data: diff.result!,\n    complete: !!diff.complete,\n  } as UseFragmentResult<TData>;\n\n  if (diff.missing) {\n    result.missing = mergeDeepArray(diff.missing.map((error) => error.missing));\n  }\n\n  return result;\n}\n"]}