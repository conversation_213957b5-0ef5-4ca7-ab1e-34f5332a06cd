{"version": 3, "file": "useApolloClient.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useApolloClient.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAC7D,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAEjC,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAEvD;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,eAAe,CAC7B,QAA+B;IAE/B,IAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC,CAAC;IACrD,IAAM,MAAM,GAAG,QAAQ,IAAI,OAAO,CAAC,MAAM,CAAC;IAC1C,SAAS,CACP,CAAC,CAAC,MAAM,EACR,oEAAoE;QAClE,0EAA0E;QAC1E,0BAA0B,CAC7B,CAAC;IAEF,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport type { ApolloClient } from \"../../core/index.js\";\nimport { getApolloContext } from \"../context/index.js\";\n\n/**\n * @example\n * ```jsx\n * import { useApolloClient } from '@apollo/client';\n *\n * function SomeComponent() {\n *   const client = useApolloClient();\n *   // `client` is now set to the `ApolloClient` instance being used by the\n *   // application (that was configured using something like `ApolloProvider`)\n * }\n * ```\n *\n * @since 3.0.0\n * @returns The `ApolloClient` instance being used by the application.\n */\nexport function useApolloClient(\n  override?: ApolloClient<object>\n): ApolloClient<object> {\n  const context = React.useContext(getApolloContext());\n  const client = override || context.client;\n  invariant(\n    !!client,\n    'Could not find \"client\" in the context or passed in as an option. ' +\n      \"Wrap the root component in an <ApolloProvider>, or pass an ApolloClient \" +\n      \"instance in via options.\"\n  );\n\n  return client;\n}\n"]}