{"version": 3, "file": "useSuspenseFragment.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useSuspenseFragment.ts"], "names": [], "mappings": ";AAQA,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAC;AACxD,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAEjC,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAC5C,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAC;AA6C/C,IAAM,gBAAgB,GAAG,EAGxB,CAAC;AAoCF,MAAM,UAAU,mBAAmB,CAIjC,OAAsD;IAEtD,OAAO,QAAQ,CACb,qBAAqB;IACrB,yDAAyD;IACzD,oBAAoB,EACpB,eAAe,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAC1E,CAAC,OAAO,CAAC,CAAC;AACb,CAAC;AAED,SAAS,oBAAoB,CAI3B,OAAsD;IAEtD,IAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACvC,IAAA,IAAI,GAAgB,OAAO,KAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;IAC5B,IAAA,KAAK,GAAK,MAAM,MAAX,CAAY;IAEzB,IAAM,EAAE,GAAG,KAAK,CAAC,OAAO,CACtB;QACE,OAAA,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI;YAC/B,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI;gBACtB,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;IAFtB,CAEsB,EACxB,CAAC,KAAK,EAAE,IAAI,CAAC,CACG,CAAC;IAEnB,IAAM,WAAW,GACf,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CACnB,gBAAgB,CAAC,MAAM,CAAC,CAAC,cAAc,CACrC,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC,EACrD,MAAM,wBACD,OAAO,KAAE,SAAS,EAAE,SAAuB,EAAE,IAAI,EAAE,EAAE,IAC3D,CACF,CAAC;IAEA,IAAA,KAAwB,KAAK,CAAC,QAAQ,CAGxC,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,CACxC,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,OAAO,CAAC,CACvC,CACF,EANI,OAAO,QAAA,EAAE,UAAU,QAMvB,CAAC;IAEF,KAAK,CAAC,SAAS,CAAC;QACd,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;YACzB,OAAO;QACT,CAAC;QAED,IAAM,OAAO,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;QACrC,IAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,UAAC,OAAO;YAChD,UAAU,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,CAAC;YACV,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACxB,CAAC;IAED,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,GAAG,EAAE,CAAC;QACnC,yDAAyD;QACzD,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC;QAC7B,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;IACnC,CAAC;IAED,IAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAE/B,OAAO,EAAE,IAAI,MAAA,EAAE,CAAC;AAClB,CAAC", "sourcesContent": ["import type {\n  ApolloClient,\n  DocumentNode,\n  OperationVariables,\n  Reference,\n  StoreObject,\n  TypedDocumentNode,\n} from \"../../core/index.js\";\nimport { canonicalStringify } from \"../../cache/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { getSuspenseCache } from \"../internal/index.js\";\nimport * as React from \"rehackt\";\nimport type { FragmentKey } from \"../internal/cache/types.js\";\nimport { __use } from \"./internal/__use.js\";\nimport { wrapHook } from \"./internal/index.js\";\nimport type { FragmentType, MaybeMasked } from \"../../masking/index.js\";\nimport type { NoInfer, VariablesOption } from \"../types/types.js\";\n\ntype From<TData> =\n  | StoreObject\n  | Reference\n  | FragmentType<NoInfer<TData>>\n  | string\n  | null;\n\nexport type UseSuspenseFragmentOptions<\n  TData,\n  TVariables extends OperationVariables,\n> = {\n  /**\n   * A GraphQL document created using the `gql` template string tag from\n   * `graphql-tag` with one or more fragments which will be used to determine\n   * the shape of data to read. If you provide more than one fragment in this\n   * document then you must also specify `fragmentName` to select a single.\n   */\n  fragment: DocumentNode | TypedDocumentNode<TData, TVariables>;\n\n  /**\n   * The name of the fragment in your GraphQL document to be used. If you do\n   * not provide a `fragmentName` and there is only one fragment in your\n   * `fragment` document then that fragment will be used.\n   */\n  fragmentName?: string;\n  from: From<TData>;\n  // Override this field to make it optional (default: true).\n  optimistic?: boolean;\n  /**\n   * The instance of `ApolloClient` to use to look up the fragment.\n   *\n   * By default, the instance that's passed down via context is used, but you\n   * can provide a different instance here.\n   *\n   * @docGroup 1. Operation options\n   */\n  client?: ApolloClient<any>;\n} & VariablesOption<NoInfer<TVariables>>;\n\nexport type UseSuspenseFragmentResult<TData> = { data: MaybeMasked<TData> };\n\nconst NULL_PLACEHOLDER = [] as unknown as [\n  FragmentKey,\n  Promise<MaybeMasked<any> | null>,\n];\n\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: UseSuspenseFragmentOptions<TData, TVariables> & {\n    from: NonNullable<From<TData>>;\n  }\n): UseSuspenseFragmentResult<TData>;\n\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: UseSuspenseFragmentOptions<TData, TVariables> & {\n    from: null;\n  }\n): UseSuspenseFragmentResult<null>;\n\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: UseSuspenseFragmentOptions<TData, TVariables> & {\n    from: From<TData>;\n  }\n): UseSuspenseFragmentResult<TData | null>;\n\nexport function useSuspenseFragment<\n  TData,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: UseSuspenseFragmentOptions<TData, TVariables>\n): UseSuspenseFragmentResult<TData>;\n\nexport function useSuspenseFragment<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: UseSuspenseFragmentOptions<TData, TVariables>\n): UseSuspenseFragmentResult<TData | null> {\n  return wrapHook(\n    \"useSuspenseFragment\",\n    // eslint-disable-next-line react-compiler/react-compiler\n    useSuspenseFragment_,\n    useApolloClient(typeof options === \"object\" ? options.client : undefined)\n  )(options);\n}\n\nfunction useSuspenseFragment_<\n  TData = unknown,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  options: UseSuspenseFragmentOptions<TData, TVariables>\n): UseSuspenseFragmentResult<TData | null> {\n  const client = useApolloClient(options.client);\n  const { from, variables } = options;\n  const { cache } = client;\n\n  const id = React.useMemo(\n    () =>\n      typeof from === \"string\" ? from\n      : from === null ? null\n      : cache.identify(from),\n    [cache, from]\n  ) as string | null;\n\n  const fragmentRef =\n    id === null ? null : (\n      getSuspenseCache(client).getFragmentRef(\n        [id, options.fragment, canonicalStringify(variables)],\n        client,\n        { ...options, variables: variables as TVariables, from: id }\n      )\n    );\n\n  let [current, setPromise] = React.useState<\n    [FragmentKey, Promise<MaybeMasked<TData> | null>]\n  >(\n    fragmentRef === null ? NULL_PLACEHOLDER : (\n      [fragmentRef.key, fragmentRef.promise]\n    )\n  );\n\n  React.useEffect(() => {\n    if (fragmentRef === null) {\n      return;\n    }\n\n    const dispose = fragmentRef.retain();\n    const removeListener = fragmentRef.listen((promise) => {\n      setPromise([fragmentRef.key, promise]);\n    });\n\n    return () => {\n      dispose();\n      removeListener();\n    };\n  }, [fragmentRef]);\n\n  if (fragmentRef === null) {\n    return { data: null };\n  }\n\n  if (current[0] !== fragmentRef.key) {\n    // eslint-disable-next-line react-compiler/react-compiler\n    current[0] = fragmentRef.key;\n    current[1] = fragmentRef.promise;\n  }\n\n  const data = __use(current[1]);\n\n  return { data };\n}\n"]}