{"version": 3, "file": "hooks.cjs", "sources": ["useApolloClient.js", "useSyncExternalStore.js", "internal/useDeepMemo.js", "internal/useIsomorphicLayoutEffect.js", "internal/useRenderGuard.js", "internal/__use.js", "internal/wrapHook.js", "useQuery.js", "useLazyQuery.js", "useMutation.js", "useSubscription.js", "useReactiveVar.js", "useFragment.js", "constants.js", "useSuspenseQuery.js", "useBackgroundQuery.js", "useSuspenseFragment.js", "useLoadableQuery.js", "useQueryRefHandlers.js", "useReadQuery.js"], "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport { getApolloContext } from \"../context/index.js\";\n/**\n * @example\n * ```jsx\n * import { useApolloClient } from '@apollo/client';\n *\n * function SomeComponent() {\n *   const client = useApolloClient();\n *   // `client` is now set to the `ApolloClient` instance being used by the\n *   // application (that was configured using something like `ApolloProvider`)\n * }\n * ```\n *\n * @since 3.0.0\n * @returns The `ApolloClient` instance being used by the application.\n */\nexport function useApolloClient(override) {\n    var context = React.useContext(getApolloContext());\n    var client = override || context.client;\n    invariant(!!client, 58);\n    return client;\n}\n//# sourceMappingURL=useApolloClient.js.map", "import { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport { canUseLayoutEffect } from \"../../utilities/index.js\";\nvar didWarnUncachedGetSnapshot = false;\n// Prevent webpack from complaining about our feature detection of the\n// useSyncExternalStore property of the React namespace, which is expected not\n// to exist when using React 17 and earlier, and that's fine.\nvar uSESKey = \"useSyncExternalStore\";\nvar realHook = React[uSESKey];\n// Adapted from https://www.npmjs.com/package/use-sync-external-store, with\n// Apollo Client deviations called out by \"// DEVIATION ...\" comments.\n// When/if React.useSyncExternalStore is defined, delegate fully to it.\nexport var useSyncExternalStore = realHook ||\n    (function (subscribe, getSnapshot, getServerSnapshot) {\n        // Read the current snapshot from the store on every render. Again, this\n        // breaks the rules of React, and only works here because of specific\n        // implementation details, most importantly that updates are\n        // always synchronous.\n        var value = getSnapshot();\n        if (\n        // DEVIATION: Using __DEV__\n        globalThis.__DEV__ !== false &&\n            !didWarnUncachedGetSnapshot &&\n            // DEVIATION: Not using Object.is because we know our snapshots will never\n            // be exotic primitive values like NaN, which is !== itself.\n            value !== getSnapshot()) {\n            didWarnUncachedGetSnapshot = true;\n            // DEVIATION: Using invariant.error instead of console.error directly.\n            globalThis.__DEV__ !== false && invariant.error(68);\n        }\n        // Because updates are synchronous, we don't queue them. Instead we force a\n        // re-render whenever the subscribed state changes by updating an some\n        // arbitrary useState hook. Then, during render, we call getSnapshot to read\n        // the current value.\n        //\n        // Because we don't actually use the state returned by the useState hook, we\n        // can save a bit of memory by storing other stuff in that slot.\n        //\n        // To implement the early bailout, we need to track some things on a mutable\n        // object. Usually, we would put that in a useRef hook, but we can stash it in\n        // our useState hook instead.\n        //\n        // To force a re-render, we call forceUpdate({inst}). That works because the\n        // new object always fails an equality check.\n        var _a = React.useState({\n            inst: { value: value, getSnapshot: getSnapshot },\n        }), inst = _a[0].inst, forceUpdate = _a[1];\n        // Track the latest getSnapshot function with a ref. This needs to be updated\n        // in the layout phase so we can access it during the tearing check that\n        // happens on subscribe.\n        if (canUseLayoutEffect) {\n            // DEVIATION: We avoid calling useLayoutEffect when !canUseLayoutEffect,\n            // which may seem like a conditional hook, but this code ends up behaving\n            // unconditionally (one way or the other) because canUseLayoutEffect is\n            // constant.\n            React.useLayoutEffect(function () {\n                Object.assign(inst, { value: value, getSnapshot: getSnapshot });\n                // Whenever getSnapshot or subscribe changes, we need to check in the\n                // commit phase if there was an interleaved mutation. In concurrent mode\n                // this can happen all the time, but even in synchronous mode, an earlier\n                // effect may have mutated the store.\n                if (checkIfSnapshotChanged(inst)) {\n                    // Force a re-render.\n                    forceUpdate({ inst: inst });\n                }\n                // React Hook React.useLayoutEffect has a missing dependency: 'inst'. Either include it or remove the dependency array.\n                // eslint-disable-next-line react-hooks/exhaustive-deps\n            }, [subscribe, value, getSnapshot]);\n        }\n        else {\n            Object.assign(inst, { value: value, getSnapshot: getSnapshot });\n        }\n        React.useEffect(function () {\n            // Check for changes right before subscribing. Subsequent changes will be\n            // detected in the subscription handler.\n            if (checkIfSnapshotChanged(inst)) {\n                // Force a re-render.\n                forceUpdate({ inst: inst });\n            }\n            // Subscribe to the store and return a clean-up function.\n            return subscribe(function handleStoreChange() {\n                // TODO: Because there is no cross-renderer API for batching updates, it's\n                // up to the consumer of this library to wrap their subscription event\n                // with unstable_batchedUpdates. Should we try to detect when this isn't\n                // the case and print a warning in development?\n                // The store changed. Check if the snapshot changed since the last time we\n                // read from the store.\n                if (checkIfSnapshotChanged(inst)) {\n                    // Force a re-render.\n                    forceUpdate({ inst: inst });\n                }\n            });\n            // React Hook React.useEffect has a missing dependency: 'inst'. Either include it or remove the dependency array.\n            // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [subscribe]);\n        return value;\n    });\nfunction checkIfSnapshotChanged(_a) {\n    var value = _a.value, getSnapshot = _a.getSnapshot;\n    try {\n        return value !== getSnapshot();\n    }\n    catch (_b) {\n        return true;\n    }\n}\n//# sourceMappingURL=useSyncExternalStore.js.map", "import * as React from \"rehackt\";\nimport { equal } from \"@wry/equality\";\nexport function useDeepMemo(memoFn, deps) {\n    var ref = React.useRef(void 0);\n    if (!ref.current || !equal(ref.current.deps, deps)) {\n        // eslint-disable-next-line react-compiler/react-compiler\n        ref.current = { value: memoFn(), deps: deps };\n    }\n    return ref.current.value;\n}\n//# sourceMappingURL=useDeepMemo.js.map", "import * as React from \"rehackt\";\nimport { canUseDOM } from \"../../../utilities/index.js\";\n// use canUseDOM here instead of canUseLayoutEffect because we want to be able\n// to use useLayoutEffect in our jest tests. useLayoutEffect seems to work fine\n// in useSuspenseQuery tests, but to honor the original comment about the\n// warnings for useSyncExternalStore implementation, canUseLayoutEffect is left\n// alone.\nexport var useIsomorphicLayoutEffect = canUseDOM ? React.useLayoutEffect : React.useEffect;\n//# sourceMappingURL=useIsomorphicLayoutEffect.js.map", "import * as React from \"rehackt\";\nvar Ctx;\nfunction noop() { }\nexport function useRenderGuard() {\n    if (!Ctx) {\n        // we want the intialization to be lazy because `createContext` would error on import in a RSC\n        Ctx = React.createContext(null);\n    }\n    return React.useCallback(\n    /**\n     * @returns true if the hook was called during render\n     */ function () {\n        var orig = console.error;\n        try {\n            console.error = noop;\n            /**\n             * `useContext` can be called conditionally during render, so this is safe.\n             * (Also, during render we would want to throw as a reaction to this anyways, so it\n             * wouldn't even matter if we got the order of hooks mixed up...)\n             *\n             * They cannot however be called outside of Render, and that's what we're testing here.\n             *\n             * Different versions of React have different behaviour on an invalid hook call:\n             *\n             * React 16.8 - 17: throws an error\n             * https://github.com/facebook/react/blob/2b93d686e359c7afa299e2ec5cf63160a32a1155/packages/react/src/ReactHooks.js#L18-L26\n             *\n             * React 18 & 19: `console.error` in development, then `resolveDispatcher` returns `null` and a member access on `null` throws.\n             * https://github.com/facebook/react/blob/58e8304483ebfadd02a295339b5e9a989ac98c6e/packages/react/src/ReactHooks.js#L28-L35\n             */\n            React[\"useContext\" /* hide this from the linter */](Ctx);\n            return true;\n        }\n        catch (e) {\n            return false;\n        }\n        finally {\n            console.error = orig;\n        }\n    }, []);\n}\n//# sourceMappingURL=useRenderGuard.js.map", "import { wrapPromiseWithState } from \"../../../utilities/index.js\";\nimport * as React from \"rehackt\";\n// Prevent webpack from complaining about our feature detection of the\n// use property of the React namespace, which is expected not\n// to exist when using current stable versions, and that's fine.\nvar useKey = \"use\";\nvar realHook = React[useKey];\n// This is named with two underscores to allow this hook to evade typical rules of\n// hooks (i.e. it can be used conditionally)\nexport var __use = realHook ||\n    function __use(promise) {\n        var statefulPromise = wrapPromiseWithState(promise);\n        switch (statefulPromise.status) {\n            case \"pending\":\n                throw statefulPromise;\n            case \"rejected\":\n                throw statefulPromise.reason;\n            case \"fulfilled\":\n                return statefulPromise.value;\n        }\n    };\n//# sourceMappingURL=__use.js.map", "var wrapperSymbol = Symbol.for(\"apollo.hook.wrappers\");\n/**\n * @internal\n *\n * Makes an Apollo Client hook \"wrappable\".\n * That means that the Apollo Client instance can expose a \"wrapper\" that will be\n * used to wrap the original hook implementation with additional logic.\n * @example\n * ```tsx\n * // this is already done in `@apollo/client` for all wrappable hooks (see `WrappableHooks`)\n * // following this pattern\n * function useQuery() {\n *   return wrapHook('useQuery', _useQuery, options.client)(query, options);\n * }\n * function _useQuery(query, options) {\n *   // original implementation\n * }\n *\n * // this is what a library like `@apollo/client-react-streaming` would do\n * class ApolloClientWithStreaming extends ApolloClient {\n *   constructor(options) {\n *     super(options);\n *     this.queryManager[Symbol.for(\"apollo.hook.wrappers\")] = {\n *       useQuery: (original) => (query, options) => {\n *         console.log(\"useQuery was called with options\", options);\n *         return original(query, options);\n *       }\n *     }\n *   }\n * }\n *\n * // this will now log the options and then call the original `useQuery`\n * const client = new ApolloClientWithStreaming({ ... });\n * useQuery(query, { client });\n * ```\n */\nexport function wrapHook(hookName, useHook, clientOrObsQuery) {\n    var queryManager = clientOrObsQuery[\"queryManager\"];\n    var wrappers = queryManager && queryManager[wrapperSymbol];\n    var wrapper = wrappers && wrappers[hookName];\n    return wrapper ? wrapper(useHook) : useHook;\n}\n//# sourceMappingURL=wrapHook.js.map", "import { __assign, __rest } from \"tslib\";\n/**\n * Function parameters in this file try to follow a common order for the sake of\n * readability and consistency. The order is as follows:\n *\n * resultData\n * observable\n * client\n * query\n * options\n * watchQueryOptions\n * makeWatchQueryOptions\n * isSSRAllowed\n * disableNetworkFetches\n * partialRefetch\n * renderPromises\n * isSyncSSR\n * callbacks\n */\n/** */\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nimport { equal } from \"@wry/equality\";\nimport { mergeOptions } from \"../../utilities/index.js\";\nimport { getApolloContext } from \"../context/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\nimport { ObservableQuery, NetworkStatus } from \"../../core/index.js\";\nimport { DocumentType, verifyDocumentType } from \"../parser/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { compact, isNonEmptyArray, maybeDeepFreeze, } from \"../../utilities/index.js\";\nimport { wrapHook } from \"./internal/index.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction noop() { }\nvar lastWatchOptions = Symbol();\n/**\n * A hook for executing queries in an Apollo application.\n *\n * To run a query within a React component, call `useQuery` and pass it a GraphQL query document.\n *\n * When your component renders, `useQuery` returns an object from Apollo Client that contains `loading`, `error`, and `data` properties you can use to render your UI.\n *\n * > Refer to the [Queries](https://www.apollographql.com/docs/react/data/queries) section for a more in-depth overview of `useQuery`.\n *\n * @example\n * ```jsx\n * import { gql, useQuery } from '@apollo/client';\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function Hello() {\n *   const { loading, error, data } = useQuery(GET_GREETING, {\n *     variables: { language: 'english' },\n *   });\n *   if (loading) return <p>Loading ...</p>;\n *   return <h1>Hello {data.greeting.message}!</h1>;\n * }\n * ```\n * @since 3.0.0\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Options to control how the query is executed.\n * @returns Query result object\n */\nexport function useQuery(query, options) {\n    if (options === void 0) { options = Object.create(null); }\n    return wrapHook(\"useQuery\", \n    // eslint-disable-next-line react-compiler/react-compiler\n    useQuery_, useApolloClient(options && options.client))(query, options);\n}\nfunction useQuery_(query, options) {\n    var _a = useQueryInternals(query, options), result = _a.result, obsQueryFields = _a.obsQueryFields;\n    return React.useMemo(function () { return (__assign(__assign({}, result), obsQueryFields)); }, [result, obsQueryFields]);\n}\nfunction useInternalState(client, query, options, renderPromises, makeWatchQueryOptions) {\n    function createInternalState(previous) {\n        var _a;\n        verifyDocumentType(query, DocumentType.Query);\n        var internalState = {\n            client: client,\n            query: query,\n            observable: \n            // See if there is an existing observable that was used to fetch the same\n            // data and if so, use it instead since it will contain the proper queryId\n            // to fetch the result set. This is used during SSR.\n            (renderPromises &&\n                renderPromises.getSSRObservable(makeWatchQueryOptions())) ||\n                ObservableQuery[\"inactiveOnCreation\"].withValue(!renderPromises, function () {\n                    return client.watchQuery(getObsQueryOptions(void 0, client, options, makeWatchQueryOptions()));\n                }),\n            resultData: {\n                // Reuse previousData from previous InternalState (if any) to provide\n                // continuity of previousData even if/when the query or client changes.\n                previousData: (_a = previous === null || previous === void 0 ? void 0 : previous.resultData.current) === null || _a === void 0 ? void 0 : _a.data,\n            },\n        };\n        return internalState;\n    }\n    var _a = React.useState(createInternalState), internalState = _a[0], updateInternalState = _a[1];\n    /**\n     * Used by `useLazyQuery` when a new query is executed.\n     * We keep this logic here since it needs to update things in unsafe\n     * ways and here we at least can keep track of that in a single place.\n     */\n    function onQueryExecuted(watchQueryOptions) {\n        var _a;\n        var _b;\n        // this needs to be set to prevent an immediate `resubscribe` in the\n        // next rerender of the `useQuery` internals\n        Object.assign(internalState.observable, (_a = {},\n            _a[lastWatchOptions] = watchQueryOptions,\n            _a));\n        var resultData = internalState.resultData;\n        updateInternalState(__assign(__assign({}, internalState), { \n            // might be a different query\n            query: watchQueryOptions.query, resultData: Object.assign(resultData, {\n                // We need to modify the previous `resultData` object as we rely on the\n                // object reference in other places\n                previousData: ((_b = resultData.current) === null || _b === void 0 ? void 0 : _b.data) || resultData.previousData,\n                current: undefined,\n            }) }));\n    }\n    if (client !== internalState.client || query !== internalState.query) {\n        // If the client or query have changed, we need to create a new InternalState.\n        // This will trigger a re-render with the new state, but it will also continue\n        // to run the current render function to completion.\n        // Since we sometimes trigger some side-effects in the render function, we\n        // re-assign `state` to the new state to ensure that those side-effects are\n        // triggered with the new state.\n        var newInternalState = createInternalState(internalState);\n        updateInternalState(newInternalState);\n        return [newInternalState, onQueryExecuted];\n    }\n    return [internalState, onQueryExecuted];\n}\nexport function useQueryInternals(query, options) {\n    var client = useApolloClient(options.client);\n    var renderPromises = React.useContext(getApolloContext()).renderPromises;\n    var isSyncSSR = !!renderPromises;\n    var disableNetworkFetches = client.disableNetworkFetches;\n    var ssrAllowed = options.ssr !== false && !options.skip;\n    var partialRefetch = options.partialRefetch;\n    var makeWatchQueryOptions = createMakeWatchQueryOptions(client, query, options, isSyncSSR);\n    var _a = useInternalState(client, query, options, renderPromises, makeWatchQueryOptions), _b = _a[0], observable = _b.observable, resultData = _b.resultData, onQueryExecuted = _a[1];\n    var watchQueryOptions = makeWatchQueryOptions(observable);\n    useResubscribeIfNecessary(resultData, // might get mutated during render\n    observable, // might get mutated during render\n    client, options, watchQueryOptions);\n    var obsQueryFields = React.useMemo(function () { return bindObservableMethods(observable); }, [observable]);\n    useRegisterSSRObservable(observable, renderPromises, ssrAllowed);\n    var result = useObservableSubscriptionResult(resultData, observable, client, options, watchQueryOptions, disableNetworkFetches, partialRefetch, isSyncSSR, {\n        onCompleted: options.onCompleted || noop,\n        onError: options.onError || noop,\n    });\n    return {\n        result: result,\n        obsQueryFields: obsQueryFields,\n        observable: observable,\n        resultData: resultData,\n        client: client,\n        onQueryExecuted: onQueryExecuted,\n    };\n}\nfunction useObservableSubscriptionResult(resultData, observable, client, options, watchQueryOptions, disableNetworkFetches, partialRefetch, isSyncSSR, callbacks) {\n    var callbackRef = React.useRef(callbacks);\n    React.useEffect(function () {\n        // Make sure state.onCompleted and state.onError always reflect the latest\n        // options.onCompleted and options.onError callbacks provided to useQuery,\n        // since those functions are often recreated every time useQuery is called.\n        // Like the forceUpdate method, the versions of these methods inherited from\n        // InternalState.prototype are empty no-ops, but we can override them on the\n        // base state object (without modifying the prototype).\n        // eslint-disable-next-line react-compiler/react-compiler\n        callbackRef.current = callbacks;\n    });\n    var resultOverride = ((isSyncSSR || disableNetworkFetches) &&\n        options.ssr === false &&\n        !options.skip) ?\n        // If SSR has been explicitly disabled, and this function has been called\n        // on the server side, return the default loading state.\n        ssrDisabledResult\n        : options.skip || watchQueryOptions.fetchPolicy === \"standby\" ?\n            // When skipping a query (ie. we're not querying for data but still want to\n            // render children), make sure the `data` is cleared out and `loading` is\n            // set to `false` (since we aren't loading anything).\n            //\n            // NOTE: We no longer think this is the correct behavior. Skipping should\n            // not automatically set `data` to `undefined`, but instead leave the\n            // previous data in place. In other words, skipping should not mandate that\n            // previously received data is all of a sudden removed. Unfortunately,\n            // changing this is breaking, so we'll have to wait until Apollo Client 4.0\n            // to address this.\n            skipStandbyResult\n            : void 0;\n    var previousData = resultData.previousData;\n    var currentResultOverride = React.useMemo(function () {\n        return resultOverride &&\n            toQueryResult(resultOverride, previousData, observable, client);\n    }, [client, observable, resultOverride, previousData]);\n    return useSyncExternalStore(React.useCallback(function (handleStoreChange) {\n        // reference `disableNetworkFetches` here to ensure that the rules of hooks\n        // keep it as a dependency of this effect, even though it's not used\n        disableNetworkFetches;\n        if (isSyncSSR) {\n            return function () { };\n        }\n        var onNext = function () {\n            var previousResult = resultData.current;\n            // We use `getCurrentResult()` instead of the onNext argument because\n            // the values differ slightly. Specifically, loading results will have\n            // an empty object for data instead of `undefined` for some reason.\n            var result = observable.getCurrentResult();\n            // Make sure we're not attempting to re-render similar results\n            if (previousResult &&\n                previousResult.loading === result.loading &&\n                previousResult.networkStatus === result.networkStatus &&\n                equal(previousResult.data, result.data)) {\n                return;\n            }\n            setResult(result, resultData, observable, client, partialRefetch, handleStoreChange, callbackRef.current);\n        };\n        var onError = function (error) {\n            subscription.current.unsubscribe();\n            subscription.current = observable.resubscribeAfterError(onNext, onError);\n            if (!hasOwnProperty.call(error, \"graphQLErrors\")) {\n                // The error is not a GraphQL error\n                throw error;\n            }\n            var previousResult = resultData.current;\n            if (!previousResult ||\n                (previousResult && previousResult.loading) ||\n                !equal(error, previousResult.error)) {\n                setResult({\n                    data: (previousResult &&\n                        previousResult.data),\n                    error: error,\n                    loading: false,\n                    networkStatus: NetworkStatus.error,\n                }, resultData, observable, client, partialRefetch, handleStoreChange, callbackRef.current);\n            }\n        };\n        // TODO evaluate if we keep this in\n        // React Compiler cannot handle scoped `let` access, but a mutable object\n        // like this is fine.\n        // was:\n        // let subscription = observable.subscribe(onNext, onError);\n        var subscription = { current: observable.subscribe(onNext, onError) };\n        // Do the \"unsubscribe\" with a short delay.\n        // This way, an existing subscription can be reused without an additional\n        // request if \"unsubscribe\"  and \"resubscribe\" to the same ObservableQuery\n        // happen in very fast succession.\n        return function () {\n            setTimeout(function () { return subscription.current.unsubscribe(); });\n        };\n    }, [\n        disableNetworkFetches,\n        isSyncSSR,\n        observable,\n        resultData,\n        partialRefetch,\n        client,\n    ]), function () {\n        return currentResultOverride ||\n            getCurrentResult(resultData, observable, callbackRef.current, partialRefetch, client);\n    }, function () {\n        return currentResultOverride ||\n            getCurrentResult(resultData, observable, callbackRef.current, partialRefetch, client);\n    });\n}\nfunction useRegisterSSRObservable(observable, renderPromises, ssrAllowed) {\n    if (renderPromises && ssrAllowed) {\n        renderPromises.registerSSRObservable(observable);\n        if (observable.getCurrentResult().loading) {\n            // TODO: This is a legacy API which could probably be cleaned up\n            renderPromises.addObservableQueryPromise(observable);\n        }\n    }\n}\n// this hook is not compatible with any rules of React, and there's no good way to rewrite it.\n// it should stay a separate hook that will not be optimized by the compiler\nfunction useResubscribeIfNecessary(\n/** this hook will mutate properties on `resultData` */\nresultData, \n/** this hook will mutate properties on `observable` */\nobservable, client, options, watchQueryOptions) {\n    var _a;\n    if (observable[lastWatchOptions] &&\n        !equal(observable[lastWatchOptions], watchQueryOptions)) {\n        // Though it might be tempting to postpone this reobserve call to the\n        // useEffect block, we need getCurrentResult to return an appropriate\n        // loading:true result synchronously (later within the same call to\n        // useQuery). Since we already have this.observable here (not true for\n        // the very first call to useQuery), we are not initiating any new\n        // subscriptions, though it does feel less than ideal that reobserve\n        // (potentially) kicks off a network request (for example, when the\n        // variables have changed), which is technically a side-effect.\n        observable.reobserve(getObsQueryOptions(observable, client, options, watchQueryOptions));\n        // Make sure getCurrentResult returns a fresh ApolloQueryResult<TData>,\n        // but save the current data as this.previousData, just like setResult\n        // usually does.\n        resultData.previousData =\n            ((_a = resultData.current) === null || _a === void 0 ? void 0 : _a.data) || resultData.previousData;\n        resultData.current = void 0;\n    }\n    observable[lastWatchOptions] = watchQueryOptions;\n}\n/*\n * A function to massage options before passing them to ObservableQuery.\n * This is two-step curried because we want to reuse the `make` function,\n * but the `observable` might differ between calls to `make`.\n */\nexport function createMakeWatchQueryOptions(client, query, _a, isSyncSSR) {\n    if (_a === void 0) { _a = {}; }\n    var skip = _a.skip, ssr = _a.ssr, onCompleted = _a.onCompleted, onError = _a.onError, defaultOptions = _a.defaultOptions, \n    // The above options are useQuery-specific, so this ...otherOptions spread\n    // makes otherOptions almost a WatchQueryOptions object, except for the\n    // query property that we add below.\n    otherOptions = __rest(_a, [\"skip\", \"ssr\", \"onCompleted\", \"onError\", \"defaultOptions\"]);\n    return function (observable) {\n        // This Object.assign is safe because otherOptions is a fresh ...rest object\n        // that did not exist until just now, so modifications are still allowed.\n        var watchQueryOptions = Object.assign(otherOptions, { query: query });\n        if (isSyncSSR &&\n            (watchQueryOptions.fetchPolicy === \"network-only\" ||\n                watchQueryOptions.fetchPolicy === \"cache-and-network\")) {\n            // this behavior was added to react-apollo without explanation in this PR\n            // https://github.com/apollographql/react-apollo/pull/1579\n            watchQueryOptions.fetchPolicy = \"cache-first\";\n        }\n        if (!watchQueryOptions.variables) {\n            watchQueryOptions.variables = {};\n        }\n        if (skip) {\n            // When skipping, we set watchQueryOptions.fetchPolicy initially to\n            // \"standby\", but we also need/want to preserve the initial non-standby\n            // fetchPolicy that would have been used if not skipping.\n            watchQueryOptions.initialFetchPolicy =\n                watchQueryOptions.initialFetchPolicy ||\n                    watchQueryOptions.fetchPolicy ||\n                    getDefaultFetchPolicy(defaultOptions, client.defaultOptions);\n            watchQueryOptions.fetchPolicy = \"standby\";\n        }\n        else if (!watchQueryOptions.fetchPolicy) {\n            watchQueryOptions.fetchPolicy =\n                (observable === null || observable === void 0 ? void 0 : observable.options.initialFetchPolicy) ||\n                    getDefaultFetchPolicy(defaultOptions, client.defaultOptions);\n        }\n        return watchQueryOptions;\n    };\n}\nexport function getObsQueryOptions(observable, client, queryHookOptions, watchQueryOptions) {\n    var toMerge = [];\n    var globalDefaults = client.defaultOptions.watchQuery;\n    if (globalDefaults)\n        toMerge.push(globalDefaults);\n    if (queryHookOptions.defaultOptions) {\n        toMerge.push(queryHookOptions.defaultOptions);\n    }\n    // We use compact rather than mergeOptions for this part of the merge,\n    // because we want watchQueryOptions.variables (if defined) to replace\n    // this.observable.options.variables whole. This replacement allows\n    // removing variables by removing them from the variables input to\n    // useQuery. If the variables were always merged together (rather than\n    // replaced), there would be no way to remove existing variables.\n    // However, the variables from options.defaultOptions and globalDefaults\n    // (if provided) should be merged, to ensure individual defaulted\n    // variables always have values, if not otherwise defined in\n    // observable.options or watchQueryOptions.\n    toMerge.push(compact(observable && observable.options, watchQueryOptions));\n    return toMerge.reduce(mergeOptions);\n}\nfunction setResult(nextResult, resultData, observable, client, partialRefetch, forceUpdate, callbacks) {\n    var previousResult = resultData.current;\n    if (previousResult && previousResult.data) {\n        resultData.previousData = previousResult.data;\n    }\n    if (!nextResult.error && isNonEmptyArray(nextResult.errors)) {\n        // Until a set naming convention for networkError and graphQLErrors is\n        // decided upon, we map errors (graphQLErrors) to the error options.\n        // TODO: Is it possible for both result.error and result.errors to be\n        // defined here?\n        nextResult.error = new ApolloError({ graphQLErrors: nextResult.errors });\n    }\n    resultData.current = toQueryResult(unsafeHandlePartialRefetch(nextResult, observable, partialRefetch), resultData.previousData, observable, client);\n    // Calling state.setResult always triggers an update, though some call sites\n    // perform additional equality checks before committing to an update.\n    forceUpdate();\n    handleErrorOrCompleted(nextResult, previousResult === null || previousResult === void 0 ? void 0 : previousResult.networkStatus, callbacks);\n}\nfunction handleErrorOrCompleted(result, previousNetworkStatus, callbacks) {\n    if (!result.loading) {\n        var error_1 = toApolloError(result);\n        // wait a tick in case we are in the middle of rendering a component\n        Promise.resolve()\n            .then(function () {\n            if (error_1) {\n                callbacks.onError(error_1);\n            }\n            else if (result.data &&\n                previousNetworkStatus !== result.networkStatus &&\n                result.networkStatus === NetworkStatus.ready) {\n                callbacks.onCompleted(result.data);\n            }\n        })\n            .catch(function (error) {\n            globalThis.__DEV__ !== false && invariant.warn(error);\n        });\n    }\n}\nfunction getCurrentResult(resultData, observable, callbacks, partialRefetch, client) {\n    // Using this.result as a cache ensures getCurrentResult continues returning\n    // the same (===) result object, unless state.setResult has been called, or\n    // we're doing server rendering and therefore override the result below.\n    if (!resultData.current) {\n        // WARNING: SIDE-EFFECTS IN THE RENDER FUNCTION\n        // this could call unsafeHandlePartialRefetch\n        setResult(observable.getCurrentResult(), resultData, observable, client, partialRefetch, function () { }, callbacks);\n    }\n    return resultData.current;\n}\nexport function getDefaultFetchPolicy(queryHookDefaultOptions, clientDefaultOptions) {\n    var _a;\n    return ((queryHookDefaultOptions === null || queryHookDefaultOptions === void 0 ? void 0 : queryHookDefaultOptions.fetchPolicy) ||\n        ((_a = clientDefaultOptions === null || clientDefaultOptions === void 0 ? void 0 : clientDefaultOptions.watchQuery) === null || _a === void 0 ? void 0 : _a.fetchPolicy) ||\n        \"cache-first\");\n}\nexport function toApolloError(result) {\n    return isNonEmptyArray(result.errors) ?\n        new ApolloError({ graphQLErrors: result.errors })\n        : result.error;\n}\nexport function toQueryResult(result, previousData, observable, client) {\n    var data = result.data, partial = result.partial, resultWithoutPartial = __rest(result, [\"data\", \"partial\"]);\n    var queryResult = __assign(__assign({ data: data }, resultWithoutPartial), { client: client, observable: observable, variables: observable.variables, called: result !== ssrDisabledResult && result !== skipStandbyResult, previousData: previousData });\n    return queryResult;\n}\nfunction unsafeHandlePartialRefetch(result, observable, partialRefetch) {\n    // TODO: This code should be removed when the partialRefetch option is\n    // removed. I was unable to get this hook to behave reasonably in certain\n    // edge cases when this block was put in an effect.\n    if (result.partial &&\n        partialRefetch &&\n        !result.loading &&\n        (!result.data || Object.keys(result.data).length === 0) &&\n        observable.options.fetchPolicy !== \"cache-only\") {\n        observable.refetch();\n        return __assign(__assign({}, result), { loading: true, networkStatus: NetworkStatus.refetch });\n    }\n    return result;\n}\nvar ssrDisabledResult = maybeDeepFreeze({\n    loading: true,\n    data: void 0,\n    error: void 0,\n    networkStatus: NetworkStatus.loading,\n});\nvar skipStandbyResult = maybeDeepFreeze({\n    loading: false,\n    data: void 0,\n    error: void 0,\n    networkStatus: NetworkStatus.ready,\n});\nfunction bindObservableMethods(observable) {\n    return {\n        refetch: observable.refetch.bind(observable),\n        reobserve: observable.reobserve.bind(observable),\n        fetchMore: observable.fetchMore.bind(observable),\n        updateQuery: observable.updateQuery.bind(observable),\n        startPolling: observable.startPolling.bind(observable),\n        stopPolling: observable.stopPolling.bind(observable),\n        subscribeToMore: observable.subscribeToMore.bind(observable),\n    };\n}\n//# sourceMappingURL=useQuery.js.map", "import { __assign } from \"tslib\";\nimport * as React from \"rehackt\";\nimport { mergeOptions } from \"../../utilities/index.js\";\nimport { createMakeWatchQueryOptions, getDefaultFetchPolicy, getObsQueryOptions, toQueryResult, useQueryInternals, } from \"./useQuery.js\";\nimport { useIsomorphicLayoutEffect } from \"./internal/useIsomorphicLayoutEffect.js\";\n// The following methods, when called will execute the query, regardless of\n// whether the useLazyQuery execute function was called before.\nvar EAGER_METHODS = [\n    \"refetch\",\n    \"reobserve\",\n    \"fetchMore\",\n    \"updateQuery\",\n    \"startPolling\",\n    \"stopPolling\",\n    \"subscribeToMore\",\n];\n/**\n * A hook for imperatively executing queries in an Apollo application, e.g. in response to user interaction.\n *\n * > Refer to the [Queries - Manual execution with useLazyQuery](https://www.apollographql.com/docs/react/data/queries#manual-execution-with-uselazyquery) section for a more in-depth overview of `useLazyQuery`.\n *\n * @example\n * ```jsx\n * import { gql, useLazyQuery } from \"@apollo/client\";\n *\n * const GET_GREETING = gql`\n *   query GetGreeting($language: String!) {\n *     greeting(language: $language) {\n *       message\n *     }\n *   }\n * `;\n *\n * function Hello() {\n *   const [loadGreeting, { called, loading, data }] = useLazyQuery(\n *     GET_GREETING,\n *     { variables: { language: \"english\" } }\n *   );\n *   if (called && loading) return <p>Loading ...</p>\n *   if (!called) {\n *     return <button onClick={() => loadGreeting()}>Load greeting</button>\n *   }\n *   return <h1>Hello {data.greeting.message}!</h1>;\n * }\n * ```\n * @since 3.0.0\n *\n * @param query - A GraphQL query document parsed into an AST by `gql`.\n * @param options - Default options to control how the query is executed.\n * @returns A tuple in the form of `[execute, result]`\n */\nexport function useLazyQuery(query, options) {\n    var _a;\n    var execOptionsRef = React.useRef(void 0);\n    var optionsRef = React.useRef(void 0);\n    var queryRef = React.useRef(void 0);\n    var merged = mergeOptions(options, execOptionsRef.current || {});\n    var document = (_a = merged === null || merged === void 0 ? void 0 : merged.query) !== null && _a !== void 0 ? _a : query;\n    // Use refs to track options and the used query to ensure the `execute`\n    // function remains referentially stable between renders.\n    optionsRef.current = options;\n    queryRef.current = document;\n    var queryHookOptions = __assign(__assign({}, merged), { skip: !execOptionsRef.current });\n    var _b = useQueryInternals(document, queryHookOptions), obsQueryFields = _b.obsQueryFields, useQueryResult = _b.result, client = _b.client, resultData = _b.resultData, observable = _b.observable, onQueryExecuted = _b.onQueryExecuted;\n    var initialFetchPolicy = observable.options.initialFetchPolicy ||\n        getDefaultFetchPolicy(queryHookOptions.defaultOptions, client.defaultOptions);\n    var forceUpdateState = React.useReducer(function (tick) { return tick + 1; }, 0)[1];\n    // We use useMemo here to make sure the eager methods have a stable identity.\n    var eagerMethods = React.useMemo(function () {\n        var eagerMethods = {};\n        var _loop_1 = function (key) {\n            var method = obsQueryFields[key];\n            eagerMethods[key] = function () {\n                if (!execOptionsRef.current) {\n                    execOptionsRef.current = Object.create(null);\n                    // Only the first time populating execOptionsRef.current matters here.\n                    forceUpdateState();\n                }\n                // @ts-expect-error this is just too generic to type\n                return method.apply(this, arguments);\n            };\n        };\n        for (var _i = 0, EAGER_METHODS_1 = EAGER_METHODS; _i < EAGER_METHODS_1.length; _i++) {\n            var key = EAGER_METHODS_1[_i];\n            _loop_1(key);\n        }\n        return eagerMethods;\n    }, [forceUpdateState, obsQueryFields]);\n    var called = !!execOptionsRef.current;\n    var result = React.useMemo(function () { return (__assign(__assign(__assign({}, useQueryResult), eagerMethods), { called: called })); }, [useQueryResult, eagerMethods, called]);\n    var execute = React.useCallback(function (executeOptions) {\n        execOptionsRef.current =\n            executeOptions ? __assign(__assign({}, executeOptions), { fetchPolicy: executeOptions.fetchPolicy || initialFetchPolicy }) : {\n                fetchPolicy: initialFetchPolicy,\n            };\n        var options = mergeOptions(optionsRef.current, __assign({ query: queryRef.current }, execOptionsRef.current));\n        var promise = executeQuery(resultData, observable, client, document, __assign(__assign({}, options), { skip: false }), onQueryExecuted).then(function (queryResult) { return Object.assign(queryResult, eagerMethods); });\n        // Because the return value of `useLazyQuery` is usually floated, we need\n        // to catch the promise to prevent unhandled rejections.\n        promise.catch(function () { });\n        return promise;\n    }, [\n        client,\n        document,\n        eagerMethods,\n        initialFetchPolicy,\n        observable,\n        resultData,\n        onQueryExecuted,\n    ]);\n    var executeRef = React.useRef(execute);\n    useIsomorphicLayoutEffect(function () {\n        executeRef.current = execute;\n    });\n    var stableExecute = React.useCallback(function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return executeRef.current.apply(executeRef, args);\n    }, []);\n    return [stableExecute, result];\n}\nfunction executeQuery(resultData, observable, client, currentQuery, options, onQueryExecuted) {\n    var query = options.query || currentQuery;\n    var watchQueryOptions = createMakeWatchQueryOptions(client, query, options, false)(observable);\n    var concast = observable.reobserveAsConcast(getObsQueryOptions(observable, client, options, watchQueryOptions));\n    onQueryExecuted(watchQueryOptions);\n    return new Promise(function (resolve) {\n        var result;\n        // Subscribe to the concast independently of the ObservableQuery in case\n        // the component gets unmounted before the promise resolves. This prevents\n        // the concast from terminating early and resolving with `undefined` when\n        // there are no more subscribers for the concast.\n        concast.subscribe({\n            next: function (value) {\n                result = value;\n            },\n            error: function () {\n                resolve(toQueryResult(observable.getCurrentResult(), resultData.previousData, observable, client));\n            },\n            complete: function () {\n                resolve(toQueryResult(observable[\"maskResult\"](result), resultData.previousData, observable, client));\n            },\n        });\n    });\n}\n//# sourceMappingURL=useLazyQuery.js.map", "import { __assign } from \"tslib\";\nimport * as React from \"rehackt\";\nimport { mergeOptions } from \"../../utilities/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { DocumentType, verifyDocumentType } from \"../parser/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useIsomorphicLayoutEffect } from \"./internal/useIsomorphicLayoutEffect.js\";\n/**\n *\n *\n * > Refer to the [Mutations](https://www.apollographql.com/docs/react/data/mutations/) section for a more in-depth overview of `useMutation`.\n *\n * @example\n * ```jsx\n * import { gql, useMutation } from '@apollo/client';\n *\n * const ADD_TODO = gql`\n *   mutation AddTodo($type: String!) {\n *     addTodo(type: $type) {\n *       id\n *       type\n *     }\n *   }\n * `;\n *\n * function AddTodo() {\n *   let input;\n *   const [addTodo, { data }] = useMutation(ADD_TODO);\n *\n *   return (\n *     <div>\n *       <form\n *         onSubmit={e => {\n *           e.preventDefault();\n *           addTodo({ variables: { type: input.value } });\n *           input.value = '';\n *         }}\n *       >\n *         <input\n *           ref={node => {\n *             input = node;\n *           }}\n *         />\n *         <button type=\"submit\">Add Todo</button>\n *       </form>\n *     </div>\n *   );\n * }\n * ```\n * @since 3.0.0\n * @param mutation - A GraphQL mutation document parsed into an AST by `gql`.\n * @param options - Options to control how the mutation is executed.\n * @returns A tuple in the form of `[mutate, result]`\n */\nexport function useMutation(mutation, options) {\n    var client = useApolloClient(options === null || options === void 0 ? void 0 : options.client);\n    verifyDocumentType(mutation, DocumentType.Mutation);\n    var _a = React.useState({\n        called: false,\n        loading: false,\n        client: client,\n    }), result = _a[0], setResult = _a[1];\n    var ref = React.useRef({\n        result: result,\n        mutationId: 0,\n        isMounted: true,\n        client: client,\n        mutation: mutation,\n        options: options,\n    });\n    useIsomorphicLayoutEffect(function () {\n        Object.assign(ref.current, { client: client, options: options, mutation: mutation });\n    });\n    var execute = React.useCallback(function (executeOptions) {\n        if (executeOptions === void 0) { executeOptions = {}; }\n        var _a = ref.current, options = _a.options, mutation = _a.mutation;\n        var baseOptions = __assign(__assign({}, options), { mutation: mutation });\n        var client = executeOptions.client || ref.current.client;\n        if (!ref.current.result.loading &&\n            !baseOptions.ignoreResults &&\n            ref.current.isMounted) {\n            setResult((ref.current.result = {\n                loading: true,\n                error: void 0,\n                data: void 0,\n                called: true,\n                client: client,\n            }));\n        }\n        var mutationId = ++ref.current.mutationId;\n        var clientOptions = mergeOptions(baseOptions, executeOptions);\n        return client\n            .mutate(clientOptions)\n            .then(function (response) {\n            var _a, _b;\n            var data = response.data, errors = response.errors;\n            var error = errors && errors.length > 0 ?\n                new ApolloError({ graphQLErrors: errors })\n                : void 0;\n            var onError = executeOptions.onError || ((_a = ref.current.options) === null || _a === void 0 ? void 0 : _a.onError);\n            if (error && onError) {\n                onError(error, clientOptions);\n            }\n            if (mutationId === ref.current.mutationId &&\n                !clientOptions.ignoreResults) {\n                var result_1 = {\n                    called: true,\n                    loading: false,\n                    data: data,\n                    error: error,\n                    client: client,\n                };\n                if (ref.current.isMounted && !equal(ref.current.result, result_1)) {\n                    setResult((ref.current.result = result_1));\n                }\n            }\n            var onCompleted = executeOptions.onCompleted || ((_b = ref.current.options) === null || _b === void 0 ? void 0 : _b.onCompleted);\n            if (!error) {\n                onCompleted === null || onCompleted === void 0 ? void 0 : onCompleted(response.data, clientOptions);\n            }\n            return response;\n        }, function (error) {\n            var _a;\n            if (mutationId === ref.current.mutationId &&\n                ref.current.isMounted) {\n                var result_2 = {\n                    loading: false,\n                    error: error,\n                    data: void 0,\n                    called: true,\n                    client: client,\n                };\n                if (!equal(ref.current.result, result_2)) {\n                    setResult((ref.current.result = result_2));\n                }\n            }\n            var onError = executeOptions.onError || ((_a = ref.current.options) === null || _a === void 0 ? void 0 : _a.onError);\n            if (onError) {\n                onError(error, clientOptions);\n                // TODO(brian): why are we returning this here???\n                return { data: void 0, errors: error };\n            }\n            throw error;\n        });\n    }, []);\n    var reset = React.useCallback(function () {\n        if (ref.current.isMounted) {\n            var result_3 = {\n                called: false,\n                loading: false,\n                client: ref.current.client,\n            };\n            Object.assign(ref.current, { mutationId: 0, result: result_3 });\n            setResult(result_3);\n        }\n    }, []);\n    React.useEffect(function () {\n        var current = ref.current;\n        // eslint-disable-next-line react-compiler/react-compiler\n        current.isMounted = true;\n        return function () {\n            current.isMounted = false;\n        };\n    }, []);\n    return [execute, __assign({ reset: reset }, result)];\n}\n//# sourceMappingURL=useMutation.js.map", "import { __assign } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport * as React from \"rehackt\";\nimport { equal } from \"@wry/equality\";\nimport { DocumentType, verifyDocumentType } from \"../parser/index.js\";\nimport { ApolloError, Observable } from \"../../core/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useDeepMemo } from \"./internal/useDeepMemo.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nimport { toApolloError } from \"./useQuery.js\";\nimport { useIsomorphicLayoutEffect } from \"./internal/useIsomorphicLayoutEffect.js\";\n/**\n * > Refer to the [Subscriptions](https://www.apollographql.com/docs/react/data/subscriptions/) section for a more in-depth overview of `useSubscription`.\n *\n * @example\n * ```jsx\n * const COMMENTS_SUBSCRIPTION = gql`\n *   subscription OnCommentAdded($repoFullName: String!) {\n *     commentAdded(repoFullName: $repoFullName) {\n *       id\n *       content\n *     }\n *   }\n * `;\n *\n * function DontReadTheComments({ repoFullName }) {\n *   const {\n *     data: { commentAdded },\n *     loading,\n *   } = useSubscription(COMMENTS_SUBSCRIPTION, { variables: { repoFullName } });\n *   return <h4>New comment: {!loading && commentAdded.content}</h4>;\n * }\n * ```\n * @remarks\n * #### Consider using `onData` instead of `useEffect`\n *\n * If you want to react to incoming data, please use the `onData` option instead of `useEffect`.\n * State updates you make inside a `useEffect` hook might cause additional rerenders, and `useEffect` is mostly meant for side effects of rendering, not as an event handler.\n * State updates made in an event handler like `onData` might - depending on the React version - be batched and cause only a single rerender.\n *\n * Consider the following component:\n *\n * ```jsx\n * export function Subscriptions() {\n *   const { data, error, loading } = useSubscription(query);\n *   const [accumulatedData, setAccumulatedData] = useState([]);\n *\n *   useEffect(() => {\n *     setAccumulatedData((prev) => [...prev, data]);\n *   }, [data]);\n *\n *   return (\n *     <>\n *       {loading && <p>Loading...</p>}\n *       {JSON.stringify(accumulatedData, undefined, 2)}\n *     </>\n *   );\n * }\n * ```\n *\n * Instead of using `useEffect` here, we can re-write this component to use the `onData` callback function accepted in `useSubscription`'s `options` object:\n *\n * ```jsx\n * export function Subscriptions() {\n *   const [accumulatedData, setAccumulatedData] = useState([]);\n *   const { data, error, loading } = useSubscription(\n *     query,\n *     {\n *       onData({ data }) {\n *         setAccumulatedData((prev) => [...prev, data])\n *       }\n *     }\n *   );\n *\n *   return (\n *     <>\n *       {loading && <p>Loading...</p>}\n *       {JSON.stringify(accumulatedData, undefined, 2)}\n *     </>\n *   );\n * }\n * ```\n *\n * > ⚠️ **Note:** The `useSubscription` option `onData` is available in Apollo Client >= 3.7. In previous versions, the equivalent option is named `onSubscriptionData`.\n *\n * Now, the first message will be added to the `accumulatedData` array since `onData` is called _before_ the component re-renders. React 18 automatic batching is still in effect and results in a single re-render, but with `onData` we can guarantee each message received after the component mounts is added to `accumulatedData`.\n *\n * @since 3.0.0\n * @param subscription - A GraphQL subscription document parsed into an AST by `gql`.\n * @param options - Options to control how the subscription is executed.\n * @returns Query result object\n */\nexport function useSubscription(subscription, options) {\n    if (options === void 0) { options = Object.create(null); }\n    var hasIssuedDeprecationWarningRef = React.useRef(false);\n    var client = useApolloClient(options.client);\n    verifyDocumentType(subscription, DocumentType.Subscription);\n    if (!hasIssuedDeprecationWarningRef.current) {\n        // eslint-disable-next-line react-compiler/react-compiler\n        hasIssuedDeprecationWarningRef.current = true;\n        if (options.onSubscriptionData) {\n            globalThis.__DEV__ !== false && invariant.warn(options.onData ? 61 : 62);\n        }\n        if (options.onSubscriptionComplete) {\n            globalThis.__DEV__ !== false && invariant.warn(options.onComplete ? 63 : 64);\n        }\n    }\n    var skip = options.skip, fetchPolicy = options.fetchPolicy, errorPolicy = options.errorPolicy, shouldResubscribe = options.shouldResubscribe, context = options.context, extensions = options.extensions, ignoreResults = options.ignoreResults;\n    var variables = useDeepMemo(function () { return options.variables; }, [options.variables]);\n    var recreate = function () {\n        return createSubscription(client, subscription, variables, fetchPolicy, errorPolicy, context, extensions);\n    };\n    var _a = React.useState(options.skip ? null : recreate), observable = _a[0], setObservable = _a[1];\n    var recreateRef = React.useRef(recreate);\n    useIsomorphicLayoutEffect(function () {\n        recreateRef.current = recreate;\n    });\n    if (skip) {\n        if (observable) {\n            setObservable((observable = null));\n        }\n    }\n    else if (!observable ||\n        ((client !== observable.__.client ||\n            subscription !== observable.__.query ||\n            fetchPolicy !== observable.__.fetchPolicy ||\n            errorPolicy !== observable.__.errorPolicy ||\n            !equal(variables, observable.__.variables)) &&\n            (typeof shouldResubscribe === \"function\" ?\n                !!shouldResubscribe(options)\n                : shouldResubscribe) !== false)) {\n        setObservable((observable = recreate()));\n    }\n    var optionsRef = React.useRef(options);\n    React.useEffect(function () {\n        optionsRef.current = options;\n    });\n    var fallbackLoading = !skip && !ignoreResults;\n    var fallbackResult = React.useMemo(function () { return ({\n        loading: fallbackLoading,\n        error: void 0,\n        data: void 0,\n        variables: variables,\n    }); }, [fallbackLoading, variables]);\n    var ignoreResultsRef = React.useRef(ignoreResults);\n    useIsomorphicLayoutEffect(function () {\n        // We cannot reference `ignoreResults` directly in the effect below\n        // it would add a dependency to the `useEffect` deps array, which means the\n        // subscription would be recreated if `ignoreResults` changes\n        // As a result, on resubscription, the last result would be re-delivered,\n        // rendering the component one additional time, and re-triggering `onData`.\n        // The same applies to `fetchPolicy`, which results in a new `observable`\n        // being created. We cannot really avoid it in that case, but we can at least\n        // avoid it for `ignoreResults`.\n        ignoreResultsRef.current = ignoreResults;\n    });\n    var ret = useSyncExternalStore(React.useCallback(function (update) {\n        if (!observable) {\n            return function () { };\n        }\n        var subscriptionStopped = false;\n        var variables = observable.__.variables;\n        var client = observable.__.client;\n        var subscription = observable.subscribe({\n            next: function (fetchResult) {\n                var _a, _b;\n                if (subscriptionStopped) {\n                    return;\n                }\n                var result = {\n                    loading: false,\n                    // TODO: fetchResult.data can be null but SubscriptionResult.data\n                    // expects TData | undefined only\n                    data: fetchResult.data,\n                    error: toApolloError(fetchResult),\n                    variables: variables,\n                };\n                observable.__.setResult(result);\n                if (!ignoreResultsRef.current)\n                    update();\n                if (result.error) {\n                    (_b = (_a = optionsRef.current).onError) === null || _b === void 0 ? void 0 : _b.call(_a, result.error);\n                }\n                else if (optionsRef.current.onData) {\n                    optionsRef.current.onData({\n                        client: client,\n                        data: result,\n                    });\n                }\n                else if (optionsRef.current.onSubscriptionData) {\n                    optionsRef.current.onSubscriptionData({\n                        client: client,\n                        subscriptionData: result,\n                    });\n                }\n            },\n            error: function (error) {\n                var _a, _b;\n                error =\n                    error instanceof ApolloError ? error : (new ApolloError({ protocolErrors: [error] }));\n                if (!subscriptionStopped) {\n                    observable.__.setResult({\n                        loading: false,\n                        data: void 0,\n                        error: error,\n                        variables: variables,\n                    });\n                    if (!ignoreResultsRef.current)\n                        update();\n                    (_b = (_a = optionsRef.current).onError) === null || _b === void 0 ? void 0 : _b.call(_a, error);\n                }\n            },\n            complete: function () {\n                if (!subscriptionStopped) {\n                    if (optionsRef.current.onComplete) {\n                        optionsRef.current.onComplete();\n                    }\n                    else if (optionsRef.current.onSubscriptionComplete) {\n                        optionsRef.current.onSubscriptionComplete();\n                    }\n                }\n            },\n        });\n        return function () {\n            // immediately stop receiving subscription values, but do not unsubscribe\n            // until after a short delay in case another useSubscription hook is\n            // reusing the same underlying observable and is about to subscribe\n            subscriptionStopped = true;\n            setTimeout(function () {\n                subscription.unsubscribe();\n            });\n        };\n    }, [observable]), function () {\n        return observable && !skip && !ignoreResults ?\n            observable.__.result\n            : fallbackResult;\n    }, function () { return fallbackResult; });\n    var restart = React.useCallback(function () {\n        invariant(!optionsRef.current.skip, 65);\n        setObservable(recreateRef.current());\n    }, [optionsRef, recreateRef]);\n    return React.useMemo(function () { return (__assign(__assign({}, ret), { restart: restart })); }, [ret, restart]);\n}\nfunction createSubscription(client, query, variables, fetchPolicy, errorPolicy, context, extensions) {\n    var options = {\n        query: query,\n        variables: variables,\n        fetchPolicy: fetchPolicy,\n        errorPolicy: errorPolicy,\n        context: context,\n        extensions: extensions,\n    };\n    var __ = __assign(__assign({}, options), { client: client, result: {\n            loading: true,\n            data: void 0,\n            error: void 0,\n            variables: variables,\n        }, setResult: function (result) {\n            __.result = result;\n        } });\n    var observable = null;\n    return Object.assign(new Observable(function (observer) {\n        // lazily start the subscription when the first observer subscribes\n        // to get around strict mode\n        if (!observable) {\n            observable = client.subscribe(options);\n        }\n        var sub = observable.subscribe(observer);\n        return function () { return sub.unsubscribe(); };\n    }), {\n        /**\n         * A tracking object to store details about the observable and the latest result of the subscription.\n         */\n        __: __,\n    });\n}\n//# sourceMappingURL=useSubscription.js.map", "import * as React from \"rehackt\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\n/**\n * Reads the value of a [reactive variable](https://www.apollographql.com/docs/react/local-state/reactive-variables/) and re-renders the containing component whenever that variable's value changes. This enables a reactive variable to trigger changes _without_ relying on the `useQuery` hook.\n *\n * @example\n * ```jsx\n * import { makeVar, useReactiveVar } from \"@apollo/client\";\n * export const cartItemsVar = makeVar([]);\n *\n * export function Cart() {\n *   const cartItems = useReactiveVar(cartItemsVar);\n *   // ...\n * }\n * ```\n * @since 3.2.0\n * @param rv - A reactive variable.\n * @returns The current value of the reactive variable.\n */\nexport function useReactiveVar(rv) {\n    return useSyncExternalStore(React.useCallback(function (update) {\n        // By reusing the same onNext function in the nested call to\n        // rv.onNextChange(onNext), we can keep using the initial clean-up function\n        // returned by rv.onNextChange(function onNext(v){...}), without having to\n        // register the new clean-up function (returned by the nested\n        // rv.onNextChange(onNext)) with yet another callback.\n        return rv.onNextChange(function onNext() {\n            update();\n            rv.onNextChange(onNext);\n        });\n    }, [rv]), rv, rv);\n}\n//# sourceMappingURL=useReactiveVar.js.map", "import { __assign, __rest } from \"tslib\";\nimport * as React from \"rehackt\";\nimport { mergeDeepArray } from \"../../utilities/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nimport { useDeepMemo, wrapHook } from \"./internal/index.js\";\nimport equal from \"@wry/equality\";\nexport function useFragment(options) {\n    return wrapHook(\"useFragment\", \n    // eslint-disable-next-line react-compiler/react-compiler\n    useFragment_, useApolloClient(options.client))(options);\n}\nfunction useFragment_(options) {\n    var client = useApolloClient(options.client);\n    var cache = client.cache;\n    var from = options.from, rest = __rest(options, [\"from\"]);\n    // We calculate the cache id seperately from `stableOptions` because we don't\n    // want changes to non key fields in the `from` property to affect\n    // `stableOptions` and retrigger our subscription. If the cache identifier\n    // stays the same between renders, we want to reuse the existing subscription.\n    var id = React.useMemo(function () {\n        return typeof from === \"string\" ? from\n            : from === null ? null\n                : cache.identify(from);\n    }, [cache, from]);\n    var stableOptions = useDeepMemo(function () { return (__assign(__assign({}, rest), { from: id })); }, [rest, id]);\n    // Since .next is async, we need to make sure that we\n    // get the correct diff on the next render given new diffOptions\n    var diff = React.useMemo(function () {\n        var fragment = stableOptions.fragment, fragmentName = stableOptions.fragmentName, from = stableOptions.from, _a = stableOptions.optimistic, optimistic = _a === void 0 ? true : _a;\n        if (from === null) {\n            return {\n                result: diffToResult({\n                    result: {},\n                    complete: false,\n                }),\n            };\n        }\n        var cache = client.cache;\n        var diff = cache.diff(__assign(__assign({}, stableOptions), { returnPartialData: true, id: from, query: cache[\"getFragmentDoc\"](fragment, fragmentName), optimistic: optimistic }));\n        return {\n            result: diffToResult(__assign(__assign({}, diff), { result: client[\"queryManager\"].maskFragment({\n                    fragment: fragment,\n                    fragmentName: fragmentName,\n                    data: diff.result,\n                }) })),\n        };\n    }, [client, stableOptions]);\n    // Used for both getSnapshot and getServerSnapshot\n    var getSnapshot = React.useCallback(function () { return diff.result; }, [diff]);\n    return useSyncExternalStore(React.useCallback(function (forceUpdate) {\n        var lastTimeout = 0;\n        var subscription = stableOptions.from === null ?\n            null\n            : client.watchFragment(stableOptions).subscribe({\n                next: function (result) {\n                    // Since `next` is called async by zen-observable, we want to avoid\n                    // unnecessarily rerendering this hook for the initial result\n                    // emitted from watchFragment which should be equal to\n                    // `diff.result`.\n                    if (equal(result, diff.result))\n                        return;\n                    diff.result = result;\n                    // If we get another update before we've re-rendered, bail out of\n                    // the update and try again. This ensures that the relative timing\n                    // between useQuery and useFragment stays roughly the same as\n                    // fixed in https://github.com/apollographql/apollo-client/pull/11083\n                    clearTimeout(lastTimeout);\n                    lastTimeout = setTimeout(forceUpdate);\n                },\n            });\n        return function () {\n            subscription === null || subscription === void 0 ? void 0 : subscription.unsubscribe();\n            clearTimeout(lastTimeout);\n        };\n    }, [client, stableOptions, diff]), getSnapshot, getSnapshot);\n}\nfunction diffToResult(diff) {\n    var result = {\n        data: diff.result,\n        complete: !!diff.complete,\n    };\n    if (diff.missing) {\n        result.missing = mergeDeepArray(diff.missing.map(function (error) { return error.missing; }));\n    }\n    return result;\n}\n//# sourceMappingURL=useFragment.js.map", "export var skipToken = Symbol.for(\"apollo.skipToken\");\n//# sourceMappingURL=constants.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport * as React from \"rehackt\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { ApolloError, NetworkStatus } from \"../../core/index.js\";\nimport { isNonEmptyArray } from \"../../utilities/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { DocumentType, verifyDocumentType } from \"../parser/index.js\";\nimport { __use, useDeepMemo, wrapHook } from \"./internal/index.js\";\nimport { getSuspenseCache } from \"../internal/index.js\";\nimport { canonicalStringify } from \"../../cache/index.js\";\nimport { skipToken } from \"./constants.js\";\nexport function useSuspenseQuery(query, options) {\n    if (options === void 0) { options = Object.create(null); }\n    return wrapHook(\"useSuspenseQuery\", \n    // eslint-disable-next-line react-compiler/react-compiler\n    useSuspenseQuery_, useApolloClient(typeof options === \"object\" ? options.client : undefined))(query, options);\n}\nfunction useSuspenseQuery_(query, options) {\n    var client = useApolloClient(options.client);\n    var suspenseCache = getSuspenseCache(client);\n    var watchQueryOptions = useWatchQueryOptions({\n        client: client,\n        query: query,\n        options: options,\n    });\n    var fetchPolicy = watchQueryOptions.fetchPolicy, variables = watchQueryOptions.variables;\n    var _a = options.queryKey, queryKey = _a === void 0 ? [] : _a;\n    var cacheKey = __spreadArray([\n        query,\n        canonicalStringify(variables)\n    ], [].concat(queryKey), true);\n    var queryRef = suspenseCache.getQueryRef(cacheKey, function () {\n        return client.watchQuery(watchQueryOptions);\n    });\n    var _b = React.useState([queryRef.key, queryRef.promise]), current = _b[0], setPromise = _b[1];\n    // This saves us a re-execution of the render function when a variable changed.\n    if (current[0] !== queryRef.key) {\n        // eslint-disable-next-line react-compiler/react-compiler\n        current[0] = queryRef.key;\n        current[1] = queryRef.promise;\n    }\n    var promise = current[1];\n    if (queryRef.didChangeOptions(watchQueryOptions)) {\n        current[1] = promise = queryRef.applyOptions(watchQueryOptions);\n    }\n    React.useEffect(function () {\n        var dispose = queryRef.retain();\n        var removeListener = queryRef.listen(function (promise) {\n            setPromise([queryRef.key, promise]);\n        });\n        return function () {\n            removeListener();\n            dispose();\n        };\n    }, [queryRef]);\n    var skipResult = React.useMemo(function () {\n        var error = toApolloError(queryRef.result);\n        return {\n            loading: false,\n            data: queryRef.result.data,\n            networkStatus: error ? NetworkStatus.error : NetworkStatus.ready,\n            error: error,\n        };\n    }, [queryRef.result]);\n    var result = fetchPolicy === \"standby\" ? skipResult : __use(promise);\n    var fetchMore = React.useCallback(function (options) {\n        var promise = queryRef.fetchMore(options);\n        setPromise([queryRef.key, queryRef.promise]);\n        return promise;\n    }, [queryRef]);\n    var refetch = React.useCallback(function (variables) {\n        var promise = queryRef.refetch(variables);\n        setPromise([queryRef.key, queryRef.promise]);\n        return promise;\n    }, [queryRef]);\n    // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n    var subscribeToMore = queryRef.observable\n        .subscribeToMore;\n    return React.useMemo(function () {\n        return {\n            client: client,\n            data: result.data,\n            error: toApolloError(result),\n            networkStatus: result.networkStatus,\n            fetchMore: fetchMore,\n            refetch: refetch,\n            subscribeToMore: subscribeToMore,\n        };\n    }, [client, fetchMore, refetch, result, subscribeToMore]);\n}\nfunction validateOptions(options) {\n    var query = options.query, fetchPolicy = options.fetchPolicy, returnPartialData = options.returnPartialData;\n    verifyDocumentType(query, DocumentType.Query);\n    validateFetchPolicy(fetchPolicy);\n    validatePartialDataReturn(fetchPolicy, returnPartialData);\n}\nfunction validateFetchPolicy(fetchPolicy) {\n    if (fetchPolicy === void 0) { fetchPolicy = \"cache-first\"; }\n    var supportedFetchPolicies = [\n        \"cache-first\",\n        \"network-only\",\n        \"no-cache\",\n        \"cache-and-network\",\n    ];\n    invariant(supportedFetchPolicies.includes(fetchPolicy), 66, fetchPolicy);\n}\nfunction validatePartialDataReturn(fetchPolicy, returnPartialData) {\n    if (fetchPolicy === \"no-cache\" && returnPartialData) {\n        globalThis.__DEV__ !== false && invariant.warn(67);\n    }\n}\nexport function toApolloError(result) {\n    return isNonEmptyArray(result.errors) ?\n        new ApolloError({ graphQLErrors: result.errors })\n        : result.error;\n}\nexport function useWatchQueryOptions(_a) {\n    var client = _a.client, query = _a.query, options = _a.options;\n    return useDeepMemo(function () {\n        var _a;\n        if (options === skipToken) {\n            return { query: query, fetchPolicy: \"standby\" };\n        }\n        var fetchPolicy = options.fetchPolicy ||\n            ((_a = client.defaultOptions.watchQuery) === null || _a === void 0 ? void 0 : _a.fetchPolicy) ||\n            \"cache-first\";\n        var watchQueryOptions = __assign(__assign({}, options), { fetchPolicy: fetchPolicy, query: query, notifyOnNetworkStatusChange: false, nextFetchPolicy: void 0 });\n        if (globalThis.__DEV__ !== false) {\n            validateOptions(watchQueryOptions);\n        }\n        // Assign the updated fetch policy after our validation since `standby` is\n        // not a supported fetch policy on its own without the use of `skip`.\n        if (options.skip) {\n            watchQueryOptions.fetchPolicy = \"standby\";\n        }\n        return watchQueryOptions;\n    }, [client, options, query]);\n}\n//# sourceMappingURL=useSuspenseQuery.js.map", "import { __spreadArray } from \"tslib\";\nimport * as React from \"rehackt\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { getSuspenseCache, unwrapQueryRef, updateWrappedQueryRef, wrapQueryRef, } from \"../internal/index.js\";\nimport { wrapHook } from \"./internal/index.js\";\nimport { useWatchQueryOptions } from \"./useSuspenseQuery.js\";\nimport { canonicalStringify } from \"../../cache/index.js\";\nexport function useBackgroundQuery(query, options) {\n    if (options === void 0) { options = Object.create(null); }\n    return wrapHook(\"useBackgroundQuery\", \n    // eslint-disable-next-line react-compiler/react-compiler\n    useBackgroundQuery_, useApolloClient(typeof options === \"object\" ? options.client : undefined))(query, options);\n}\nfunction useBackgroundQuery_(query, options) {\n    var client = useApolloClient(options.client);\n    var suspenseCache = getSuspenseCache(client);\n    var watchQueryOptions = useWatchQueryOptions({ client: client, query: query, options: options });\n    var fetchPolicy = watchQueryOptions.fetchPolicy, variables = watchQueryOptions.variables;\n    var _a = options.queryKey, queryKey = _a === void 0 ? [] : _a;\n    // This ref tracks the first time query execution is enabled to determine\n    // whether to return a query ref or `undefined`. When initialized\n    // in a skipped state (either via `skip: true` or `skipToken`) we return\n    // `undefined` for the `queryRef` until the query has been enabled. Once\n    // enabled, a query ref is always returned regardless of whether the query is\n    // skipped again later.\n    var didFetchResult = React.useRef(fetchPolicy !== \"standby\");\n    didFetchResult.current || (didFetchResult.current = fetchPolicy !== \"standby\");\n    var cacheKey = __spreadArray([\n        query,\n        canonicalStringify(variables)\n    ], [].concat(queryKey), true);\n    var queryRef = suspenseCache.getQueryRef(cacheKey, function () {\n        return client.watchQuery(watchQueryOptions);\n    });\n    var _b = React.useState(wrapQueryRef(queryRef)), wrappedQueryRef = _b[0], setWrappedQueryRef = _b[1];\n    if (unwrapQueryRef(wrappedQueryRef) !== queryRef) {\n        setWrappedQueryRef(wrapQueryRef(queryRef));\n    }\n    if (queryRef.didChangeOptions(watchQueryOptions)) {\n        var promise = queryRef.applyOptions(watchQueryOptions);\n        updateWrappedQueryRef(wrappedQueryRef, promise);\n    }\n    // This prevents issues where rerendering useBackgroundQuery after the\n    // queryRef has been disposed would cause the hook to return a new queryRef\n    // instance since disposal also removes it from the suspense cache. We add\n    // the queryRef back in the suspense cache so that the next render will reuse\n    // this queryRef rather than initializing a new instance.\n    React.useEffect(function () {\n        // Since the queryRef is disposed async via `setTimeout`, we have to wait a\n        // tick before checking it and adding back to the suspense cache.\n        var id = setTimeout(function () {\n            if (queryRef.disposed) {\n                suspenseCache.add(cacheKey, queryRef);\n            }\n        });\n        return function () { return clearTimeout(id); };\n        // Omitting the deps is intentional. This avoids stale closures and the\n        // conditional ensures we aren't running the logic on each render.\n    });\n    var fetchMore = React.useCallback(function (options) {\n        var promise = queryRef.fetchMore(options);\n        setWrappedQueryRef(wrapQueryRef(queryRef));\n        return promise;\n    }, [queryRef]);\n    var refetch = React.useCallback(function (variables) {\n        var promise = queryRef.refetch(variables);\n        setWrappedQueryRef(wrapQueryRef(queryRef));\n        return promise;\n    }, [queryRef]);\n    React.useEffect(function () { return queryRef.softRetain(); }, [queryRef]);\n    return [\n        didFetchResult.current ? wrappedQueryRef : void 0,\n        {\n            fetchMore: fetchMore,\n            refetch: refetch,\n            // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n            subscribeToMore: queryRef.observable\n                .subscribeToMore,\n        },\n    ];\n}\n//# sourceMappingURL=useBackgroundQuery.js.map", "import { __assign } from \"tslib\";\nimport { canonicalStringify } from \"../../cache/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { getSuspenseCache } from \"../internal/index.js\";\nimport * as React from \"rehackt\";\nimport { __use } from \"./internal/__use.js\";\nimport { wrapHook } from \"./internal/index.js\";\nvar NULL_PLACEHOLDER = [];\nexport function useSuspenseFragment(options) {\n    return wrapHook(\"useSuspenseFragment\", \n    // eslint-disable-next-line react-compiler/react-compiler\n    useSuspenseFragment_, useApolloClient(typeof options === \"object\" ? options.client : undefined))(options);\n}\nfunction useSuspenseFragment_(options) {\n    var client = useApolloClient(options.client);\n    var from = options.from, variables = options.variables;\n    var cache = client.cache;\n    var id = React.useMemo(function () {\n        return typeof from === \"string\" ? from\n            : from === null ? null\n                : cache.identify(from);\n    }, [cache, from]);\n    var fragmentRef = id === null ? null : (getSuspenseCache(client).getFragmentRef([id, options.fragment, canonicalStringify(variables)], client, __assign(__assign({}, options), { variables: variables, from: id })));\n    var _a = React.useState(fragmentRef === null ? NULL_PLACEHOLDER : ([fragmentRef.key, fragmentRef.promise])), current = _a[0], setPromise = _a[1];\n    React.useEffect(function () {\n        if (fragmentRef === null) {\n            return;\n        }\n        var dispose = fragmentRef.retain();\n        var removeListener = fragmentRef.listen(function (promise) {\n            setPromise([fragmentRef.key, promise]);\n        });\n        return function () {\n            dispose();\n            removeListener();\n        };\n    }, [fragmentRef]);\n    if (fragmentRef === null) {\n        return { data: null };\n    }\n    if (current[0] !== fragmentRef.key) {\n        // eslint-disable-next-line react-compiler/react-compiler\n        current[0] = fragmentRef.key;\n        current[1] = fragmentRef.promise;\n    }\n    var data = __use(current[1]);\n    return { data: data };\n}\n//# sourceMappingURL=useSuspenseFragment.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport * as React from \"rehackt\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { assertWrappedQueryRef, getSuspenseCache, unwrapQueryRef, updateWrappedQueryRef, wrapQueryRef, } from \"../internal/index.js\";\nimport { useRenderGuard } from \"./internal/index.js\";\nimport { useWatchQueryOptions } from \"./useSuspenseQuery.js\";\nimport { canonicalStringify } from \"../../cache/index.js\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nexport function useLoadableQuery(query, options) {\n    if (options === void 0) { options = Object.create(null); }\n    var client = useApolloClient(options.client);\n    var suspenseCache = getSuspenseCache(client);\n    var watchQueryOptions = useWatchQueryOptions({ client: client, query: query, options: options });\n    var _a = options.queryKey, queryKey = _a === void 0 ? [] : _a;\n    var _b = React.useState(null), queryRef = _b[0], setQueryRef = _b[1];\n    assertWrappedQueryRef(queryRef);\n    var internalQueryRef = queryRef && unwrapQueryRef(queryRef);\n    if (queryRef && (internalQueryRef === null || internalQueryRef === void 0 ? void 0 : internalQueryRef.didChangeOptions(watchQueryOptions))) {\n        var promise = internalQueryRef.applyOptions(watchQueryOptions);\n        updateWrappedQueryRef(queryRef, promise);\n    }\n    var calledDuringRender = useRenderGuard();\n    var fetchMore = React.useCallback(function (options) {\n        if (!internalQueryRef) {\n            throw new Error(\"The query has not been loaded. Please load the query.\");\n        }\n        var promise = internalQueryRef.fetchMore(options);\n        setQueryRef(wrapQueryRef(internalQueryRef));\n        return promise;\n    }, [internalQueryRef]);\n    var refetch = React.useCallback(function (options) {\n        if (!internalQueryRef) {\n            throw new Error(\"The query has not been loaded. Please load the query.\");\n        }\n        var promise = internalQueryRef.refetch(options);\n        setQueryRef(wrapQueryRef(internalQueryRef));\n        return promise;\n    }, [internalQueryRef]);\n    var loadQuery = React.useCallback(function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        invariant(!calledDuringRender(), 59);\n        var variables = args[0];\n        var cacheKey = __spreadArray([\n            query,\n            canonicalStringify(variables)\n        ], [].concat(queryKey), true);\n        var queryRef = suspenseCache.getQueryRef(cacheKey, function () {\n            return client.watchQuery(__assign(__assign({}, watchQueryOptions), { variables: variables }));\n        });\n        setQueryRef(wrapQueryRef(queryRef));\n    }, [\n        query,\n        queryKey,\n        suspenseCache,\n        watchQueryOptions,\n        calledDuringRender,\n        client,\n    ]);\n    var subscribeToMore = React.useCallback(function (options) {\n        invariant(internalQueryRef, 60);\n        return internalQueryRef.observable.subscribeToMore(\n        // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n        options);\n    }, [internalQueryRef]);\n    var reset = React.useCallback(function () {\n        setQueryRef(null);\n    }, []);\n    return [loadQuery, queryRef, { fetchMore: fetchMore, refetch: refetch, reset: reset, subscribeToMore: subscribeToMore }];\n}\n//# sourceMappingURL=useLoadableQuery.js.map", "import * as React from \"rehackt\";\nimport { assertWrappedQueryRef, getWrappedPromise, unwrapQueryRef, updateWrappedQueryRef, wrapQueryRef, } from \"../internal/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { wrapHook } from \"./internal/index.js\";\n/**\n * A React hook that returns a `refetch` and `fetchMore` function for a given\n * `queryRef`.\n *\n * This is useful to get access to handlers for a `queryRef` that was created by\n * `createQueryPreloader` or when the handlers for a `queryRef` produced in\n * a different component are inaccessible.\n *\n * @example\n * ```tsx\n * const MyComponent({ queryRef }) {\n *   const { refetch, fetchMore } = useQueryRefHandlers(queryRef);\n *\n *   // ...\n * }\n * ```\n * @since 3.9.0\n * @param queryRef - A `QueryRef` returned from `useBackgroundQuery`, `useLoadableQuery`, or `createQueryPreloader`.\n */\nexport function useQueryRefHandlers(queryRef) {\n    var unwrapped = unwrapQueryRef(queryRef);\n    var clientOrObsQuery = useApolloClient(unwrapped ?\n        // passing an `ObservableQuery` is not supported by the types, but it will\n        // return any truthy value that is passed in as an override so we cast the result\n        unwrapped[\"observable\"]\n        : undefined);\n    return wrapHook(\"useQueryRefHandlers\", \n    // eslint-disable-next-line react-compiler/react-compiler\n    useQueryRefHandlers_, clientOrObsQuery)(queryRef);\n}\nfunction useQueryRefHandlers_(queryRef) {\n    assertWrappedQueryRef(queryRef);\n    var _a = React.useState(queryRef), previousQueryRef = _a[0], setPreviousQueryRef = _a[1];\n    var _b = React.useState(queryRef), wrappedQueryRef = _b[0], setWrappedQueryRef = _b[1];\n    var internalQueryRef = unwrapQueryRef(queryRef);\n    // To ensure we can support React transitions, this hook needs to manage the\n    // queryRef state and apply React's state value immediately to the existing\n    // queryRef since this hook doesn't return the queryRef directly\n    if (previousQueryRef !== queryRef) {\n        setPreviousQueryRef(queryRef);\n        setWrappedQueryRef(queryRef);\n    }\n    else {\n        updateWrappedQueryRef(queryRef, getWrappedPromise(wrappedQueryRef));\n    }\n    var refetch = React.useCallback(function (variables) {\n        var promise = internalQueryRef.refetch(variables);\n        setWrappedQueryRef(wrapQueryRef(internalQueryRef));\n        return promise;\n    }, [internalQueryRef]);\n    var fetchMore = React.useCallback(function (options) {\n        var promise = internalQueryRef.fetchMore(options);\n        setWrappedQueryRef(wrapQueryRef(internalQueryRef));\n        return promise;\n    }, [internalQueryRef]);\n    return {\n        refetch: refetch,\n        fetchMore: fetchMore,\n        // TODO: The internalQueryRef doesn't have TVariables' type information so we have to cast it here\n        subscribeToMore: internalQueryRef.observable\n            .subscribeToMore,\n    };\n}\n//# sourceMappingURL=useQueryRefHandlers.js.map", "import * as React from \"rehackt\";\nimport { assertWrappedQueryRef, getWrappedPromise, unwrapQueryRef, updateWrappedQueryRef, } from \"../internal/index.js\";\nimport { __use, wrapHook } from \"./internal/index.js\";\nimport { toApolloError } from \"./useSuspenseQuery.js\";\nimport { useSyncExternalStore } from \"./useSyncExternalStore.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nexport function useReadQuery(queryRef) {\n    var unwrapped = unwrapQueryRef(queryRef);\n    var clientOrObsQuery = useApolloClient(unwrapped ?\n        // passing an `ObservableQuery` is not supported by the types, but it will\n        // return any truthy value that is passed in as an override so we cast the result\n        unwrapped[\"observable\"]\n        : undefined);\n    return wrapHook(\"useReadQuery\", \n    // eslint-disable-next-line react-compiler/react-compiler\n    useReadQuery_, clientOrObsQuery)(queryRef);\n}\nfunction useReadQuery_(queryRef) {\n    assertWrappedQueryRef(queryRef);\n    var internalQueryRef = React.useMemo(function () { return unwrapQueryRef(queryRef); }, [queryRef]);\n    var getPromise = React.useCallback(function () { return getWrappedPromise(queryRef); }, [queryRef]);\n    if (internalQueryRef.disposed) {\n        internalQueryRef.reinitialize();\n        updateWrappedQueryRef(queryRef, internalQueryRef.promise);\n    }\n    React.useEffect(function () { return internalQueryRef.retain(); }, [internalQueryRef]);\n    var promise = useSyncExternalStore(React.useCallback(function (forceUpdate) {\n        return internalQueryRef.listen(function (promise) {\n            updateWrappedQueryRef(queryRef, promise);\n            forceUpdate();\n        });\n    }, [internalQueryRef, queryRef]), getPromise, getPromise);\n    var result = __use(promise);\n    return React.useMemo(function () {\n        return {\n            data: result.data,\n            networkStatus: result.networkStatus,\n            error: toApolloError(result),\n        };\n    }, [result]);\n}\n//# sourceMappingURL=useReadQuery.js.map"], "names": ["context", "React", "getApolloContext", "invariant", "realHook", "canUseLayoutEffect", "equal", "canUseDOM", "noop", "wrapPromiseWithState", "__assign", "verifyDocumentType", "DocumentType", "ObservableQuery", "NetworkStatus", "__rest", "compact", "mergeOptions", "isNonEmptyArray", "ApolloError", "toApolloError", "maybeDeepFreeze", "errors", "Observable", "mergeDeepArray", "getSuspenseCache", "__spread<PERSON><PERSON>y", "canonicalStringify", "wrapQueryRef", "unwrapQueryRef", "updateWrappedQueryRef", "cache", "assertWrappedQueryRef", "getWrappedPromise"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBO,SAAS,eAAe,CAAC,QAAQ,EAAE;AAC1C,IAAI,IAAIA,SAAO,GAAGC,gBAAK,CAAC,UAAU,CAACC,wBAAgB,EAAE,CAAC,CAAC;AACvD,IAAI,IAAI,MAAM,GAAG,QAAQ,IAAIF,SAAO,CAAC,MAAM,CAAC;AAC5C,IAAIG,iBAAS,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;AAC5B,IAAI,OAAO,MAAM,CAAC;AAClB;;ACpBA,IAAI,0BAA0B,GAAG,KAAK,CAAC;AAIvC,IAAI,OAAO,GAAG,sBAAsB,CAAC;AACrC,IAAIC,UAAQ,GAAGH,gBAAK,CAAC,OAAO,CAAC,CAAC;AAIvB,IAAI,oBAAoB,GAAGG,UAAQ;AAC1C,KAAK,UAAU,SAAS,EAAE,WAAW,EAAE,iBAAiB,EAAE;AAK1D,QAAQ,IAAI,KAAK,GAAG,WAAW,EAAE,CAAC;AAClC,QAAQ;AAER,QAAQ,UAAU,CAAC,OAAO,KAAK,KAAK;AACpC,YAAY,CAAC,0BAA0B;AAGvC,YAAY,KAAK,KAAK,WAAW,EAAE,EAAE;AACrC,YAAY,0BAA0B,GAAG,IAAI,CAAC;AAE9C,YAAY,UAAU,CAAC,OAAO,KAAK,KAAK,IAAID,iBAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAChE,SAAS;AAeT,QAAQ,IAAI,EAAE,GAAGF,gBAAK,CAAC,QAAQ,CAAC;AAChC,YAAY,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE;AAC5D,SAAS,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAInD,QAAQ,IAAII,4BAAkB,EAAE;AAKhC,YAAYJ,gBAAK,CAAC,eAAe,CAAC,YAAY;AAC9C,gBAAgB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,CAAC;AAKhF,gBAAgB,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;AAElD,oBAAoB,WAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAChD,iBAAiB;AAGjB,aAAa,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;AAChD,SAAS;AACT,aAAa;AACb,YAAY,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC,CAAC;AAC5E,SAAS;AACT,QAAQA,gBAAK,CAAC,SAAS,CAAC,YAAY;AAGpC,YAAY,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;AAE9C,gBAAgB,WAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5C,aAAa;AAEb,YAAY,OAAO,SAAS,CAAC,SAAS,iBAAiB,GAAG;AAO1D,gBAAgB,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;AAElD,oBAAoB,WAAW,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AAChD,iBAAiB;AACjB,aAAa,CAAC,CAAC;AAGf,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;AACxB,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC,CAAC;AACP,SAAS,sBAAsB,CAAC,EAAE,EAAE;AACpC,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC;AACvD,IAAI,IAAI;AACR,QAAQ,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;AACvC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE;AACf,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL;;ACvGO,SAAS,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;AAC1C,IAAI,IAAI,GAAG,GAAGA,gBAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACnC,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,CAACK,WAAK,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;AAExD,QAAQ,GAAG,CAAC,OAAO,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACtD,KAAK;AACL,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC;AAC7B;;ACFO,IAAI,yBAAyB,GAAGC,mBAAS,GAAGN,gBAAK,CAAC,eAAe,GAAGA,gBAAK,CAAC,SAAS;;ACN1F,IAAI,GAAG,CAAC;AACR,SAASO,MAAI,GAAG,GAAG;AACZ,SAAS,cAAc,GAAG;AACjC,IAAI,IAAI,CAAC,GAAG,EAAE;AAEd,QAAQ,GAAG,GAAGP,gBAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,OAAOA,gBAAK,CAAC,WAAW;AAGrB,CAAC,YAAY;AACpB,QAAQ,IAAI,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC;AACjC,QAAQ,IAAI;AACZ,YAAY,OAAO,CAAC,KAAK,GAAGO,MAAI,CAAC;AAgBjC,YAAYP,gBAAK,CAAC,YAAY,EAAiC,CAAC,GAAG,CAAC,CAAC;AACrE,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE;AAClB,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,gBAAgB;AAChB,YAAY,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AACjC,SAAS;AACT,KAAK,EAAE,EAAE,CAAC,CAAC;AACX;;ACnCA,IAAI,MAAM,GAAG,KAAK,CAAC;AACnB,IAAI,QAAQ,GAAGA,gBAAK,CAAC,MAAM,CAAC,CAAC;AAGtB,IAAI,KAAK,GAAG,QAAQ;AAC3B,IAAI,SAAS,KAAK,CAAC,OAAO,EAAE;AAC5B,QAAQ,IAAI,eAAe,GAAGQ,8BAAoB,CAAC,OAAO,CAAC,CAAC;AAC5D,QAAQ,QAAQ,eAAe,CAAC,MAAM;AACtC,YAAY,KAAK,SAAS;AAC1B,gBAAgB,MAAM,eAAe,CAAC;AACtC,YAAY,KAAK,UAAU;AAC3B,gBAAgB,MAAM,eAAe,CAAC,MAAM,CAAC;AAC7C,YAAY,KAAK,WAAW;AAC5B,gBAAgB,OAAO,eAAe,CAAC,KAAK,CAAC;AAC7C,SAAS;AACT,KAAK;;ACpBL,IAAI,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;AAoChD,SAAS,QAAQ,CAAC,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE;AAC9D,IAAI,IAAI,YAAY,GAAG,gBAAgB,CAAC,cAAc,CAAC,CAAC;AACxD,IAAI,IAAI,QAAQ,GAAG,YAAY,IAAI,YAAY,CAAC,aAAa,CAAC,CAAC;AAC/D,IAAI,IAAI,OAAO,GAAG,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACjD,IAAI,OAAO,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;AAChD;;ACTA,IAAI,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AACrD,SAAS,IAAI,GAAG,GAAG;AACnB,IAAI,gBAAgB,GAAG,MAAM,EAAE,CAAC;AAmCzB,SAAS,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE;AACzC,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC9D,IAAI,OAAO,QAAQ,CAAC,UAAU;AAE9B,IAAI,SAAS,EAAE,eAAe,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC3E,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE,OAAO,EAAE;AACnC,IAAI,IAAI,EAAE,GAAG,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,cAAc,GAAG,EAAE,CAAC,cAAc,CAAC;AACvG,IAAI,OAAOR,gBAAK,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQS,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC;AAC7H,CAAC;AACD,SAAS,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,qBAAqB,EAAE;AACzF,IAAI,SAAS,mBAAmB,CAAC,QAAQ,EAAE;AAC3C,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQC,yBAAkB,CAAC,KAAK,EAAEC,mBAAY,CAAC,KAAK,CAAC,CAAC;AACtD,QAAQ,IAAI,aAAa,GAAG;AAC5B,YAAY,MAAM,EAAE,MAAM;AAC1B,YAAY,KAAK,EAAE,KAAK;AACxB,YAAY,UAAU;AAItB,YAAY,CAAC,cAAc;AAC3B,gBAAgB,cAAc,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,CAAC;AACxE,gBAAgBC,oBAAe,CAAC,oBAAoB,CAAC,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,YAAY;AAC7F,oBAAoB,OAAO,MAAM,CAAC,UAAU,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC,CAAC,CAAC;AACnH,iBAAiB,CAAC;AAClB,YAAY,UAAU,EAAE;AAGxB,gBAAgB,YAAY,EAAE,CAAC,EAAE,GAAG,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI;AACjK,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,OAAO,aAAa,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,EAAE,GAAGZ,gBAAK,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,mBAAmB,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAMrG,IAAI,SAAS,eAAe,CAAC,iBAAiB,EAAE;AAChD,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,EAAE,CAAC;AAGf,QAAQ,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,UAAU,GAAG,EAAE,GAAG,EAAE;AACxD,YAAY,EAAE,CAAC,gBAAgB,CAAC,GAAG,iBAAiB;AACpD,YAAY,EAAE,EAAE,CAAC;AACjB,QAAQ,IAAI,UAAU,GAAG,aAAa,CAAC,UAAU,CAAC;AAClD,QAAQ,mBAAmB,CAACS,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE;AAElE,YAAY,KAAK,EAAE,iBAAiB,CAAC,KAAK,EAAE,UAAU,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;AAGlF,gBAAgB,YAAY,EAAE,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,UAAU,CAAC,YAAY;AACjI,gBAAgB,OAAO,EAAE,SAAS;AAClC,aAAa,CAAC,EAAE,CAAC,CAAC,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,MAAM,KAAK,aAAa,CAAC,MAAM,IAAI,KAAK,KAAK,aAAa,CAAC,KAAK,EAAE;AAO1E,QAAQ,IAAI,gBAAgB,GAAG,mBAAmB,CAAC,aAAa,CAAC,CAAC;AAClE,QAAQ,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;AAC9C,QAAQ,OAAO,CAAC,gBAAgB,EAAE,eAAe,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,OAAO,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;AAC5C,CAAC;AACM,SAAS,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE;AAClD,IAAI,IAAI,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,cAAc,GAAGT,gBAAK,CAAC,UAAU,CAACC,wBAAgB,EAAE,CAAC,CAAC,cAAc,CAAC;AAC7E,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,cAAc,CAAC;AACrC,IAAI,IAAI,qBAAqB,GAAG,MAAM,CAAC,qBAAqB,CAAC;AAC7D,IAAI,IAAI,UAAU,GAAG,OAAO,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAC5D,IAAI,IAAI,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AAChD,IAAI,IAAI,qBAAqB,GAAG,2BAA2B,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC;AAC/F,IAAI,IAAI,EAAE,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,qBAAqB,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,eAAe,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1L,IAAI,IAAI,iBAAiB,GAAG,qBAAqB,CAAC,UAAU,CAAC,CAAC;AAC9D,IAAI,yBAAyB,CAAC,UAAU;AACxC,IAAI,UAAU;AACd,IAAI,MAAM,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;AACxC,IAAI,IAAI,cAAc,GAAGD,gBAAK,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,qBAAqB,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;AAChH,IAAI,wBAAwB,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;AACrE,IAAI,IAAI,MAAM,GAAG,+BAA+B,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,cAAc,EAAE,SAAS,EAAE;AAC/J,QAAQ,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI;AAChD,QAAQ,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,IAAI;AACxC,KAAK,CAAC,CAAC;AACP,IAAI,OAAO;AACX,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,cAAc,EAAE,cAAc;AACtC,QAAQ,UAAU,EAAE,UAAU;AAC9B,QAAQ,UAAU,EAAE,UAAU;AAC9B,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,eAAe,EAAE,eAAe;AACxC,KAAK,CAAC;AACN,CAAC;AACD,SAAS,+BAA+B,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE;AAClK,IAAI,IAAI,WAAW,GAAGA,gBAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAC9C,IAAIA,gBAAK,CAAC,SAAS,CAAC,YAAY;AAQhC,QAAQ,WAAW,CAAC,OAAO,GAAG,SAAS,CAAC;AACxC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,SAAS,IAAI,qBAAqB;AAC7D,QAAQ,OAAO,CAAC,GAAG,KAAK,KAAK;AAC7B,QAAQ,CAAC,OAAO,CAAC,IAAI;AAGrB,QAAQ,iBAAiB;AACzB,UAAU,OAAO,CAAC,IAAI,IAAI,iBAAiB,CAAC,WAAW,KAAK,SAAS;AAWrE,YAAY,iBAAiB;AAC7B,cAAc,KAAK,CAAC,CAAC;AACrB,IAAI,IAAI,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;AAC/C,IAAI,IAAI,qBAAqB,GAAGA,gBAAK,CAAC,OAAO,CAAC,YAAY;AAC1D,QAAQ,OAAO,cAAc;AAC7B,YAAY,aAAa,CAAC,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;AAC5E,KAAK,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,CAAC,CAAC,CAAC;AAC3D,IAAI,OAAO,oBAAoB,CAACA,gBAAK,CAAC,WAAW,CAAC,UAAU,iBAAiB,EAAE;AAI/E,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,OAAO,YAAY,GAAG,CAAC;AACnC,SAAS;AACT,QAAQ,IAAI,MAAM,GAAG,YAAY;AACjC,YAAY,IAAI,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC;AAIpD,YAAY,IAAI,MAAM,GAAG,UAAU,CAAC,gBAAgB,EAAE,CAAC;AAEvD,YAAY,IAAI,cAAc;AAC9B,gBAAgB,cAAc,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO;AACzD,gBAAgB,cAAc,CAAC,aAAa,KAAK,MAAM,CAAC,aAAa;AACrE,gBAAgBK,WAAK,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE;AACzD,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,SAAS,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;AACtH,SAAS,CAAC;AACV,QAAQ,IAAI,OAAO,GAAG,UAAU,KAAK,EAAE;AACvC,YAAY,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;AAC/C,YAAY,YAAY,CAAC,OAAO,GAAG,UAAU,CAAC,qBAAqB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACrF,YAAY,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC,EAAE;AAE9D,gBAAgB,MAAM,KAAK,CAAC;AAC5B,aAAa;AACb,YAAY,IAAI,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC;AACpD,YAAY,IAAI,CAAC,cAAc;AAC/B,iBAAiB,cAAc,IAAI,cAAc,CAAC,OAAO,CAAC;AAC1D,gBAAgB,CAACA,WAAK,CAAC,KAAK,EAAE,cAAc,CAAC,KAAK,CAAC,EAAE;AACrD,gBAAgB,SAAS,CAAC;AAC1B,oBAAoB,IAAI,GAAG,cAAc;AACzC,wBAAwB,cAAc,CAAC,IAAI,CAAC;AAC5C,oBAAoB,KAAK,EAAE,KAAK;AAChC,oBAAoB,OAAO,EAAE,KAAK;AAClC,oBAAoB,aAAa,EAAEQ,kBAAa,CAAC,KAAK;AACtD,iBAAiB,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;AAC3G,aAAa;AACb,SAAS,CAAC;AAMV,QAAQ,IAAI,YAAY,GAAG,EAAE,OAAO,EAAE,UAAU,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC;AAK9E,QAAQ,OAAO,YAAY;AAC3B,YAAY,UAAU,CAAC,YAAY,EAAE,OAAO,YAAY,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;AACnF,SAAS,CAAC;AACV,KAAK,EAAE;AACP,QAAQ,qBAAqB;AAC7B,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAClB,QAAQ,cAAc;AACtB,QAAQ,MAAM;AACd,KAAK,CAAC,EAAE,YAAY;AACpB,QAAQ,OAAO,qBAAqB;AACpC,YAAY,gBAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,OAAO,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;AAClG,KAAK,EAAE,YAAY;AACnB,QAAQ,OAAO,qBAAqB;AACpC,YAAY,gBAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,OAAO,EAAE,cAAc,EAAE,MAAM,CAAC,CAAC;AAClG,KAAK,CAAC,CAAC;AACP,CAAC;AACD,SAAS,wBAAwB,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE;AAC1E,IAAI,IAAI,cAAc,IAAI,UAAU,EAAE;AACtC,QAAQ,cAAc,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;AACzD,QAAQ,IAAI,UAAU,CAAC,gBAAgB,EAAE,CAAC,OAAO,EAAE;AAEnD,YAAY,cAAc,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;AACjE,SAAS;AACT,KAAK;AACL,CAAC;AAGD,SAAS,yBAAyB;AAElC,UAAU;AAEV,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE;AAChD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,UAAU,CAAC,gBAAgB,CAAC;AACpC,QAAQ,CAACR,WAAK,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,iBAAiB,CAAC,EAAE;AASjE,QAAQ,UAAU,CAAC,SAAS,CAAC,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAIjG,QAAQ,UAAU,CAAC,YAAY;AAC/B,YAAY,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,KAAK,UAAU,CAAC,YAAY,CAAC;AAChH,QAAQ,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,UAAU,CAAC,gBAAgB,CAAC,GAAG,iBAAiB,CAAC;AACrD,CAAC;AAMM,SAAS,2BAA2B,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,SAAS,EAAE;AAC1E,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE;AACnC,IAAO,IAAC,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,CAAO,EAAE,CAAC,GAAG,CAAC,CAAe,EAAE,CAAC,WAAW,CAAC,CAAW,EAAE,CAAC,OAAO,CAAC,KAAC,cAAc,GAAG,EAAE,CAAC,cAAc,CAAC;AAI7H,IAAI,YAAY,GAAGS,YAAM,CAAC,EAAE,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,gBAAgB,CAAC,EAAE;AAC3F,IAAI,OAAO,UAAU,UAAU,EAAE;AAGjC,QAAQ,IAAI,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AAC9E,QAAQ,IAAI,SAAS;AACrB,aAAa,iBAAiB,CAAC,WAAW,KAAK,cAAc;AAC7D,gBAAgB,iBAAiB,CAAC,WAAW,KAAK,mBAAmB,CAAC,EAAE;AAGxE,YAAY,iBAAiB,CAAC,WAAW,GAAG,aAAa,CAAC;AAC1D,SAAS;AACT,QAAQ,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE;AAC1C,YAAY,iBAAiB,CAAC,SAAS,GAAG,EAAE,CAAC;AAC7C,SAAS;AACT,QAAQ,IAAI,IAAI,EAAE;AAIlB,YAAY,iBAAiB,CAAC,kBAAkB;AAChD,gBAAgB,iBAAiB,CAAC,kBAAkB;AACpD,oBAAoB,iBAAiB,CAAC,WAAW;AACjD,oBAAoB,qBAAqB,CAAC,cAAc,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;AACjF,YAAY,iBAAiB,CAAC,WAAW,GAAG,SAAS,CAAC;AACtD,SAAS;AACT,aAAa,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE;AACjD,YAAY,iBAAiB,CAAC,WAAW;AACzC,gBAAgB,CAAC,UAAU,KAAK,IAAI,IAAI,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,kBAAkB;AAC9G,oBAAoB,qBAAqB,CAAC,cAAc,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;AACjF,SAAS;AACT,QAAQ,OAAO,iBAAiB,CAAC;AACjC,KAAK,CAAC;AACN,CAAC;AACM,SAAS,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,EAAE;AAC5F,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC;AAC1D,IAAI,IAAI,cAAc;AACtB,QAAQ,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AACrC,IAAI,IAAI,gBAAgB,CAAC,cAAc,EAAE;AACzC,QAAQ,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;AACtD,KAAK;AAWL,IAAI,OAAO,CAAC,IAAI,CAACC,iBAAO,CAAC,UAAU,IAAI,UAAU,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;AAC/E,IAAI,OAAO,OAAO,CAAC,MAAM,CAACC,sBAAY,CAAC,CAAC;AACxC,CAAC;AACD,SAAS,SAAS,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE;AACvG,IAAI,IAAI,cAAc,GAAG,UAAU,CAAC,OAAO,CAAC;AAC5C,IAAI,IAAI,cAAc,IAAI,cAAc,CAAC,IAAI,EAAE;AAC/C,QAAQ,UAAU,CAAC,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC;AACtD,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,IAAIC,yBAAe,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;AAKjE,QAAQ,UAAU,CAAC,KAAK,GAAG,IAAIC,kBAAW,CAAC,EAAE,aAAa,EAAE,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;AACjF,KAAK;AACL,IAAI,UAAU,CAAC,OAAO,GAAG,aAAa,CAAC,0BAA0B,CAAC,UAAU,EAAE,UAAU,EAAE,cAAc,CAAC,EAAE,UAAU,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;AAGxJ,IAAI,WAAW,EAAE,CAAC;AAClB,IAAI,sBAAsB,CAAC,UAAU,EAAE,cAAc,KAAK,IAAI,IAAI,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;AAChJ,CAAC;AACD,SAAS,sBAAsB,CAAC,MAAM,EAAE,qBAAqB,EAAE,SAAS,EAAE;AAC1E,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;AACzB,QAAQ,IAAI,OAAO,GAAGC,eAAa,CAAC,MAAM,CAAC,CAAC;AAE5C,QAAQ,OAAO,CAAC,OAAO,EAAE;AACzB,aAAa,IAAI,CAAC,YAAY;AAC9B,YAAY,IAAI,OAAO,EAAE;AACzB,gBAAgB,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC3C,aAAa;AACb,iBAAiB,IAAI,MAAM,CAAC,IAAI;AAChC,gBAAgB,qBAAqB,KAAK,MAAM,CAAC,aAAa;AAC9D,gBAAgB,MAAM,CAAC,aAAa,KAAKN,kBAAa,CAAC,KAAK,EAAE;AAC9D,gBAAgB,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACnD,aAAa;AACb,SAAS,CAAC;AACV,aAAa,KAAK,CAAC,UAAU,KAAK,EAAE;AACpC,YAAY,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIX,iBAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClE,SAAS,CAAC,CAAC;AACX,KAAK;AACL,CAAC;AACD,SAAS,gBAAgB,CAAC,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE;AAIrF,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;AAG7B,QAAQ,SAAS,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,GAAG,EAAE,SAAS,CAAC,CAAC;AAC7H,KAAK;AACL,IAAI,OAAO,UAAU,CAAC,OAAO,CAAC;AAC9B,CAAC;AACM,SAAS,qBAAqB,CAAC,uBAAuB,EAAE,oBAAoB,EAAE;AACrF,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,QAAQ,CAAC,uBAAuB,KAAK,IAAI,IAAI,uBAAuB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,uBAAuB,CAAC,WAAW;AAClI,SAAS,CAAC,EAAE,GAAG,oBAAoB,KAAK,IAAI,IAAI,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,oBAAoB,CAAC,UAAU,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC;AAChL,QAAQ,aAAa,EAAE;AACvB,CAAC;AACM,SAASiB,eAAa,CAAC,MAAM,EAAE;AACtC,IAAI,OAAOF,yBAAe,CAAC,MAAM,CAAC,MAAM,CAAC;AACzC,QAAQ,IAAIC,kBAAW,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;AACzD,UAAU,MAAM,CAAC,KAAK,CAAC;AACvB,CAAC;AACM,SAAS,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE;AACxE,IAAO,IAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAW,MAAM,CAAC,OAAO,CAAC,KAAC,oBAAoB,GAAGJ,YAAM,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;AACjH,IAAI,IAAI,WAAW,GAAGL,cAAQ,CAACA,cAAQ,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,oBAAoB,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,KAAK,iBAAiB,IAAI,MAAM,KAAK,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC,CAAC;AAC9P,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC;AACD,SAAS,0BAA0B,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE;AAIxE,IAAI,IAAI,MAAM,CAAC,OAAO;AACtB,QAAQ,cAAc;AACtB,QAAQ,CAAC,MAAM,CAAC,OAAO;AACvB,SAAS,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;AAC/D,QAAQ,UAAU,CAAC,OAAO,CAAC,WAAW,KAAK,YAAY,EAAE;AACzD,QAAQ,UAAU,CAAC,OAAO,EAAE,CAAC;AAC7B,QAAQ,OAAOA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAEI,kBAAa,CAAC,OAAO,EAAE,CAAC,CAAC;AACvG,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AACD,IAAI,iBAAiB,GAAGO,yBAAe,CAAC;AACxC,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,IAAI,EAAE,KAAK,CAAC;AAChB,IAAI,KAAK,EAAE,KAAK,CAAC;AACjB,IAAI,aAAa,EAAEP,kBAAa,CAAC,OAAO;AACxC,CAAC,CAAC,CAAC;AACH,IAAI,iBAAiB,GAAGO,yBAAe,CAAC;AACxC,IAAI,OAAO,EAAE,KAAK;AAClB,IAAI,IAAI,EAAE,KAAK,CAAC;AAChB,IAAI,KAAK,EAAE,KAAK,CAAC;AACjB,IAAI,aAAa,EAAEP,kBAAa,CAAC,KAAK;AACtC,CAAC,CAAC,CAAC;AACH,SAAS,qBAAqB,CAAC,UAAU,EAAE;AAC3C,IAAI,OAAO;AACX,QAAQ,OAAO,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;AACpD,QAAQ,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;AACxD,QAAQ,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;AACxD,QAAQ,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5D,QAAQ,YAAY,EAAE,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9D,QAAQ,WAAW,EAAE,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5D,QAAQ,eAAe,EAAE,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;AACpE,KAAK,CAAC;AACN;;ACtdA,IAAI,aAAa,GAAG;AACpB,IAAI,SAAS;AACb,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,iBAAiB;AACrB,CAAC,CAAC;AAoCK,SAAS,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE;AAC7C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,cAAc,GAAGb,gBAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAC9C,IAAI,IAAI,UAAU,GAAGA,gBAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,QAAQ,GAAGA,gBAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACxC,IAAI,IAAI,MAAM,GAAGgB,sBAAY,CAAC,OAAO,EAAE,cAAc,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;AACrE,IAAI,IAAI,QAAQ,GAAG,CAAC,EAAE,GAAG,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC;AAG9H,IAAI,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;AACjC,IAAI,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC;AAChC,IAAI,IAAI,gBAAgB,GAAGP,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC;AAC7F,IAAI,IAAI,EAAE,GAAG,iBAAiB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,EAAE,cAAc,GAAG,EAAE,CAAC,cAAc,EAAE,cAAc,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,eAAe,GAAG,EAAE,CAAC,eAAe,CAAC;AAC7O,IAAI,IAAI,kBAAkB,GAAG,UAAU,CAAC,OAAO,CAAC,kBAAkB;AAClE,QAAQ,qBAAqB,CAAC,gBAAgB,CAAC,cAAc,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;AACtF,IAAI,IAAI,gBAAgB,GAAGT,gBAAK,CAAC,UAAU,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAExF,IAAI,IAAI,YAAY,GAAGA,gBAAK,CAAC,OAAO,CAAC,YAAY;AACjD,QAAQ,IAAI,YAAY,GAAG,EAAE,CAAC;AAC9B,QAAQ,IAAI,OAAO,GAAG,UAAU,GAAG,EAAE;AACrC,YAAY,IAAI,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;AAC7C,YAAY,YAAY,CAAC,GAAG,CAAC,GAAG,YAAY;AAC5C,gBAAgB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;AAC7C,oBAAoB,cAAc,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAEjE,oBAAoB,gBAAgB,EAAE,CAAC;AACvC,iBAAiB;AAEjB,gBAAgB,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACrD,aAAa,CAAC;AACd,SAAS,CAAC;AACV,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,eAAe,GAAG,aAAa,EAAE,EAAE,GAAG,eAAe,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAC7F,YAAY,IAAI,GAAG,GAAG,eAAe,CAAC,EAAE,CAAC,CAAC;AAC1C,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC;AACzB,SAAS;AACT,QAAQ,OAAO,YAAY,CAAC;AAC5B,KAAK,EAAE,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC,CAAC;AAC3C,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC;AAC1C,IAAI,IAAI,MAAM,GAAGA,gBAAK,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQS,cAAQ,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,cAAc,CAAC,EAAE,YAAY,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;AACrL,IAAI,IAAI,OAAO,GAAGT,gBAAK,CAAC,WAAW,CAAC,UAAU,cAAc,EAAE;AAC9D,QAAQ,cAAc,CAAC,OAAO;AAC9B,YAAY,cAAc,GAAGS,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,WAAW,EAAE,cAAc,CAAC,WAAW,IAAI,kBAAkB,EAAE,CAAC,GAAG;AACzI,gBAAgB,WAAW,EAAE,kBAAkB;AAC/C,aAAa,CAAC;AACd,QAAQ,IAAI,OAAO,GAAGO,sBAAY,CAAC,UAAU,CAAC,OAAO,EAAEP,cAAQ,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,OAAO,EAAE,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;AACtH,QAAQ,IAAI,OAAO,GAAG,YAAY,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAEA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,IAAI,CAAC,UAAU,WAAW,EAAE,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;AAGlO,QAAQ,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;AACvC,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,EAAE;AACP,QAAQ,MAAM;AACd,QAAQ,QAAQ;AAChB,QAAQ,YAAY;AACpB,QAAQ,kBAAkB;AAC1B,QAAQ,UAAU;AAClB,QAAQ,UAAU;AAClB,QAAQ,eAAe;AACvB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,UAAU,GAAGT,gBAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC3C,IAAI,yBAAyB,CAAC,YAAY;AAC1C,QAAQ,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;AACrC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,aAAa,GAAGA,gBAAK,CAAC,WAAW,CAAC,YAAY;AACtD,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AACtB,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC1D,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,IAAI,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACnC,CAAC;AACD,SAAS,YAAY,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,eAAe,EAAE;AAC9F,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,YAAY,CAAC;AAC9C,IAAI,IAAI,iBAAiB,GAAG,2BAA2B,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC;AACnG,IAAI,IAAI,OAAO,GAAG,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC,CAAC;AACpH,IAAI,eAAe,CAAC,iBAAiB,CAAC,CAAC;AACvC,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE;AAC1C,QAAQ,IAAI,MAAM,CAAC;AAKnB,QAAQ,OAAO,CAAC,SAAS,CAAC;AAC1B,YAAY,IAAI,EAAE,UAAU,KAAK,EAAE;AACnC,gBAAgB,MAAM,GAAG,KAAK,CAAC;AAC/B,aAAa;AACb,YAAY,KAAK,EAAE,YAAY;AAC/B,gBAAgB,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,UAAU,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;AACnH,aAAa;AACb,YAAY,QAAQ,EAAE,YAAY;AAClC,gBAAgB,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,EAAE,UAAU,CAAC,YAAY,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC;AACtH,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP;;AC3FO,SAAS,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE;AAC/C,IAAI,IAAI,MAAM,GAAG,eAAe,CAAC,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AACnG,IAAIU,yBAAkB,CAAC,QAAQ,EAAEC,mBAAY,CAAC,QAAQ,CAAC,CAAC;AACxD,IAAI,IAAI,EAAE,GAAGX,gBAAK,CAAC,QAAQ,CAAC;AAC5B,QAAQ,MAAM,EAAE,KAAK;AACrB,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,MAAM,EAAE,MAAM;AACtB,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,GAAG,GAAGA,gBAAK,CAAC,MAAM,CAAC;AAC3B,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,UAAU,EAAE,CAAC;AACrB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,QAAQ,EAAE,QAAQ;AAC1B,QAAQ,OAAO,EAAE,OAAO;AACxB,KAAK,CAAC,CAAC;AACP,IAAI,yBAAyB,CAAC,YAAY;AAC1C,QAAQ,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC7F,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,OAAO,GAAGA,gBAAK,CAAC,WAAW,CAAC,UAAU,cAAc,EAAE;AAC9D,QAAQ,IAAI,cAAc,KAAK,KAAK,CAAC,EAAE,EAAE,cAAc,GAAG,EAAE,CAAC,EAAE;AAC/D,QAAQ,IAAI,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,CAAC;AAC3E,QAAQ,IAAI,WAAW,GAAGS,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;AAClF,QAAQ,IAAI,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC;AACjE,QAAQ,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO;AACvC,YAAY,CAAC,WAAW,CAAC,aAAa;AACtC,YAAY,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE;AACnC,YAAY,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG;AAC5C,gBAAgB,OAAO,EAAE,IAAI;AAC7B,gBAAgB,KAAK,EAAE,KAAK,CAAC;AAC7B,gBAAgB,IAAI,EAAE,KAAK,CAAC;AAC5B,gBAAgB,MAAM,EAAE,IAAI;AAC5B,gBAAgB,MAAM,EAAE,MAAM;AAC9B,aAAa,EAAE,CAAC;AAChB,SAAS;AACT,QAAQ,IAAI,UAAU,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC;AAClD,QAAQ,IAAI,aAAa,GAAGO,sBAAY,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AACtE,QAAQ,OAAO,MAAM;AACrB,aAAa,MAAM,CAAC,aAAa,CAAC;AAClC,aAAa,IAAI,CAAC,UAAU,QAAQ,EAAE;AACtC,YAAY,IAAI,EAAE,EAAE,EAAE,CAAC;AACvB,YAAY,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAEK,QAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC/D,YAAY,IAAI,KAAK,GAAGA,QAAM,IAAIA,QAAM,CAAC,MAAM,GAAG,CAAC;AACnD,gBAAgB,IAAIH,kBAAW,CAAC,EAAE,aAAa,EAAEG,QAAM,EAAE,CAAC;AAC1D,kBAAkB,KAAK,CAAC,CAAC;AACzB,YAAY,IAAI,OAAO,GAAG,cAAc,CAAC,OAAO,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC;AACjI,YAAY,IAAI,KAAK,IAAI,OAAO,EAAE;AAClC,gBAAgB,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AAC9C,aAAa;AACb,YAAY,IAAI,UAAU,KAAK,GAAG,CAAC,OAAO,CAAC,UAAU;AACrD,gBAAgB,CAAC,aAAa,CAAC,aAAa,EAAE;AAC9C,gBAAgB,IAAI,QAAQ,GAAG;AAC/B,oBAAoB,MAAM,EAAE,IAAI;AAChC,oBAAoB,OAAO,EAAE,KAAK;AAClC,oBAAoB,IAAI,EAAE,IAAI;AAC9B,oBAAoB,KAAK,EAAE,KAAK;AAChC,oBAAoB,MAAM,EAAE,MAAM;AAClC,iBAAiB,CAAC;AAClB,gBAAgB,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,CAAChB,WAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;AACnF,oBAAoB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;AAC/D,iBAAiB;AACjB,aAAa;AACb,YAAY,IAAI,WAAW,GAAG,cAAc,CAAC,WAAW,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,CAAC;AAC7I,YAAY,IAAI,CAAC,KAAK,EAAE;AACxB,gBAAgB,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;AACpH,aAAa;AACb,YAAY,OAAO,QAAQ,CAAC;AAC5B,SAAS,EAAE,UAAU,KAAK,EAAE;AAC5B,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,IAAI,UAAU,KAAK,GAAG,CAAC,OAAO,CAAC,UAAU;AACrD,gBAAgB,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE;AACvC,gBAAgB,IAAI,QAAQ,GAAG;AAC/B,oBAAoB,OAAO,EAAE,KAAK;AAClC,oBAAoB,KAAK,EAAE,KAAK;AAChC,oBAAoB,IAAI,EAAE,KAAK,CAAC;AAChC,oBAAoB,MAAM,EAAE,IAAI;AAChC,oBAAoB,MAAM,EAAE,MAAM;AAClC,iBAAiB,CAAC;AAClB,gBAAgB,IAAI,CAACA,WAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE;AAC1D,oBAAoB,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;AAC/D,iBAAiB;AACjB,aAAa;AACb,YAAY,IAAI,OAAO,GAAG,cAAc,CAAC,OAAO,KAAK,CAAC,EAAE,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC;AACjI,YAAY,IAAI,OAAO,EAAE;AACzB,gBAAgB,OAAO,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AAE9C,gBAAgB,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AACvD,aAAa;AACb,YAAY,MAAM,KAAK,CAAC;AACxB,SAAS,CAAC,CAAC;AACX,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,IAAI,IAAI,KAAK,GAAGL,gBAAK,CAAC,WAAW,CAAC,YAAY;AAC9C,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE;AACnC,YAAY,IAAI,QAAQ,GAAG;AAC3B,gBAAgB,MAAM,EAAE,KAAK;AAC7B,gBAAgB,OAAO,EAAE,KAAK;AAC9B,gBAAgB,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM;AAC1C,aAAa,CAAC;AACd,YAAY,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC5E,YAAY,SAAS,CAAC,QAAQ,CAAC,CAAC;AAChC,SAAS;AACT,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,IAAIA,gBAAK,CAAC,SAAS,CAAC,YAAY;AAChC,QAAQ,IAAI,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AAElC,QAAQ,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;AACjC,QAAQ,OAAO,YAAY;AAC3B,YAAY,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;AACtC,SAAS,CAAC;AACV,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,IAAI,OAAO,CAAC,OAAO,EAAES,cAAQ,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC;AACzD;;AC1EO,SAAS,eAAe,CAAC,YAAY,EAAE,OAAO,EAAE;AACvD,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC9D,IAAI,IAAI,8BAA8B,GAAGT,gBAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC7D,IAAI,IAAI,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjD,IAAIU,yBAAkB,CAAC,YAAY,EAAEC,mBAAY,CAAC,YAAY,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,8BAA8B,CAAC,OAAO,EAAE;AAEjD,QAAQ,8BAA8B,CAAC,OAAO,GAAG,IAAI,CAAC;AACtD,QAAQ,IAAI,OAAO,CAAC,kBAAkB,EAAE;AACxC,YAAY,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIT,iBAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACrF,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,sBAAsB,EAAE;AAC5C,YAAY,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIA,iBAAS,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AACzF,SAAS;AACT,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,EAAE,UAAU,GAAG,OAAO,CAAC,UAAU,EAAE,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AACpP,IAAI,IAAI,SAAS,GAAG,WAAW,CAAC,YAAY,EAAE,OAAO,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC;AAChG,IAAI,IAAI,QAAQ,GAAG,YAAY;AAC/B,QAAQ,OAAO,kBAAkB,CAAC,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;AAClH,KAAK,CAAC;AACN,IAAI,IAAI,EAAE,GAAGF,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,GAAG,QAAQ,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACvG,IAAI,IAAI,WAAW,GAAGA,gBAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7C,IAAI,yBAAyB,CAAC,YAAY;AAC1C,QAAQ,WAAW,CAAC,OAAO,GAAG,QAAQ,CAAC;AACvC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,IAAI,EAAE;AACd,QAAQ,IAAI,UAAU,EAAE;AACxB,YAAY,aAAa,EAAE,UAAU,GAAG,IAAI,EAAE,CAAC;AAC/C,SAAS;AACT,KAAK;AACL,SAAS,IAAI,CAAC,UAAU;AACxB,SAAS,CAAC,MAAM,KAAK,UAAU,CAAC,EAAE,CAAC,MAAM;AACzC,YAAY,YAAY,KAAK,UAAU,CAAC,EAAE,CAAC,KAAK;AAChD,YAAY,WAAW,KAAK,UAAU,CAAC,EAAE,CAAC,WAAW;AACrD,YAAY,WAAW,KAAK,UAAU,CAAC,EAAE,CAAC,WAAW;AACrD,YAAY,CAACK,WAAK,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;AACtD,YAAY,CAAC,OAAO,iBAAiB,KAAK,UAAU;AACpD,gBAAgB,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC;AAC5C,kBAAkB,iBAAiB,MAAM,KAAK,CAAC,EAAE;AACjD,QAAQ,aAAa,EAAE,UAAU,GAAG,QAAQ,EAAE,EAAE,CAAC;AACjD,KAAK;AACL,IAAI,IAAI,UAAU,GAAGL,gBAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC3C,IAAIA,gBAAK,CAAC,SAAS,CAAC,YAAY;AAChC,QAAQ,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;AACrC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,eAAe,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC;AAClD,IAAI,IAAI,cAAc,GAAGA,gBAAK,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ;AAC7D,QAAQ,OAAO,EAAE,eAAe;AAChC,QAAQ,KAAK,EAAE,KAAK,CAAC;AACrB,QAAQ,IAAI,EAAE,KAAK,CAAC;AACpB,QAAQ,SAAS,EAAE,SAAS;AAC5B,KAAK,EAAE,EAAE,EAAE,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC,CAAC;AACzC,IAAI,IAAI,gBAAgB,GAAGA,gBAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACvD,IAAI,yBAAyB,CAAC,YAAY;AAS1C,QAAQ,gBAAgB,CAAC,OAAO,GAAG,aAAa,CAAC;AACjD,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,GAAG,GAAG,oBAAoB,CAACA,gBAAK,CAAC,WAAW,CAAC,UAAU,MAAM,EAAE;AACvE,QAAQ,IAAI,CAAC,UAAU,EAAE;AACzB,YAAY,OAAO,YAAY,GAAG,CAAC;AACnC,SAAS;AACT,QAAQ,IAAI,mBAAmB,GAAG,KAAK,CAAC;AACxC,QAAQ,IAAI,SAAS,GAAG,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;AAChD,QAAQ,IAAI,MAAM,GAAG,UAAU,CAAC,EAAE,CAAC,MAAM,CAAC;AAC1C,QAAQ,IAAI,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC;AAChD,YAAY,IAAI,EAAE,UAAU,WAAW,EAAE;AACzC,gBAAgB,IAAI,EAAE,EAAE,EAAE,CAAC;AAC3B,gBAAgB,IAAI,mBAAmB,EAAE;AACzC,oBAAoB,OAAO;AAC3B,iBAAiB;AACjB,gBAAgB,IAAI,MAAM,GAAG;AAC7B,oBAAoB,OAAO,EAAE,KAAK;AAGlC,oBAAoB,IAAI,EAAE,WAAW,CAAC,IAAI;AAC1C,oBAAoB,KAAK,EAAEmB,eAAa,CAAC,WAAW,CAAC;AACrD,oBAAoB,SAAS,EAAE,SAAS;AACxC,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAChD,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,OAAO;AAC7C,oBAAoB,MAAM,EAAE,CAAC;AAC7B,gBAAgB,IAAI,MAAM,CAAC,KAAK,EAAE;AAClC,oBAAoB,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,OAAO,EAAE,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5H,iBAAiB;AACjB,qBAAqB,IAAI,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE;AACpD,oBAAoB,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AAC9C,wBAAwB,MAAM,EAAE,MAAM;AACtC,wBAAwB,IAAI,EAAE,MAAM;AACpC,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,qBAAqB,IAAI,UAAU,CAAC,OAAO,CAAC,kBAAkB,EAAE;AAChE,oBAAoB,UAAU,CAAC,OAAO,CAAC,kBAAkB,CAAC;AAC1D,wBAAwB,MAAM,EAAE,MAAM;AACtC,wBAAwB,gBAAgB,EAAE,MAAM;AAChD,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,aAAa;AACb,YAAY,KAAK,EAAE,UAAU,KAAK,EAAE;AACpC,gBAAgB,IAAI,EAAE,EAAE,EAAE,CAAC;AAC3B,gBAAgB,KAAK;AACrB,oBAAoB,KAAK,YAAYD,gBAAW,GAAG,KAAK,IAAI,IAAIA,gBAAW,CAAC,EAAE,cAAc,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1G,gBAAgB,IAAI,CAAC,mBAAmB,EAAE;AAC1C,oBAAoB,UAAU,CAAC,EAAE,CAAC,SAAS,CAAC;AAC5C,wBAAwB,OAAO,EAAE,KAAK;AACtC,wBAAwB,IAAI,EAAE,KAAK,CAAC;AACpC,wBAAwB,KAAK,EAAE,KAAK;AACpC,wBAAwB,SAAS,EAAE,SAAS;AAC5C,qBAAqB,CAAC,CAAC;AACvB,oBAAoB,IAAI,CAAC,gBAAgB,CAAC,OAAO;AACjD,wBAAwB,MAAM,EAAE,CAAC;AACjC,oBAAoB,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,UAAU,CAAC,OAAO,EAAE,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACrH,iBAAiB;AACjB,aAAa;AACb,YAAY,QAAQ,EAAE,YAAY;AAClC,gBAAgB,IAAI,CAAC,mBAAmB,EAAE;AAC1C,oBAAoB,IAAI,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE;AACvD,wBAAwB,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;AACxD,qBAAqB;AACrB,yBAAyB,IAAI,UAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE;AACxE,wBAAwB,UAAU,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC;AACpE,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,YAAY;AAI3B,YAAY,mBAAmB,GAAG,IAAI,CAAC;AACvC,YAAY,UAAU,CAAC,YAAY;AACnC,gBAAgB,YAAY,CAAC,WAAW,EAAE,CAAC;AAC3C,aAAa,CAAC,CAAC;AACf,SAAS,CAAC;AACV,KAAK,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,YAAY;AAClC,QAAQ,OAAO,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,aAAa;AACpD,YAAY,UAAU,CAAC,EAAE,CAAC,MAAM;AAChC,cAAc,cAAc,CAAC;AAC7B,KAAK,EAAE,YAAY,EAAE,OAAO,cAAc,CAAC,EAAE,CAAC,CAAC;AAC/C,IAAI,IAAI,OAAO,GAAGlB,gBAAK,CAAC,WAAW,CAAC,YAAY;AAChD,QAAQE,iBAAS,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAChD,QAAQ,aAAa,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC,CAAC;AAC7C,KAAK,EAAE,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC;AAClC,IAAI,OAAOF,gBAAK,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQS,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AACtH,CAAC;AACD,SAAS,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE;AACrG,IAAI,IAAI,OAAO,GAAG;AAClB,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,SAAS,EAAE,SAAS;AAC5B,QAAQ,WAAW,EAAE,WAAW;AAChC,QAAQ,WAAW,EAAE,WAAW;AAChC,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,UAAU,EAAE,UAAU;AAC9B,KAAK,CAAC;AACN,IAAI,IAAI,EAAE,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AACvE,YAAY,OAAO,EAAE,IAAI;AACzB,YAAY,IAAI,EAAE,KAAK,CAAC;AACxB,YAAY,KAAK,EAAE,KAAK,CAAC;AACzB,YAAY,SAAS,EAAE,SAAS;AAChC,SAAS,EAAE,SAAS,EAAE,UAAU,MAAM,EAAE;AACxC,YAAY,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;AAC/B,SAAS,EAAE,CAAC,CAAC;AACb,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC;AAC1B,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,IAAIa,eAAU,CAAC,UAAU,QAAQ,EAAE;AAG5D,QAAQ,IAAI,CAAC,UAAU,EAAE;AACzB,YAAY,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACnD,SAAS;AACT,QAAQ,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACjD,QAAQ,OAAO,YAAY,EAAE,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;AACzD,KAAK,CAAC,EAAE;AAIR,QAAQ,EAAE,EAAE,EAAE;AACd,KAAK,CAAC,CAAC;AACP;;AChQO,SAAS,cAAc,CAAC,EAAE,EAAE;AACnC,IAAI,OAAO,oBAAoB,CAACtB,gBAAK,CAAC,WAAW,CAAC,UAAU,MAAM,EAAE;AAMpE,QAAQ,OAAO,EAAE,CAAC,YAAY,CAAC,SAAS,MAAM,GAAG;AACjD,YAAY,MAAM,EAAE,CAAC;AACrB,YAAY,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACpC,SAAS,CAAC,CAAC;AACX,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AACtB;;ACxBO,SAAS,WAAW,CAAC,OAAO,EAAE;AACrC,IAAI,OAAO,QAAQ,CAAC,aAAa;AAEjC,IAAI,YAAY,EAAE,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AAC5D,CAAC;AACD,SAAS,YAAY,CAAC,OAAO,EAAE;AAC/B,IAAI,IAAI,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAC7B,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,IAAI,GAAGc,YAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAK9D,IAAI,IAAI,EAAE,GAAGd,gBAAK,CAAC,OAAO,CAAC,YAAY;AACvC,QAAQ,OAAO,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI;AAC9C,cAAc,IAAI,KAAK,IAAI,GAAG,IAAI;AAClC,kBAAkB,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvC,KAAK,EAAE,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AACtB,IAAI,IAAI,aAAa,GAAG,WAAW,CAAC,YAAY,EAAE,QAAQS,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;AAGtH,IAAI,IAAI,IAAI,GAAGT,gBAAK,CAAC,OAAO,CAAC,YAAY;AACzC,QAAQ,IAAI,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE,YAAY,GAAG,aAAa,CAAC,YAAY,EAAE,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,EAAE,GAAG,aAAa,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;AAC3L,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE;AAC3B,YAAY,OAAO;AACnB,gBAAgB,MAAM,EAAE,YAAY,CAAC;AACrC,oBAAoB,MAAM,EAAE,EAAE;AAC9B,oBAAoB,QAAQ,EAAE,KAAK;AACnC,iBAAiB,CAAC;AAClB,aAAa,CAAC;AACd,SAAS;AACT,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AACjC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAACS,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE,YAAY,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;AAC5L,QAAQ,OAAO;AACf,YAAY,MAAM,EAAE,YAAY,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC,YAAY,CAAC;AAC5G,oBAAoB,QAAQ,EAAE,QAAQ;AACtC,oBAAoB,YAAY,EAAE,YAAY;AAC9C,oBAAoB,IAAI,EAAE,IAAI,CAAC,MAAM;AACrC,iBAAiB,CAAC,EAAE,CAAC,CAAC;AACtB,SAAS,CAAC;AACV,KAAK,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;AAEhC,IAAI,IAAI,WAAW,GAAGT,gBAAK,CAAC,WAAW,CAAC,YAAY,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;AACrF,IAAI,OAAO,oBAAoB,CAACA,gBAAK,CAAC,WAAW,CAAC,UAAU,WAAW,EAAE;AACzE,QAAQ,IAAI,WAAW,GAAG,CAAC,CAAC;AAC5B,QAAQ,IAAI,YAAY,GAAG,aAAa,CAAC,IAAI,KAAK,IAAI;AACtD,YAAY,IAAI;AAChB,cAAc,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC,SAAS,CAAC;AAC5D,gBAAgB,IAAI,EAAE,UAAU,MAAM,EAAE;AAKxC,oBAAoB,IAAIK,cAAK,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC;AAClD,wBAAwB,OAAO;AAC/B,oBAAoB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAKzC,oBAAoB,YAAY,CAAC,WAAW,CAAC,CAAC;AAC9C,oBAAoB,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;AAC1D,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,QAAQ,OAAO,YAAY;AAC3B,YAAY,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;AACnG,YAAY,YAAY,CAAC,WAAW,CAAC,CAAC;AACtC,SAAS,CAAC;AACV,KAAK,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,IAAI,CAAC,CAAC,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;AACjE,CAAC;AACD,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI,MAAM,GAAG;AACjB,QAAQ,IAAI,EAAE,IAAI,CAAC,MAAM;AACzB,QAAQ,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;AACjC,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,QAAQ,MAAM,CAAC,OAAO,GAAGkB,wBAAc,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACtG,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB;;ACtFU,IAAC,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,kBAAkB;;ACW7C,SAAS,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE;AACjD,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC9D,IAAI,OAAO,QAAQ,CAAC,kBAAkB;AAEtC,IAAI,iBAAiB,EAAE,eAAe,CAAC,OAAO,OAAO,KAAK,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAClH,CAAC;AACD,SAAS,iBAAiB,CAAC,KAAK,EAAE,OAAO,EAAE;AAC3C,IAAI,IAAI,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,aAAa,GAAGC,yBAAgB,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,iBAAiB,GAAG,oBAAoB,CAAC;AACjD,QAAQ,MAAM,EAAE,MAAM;AACtB,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,OAAO,EAAE,OAAO;AACxB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,WAAW,GAAG,iBAAiB,CAAC,WAAW,EAAE,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC;AAC7F,IAAI,IAAI,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAClE,IAAI,IAAI,QAAQ,GAAGC,mBAAa,CAAC;AACjC,QAAQ,KAAK;AACb,QAAQC,wBAAkB,CAAC,SAAS,CAAC;AACrC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC,IAAI,IAAI,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAC,QAAQ,EAAE,YAAY;AACnE,QAAQ,OAAO,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;AACpD,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,EAAE,GAAG1B,gBAAK,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAEnG,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE;AAErC,QAAQ,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;AAClC,QAAQ,OAAO,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC;AACtC,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAI,IAAI,QAAQ,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;AACtD,QAAQ,OAAO,CAAC,CAAC,CAAC,GAAG,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;AACxE,KAAK;AACL,IAAIA,gBAAK,CAAC,SAAS,CAAC,YAAY;AAChC,QAAQ,IAAI,OAAO,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;AACxC,QAAQ,IAAI,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE;AAChE,YAAY,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAChD,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,YAAY;AAC3B,YAAY,cAAc,EAAE,CAAC;AAC7B,YAAY,OAAO,EAAE,CAAC;AACtB,SAAS,CAAC;AACV,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnB,IAAI,IAAI,UAAU,GAAGA,gBAAK,CAAC,OAAO,CAAC,YAAY;AAC/C,QAAQ,IAAI,KAAK,GAAG,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACnD,QAAQ,OAAO;AACf,YAAY,OAAO,EAAE,KAAK;AAC1B,YAAY,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI;AACtC,YAAY,aAAa,EAAE,KAAK,GAAGa,kBAAa,CAAC,KAAK,GAAGA,kBAAa,CAAC,KAAK;AAC5E,YAAY,KAAK,EAAE,KAAK;AACxB,SAAS,CAAC;AACV,KAAK,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC1B,IAAI,IAAI,MAAM,GAAG,WAAW,KAAK,SAAS,GAAG,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;AACzE,IAAI,IAAI,SAAS,GAAGb,gBAAK,CAAC,WAAW,CAAC,UAAU,OAAO,EAAE;AACzD,QAAQ,IAAI,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAClD,QAAQ,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;AACrD,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnB,IAAI,IAAI,OAAO,GAAGA,gBAAK,CAAC,WAAW,CAAC,UAAU,SAAS,EAAE;AACzD,QAAQ,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAClD,QAAQ,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;AACrD,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AAEnB,IAAI,IAAI,eAAe,GAAG,QAAQ,CAAC,UAAU;AAC7C,SAAS,eAAe,CAAC;AACzB,IAAI,OAAOA,gBAAK,CAAC,OAAO,CAAC,YAAY;AACrC,QAAQ,OAAO;AACf,YAAY,MAAM,EAAE,MAAM;AAC1B,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;AAC7B,YAAY,KAAK,EAAE,aAAa,CAAC,MAAM,CAAC;AACxC,YAAY,aAAa,EAAE,MAAM,CAAC,aAAa;AAC/C,YAAY,SAAS,EAAE,SAAS;AAChC,YAAY,OAAO,EAAE,OAAO;AAC5B,YAAY,eAAe,EAAE,eAAe;AAC5C,SAAS,CAAC;AACV,KAAK,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC;AAC9D,CAAC;AACD,SAAS,eAAe,CAAC,OAAO,EAAE;AAClC,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,OAAO,CAAC,WAAW,EAAE,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;AAChH,IAAIU,yBAAkB,CAAC,KAAK,EAAEC,mBAAY,CAAC,KAAK,CAAC,CAAC;AAClD,IAAI,mBAAmB,CAAC,WAAW,CAAC,CAAC;AACrC,IAAI,yBAAyB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC9D,CAAC;AACD,SAAS,mBAAmB,CAAC,WAAW,EAAE;AAC1C,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,aAAa,CAAC,EAAE;AAChE,IAAI,IAAI,sBAAsB,GAAG;AACjC,QAAQ,aAAa;AACrB,QAAQ,cAAc;AACtB,QAAQ,UAAU;AAClB,QAAQ,mBAAmB;AAC3B,KAAK,CAAC;AACN,IAAIT,iBAAS,CAAC,sBAAsB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,WAAW,CAAC,CAAC;AAC7E,CAAC;AACD,SAAS,yBAAyB,CAAC,WAAW,EAAE,iBAAiB,EAAE;AACnE,IAAI,IAAI,WAAW,KAAK,UAAU,IAAI,iBAAiB,EAAE;AACzD,QAAQ,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIA,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3D,KAAK;AACL,CAAC;AACM,SAAS,aAAa,CAAC,MAAM,EAAE;AACtC,IAAI,OAAOe,yBAAe,CAAC,MAAM,CAAC,MAAM,CAAC;AACzC,QAAQ,IAAIC,gBAAW,CAAC,EAAE,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC;AACzD,UAAU,MAAM,CAAC,KAAK,CAAC;AACvB,CAAC;AACM,SAAS,oBAAoB,CAAC,EAAE,EAAE;AACzC,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AACnE,IAAI,OAAO,WAAW,CAAC,YAAY;AACnC,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,OAAO,KAAK,SAAS,EAAE;AACnC,YAAY,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC;AAC5D,SAAS;AACT,QAAQ,IAAI,WAAW,GAAG,OAAO,CAAC,WAAW;AAC7C,aAAa,CAAC,EAAE,GAAG,MAAM,CAAC,cAAc,CAAC,UAAU,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC;AACzG,YAAY,aAAa,CAAC;AAC1B,QAAQ,IAAI,iBAAiB,GAAGT,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,KAAK,EAAE,eAAe,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;AACzK,QAAQ,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAC1C,YAAY,eAAe,CAAC,iBAAiB,CAAC,CAAC;AAC/C,SAAS;AAGT,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE;AAC1B,YAAY,iBAAiB,CAAC,WAAW,GAAG,SAAS,CAAC;AACtD,SAAS;AACT,QAAQ,OAAO,iBAAiB,CAAC;AACjC,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AACjC;;AClIO,SAAS,kBAAkB,CAAC,KAAK,EAAE,OAAO,EAAE;AACnD,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC9D,IAAI,OAAO,QAAQ,CAAC,oBAAoB;AAExC,IAAI,mBAAmB,EAAE,eAAe,CAAC,OAAO,OAAO,KAAK,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACpH,CAAC;AACD,SAAS,mBAAmB,CAAC,KAAK,EAAE,OAAO,EAAE;AAC7C,IAAI,IAAI,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,aAAa,GAAGe,yBAAgB,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;AACrG,IAAI,IAAI,WAAW,GAAG,iBAAiB,CAAC,WAAW,EAAE,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC;AAC7F,IAAI,IAAI,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAOlE,IAAI,IAAI,cAAc,GAAGxB,gBAAK,CAAC,MAAM,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC;AACjE,IAAI,cAAc,CAAC,OAAO,KAAK,cAAc,CAAC,OAAO,GAAG,WAAW,KAAK,SAAS,CAAC,CAAC;AACnF,IAAI,IAAI,QAAQ,GAAGyB,mBAAa,CAAC;AACjC,QAAQ,KAAK;AACb,QAAQC,wBAAkB,CAAC,SAAS,CAAC;AACrC,KAAK,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC,IAAI,IAAI,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAC,QAAQ,EAAE,YAAY;AACnE,QAAQ,OAAO,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;AACpD,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,EAAE,GAAG1B,gBAAK,CAAC,QAAQ,CAAC2B,qBAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,eAAe,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACzG,IAAI,IAAIC,uBAAc,CAAC,eAAe,CAAC,KAAK,QAAQ,EAAE;AACtD,QAAQ,kBAAkB,CAACD,qBAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,IAAI,QAAQ,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,EAAE;AACtD,QAAQ,IAAI,OAAO,GAAG,QAAQ,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;AAC/D,QAAQE,8BAAqB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;AACxD,KAAK;AAML,IAAI7B,gBAAK,CAAC,SAAS,CAAC,YAAY;AAGhC,QAAQ,IAAI,EAAE,GAAG,UAAU,CAAC,YAAY;AACxC,YAAY,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACnC,gBAAgB,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACtD,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,YAAY,EAAE,OAAO,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;AAGxD,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,SAAS,GAAGA,gBAAK,CAAC,WAAW,CAAC,UAAU,OAAO,EAAE;AACzD,QAAQ,IAAI,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAClD,QAAQ,kBAAkB,CAAC2B,qBAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnD,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnB,IAAI,IAAI,OAAO,GAAG3B,gBAAK,CAAC,WAAW,CAAC,UAAU,SAAS,EAAE;AACzD,QAAQ,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAClD,QAAQ,kBAAkB,CAAC2B,qBAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnD,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnB,IAAI3B,gBAAK,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,QAAQ,CAAC,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/E,IAAI,OAAO;AACX,QAAQ,cAAc,CAAC,OAAO,GAAG,eAAe,GAAG,KAAK,CAAC;AACzD,QAAQ;AACR,YAAY,SAAS,EAAE,SAAS;AAChC,YAAY,OAAO,EAAE,OAAO;AAE5B,YAAY,eAAe,EAAE,QAAQ,CAAC,UAAU;AAChD,iBAAiB,eAAe;AAChC,SAAS;AACT,KAAK,CAAC;AACN;;ACzEA,IAAI,gBAAgB,GAAG,EAAE,CAAC;AACnB,SAAS,mBAAmB,CAAC,OAAO,EAAE;AAC7C,IAAI,OAAO,QAAQ,CAAC,qBAAqB;AAEzC,IAAI,oBAAoB,EAAE,eAAe,CAAC,OAAO,OAAO,KAAK,QAAQ,GAAG,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AAC9G,CAAC;AACD,SAAS,oBAAoB,CAAC,OAAO,EAAE;AACvC,IAAI,IAAI,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;AAC3D,IAAI,IAAI8B,OAAK,GAAG,MAAM,CAAC,KAAK,CAAC;AAC7B,IAAI,IAAI,EAAE,GAAG9B,gBAAK,CAAC,OAAO,CAAC,YAAY;AACvC,QAAQ,OAAO,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI;AAC9C,cAAc,IAAI,KAAK,IAAI,GAAG,IAAI;AAClC,kBAAkB8B,OAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvC,KAAK,EAAE,CAACA,OAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AACtB,IAAI,IAAI,WAAW,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,IAAIN,yBAAgB,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,EAAEE,wBAAkB,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,EAAEjB,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACzN,IAAI,IAAI,EAAE,GAAGT,gBAAK,CAAC,QAAQ,CAAC,WAAW,KAAK,IAAI,GAAG,gBAAgB,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACrJ,IAAIA,gBAAK,CAAC,SAAS,CAAC,YAAY;AAChC,QAAQ,IAAI,WAAW,KAAK,IAAI,EAAE;AAClC,YAAY,OAAO;AACnB,SAAS;AACT,QAAQ,IAAI,OAAO,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC;AAC3C,QAAQ,IAAI,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE;AACnE,YAAY,UAAU,CAAC,CAAC,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AACnD,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,YAAY;AAC3B,YAAY,OAAO,EAAE,CAAC;AACtB,YAAY,cAAc,EAAE,CAAC;AAC7B,SAAS,CAAC;AACV,KAAK,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;AACtB,IAAI,IAAI,WAAW,KAAK,IAAI,EAAE;AAC9B,QAAQ,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,KAAK,WAAW,CAAC,GAAG,EAAE;AAExC,QAAQ,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC;AACrC,QAAQ,OAAO,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC;AACzC,KAAK;AACL,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACjC,IAAI,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAC1B;;ACvCO,SAAS,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE;AACjD,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC9D,IAAI,IAAI,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,aAAa,GAAGwB,yBAAgB,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;AACrG,IAAI,IAAI,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,QAAQ,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAClE,IAAI,IAAI,EAAE,GAAGxB,gBAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,QAAQ,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACzE,IAAI+B,8BAAqB,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI,IAAI,gBAAgB,GAAG,QAAQ,IAAIH,uBAAc,CAAC,QAAQ,CAAC,CAAC;AAChE,IAAI,IAAI,QAAQ,KAAK,gBAAgB,KAAK,IAAI,IAAI,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,gBAAgB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,EAAE;AAChJ,QAAQ,IAAI,OAAO,GAAG,gBAAgB,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;AACvE,QAAQC,8BAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,IAAI,kBAAkB,GAAG,cAAc,EAAE,CAAC;AAC9C,IAAI,IAAI,SAAS,GAAG7B,gBAAK,CAAC,WAAW,CAAC,UAAU,OAAO,EAAE;AACzD,QAAQ,IAAI,CAAC,gBAAgB,EAAE;AAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AACrF,SAAS;AACT,QAAQ,IAAI,OAAO,GAAG,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC1D,QAAQ,WAAW,CAAC2B,qBAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACpD,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3B,IAAI,IAAI,OAAO,GAAG3B,gBAAK,CAAC,WAAW,CAAC,UAAU,OAAO,EAAE;AACvD,QAAQ,IAAI,CAAC,gBAAgB,EAAE;AAC/B,YAAY,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AACrF,SAAS;AACT,QAAQ,IAAI,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACxD,QAAQ,WAAW,CAAC2B,qBAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACpD,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3B,IAAI,IAAI,SAAS,GAAG3B,gBAAK,CAAC,WAAW,CAAC,YAAY;AAClD,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AACtB,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACrC,SAAS;AACT,QAAQE,iBAAS,CAAC,CAAC,kBAAkB,EAAE,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAChC,QAAQ,IAAI,QAAQ,GAAGuB,mBAAa,CAAC;AACrC,YAAY,KAAK;AACjB,YAAYC,wBAAkB,CAAC,SAAS,CAAC;AACzC,SAAS,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC;AACtC,QAAQ,IAAI,QAAQ,GAAG,aAAa,CAAC,WAAW,CAAC,QAAQ,EAAE,YAAY;AACvE,YAAY,OAAO,MAAM,CAAC,UAAU,CAACjB,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,iBAAiB,CAAC,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;AAC1G,SAAS,CAAC,CAAC;AACX,QAAQ,WAAW,CAACkB,qBAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC5C,KAAK,EAAE;AACP,QAAQ,KAAK;AACb,QAAQ,QAAQ;AAChB,QAAQ,aAAa;AACrB,QAAQ,iBAAiB;AACzB,QAAQ,kBAAkB;AAC1B,QAAQ,MAAM;AACd,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,eAAe,GAAG3B,gBAAK,CAAC,WAAW,CAAC,UAAU,OAAO,EAAE;AAC/D,QAAQE,iBAAS,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;AACxC,QAAQ,OAAO,gBAAgB,CAAC,UAAU,CAAC,eAAe;AAE1D,QAAQ,OAAO,CAAC,CAAC;AACjB,KAAK,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3B,IAAI,IAAI,KAAK,GAAGF,gBAAK,CAAC,WAAW,CAAC,YAAY;AAC9C,QAAQ,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1B,KAAK,EAAE,EAAE,CAAC,CAAC;AACX,IAAI,OAAO,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,eAAe,EAAE,eAAe,EAAE,CAAC,CAAC;AAC7H;;AChDO,SAAS,mBAAmB,CAAC,QAAQ,EAAE;AAC9C,IAAI,IAAI,SAAS,GAAG4B,uBAAc,CAAC,QAAQ,CAAC,CAAC;AAC7C,IAAI,IAAI,gBAAgB,GAAG,eAAe,CAAC,SAAS;AAGpD,QAAQ,SAAS,CAAC,YAAY,CAAC;AAC/B,UAAU,SAAS,CAAC,CAAC;AACrB,IAAI,OAAO,QAAQ,CAAC,qBAAqB;AAEzC,IAAI,oBAAoB,EAAE,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC;AACtD,CAAC;AACD,SAAS,oBAAoB,CAAC,QAAQ,EAAE;AACxC,IAAIG,8BAAqB,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI,IAAI,EAAE,GAAG/B,gBAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,gBAAgB,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,mBAAmB,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC7F,IAAI,IAAI,EAAE,GAAGA,gBAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,eAAe,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3F,IAAI,IAAI,gBAAgB,GAAG4B,uBAAc,CAAC,QAAQ,CAAC,CAAC;AAIpD,IAAI,IAAI,gBAAgB,KAAK,QAAQ,EAAE;AACvC,QAAQ,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AACtC,QAAQ,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AACrC,KAAK;AACL,SAAS;AACT,QAAQC,8BAAqB,CAAC,QAAQ,EAAEG,0BAAiB,CAAC,eAAe,CAAC,CAAC,CAAC;AAC5E,KAAK;AACL,IAAI,IAAI,OAAO,GAAGhC,gBAAK,CAAC,WAAW,CAAC,UAAU,SAAS,EAAE;AACzD,QAAQ,IAAI,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AAC1D,QAAQ,kBAAkB,CAAC2B,qBAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3D,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3B,IAAI,IAAI,SAAS,GAAG3B,gBAAK,CAAC,WAAW,CAAC,UAAU,OAAO,EAAE;AACzD,QAAQ,IAAI,OAAO,GAAG,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC1D,QAAQ,kBAAkB,CAAC2B,qBAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3D,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3B,IAAI,OAAO;AACX,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,SAAS,EAAE,SAAS;AAE5B,QAAQ,eAAe,EAAE,gBAAgB,CAAC,UAAU;AACpD,aAAa,eAAe;AAC5B,KAAK,CAAC;AACN;;AC5DO,SAAS,YAAY,CAAC,QAAQ,EAAE;AACvC,IAAI,IAAI,SAAS,GAAGC,uBAAc,CAAC,QAAQ,CAAC,CAAC;AAC7C,IAAI,IAAI,gBAAgB,GAAG,eAAe,CAAC,SAAS;AAGpD,QAAQ,SAAS,CAAC,YAAY,CAAC;AAC/B,UAAU,SAAS,CAAC,CAAC;AACrB,IAAI,OAAO,QAAQ,CAAC,cAAc;AAElC,IAAI,aAAa,EAAE,gBAAgB,CAAC,CAAC,QAAQ,CAAC,CAAC;AAC/C,CAAC;AACD,SAAS,aAAa,CAAC,QAAQ,EAAE;AACjC,IAAIG,8BAAqB,CAAC,QAAQ,CAAC,CAAC;AACpC,IAAI,IAAI,gBAAgB,GAAG/B,gBAAK,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO4B,uBAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvG,IAAI,IAAI,UAAU,GAAG5B,gBAAK,CAAC,WAAW,CAAC,YAAY,EAAE,OAAOgC,0BAAiB,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AACxG,IAAI,IAAI,gBAAgB,CAAC,QAAQ,EAAE;AACnC,QAAQ,gBAAgB,CAAC,YAAY,EAAE,CAAC;AACxC,QAAQH,8BAAqB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAClE,KAAK;AACL,IAAI7B,gBAAK,CAAC,SAAS,CAAC,YAAY,EAAE,OAAO,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC3F,IAAI,IAAI,OAAO,GAAG,oBAAoB,CAACA,gBAAK,CAAC,WAAW,CAAC,UAAU,WAAW,EAAE;AAChF,QAAQ,OAAO,gBAAgB,CAAC,MAAM,CAAC,UAAU,OAAO,EAAE;AAC1D,YAAY6B,8BAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACrD,YAAY,WAAW,EAAE,CAAC;AAC1B,SAAS,CAAC,CAAC;AACX,KAAK,EAAE,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AAC9D,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;AAChC,IAAI,OAAO7B,gBAAK,CAAC,OAAO,CAAC,YAAY;AACrC,QAAQ,OAAO;AACf,YAAY,IAAI,EAAE,MAAM,CAAC,IAAI;AAC7B,YAAY,aAAa,EAAE,MAAM,CAAC,aAAa;AAC/C,YAAY,KAAK,EAAE,aAAa,CAAC,MAAM,CAAC;AACxC,SAAS,CAAC;AACV,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACjB;;;;;;;;;;;;;;;;;"}