{"version": 3, "file": "useMutation.js", "sourceRoot": "", "sources": ["../../../src/react/hooks/useMutation.ts"], "names": [], "mappings": ";AAAA,OAAO,KAAK,KAAK,MAAM,SAAS,CAAC;AAiBjC,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAC;AACxD,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,oBAAoB,CAAC;AACtE,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AACvD,OAAO,EAAE,yBAAyB,EAAE,MAAM,yCAAyC,CAAC;AAEpF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8CG;AACH,MAAM,UAAU,WAAW,CAMzB,QAA6D,EAC7D,OAKC;IAED,IAAM,MAAM,GAAG,eAAe,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,CAAC,CAAC;IAChD,kBAAkB,CAAC,QAAQ,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC9C,IAAA,KAAsB,KAAK,CAAC,QAAQ,CAAgC;QACxE,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,KAAK;QACd,MAAM,QAAA;KACP,CAAC,EAJK,MAAM,QAAA,EAAE,SAAS,QAItB,CAAC;IAEH,IAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC;QACvB,MAAM,QAAA;QACN,UAAU,EAAE,CAAC;QACb,SAAS,EAAE,IAAI;QACf,MAAM,QAAA;QACN,QAAQ,UAAA;QACR,OAAO,SAAA;KACR,CAAC,CAAC;IAEH,yBAAyB,CAAC;QACxB,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,MAAM,QAAA,EAAE,OAAO,SAAA,EAAE,QAAQ,UAAA,EAAE,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;IAEH,IAAM,OAAO,GAAG,KAAK,CAAC,WAAW,CAC/B,UACE,cAKM;QALN,+BAAA,EAAA,mBAKM;QAEA,IAAA,KAAwB,GAAG,CAAC,OAAO,EAAjC,OAAO,aAAA,EAAE,QAAQ,cAAgB,CAAC;QAC1C,IAAM,WAAW,yBAAQ,OAAO,KAAE,QAAQ,UAAA,GAAE,CAAC;QAC7C,IAAM,MAAM,GAAG,cAAc,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC;QAE3D,IACE,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO;YAC3B,CAAC,WAAW,CAAC,aAAa;YAC1B,GAAG,CAAC,OAAO,CAAC,SAAS,EACrB,CAAC;YACD,SAAS,CACP,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG;gBACpB,OAAO,EAAE,IAAI;gBACb,KAAK,EAAE,KAAK,CAAC;gBACb,IAAI,EAAE,KAAK,CAAC;gBACZ,MAAM,EAAE,IAAI;gBACZ,MAAM,QAAA;aACP,CAAC,CACH,CAAC;QACJ,CAAC;QAED,IAAM,UAAU,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC;QAC5C,IAAM,aAAa,GAAG,YAAY,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QAEhE,OAAO,MAAM;aACV,MAAM,CAAC,aAA2D,CAAC;aACnE,IAAI,CACH,UAAC,QAAQ;;YACC,IAAA,IAAI,GAAa,QAAQ,KAArB,EAAE,MAAM,GAAK,QAAQ,OAAb,CAAc;YAClC,IAAM,KAAK,GACT,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC3B,IAAI,WAAW,CAAC,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC;gBAC5C,CAAC,CAAC,KAAK,CAAC,CAAC;YAEX,IAAM,OAAO,GACX,cAAc,CAAC,OAAO,KAAI,MAAA,GAAG,CAAC,OAAO,CAAC,OAAO,0CAAE,OAAO,CAAA,CAAC;YAEzD,IAAI,KAAK,IAAI,OAAO,EAAE,CAAC;gBACrB,OAAO,CACL,KAAK,EACL,aAA2D,CAC5D,CAAC;YACJ,CAAC;YAED,IACE,UAAU,KAAK,GAAG,CAAC,OAAO,CAAC,UAAU;gBACrC,CAAC,aAAa,CAAC,aAAa,EAC5B,CAAC;gBACD,IAAM,QAAM,GAAG;oBACb,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,KAAK;oBACd,IAAI,MAAA;oBACJ,KAAK,OAAA;oBACL,MAAM,QAAA;iBACP,CAAC;gBAEF,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,QAAM,CAAC,EAAE,CAAC;oBAChE,SAAS,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,QAAM,CAAC,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;YAED,IAAM,WAAW,GACf,cAAc,CAAC,WAAW,KAAI,MAAA,GAAG,CAAC,OAAO,CAAC,OAAO,0CAAE,WAAW,CAAA,CAAC;YAEjE,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,WAAW,aAAX,WAAW,uBAAX,WAAW,CACT,QAAQ,CAAC,IAAK,EACd,aAA2D,CAC5D,CAAC;YACJ,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,UAAC,KAAK;;YACJ,IACE,UAAU,KAAK,GAAG,CAAC,OAAO,CAAC,UAAU;gBACrC,GAAG,CAAC,OAAO,CAAC,SAAS,EACrB,CAAC;gBACD,IAAM,QAAM,GAAG;oBACb,OAAO,EAAE,KAAK;oBACd,KAAK,OAAA;oBACL,IAAI,EAAE,KAAK,CAAC;oBACZ,MAAM,EAAE,IAAI;oBACZ,MAAM,QAAA;iBACP,CAAC;gBAEF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,QAAM,CAAC,EAAE,CAAC;oBACvC,SAAS,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,GAAG,QAAM,CAAC,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;YAED,IAAM,OAAO,GACX,cAAc,CAAC,OAAO,KAAI,MAAA,GAAG,CAAC,OAAO,CAAC,OAAO,0CAAE,OAAO,CAAA,CAAC;YAEzD,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CACL,KAAK,EACL,aAA2D,CAC5D,CAAC;gBAEF,iDAAiD;gBACjD,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;YACzC,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC,CACF,CAAC;IACN,CAAC,EACD,EAAE,CACH,CAAC;IAEF,IAAM,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC;QAC9B,IAAI,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YAC1B,IAAM,QAAM,GAAG;gBACb,MAAM,EAAE,KAAK;gBACb,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,MAAM;aAC3B,CAAC;YACF,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE,MAAM,UAAA,EAAE,CAAC,CAAC;YACtD,SAAS,CAAC,QAAM,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,KAAK,CAAC,SAAS,CAAC;QACd,IAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;QAC5B,yDAAyD;QACzD,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QAEzB,OAAO;YACL,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;QAC5B,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO,CAAC,OAAO,aAAI,KAAK,OAAA,IAAK,MAAM,EAAG,CAAC;AACzC,CAAC", "sourcesContent": ["import * as React from \"rehackt\";\nimport type { DocumentNode } from \"graphql\";\nimport type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\nimport type {\n  MutationFunctionOptions,\n  MutationHookOptions,\n  MutationResult,\n  MutationTuple,\n  NoInfer,\n} from \"../types/types.js\";\n\nimport type {\n  ApolloCache,\n  DefaultContext,\n  MutationOptions,\n  OperationVariables,\n} from \"../../core/index.js\";\nimport { mergeOptions } from \"../../utilities/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { DocumentType, verifyDocumentType } from \"../parser/index.js\";\nimport { ApolloError } from \"../../errors/index.js\";\nimport { useApolloClient } from \"./useApolloClient.js\";\nimport { useIsomorphicLayoutEffect } from \"./internal/useIsomorphicLayoutEffect.js\";\n\n/**\n *\n *\n * > Refer to the [Mutations](https://www.apollographql.com/docs/react/data/mutations/) section for a more in-depth overview of `useMutation`.\n *\n * @example\n * ```jsx\n * import { gql, useMutation } from '@apollo/client';\n *\n * const ADD_TODO = gql`\n *   mutation AddTodo($type: String!) {\n *     addTodo(type: $type) {\n *       id\n *       type\n *     }\n *   }\n * `;\n *\n * function AddTodo() {\n *   let input;\n *   const [addTodo, { data }] = useMutation(ADD_TODO);\n *\n *   return (\n *     <div>\n *       <form\n *         onSubmit={e => {\n *           e.preventDefault();\n *           addTodo({ variables: { type: input.value } });\n *           input.value = '';\n *         }}\n *       >\n *         <input\n *           ref={node => {\n *             input = node;\n *           }}\n *         />\n *         <button type=\"submit\">Add Todo</button>\n *       </form>\n *     </div>\n *   );\n * }\n * ```\n * @since 3.0.0\n * @param mutation - A GraphQL mutation document parsed into an AST by `gql`.\n * @param options - Options to control how the mutation is executed.\n * @returns A tuple in the form of `[mutate, result]`\n */\nexport function useMutation<\n  TData = any,\n  TVariables = OperationVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n>(\n  mutation: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options?: MutationHookOptions<\n    NoInfer<TData>,\n    NoInfer<TVariables>,\n    TContext,\n    TCache\n  >\n): MutationTuple<TData, TVariables, TContext, TCache> {\n  const client = useApolloClient(options?.client);\n  verifyDocumentType(mutation, DocumentType.Mutation);\n  const [result, setResult] = React.useState<Omit<MutationResult, \"reset\">>({\n    called: false,\n    loading: false,\n    client,\n  });\n\n  const ref = React.useRef({\n    result,\n    mutationId: 0,\n    isMounted: true,\n    client,\n    mutation,\n    options,\n  });\n\n  useIsomorphicLayoutEffect(() => {\n    Object.assign(ref.current, { client, options, mutation });\n  });\n\n  const execute = React.useCallback(\n    (\n      executeOptions: MutationFunctionOptions<\n        TData,\n        TVariables,\n        TContext,\n        TCache\n      > = {}\n    ) => {\n      const { options, mutation } = ref.current;\n      const baseOptions = { ...options, mutation };\n      const client = executeOptions.client || ref.current.client;\n\n      if (\n        !ref.current.result.loading &&\n        !baseOptions.ignoreResults &&\n        ref.current.isMounted\n      ) {\n        setResult(\n          (ref.current.result = {\n            loading: true,\n            error: void 0,\n            data: void 0,\n            called: true,\n            client,\n          })\n        );\n      }\n\n      const mutationId = ++ref.current.mutationId;\n      const clientOptions = mergeOptions(baseOptions, executeOptions);\n\n      return client\n        .mutate(clientOptions as MutationOptions<TData, OperationVariables>)\n        .then(\n          (response) => {\n            const { data, errors } = response;\n            const error =\n              errors && errors.length > 0 ?\n                new ApolloError({ graphQLErrors: errors })\n              : void 0;\n\n            const onError =\n              executeOptions.onError || ref.current.options?.onError;\n\n            if (error && onError) {\n              onError(\n                error,\n                clientOptions as MutationOptions<TData, OperationVariables>\n              );\n            }\n\n            if (\n              mutationId === ref.current.mutationId &&\n              !clientOptions.ignoreResults\n            ) {\n              const result = {\n                called: true,\n                loading: false,\n                data,\n                error,\n                client,\n              };\n\n              if (ref.current.isMounted && !equal(ref.current.result, result)) {\n                setResult((ref.current.result = result));\n              }\n            }\n\n            const onCompleted =\n              executeOptions.onCompleted || ref.current.options?.onCompleted;\n\n            if (!error) {\n              onCompleted?.(\n                response.data!,\n                clientOptions as MutationOptions<TData, OperationVariables>\n              );\n            }\n\n            return response;\n          },\n          (error) => {\n            if (\n              mutationId === ref.current.mutationId &&\n              ref.current.isMounted\n            ) {\n              const result = {\n                loading: false,\n                error,\n                data: void 0,\n                called: true,\n                client,\n              };\n\n              if (!equal(ref.current.result, result)) {\n                setResult((ref.current.result = result));\n              }\n            }\n\n            const onError =\n              executeOptions.onError || ref.current.options?.onError;\n\n            if (onError) {\n              onError(\n                error,\n                clientOptions as MutationOptions<TData, OperationVariables>\n              );\n\n              // TODO(brian): why are we returning this here???\n              return { data: void 0, errors: error };\n            }\n\n            throw error;\n          }\n        );\n    },\n    []\n  );\n\n  const reset = React.useCallback(() => {\n    if (ref.current.isMounted) {\n      const result = {\n        called: false,\n        loading: false,\n        client: ref.current.client,\n      };\n      Object.assign(ref.current, { mutationId: 0, result });\n      setResult(result);\n    }\n  }, []);\n\n  React.useEffect(() => {\n    const current = ref.current;\n    // eslint-disable-next-line react-compiler/react-compiler\n    current.isMounted = true;\n\n    return () => {\n      current.isMounted = false;\n    };\n  }, []);\n\n  return [execute, { reset, ...result }];\n}\n"]}