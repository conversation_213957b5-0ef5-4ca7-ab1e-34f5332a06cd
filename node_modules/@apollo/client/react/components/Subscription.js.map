{"version": 3, "file": "Subscription.js", "sourceRoot": "", "sources": ["../../../src/react/components/Subscription.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,SAAS,MAAM,YAAY,CAAC;AAKxC,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD;;;;;GAKG;AACH,MAAM,UAAU,YAAY,CAI1B,KAAsD;IAEtD,IAAM,MAAM,GAAG,eAAe,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;IAC1D,OAAO,KAAK,CAAC,QAAQ,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AAClE,CAAC;AAQD,YAAY,CAAC,SAAS,GAAG;IACvB,YAAY,EAAE,SAAS,CAAC,MAAM,CAAC,UAAU;IACzC,SAAS,EAAE,SAAS,CAAC,MAAM;IAC3B,QAAQ,EAAE,SAAS,CAAC,IAAI;IACxB,kBAAkB,EAAE,SAAS,CAAC,IAAI;IAClC,MAAM,EAAE,SAAS,CAAC,IAAI;IACtB,sBAAsB,EAAE,SAAS,CAAC,IAAI;IACtC,UAAU,EAAE,SAAS,CAAC,IAAI;IAC1B,iBAAiB,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;CAClC,CAAC", "sourcesContent": ["import * as PropTypes from \"prop-types\";\nimport type * as ReactTypes from \"react\";\n\nimport type { OperationVariables } from \"../../core/index.js\";\nimport type { SubscriptionComponentOptions } from \"./types.js\";\nimport { useSubscription } from \"../hooks/index.js\";\n\n/**\n * @deprecated\n * Official support for React Apollo render prop components ended in March 2020.\n * This library is still included in the `@apollo/client` package,\n * but it no longer receives feature updates or bug fixes.\n */\nexport function Subscription<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  props: SubscriptionComponentOptions<TData, TVariables>\n): ReactTypes.JSX.Element | null {\n  const result = useSubscription(props.subscription, props);\n  return props.children && result ? props.children(result) : null;\n}\n\nexport interface Subscription<TData, TVariables extends OperationVariables> {\n  propTypes: PropTypes.InferProps<\n    SubscriptionComponentOptions<TData, TVariables>\n  >;\n}\n\nSubscription.propTypes = {\n  subscription: PropTypes.object.isRequired,\n  variables: PropTypes.object,\n  children: PropTypes.func,\n  onSubscriptionData: PropTypes.func,\n  onData: PropTypes.func,\n  onSubscriptionComplete: PropTypes.func,\n  onComplete: PropTypes.func,\n  shouldResubscribe: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n} as Subscription<any, any>[\"propTypes\"];\n"]}