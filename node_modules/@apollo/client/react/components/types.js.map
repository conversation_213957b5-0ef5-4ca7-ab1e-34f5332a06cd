{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/react/components/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { DocumentNode } from \"graphql\";\nimport type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\n\nimport type * as ReactTypes from \"react\";\n\nimport type {\n  OperationVariables,\n  DefaultContext,\n  ApolloCache,\n} from \"../../core/index.js\";\nimport type {\n  QueryFunctionOptions,\n  QueryResult,\n  BaseMutationOptions,\n  MutationFunction,\n  MutationResult,\n  BaseSubscriptionOptions,\n  SubscriptionResult,\n} from \"../types/types.js\";\n\nexport interface QueryComponentOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends QueryFunctionOptions<TData, TVariables> {\n  children: (\n    result: QueryResult<TData, TVariables>\n  ) => ReactTypes.JSX.Element | null;\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>;\n}\n\nexport interface MutationComponentOptions<\n  TData = any,\n  TVariables = OperationVariables,\n  TContext = DefaultContext,\n  TCache extends ApolloCache<any> = ApolloCache<any>,\n> extends BaseMutationOptions<TData, TVariables, TContext, TCache> {\n  mutation: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  children: (\n    mutateFunction: MutationFunction<TData, TVariables, TContext>,\n    result: MutationResult<TData>\n  ) => ReactTypes.JSX.Element | null;\n}\n\nexport interface SubscriptionComponentOptions<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends BaseSubscriptionOptions<TData, TVariables> {\n  /** {@inheritDoc @apollo/client!SubscriptionOptionsDocumentation#query:member} */\n  subscription: DocumentNode | TypedDocumentNode<TData, TVariables>;\n  children?:\n    | null\n    | ((result: SubscriptionResult<TData>) => ReactTypes.JSX.Element | null);\n}\n"]}