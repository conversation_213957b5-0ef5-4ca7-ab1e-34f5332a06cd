{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/react/components/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC", "sourcesContent": ["export { Query } from \"./Query.js\";\nexport { Mutation } from \"./Mutation.js\";\nexport { Subscription } from \"./Subscription.js\";\n\nexport type * from \"./types.js\";\n"]}