{"version": 3, "file": "Mutation.js", "sourceRoot": "", "sources": ["../../../src/react/components/Mutation.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,SAAS,MAAM,YAAY,CAAC;AAKxC,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAEhD;;;;;GAKG;AACH,MAAM,UAAU,QAAQ,CACtB,KAAkD;IAE5C,IAAA,KAAwB,WAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAzD,WAAW,QAAA,EAAE,MAAM,QAAsC,CAAC;IACjE,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACrE,CAAC;AAMD,QAAQ,CAAC,SAAS,GAAG;IACnB,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,UAAU;IACrC,SAAS,EAAE,SAAS,CAAC,MAAM;IAC3B,kBAAkB,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;IAC3E,cAAc,EAAE,SAAS,CAAC,SAAS,CAAC;QAClC,SAAS,CAAC,OAAO,CACf,SAAS,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,CAC1D;QACD,SAAS,CAAC,IAAI;KACf,CAAC;IACF,mBAAmB,EAAE,SAAS,CAAC,IAAI;IACnC,MAAM,EAAE,SAAS,CAAC,IAAI;IACtB,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU;IACnC,WAAW,EAAE,SAAS,CAAC,IAAI;IAC3B,OAAO,EAAE,SAAS,CAAC,IAAI;IACvB,WAAW,EAAE,SAAS,CAAC,MAAM;CACK,CAAC", "sourcesContent": ["import * as PropTypes from \"prop-types\";\nimport type * as ReactTypes from \"react\";\n\nimport type { OperationVariables } from \"../../core/index.js\";\nimport type { MutationComponentOptions } from \"./types.js\";\nimport { useMutation } from \"../hooks/index.js\";\n\n/**\n * @deprecated\n * Official support for React Apollo render prop components ended in March 2020.\n * This library is still included in the `@apollo/client` package,\n * but it no longer receives feature updates or bug fixes.\n */\nexport function Mutation<TData = any, TVariables = OperationVariables>(\n  props: MutationComponentOptions<TData, TVariables>\n): ReactTypes.JSX.Element | null {\n  const [runMutation, result] = useMutation(props.mutation, props);\n  return props.children ? props.children(runMutation, result) : null;\n}\n\nexport interface Mutation<TData, TVariables> {\n  propTypes: PropTypes.InferProps<MutationComponentOptions<TData, TVariables>>;\n}\n\nMutation.propTypes = {\n  mutation: PropTypes.object.isRequired,\n  variables: PropTypes.object,\n  optimisticResponse: PropTypes.oneOfType([PropTypes.object, PropTypes.func]),\n  refetchQueries: PropTypes.oneOfType([\n    PropTypes.arrayOf(\n      PropTypes.oneOfType([PropTypes.string, PropTypes.object])\n    ),\n    PropTypes.func,\n  ]),\n  awaitRefetchQueries: PropTypes.bool,\n  update: PropTypes.func,\n  children: PropTypes.func.isRequired,\n  onCompleted: PropTypes.func,\n  onError: PropTypes.func,\n  fetchPolicy: PropTypes.string,\n} as Mutation<any, any>[\"propTypes\"];\n"]}