{"version": 3, "file": "components.cjs", "sources": ["Query.js", "Mutation.js", "Subscription.js"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport * as PropTypes from \"prop-types\";\nimport { useQuery } from \"../hooks/index.js\";\n/**\n * @deprecated\n * Official support for React Apollo render prop components ended in March 2020.\n * This library is still included in the `@apollo/client` package,\n * but it no longer receives feature updates or bug fixes.\n */\nexport function Query(props) {\n    var children = props.children, query = props.query, options = __rest(props, [\"children\", \"query\"]);\n    var result = useQuery(query, options);\n    return result ? children(result) : null;\n}\nQuery.propTypes = {\n    client: PropTypes.object,\n    children: PropTypes.func.isRequired,\n    fetchPolicy: PropTypes.string,\n    notifyOnNetworkStatusChange: PropTypes.bool,\n    onCompleted: PropTypes.func,\n    onError: PropTypes.func,\n    pollInterval: PropTypes.number,\n    query: PropTypes.object.isRequired,\n    variables: PropTypes.object,\n    ssr: PropTypes.bool,\n    partialRefetch: PropTypes.bool,\n    returnPartialData: PropTypes.bool,\n};\n//# sourceMappingURL=Query.js.map", "import * as PropTypes from \"prop-types\";\nimport { useMutation } from \"../hooks/index.js\";\n/**\n * @deprecated\n * Official support for React Apollo render prop components ended in March 2020.\n * This library is still included in the `@apollo/client` package,\n * but it no longer receives feature updates or bug fixes.\n */\nexport function Mutation(props) {\n    var _a = useMutation(props.mutation, props), runMutation = _a[0], result = _a[1];\n    return props.children ? props.children(runMutation, result) : null;\n}\nMutation.propTypes = {\n    mutation: PropTypes.object.isRequired,\n    variables: PropTypes.object,\n    optimisticResponse: PropTypes.oneOfType([PropTypes.object, PropTypes.func]),\n    refetchQueries: PropTypes.oneOfType([\n        PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.object])),\n        PropTypes.func,\n    ]),\n    awaitRefetchQueries: PropTypes.bool,\n    update: PropTypes.func,\n    children: PropTypes.func.isRequired,\n    onCompleted: PropTypes.func,\n    onError: PropTypes.func,\n    fetchPolicy: PropTypes.string,\n};\n//# sourceMappingURL=Mutation.js.map", "import * as PropTypes from \"prop-types\";\nimport { useSubscription } from \"../hooks/index.js\";\n/**\n * @deprecated\n * Official support for React Apollo render prop components ended in March 2020.\n * This library is still included in the `@apollo/client` package,\n * but it no longer receives feature updates or bug fixes.\n */\nexport function Subscription(props) {\n    var result = useSubscription(props.subscription, props);\n    return props.children && result ? props.children(result) : null;\n}\nSubscription.propTypes = {\n    subscription: PropTypes.object.isRequired,\n    variables: PropTypes.object,\n    children: PropTypes.func,\n    onSubscriptionData: PropTypes.func,\n    onData: PropTypes.func,\n    onSubscriptionComplete: PropTypes.func,\n    onComplete: PropTypes.func,\n    shouldResubscribe: PropTypes.oneOfType([PropTypes.func, PropTypes.bool]),\n};\n//# sourceMappingURL=Subscription.js.map"], "names": ["__rest", "useQuery", "PropTypes", "useMutation", "useSubscription"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AASO,SAAS,KAAK,CAAC,KAAK,EAAE;AAC7B,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC,QAAQ,EAAE,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,OAAO,GAAGA,YAAM,CAAC,KAAK,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC;AACvG,IAAI,IAAI,MAAM,GAAGC,cAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAC1C,IAAI,OAAO,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC5C,CAAC;AACD,KAAK,CAAC,SAAS,GAAG;AAClB,IAAI,MAAM,EAAEC,oBAAS,CAAC,MAAM;AAC5B,IAAI,QAAQ,EAAEA,oBAAS,CAAC,IAAI,CAAC,UAAU;AACvC,IAAI,WAAW,EAAEA,oBAAS,CAAC,MAAM;AACjC,IAAI,2BAA2B,EAAEA,oBAAS,CAAC,IAAI;AAC/C,IAAI,WAAW,EAAEA,oBAAS,CAAC,IAAI;AAC/B,IAAI,OAAO,EAAEA,oBAAS,CAAC,IAAI;AAC3B,IAAI,YAAY,EAAEA,oBAAS,CAAC,MAAM;AAClC,IAAI,KAAK,EAAEA,oBAAS,CAAC,MAAM,CAAC,UAAU;AACtC,IAAI,SAAS,EAAEA,oBAAS,CAAC,MAAM;AAC/B,IAAI,GAAG,EAAEA,oBAAS,CAAC,IAAI;AACvB,IAAI,cAAc,EAAEA,oBAAS,CAAC,IAAI;AAClC,IAAI,iBAAiB,EAAEA,oBAAS,CAAC,IAAI;AACrC,CAAC;;ACnBM,SAAS,QAAQ,CAAC,KAAK,EAAE;AAChC,IAAI,IAAI,EAAE,GAAGC,iBAAW,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,WAAW,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AACrF,IAAI,OAAO,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC;AACvE,CAAC;AACD,QAAQ,CAAC,SAAS,GAAG;AACrB,IAAI,QAAQ,EAAED,oBAAS,CAAC,MAAM,CAAC,UAAU;AACzC,IAAI,SAAS,EAAEA,oBAAS,CAAC,MAAM;AAC/B,IAAI,kBAAkB,EAAEA,oBAAS,CAAC,SAAS,CAAC,CAACA,oBAAS,CAAC,MAAM,EAAEA,oBAAS,CAAC,IAAI,CAAC,CAAC;AAC/E,IAAI,cAAc,EAAEA,oBAAS,CAAC,SAAS,CAAC;AACxC,QAAQA,oBAAS,CAAC,OAAO,CAACA,oBAAS,CAAC,SAAS,CAAC,CAACA,oBAAS,CAAC,MAAM,EAAEA,oBAAS,CAAC,MAAM,CAAC,CAAC,CAAC;AACpF,QAAQA,oBAAS,CAAC,IAAI;AACtB,KAAK,CAAC;AACN,IAAI,mBAAmB,EAAEA,oBAAS,CAAC,IAAI;AACvC,IAAI,MAAM,EAAEA,oBAAS,CAAC,IAAI;AAC1B,IAAI,QAAQ,EAAEA,oBAAS,CAAC,IAAI,CAAC,UAAU;AACvC,IAAI,WAAW,EAAEA,oBAAS,CAAC,IAAI;AAC/B,IAAI,OAAO,EAAEA,oBAAS,CAAC,IAAI;AAC3B,IAAI,WAAW,EAAEA,oBAAS,CAAC,MAAM;AACjC,CAAC;;AClBM,SAAS,YAAY,CAAC,KAAK,EAAE;AACpC,IAAI,IAAI,MAAM,GAAGE,qBAAe,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AAC5D,IAAI,OAAO,KAAK,CAAC,QAAQ,IAAI,MAAM,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AACpE,CAAC;AACD,YAAY,CAAC,SAAS,GAAG;AACzB,IAAI,YAAY,EAAEF,oBAAS,CAAC,MAAM,CAAC,UAAU;AAC7C,IAAI,SAAS,EAAEA,oBAAS,CAAC,MAAM;AAC/B,IAAI,QAAQ,EAAEA,oBAAS,CAAC,IAAI;AAC5B,IAAI,kBAAkB,EAAEA,oBAAS,CAAC,IAAI;AACtC,IAAI,MAAM,EAAEA,oBAAS,CAAC,IAAI;AAC1B,IAAI,sBAAsB,EAAEA,oBAAS,CAAC,IAAI;AAC1C,IAAI,UAAU,EAAEA,oBAAS,CAAC,IAAI;AAC9B,IAAI,iBAAiB,EAAEA,oBAAS,CAAC,SAAS,CAAC,CAACA,oBAAS,CAAC,IAAI,EAAEA,oBAAS,CAAC,IAAI,CAAC,CAAC;AAC5E,CAAC;;;;;;"}