{"version": 3, "file": "Query.js", "sourceRoot": "", "sources": ["../../../src/react/components/Query.tsx"], "names": [], "mappings": ";AAAA,OAAO,KAAK,SAAS,MAAM,YAAY,CAAC;AAKxC,OAAO,EAAE,QAAQ,EAAE,MAAM,mBAAmB,CAAC;AAE7C;;;;;GAKG;AACH,MAAM,UAAU,KAAK,CAInB,KAA+C;IAEvC,IAAA,QAAQ,GAAwB,KAAK,SAA7B,EAAE,KAAK,GAAiB,KAAK,MAAtB,EAAK,OAAO,UAAK,KAAK,EAAvC,qBAA+B,CAAF,CAAW;IAC9C,IAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACxC,OAAO,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAa,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;AACjD,CAAC;AAMD,KAAK,CAAC,SAAS,GAAG;IAChB,MAAM,EAAE,SAAS,CAAC,MAAM;IACxB,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,UAAU;IACnC,WAAW,EAAE,SAAS,CAAC,MAAM;IAC7B,2BAA2B,EAAE,SAAS,CAAC,IAAI;IAC3C,WAAW,EAAE,SAAS,CAAC,IAAI;IAC3B,OAAO,EAAE,SAAS,CAAC,IAAI;IACvB,YAAY,EAAE,SAAS,CAAC,MAAM;IAC9B,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,UAAU;IAClC,SAAS,EAAE,SAAS,CAAC,MAAM;IAC3B,GAAG,EAAE,SAAS,CAAC,IAAI;IACnB,cAAc,EAAE,SAAS,CAAC,IAAI;IAC9B,iBAAiB,EAAE,SAAS,CAAC,IAAI;CACF,CAAC", "sourcesContent": ["import * as PropTypes from \"prop-types\";\nimport type * as ReactTypes from \"react\";\n\nimport type { OperationVariables } from \"../../core/index.js\";\nimport type { QueryComponentOptions } from \"./types.js\";\nimport { useQuery } from \"../hooks/index.js\";\n\n/**\n * @deprecated\n * Official support for React Apollo render prop components ended in March 2020.\n * This library is still included in the `@apollo/client` package,\n * but it no longer receives feature updates or bug fixes.\n */\nexport function Query<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  props: QueryComponentOptions<TData, TVariables>\n): ReactTypes.JSX.Element | null {\n  const { children, query, ...options } = props;\n  const result = useQuery(query, options);\n  return result ? children(result as any) : null;\n}\n\nexport interface Query<TData, TVariables extends OperationVariables> {\n  propTypes: PropTypes.InferProps<QueryComponentOptions<TData, TVariables>>;\n}\n\nQuery.propTypes = {\n  client: PropTypes.object,\n  children: PropTypes.func.isRequired,\n  fetchPolicy: PropTypes.string,\n  notifyOnNetworkStatusChange: PropTypes.bool,\n  onCompleted: PropTypes.func,\n  onError: PropTypes.func,\n  pollInterval: PropTypes.number,\n  query: PropTypes.object.isRequired,\n  variables: PropTypes.object,\n  ssr: PropTypes.bool,\n  partialRefetch: PropTypes.bool,\n  returnPartialData: PropTypes.bool,\n} as Query<any, any>[\"propTypes\"];\n"]}