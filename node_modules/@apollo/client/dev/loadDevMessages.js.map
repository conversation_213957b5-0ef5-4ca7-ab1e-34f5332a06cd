{"version": 3, "file": "loadDevMessages.js", "sourceRoot": "", "sources": ["../../src/dev/loadDevMessages.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,2BAA2B,CAAC;AAChF,OAAO,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AAEvE,MAAM,UAAU,eAAe;IAC7B,uBAAuB,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAC/D,CAAC", "sourcesContent": ["import { devDebug, devError, devLog, devWarn } from \"../invariantErrorCodes.js\";\nimport { loadErrorMessageHandler } from \"./loadErrorMessageHandler.js\";\n\nexport function loadDevMessages() {\n  loadErrorMessageHandler(devDebug, devError, devLog, devWarn);\n}\n"]}