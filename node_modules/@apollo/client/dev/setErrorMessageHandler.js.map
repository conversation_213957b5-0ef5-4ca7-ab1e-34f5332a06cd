{"version": 3, "file": "setErrorMessageHandler.js", "sourceRoot": "", "sources": ["../../src/dev/setErrorMessageHandler.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,+BAA+B,CAAC;AACvD,OAAO,EAAE,yBAAyB,EAAE,MAAM,2CAA2C,CAAC;AAgCtF;;GAEG;AACH,MAAM,UAAU,sBAAsB,CAAC,OAA4B;IACjE,MAAM,CAAC,yBAAyB,CAAC,GAAG,OAAsC,CAAC;AAC7E,CAAC", "sourcesContent": ["import type { ErrorCodes } from \"../invariantErrorCodes.js\";\nimport { global } from \"../utilities/globals/index.js\";\nimport { ApolloErrorMessageHandler } from \"../utilities/globals/invariantWrappers.js\";\n\n/**\n * The error message handler is a function that is called when a message is\n * logged or an error is thrown to determine the contents of the error message\n * to be logged or thrown.\n */\nexport type ErrorMessageHandler = {\n  /**\n   * @param message - Usually the error message number (as defined in\n   * `@apollo/client/invariantErrorCodes.js`).\n   * In some edge cases, this can already be a string, that can be passed through\n   * as an error message.\n   *\n   * @param args - The placeholders that can be passed into the error message (pre-stringified).\n   * These relate with the `%s` and `%d` [substitution strings](https://developer.mozilla.org/en-US/docs/Web/API/console#using_string_substitutions)\n   * in the error message defined in `@apollo/client/invariantErrorCodes.js`.\n   *\n   * ⚠️ Note that arguments will only be passed in for error messages.\n   * For normal log messages, you will get an empty array here and they will directly\n   * be passed to `console.log` instead, to have the string subsitution done by the\n   * engine, as that allows for nicer (and in the case of a browser, interactive)\n   * output.\n   *\n   * @returns The error message to be logged or thrown. If it returns `undefined`,\n   *          the mechanism will fall back to the default:\n   *          A link to https://go.apollo.dev/c/err with Apollo Client version,\n   *          the error message number, and the error message arguments encoded into\n   *          the URL hash.\n   */ (message: string | number, args: string[]): string | undefined;\n};\n\n/**\n * Overrides the global \"Error Message Handler\" with a custom implementation.\n */\nexport function setErrorMessageHandler(handler: ErrorMessageHandler) {\n  global[ApolloErrorMessageHandler] = handler as typeof handler & ErrorCodes;\n}\n"]}