'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var globals = require('../utilities/globals');
var tsInvariant = require('ts-invariant');

const errorCodes =
{
  2: {
    file: "@apollo/client/cache/inmemory/entityStore.js",
    condition: "typeof dataId === \"string\"",
    message: "store.merge expects a string ID"
  },
  5: {
    file: "@apollo/client/cache/inmemory/key-extractor.js",
    condition: "extracted !== void 0",
    message: "Missing field '%s' while extracting keyFields from %s"
  },
  6: {
    file: "@apollo/client/cache/inmemory/policies.js",
    condition: "!old || old === which",
    message: "Cannot change root %s __typename more than once"
  },
  9: {
    file: "@apollo/client/cache/inmemory/policies.js",
    message: "Cannot automatically merge arrays"
  },
  10: {
    file: "@apollo/client/cache/inmemory/readFromStore.js",
    message: "No fragment named %s"
  },
  11: {
    file: "@apollo/client/cache/inmemory/readFromStore.js",
    condition: "!isReference(value)",
    message: "Missing selection set for object of type %s returned for query field %s"
  },
  12: {
    file: "@apollo/client/cache/inmemory/writeToStore.js",
    message: "Could not identify object %s"
  },
  14: {
    file: "@apollo/client/cache/inmemory/writeToStore.js",
    message: "No fragment named %s"
  },
  16: {
    file: "@apollo/client/core/ApolloClient.js",
    message: "To initialize Apollo Client, you must specify a 'cache' property " +
        "in the options object. \n" +
        "For more information, please visit: https://go.apollo.dev/c/docs"
  },
  17: {
    file: "@apollo/client/core/ApolloClient.js",
    condition: "options.fetchPolicy !== \"cache-and-network\"",
    message: "The cache-and-network fetchPolicy does not work with client.query, because " +
        "client.query can only return a single result. Please use client.watchQuery " +
        "to receive multiple results from the cache and the network, or consider " +
        "using a different fetchPolicy, such as cache-first or network-only."
  },
  19: {
    file: "@apollo/client/core/LocalState.js",
    condition: "fragment",
    message: "No fragment named %s"
  },
  20: {
    file: "@apollo/client/core/LocalState.js",
    condition: "fragment",
    message: "No fragment named %s"
  },
  22: {
    file: "@apollo/client/core/ObservableQuery.js",
    condition: "updateQuery",
    message: "You must provide an `updateQuery` function when using `fetchMore` with a `no-cache` fetch policy."
  },
  24: {
    file: "@apollo/client/core/ObservableQuery.js",
    condition: "pollInterval",
    message: "Attempted to start a polling query without a polling interval."
  },
  27: {
    file: "@apollo/client/core/QueryManager.js",
    message: "QueryManager stopped while query was in flight"
  },
  28: {
    file: "@apollo/client/core/QueryManager.js",
    condition: "mutation",
    message: "mutation option is required. You must specify your GraphQL document in the mutation option."
  },
  29: {
    file: "@apollo/client/core/QueryManager.js",
    condition: "fetchPolicy === \"network-only\" || fetchPolicy === \"no-cache\"",
    message: "Mutations support only 'network-only' or 'no-cache' fetchPolicy strings. The default `network-only` behavior automatically writes mutation results to the cache. Passing `no-cache` skips the cache write."
  },
  30: {
    file: "@apollo/client/core/QueryManager.js",
    condition: "options.query",
    message: "query option is required. You must specify your GraphQL document " +
        "in the query option."
  },
  31: {
    file: "@apollo/client/core/QueryManager.js",
    condition: "options.query.kind === \"Document\"",
    message: 'You must wrap the query string in a "gql" tag.'
  },
  32: {
    file: "@apollo/client/core/QueryManager.js",
    condition: "!options.returnPartialData",
    message: "returnPartialData option only supported on watchQuery."
  },
  33: {
    file: "@apollo/client/core/QueryManager.js",
    condition: "!options.pollInterval",
    message: "pollInterval option only supported on watchQuery."
  },
  34: {
    file: "@apollo/client/core/QueryManager.js",
    message: "Store reset while query was in flight (not completed in link chain)"
  },
  39: {
    file: "@apollo/client/link/core/ApolloLink.js",
    message: "request is not implemented"
  },
  40: {
    file: "@apollo/client/link/http/checkFetcher.js",
    message: "\n\"fetch\" has not been found globally and no fetcher has been configured. To fix this, install a fetch package (like https://www.npmjs.com/package/cross-fetch), instantiate the fetcher, and pass it into your HttpLink constructor. For example:\n\nimport fetch from 'cross-fetch';\nimport { ApolloClient, HttpLink } from '@apollo/client';\nconst client = new ApolloClient({\n  link: new HttpLink({ uri: '/graphql', fetch })\n});\n    "
  },
  42: {
    file: "@apollo/client/link/http/serializeFetchParameter.js",
    message: "Network request failed. %s is not serializable: %s"
  },
  43: {
    file: "@apollo/client/link/persisted-queries/index.js",
    condition: "options &&\n    (typeof options.sha256 === \"function\" ||\n        typeof options.generateHash === \"function\")",
    message: 'Missing/invalid "sha256" or "generateHash" function. Please ' +
        'configure one using the "createPersistedQueryLink(options)" options ' +
        "parameter."
  },
  44: {
    file: "@apollo/client/link/persisted-queries/index.js",
    condition: "forward",
    message: "PersistedQueryLink cannot be the last link in the chain."
  },
  46: {
    file: "@apollo/client/link/utils/validateOperation.js",
    message: "illegal argument: %s"
  },
  47: {
    file: "@apollo/client/masking/maskDefinition.js",
    condition: "fragment",
    message: "Could not find fragment with name '%s'."
  },
  49: {
    file: "@apollo/client/masking/maskFragment.js",
    condition: "fragments.length === 1",
    message: "Found %s fragments. `fragmentName` must be provided when there is not exactly 1 fragment."
  },
  50: {
    file: "@apollo/client/masking/maskFragment.js",
    condition: "!!fragment",
    message: "Could not find fragment with name \"%s\"."
  },
  51: {
    file: "@apollo/client/masking/maskOperation.js",
    condition: "definition",
    message: "Expected a parsed GraphQL document with a query, mutation, or subscription."
  },
  53: {
    file: "@apollo/client/react/context/ApolloConsumer.js",
    condition: "context && context.client",
    message: 'Could not find "client" in the context of ApolloConsumer. ' +
        "Wrap the root component in an <ApolloProvider>."
  },
  54: {
    file: "@apollo/client/react/context/ApolloContext.js",
    condition: "\"createContext\" in React",
    message: "Invoking `getApolloContext` in an environment where `React.createContext` is not available.\n" +
        "The Apollo Client functionality you are trying to use is only available in React Client Components.\n" +
        'Please make sure to add "use client" at the top of your file.\n' +
        "For more information, see https://nextjs.org/docs/getting-started/react-essentials#client-components"
  },
  55: {
    file: "@apollo/client/react/context/ApolloProvider.js",
    condition: "context.client",
    message: "ApolloProvider was not passed a client instance. Make " +
        'sure you pass in your client via the "client" prop.'
  },
  56: {
    file: "@apollo/client/react/hoc/hoc-utils.js",
    condition: "this.withRef",
    message: "To access the wrapped instance, you need to specify " +
        "{ withRef: true } in the options"
  },
  57: {
    file: "@apollo/client/react/hoc/withApollo.js",
    condition: "operationOptions.withRef",
    message: "To access the wrapped instance, you need to specify " +
        "{ withRef: true } in the options"
  },
  58: {
    file: "@apollo/client/react/hooks/useApolloClient.js",
    condition: "!!client",
    message: 'Could not find "client" in the context or passed in as an option. ' +
        "Wrap the root component in an <ApolloProvider>, or pass an ApolloClient " +
        "instance in via options."
  },
  59: {
    file: "@apollo/client/react/hooks/useLoadableQuery.js",
    condition: "!calledDuringRender()",
    message: "useLoadableQuery: 'loadQuery' should not be called during render. To start a query during render, use the 'useBackgroundQuery' hook."
  },
  60: {
    file: "@apollo/client/react/hooks/useLoadableQuery.js",
    condition: "internalQueryRef",
    message: "The query has not been loaded. Please load the query."
  },
  65: {
    file: "@apollo/client/react/hooks/useSubscription.js",
    condition: "!optionsRef.current.skip",
    message: "A subscription that is skipped cannot be restarted."
  },
  66: {
    file: "@apollo/client/react/hooks/useSuspenseQuery.js",
    condition: "supportedFetchPolicies.includes(fetchPolicy)",
    message: "The fetch policy `%s` is not supported with suspense."
  },
  69: {
    file: "@apollo/client/react/internal/cache/QueryReference.js",
    condition: "!queryRef || QUERY_REFERENCE_SYMBOL in queryRef",
    message: "Expected a QueryRef object, but got something else instead."
  },
  70: {
    file: "@apollo/client/react/parser/index.js",
    condition: "!!document && !!document.kind",
    message: "Argument of %s passed to parser was not a valid GraphQL " +
        "DocumentNode. You may need to use 'graphql-tag' or another method " +
        "to convert your operation into a document"
  },
  71: {
    file: "@apollo/client/react/parser/index.js",
    condition: "!fragments.length ||\n    queries.length ||\n    mutations.length ||\n    subscriptions.length",
    message: "Passing only a fragment to 'graphql' is not yet supported. " +
        "You must include a query, subscription or mutation as well"
  },
  72: {
    file: "@apollo/client/react/parser/index.js",
    condition: "queries.length + mutations.length + subscriptions.length <= 1",
    message: "react-apollo only supports a query, subscription, or a mutation per HOC. " +
        "%s had %s queries, %s " +
        "subscriptions and %s mutations. " +
        "You can use 'compose' to join multiple operation types to a component"
  },
  73: {
    file: "@apollo/client/react/parser/index.js",
    condition: "definitions.length === 1",
    message: "react-apollo only supports one definition per HOC. %s had " +
        "%s definitions. " +
        "You can use 'compose' to join multiple operation types to a component"
  },
  74: {
    file: "@apollo/client/react/parser/index.js",
    condition: "operation.type === type",
    message: "Running a %s requires a graphql " + "%s, but a %s was used instead."
  },
  75: {
    file: "@apollo/client/testing/core/mocking/mockLink.js",
    condition: "queryWithoutClientOnlyDirectives",
    message: "query is required"
  },
  76: {
    file: "@apollo/client/testing/core/mocking/mockLink.js",
    condition: "mockedResponse.maxUsageCount > 0",
    message: "Mock response maxUsageCount must be greater than 0, %s given"
  },
  77: {
    file: "@apollo/client/utilities/graphql/DocumentTransform.js",
    condition: "Array.isArray(cacheKeys)",
    message: "`getCacheKey` must return an array or undefined"
  },
  78: {
    file: "@apollo/client/utilities/graphql/directives.js",
    condition: "evaledValue !== void 0",
    message: "Invalid variable referenced in @%s directive."
  },
  79: {
    file: "@apollo/client/utilities/graphql/directives.js",
    condition: "directiveArguments && directiveArguments.length === 1",
    message: "Incorrect number of arguments for the @%s directive."
  },
  80: {
    file: "@apollo/client/utilities/graphql/directives.js",
    condition: "ifArgument.name && ifArgument.name.value === \"if\"",
    message: "Invalid argument for the @%s directive."
  },
  81: {
    file: "@apollo/client/utilities/graphql/directives.js",
    condition: "ifValue &&\n    (ifValue.kind === \"Variable\" || ifValue.kind === \"BooleanValue\")",
    message: "Argument for the @%s directive must be a variable or a boolean value."
  },
  85: {
    file: "@apollo/client/utilities/graphql/fragments.js",
    message: "Found a %s operation%s. " +
        "No operations are allowed when using a fragment as a query. Only fragments are allowed."
  },
  86: {
    file: "@apollo/client/utilities/graphql/fragments.js",
    condition: "fragments.length === 1",
    message: "Found %s fragments. `fragmentName` must be provided when there is not exactly 1 fragment."
  },
  87: {
    file: "@apollo/client/utilities/graphql/fragments.js",
    condition: "fragment",
    message: "No fragment named %s"
  },
  88: {
    file: "@apollo/client/utilities/graphql/getFromAST.js",
    condition: "doc && doc.kind === \"Document\"",
    message: "Expecting a parsed GraphQL document. Perhaps you need to wrap the query string in a \"gql\" tag? http://docs.apollostack.com/apollo-client/core.html#gql"
  },
  89: {
    file: "@apollo/client/utilities/graphql/getFromAST.js",
    message: "Schema type definitions not allowed in queries. Found: \"%s\""
  },
  90: {
    file: "@apollo/client/utilities/graphql/getFromAST.js",
    condition: "operations.length <= 1",
    message: "Ambiguous GraphQL document: contains %s operations"
  },
  91: {
    file: "@apollo/client/utilities/graphql/getFromAST.js",
    condition: "queryDef && queryDef.operation === \"query\"",
    message: "Must contain a query definition."
  },
  92: {
    file: "@apollo/client/utilities/graphql/getFromAST.js",
    condition: "doc.kind === \"Document\"",
    message: "Expecting a parsed GraphQL document. Perhaps you need to wrap the query string in a \"gql\" tag? http://docs.apollostack.com/apollo-client/core.html#gql"
  },
  93: {
    file: "@apollo/client/utilities/graphql/getFromAST.js",
    condition: "doc.definitions.length <= 1",
    message: "Fragment must have exactly one definition."
  },
  94: {
    file: "@apollo/client/utilities/graphql/getFromAST.js",
    condition: "fragmentDef.kind === \"FragmentDefinition\"",
    message: "Must be a fragment definition."
  },
  95: {
    file: "@apollo/client/utilities/graphql/getFromAST.js",
    message: "Expected a parsed GraphQL query with a query, mutation, subscription, or a fragment."
  },
  96: {
    file: "@apollo/client/utilities/graphql/storeUtils.js",
    message: "The inline argument \"%s\" of kind \"%s\"" +
        "is not supported. Use variables instead of inline arguments to " +
        "overcome this limitation."
  }
};
const devDebug = {
  18: {
    file: "@apollo/client/core/ApolloClient.js",
    message: "In client.refetchQueries, Promise.all promise rejected with error %o"
  },
  26: {
    file: "@apollo/client/core/ObservableQuery.js",
    message: "Missing cache result fields: %o"
  }
};
const devLog = {};
const devWarn = {
  1: {
    file: "@apollo/client/cache/core/cache.js",
    message: "Could not identify object passed to `from` for '%s' fragment, either because the object is non-normalized or the key fields are missing. If you are masking this object, please ensure the key fields are requested by the parent object."
  },
  3: {
    file: "@apollo/client/cache/inmemory/entityStore.js",
    message: "cache.modify: You are trying to write a Reference that is not part of the store: %o\n" +
        "Please make sure to set the `mergeIntoStore` parameter to `true` when creating a Reference that is not part of the store yet:\n" +
        "`toReference(object, true)`"
  },
  4: {
    file: "@apollo/client/cache/inmemory/entityStore.js",
    message: "cache.modify: Writing an array with a mix of both References and Objects will not result in the Objects being normalized correctly.\n" +
        "Please convert the object instance %o to a Reference before writing it to the cache by calling `toReference(object, true)`."
  },
  7: {
    file: "@apollo/client/cache/inmemory/policies.js",
    message: "Inferring subtype %s of supertype %s"
  },
  8: {
    file: "@apollo/client/cache/inmemory/policies.js",
    message: "Undefined 'from' passed to readField with arguments %s"
  },
  15: {
    file: "@apollo/client/cache/inmemory/writeToStore.js",
    message: "Cache data may be lost when replacing the %s field of a %s object.\n\nThis could cause additional (usually avoidable) network requests to fetch data that were otherwise cached.\n\nTo address this problem (which is not a bug in Apollo Client), %sdefine a custom merge function for the %s field, so InMemoryCache can safely merge these objects:\n\n  existing: %o\n  incoming: %o\n\nFor more information about these options, please refer to the documentation:\n\n  * Ensuring entity objects have IDs: https://go.apollo.dev/c/generating-unique-identifiers\n  * Defining custom merge functions: https://go.apollo.dev/c/merging-non-normalized-objects\n"
  },
  21: {
    file: "@apollo/client/core/ObservableQuery.js",
    message: "Called refetch(%o) for query %o, which does not declare a $variables variable.\nDid you mean to call refetch(variables) instead of refetch({ variables })?"
  },
  35: {
    file: "@apollo/client/core/QueryManager.js",
    message: "Unknown query named \"%s\" requested in refetchQueries options.include array"
  },
  36: {
    file: "@apollo/client/core/QueryManager.js",
    message: "Unknown anonymous query requested in refetchQueries options.include array"
  },
  37: {
    file: "@apollo/client/core/QueryManager.js",
    message: '[%s]: Fragments masked by data masking are inaccessible when using fetch policy "no-cache". Please add `@unmask` to each fragment spread to access the data.'
  },
  38: {
    file: "@apollo/client/link/core/ApolloLink.js",
    message: "You are calling concat on a terminating link, which will have no effect %o"
  },
  41: {
    file: "@apollo/client/link/http/createHttpLink.js",
    message: "Multipart-subscriptions do not support @defer"
  },
  45: {
    file: "@apollo/client/link/utils/toPromise.js",
    message: "Promise Wrapper does not support multiple results from Observable"
  },
  48: {
    file: "@apollo/client/masking/maskDefinition.js",
    message: "Accessing unmasked field on %s at path '%s'. This field will not be available when masking is enabled. Please read the field from the fragment instead."
  },
  52: {
    file: "@apollo/client/masking/utils.js",
    message: "The configured cache does not support data masking which effectively disables it. Please use a cache that supports data masking or disable data masking to silence this warning."
  },
  61: {
    file: "@apollo/client/react/hooks/useSubscription.js",
    message: "'useSubscription' supports only the 'onSubscriptionData' or 'onData' option, but not both. Only the 'onData' option will be used."
  },
  62: {
    file: "@apollo/client/react/hooks/useSubscription.js",
    message: "'onSubscriptionData' is deprecated and will be removed in a future major version. Please use the 'onData' option instead."
  },
  63: {
    file: "@apollo/client/react/hooks/useSubscription.js",
    message: "'useSubscription' supports only the 'onSubscriptionComplete' or 'onComplete' option, but not both. Only the 'onComplete' option will be used."
  },
  64: {
    file: "@apollo/client/react/hooks/useSubscription.js",
    message: "'onSubscriptionComplete' is deprecated and will be removed in a future major version. Please use the 'onComplete' option instead."
  },
  67: {
    file: "@apollo/client/react/hooks/useSuspenseQuery.js",
    message: "Using `returnPartialData` with a `no-cache` fetch policy has no effect. To read partial data from the cache, consider using an alternate fetch policy."
  },
  82: {
    file: "@apollo/client/utilities/graphql/directives.js",
    message: "@unmask 'mode' argument does not support variables."
  },
  83: {
    file: "@apollo/client/utilities/graphql/directives.js",
    message: "@unmask 'mode' argument must be of type string."
  },
  84: {
    file: "@apollo/client/utilities/graphql/directives.js",
    message: "@unmask 'mode' argument does not recognize value '%s'."
  },
  98: {
    file: "@apollo/client/utilities/graphql/transform.js",
    message: "Removing an @connection directive even though it does not have a key. " +
        "You may want to use the key parameter to specify a store key."
  }
};
const devError = {
  13: {
    file: "@apollo/client/cache/inmemory/writeToStore.js",
    message: "Missing field '%s' while writing result %o"
  },
  23: {
    file: "@apollo/client/core/ObservableQuery.js",
    message: "Unhandled GraphQL subscription error"
  },
  25: {
    file: "@apollo/client/core/ObservableQuery.js",
    message: "Unhandled error"
  },
  68: {
    file: "@apollo/client/react/hooks/useSyncExternalStore.js",
    message: "The result of getSnapshot should be cached to avoid an infinite loop"
  },
  97: {
    file: "@apollo/client/utilities/graphql/transform.js",
    message: "Could not find operation or fragment"
  }
};

var version = "3.13.8";

function maybe(thunk) {
    try {
        return thunk();
    }
    catch (_a) { }
}

var global$1 = (maybe(function () { return globalThis; }) ||
    maybe(function () { return window; }) ||
    maybe(function () { return self; }) ||
    maybe(function () { return global; }) ||
maybe(function () {
    return maybe.constructor("return this")();
}));

var prefixCounts = new Map();
function makeUniqueId(prefix) {
    var count = prefixCounts.get(prefix) || 1;
    prefixCounts.set(prefix, count + 1);
    return "".concat(prefix, ":").concat(count, ":").concat(Math.random().toString(36).slice(2));
}

function stringifyForDisplay(value, space) {
    if (space === void 0) { space = 0; }
    var undefId = makeUniqueId("stringifyForDisplay");
    return JSON.stringify(value, function (key, value) {
        return value === void 0 ? undefId : value;
    }, space)
        .split(JSON.stringify(undefId))
        .join("<undefined>");
}

function wrap(fn) {
    return function (message) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (typeof message === "number") {
            var arg0 = message;
            message = getHandledErrorMsg(arg0);
            if (!message) {
                message = getFallbackErrorMsg(arg0, args);
                args = [];
            }
        }
        fn.apply(void 0, [message].concat(args));
    };
}
Object.assign(function invariant(condition, message) {
    var args = [];
    for (var _i = 2; _i < arguments.length; _i++) {
        args[_i - 2] = arguments[_i];
    }
    if (!condition) {
        tsInvariant.invariant(condition, getHandledErrorMsg(message, args) || getFallbackErrorMsg(message, args));
    }
}, {
    debug: wrap(tsInvariant.invariant.debug),
    log: wrap(tsInvariant.invariant.log),
    warn: wrap(tsInvariant.invariant.warn),
    error: wrap(tsInvariant.invariant.error),
});
var ApolloErrorMessageHandler = Symbol.for("ApolloErrorMessageHandler_" + version);
function stringify(arg) {
    if (typeof arg == "string") {
        return arg;
    }
    try {
        return stringifyForDisplay(arg, 2).slice(0, 1000);
    }
    catch (_a) {
        return "<non-serializable>";
    }
}
function getHandledErrorMsg(message, messageArgs) {
    if (messageArgs === void 0) { messageArgs = []; }
    if (!message)
        return;
    return (global$1[ApolloErrorMessageHandler] &&
        global$1[ApolloErrorMessageHandler](message, messageArgs.map(stringify)));
}
function getFallbackErrorMsg(message, messageArgs) {
    if (messageArgs === void 0) { messageArgs = []; }
    if (!message)
        return;
    return "An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#".concat(encodeURIComponent(JSON.stringify({
        version: version,
        message: message,
        args: messageArgs.map(stringify),
    })));
}

function setErrorMessageHandler(handler) {
    globals.global[ApolloErrorMessageHandler] = handler;
}

function loadErrorMessageHandler() {
    var errorCodes = [];
    for (var _i = 0; _i < arguments.length; _i++) {
        errorCodes[_i] = arguments[_i];
    }
    setErrorMessageHandler(handler);
    for (var _a = 0, errorCodes_1 = errorCodes; _a < errorCodes_1.length; _a++) {
        var codes = errorCodes_1[_a];
        Object.assign(handler, codes);
    }
    return handler;
}
var handler = (function (message, args) {
    if (typeof message === "number") {
        var definition = globals.global[ApolloErrorMessageHandler][message];
        if (!message || !(definition === null || definition === void 0 ? void 0 : definition.message))
            return;
        message = definition.message;
    }
    return args.reduce(function (msg, arg) { return msg.replace(/%[sdfo]/, String(arg)); }, String(message));
});

function loadDevMessages() {
    loadErrorMessageHandler(devDebug, devError, devLog, devWarn);
}

function loadErrorMessages() {
    loadErrorMessageHandler(errorCodes);
}

exports.loadDevMessages = loadDevMessages;
exports.loadErrorMessageHandler = loadErrorMessageHandler;
exports.loadErrorMessages = loadErrorMessages;
exports.setErrorMessageHandler = setErrorMessageHandler;
//# sourceMappingURL=dev.cjs.map
