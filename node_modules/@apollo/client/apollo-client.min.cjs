"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("tslib"),t=require("ts-invariant"),r=require("@wry/equality"),n=require("graphql"),i=require("@wry/caches"),o=require("optimism"),a=require("zen-observable-ts");require("symbol-observable");var s=require("@wry/trie"),u=require("graphql-tag"),c=require("rehackt");function l(e){return e&&"object"==typeof e&&"default"in e?e.default:e}function f(e){if(e&&e.__esModule)return e;var t=Object.create(null);if(e)for(var r in e)t[r]=e[r];return t.default=e,Object.freeze(t)}var h=l(r),p=f(c),d="3.13.8";function v(e){try{return e()}catch(e){}}var y=v((function(){return globalThis}))||v((function(){return window}))||v((function(){return self}))||v((function(){return global}))||v((function(){return v.constructor("return this")()})),m=new Map;function g(e){var t=m.get(e)||1;return m.set(e,t+1),"".concat(e,":").concat(t,":").concat(Math.random().toString(36).slice(2))}function b(e,t){void 0===t&&(t=0);var r=g("stringifyForDisplay");return JSON.stringify(e,(function(e,t){return void 0===t?r:t}),t).split(JSON.stringify(r)).join("<undefined>")}function _(e){return function(t){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];if("number"==typeof t){var i=t;(t=x(i))||(t=q(i,r),r=[])}e.apply(void 0,[t].concat(r))}}var k=Object.assign((function(e,r){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];e||t.invariant(e,x(r,n)||q(r,n))}),{debug:_(t.invariant.debug),log:_(t.invariant.log),warn:_(t.invariant.warn),error:_(t.invariant.error)});function w(e){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return new t.InvariantError(x(e,r)||q(e,r))}var S=Symbol.for("ApolloErrorMessageHandler_"+d);function O(e){if("string"==typeof e)return e;try{return b(e,2).slice(0,1e3)}catch(e){return"<non-serializable>"}}function x(e,t){if(void 0===t&&(t=[]),e)return y[S]&&y[S](e,t.map(O))}function q(e,t){if(void 0===t&&(t=[]),e)return"An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#".concat(encodeURIComponent(JSON.stringify({version:d,message:e,args:t.map(O)})))}function R(e,t){var r=e.directives;return!r||!r.length||function(e){var t=[];e&&e.length&&e.forEach((function(e){if(function(e){var t=e.name.value;return"skip"===t||"include"===t}(e)){var r=e.arguments,n=e.name.value;k(r&&1===r.length,79,n);var i=r[0];k(i.name&&"if"===i.name.value,80,n);var o=i.value;k(o&&("Variable"===o.kind||"BooleanValue"===o.kind),81,n),t.push({directive:e,ifArgument:i})}}));return t}(r).every((function(e){var r=e.directive,n=e.ifArgument,i=!1;return"Variable"===n.value.kind?(i=t&&t[n.value.name.value],k(void 0!==i,78,r.name.value)):i=n.value.value,"skip"===r.name.value?!i:i}))}function E(e,t,r){var i=new Set(e),o=i.size;return n.visit(t,{Directive:function(e){if(i.delete(e.name.value)&&(!r||!i.size))return n.BREAK}}),r?!i.size:i.size<o}function P(e){return e&&E(["client","export"],e,!0)}function T(e){var t,r,n=null===(t=e.directives)||void 0===t?void 0:t.find((function(e){return"unmask"===e.name.value}));if(!n)return"mask";var i=null===(r=n.arguments)||void 0===r?void 0:r.find((function(e){return"mode"===e.name.value}));return i&&"value"in i.value&&"migrate"===i.value.value?"migrate":"unmask"}var C="ReactNative"==v((function(){return navigator.product})),D="function"==typeof WeakMap&&!(C&&!global.HermesInternal),F="function"==typeof WeakSet,M="function"==typeof Symbol&&"function"==typeof Symbol.for,Q=M&&Symbol.asyncIterator,I="function"==typeof v((function(){return window.document.createElement})),N=v((function(){return navigator.userAgent.indexOf("jsdom")>=0}))||!1,j=(I||C)&&!N;function A(e){return null!==e&&"object"==typeof e}function L(t,r){var n=r,i=[];return t.definitions.forEach((function(e){if("OperationDefinition"===e.kind)throw w(85,e.operation,e.name?" named '".concat(e.name.value,"'"):"");"FragmentDefinition"===e.kind&&i.push(e)})),void 0===n&&(k(1===i.length,86,i.length),n=i[0].name.value),e.__assign(e.__assign({},t),{definitions:e.__spreadArray([{kind:"OperationDefinition",operation:"query",selectionSet:{kind:"SelectionSet",selections:[{kind:"FragmentSpread",name:{kind:"Name",value:n}}]}}],t.definitions,!0)})}function V(e){void 0===e&&(e=[]);var t={};return e.forEach((function(e){t[e.name.value]=e})),t}function W(e,t){switch(e.kind){case"InlineFragment":return e;case"FragmentSpread":var r=e.name.value;if("function"==typeof t)return t(r);var n=t&&t[r];return k(n,87,r),n||null;default:return null}}var z=new WeakSet;function B(e){e.size<=(e.max||-1)||z.has(e)||(z.add(e),setTimeout((function(){e.clean(),z.delete(e)}),100))}var K=function(e,t){var r=new i.WeakCache(e,t);return r.set=function(e,t){var r=i.WeakCache.prototype.set.call(this,e,t);return B(this),r},r},U=function(e,t){var r=new i.StrongCache(e,t);return r.set=function(e,t){var r=i.StrongCache.prototype.set.call(this,e,t);return B(this),r},r},J=Symbol.for("apollo.cacheSize"),H=e.__assign({},y[J]);var G,Y=Object.assign((function(e){return JSON.stringify(e,X)}),{reset:function(){G=new U(H.canonicalStringify||1e3)}});function X(e,t){if(t&&"object"==typeof t){var r=Object.getPrototypeOf(t);if(r===Object.prototype||null===r){var n=Object.keys(t);if(n.every($))return t;var i=JSON.stringify(n),o=G.get(i);if(!o){n.sort();var a=JSON.stringify(n);o=G.get(a)||n,G.set(i,o),G.set(a,o)}var s=Object.create(r);return o.forEach((function(e){s[e]=t[e]})),s}}return t}function $(e,t,r){return 0===t||r[t-1]<=e}function Z(e){return{__ref:String(e)}}function ee(e){return Boolean(e&&"object"==typeof e&&"string"==typeof e.__ref)}function te(e,t,r,n){if(function(e){return"IntValue"===e.kind}(r)||function(e){return"FloatValue"===e.kind}(r))e[t.value]=Number(r.value);else if(function(e){return"BooleanValue"===e.kind}(r)||function(e){return"StringValue"===e.kind}(r))e[t.value]=r.value;else if(function(e){return"ObjectValue"===e.kind}(r)){var i={};r.fields.map((function(e){return te(i,e.name,e.value,n)})),e[t.value]=i}else if(function(e){return"Variable"===e.kind}(r)){var o=(n||{})[r.name.value];e[t.value]=o}else if(function(e){return"ListValue"===e.kind}(r))e[t.value]=r.values.map((function(e){var r={};return te(r,t,e,n),r[t.value]}));else if(function(e){return"EnumValue"===e.kind}(r))e[t.value]=r.value;else{if(!function(e){return"NullValue"===e.kind}(r))throw w(96,t.value,r.kind);e[t.value]=null}}Y.reset();var re=["connection","include","skip","client","rest","export","nonreactive"],ne=Y,ie=Object.assign((function(e,t,r){if(t&&r&&r.connection&&r.connection.key){if(r.connection.filter&&r.connection.filter.length>0){var n=r.connection.filter?r.connection.filter:[];n.sort();var i={};return n.forEach((function(e){i[e]=t[e]})),"".concat(r.connection.key,"(").concat(ne(i),")")}return r.connection.key}var o=e;if(t){var a=ne(t);o+="(".concat(a,")")}return r&&Object.keys(r).forEach((function(e){-1===re.indexOf(e)&&(r[e]&&Object.keys(r[e]).length?o+="@".concat(e,"(").concat(ne(r[e]),")"):o+="@".concat(e))})),o}),{setStringify:function(e){var t=ne;return ne=e,t}});function oe(e,t){if(e.arguments&&e.arguments.length){var r={};return e.arguments.forEach((function(e){var n=e.name,i=e.value;return te(r,n,i,t)})),r}return null}function ae(e){return e.alias?e.alias.value:e.name.value}function se(e,t,r){for(var n,i=0,o=t.selections;i<o.length;i++){if(ue(u=o[i])){if("__typename"===u.name.value)return e[ae(u)]}else n?n.push(u):n=[u]}if("string"==typeof e.__typename)return e.__typename;if(n)for(var a=0,s=n;a<s.length;a++){var u,c=se(e,W(u=s[a],r).selectionSet,r);if("string"==typeof c)return c}}function ue(e){return"Field"===e.kind}function ce(e){k(e&&"Document"===e.kind,88);var t=e.definitions.filter((function(e){return"FragmentDefinition"!==e.kind})).map((function(e){if("OperationDefinition"!==e.kind)throw w(89,e.kind);return e}));return k(t.length<=1,90,t.length),e}function le(e){return ce(e),e.definitions.filter((function(e){return"OperationDefinition"===e.kind}))[0]}function fe(e){return e.definitions.filter((function(e){return"OperationDefinition"===e.kind&&!!e.name})).map((function(e){return e.name.value}))[0]||null}function he(e){return e.definitions.filter((function(e){return"FragmentDefinition"===e.kind}))}function pe(e){var t=le(e);return k(t&&"query"===t.operation,91),t}function de(e){k("Document"===e.kind,92),k(e.definitions.length<=1,93);var t=e.definitions[0];return k("FragmentDefinition"===t.kind,94),t}function ve(e){var t;ce(e);for(var r=0,n=e.definitions;r<n.length;r++){var i=n[r];if("OperationDefinition"===i.kind){var o=i.operation;if("query"===o||"mutation"===o||"subscription"===o)return i}"FragmentDefinition"!==i.kind||t||(t=i)}if(t)return t;throw w(95)}function ye(e){var t=Object.create(null),r=e&&e.variableDefinitions;return r&&r.length&&r.forEach((function(e){e.defaultValue&&te(t,e.variable.name,e.defaultValue)})),t}function me(e){return e}var ge,be=function(){function e(e,t){void 0===t&&(t=Object.create(null)),this.resultCache=F?new WeakSet:new Set,this.transform=e,t.getCacheKey&&(this.getCacheKey=t.getCacheKey),this.cached=!1!==t.cache,this.resetCache()}return e.prototype.getCacheKey=function(e){return[e]},e.identity=function(){return new e(me,{cache:!1})},e.split=function(t,r,n){return void 0===n&&(n=e.identity()),Object.assign(new e((function(e){return(t(e)?r:n).transformDocument(e)}),{cache:!1}),{left:r,right:n})},e.prototype.resetCache=function(){var t=this;if(this.cached){var r=new s.Trie(D);this.performWork=o.wrap(e.prototype.performWork.bind(this),{makeCacheKey:function(e){var n=t.getCacheKey(e);if(n)return k(Array.isArray(n),77),r.lookupArray(n)},max:H["documentTransform.cache"],cache:i.WeakCache})}},e.prototype.performWork=function(e){return ce(e),this.transform(e)},e.prototype.transformDocument=function(e){if(this.resultCache.has(e))return e;var t=this.performWork(e);return this.resultCache.add(t),t},e.prototype.concat=function(t){var r=this;return Object.assign(new e((function(e){return t.transformDocument(r.transformDocument(e))}),{cache:!1}),{left:this,right:t})},e}(),_e=Object.assign((function(e){var t=ge.get(e);return t||(t=n.print(e),ge.set(e,t)),t}),{reset:function(){ge=new K(H.print||2e3)}});_e.reset();var ke=Array.isArray;function we(e){return Array.isArray(e)&&e.length>0}var Se={kind:n.Kind.FIELD,name:{kind:n.Kind.NAME,value:"__typename"}};function Oe(e,t){return!e||e.selectionSet.selections.every((function(e){return e.kind===n.Kind.FRAGMENT_SPREAD&&Oe(t[e.name.value],t)}))}function xe(e){return Oe(le(e)||de(e),V(he(e)))?null:e}function qe(e){var t=new Map;return function(r){void 0===r&&(r=e);var n=t.get(r);return n||t.set(r,n={variables:new Set,fragmentSpreads:new Set}),n}}function Re(t,r){ce(r);for(var i=qe(""),o=qe(""),a=function(e){for(var t=0,r=void 0;t<e.length&&(r=e[t]);++t)if(!ke(r)){if(r.kind===n.Kind.OPERATION_DEFINITION)return i(r.name&&r.name.value);if(r.kind===n.Kind.FRAGMENT_DEFINITION)return o(r.name.value)}return null},s=0,u=r.definitions.length-1;u>=0;--u)r.definitions[u].kind===n.Kind.OPERATION_DEFINITION&&++s;var c,l,f,h=(c=t,l=new Map,f=new Map,c.forEach((function(e){e&&(e.name?l.set(e.name,e):e.test&&f.set(e.test,e))})),function(e){var t=l.get(e.name.value);return!t&&f.size&&f.forEach((function(r,n){n(e)&&(t=r)})),t}),p=function(e){return we(e)&&e.map(h).some((function(e){return e&&e.remove}))},d=new Map,v=!1,y={enter:function(e){if(p(e.directives))return v=!0,null}},m=n.visit(r,{Field:y,InlineFragment:y,VariableDefinition:{enter:function(){return!1}},Variable:{enter:function(e,t,r,n,i){var o=a(i);o&&o.variables.add(e.name.value)}},FragmentSpread:{enter:function(e,t,r,n,i){if(p(e.directives))return v=!0,null;var o=a(i);o&&o.fragmentSpreads.add(e.name.value)}},FragmentDefinition:{enter:function(e,t,r,n){d.set(JSON.stringify(n),e)},leave:function(e,t,r,i){return e===d.get(JSON.stringify(i))?e:s>0&&e.selectionSet.selections.every((function(e){return e.kind===n.Kind.FIELD&&"__typename"===e.name.value}))?(o(e.name.value).removed=!0,v=!0,null):void 0}},Directive:{leave:function(e){if(h(e))return v=!0,null}}});if(!v)return r;var g=function(e){return e.transitiveVars||(e.transitiveVars=new Set(e.variables),e.removed||e.fragmentSpreads.forEach((function(t){g(o(t)).transitiveVars.forEach((function(t){e.transitiveVars.add(t)}))}))),e},b=new Set;m.definitions.forEach((function(e){e.kind===n.Kind.OPERATION_DEFINITION?g(i(e.name&&e.name.value)).fragmentSpreads.forEach((function(e){b.add(e)})):e.kind!==n.Kind.FRAGMENT_DEFINITION||0!==s||o(e.name.value).removed||b.add(e.name.value)})),b.forEach((function(e){g(o(e)).fragmentSpreads.forEach((function(e){b.add(e)}))}));var _={enter:function(e){if(t=e.name.value,!b.has(t)||o(t).removed)return null;var t}};return xe(n.visit(m,{FragmentSpread:_,FragmentDefinition:_,OperationDefinition:{leave:function(t){if(t.variableDefinitions){var r=g(i(t.name&&t.name.value)).transitiveVars;if(r.size<t.variableDefinitions.length)return e.__assign(e.__assign({},t),{variableDefinitions:t.variableDefinitions.filter((function(e){return r.has(e.variable.name.value)}))})}}}}))}var Ee=Object.assign((function(t){return n.visit(t,{SelectionSet:{enter:function(t,r,i){if(!i||i.kind!==n.Kind.OPERATION_DEFINITION){var o=t.selections;if(o)if(!o.some((function(e){return ue(e)&&("__typename"===e.name.value||0===e.name.value.lastIndexOf("__",0))}))){var a=i;if(!(ue(a)&&a.directives&&a.directives.some((function(e){return"export"===e.name.value}))))return e.__assign(e.__assign({},t),{selections:e.__spreadArray(e.__spreadArray([],o,!0),[Se],!1)})}}}}})}),{added:function(e){return e===Se}});function Pe(t){return"query"===ve(t).operation?t:n.visit(t,{OperationDefinition:{enter:function(t){return e.__assign(e.__assign({},t),{operation:"query"})}}})}function Te(e){return ce(e),Re([{test:function(e){return"client"===e.name.value},remove:!0}],e)}function Ce(t){return ce(t),n.visit(t,{FragmentSpread:function(t){var r;if(!(null===(r=t.directives)||void 0===r?void 0:r.some((function(e){return"unmask"===e.name.value}))))return e.__assign(e.__assign({},t),{directives:e.__spreadArray(e.__spreadArray([],t.directives||[],!0),[{kind:n.Kind.DIRECTIVE,name:{kind:n.Kind.NAME,value:"nonreactive"}}],!1)})}})}var De=Object.prototype.hasOwnProperty;function Fe(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Me(e)}function Me(e){var t=e[0]||{},r=e.length;if(r>1)for(var n=new Ie,i=1;i<r;++i)t=n.merge(t,e[i]);return t}var Qe=function(e,t,r){return this.merge(e[r],t[r])},Ie=function(){function t(e){void 0===e&&(e=Qe),this.reconciler=e,this.isObject=A,this.pastCopies=new Set}return t.prototype.merge=function(t,r){for(var n=this,i=[],o=2;o<arguments.length;o++)i[o-2]=arguments[o];return A(r)&&A(t)?(Object.keys(r).forEach((function(o){if(De.call(t,o)){var a=t[o];if(r[o]!==a){var s=n.reconciler.apply(n,e.__spreadArray([t,r,o],i,!1));s!==a&&((t=n.shallowCopyForMerge(t))[o]=s)}}else(t=n.shallowCopyForMerge(t))[o]=r[o]})),t):r},t.prototype.shallowCopyForMerge=function(t){return A(t)&&(this.pastCopies.has(t)||(t=Array.isArray(t)?t.slice(0):e.__assign({__proto__:Object.getPrototypeOf(t)},t),this.pastCopies.add(t))),t},t}();function Ne(e){var t=Promise.resolve(e);return t.status="fulfilled",t.value=e,t}function je(e){if(function(e){return"status"in e}(e))return e;var t=e;return t.status="pending",t.then((function(e){if("pending"===t.status){var r=t;r.status="fulfilled",r.value=e}}),(function(e){if("pending"===t.status){var r=t;r.status="rejected",r.reason=e}})),e}var Ae=Object.prototype.toString;function Le(e){return Ve(e)}function Ve(e,t){switch(Ae.call(e)){case"[object Array]":if((t=t||new Map).has(e))return t.get(e);var r=e.slice(0);return t.set(e,r),r.forEach((function(e,n){r[n]=Ve(e,t)})),r;case"[object Object]":if((t=t||new Map).has(e))return t.get(e);var n=Object.create(Object.getPrototypeOf(e));return t.set(e,n),Object.keys(e).forEach((function(r){n[r]=Ve(e[r],t)})),n;default:return e}}function We(e,t,r){var n=[];e.forEach((function(e){return e[t]&&n.push(e)})),n.forEach((function(e){return e[t](r)}))}function ze(e,t,r){return new a.Observable((function(n){var i={then:function(e){return new Promise((function(t){return t(e())}))}};function o(e,t){return function(r){if(e){var o=function(){return n.closed?0:e(r)};i=i.then(o,o).then((function(e){return n.next(e)}),(function(e){return n.error(e)}))}else n[t](r)}}var a={next:o(t,"next"),error:o(r,"error"),complete:function(){i.then((function(){return n.complete()}))}},s=e.subscribe(a);return function(){return s.unsubscribe()}}))}function Be(e){function t(t){Object.defineProperty(e,t,{value:a.Observable})}return M&&Symbol.species&&t(Symbol.species),t("@@species"),e}function Ke(e){return e&&"function"==typeof e.then}var Ue=function(t){function r(e){var r=t.call(this,(function(e){return r.addObserver(e),function(){return r.removeObserver(e)}}))||this;return r.observers=new Set,r.promise=new Promise((function(e,t){r.resolve=e,r.reject=t})),r.handlers={next:function(e){null!==r.sub&&(r.latest=["next",e],r.notify("next",e),We(r.observers,"next",e))},error:function(e){var t=r.sub;null!==t&&(t&&setTimeout((function(){return t.unsubscribe()})),r.sub=null,r.latest=["error",e],r.reject(e),r.notify("error",e),We(r.observers,"error",e))},complete:function(){var e=r,t=e.sub,n=e.sources;if(null!==t){var i=(void 0===n?[]:n).shift();i?Ke(i)?i.then((function(e){return r.sub=e.subscribe(r.handlers)}),r.handlers.error):r.sub=i.subscribe(r.handlers):(t&&setTimeout((function(){return t.unsubscribe()})),r.sub=null,r.latest&&"next"===r.latest[0]?r.resolve(r.latest[1]):r.resolve(),r.notify("complete"),We(r.observers,"complete"))}}},r.nextResultListeners=new Set,r.cancel=function(e){r.reject(e),r.sources=[],r.handlers.error(e)},r.promise.catch((function(e){})),"function"==typeof e&&(e=[new a.Observable(e)]),Ke(e)?e.then((function(e){return r.start(e)}),r.handlers.error):r.start(e),r}return e.__extends(r,t),r.prototype.start=function(e){void 0===this.sub&&(this.sources=Array.from(e),this.handlers.complete())},r.prototype.deliverLastMessage=function(e){if(this.latest){var t=this.latest[0],r=e[t];r&&r.call(e,this.latest[1]),null===this.sub&&"next"===t&&e.complete&&e.complete()}},r.prototype.addObserver=function(e){this.observers.has(e)||(this.deliverLastMessage(e),this.observers.add(e))},r.prototype.removeObserver=function(e){this.observers.delete(e)&&this.observers.size<1&&this.handlers.complete()},r.prototype.notify=function(e,t){var r=this.nextResultListeners;r.size&&(this.nextResultListeners=new Set,r.forEach((function(r){return r(e,t)})))},r.prototype.beforeNext=function(e){var t=!1;this.nextResultListeners.add((function(r,n){t||(t=!0,e(r,n))}))},r}(a.Observable);function Je(e){return"incremental"in e}function He(e){return A(e)&&"payload"in e}function Ge(e,t){var r=e,n=new Ie;return Je(t)&&we(t.incremental)&&t.incremental.forEach((function(e){for(var t=e.data,i=e.path,o=i.length-1;o>=0;--o){var a=i[o],s=!isNaN(+a)?[]:{};s[a]=t,t=s}r=n.merge(r,t)})),r}function Ye(e){return we(Xe(e))}function Xe(e){var t=we(e.errors)?e.errors.slice(0):[];return Je(e)&&we(e.incremental)&&e.incremental.forEach((function(e){e.errors&&t.push.apply(t,e.errors)})),t}function $e(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=Object.create(null);return e.forEach((function(e){e&&Object.keys(e).forEach((function(t){var n=e[t];void 0!==n&&(r[t]=n)}))})),r}function Ze(t,r){return $e(t,r,r.variables&&{variables:$e(e.__assign(e.__assign({},t&&t.variables),r.variables))})}function et(e){return new a.Observable((function(t){t.error(e)}))}Be(Ue);var tt=function(e,t,r){var n=new Error(r);throw n.name="ServerError",n.response=e,n.statusCode=e.status,n.result=t,n};function rt(e,t){return t?t(e):a.Observable.of()}function nt(e){return"function"==typeof e?new ot(e):e}function it(e){return e.request.length<=1}var ot=function(){function t(e){e&&(this.request=e)}return t.empty=function(){return new t((function(){return a.Observable.of()}))},t.from=function(e){return 0===e.length?t.empty():e.map(nt).reduce((function(e,t){return e.concat(t)}))},t.split=function(e,r,n){var i,o=nt(r),s=nt(n||new t(rt));return i=it(o)&&it(s)?new t((function(t){return e(t)?o.request(t)||a.Observable.of():s.request(t)||a.Observable.of()})):new t((function(t,r){return e(t)?o.request(t,r)||a.Observable.of():s.request(t,r)||a.Observable.of()})),Object.assign(i,{left:o,right:s})},t.execute=function(t,r){return t.request(function(t,r){var n=e.__assign({},t);return Object.defineProperty(r,"setContext",{enumerable:!1,value:function(t){n="function"==typeof t?e.__assign(e.__assign({},n),t(n)):e.__assign(e.__assign({},n),t)}}),Object.defineProperty(r,"getContext",{enumerable:!1,value:function(){return e.__assign({},n)}}),r}(r.context,function(e){var t={variables:e.variables||{},extensions:e.extensions||{},operationName:e.operationName,query:e.query};return t.operationName||(t.operationName="string"!=typeof t.query?fe(t.query)||void 0:""),t}(function(e){for(var t=["query","operationName","variables","extensions","context"],r=0,n=Object.keys(e);r<n.length;r++){var i=n[r];if(t.indexOf(i)<0)throw w(46,i)}return e}(r))))||a.Observable.of()},t.concat=function(e,r){var n=nt(e);if(it(n))return n;var i,o=nt(r);return i=it(o)?new t((function(e){return n.request(e,(function(e){return o.request(e)||a.Observable.of()}))||a.Observable.of()})):new t((function(e,t){return n.request(e,(function(e){return o.request(e,t)||a.Observable.of()}))||a.Observable.of()})),Object.assign(i,{left:n,right:o})},t.prototype.split=function(e,r,n){return this.concat(t.split(e,r,n||new t(rt)))},t.prototype.concat=function(e){return t.concat(this,e)},t.prototype.request=function(e,t){throw w(39)},t.prototype.onError=function(e,t){if(t&&t.error)return t.error(e),!1;throw e},t.prototype.setOnError=function(e){return this.onError=e,this},t}(),at=ot.empty,st=ot.from,ut=ot.split,ct=ot.concat,lt=ot.execute;function ft(e){var t=null,r=null,n=!1,i=[],o=[];function a(e){if(!r){if(o.length){var t=o.shift();if(Array.isArray(t)&&t[0])return t[0]({value:e,done:!1})}i.push(e)}}function s(e){r=e,o.slice().forEach((function(t){t[1](e)})),!t||t()}function u(){n=!0,o.slice().forEach((function(e){e[0]({value:void 0,done:!0})})),!t||t()}t=function(){t=null,e.removeListener("data",a),e.removeListener("error",s),e.removeListener("end",u),e.removeListener("finish",u),e.removeListener("close",u)},e.on("data",a),e.on("error",s),e.on("end",u),e.on("finish",u),e.on("close",u);var c={next:function(){return new Promise((function(e,t){return r?t(r):i.length?e({value:i.shift(),done:!1}):n?e({value:void 0,done:!0}):void o.push([e,t])}))}};return Q&&(c[Symbol.asyncIterator]=function(){return this}),c}function ht(e){var t={next:function(){return e.read()}};return Q&&(t[Symbol.asyncIterator]=function(){return this}),t}function pt(e){var t,r,n=e;if(e.body&&(n=e.body),function(e){return!(!Q||!e[Symbol.asyncIterator])}(n))return r=n[Symbol.asyncIterator](),(t={next:function(){return r.next()}})[Symbol.asyncIterator]=function(){return this},t;if(function(e){return!!e.getReader}(n))return ht(n.getReader());if(function(e){return!!e.stream}(n))return ht(n.stream().getReader());if(function(e){return!!e.arrayBuffer}(n))return function(e){var t=!1,r={next:function(){return t?Promise.resolve({value:void 0,done:!0}):(t=!0,new Promise((function(t,r){e.then((function(e){t({value:e,done:!1})})).catch(r)})))}};return Q&&(r[Symbol.asyncIterator]=function(){return this}),r}(n.arrayBuffer());if(function(e){return!!e.pipe}(n))return ft(n);throw new Error("Unknown body type for responseIterator. Please pass a streamable response.")}var dt=Symbol();function vt(e){return e.hasOwnProperty("graphQLErrors")}var yt=function(t){function r(n){var i,o,a=n.graphQLErrors,s=n.protocolErrors,u=n.clientErrors,c=n.networkError,l=n.errorMessage,f=n.extraInfo,h=t.call(this,l)||this;return h.name="ApolloError",h.graphQLErrors=a||[],h.protocolErrors=s||[],h.clientErrors=u||[],h.networkError=c||null,h.message=l||(i=h,o=e.__spreadArray(e.__spreadArray(e.__spreadArray([],i.graphQLErrors,!0),i.clientErrors,!0),i.protocolErrors,!0),i.networkError&&o.push(i.networkError),o.map((function(e){return A(e)&&e.message||"Error message not found."})).join("\n")),h.extraInfo=f,h.cause=e.__spreadArray(e.__spreadArray(e.__spreadArray([c],a||[],!0),s||[],!0),u||[],!0).find((function(e){return!!e}))||null,h.__proto__=r.prototype,h}return e.__extends(r,t),r}(Error),mt=Object.prototype.hasOwnProperty;function gt(e){var t={};return e.split("\n").forEach((function(e){var r=e.indexOf(":");if(r>-1){var n=e.slice(0,r).trim().toLowerCase(),i=e.slice(r+1).trim();t[n]=i}})),t}function bt(e,t){if(e.status>=300){tt(e,function(){try{return JSON.parse(t)}catch(e){return t}}(),"Response not successful: Received status code ".concat(e.status))}try{return JSON.parse(t)}catch(n){var r=n;throw r.name="ServerParseError",r.response=e,r.statusCode=e.status,r.bodyText=t,r}}function _t(e){return function(t){return t.text().then((function(e){return bt(t,e)})).then((function(r){return Array.isArray(r)||mt.call(r,"data")||mt.call(r,"errors")||tt(t,r,"Server response was missing for query '".concat(Array.isArray(e)?e.map((function(e){return e.operationName})):e.operationName,"'.")),r}))}}var kt=function(e,t){var r;try{r=JSON.stringify(e)}catch(e){var n=w(42,t,e.message);throw n.parseError=e,n}return r},wt={http:{includeQuery:!0,includeExtensions:!1,preserveHeaderCase:!1},headers:{accept:"*/*","content-type":"application/json"},options:{method:"POST"}},St=function(e,t){return t(e)};function Ot(t,r){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];var o={},a={};n.forEach((function(t){o=e.__assign(e.__assign(e.__assign({},o),t.options),{headers:e.__assign(e.__assign({},o.headers),t.headers)}),t.credentials&&(o.credentials=t.credentials),a=e.__assign(e.__assign({},a),t.http)})),o.headers&&(o.headers=function(e,t){if(!t){var r={};return Object.keys(Object(e)).forEach((function(t){r[t.toLowerCase()]=e[t]})),r}var n={};Object.keys(Object(e)).forEach((function(t){n[t.toLowerCase()]={originalName:t,value:e[t]}}));var i={};return Object.keys(n).forEach((function(e){i[n[e].originalName]=n[e].value})),i}(o.headers,a.preserveHeaderCase));var s=t.operationName,u=t.extensions,c=t.variables,l=t.query,f={operationName:s,variables:c};return a.includeExtensions&&(f.extensions=u),a.includeQuery&&(f.query=r(l,_e)),{options:o,body:f}}var xt=function(e){if(!e&&"undefined"==typeof fetch)throw w(40)},qt=function(e,t){var r=e.getContext().uri;return r||("function"==typeof t?t(e):t||"/graphql")};function Rt(e,t){var r=[],n=function(e,t){r.push("".concat(e,"=").concat(encodeURIComponent(t)))};if("query"in t&&n("query",t.query),t.operationName&&n("operationName",t.operationName),t.variables){var i=void 0;try{i=kt(t.variables,"Variables map")}catch(e){return{parseError:e}}n("variables",i)}if(t.extensions){var o=void 0;try{o=kt(t.extensions,"Extensions map")}catch(e){return{parseError:e}}n("extensions",o)}var a="",s=e,u=e.indexOf("#");-1!==u&&(a=e.substr(u),s=e.substr(0,u));var c=-1===s.indexOf("?")?"?":"&";return{newURI:s+c+r.join("&")+a}}var Et=v((function(){return fetch})),Pt=function(t){void 0===t&&(t={});var r=t.uri,i=void 0===r?"/graphql":r,o=t.fetch,s=t.print,u=void 0===s?St:s,c=t.includeExtensions,l=t.preserveHeaderCase,f=t.useGETForQueries,h=t.includeUnusedVariables,p=void 0!==h&&h,d=e.__rest(t,["uri","fetch","print","includeExtensions","preserveHeaderCase","useGETForQueries","includeUnusedVariables"]);var y={http:{includeExtensions:c,preserveHeaderCase:l},options:d.fetchOptions,credentials:d.credentials,headers:d.headers};return new ot((function(t){var r=qt(t,i),s=t.getContext(),c={};if(s.clientAwareness){var l=s.clientAwareness,h=l.name,d=l.version;h&&(c["apollographql-client-name"]=h),d&&(c["apollographql-client-version"]=d)}var m=e.__assign(e.__assign({},c),s.headers),g={http:s.http,options:s.fetchOptions,credentials:s.credentials,headers:m};if(E(["client"],t.query)){var b=Te(t.query);if(!b)return et(new Error("HttpLink: Trying to send a client-only query to the server. To send to the server, ensure a non-client field is added to the query or set the `transformOptions.removeClientFields` option to `true`."));t.query=b}var _,k,w,S,O,x=Ot(t,u,wt,y,g),q=x.options,R=x.body;R.variables&&!p&&(R.variables=(_=R.variables,k=t.query,w=e.__assign({},_),S=new Set(Object.keys(_)),n.visit(k,{Variable:function(e,t,r){r&&"VariableDefinition"!==r.kind&&S.delete(e.name.value)}}),S.forEach((function(e){delete w[e]})),w)),q.signal||"undefined"==typeof AbortController||(O=new AbortController,q.signal=O.signal);var P,T="OperationDefinition"===(P=ve(t.query)).kind&&"subscription"===P.operation,C=E(["defer"],t.query);if(f&&!t.query.definitions.some((function(e){return"OperationDefinition"===e.kind&&"mutation"===e.operation}))&&(q.method="GET"),C||T){q.headers=q.headers||{};var D="multipart/mixed;";T?D+="boundary=graphql;subscriptionSpec=1.0,application/json":C&&(D+="deferSpec=20220824,application/json"),q.headers.accept=D}if("GET"===q.method){var F=Rt(r,R),M=F.newURI,Q=F.parseError;if(Q)return et(Q);r=M}else try{q.body=kt(R,"Payload")}catch(Q){return et(Q)}return new a.Observable((function(n){var i=o||v((function(){return fetch}))||Et,a=n.next.bind(n);return i(r,q).then((function(r){var n;t.setContext({response:r});var i=null===(n=r.headers)||void 0===n?void 0:n.get("content-type");return null!==i&&/^multipart\/mixed/i.test(i)?function(t,r){return e.__awaiter(this,void 0,void 0,(function(){var n,i,o,a,s,u,c,l,f,h,p,d,v,y,m,g,b,_,k,w,S,O,x,q;return e.__generator(this,(function(R){switch(R.label){case 0:if(void 0===TextDecoder)throw new Error("TextDecoder must be defined in the environment: please import a polyfill.");n=new TextDecoder("utf-8"),i=null===(q=t.headers)||void 0===q?void 0:q.get("content-type"),o="boundary=",a=(null==i?void 0:i.includes(o))?null==i?void 0:i.substring((null==i?void 0:i.indexOf(o))+9).replace(/['"]/g,"").replace(/\;(.*)/gm,"").trim():"-",s="\r\n--".concat(a),u="",c=pt(t),l=!0,R.label=1;case 1:return l?[4,c.next()]:[3,3];case 2:for(f=R.sent(),h=f.value,p=f.done,d="string"==typeof h?h:n.decode(h),v=u.length-s.length+1,l=!p,y=(u+=d).indexOf(s,v);y>-1;){if(m=void 0,O=[u.slice(0,y),u.slice(y+s.length)],u=O[1],g=(m=O[0]).indexOf("\r\n\r\n"),b=gt(m.slice(0,g)),(_=b["content-type"])&&-1===_.toLowerCase().indexOf("application/json"))throw new Error("Unsupported patch content type: application/json is required.");if(k=m.slice(g))if(w=bt(t,k),Object.keys(w).length>1||"data"in w||"incremental"in w||"errors"in w||"payload"in w)if(He(w)){if(S={},"payload"in w){if(1===Object.keys(w).length&&null===w.payload)return[2];S=e.__assign({},w.payload)}"errors"in w&&(S=e.__assign(e.__assign({},S),{extensions:e.__assign(e.__assign({},"extensions"in S?S.extensions:null),(x={},x[dt]=w.errors,x))})),r(S)}else r(w);else if(1===Object.keys(w).length&&"hasNext"in w&&!w.hasNext)return[2];y=u.indexOf(s)}return[3,1];case 3:return[2]}}))}))}(r,a):_t(t)(r).then(a)})).then((function(){O=void 0,n.complete()})).catch((function(e){O=void 0,function(e,t){e.result&&e.result.errors&&e.result.data&&t.next(e.result),t.error(e)}(e,n)})),function(){O&&O.abort()}}))}))},Tt=function(t){function r(e){void 0===e&&(e={});var r=t.call(this,Pt(e).request)||this;return r.options=e,r}return e.__extends(r,t),r}(ot);function Ct(t,r,n,i){var o=r.data,a=e.__rest(r,["data"]),s=n.data,u=e.__rest(n,["data"]);return h(a,u)&&Dt(ve(t).selectionSet,o,s,{fragmentMap:V(he(t)),variables:i})}function Dt(e,t,r,n){if(t===r)return!0;var i=new Set;return e.selections.every((function(e){if(i.has(e))return!0;if(i.add(e),!R(e,n.variables))return!0;if(Ft(e))return!0;if(ue(e)){var o=ae(e),a=t&&t[o],s=r&&r[o],u=e.selectionSet;if(!u)return h(a,s);var c=Array.isArray(a),l=Array.isArray(s);if(c!==l)return!1;if(c&&l){var f=a.length;if(s.length!==f)return!1;for(var p=0;p<f;++p)if(!Dt(u,a[p],s[p],n))return!1;return!0}return Dt(u,a,s,n)}var d=W(e,n.fragmentMap);return d?!!Ft(d)||Dt(d.selectionSet,t,r,n):void 0}))}function Ft(e){return!!e.directives&&e.directives.some(Mt)}function Mt(e){return"nonreactive"===e.name.value}var Qt=D?WeakMap:Map,It=F?WeakSet:Set,Nt=new o.Slot;function jt(e,t,r){return Nt.withValue(!0,(function(){var n=At(e,t,r,!1);return Object.isFrozen(e),n}))}function At(e,t,r,i,o){var a=r.knownChanged,s=function(e,t){if(t.has(e))return t.get(e);var r=Array.isArray(e)?[]:Object.create(null);return t.set(e,r),r}(e,r.mutableTargets);if(Array.isArray(e)){for(var u=0,c=Array.from(e.entries());u<c.length;u++){var l=c[u],f=l[0],h=l[1];if(null!==h){var p=At(h,t,r,i,void 0);a.has(p)&&a.add(s),s[f]=p}else s[f]=null}return a.has(s)?s:e}for(var d=0,v=t.selections;d<v.length;d++){var y=v[d],m=void 0;if(i&&a.add(s),y.kind===n.Kind.FIELD){var g=ae(y),b=y.selectionSet;if(void 0===(m=s[g]||e[g]))continue;if(b&&null!==m){p=At(e[g],b,r,i,void 0);a.has(p)&&(m=p)}s[g]=m}if(y.kind!==n.Kind.INLINE_FRAGMENT||y.typeCondition&&!r.cache.fragmentMatches(y,e.__typename)||(m=At(e,y.selectionSet,r,i,o)),y.kind===n.Kind.FRAGMENT_SPREAD){var _=y.name.value,w=r.fragmentMap[_]||(r.fragmentMap[_]=r.cache.lookupFragment(_));k(w,47,_);var S=T(y);"mask"!==S&&(m=At(e,w.selectionSet,r,"migrate"===S,o))}a.has(m)&&a.add(s)}return"__typename"in e&&!("__typename"in s)&&(s.__typename=e.__typename),Object.keys(s).length!==Object.keys(e).length&&a.add(s),a.has(s)?s:e}function Lt(e,t,r,i){if(!r.fragmentMatches)return e;var o=t.definitions.filter((function(e){return e.kind===n.Kind.FRAGMENT_DEFINITION}));void 0===i&&(k(1===o.length,49,o.length),i=o[0].name.value);var a=o.find((function(e){return e.name.value===i}));return k(!!a,50,i),null==e||h(e,{})?e:jt(e,a.selectionSet,{operationType:"fragment",operationName:a.name.value,fragmentMap:V(he(t)),cache:r,mutableTargets:new Qt,knownChanged:new It})}var Vt=function(){function t(){this.assumeImmutableResults=!1,this.getFragmentDoc=o.wrap(L,{max:H["cache.fragmentQueryDocuments"]||1e3,cache:i.WeakCache})}return t.prototype.lookupFragment=function(e){return null},t.prototype.batch=function(e){var t,r=this,n="string"==typeof e.optimistic?e.optimistic:!1===e.optimistic?null:void 0;return this.performTransaction((function(){return t=e.update(r)}),n),t},t.prototype.recordOptimisticTransaction=function(e,t){this.performTransaction(e,t)},t.prototype.transformDocument=function(e){return e},t.prototype.transformForLink=function(e){return e},t.prototype.identify=function(e){},t.prototype.gc=function(){return[]},t.prototype.modify=function(e){return!1},t.prototype.readQuery=function(t,r){return void 0===r&&(r=!!t.optimistic),this.read(e.__assign(e.__assign({},t),{rootId:t.id||"ROOT_QUERY",optimistic:r}))},t.prototype.watchFragment=function(t){var r,n=this,i=t.fragment,o=t.fragmentName,s=t.from,u=t.optimistic,c=void 0===u||u,l=e.__rest(t,["fragment","fragmentName","from","optimistic"]),f=this.getFragmentDoc(i,o),h=void 0===s||"string"==typeof s?s:this.identify(s),p=!!t[Symbol.for("apollo.dataMasking")],d=e.__assign(e.__assign({},l),{returnPartialData:!0,id:h,query:f,optimistic:c});return new a.Observable((function(a){return n.watch(e.__assign(e.__assign({},d),{immediate:!0,callback:function(s){var u=p?Lt(s.result,i,n,o):s.result;if(!r||!Ct(f,{data:r.result},{data:u},t.variables)){var c={data:u,complete:!!s.complete};s.missing&&(c.missing=Me(s.missing.map((function(e){return e.missing})))),r=e.__assign(e.__assign({},s),{result:u}),a.next(c)}}}))}))},t.prototype.readFragment=function(t,r){return void 0===r&&(r=!!t.optimistic),this.read(e.__assign(e.__assign({},t),{query:this.getFragmentDoc(t.fragment,t.fragmentName),rootId:t.id,optimistic:r}))},t.prototype.writeQuery=function(t){var r=t.id,n=t.data,i=e.__rest(t,["id","data"]);return this.write(Object.assign(i,{dataId:r||"ROOT_QUERY",result:n}))},t.prototype.writeFragment=function(t){var r=t.id,n=t.data,i=t.fragment,o=t.fragmentName,a=e.__rest(t,["id","data","fragment","fragmentName"]);return this.write(Object.assign(a,{query:this.getFragmentDoc(i,o),dataId:r,result:n}))},t.prototype.updateQuery=function(t,r){return this.batch({update:function(n){var i=n.readQuery(t),o=r(i);return null==o?i:(n.writeQuery(e.__assign(e.__assign({},t),{data:o})),o)}})},t.prototype.updateFragment=function(t,r){return this.batch({update:function(n){var i=n.readFragment(t),o=r(i);return null==o?i:(n.writeFragment(e.__assign(e.__assign({},t),{data:o})),o)}})},t}();exports.Cache=void 0,exports.Cache||(exports.Cache={});var Wt=function(t){function r(e,n,i,o){var a,s=t.call(this,e)||this;if(s.message=e,s.path=n,s.query=i,s.variables=o,Array.isArray(s.path)){s.missing=s.message;for(var u=s.path.length-1;u>=0;--u)s.missing=((a={})[s.path[u]]=s.missing,a)}else s.missing=s.path;return s.__proto__=r.prototype,s}return e.__extends(r,t),r}(Error),zt=Object.prototype.hasOwnProperty;function Bt(e){return null==e}function Kt(e,t){var r=e.__typename,n=e.id,i=e._id;if("string"==typeof r&&(t&&(t.keyObject=Bt(n)?Bt(i)?void 0:{_id:i}:{id:n}),Bt(n)&&!Bt(i)&&(n=i),!Bt(n)))return"".concat(r,":").concat("number"==typeof n||"string"==typeof n?n:JSON.stringify(n))}var Ut={dataIdFromObject:Kt,addTypename:!0,resultCaching:!0,canonizeResults:!1};function Jt(e){var t=e.canonizeResults;return void 0===t?Ut.canonizeResults:t}var Ht=/^[_a-z][_0-9a-z]*/i;function Gt(e){var t=e.match(Ht);return t?t[0]:e}function Yt(e,t,r){return!!A(t)&&(ke(t)?t.every((function(t){return Yt(e,t,r)})):e.selections.every((function(e){if(ue(e)&&R(e,r)){var n=ae(e);return zt.call(t,n)&&(!e.selectionSet||Yt(e.selectionSet,t[n],r))}return!0})))}function Xt(e){return A(e)&&!ee(e)&&!ke(e)}function $t(e,t){var r=V(he(e));return{fragmentMap:r,lookupFragment:function(e){var n=r[e];return!n&&t&&(n=t.lookup(e)),n||null}}}var Zt=Object.create(null),er=function(){return Zt},tr=Object.create(null),rr=function(){function t(e,t){var r=this;this.policies=e,this.group=t,this.data=Object.create(null),this.rootIds=Object.create(null),this.refs=Object.create(null),this.getFieldValue=function(e,t){return ee(e)?r.get(e.__ref,t):e&&e[t]},this.canRead=function(e){return ee(e)?r.has(e.__ref):"object"==typeof e},this.toReference=function(e,t){if("string"==typeof e)return Z(e);if(ee(e))return e;var n=r.policies.identify(e)[0];if(n){var i=Z(n);return t&&r.merge(n,e),i}}}return t.prototype.toObject=function(){return e.__assign({},this.data)},t.prototype.has=function(e){return void 0!==this.lookup(e,!0)},t.prototype.get=function(e,t){if(this.group.depend(e,t),zt.call(this.data,e)){var r=this.data[e];if(r&&zt.call(r,t))return r[t]}return"__typename"===t&&zt.call(this.policies.rootTypenamesById,e)?this.policies.rootTypenamesById[e]:this instanceof ar?this.parent.get(e,t):void 0},t.prototype.lookup=function(e,t){return t&&this.group.depend(e,"__exists"),zt.call(this.data,e)?this.data[e]:this instanceof ar?this.parent.lookup(e,t):this.policies.rootTypenamesById[e]?Object.create(null):void 0},t.prototype.merge=function(e,t){var r,n=this;ee(e)&&(e=e.__ref),ee(t)&&(t=t.__ref);var i="string"==typeof e?this.lookup(r=e):e,o="string"==typeof t?this.lookup(r=t):t;if(o){k("string"==typeof r,2);var a=new Ie(ur).merge(i,o);if(this.data[r]=a,a!==i&&(delete this.refs[r],this.group.caching)){var s=Object.create(null);i||(s.__exists=1),Object.keys(o).forEach((function(e){if(!i||i[e]!==a[e]){s[e]=1;var t=Gt(e);t===e||n.policies.hasKeyArgs(a.__typename,t)||(s[t]=1),void 0!==a[e]||n instanceof ar||delete a[e]}})),!s.__typename||i&&i.__typename||this.policies.rootTypenamesById[r]!==a.__typename||delete s.__typename,Object.keys(s).forEach((function(e){return n.group.dirty(r,e)}))}}},t.prototype.modify=function(t,r){var n=this,i=this.lookup(t);if(i){var o=Object.create(null),a=!1,s=!0,u={DELETE:Zt,INVALIDATE:tr,isReference:ee,toReference:this.toReference,canRead:this.canRead,readField:function(e,r){return n.policies.readField("string"==typeof e?{fieldName:e,from:r||Z(t)}:e,{store:n})}};if(Object.keys(i).forEach((function(c){var l=Gt(c),f=i[c];if(void 0!==f){var h="function"==typeof r?r:r[c]||r[l];if(h){var p=h===er?Zt:h(f,e.__assign(e.__assign({},u),{fieldName:l,storeFieldName:c,storage:n.getStorage(t,c)}));if(p===tr)n.group.dirty(t,c);else if(p===Zt&&(p=void 0),p!==f)o[c]=p,a=!0,f=p}void 0!==f&&(s=!1)}})),a)return this.merge(t,o),s&&(this instanceof ar?this.data[t]=void 0:delete this.data[t],this.group.dirty(t,"__exists")),!0}return!1},t.prototype.delete=function(e,t,r){var n,i=this.lookup(e);if(i){var o=this.getFieldValue(i,"__typename"),a=t&&r?this.policies.getStoreFieldName({typename:o,fieldName:t,args:r}):t;return this.modify(e,a?((n={})[a]=er,n):er)}return!1},t.prototype.evict=function(e,t){var r=!1;return e.id&&(zt.call(this.data,e.id)&&(r=this.delete(e.id,e.fieldName,e.args)),this instanceof ar&&this!==t&&(r=this.parent.evict(e,t)||r),(e.fieldName||r)&&this.group.dirty(e.id,e.fieldName||"__exists")),r},t.prototype.clear=function(){this.replace(null)},t.prototype.extract=function(){var e=this,t=this.toObject(),r=[];return this.getRootIdSet().forEach((function(t){zt.call(e.policies.rootTypenamesById,t)||r.push(t)})),r.length&&(t.__META={extraRootIds:r.sort()}),t},t.prototype.replace=function(t){var r=this;if(Object.keys(this.data).forEach((function(e){t&&zt.call(t,e)||r.delete(e)})),t){var n=t.__META,i=e.__rest(t,["__META"]);Object.keys(i).forEach((function(e){r.merge(e,i[e])})),n&&n.extraRootIds.forEach(this.retain,this)}},t.prototype.retain=function(e){return this.rootIds[e]=(this.rootIds[e]||0)+1},t.prototype.release=function(e){if(this.rootIds[e]>0){var t=--this.rootIds[e];return t||delete this.rootIds[e],t}return 0},t.prototype.getRootIdSet=function(e){return void 0===e&&(e=new Set),Object.keys(this.rootIds).forEach(e.add,e),this instanceof ar?this.parent.getRootIdSet(e):Object.keys(this.policies.rootTypenamesById).forEach(e.add,e),e},t.prototype.gc=function(){var e=this,t=this.getRootIdSet(),r=this.toObject();t.forEach((function(n){zt.call(r,n)&&(Object.keys(e.findChildRefIds(n)).forEach(t.add,t),delete r[n])}));var n=Object.keys(r);if(n.length){for(var i=this;i instanceof ar;)i=i.parent;n.forEach((function(e){return i.delete(e)}))}return n},t.prototype.findChildRefIds=function(e){if(!zt.call(this.refs,e)){var t=this.refs[e]=Object.create(null),r=this.data[e];if(!r)return t;var n=new Set([r]);n.forEach((function(e){ee(e)&&(t[e.__ref]=!0),A(e)&&Object.keys(e).forEach((function(t){var r=e[t];A(r)&&n.add(r)}))}))}return this.refs[e]},t.prototype.makeCacheKey=function(){return this.group.keyMaker.lookupArray(arguments)},t}(),nr=function(){function e(e,t){void 0===t&&(t=null),this.caching=e,this.parent=t,this.d=null,this.resetCaching()}return e.prototype.resetCaching=function(){this.d=this.caching?o.dep():null,this.keyMaker=new s.Trie(D)},e.prototype.depend=function(e,t){if(this.d){this.d(ir(e,t));var r=Gt(t);r!==t&&this.d(ir(e,r)),this.parent&&this.parent.depend(e,t)}},e.prototype.dirty=function(e,t){this.d&&this.d.dirty(ir(e,t),"__exists"===t?"forget":"setDirty")},e}();function ir(e,t){return t+"#"+e}function or(e,t){cr(e)&&e.group.depend(t,"__exists")}!function(t){var r=function(t){function r(e){var r=e.policies,n=e.resultCaching,i=void 0===n||n,o=e.seed,a=t.call(this,r,new nr(i))||this;return a.stump=new sr(a),a.storageTrie=new s.Trie(D),o&&a.replace(o),a}return e.__extends(r,t),r.prototype.addLayer=function(e,t){return this.stump.addLayer(e,t)},r.prototype.removeLayer=function(){return this},r.prototype.getStorage=function(){return this.storageTrie.lookupArray(arguments)},r}(t);t.Root=r}(rr||(rr={}));var ar=function(t){function n(e,r,n,i){var o=t.call(this,r.policies,i)||this;return o.id=e,o.parent=r,o.replay=n,o.group=i,n(o),o}return e.__extends(n,t),n.prototype.addLayer=function(e,t){return new n(e,this,t,this.group)},n.prototype.removeLayer=function(e){var t=this,n=this.parent.removeLayer(e);return e===this.id?(this.group.caching&&Object.keys(this.data).forEach((function(e){var i=t.data[e],o=n.lookup(e);o?i?i!==o&&Object.keys(i).forEach((function(n){r.equal(i[n],o[n])||t.group.dirty(e,n)})):(t.group.dirty(e,"__exists"),Object.keys(o).forEach((function(r){t.group.dirty(e,r)}))):t.delete(e)})),n):n===this.parent?this:n.addLayer(this.id,this.replay)},n.prototype.toObject=function(){return e.__assign(e.__assign({},this.parent.toObject()),this.data)},n.prototype.findChildRefIds=function(r){var n=this.parent.findChildRefIds(r);return zt.call(this.data,r)?e.__assign(e.__assign({},n),t.prototype.findChildRefIds.call(this,r)):n},n.prototype.getStorage=function(){for(var e=this.parent;e.parent;)e=e.parent;return e.getStorage.apply(e,arguments)},n}(rr),sr=function(t){function r(e){return t.call(this,"EntityStore.Stump",e,(function(){}),new nr(e.group.caching,e.group))||this}return e.__extends(r,t),r.prototype.removeLayer=function(){return this},r.prototype.merge=function(e,t){return this.parent.merge(e,t)},r}(ar);function ur(e,t,n){var i=e[n],o=t[n];return r.equal(i,o)?i:o}function cr(e){return!!(e instanceof rr&&e.group.caching)}var lr=function(){function t(){this.known=new(F?WeakSet:Set),this.pool=new s.Trie(D),this.passes=new WeakMap,this.keysByJSON=new Map,this.empty=this.admit({})}return t.prototype.isKnown=function(e){return A(e)&&this.known.has(e)},t.prototype.pass=function(t){if(A(t)){var r=function(t){return A(t)?ke(t)?t.slice(0):e.__assign({__proto__:Object.getPrototypeOf(t)},t):t}(t);return this.passes.set(r,t),r}return t},t.prototype.admit=function(e){var t=this;if(A(e)){var r=this.passes.get(e);if(r)return r;switch(Object.getPrototypeOf(e)){case Array.prototype:if(this.known.has(e))return e;var n=e.map(this.admit,this);return(s=this.pool.lookupArray(n)).array||this.known.add(s.array=n),s.array;case null:case Object.prototype:if(this.known.has(e))return e;var i=Object.getPrototypeOf(e),o=[i],a=this.sortedKeys(e);o.push(a.json);var s,u=o.length;if(a.sorted.forEach((function(r){o.push(t.admit(e[r]))})),!(s=this.pool.lookupArray(o)).object){var c=s.object=Object.create(i);this.known.add(c),a.sorted.forEach((function(e,t){c[e]=o[u+t]}))}return s.object}}return e},t.prototype.sortedKeys=function(e){var t=Object.keys(e),r=this.pool.lookupArray(t);if(!r.keys){t.sort();var n=JSON.stringify(t);(r.keys=this.keysByJSON.get(n))||this.keysByJSON.set(n,r.keys={sorted:t,json:n})}return r.keys},t}();function fr(e){return[e.selectionSet,e.objectOrReference,e.context,e.context.canonizeResults]}var hr=function(){function t(t){var r=this;this.knownResults=new(D?WeakMap:Map),this.config=$e(t,{addTypename:!1!==t.addTypename,canonizeResults:Jt(t)}),this.canon=t.canon||new lr,this.executeSelectionSet=o.wrap((function(t){var n,i=t.context.canonizeResults,o=fr(t);o[3]=!i;var a=(n=r.executeSelectionSet).peek.apply(n,o);return a?i?e.__assign(e.__assign({},a),{result:r.canon.admit(a.result)}):a:(or(t.context.store,t.enclosingRef.__ref),r.execSelectionSetImpl(t))}),{max:this.config.resultCacheMaxSize||H["inMemoryCache.executeSelectionSet"]||5e4,keyArgs:fr,makeCacheKey:function(e,t,r,n){if(cr(r.store))return r.store.makeCacheKey(e,ee(t)?t.__ref:t,r.varString,n)}}),this.executeSubSelectedArray=o.wrap((function(e){return or(e.context.store,e.enclosingRef.__ref),r.execSubSelectedArrayImpl(e)}),{max:this.config.resultCacheMaxSize||H["inMemoryCache.executeSubSelectedArray"]||1e4,makeCacheKey:function(e){var t=e.field,r=e.array,n=e.context;if(cr(n.store))return n.store.makeCacheKey(t,r,n.varString)}})}return t.prototype.resetCanon=function(){this.canon=new lr},t.prototype.diffQueryAgainstStore=function(t){var r=t.store,n=t.query,i=t.rootId,o=void 0===i?"ROOT_QUERY":i,a=t.variables,s=t.returnPartialData,u=void 0===s||s,c=t.canonizeResults,l=void 0===c?this.config.canonizeResults:c,f=this.config.cache.policies;a=e.__assign(e.__assign({},ye(pe(n))),a);var h,p=Z(o),d=this.executeSelectionSet({selectionSet:ve(n).selectionSet,objectOrReference:p,enclosingRef:p,context:e.__assign({store:r,query:n,policies:f,variables:a,varString:Y(a),canonizeResults:l},$t(n,this.config.fragments))});if(d.missing&&(h=[new Wt(pr(d.missing),d.missing,n,a)],!u))throw h[0];return{result:d.result,complete:!h,missing:h}},t.prototype.isFresh=function(e,t,r,n){if(cr(n.store)&&this.knownResults.get(e)===r){var i=this.executeSelectionSet.peek(r,t,n,this.canon.isKnown(e));if(i&&e===i.result)return!0}return!1},t.prototype.execSelectionSetImpl=function(e){var t=this,r=e.selectionSet,i=e.objectOrReference,o=e.enclosingRef,a=e.context;if(ee(i)&&!a.policies.rootTypenamesById[i.__ref]&&!a.store.has(i.__ref))return{result:this.canon.empty,missing:"Dangling reference to missing ".concat(i.__ref," object")};var s,u=a.variables,c=a.policies,l=a.store.getFieldValue(i,"__typename"),f=[],h=new Ie;function p(e,t){var r;return e.missing&&(s=h.merge(s,((r={})[t]=e.missing,r))),e.result}this.config.addTypename&&"string"==typeof l&&!c.rootIdsByTypename[l]&&f.push({__typename:l});var d=new Set(r.selections);d.forEach((function(e){var r,v;if(R(e,u))if(ue(e)){var y=c.readField({fieldName:e.name.value,field:e,variables:a.variables,from:i},a),m=ae(e);void 0===y?Ee.added(e)||(s=h.merge(s,((r={})[m]="Can't find field '".concat(e.name.value,"' on ").concat(ee(i)?i.__ref+" object":"object "+JSON.stringify(i,null,2)),r))):ke(y)?y.length>0&&(y=p(t.executeSubSelectedArray({field:e,array:y,enclosingRef:o,context:a}),m)):e.selectionSet?null!=y&&(y=p(t.executeSelectionSet({selectionSet:e.selectionSet,objectOrReference:y,enclosingRef:ee(y)?y:o,context:a}),m)):a.canonizeResults&&(y=t.canon.pass(y)),void 0!==y&&f.push(((v={})[m]=y,v))}else{var g=W(e,a.lookupFragment);if(!g&&e.kind===n.Kind.FRAGMENT_SPREAD)throw w(10,e.name.value);g&&c.fragmentMatches(g,l)&&g.selectionSet.selections.forEach(d.add,d)}}));var v={result:Me(f),missing:s},y=a.canonizeResults?this.canon.admit(v):v;return y.result&&this.knownResults.set(y.result,r),y},t.prototype.execSubSelectedArrayImpl=function(e){var t,r=this,n=e.field,i=e.array,o=e.enclosingRef,a=e.context,s=new Ie;function u(e,r){var n;return e.missing&&(t=s.merge(t,((n={})[r]=e.missing,n))),e.result}return n.selectionSet&&(i=i.filter(a.store.canRead)),i=i.map((function(e,t){return null===e?null:ke(e)?u(r.executeSubSelectedArray({field:n,array:e,enclosingRef:o,context:a}),t):n.selectionSet?u(r.executeSelectionSet({selectionSet:n.selectionSet,objectOrReference:e,enclosingRef:ee(e)?e:o,context:a}),t):e})),{result:a.canonizeResults?this.canon.admit(i):i,missing:t}},t}();function pr(e){try{JSON.stringify(e,(function(e,t){if("string"==typeof t)throw t;return t}))}catch(e){return e}}var dr=new o.Slot,vr=new WeakMap;function yr(e){var t=vr.get(e);return t||vr.set(e,t={vars:new Set,dep:o.dep()}),t}function mr(e){yr(e).vars.forEach((function(t){return t.forgetCache(e)}))}function gr(e){var t=new Set,r=new Set,n=function(o){if(arguments.length>0){if(e!==o){e=o,t.forEach((function(e){yr(e).dep.dirty(n),function(e){e.broadcastWatches&&e.broadcastWatches()}(e)}));var a=Array.from(r);r.clear(),a.forEach((function(t){return t(e)}))}}else{var s=dr.getValue();s&&(i(s),yr(s).dep(n))}return e};n.onNextChange=function(e){return r.add(e),function(){r.delete(e)}};var i=n.attachCache=function(e){return t.add(e),yr(e).vars.add(n),n};return n.forgetCache=function(e){return t.delete(e)},n}var br=Object.create(null);function _r(e){var t=JSON.stringify(e);return br[t]||(br[t]=Object.create(null))}function kr(e){var t=_r(e);return t.keyFieldsFn||(t.keyFieldsFn=function(t,r){var n=function(e,t){return r.readField(t,e)},i=r.keyObject=Sr(e,(function(e){var i=qr(r.storeObject,e,n);return void 0===i&&t!==r.storeObject&&zt.call(t,e[0])&&(i=qr(t,e,xr)),k(void 0!==i,5,e.join("."),t),i}));return"".concat(r.typename,":").concat(JSON.stringify(i))})}function wr(e){var t=_r(e);return t.keyArgsFn||(t.keyArgsFn=function(t,r){var n=r.field,i=r.variables,o=r.fieldName,a=Sr(e,(function(e){var r=e[0],o=r.charAt(0);if("@"!==o)if("$"!==o){if(t)return qr(t,e)}else{var a=r.slice(1);if(i&&zt.call(i,a)){var s=e.slice(0);return s[0]=a,qr(i,s)}}else if(n&&we(n.directives)){var u=r.slice(1),c=n.directives.find((function(e){return e.name.value===u})),l=c&&oe(c,i);return l&&qr(l,e.slice(1))}})),s=JSON.stringify(a);return(t||"{}"!==s)&&(o+=":"+s),o})}function Sr(e,t){var r=new Ie;return Or(e).reduce((function(e,n){var i,o=t(n);if(void 0!==o){for(var a=n.length-1;a>=0;--a)(i={})[n[a]]=o,o=i;e=r.merge(e,o)}return e}),Object.create(null))}function Or(e){var t=_r(e);if(!t.paths){var r=t.paths=[],n=[];e.forEach((function(t,i){ke(t)?(Or(t).forEach((function(e){return r.push(n.concat(e))})),n.length=0):(n.push(t),ke(e[i+1])||(r.push(n.slice(0)),n.length=0))}))}return t.paths}function xr(e,t){return e[t]}function qr(e,t,r){return r=r||xr,Rr(t.reduce((function e(t,n){return ke(t)?t.map((function(t){return e(t,n)})):t&&r(t,n)}),e))}function Rr(e){return A(e)?ke(e)?e.map(Rr):Sr(Object.keys(e).sort(),(function(t){return qr(e,t)})):e}function Er(e){return void 0!==e.args?e.args:e.field?oe(e.field,e.variables):null}var Pr=function(){},Tr=function(e,t){return t.fieldName},Cr=function(e,t,r){return(0,r.mergeObjects)(e,t)},Dr=function(e,t){return t},Fr=function(){function t(t){this.config=t,this.typePolicies=Object.create(null),this.toBeAdded=Object.create(null),this.supertypeMap=new Map,this.fuzzySubtypes=new Map,this.rootIdsByTypename=Object.create(null),this.rootTypenamesById=Object.create(null),this.usingPossibleTypes=!1,this.config=e.__assign({dataIdFromObject:Kt},t),this.cache=this.config.cache,this.setRootTypename("Query"),this.setRootTypename("Mutation"),this.setRootTypename("Subscription"),t.possibleTypes&&this.addPossibleTypes(t.possibleTypes),t.typePolicies&&this.addTypePolicies(t.typePolicies)}return t.prototype.identify=function(t,r){var n,i=this,o=r&&(r.typename||(null===(n=r.storeObject)||void 0===n?void 0:n.__typename))||t.__typename;if(o===this.rootTypenamesById.ROOT_QUERY)return["ROOT_QUERY"];var a,s=r&&r.storeObject||t,u=e.__assign(e.__assign({},r),{typename:o,storeObject:s,readField:r&&r.readField||function(){var e=Qr(arguments,s);return i.readField(e,{store:i.cache.data,variables:e.variables})}}),c=o&&this.getTypePolicy(o),l=c&&c.keyFn||this.config.dataIdFromObject;return Nt.withValue(!0,(function(){for(;l;){var r=l(e.__assign(e.__assign({},t),s),u);if(!ke(r)){a=r;break}l=kr(r)}})),a=a?String(a):void 0,u.keyObject?[a,u.keyObject]:[a]},t.prototype.addTypePolicies=function(t){var r=this;Object.keys(t).forEach((function(n){var i=t[n],o=i.queryType,a=i.mutationType,s=i.subscriptionType,u=e.__rest(i,["queryType","mutationType","subscriptionType"]);o&&r.setRootTypename("Query",n),a&&r.setRootTypename("Mutation",n),s&&r.setRootTypename("Subscription",n),zt.call(r.toBeAdded,n)?r.toBeAdded[n].push(u):r.toBeAdded[n]=[u]}))},t.prototype.updateTypePolicy=function(e,t){var r=this,n=this.getTypePolicy(e),i=t.keyFields,o=t.fields;function a(e,t){e.merge="function"==typeof t?t:!0===t?Cr:!1===t?Dr:e.merge}a(n,t.merge),n.keyFn=!1===i?Pr:ke(i)?kr(i):"function"==typeof i?i:n.keyFn,o&&Object.keys(o).forEach((function(t){var n=r.getFieldPolicy(e,t,!0),i=o[t];if("function"==typeof i)n.read=i;else{var s=i.keyArgs,u=i.read,c=i.merge;n.keyFn=!1===s?Tr:ke(s)?wr(s):"function"==typeof s?s:n.keyFn,"function"==typeof u&&(n.read=u),a(n,c)}n.read&&n.merge&&(n.keyFn=n.keyFn||Tr)}))},t.prototype.setRootTypename=function(e,t){void 0===t&&(t=e);var r="ROOT_"+e.toUpperCase(),n=this.rootTypenamesById[r];t!==n&&(k(!n||n===e,6,e),n&&delete this.rootIdsByTypename[n],this.rootIdsByTypename[t]=r,this.rootTypenamesById[r]=t)},t.prototype.addPossibleTypes=function(e){var t=this;this.usingPossibleTypes=!0,Object.keys(e).forEach((function(r){t.getSupertypeSet(r,!0),e[r].forEach((function(e){t.getSupertypeSet(e,!0).add(r);var n=e.match(Ht);n&&n[0]===e||t.fuzzySubtypes.set(e,new RegExp(e))}))}))},t.prototype.getTypePolicy=function(t){var r=this;if(!zt.call(this.typePolicies,t)){var n=this.typePolicies[t]=Object.create(null);n.fields=Object.create(null);var i=this.supertypeMap.get(t);!i&&this.fuzzySubtypes.size&&(i=this.getSupertypeSet(t,!0),this.fuzzySubtypes.forEach((function(e,n){if(e.test(t)){var o=r.supertypeMap.get(n);o&&o.forEach((function(e){return i.add(e)}))}}))),i&&i.size&&i.forEach((function(t){var i=r.getTypePolicy(t),o=i.fields,a=e.__rest(i,["fields"]);Object.assign(n,a),Object.assign(n.fields,o)}))}var o=this.toBeAdded[t];return o&&o.length&&o.splice(0).forEach((function(e){r.updateTypePolicy(t,e)})),this.typePolicies[t]},t.prototype.getFieldPolicy=function(e,t,r){if(e){var n=this.getTypePolicy(e).fields;return n[t]||r&&(n[t]=Object.create(null))}},t.prototype.getSupertypeSet=function(e,t){var r=this.supertypeMap.get(e);return!r&&t&&this.supertypeMap.set(e,r=new Set),r},t.prototype.fragmentMatches=function(e,t,r,n){var i=this;if(!e.typeCondition)return!0;if(!t)return!1;var o=e.typeCondition.name.value;if(t===o)return!0;if(this.usingPossibleTypes&&this.supertypeMap.has(o))for(var a=this.getSupertypeSet(t,!0),s=[a],u=function(e){var t=i.getSupertypeSet(e,!1);t&&t.size&&s.indexOf(t)<0&&s.push(t)},c=!(!r||!this.fuzzySubtypes.size),l=0;l<s.length;++l){var f=s[l];if(f.has(o))return a.has(o)||a.add(o),!0;f.forEach(u),c&&l===s.length-1&&Yt(e.selectionSet,r,n)&&(c=!1,this.fuzzySubtypes.forEach((function(e,r){var n=t.match(e);n&&n[0]===t&&u(r)})))}return!1},t.prototype.hasKeyArgs=function(e,t){var r=this.getFieldPolicy(e,t,!1);return!(!r||!r.keyFn)},t.prototype.getStoreFieldName=function(e){var t,r=e.typename,n=e.fieldName,i=this.getFieldPolicy(r,n,!1),o=i&&i.keyFn;if(o&&r)for(var a={typename:r,fieldName:n,field:e.field||null,variables:e.variables},s=Er(e);o;){var u=o(s,a);if(!ke(u)){t=u||n;break}o=wr(u)}return void 0===t&&(t=e.field?function(e,t){var r=null;e.directives&&(r={},e.directives.forEach((function(e){r[e.name.value]={},e.arguments&&e.arguments.forEach((function(n){var i=n.name,o=n.value;return te(r[e.name.value],i,o,t)}))})));var n=null;return e.arguments&&e.arguments.length&&(n={},e.arguments.forEach((function(e){var r=e.name,i=e.value;return te(n,r,i,t)}))),ie(e.name.value,n,r)}(e.field,e.variables):ie(n,Er(e))),!1===t?n:n===Gt(t)?t:n+":"+t},t.prototype.readField=function(e,t){var r=e.from;if(r&&(e.field||e.fieldName)){if(void 0===e.typename){var n=t.store.getFieldValue(r,"__typename");n&&(e.typename=n)}var i=this.getStoreFieldName(e),o=Gt(i),a=t.store.getFieldValue(r,i),s=this.getFieldPolicy(e.typename,o,!1),u=s&&s.read;if(u){var c=Mr(this,r,e,t,t.store.getStorage(ee(r)?r.__ref:r,i));return dr.withValue(this.cache,u,[a,c])}return a}},t.prototype.getReadFunction=function(e,t){var r=this.getFieldPolicy(e,t,!1);return r&&r.read},t.prototype.getMergeFunction=function(e,t,r){var n=this.getFieldPolicy(e,t,!1),i=n&&n.merge;return!i&&r&&(i=(n=this.getTypePolicy(r))&&n.merge),i},t.prototype.runMergeFunction=function(e,t,r,n,i){var o=r.field,a=r.typename,s=r.merge;return s===Cr?Ir(n.store)(e,t):s===Dr?t:(n.overwrite&&(e=void 0),s(e,t,Mr(this,void 0,{typename:a,fieldName:o.name.value,field:o,variables:n.variables},n,i||Object.create(null))))},t}();function Mr(e,t,r,n,i){var o=e.getStoreFieldName(r),a=Gt(o),s=r.variables||n.variables,u=n.store,c=u.toReference,l=u.canRead;return{args:Er(r),field:r.field||null,fieldName:a,storeFieldName:o,variables:s,isReference:ee,toReference:c,storage:i,cache:e.cache,canRead:l,readField:function(){return e.readField(Qr(arguments,t,s),n)},mergeObjects:Ir(n.store)}}function Qr(t,r,n){var i,o=t[0],a=t[1],s=t.length;return"string"==typeof o?i={fieldName:o,from:s>1?a:r}:(i=e.__assign({},o),zt.call(i,"from")||(i.from=r)),void 0===i.variables&&(i.variables=n),i}function Ir(t){return function(r,n){if(ke(r)||ke(n))throw w(9);if(A(r)&&A(n)){var i=t.getFieldValue(r,"__typename"),o=t.getFieldValue(n,"__typename");if(i&&o&&i!==o)return n;if(ee(r)&&Xt(n))return t.merge(r.__ref,n),r;if(Xt(r)&&ee(n))return t.merge(r,n.__ref),n;if(Xt(r)&&Xt(n))return e.__assign(e.__assign({},r),n)}return n}}function Nr(t,r,n){var i="".concat(r).concat(n),o=t.flavors.get(i);return o||t.flavors.set(i,o=t.clientOnly===r&&t.deferred===n?t:e.__assign(e.__assign({},t),{clientOnly:r,deferred:n})),o}var jr=function(){function t(e,t,r){this.cache=e,this.reader=t,this.fragments=r}return t.prototype.writeToStore=function(t,r){var n=this,i=r.query,o=r.result,a=r.dataId,s=r.variables,u=r.overwrite,c=le(i),l=new Ie;s=e.__assign(e.__assign({},ye(c)),s);var f=e.__assign(e.__assign({store:t,written:Object.create(null),merge:function(e,t){return l.merge(e,t)},variables:s,varString:Y(s)},$t(i,this.fragments)),{overwrite:!!u,incomingById:new Map,clientOnly:!1,deferred:!1,flavors:new Map}),h=this.processSelectionSet({result:o||Object.create(null),dataId:a,selectionSet:c.selectionSet,mergeTree:{map:new Map},context:f});if(!ee(h))throw w(12,o);return f.incomingById.forEach((function(e,r){var i=e.storeObject,o=e.mergeTree,a=(e.fieldNodeSet,Z(r));if(o&&o.map.size){var s=n.applyMerges(o,a,i,f);if(ee(s))return;i=s}t.merge(r,i)})),t.retain(h.__ref),h},t.prototype.processSelectionSet=function(t){var r=this,n=t.dataId,i=t.result,o=t.selectionSet,a=t.context,s=t.mergeTree,u=this.cache.policies,c=Object.create(null),l=n&&u.rootTypenamesById[n]||se(i,o,a.fragmentMap)||n&&a.store.get(n,"__typename");"string"==typeof l&&(c.__typename=l);var f=function(){var t=Qr(arguments,c,a.variables);if(ee(t.from)){var r=a.incomingById.get(t.from.__ref);if(r){var n=u.readField(e.__assign(e.__assign({},t),{from:r.storeObject}),a);if(void 0!==n)return n}}return u.readField(t,a)},h=new Set;this.flattenFields(o,i,a,l).forEach((function(e,t){var n,o=ae(t),a=i[o];if(h.add(t),void 0!==a){var p=u.getStoreFieldName({typename:l,fieldName:t.name.value,field:t,variables:e.variables}),d=Lr(s,p),v=r.processFieldValue(a,t,t.selectionSet?Nr(e,!1,!1):e,d),y=void 0;t.selectionSet&&(ee(v)||Xt(v))&&(y=f("__typename",v));var m=u.getMergeFunction(l,t.name.value,y);m?d.info={field:t,typename:l,merge:m}:zr(s,p),c=e.merge(c,((n={})[p]=v,n))}else 0}));try{var p=u.identify(i,{typename:l,selectionSet:o,fragmentMap:a.fragmentMap,storeObject:c,readField:f}),d=p[0],v=p[1];n=n||d,v&&(c=a.merge(c,v))}catch(e){if(!n)throw e}if("string"==typeof n){var y=Z(n),m=a.written[n]||(a.written[n]=[]);if(m.indexOf(o)>=0)return y;if(m.push(o),this.reader&&this.reader.isFresh(i,y,o,a))return y;var g=a.incomingById.get(n);return g?(g.storeObject=a.merge(g.storeObject,c),g.mergeTree=Vr(g.mergeTree,s),h.forEach((function(e){return g.fieldNodeSet.add(e)}))):a.incomingById.set(n,{storeObject:c,mergeTree:Wr(s)?void 0:s,fieldNodeSet:h}),y}return c},t.prototype.processFieldValue=function(e,t,r,n){var i=this;return t.selectionSet&&null!==e?ke(e)?e.map((function(e,o){var a=i.processFieldValue(e,t,r,Lr(n,o));return zr(n,o),a})):this.processSelectionSet({result:e,selectionSet:t.selectionSet,context:r,mergeTree:n}):e},t.prototype.flattenFields=function(e,t,r,i){void 0===i&&(i=se(t,e,r.fragmentMap));var o=new Map,a=this.cache.policies,u=new s.Trie(!1);return function e(s,c){var l=u.lookup(s,c.clientOnly,c.deferred);l.visited||(l.visited=!0,s.selections.forEach((function(s){if(R(s,r.variables)){var u=c.clientOnly,l=c.deferred;if(u&&l||!we(s.directives)||s.directives.forEach((function(e){var t=e.name.value;if("client"===t&&(u=!0),"defer"===t){var n=oe(e,r.variables);n&&!1===n.if||(l=!0)}})),ue(s)){var f=o.get(s);f&&(u=u&&f.clientOnly,l=l&&f.deferred),o.set(s,Nr(r,u,l))}else{var h=W(s,r.lookupFragment);if(!h&&s.kind===n.Kind.FRAGMENT_SPREAD)throw w(14,s.name.value);h&&a.fragmentMatches(h,i,t,r.variables)&&e(h.selectionSet,Nr(r,u,l))}}})))}(e,r),o},t.prototype.applyMerges=function(t,r,n,i,o){var a,s=this;if(t.map.size&&!ee(n)){var u,c=ke(n)||!ee(r)&&!Xt(r)?void 0:r,l=n;c&&!o&&(o=[ee(c)?c.__ref:c]);var f=function(e,t){return ke(e)?"number"==typeof t?e[t]:void 0:i.store.getFieldValue(e,String(t))};t.map.forEach((function(e,t){var r=f(c,t),n=f(l,t);if(void 0!==n){o&&o.push(t);var a=s.applyMerges(e,r,n,i,o);a!==n&&(u=u||new Map).set(t,a),o&&k(o.pop()===t)}})),u&&(n=ke(l)?l.slice(0):e.__assign({},l),u.forEach((function(e,t){n[t]=e})))}return t.info?this.cache.policies.runMergeFunction(r,n,t.info,i,o&&(a=i.store).getStorage.apply(a,o)):n},t}(),Ar=[];function Lr(e,t){var r=e.map;return r.has(t)||r.set(t,Ar.pop()||{map:new Map}),r.get(t)}function Vr(t,r){if(t===r||!r||Wr(r))return t;if(!t||Wr(t))return r;var n=t.info&&r.info?e.__assign(e.__assign({},t.info),r.info):t.info||r.info,i=t.map.size&&r.map.size,o={info:n,map:i?new Map:t.map.size?t.map:r.map};if(i){var a=new Set(r.map.keys());t.map.forEach((function(e,t){o.map.set(t,Vr(e,r.map.get(t))),a.delete(t)})),a.forEach((function(e){o.map.set(e,Vr(r.map.get(e),t.map.get(e)))}))}return o}function Wr(e){return!e||!(e.info||e.map.size)}function zr(e,t){var r=e.map,n=r.get(t);n&&Wr(n)&&(Ar.push(n),r.delete(t))}new Set;var Br,Kr=function(t){function n(e){void 0===e&&(e={});var r=t.call(this)||this;return r.watches=new Set,r.addTypenameTransform=new be(Ee),r.assumeImmutableResults=!0,r.makeVar=gr,r.txCount=0,r.config=function(e){return $e(Ut,e)}(e),r.addTypename=!!r.config.addTypename,r.policies=new Fr({cache:r,dataIdFromObject:r.config.dataIdFromObject,possibleTypes:r.config.possibleTypes,typePolicies:r.config.typePolicies}),r.init(),r}return e.__extends(n,t),n.prototype.init=function(){var e=this.data=new rr.Root({policies:this.policies,resultCaching:this.config.resultCaching});this.optimisticData=e.stump,this.resetResultCache()},n.prototype.resetResultCache=function(e){var t=this,r=this.storeReader,n=this.config.fragments;this.storeWriter=new jr(this,this.storeReader=new hr({cache:this,addTypename:this.addTypename,resultCacheMaxSize:this.config.resultCacheMaxSize,canonizeResults:Jt(this.config),canon:e?void 0:r&&r.canon,fragments:n}),n),this.maybeBroadcastWatch=o.wrap((function(e,r){return t.broadcastWatch(e,r)}),{max:this.config.resultCacheMaxSize||H["inMemoryCache.maybeBroadcastWatch"]||5e3,makeCacheKey:function(e){var r=e.optimistic?t.optimisticData:t.data;if(cr(r)){var n=e.optimistic,i=e.id,o=e.variables;return r.makeCacheKey(e.query,e.callback,Y({optimistic:n,id:i,variables:o}))}}}),new Set([this.data.group,this.optimisticData.group]).forEach((function(e){return e.resetCaching()}))},n.prototype.restore=function(e){return this.init(),e&&this.data.replace(e),this},n.prototype.extract=function(e){return void 0===e&&(e=!1),(e?this.optimisticData:this.data).extract()},n.prototype.read=function(t){var r=t.returnPartialData,n=void 0!==r&&r;try{return this.storeReader.diffQueryAgainstStore(e.__assign(e.__assign({},t),{store:t.optimistic?this.optimisticData:this.data,config:this.config,returnPartialData:n})).result||null}catch(e){if(e instanceof Wt)return null;throw e}},n.prototype.write=function(e){try{return++this.txCount,this.storeWriter.writeToStore(this.data,e)}finally{--this.txCount||!1===e.broadcast||this.broadcastWatches()}},n.prototype.modify=function(e){if(zt.call(e,"id")&&!e.id)return!1;var t=e.optimistic?this.optimisticData:this.data;try{return++this.txCount,t.modify(e.id||"ROOT_QUERY",e.fields)}finally{--this.txCount||!1===e.broadcast||this.broadcastWatches()}},n.prototype.diff=function(t){return this.storeReader.diffQueryAgainstStore(e.__assign(e.__assign({},t),{store:t.optimistic?this.optimisticData:this.data,rootId:t.id||"ROOT_QUERY",config:this.config}))},n.prototype.watch=function(e){var t=this;return this.watches.size||function(e){yr(e).vars.forEach((function(t){return t.attachCache(e)}))}(this),this.watches.add(e),e.immediate&&this.maybeBroadcastWatch(e),function(){t.watches.delete(e)&&!t.watches.size&&mr(t),t.maybeBroadcastWatch.forget(e)}},n.prototype.gc=function(e){var t;Y.reset(),_e.reset(),this.addTypenameTransform.resetCache(),null===(t=this.config.fragments)||void 0===t||t.resetCaches();var r=this.optimisticData.gc();return e&&!this.txCount&&(e.resetResultCache?this.resetResultCache(e.resetResultIdentities):e.resetResultIdentities&&this.storeReader.resetCanon()),r},n.prototype.retain=function(e,t){return(t?this.optimisticData:this.data).retain(e)},n.prototype.release=function(e,t){return(t?this.optimisticData:this.data).release(e)},n.prototype.identify=function(e){if(ee(e))return e.__ref;try{return this.policies.identify(e)[0]}catch(e){}},n.prototype.evict=function(t){if(!t.id){if(zt.call(t,"id"))return!1;t=e.__assign(e.__assign({},t),{id:"ROOT_QUERY"})}try{return++this.txCount,this.optimisticData.evict(t,this.data)}finally{--this.txCount||!1===t.broadcast||this.broadcastWatches()}},n.prototype.reset=function(e){var t=this;return this.init(),Y.reset(),e&&e.discardWatches?(this.watches.forEach((function(e){return t.maybeBroadcastWatch.forget(e)})),this.watches.clear(),mr(this)):this.broadcastWatches(),Promise.resolve()},n.prototype.removeOptimistic=function(e){var t=this.optimisticData.removeLayer(e);t!==this.optimisticData&&(this.optimisticData=t,this.broadcastWatches())},n.prototype.batch=function(t){var r,n=this,i=t.update,o=t.optimistic,a=void 0===o||o,s=t.removeOptimistic,u=t.onWatchUpdated,c=function(e){var t=n,o=t.data,a=t.optimisticData;++n.txCount,e&&(n.data=n.optimisticData=e);try{return r=i(n)}finally{--n.txCount,n.data=o,n.optimisticData=a}},l=new Set;return u&&!this.txCount&&this.broadcastWatches(e.__assign(e.__assign({},t),{onWatchUpdated:function(e){return l.add(e),!1}})),"string"==typeof a?this.optimisticData=this.optimisticData.addLayer(a,c):!1===a?c(this.data):c(),"string"==typeof s&&(this.optimisticData=this.optimisticData.removeLayer(s)),u&&l.size?(this.broadcastWatches(e.__assign(e.__assign({},t),{onWatchUpdated:function(e,t){var r=u.call(this,e,t);return!1!==r&&l.delete(e),r}})),l.size&&l.forEach((function(e){return n.maybeBroadcastWatch.dirty(e)}))):this.broadcastWatches(t),r},n.prototype.performTransaction=function(e,t){return this.batch({update:e,optimistic:t||null!==t})},n.prototype.transformDocument=function(e){return this.addTypenameToDocument(this.addFragmentsToDocument(e))},n.prototype.fragmentMatches=function(e,t){return this.policies.fragmentMatches(e,t)},n.prototype.lookupFragment=function(e){var t;return(null===(t=this.config.fragments)||void 0===t?void 0:t.lookup(e))||null},n.prototype.broadcastWatches=function(e){var t=this;this.txCount||this.watches.forEach((function(r){return t.maybeBroadcastWatch(r,e)}))},n.prototype.addFragmentsToDocument=function(e){var t=this.config.fragments;return t?t.transform(e):e},n.prototype.addTypenameToDocument=function(e){return this.addTypename?this.addTypenameTransform.transformDocument(e):e},n.prototype.broadcastWatch=function(e,t){var n=e.lastDiff,i=this.diff(e);t&&(e.optimistic&&"string"==typeof t.optimistic&&(i.fromOptimisticTransaction=!0),t.onWatchUpdated&&!1===t.onWatchUpdated.call(this,e,i,n))||n&&r.equal(n.result,i.result)||e.callback(e.lastDiff=i,n)},n}(Vt);function Ur(e){return!!e&&e<7}exports.NetworkStatus=void 0,(Br=exports.NetworkStatus||(exports.NetworkStatus={}))[Br.loading=1]="loading",Br[Br.setVariables=2]="setVariables",Br[Br.fetchMore=3]="fetchMore",Br[Br.refetch=4]="refetch",Br[Br.poll=6]="poll",Br[Br.ready=7]="ready",Br[Br.error=8]="error";var Jr=Object.assign,Hr=(Object.hasOwnProperty,function(t){function n(r){var i=r.queryManager,o=r.queryInfo,a=r.options,s=this,u=n.inactiveOnCreation.getValue();(s=t.call(this,(function(e){u&&(i.queries.set(s.queryId,o),u=!1);try{var t=e._subscription._observer;t&&!t.error&&(t.error=Gr)}catch(e){}var r=!s.observers.size;s.observers.add(e);var n=s.last;return n&&n.error?e.error&&e.error(n.error):n&&n.result&&e.next&&e.next(s.maskResult(n.result)),r&&s.reobserve().catch((function(){})),function(){s.observers.delete(e)&&!s.observers.size&&s.tearDownQuery()}}))||this).observers=new Set,s.subscriptions=new Set,s.dirty=!1,s.queryInfo=o,s.queryManager=i,s.waitForOwnResult=Yr(a.fetchPolicy),s.isTornDown=!1,s.subscribeToMore=s.subscribeToMore.bind(s),s.maskResult=s.maskResult.bind(s);var c=i.defaultOptions.watchQuery,l=(void 0===c?{}:c).fetchPolicy,f=void 0===l?"cache-first":l,h=a.fetchPolicy,p=void 0===h?f:h,d=a.initialFetchPolicy,v=void 0===d?"standby"===p?f:p:d;s.options=e.__assign(e.__assign({},a),{initialFetchPolicy:v,fetchPolicy:p}),s.queryId=o.queryId||i.generateQueryId();var y=le(s.query);return s.queryName=y&&y.name&&y.name.value,s}return e.__extends(n,t),Object.defineProperty(n.prototype,"query",{get:function(){return this.lastQuery||this.options.query},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"variables",{get:function(){return this.options.variables},enumerable:!1,configurable:!0}),n.prototype.result=function(){var e=this;return new Promise((function(t,r){var n={next:function(r){t(r),e.observers.delete(n),e.observers.size||e.queryManager.removeQuery(e.queryId),setTimeout((function(){i.unsubscribe()}),0)},error:r},i=e.subscribe(n)}))},n.prototype.resetDiff=function(){this.queryInfo.resetDiff()},n.prototype.getCurrentFullResult=function(t){void 0===t&&(t=!0);var n=this.getLastResult(!0),i=this.queryInfo.networkStatus||n&&n.networkStatus||exports.NetworkStatus.ready,o=e.__assign(e.__assign({},n),{loading:Ur(i),networkStatus:i}),a=this.options.fetchPolicy,s=void 0===a?"cache-first":a;if(Yr(s)||this.queryManager.getDocumentInfo(this.query).hasForcedResolvers);else if(this.waitForOwnResult)this.queryInfo.updateWatch();else{var u=this.queryInfo.getDiff();(u.complete||this.options.returnPartialData)&&(o.data=u.result),r.equal(o.data,{})&&(o.data=void 0),u.complete?(delete o.partial,!u.complete||o.networkStatus!==exports.NetworkStatus.loading||"cache-first"!==s&&"cache-only"!==s||(o.networkStatus=exports.NetworkStatus.ready,o.loading=!1)):o.partial=!0,o.networkStatus===exports.NetworkStatus.ready&&(o.error||o.errors)&&(o.networkStatus=exports.NetworkStatus.error)}return t&&this.updateLastResult(o),o},n.prototype.getCurrentResult=function(e){return void 0===e&&(e=!0),this.maskResult(this.getCurrentFullResult(e))},n.prototype.isDifferentFromLastResult=function(e,t){if(!this.last)return!0;var n=this.queryManager.getDocumentInfo(this.query),i=this.queryManager.dataMasking,o=i?n.nonReactiveQuery:this.query;return(i||n.hasNonreactiveDirective?!Ct(o,this.last.result,e,this.variables):!r.equal(this.last.result,e))||t&&!r.equal(this.last.variables,t)},n.prototype.getLast=function(e,t){var n=this.last;if(n&&n[e]&&(!t||r.equal(n.variables,this.variables)))return n[e]},n.prototype.getLastResult=function(e){return this.getLast("result",e)},n.prototype.getLastError=function(e){return this.getLast("error",e)},n.prototype.resetLastResults=function(){delete this.last,this.isTornDown=!1},n.prototype.resetQueryStoreErrors=function(){this.queryManager.resetErrors(this.queryId)},n.prototype.refetch=function(t){var n={pollInterval:0},i=this.options.fetchPolicy;return n.fetchPolicy="no-cache"===i?"no-cache":"network-only",t&&!r.equal(this.options.variables,t)&&(n.variables=this.options.variables=e.__assign(e.__assign({},this.options.variables),t)),this.queryInfo.resetLastWrite(),this.reobserve(n,exports.NetworkStatus.refetch)},n.prototype.fetchMore=function(t){var r=this,n=e.__assign(e.__assign({},t.query?t:e.__assign(e.__assign(e.__assign(e.__assign({},this.options),{query:this.options.query}),t),{variables:e.__assign(e.__assign({},this.options.variables),t.variables)})),{fetchPolicy:"no-cache"});n.query=this.transformDocument(n.query);var i=this.queryManager.generateQueryId();this.lastQuery=t.query?this.transformDocument(this.options.query):n.query;var o=this.queryInfo,a=o.networkStatus;o.networkStatus=exports.NetworkStatus.fetchMore,n.notifyOnNetworkStatusChange&&this.observe();var s=new Set,u=null==t?void 0:t.updateQuery,c="no-cache"!==this.options.fetchPolicy;return c||k(u,22),this.queryManager.fetchQuery(i,n,exports.NetworkStatus.fetchMore).then((function(l){if(r.queryManager.removeQuery(i),o.networkStatus===exports.NetworkStatus.fetchMore&&(o.networkStatus=a),c)r.queryManager.cache.batch({update:function(e){var i=t.updateQuery;i?e.updateQuery({query:r.query,variables:r.variables,returnPartialData:!0,optimistic:!1},(function(e){return i(e,{fetchMoreResult:l.data,variables:n.variables})})):e.writeQuery({query:n.query,variables:n.variables,data:l.data})},onWatchUpdated:function(e){s.add(e.query)}});else{var f=r.getLast("result"),h=u(f.data,{fetchMoreResult:l.data,variables:n.variables});r.reportResult(e.__assign(e.__assign({},f),{networkStatus:a,loading:Ur(a),data:h}),r.variables)}return r.maskResult(l)})).finally((function(){c&&!s.has(r.query)&&r.reobserveCacheFirst()}))},n.prototype.subscribeToMore=function(t){var r=this,n=this.queryManager.startGraphQLSubscription({query:t.document,variables:t.variables,context:t.context}).subscribe({next:function(n){var i=t.updateQuery;i&&r.updateQuery((function(t,r){return i(t,e.__assign({subscriptionData:n},r))}))},error:function(e){t.onError&&t.onError(e)}});return this.subscriptions.add(n),function(){r.subscriptions.delete(n)&&n.unsubscribe()}},n.prototype.setOptions=function(e){return this.reobserve(e)},n.prototype.silentSetOptions=function(e){var t=$e(this.options,e||{});Jr(this.options,t)},n.prototype.setVariables=function(e){return r.equal(this.variables,e)?this.observers.size?this.result():Promise.resolve():(this.options.variables=e,this.observers.size?this.reobserve({fetchPolicy:this.options.initialFetchPolicy,variables:e},exports.NetworkStatus.setVariables):Promise.resolve())},n.prototype.updateQuery=function(e){var t=this.queryManager,r=t.cache.diff({query:this.options.query,variables:this.variables,returnPartialData:!0,optimistic:!1}),n=r.result,i=r.complete,o=e(n,{variables:this.variables,complete:!!i,previousData:n});o&&(t.cache.writeQuery({query:this.options.query,data:o,variables:this.variables}),t.broadcastQueries())},n.prototype.startPolling=function(e){this.options.pollInterval=e,this.updatePolling()},n.prototype.stopPolling=function(){this.options.pollInterval=0,this.updatePolling()},n.prototype.applyNextFetchPolicy=function(e,t){if(t.nextFetchPolicy){var r=t.fetchPolicy,n=void 0===r?"cache-first":r,i=t.initialFetchPolicy,o=void 0===i?n:i;"standby"===n||("function"==typeof t.nextFetchPolicy?t.fetchPolicy=t.nextFetchPolicy(n,{reason:e,options:t,observable:this,initialFetchPolicy:o}):t.fetchPolicy="variables-changed"===e?o:t.nextFetchPolicy)}return t.fetchPolicy},n.prototype.fetch=function(e,t,r){var n=this.queryManager.getOrCreateQuery(this.queryId);return n.setObservableQuery(this),this.queryManager.fetchConcastWithInfo(n,e,t,r)},n.prototype.updatePolling=function(){var e=this;if(!this.queryManager.ssrMode){var t=this.pollingInfo,r=this.options.pollInterval;if(r&&this.hasObservers()){if(!t||t.interval!==r){k(r,24),(t||(this.pollingInfo={})).interval=r;var n=function(){var t,r;e.pollingInfo&&(Ur(e.queryInfo.networkStatus)||(null===(r=(t=e.options).skipPollAttempt)||void 0===r?void 0:r.call(t))?i():e.reobserve({fetchPolicy:"no-cache"===e.options.initialFetchPolicy?"no-cache":"network-only"},exports.NetworkStatus.poll).then(i,i))},i=function(){var t=e.pollingInfo;t&&(clearTimeout(t.timeout),t.timeout=setTimeout(n,t.interval))};i()}}else t&&(clearTimeout(t.timeout),delete this.pollingInfo)}},n.prototype.updateLastResult=function(t,n){void 0===n&&(n=this.variables);var i=this.getLastError();return i&&this.last&&!r.equal(n,this.last.variables)&&(i=void 0),this.last=e.__assign({result:this.queryManager.assumeImmutableResults?t:Le(t),variables:n},i?{error:i}:null)},n.prototype.reobserveAsConcast=function(t,n){var i=this;this.isTornDown=!1;var o=n===exports.NetworkStatus.refetch||n===exports.NetworkStatus.fetchMore||n===exports.NetworkStatus.poll,a=this.options.variables,s=this.options.fetchPolicy,u=$e(this.options,t||{}),c=o?u:Jr(this.options,u),l=this.transformDocument(c.query);this.lastQuery=l,o||(this.updatePolling(),!t||!t.variables||r.equal(t.variables,a)||"standby"===c.fetchPolicy||c.fetchPolicy!==s&&"function"!=typeof c.nextFetchPolicy||(this.applyNextFetchPolicy("variables-changed",c),void 0===n&&(n=exports.NetworkStatus.setVariables))),this.waitForOwnResult&&(this.waitForOwnResult=Yr(c.fetchPolicy));var f=function(){i.concast===d&&(i.waitForOwnResult=!1)},h=c.variables&&e.__assign({},c.variables),p=this.fetch(c,n,l),d=p.concast,v=p.fromLink,y={next:function(e){r.equal(i.variables,h)&&(f(),i.reportResult(e,h))},error:function(e){r.equal(i.variables,h)&&(vt(e)||(e=new yt({networkError:e})),f(),i.reportError(e,h))}};return o||!v&&this.concast||(this.concast&&this.observer&&this.concast.removeObserver(this.observer),this.concast=d,this.observer=y),d.addObserver(y),d},n.prototype.reobserve=function(e,t){return(r=this.reobserveAsConcast(e,t).promise.then(this.maskResult)).catch((function(){})),r;var r},n.prototype.resubscribeAfterError=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=this.last;this.resetLastResults();var n=this.subscribe.apply(this,e);return this.last=r,n},n.prototype.observe=function(){this.reportResult(this.getCurrentFullResult(!1),this.variables)},n.prototype.reportResult=function(e,t){var r=this.getLastError(),n=this.isDifferentFromLastResult(e,t);(r||!e.partial||this.options.returnPartialData)&&this.updateLastResult(e,t),(r||n)&&We(this.observers,"next",this.maskResult(e))},n.prototype.reportError=function(t,r){var n=e.__assign(e.__assign({},this.getLastResult()),{error:t,errors:t.graphQLErrors,networkStatus:exports.NetworkStatus.error,loading:!1});this.updateLastResult(n,r),We(this.observers,"error",this.last.error=t)},n.prototype.hasObservers=function(){return this.observers.size>0},n.prototype.tearDownQuery=function(){this.isTornDown||(this.concast&&this.observer&&(this.concast.removeObserver(this.observer),delete this.concast,delete this.observer),this.stopPolling(),this.subscriptions.forEach((function(e){return e.unsubscribe()})),this.subscriptions.clear(),this.queryManager.stopQuery(this.queryId),this.observers.clear(),this.isTornDown=!0)},n.prototype.transformDocument=function(e){return this.queryManager.transform(e)},n.prototype.maskResult=function(t){return t&&"data"in t?e.__assign(e.__assign({},t),{data:this.queryManager.maskOperation({document:this.query,data:t.data,fetchPolicy:this.options.fetchPolicy,id:this.queryId})}):t},n.prototype.resetNotifications=function(){this.cancelNotifyTimeout(),this.dirty=!1},n.prototype.cancelNotifyTimeout=function(){this.notifyTimeout&&(clearTimeout(this.notifyTimeout),this.notifyTimeout=void 0)},n.prototype.scheduleNotify=function(){var e=this;this.dirty||(this.dirty=!0,this.notifyTimeout||(this.notifyTimeout=setTimeout((function(){return e.notify()}),0)))},n.prototype.notify=function(){(this.cancelNotifyTimeout(),this.dirty)&&("cache-only"!=this.options.fetchPolicy&&"cache-and-network"!=this.options.fetchPolicy&&Ur(this.queryInfo.networkStatus)||(this.queryInfo.getDiff().fromOptimisticTransaction?this.observe():this.reobserveCacheFirst()));this.dirty=!1},n.prototype.reobserveCacheFirst=function(){var e=this.options,t=e.fetchPolicy,r=e.nextFetchPolicy;return"cache-and-network"===t||"network-only"===t?this.reobserve({fetchPolicy:"cache-first",nextFetchPolicy:function(e,n){return this.nextFetchPolicy=r,"function"==typeof this.nextFetchPolicy?this.nextFetchPolicy(e,n):t}}):this.reobserve()},n.inactiveOnCreation=new o.Slot,n}(a.Observable));function Gr(e){}function Yr(e){return"network-only"===e||"no-cache"===e||"standby"===e}Be(Hr);var Xr=new(D?WeakMap:Map);function $r(e,t){var r=e[t];"function"==typeof r&&(e[t]=function(){return Xr.set(e,(Xr.get(e)+1)%1e15),r.apply(this,arguments)})}var Zr=function(){function t(e,t){void 0===t&&(t=e.generateQueryId()),this.queryId=t,this.document=null,this.lastRequestId=1,this.stopped=!1,this.observableQuery=null;var r=this.cache=e.cache;Xr.has(r)||(Xr.set(r,0),$r(r,"evict"),$r(r,"modify"),$r(r,"reset"))}return t.prototype.init=function(e){var t=e.networkStatus||exports.NetworkStatus.loading;return this.variables&&this.networkStatus!==exports.NetworkStatus.loading&&!r.equal(this.variables,e.variables)&&(t=exports.NetworkStatus.setVariables),r.equal(e.variables,this.variables)||(this.lastDiff=void 0,this.cancel()),Object.assign(this,{document:e.document,variables:e.variables,networkError:null,graphQLErrors:this.graphQLErrors||[],networkStatus:t}),e.observableQuery&&this.setObservableQuery(e.observableQuery),e.lastRequestId&&(this.lastRequestId=e.lastRequestId),this},t.prototype.resetDiff=function(){this.lastDiff=void 0},t.prototype.getDiff=function(){var e=this.getDiffOptions();if(this.lastDiff&&r.equal(e,this.lastDiff.options))return this.lastDiff.diff;this.updateWatch(this.variables);var t=this.observableQuery;if(t&&"no-cache"===t.options.fetchPolicy)return{complete:!1};var n=this.cache.diff(e);return this.updateLastDiff(n,e),n},t.prototype.updateLastDiff=function(e,t){this.lastDiff=e?{diff:e,options:t||this.getDiffOptions()}:void 0},t.prototype.getDiffOptions=function(e){var t;return void 0===e&&(e=this.variables),{query:this.document,variables:e,returnPartialData:!0,optimistic:!0,canonizeResults:null===(t=this.observableQuery)||void 0===t?void 0:t.options.canonizeResults}},t.prototype.setDiff=function(e){var t,n,i=this.lastDiff&&this.lastDiff.diff;e&&!e.complete&&(null===(t=this.observableQuery)||void 0===t?void 0:t.getLastError())||(this.updateLastDiff(e),r.equal(i&&i.result,e&&e.result)||null===(n=this.observableQuery)||void 0===n||n.scheduleNotify())},t.prototype.setObservableQuery=function(e){e!==this.observableQuery&&(this.observableQuery=e,e&&(e.queryInfo=this))},t.prototype.stop=function(){var e;if(!this.stopped){this.stopped=!0,null===(e=this.observableQuery)||void 0===e||e.resetNotifications(),this.cancel();var t=this.observableQuery;t&&t.stopPolling()}},t.prototype.cancel=function(){var e;null===(e=this.cancelWatch)||void 0===e||e.call(this),this.cancelWatch=void 0},t.prototype.updateWatch=function(t){var n=this;void 0===t&&(t=this.variables);var i=this.observableQuery;if(!i||"no-cache"!==i.options.fetchPolicy){var o=e.__assign(e.__assign({},this.getDiffOptions(t)),{watcher:this,callback:function(e){return n.setDiff(e)}});this.lastWatch&&r.equal(o,this.lastWatch)||(this.cancel(),this.cancelWatch=this.cache.watch(this.lastWatch=o))}},t.prototype.resetLastWrite=function(){this.lastWrite=void 0},t.prototype.shouldWrite=function(e,t){var n=this.lastWrite;return!(n&&n.dmCount===Xr.get(this.cache)&&r.equal(t,n.variables)&&r.equal(e.data,n.result.data))},t.prototype.markResult=function(e,t,n,i){var o,a=this,s=new Ie,u=we(e.errors)?e.errors.slice(0):[];if(null===(o=this.observableQuery)||void 0===o||o.resetNotifications(),"incremental"in e&&we(e.incremental)){var c=Ge(this.getDiff().result,e);e.data=c}else if("hasNext"in e&&e.hasNext){var l=this.getDiff();e.data=s.merge(l.result,e.data)}this.graphQLErrors=u,"no-cache"===n.fetchPolicy?this.updateLastDiff({result:e.data,complete:!0},this.getDiffOptions(n.variables)):0!==i&&(en(e,n.errorPolicy)?this.cache.performTransaction((function(o){if(a.shouldWrite(e,n.variables))o.writeQuery({query:t,data:e.data,variables:n.variables,overwrite:1===i}),a.lastWrite={result:e,variables:n.variables,dmCount:Xr.get(a.cache)};else if(a.lastDiff&&a.lastDiff.diff.complete)return void(e.data=a.lastDiff.diff.result);var s=a.getDiffOptions(n.variables),u=o.diff(s);!a.stopped&&r.equal(a.variables,n.variables)&&a.updateWatch(n.variables),a.updateLastDiff(u,s),u.complete&&(e.data=u.result)})):this.lastWrite=void 0)},t.prototype.markReady=function(){return this.networkError=null,this.networkStatus=exports.NetworkStatus.ready},t.prototype.markError=function(e){var t;return this.networkStatus=exports.NetworkStatus.error,this.lastWrite=void 0,null===(t=this.observableQuery)||void 0===t||t.resetNotifications(),e.graphQLErrors&&(this.graphQLErrors=e.graphQLErrors),e.networkError&&(this.networkError=e.networkError),e},t}();function en(e,t){void 0===t&&(t="none");var r="ignore"===t||"all"===t,n=!Ye(e);return!n&&r&&e.data&&(n=!0),n}var tn=Object.prototype.hasOwnProperty,rn=Object.create(null),nn=function(){function t(e){var t=this;this.clientAwareness={},this.queries=new Map,this.fetchCancelFns=new Map,this.transformCache=new K(H["queryManager.getDocumentInfo"]||2e3),this.queryIdCounter=1,this.requestIdCounter=1,this.mutationIdCounter=1,this.inFlightLinkObservables=new s.Trie(!1),this.noCacheWarningsByQueryId=new Set;var r=new be((function(e){return t.cache.transformDocument(e)}),{cache:!1});this.cache=e.cache,this.link=e.link,this.defaultOptions=e.defaultOptions,this.queryDeduplication=e.queryDeduplication,this.clientAwareness=e.clientAwareness,this.localState=e.localState,this.ssrMode=e.ssrMode,this.assumeImmutableResults=e.assumeImmutableResults,this.dataMasking=e.dataMasking;var n=e.documentTransform;this.documentTransform=n?r.concat(n).concat(r):r,this.defaultContext=e.defaultContext||Object.create(null),(this.onBroadcast=e.onBroadcast)&&(this.mutationStore=Object.create(null))}return t.prototype.stop=function(){var e=this;this.queries.forEach((function(t,r){e.stopQueryNoBroadcast(r)})),this.cancelPendingFetches(w(27))},t.prototype.cancelPendingFetches=function(e){this.fetchCancelFns.forEach((function(t){return t(e)})),this.fetchCancelFns.clear()},t.prototype.mutate=function(t){return e.__awaiter(this,arguments,void 0,(function(t){var r,n,i,o,a,s,u,c=t.mutation,l=t.variables,f=t.optimisticResponse,h=t.updateQueries,p=t.refetchQueries,d=void 0===p?[]:p,v=t.awaitRefetchQueries,y=void 0!==v&&v,m=t.update,g=t.onQueryUpdated,b=t.fetchPolicy,_=void 0===b?(null===(s=this.defaultOptions.mutate)||void 0===s?void 0:s.fetchPolicy)||"network-only":b,w=t.errorPolicy,S=void 0===w?(null===(u=this.defaultOptions.mutate)||void 0===u?void 0:u.errorPolicy)||"none":w,O=t.keepRootFields,x=t.context;return e.__generator(this,(function(t){switch(t.label){case 0:return k(c,28),k("network-only"===_||"no-cache"===_,29),r=this.generateMutationId(),c=this.cache.transformForLink(this.transform(c)),n=this.getDocumentInfo(c).hasClientExports,l=this.getVariables(c,l),n?[4,this.localState.addExportedVariables(c,l,x)]:[3,2];case 1:l=t.sent(),t.label=2;case 2:return i=this.mutationStore&&(this.mutationStore[r]={mutation:c,variables:l,loading:!0,error:null}),o=f&&this.markMutationOptimistic(f,{mutationId:r,document:c,variables:l,fetchPolicy:_,errorPolicy:S,context:x,updateQueries:h,update:m,keepRootFields:O}),this.broadcastQueries(),a=this,[2,new Promise((function(t,n){return ze(a.getObservableFromLink(c,e.__assign(e.__assign({},x),{optimisticResponse:o?f:void 0}),l,{},!1),(function(t){if(Ye(t)&&"none"===S)throw new yt({graphQLErrors:Xe(t)});i&&(i.loading=!1,i.error=null);var n=e.__assign({},t);return"function"==typeof d&&(d=d(n)),"ignore"===S&&Ye(n)&&delete n.errors,a.markMutationResult({mutationId:r,result:n,document:c,variables:l,fetchPolicy:_,errorPolicy:S,context:x,update:m,updateQueries:h,awaitRefetchQueries:y,refetchQueries:d,removeOptimistic:o?r:void 0,onQueryUpdated:g,keepRootFields:O})})).subscribe({next:function(n){a.broadcastQueries(),"hasNext"in n&&!1!==n.hasNext||t(e.__assign(e.__assign({},n),{data:a.maskOperation({document:c,data:n.data,fetchPolicy:_,id:r})}))},error:function(e){i&&(i.loading=!1,i.error=e),o&&a.cache.removeOptimistic(r),a.broadcastQueries(),n(e instanceof yt?e:new yt({networkError:e}))}})}))]}}))}))},t.prototype.markMutationResult=function(t,r){var n=this;void 0===r&&(r=this.cache);var i=t.result,o=[],a="no-cache"===t.fetchPolicy;if(!a&&en(i,t.errorPolicy)){if(Je(i)||o.push({result:i.data,dataId:"ROOT_MUTATION",query:t.document,variables:t.variables}),Je(i)&&we(i.incremental)){var s=r.diff({id:"ROOT_MUTATION",query:this.getDocumentInfo(t.document).asQuery,variables:t.variables,optimistic:!1,returnPartialData:!0}),u=void 0;s.result&&(u=Ge(s.result,i)),void 0!==u&&(i.data=u,o.push({result:u,dataId:"ROOT_MUTATION",query:t.document,variables:t.variables}))}var c=t.updateQueries;c&&this.queries.forEach((function(e,t){var a=e.observableQuery,s=a&&a.queryName;if(s&&tn.call(c,s)){var u=c[s],l=n.queries.get(t),f=l.document,h=l.variables,p=r.diff({query:f,variables:h,returnPartialData:!0,optimistic:!1}),d=p.result;if(p.complete&&d){var v=u(d,{mutationResult:i,queryName:f&&fe(f)||void 0,queryVariables:h});v&&o.push({result:v,dataId:"ROOT_QUERY",query:f,variables:h})}}}))}if(o.length>0||(t.refetchQueries||"").length>0||t.update||t.onQueryUpdated||t.removeOptimistic){var l=[];if(this.refetchQueries({updateCache:function(r){a||o.forEach((function(e){return r.write(e)}));var s,u=t.update,c=!(Je(s=i)||function(e){return"hasNext"in e&&"data"in e}(s))||Je(i)&&!i.hasNext;if(u){if(!a){var l=r.diff({id:"ROOT_MUTATION",query:n.getDocumentInfo(t.document).asQuery,variables:t.variables,optimistic:!1,returnPartialData:!0});l.complete&&("incremental"in(i=e.__assign(e.__assign({},i),{data:l.result}))&&delete i.incremental,"hasNext"in i&&delete i.hasNext)}c&&u(r,i,{context:t.context,variables:t.variables})}a||t.keepRootFields||!c||r.modify({id:"ROOT_MUTATION",fields:function(e,t){var r=t.fieldName,n=t.DELETE;return"__typename"===r?e:n}})},include:t.refetchQueries,optimistic:!1,removeOptimistic:t.removeOptimistic,onQueryUpdated:t.onQueryUpdated||null}).forEach((function(e){return l.push(e)})),t.awaitRefetchQueries||t.onQueryUpdated)return Promise.all(l).then((function(){return i}))}return Promise.resolve(i)},t.prototype.markMutationOptimistic=function(t,r){var n=this,i="function"==typeof t?t(r.variables,{IGNORE:rn}):t;return i!==rn&&(this.cache.recordOptimisticTransaction((function(t){try{n.markMutationResult(e.__assign(e.__assign({},r),{result:{data:i}}),t)}catch(e){}}),r.mutationId),!0)},t.prototype.fetchQuery=function(e,t,r){return this.fetchConcastWithInfo(this.getOrCreateQuery(e),t,r).concast.promise},t.prototype.getQueryStore=function(){var e=Object.create(null);return this.queries.forEach((function(t,r){e[r]={variables:t.variables,networkStatus:t.networkStatus,networkError:t.networkError,graphQLErrors:t.graphQLErrors}})),e},t.prototype.resetErrors=function(e){var t=this.queries.get(e);t&&(t.networkError=void 0,t.graphQLErrors=[])},t.prototype.transform=function(e){return this.documentTransform.transformDocument(e)},t.prototype.getDocumentInfo=function(t){var r=this.transformCache;if(!r.has(t)){var n={hasClientExports:P(t),hasForcedResolvers:this.localState.shouldForceResolvers(t),hasNonreactiveDirective:E(["nonreactive"],t),nonReactiveQuery:Ce(t),clientQuery:this.localState.clientQuery(t),serverQuery:Re([{name:"client",remove:!0},{name:"connection"},{name:"nonreactive"},{name:"unmask"}],t),defaultVars:ye(le(t)),asQuery:e.__assign(e.__assign({},t),{definitions:t.definitions.map((function(t){return"OperationDefinition"===t.kind&&"query"!==t.operation?e.__assign(e.__assign({},t),{operation:"query"}):t}))})};r.set(t,n)}return r.get(t)},t.prototype.getVariables=function(t,r){return e.__assign(e.__assign({},this.getDocumentInfo(t).defaultVars),r)},t.prototype.watchQuery=function(t){var r=this.transform(t.query);void 0===(t=e.__assign(e.__assign({},t),{variables:this.getVariables(r,t.variables)})).notifyOnNetworkStatusChange&&(t.notifyOnNetworkStatusChange=!1);var n=new Zr(this),i=new Hr({queryManager:this,queryInfo:n,options:t});return i.lastQuery=r,Hr.inactiveOnCreation.getValue()||this.queries.set(i.queryId,n),n.init({document:r,observableQuery:i,variables:i.variables}),i},t.prototype.query=function(t,r){var n=this;void 0===r&&(r=this.generateQueryId()),k(t.query,30),k("Document"===t.query.kind,31),k(!t.returnPartialData,32),k(!t.pollInterval,33);var i=this.transform(t.query);return this.fetchQuery(r,e.__assign(e.__assign({},t),{query:i})).then((function(o){return o&&e.__assign(e.__assign({},o),{data:n.maskOperation({document:i,data:o.data,fetchPolicy:t.fetchPolicy,id:r})})})).finally((function(){return n.stopQuery(r)}))},t.prototype.generateQueryId=function(){return String(this.queryIdCounter++)},t.prototype.generateRequestId=function(){return this.requestIdCounter++},t.prototype.generateMutationId=function(){return String(this.mutationIdCounter++)},t.prototype.stopQueryInStore=function(e){this.stopQueryInStoreNoBroadcast(e),this.broadcastQueries()},t.prototype.stopQueryInStoreNoBroadcast=function(e){var t=this.queries.get(e);t&&t.stop()},t.prototype.clearStore=function(e){return void 0===e&&(e={discardWatches:!0}),this.cancelPendingFetches(w(34)),this.queries.forEach((function(e){e.observableQuery?e.networkStatus=exports.NetworkStatus.loading:e.stop()})),this.mutationStore&&(this.mutationStore=Object.create(null)),this.cache.reset(e)},t.prototype.getObservableQueries=function(t){var r=this;void 0===t&&(t="active");var n=new Map,i=new Map,o=new Map,a=new Set;return Array.isArray(t)&&t.forEach((function(e){if("string"==typeof e)i.set(e,e),o.set(e,!1);else if(A(n=e)&&"Document"===n.kind&&Array.isArray(n.definitions)){var t=_e(r.transform(e));i.set(t,fe(e)),o.set(t,!1)}else A(e)&&e.query&&a.add(e);var n})),this.queries.forEach((function(e,r){var i=e.observableQuery,a=e.document;if(i){if("all"===t)return void n.set(r,i);var s=i.queryName;if("standby"===i.options.fetchPolicy||"active"===t&&!i.hasObservers())return;("active"===t||s&&o.has(s)||a&&o.has(_e(a)))&&(n.set(r,i),s&&o.set(s,!0),a&&o.set(_e(a),!0))}})),a.size&&a.forEach((function(t){var i=g("legacyOneTimeQuery"),o=r.getOrCreateQuery(i).init({document:t.query,variables:t.variables}),a=new Hr({queryManager:r,queryInfo:o,options:e.__assign(e.__assign({},t),{fetchPolicy:"network-only"})});k(a.queryId===i),o.setObservableQuery(a),n.set(i,a)})),n},t.prototype.reFetchObservableQueries=function(e){var t=this;void 0===e&&(e=!1);var r=[];return this.getObservableQueries(e?"all":"active").forEach((function(n,i){var o=n.options.fetchPolicy;n.resetLastResults(),(e||"standby"!==o&&"cache-only"!==o)&&r.push(n.refetch()),(t.queries.get(i)||n.queryInfo).setDiff(null)})),this.broadcastQueries(),Promise.all(r)},t.prototype.startGraphQLSubscription=function(e){var t=this,r=e.query,n=e.variables,i=e.fetchPolicy,o=e.errorPolicy,s=void 0===o?"none":o,u=e.context,c=void 0===u?{}:u,l=e.extensions,f=void 0===l?{}:l;r=this.transform(r),n=this.getVariables(r,n);var h=function(e){return t.getObservableFromLink(r,c,e,f).map((function(n){"no-cache"!==i&&(en(n,s)&&t.cache.write({query:r,result:n.data,dataId:"ROOT_SUBSCRIPTION",variables:e}),t.broadcastQueries());var o=Ye(n),a=function(e){return!!e.extensions&&Array.isArray(e.extensions[dt])}(n);if(o||a){var u={};if(o&&(u.graphQLErrors=n.errors),a&&(u.protocolErrors=n.extensions[dt]),"none"===s||a)throw new yt(u)}return"ignore"===s&&delete n.errors,n}))};if(this.getDocumentInfo(r).hasClientExports){var p=this.localState.addExportedVariables(r,n,c).then(h);return new a.Observable((function(e){var t=null;return p.then((function(r){return t=r.subscribe(e)}),e.error),function(){return t&&t.unsubscribe()}}))}return h(n)},t.prototype.stopQuery=function(e){this.stopQueryNoBroadcast(e),this.broadcastQueries()},t.prototype.stopQueryNoBroadcast=function(e){this.stopQueryInStoreNoBroadcast(e),this.removeQuery(e)},t.prototype.removeQuery=function(e){var t;this.fetchCancelFns.delete(e),this.queries.has(e)&&(null===(t=this.queries.get(e))||void 0===t||t.stop(),this.queries.delete(e))},t.prototype.broadcastQueries=function(){this.onBroadcast&&this.onBroadcast(),this.queries.forEach((function(e){var t;return null===(t=e.observableQuery)||void 0===t?void 0:t.notify()}))},t.prototype.getLocalState=function(){return this.localState},t.prototype.getObservableFromLink=function(t,r,n,i,o){var s,u,c=this;void 0===o&&(o=null!==(s=null==r?void 0:r.queryDeduplication)&&void 0!==s?s:this.queryDeduplication);var l=this.getDocumentInfo(t),f=l.serverQuery,h=l.clientQuery;if(f){var p=this.inFlightLinkObservables,d=this.link,v={query:f,variables:n,operationName:fe(f)||void 0,context:this.prepareContext(e.__assign(e.__assign({},r),{forceFetch:!o})),extensions:i};if(r=v.context,o){var y=_e(f),m=Y(n),g=p.lookup(y,m);if(!(u=g.observable)){var b=new Ue([lt(d,v)]);u=g.observable=b,b.beforeNext((function e(t,r){"next"===t&&"hasNext"in r&&r.hasNext?b.beforeNext(e):p.remove(y,m)}))}}else u=new Ue([lt(d,v)])}else u=new Ue([a.Observable.of({data:{}})]),r=this.prepareContext(r);return h&&(u=ze(u,(function(e){return c.localState.runResolvers({document:h,remoteResult:e,context:r,variables:n})}))),u},t.prototype.getResultsFromLink=function(e,t,r){var n=e.lastRequestId=this.generateRequestId(),i=this.cache.transformForLink(r.query);return ze(this.getObservableFromLink(i,r.context,r.variables),(function(o){var a=Xe(o),s=a.length>0,u=r.errorPolicy;if(n>=e.lastRequestId){if(s&&"none"===u)throw e.markError(new yt({graphQLErrors:a}));e.markResult(o,i,r,t),e.markReady()}var c={data:o.data,loading:!1,networkStatus:exports.NetworkStatus.ready};return s&&"none"===u&&(c.data=void 0),s&&"ignore"!==u&&(c.errors=a,c.networkStatus=exports.NetworkStatus.error),c}),(function(t){var r=vt(t)?t:new yt({networkError:t});throw n>=e.lastRequestId&&e.markError(r),r}))},t.prototype.fetchConcastWithInfo=function(e,t,r,n){var i=this;void 0===r&&(r=exports.NetworkStatus.loading),void 0===n&&(n=t.query);var o,a,s=this.getVariables(n,t.variables),u=this.defaultOptions.watchQuery,c=t.fetchPolicy,l=void 0===c?u&&u.fetchPolicy||"cache-first":c,f=t.errorPolicy,h=void 0===f?u&&u.errorPolicy||"none":f,p=t.returnPartialData,d=void 0!==p&&p,v=t.notifyOnNetworkStatusChange,y=void 0!==v&&v,m=t.context,g=void 0===m?{}:m,b=Object.assign({},t,{query:n,variables:s,fetchPolicy:l,errorPolicy:h,returnPartialData:d,notifyOnNetworkStatusChange:y,context:g}),_=function(n){b.variables=n;var o=i.fetchQueryByPolicy(e,b,r);return"standby"!==b.fetchPolicy&&o.sources.length>0&&e.observableQuery&&e.observableQuery.applyNextFetchPolicy("after-fetch",t),o},k=function(){return i.fetchCancelFns.delete(e.queryId)};if(this.fetchCancelFns.set(e.queryId,(function(e){k(),setTimeout((function(){return o.cancel(e)}))})),this.getDocumentInfo(b.query).hasClientExports)o=new Ue(this.localState.addExportedVariables(b.query,b.variables,b.context).then(_).then((function(e){return e.sources}))),a=!0;else{var w=_(b.variables);a=w.fromLink,o=new Ue(w.sources)}return o.promise.then(k,k),{concast:o,fromLink:a}},t.prototype.refetchQueries=function(e){var t=this,r=e.updateCache,n=e.include,i=e.optimistic,o=void 0!==i&&i,a=e.removeOptimistic,s=void 0===a?o?g("refetchQueries"):void 0:a,u=e.onQueryUpdated,c=new Map;n&&this.getObservableQueries(n).forEach((function(e,r){c.set(r,{oq:e,lastDiff:(t.queries.get(r)||e.queryInfo).getDiff()})}));var l=new Map;return r&&this.cache.batch({update:r,optimistic:o&&s||!1,removeOptimistic:s,onWatchUpdated:function(e,t,r){var n=e.watcher instanceof Zr&&e.watcher.observableQuery;if(n){if(u){c.delete(n.queryId);var i=u(n,t,r);return!0===i&&(i=n.refetch()),!1!==i&&l.set(n,i),i}null!==u&&c.set(n.queryId,{oq:n,lastDiff:r,diff:t})}}}),c.size&&c.forEach((function(e,r){var n,i=e.oq,o=e.lastDiff,a=e.diff;u&&(a||(a=t.cache.diff(i.queryInfo.getDiffOptions())),n=u(i,a,o)),u&&!0!==n||(n=i.refetch()),!1!==n&&l.set(i,n),r.indexOf("legacyOneTimeQuery")>=0&&t.stopQueryNoBroadcast(r)})),s&&this.cache.removeOptimistic(s),l},t.prototype.maskOperation=function(e){var t=e.document,r=e.data;return this.dataMasking?function(e,t,r){var n;if(!r.fragmentMatches)return e;var i=le(t);return k(i,51),null==e?e:jt(e,i.selectionSet,{operationType:i.operation,operationName:null===(n=i.name)||void 0===n?void 0:n.value,fragmentMap:V(he(t)),cache:r,mutableTargets:new Qt,knownChanged:new It})}(r,t,this.cache):r},t.prototype.maskFragment=function(e){var t=e.data,r=e.fragment,n=e.fragmentName;return this.dataMasking?Lt(t,r,this.cache,n):t},t.prototype.fetchQueryByPolicy=function(t,r,n){var i=this,o=r.query,s=r.variables,u=r.fetchPolicy,c=r.refetchWritePolicy,l=r.errorPolicy,f=r.returnPartialData,h=r.context,p=r.notifyOnNetworkStatusChange,d=t.networkStatus;t.init({document:o,variables:s,networkStatus:n});var v=function(){return t.getDiff()},y=function(r,n){void 0===n&&(n=t.networkStatus||exports.NetworkStatus.loading);var u=r.result;var c=function(t){return a.Observable.of(e.__assign({data:t,loading:Ur(n),networkStatus:n},r.complete?null:{partial:!0}))};return u&&i.getDocumentInfo(o).hasForcedResolvers?i.localState.runResolvers({document:o,remoteResult:{data:u},context:h,variables:s,onlyRunForcedResolvers:!0}).then((function(e){return c(e.data||void 0)})):"none"===l&&n===exports.NetworkStatus.refetch&&Array.isArray(r.missing)?c(void 0):c(u)},m="no-cache"===u?0:n===exports.NetworkStatus.refetch&&"merge"!==c?1:2,g=function(){return i.getResultsFromLink(t,m,{query:o,variables:s,context:h,fetchPolicy:u,errorPolicy:l})},b=p&&"number"==typeof d&&d!==n&&Ur(n);switch(u){default:case"cache-first":return(_=v()).complete?{fromLink:!1,sources:[y(_,t.markReady())]}:f||b?{fromLink:!0,sources:[y(_),g()]}:{fromLink:!0,sources:[g()]};case"cache-and-network":var _;return(_=v()).complete||f||b?{fromLink:!0,sources:[y(_),g()]}:{fromLink:!0,sources:[g()]};case"cache-only":return{fromLink:!1,sources:[y(v(),t.markReady())]};case"network-only":return b?{fromLink:!0,sources:[y(v()),g()]}:{fromLink:!0,sources:[g()]};case"no-cache":return b?{fromLink:!0,sources:[y(t.getDiff()),g()]}:{fromLink:!0,sources:[g()]};case"standby":return{fromLink:!1,sources:[]}}},t.prototype.getOrCreateQuery=function(e){return e&&!this.queries.has(e)&&this.queries.set(e,new Zr(this,e)),this.queries.get(e)},t.prototype.prepareContext=function(t){void 0===t&&(t={});var r=this.localState.prepareContext(t);return e.__assign(e.__assign(e.__assign({},this.defaultContext),r),{clientAwareness:this.clientAwareness})},t}(),on=function(){function t(e){var t=e.cache,r=e.client,n=e.resolvers,i=e.fragmentMatcher;this.selectionsToResolveCache=new WeakMap,this.cache=t,r&&(this.client=r),n&&this.addResolvers(n),i&&this.setFragmentMatcher(i)}return t.prototype.addResolvers=function(e){var t=this;this.resolvers=this.resolvers||{},Array.isArray(e)?e.forEach((function(e){t.resolvers=Fe(t.resolvers,e)})):this.resolvers=Fe(this.resolvers,e)},t.prototype.setResolvers=function(e){this.resolvers={},this.addResolvers(e)},t.prototype.getResolvers=function(){return this.resolvers||{}},t.prototype.runResolvers=function(t){return e.__awaiter(this,arguments,void 0,(function(t){var r=t.document,n=t.remoteResult,i=t.context,o=t.variables,a=t.onlyRunForcedResolvers,s=void 0!==a&&a;return e.__generator(this,(function(t){return r?[2,this.resolveDocument(r,n.data,i,o,this.fragmentMatcher,s).then((function(t){return e.__assign(e.__assign({},n),{data:t.result})}))]:[2,n]}))}))},t.prototype.setFragmentMatcher=function(e){this.fragmentMatcher=e},t.prototype.getFragmentMatcher=function(){return this.fragmentMatcher},t.prototype.clientQuery=function(e){return E(["client"],e)&&this.resolvers?e:null},t.prototype.serverQuery=function(e){return Te(e)},t.prototype.prepareContext=function(t){var r=this.cache;return e.__assign(e.__assign({},t),{cache:r,getCacheKey:function(e){return r.identify(e)}})},t.prototype.addExportedVariables=function(t){return e.__awaiter(this,arguments,void 0,(function(t,r,n){return void 0===r&&(r={}),void 0===n&&(n={}),e.__generator(this,(function(i){return t?[2,this.resolveDocument(t,this.buildRootValueFromCache(t,r)||{},this.prepareContext(n),r).then((function(t){return e.__assign(e.__assign({},r),t.exportedVariables)}))]:[2,e.__assign({},r)]}))}))},t.prototype.shouldForceResolvers=function(e){var t=!1;return n.visit(e,{Directive:{enter:function(e){if("client"===e.name.value&&e.arguments&&(t=e.arguments.some((function(e){return"always"===e.name.value&&"BooleanValue"===e.value.kind&&!0===e.value.value}))))return n.BREAK}}}),t},t.prototype.buildRootValueFromCache=function(e,t){return this.cache.diff({query:Pe(e),variables:t,returnPartialData:!0,optimistic:!1}).result},t.prototype.resolveDocument=function(t,r){return e.__awaiter(this,arguments,void 0,(function(t,r,n,i,o,a){var s,u,c,l,f,h,p,d,v,y;return void 0===n&&(n={}),void 0===i&&(i={}),void 0===o&&(o=function(){return!0}),void 0===a&&(a=!1),e.__generator(this,(function(m){return s=ve(t),u=he(t),c=V(u),l=this.collectSelectionsToResolve(s,c),f=s.operation,h=f?f.charAt(0).toUpperCase()+f.slice(1):"Query",d=(p=this).cache,v=p.client,y={fragmentMap:c,context:e.__assign(e.__assign({},n),{cache:d,client:v}),variables:i,fragmentMatcher:o,defaultOperationType:h,exportedVariables:{},selectionsToResolve:l,onlyRunForcedResolvers:a},[2,this.resolveSelectionSet(s.selectionSet,false,r,y).then((function(e){return{result:e,exportedVariables:y.exportedVariables}}))]}))}))},t.prototype.resolveSelectionSet=function(t,r,n,i){return e.__awaiter(this,void 0,void 0,(function(){var o,a,s,u,c,l=this;return e.__generator(this,(function(f){return o=i.fragmentMap,a=i.context,s=i.variables,u=[n],c=function(t){return e.__awaiter(l,void 0,void 0,(function(){var c,l;return e.__generator(this,(function(e){return(r||i.selectionsToResolve.has(t))&&R(t,s)?ue(t)?[2,this.resolveField(t,r,n,i).then((function(e){var r;void 0!==e&&u.push(((r={})[ae(t)]=e,r))}))]:(!function(e){return"InlineFragment"===e.kind}(t)?(c=o[t.name.value],k(c,19,t.name.value)):c=t,c&&c.typeCondition&&(l=c.typeCondition.name.value,i.fragmentMatcher(n,l,a))?[2,this.resolveSelectionSet(c.selectionSet,r,n,i).then((function(e){u.push(e)}))]:[2]):[2]}))}))},[2,Promise.all(t.selections.map(c)).then((function(){return Me(u)}))]}))}))},t.prototype.resolveField=function(t,r,n,i){return e.__awaiter(this,void 0,void 0,(function(){var o,a,s,u,c,l,f,h,p,d=this;return e.__generator(this,(function(e){return n?(o=i.variables,a=t.name.value,s=ae(t),u=a!==s,c=n[s]||n[a],l=Promise.resolve(c),i.onlyRunForcedResolvers&&!this.shouldForceResolvers(t)||(f=n.__typename||i.defaultOperationType,(h=this.resolvers&&this.resolvers[f])&&(p=h[u?a:s])&&(l=Promise.resolve(dr.withValue(this.cache,p,[n,oe(t,o),i.context,{field:t,fragmentMap:i.fragmentMap}])))),[2,l.then((function(e){var n,o;if(void 0===e&&(e=c),t.directives&&t.directives.forEach((function(t){"export"===t.name.value&&t.arguments&&t.arguments.forEach((function(t){"as"===t.name.value&&"StringValue"===t.value.kind&&(i.exportedVariables[t.value.value]=e)}))})),!t.selectionSet)return e;if(null==e)return e;var a=null!==(o=null===(n=t.directives)||void 0===n?void 0:n.some((function(e){return"client"===e.name.value})))&&void 0!==o&&o;return Array.isArray(e)?d.resolveSubSelectedArray(t,r||a,e,i):t.selectionSet?d.resolveSelectionSet(t.selectionSet,r||a,e,i):void 0}))]):[2,null]}))}))},t.prototype.resolveSubSelectedArray=function(e,t,r,n){var i=this;return Promise.all(r.map((function(r){return null===r?null:Array.isArray(r)?i.resolveSubSelectedArray(e,t,r,n):e.selectionSet?i.resolveSelectionSet(e.selectionSet,t,r,n):void 0})))},t.prototype.collectSelectionsToResolve=function(e,t){var r=function(e){return!Array.isArray(e)},i=this.selectionsToResolveCache;return function e(o){if(!i.has(o)){var a=new Set;i.set(o,a),n.visit(o,{Directive:function(e,t,i,o,s){"client"===e.name.value&&s.forEach((function(e){r(e)&&n.isSelectionNode(e)&&a.add(e)}))},FragmentSpread:function(i,o,s,u,c){var l=t[i.name.value];k(l,20,i.name.value);var f=e(l);f.size>0&&(c.forEach((function(e){r(e)&&n.isSelectionNode(e)&&a.add(e)})),a.add(i),f.forEach((function(e){a.add(e)})))}})}return i.get(o)}(e)},t}(),an=function(){function t(t){var r,n=this;if(this.resetStoreCallbacks=[],this.clearStoreCallbacks=[],!t.cache)throw w(16);var i=t.uri,o=t.credentials,a=t.headers,s=t.cache,u=t.documentTransform,c=t.ssrMode,l=void 0!==c&&c,f=t.ssrForceFetchDelay,h=void 0===f?0:f,p=t.connectToDevTools,v=t.queryDeduplication,y=void 0===v||v,m=t.defaultOptions,g=t.defaultContext,b=t.assumeImmutableResults,_=void 0===b?s.assumeImmutableResults:b,k=t.resolvers,S=t.typeDefs,O=t.fragmentMatcher,x=t.name,q=t.version,R=t.devtools,E=t.dataMasking,P=t.link;P||(P=i?new Tt({uri:i,credentials:o,headers:a}):ot.empty()),this.link=P,this.cache=s,this.disableNetworkFetches=l||h>0,this.queryDeduplication=y,this.defaultOptions=m||Object.create(null),this.typeDefs=S,this.devtoolsConfig=e.__assign(e.__assign({},R),{enabled:null!==(r=null==R?void 0:R.enabled)&&void 0!==r?r:p}),void 0===this.devtoolsConfig.enabled&&(this.devtoolsConfig.enabled=!1),h&&setTimeout((function(){return n.disableNetworkFetches=!1}),h),this.watchQuery=this.watchQuery.bind(this),this.query=this.query.bind(this),this.mutate=this.mutate.bind(this),this.watchFragment=this.watchFragment.bind(this),this.resetStore=this.resetStore.bind(this),this.reFetchObservableQueries=this.reFetchObservableQueries.bind(this),this.version=d,this.localState=new on({cache:s,client:this,resolvers:k,fragmentMatcher:O}),this.queryManager=new nn({cache:this.cache,link:this.link,defaultOptions:this.defaultOptions,defaultContext:g,documentTransform:u,queryDeduplication:y,ssrMode:l,dataMasking:!!E,clientAwareness:{name:x,version:q},localState:this.localState,assumeImmutableResults:_,onBroadcast:this.devtoolsConfig.enabled?function(){n.devToolsHookCb&&n.devToolsHookCb({action:{},state:{queries:n.queryManager.getQueryStore(),mutations:n.queryManager.mutationStore||{}},dataWithOptimisticResults:n.cache.extract(!0)})}:void 0}),this.devtoolsConfig.enabled&&this.connectToDevTools()}return t.prototype.connectToDevTools=function(){if("undefined"!=typeof window){var e=window,t=Symbol.for("apollo.devtools");(e[t]=e[t]||[]).push(this),e.__APOLLO_CLIENT__=this}},Object.defineProperty(t.prototype,"documentTransform",{get:function(){return this.queryManager.documentTransform},enumerable:!1,configurable:!0}),t.prototype.stop=function(){this.queryManager.stop()},t.prototype.watchQuery=function(t){return this.defaultOptions.watchQuery&&(t=Ze(this.defaultOptions.watchQuery,t)),!this.disableNetworkFetches||"network-only"!==t.fetchPolicy&&"cache-and-network"!==t.fetchPolicy||(t=e.__assign(e.__assign({},t),{fetchPolicy:"cache-first"})),this.queryManager.watchQuery(t)},t.prototype.query=function(t){return this.defaultOptions.query&&(t=Ze(this.defaultOptions.query,t)),k("cache-and-network"!==t.fetchPolicy,17),this.disableNetworkFetches&&"network-only"===t.fetchPolicy&&(t=e.__assign(e.__assign({},t),{fetchPolicy:"cache-first"})),this.queryManager.query(t)},t.prototype.mutate=function(e){return this.defaultOptions.mutate&&(e=Ze(this.defaultOptions.mutate,e)),this.queryManager.mutate(e)},t.prototype.subscribe=function(t){var r=this,n=this.queryManager.generateQueryId();return this.queryManager.startGraphQLSubscription(t).map((function(i){return e.__assign(e.__assign({},i),{data:r.queryManager.maskOperation({document:t.query,data:i.data,fetchPolicy:t.fetchPolicy,id:n})})}))},t.prototype.readQuery=function(e,t){return void 0===t&&(t=!1),this.cache.readQuery(e,t)},t.prototype.watchFragment=function(t){var r;return this.cache.watchFragment(e.__assign(e.__assign({},t),((r={})[Symbol.for("apollo.dataMasking")]=this.queryManager.dataMasking,r)))},t.prototype.readFragment=function(e,t){return void 0===t&&(t=!1),this.cache.readFragment(e,t)},t.prototype.writeQuery=function(e){var t=this.cache.writeQuery(e);return!1!==e.broadcast&&this.queryManager.broadcastQueries(),t},t.prototype.writeFragment=function(e){var t=this.cache.writeFragment(e);return!1!==e.broadcast&&this.queryManager.broadcastQueries(),t},t.prototype.__actionHookForDevTools=function(e){this.devToolsHookCb=e},t.prototype.__requestRaw=function(e){return lt(this.link,e)},t.prototype.resetStore=function(){var e=this;return Promise.resolve().then((function(){return e.queryManager.clearStore({discardWatches:!1})})).then((function(){return Promise.all(e.resetStoreCallbacks.map((function(e){return e()})))})).then((function(){return e.reFetchObservableQueries()}))},t.prototype.clearStore=function(){var e=this;return Promise.resolve().then((function(){return e.queryManager.clearStore({discardWatches:!0})})).then((function(){return Promise.all(e.clearStoreCallbacks.map((function(e){return e()})))}))},t.prototype.onResetStore=function(e){var t=this;return this.resetStoreCallbacks.push(e),function(){t.resetStoreCallbacks=t.resetStoreCallbacks.filter((function(t){return t!==e}))}},t.prototype.onClearStore=function(e){var t=this;return this.clearStoreCallbacks.push(e),function(){t.clearStoreCallbacks=t.clearStoreCallbacks.filter((function(t){return t!==e}))}},t.prototype.reFetchObservableQueries=function(e){return this.queryManager.reFetchObservableQueries(e)},t.prototype.refetchQueries=function(e){var t=this.queryManager.refetchQueries(e),r=[],n=[];t.forEach((function(e,t){r.push(t),n.push(e)}));var i=Promise.all(n);return i.queries=r,i.results=n,i.catch((function(e){})),i},t.prototype.getObservableQueries=function(e){return void 0===e&&(e="active"),this.queryManager.getObservableQueries(e)},t.prototype.extract=function(e){return this.cache.extract(e)},t.prototype.restore=function(e){return this.cache.restore(e)},t.prototype.addResolvers=function(e){this.localState.addResolvers(e)},t.prototype.setResolvers=function(e){this.localState.setResolvers(e)},t.prototype.getResolvers=function(){return this.localState.getResolvers()},t.prototype.setLocalStateFragmentMatcher=function(e){this.localState.setFragmentMatcher(e)},t.prototype.setLink=function(e){this.link=this.queryManager.link=e},Object.defineProperty(t.prototype,"defaultContext",{get:function(){return this.queryManager.defaultContext},enumerable:!1,configurable:!0}),t}();var sn=M?Symbol.for("__APOLLO_CONTEXT__"):"__APOLLO_CONTEXT__";function un(){k("createContext"in p,54);var e=p.createContext[sn];return e||(Object.defineProperty(p.createContext,sn,{value:e=p.createContext({}),enumerable:!1,writable:!1,configurable:!0}),e.displayName="ApolloContext"),e}var cn=un;function ln(e){var t=p.useContext(un()),r=e||t.client;return k(!!r,58),r}var fn,hn,pn=p.useSyncExternalStore||function(e,t,r){var n=t();var i=p.useState({inst:{value:n,getSnapshot:t}}),o=i[0].inst,a=i[1];return j?p.useLayoutEffect((function(){Object.assign(o,{value:n,getSnapshot:t}),dn(o)&&a({inst:o})}),[e,n,t]):Object.assign(o,{value:n,getSnapshot:t}),p.useEffect((function(){return dn(o)&&a({inst:o}),e((function(){dn(o)&&a({inst:o})}))}),[e]),n};function dn(e){var t=e.value,r=e.getSnapshot;try{return t!==r()}catch(e){return!0}}function vn(e){var t;switch(e){case exports.DocumentType.Query:t="Query";break;case exports.DocumentType.Mutation:t="Mutation";break;case exports.DocumentType.Subscription:t="Subscription"}return t}function yn(e){hn||(hn=new K(H.parser||1e3));var t,r,n=hn.get(e);if(n)return n;k(!!e&&!!e.kind,70,e);for(var i=[],o=[],a=[],s=[],u=0,c=e.definitions;u<c.length;u++){var l=c[u];if("FragmentDefinition"!==l.kind){if("OperationDefinition"===l.kind)switch(l.operation){case"query":o.push(l);break;case"mutation":a.push(l);break;case"subscription":s.push(l)}}else i.push(l)}k(!i.length||o.length||a.length||s.length,71),k(o.length+a.length+s.length<=1,72,e,o.length,s.length,a.length),r=o.length?exports.DocumentType.Query:exports.DocumentType.Mutation,o.length||a.length||(r=exports.DocumentType.Subscription);var f=o.length?o:a.length?a:s;k(1===f.length,73,e,f.length);var h=f[0];t=h.variableDefinitions||[];var p={name:h.name&&"Name"===h.name.kind?h.name.value:"data",type:r,variables:t};return hn.set(e,p),p}function mn(e,t){var r=yn(e),n=vn(t),i=vn(r.type);k(r.type===t,74,n,n,i)}function gn(e,t){var n=p.useRef(void 0);return n.current&&r.equal(n.current.deps,t)||(n.current={value:e(),deps:t}),n.current.value}exports.DocumentType=void 0,(fn=exports.DocumentType||(exports.DocumentType={}))[fn.Query=0]="Query",fn[fn.Mutation=1]="Mutation",fn[fn.Subscription=2]="Subscription",yn.resetCache=function(){hn=void 0};var bn,_n=I?p.useLayoutEffect:p.useEffect;function kn(){}var wn=p.use||function(e){var t=je(e);switch(t.status){case"pending":throw t;case"rejected":throw t.reason;case"fulfilled":return t.value}},Sn=Symbol.for("apollo.hook.wrappers");function On(e,t,r){var n=r.queryManager,i=n&&n[Sn],o=i&&i[e];return o?o(t):t}var xn=Object.prototype.hasOwnProperty;function qn(){}var Rn=Symbol();function En(t,r){var n=Pn(t,r),i=n.result,o=n.obsQueryFields;return p.useMemo((function(){return e.__assign(e.__assign({},i),o)}),[i,o])}function Pn(t,n){var i=ln(n.client),o=p.useContext(un()).renderPromises,a=!!o,s=i.disableNetworkFetches,u=!1!==n.ssr&&!n.skip,c=n.partialRefetch,l=Tn(i,t,n,a),f=function(t,r,n,i,o){function a(e){var a;return mn(r,exports.DocumentType.Query),{client:t,query:r,observable:i&&i.getSSRObservable(o())||Hr.inactiveOnCreation.withValue(!i,(function(){return t.watchQuery(Cn(void 0,t,n,o()))})),resultData:{previousData:null===(a=null==e?void 0:e.resultData.current)||void 0===a?void 0:a.data}}}var s=p.useState(a),u=s[0],c=s[1];function l(t){var r,n;Object.assign(u.observable,((r={})[Rn]=t,r));var i=u.resultData;c(e.__assign(e.__assign({},u),{query:t.query,resultData:Object.assign(i,{previousData:(null===(n=i.current)||void 0===n?void 0:n.data)||i.previousData,current:void 0})}))}if(t!==u.client||r!==u.query){var f=a(u);return c(f),[f,l]}return[u,l]}(i,t,n,o,l),h=f[0],d=h.observable,v=h.resultData,y=f[1],m=l(d);!function(e,t,n,i,o){var a;t[Rn]&&!r.equal(t[Rn],o)&&(t.reobserve(Cn(t,n,i,o)),e.previousData=(null===(a=e.current)||void 0===a?void 0:a.data)||e.previousData,e.current=void 0);t[Rn]=o}(v,d,i,n,m);var g=p.useMemo((function(){return function(e){return{refetch:e.refetch.bind(e),reobserve:e.reobserve.bind(e),fetchMore:e.fetchMore.bind(e),updateQuery:e.updateQuery.bind(e),startPolling:e.startPolling.bind(e),stopPolling:e.stopPolling.bind(e),subscribeToMore:e.subscribeToMore.bind(e)}}(d)}),[d]);!function(e,t,r){t&&r&&(t.registerSSRObservable(e),e.getCurrentResult().loading&&t.addObservableQueryPromise(e))}(d,o,u);var b=function(e,t,n,i,o,a,s,u,c){var l=p.useRef(c);p.useEffect((function(){l.current=c}));var f=!u&&!a||!1!==i.ssr||i.skip?i.skip||"standby"===o.fetchPolicy?jn:void 0:Nn,h=e.previousData,d=p.useMemo((function(){return f&&In(f,h,t,n)}),[n,t,f,h]);return pn(p.useCallback((function(i){if(u)return function(){};var o=function(){var o=e.current,a=t.getCurrentResult();o&&o.loading===a.loading&&o.networkStatus===a.networkStatus&&r.equal(o.data,a.data)||Dn(a,e,t,n,s,i,l.current)},a=function(u){if(c.current.unsubscribe(),c.current=t.resubscribeAfterError(o,a),!xn.call(u,"graphQLErrors"))throw u;var f=e.current;(!f||f&&f.loading||!r.equal(u,f.error))&&Dn({data:f&&f.data,error:u,loading:!1,networkStatus:exports.NetworkStatus.error},e,t,n,s,i,l.current)},c={current:t.subscribe(o,a)};return function(){setTimeout((function(){return c.current.unsubscribe()}))}}),[a,u,t,e,s,n]),(function(){return d||Fn(e,t,l.current,s,n)}),(function(){return d||Fn(e,t,l.current,s,n)}))}(v,d,i,n,m,s,c,a,{onCompleted:n.onCompleted||qn,onError:n.onError||qn});return{result:b,obsQueryFields:g,observable:d,resultData:v,client:i,onQueryExecuted:y}}function Tn(t,r,n,i){void 0===n&&(n={});var o=n.skip;n.ssr,n.onCompleted,n.onError;var a=n.defaultOptions,s=e.__rest(n,["skip","ssr","onCompleted","onError","defaultOptions"]);return function(e){var n=Object.assign(s,{query:r});return!i||"network-only"!==n.fetchPolicy&&"cache-and-network"!==n.fetchPolicy||(n.fetchPolicy="cache-first"),n.variables||(n.variables={}),o?(n.initialFetchPolicy=n.initialFetchPolicy||n.fetchPolicy||Mn(a,t.defaultOptions),n.fetchPolicy="standby"):n.fetchPolicy||(n.fetchPolicy=(null==e?void 0:e.options.initialFetchPolicy)||Mn(a,t.defaultOptions)),n}}function Cn(e,t,r,n){var i=[],o=t.defaultOptions.watchQuery;return o&&i.push(o),r.defaultOptions&&i.push(r.defaultOptions),i.push($e(e&&e.options,n)),i.reduce(Ze)}function Dn(t,r,n,i,o,a,s){var u=r.current;u&&u.data&&(r.previousData=u.data),!t.error&&we(t.errors)&&(t.error=new yt({graphQLErrors:t.errors})),r.current=In(function(t,r,n){if(t.partial&&n&&!t.loading&&(!t.data||0===Object.keys(t.data).length)&&"cache-only"!==r.options.fetchPolicy)return r.refetch(),e.__assign(e.__assign({},t),{loading:!0,networkStatus:exports.NetworkStatus.refetch});return t}(t,n,o),r.previousData,n,i),a(),function(e,t,r){if(!e.loading){var n=Qn(e);Promise.resolve().then((function(){n?r.onError(n):e.data&&t!==e.networkStatus&&e.networkStatus===exports.NetworkStatus.ready&&r.onCompleted(e.data)})).catch((function(e){}))}}(t,null==u?void 0:u.networkStatus,s)}function Fn(e,t,r,n,i){return e.current||Dn(t.getCurrentResult(),e,t,i,n,(function(){}),r),e.current}function Mn(e,t){var r;return(null==e?void 0:e.fetchPolicy)||(null===(r=null==t?void 0:t.watchQuery)||void 0===r?void 0:r.fetchPolicy)||"cache-first"}function Qn(e){return we(e.errors)?new yt({graphQLErrors:e.errors}):e.error}function In(t,r,n,i){var o=t.data;t.partial;var a=e.__rest(t,["data","partial"]);return e.__assign(e.__assign({data:o},a),{client:i,observable:n,variables:n.variables,called:t!==Nn&&t!==jn,previousData:r})}var Nn={loading:!0,data:void 0,error:void 0,networkStatus:exports.NetworkStatus.loading},jn={loading:!1,data:void 0,error:void 0,networkStatus:exports.NetworkStatus.ready};var An=["refetch","reobserve","fetchMore","updateQuery","startPolling","stopPolling","subscribeToMore"];function Ln(t){var r=ln(t.client),n=r.cache,i=t.from,o=e.__rest(t,["from"]),a=p.useMemo((function(){return"string"==typeof i?i:null===i?null:n.identify(i)}),[n,i]),s=gn((function(){return e.__assign(e.__assign({},o),{from:a})}),[o,a]),u=p.useMemo((function(){var t=s.fragment,n=s.fragmentName,i=s.from,o=s.optimistic,a=void 0===o||o;if(null===i)return{result:Vn({result:{},complete:!1})};var u=r.cache,c=u.diff(e.__assign(e.__assign({},s),{returnPartialData:!0,id:i,query:u.getFragmentDoc(t,n),optimistic:a}));return{result:Vn(e.__assign(e.__assign({},c),{result:r.queryManager.maskFragment({fragment:t,fragmentName:n,data:c.result})}))}}),[r,s]),c=p.useCallback((function(){return u.result}),[u]);return pn(p.useCallback((function(e){var t=0,n=null===s.from?null:r.watchFragment(s).subscribe({next:function(r){h(r,u.result)||(u.result=r,clearTimeout(t),t=setTimeout(e))}});return function(){null==n||n.unsubscribe(),clearTimeout(t)}}),[r,s,u]),c,c)}function Vn(e){var t={data:e.result,complete:!!e.complete};return e.missing&&(t.missing=Me(e.missing.map((function(e){return e.missing})))),t}var Wn=Symbol.for("apollo.internal.queryRef"),zn=Symbol.for("apollo.internal.refPromise");function Bn(e){var t,r=((t={toPromise:function(){return Un(r).then((function(){return r}))}})[Wn]=e,t[zn]=e.promise,t);return r}function Kn(e){k(!e||Wn in e,69)}function Un(e){var t=Jn(e);return"fulfilled"===t.promise.status?t.promise:e[zn]}function Jn(e){return e[Wn]}function Hn(e,t){e[zn]=t}var Gn=["canonizeResults","context","errorPolicy","fetchPolicy","refetchWritePolicy","returnPartialData"],Yn=function(){function t(e,t){var r=this;this.key={},this.listeners=new Set,this.references=0,this.softReferences=0,this.handleNext=this.handleNext.bind(this),this.handleError=this.handleError.bind(this),this.dispose=this.dispose.bind(this),this.observable=e,t.onDispose&&(this.onDispose=t.onDispose),this.setResult(),this.subscribeToQuery();var n=function(){var e;r.references||(r.autoDisposeTimeoutId=setTimeout(r.dispose,null!==(e=t.autoDisposeTimeoutMs)&&void 0!==e?e:3e4))};this.promise.then(n,n)}return Object.defineProperty(t.prototype,"disposed",{get:function(){return this.subscription.closed},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"watchQueryOptions",{get:function(){return this.observable.options},enumerable:!1,configurable:!0}),t.prototype.reinitialize=function(){var e=this.observable,t=this.watchQueryOptions.fetchPolicy,r="no-cache"===t||"standby"===t;try{if(r?e.silentSetOptions({fetchPolicy:"standby"}):(e.resetLastResults(),e.silentSetOptions({fetchPolicy:"cache-first"})),this.subscribeToQuery(),r)return;e.resetDiff(),this.setResult()}finally{e.silentSetOptions({fetchPolicy:t})}},t.prototype.retain=function(){var e=this;this.references++,clearTimeout(this.autoDisposeTimeoutId);var t=!1;return function(){t||(t=!0,e.references--,setTimeout((function(){e.references||e.dispose()})))}},t.prototype.softRetain=function(){var e=this;this.softReferences++;var t=!1;return function(){t||(t=!0,e.softReferences--,setTimeout((function(){e.softReferences||e.references||e.dispose()})))}},t.prototype.didChangeOptions=function(e){var t=this;return Gn.some((function(n){return n in e&&!r.equal(t.watchQueryOptions[n],e[n])}))},t.prototype.applyOptions=function(t){var r=this.watchQueryOptions,n=r.fetchPolicy,i=r.canonizeResults;return"standby"===n&&n!==t.fetchPolicy?this.initiateFetch(this.observable.reobserve(t)):(this.observable.silentSetOptions(t),i!==t.canonizeResults&&(this.result=e.__assign(e.__assign({},this.result),this.observable.getCurrentResult()),this.promise=Ne(this.result))),this.promise},t.prototype.listen=function(e){var t=this;return this.listeners.add(e),function(){t.listeners.delete(e)}},t.prototype.refetch=function(e){return this.initiateFetch(this.observable.refetch(e))},t.prototype.fetchMore=function(e){return this.initiateFetch(this.observable.fetchMore(e))},t.prototype.dispose=function(){this.subscription.unsubscribe(),this.onDispose()},t.prototype.onDispose=function(){},t.prototype.handleNext=function(e){var t;if("pending"===this.promise.status)void 0===e.data&&(e.data=this.result.data),this.result=e,null===(t=this.resolve)||void 0===t||t.call(this,e);else{if(e.data===this.result.data&&e.networkStatus===this.result.networkStatus)return;void 0===e.data&&(e.data=this.result.data),this.result=e,this.promise=Ne(e),this.deliver(this.promise)}},t.prototype.handleError=function(e){var t,r,n;if(this.subscription.unsubscribe(),this.subscription=this.observable.resubscribeAfterError(this.handleNext,this.handleError),"pending"===this.promise.status)null===(t=this.reject)||void 0===t||t.call(this,e);else this.promise=(r=e,(n=Promise.reject(r)).catch((function(){})),n.status="rejected",n.reason=r,n),this.deliver(this.promise)},t.prototype.deliver=function(e){this.listeners.forEach((function(t){return t(e)}))},t.prototype.initiateFetch=function(e){var t=this;return this.promise=this.createPendingPromise(),this.promise.catch((function(){})),e.then((function(){setTimeout((function(){var e;"pending"===t.promise.status&&(t.result=t.observable.getCurrentResult(),null===(e=t.resolve)||void 0===e||e.call(t,t.result))}))})).catch((function(e){var r;return null===(r=t.reject)||void 0===r?void 0:r.call(t,e)})),e},t.prototype.subscribeToQuery=function(){var e=this;this.subscription=this.observable.filter((function(t){return!r.equal(t.data,{})&&!r.equal(t,e.result)})).subscribe(this.handleNext,this.handleError)},t.prototype.setResult=function(){var e=this.observable.getCurrentResult(!1);r.equal(e,this.result)||(this.result=e,this.promise=!e.data||e.partial&&!this.watchQueryOptions.returnPartialData?this.createPendingPromise():Ne(e))},t.prototype.createPendingPromise=function(){var e=this;return je(new Promise((function(t,r){e.resolve=t,e.reject=r})))},t}(),Xn=function(){function t(e,t,r){var n=this;this.key={},this.listeners=new Set,this.references=0,this.dispose=this.dispose.bind(this),this.handleNext=this.handleNext.bind(this),this.handleError=this.handleError.bind(this),this.observable=e.watchFragment(t),r.onDispose&&(this.onDispose=r.onDispose);var i=this.getDiff(e,t),o=function(){var e;n.references||(n.autoDisposeTimeoutId=setTimeout(n.dispose,null!==(e=r.autoDisposeTimeoutMs)&&void 0!==e?e:3e4))};this.promise=i.complete?Ne(i.result):this.createPendingPromise(),this.subscribeToFragment(),this.promise.then(o,o)}return t.prototype.listen=function(e){var t=this;return this.listeners.add(e),function(){t.listeners.delete(e)}},t.prototype.retain=function(){var e=this;this.references++,clearTimeout(this.autoDisposeTimeoutId);var t=!1;return function(){t||(t=!0,e.references--,setTimeout((function(){e.references||e.dispose()})))}},t.prototype.dispose=function(){this.subscription.unsubscribe(),this.onDispose()},t.prototype.onDispose=function(){},t.prototype.subscribeToFragment=function(){this.subscription=this.observable.subscribe(this.handleNext.bind(this),this.handleError.bind(this))},t.prototype.handleNext=function(e){var t;switch(this.promise.status){case"pending":if(e.complete)return null===(t=this.resolve)||void 0===t?void 0:t.call(this,e.data);this.deliver(this.promise);break;case"fulfilled":if(r.equal(this.promise.value,e.data))return;this.promise=e.complete?Ne(e.data):this.createPendingPromise(),this.deliver(this.promise)}},t.prototype.handleError=function(e){var t;null===(t=this.reject)||void 0===t||t.call(this,e)},t.prototype.deliver=function(e){this.listeners.forEach((function(t){return t(e)}))},t.prototype.createPendingPromise=function(){var e=this;return je(new Promise((function(t,r){e.resolve=t,e.reject=r})))},t.prototype.getDiff=function(t,r){var n=t.cache,i=r.from,o=r.fragment,a=r.fragmentName,s=n.diff(e.__assign(e.__assign({},r),{query:n.getFragmentDoc(o,a),returnPartialData:!0,id:i,optimistic:!0}));return e.__assign(e.__assign({},s),{result:t.queryManager.maskFragment({fragment:o,fragmentName:a,data:s.result})})},t}(),$n=function(){function e(e){void 0===e&&(e=Object.create(null)),this.queryRefs=new s.Trie(D),this.fragmentRefs=new s.Trie(D),this.options=e}return e.prototype.getQueryRef=function(e,t){var r=this.queryRefs.lookupArray(e);return r.current||(r.current=new Yn(t(),{autoDisposeTimeoutMs:this.options.autoDisposeTimeoutMs,onDispose:function(){delete r.current}})),r.current},e.prototype.getFragmentRef=function(e,t,r){var n=this.fragmentRefs.lookupArray(e);return n.current||(n.current=new Xn(t,r,{autoDisposeTimeoutMs:this.options.autoDisposeTimeoutMs,onDispose:function(){delete n.current}})),n.current},e.prototype.add=function(e,t){this.queryRefs.lookupArray(e).current=t},e}(),Zn=Symbol.for("apollo.suspenseCache");function ei(e){var t;return e[Zn]||(e[Zn]=new $n(null===(t=e.defaultOptions.react)||void 0===t?void 0:t.suspense)),e[Zn]}var ti=Symbol.for("apollo.skipToken");function ri(t,r){var n=ln(r.client),i=ei(n),o=ii({client:n,query:t,options:r}),a=o.fetchPolicy,s=o.variables,u=r.queryKey,c=void 0===u?[]:u,l=e.__spreadArray([t,Y(s)],[].concat(c),!0),f=i.getQueryRef(l,(function(){return n.watchQuery(o)})),h=p.useState([f.key,f.promise]),d=h[0],v=h[1];d[0]!==f.key&&(d[0]=f.key,d[1]=f.promise);var y=d[1];f.didChangeOptions(o)&&(d[1]=y=f.applyOptions(o)),p.useEffect((function(){var e=f.retain(),t=f.listen((function(e){v([f.key,e])}));return function(){t(),e()}}),[f]);var m=p.useMemo((function(){var e=ni(f.result);return{loading:!1,data:f.result.data,networkStatus:e?exports.NetworkStatus.error:exports.NetworkStatus.ready,error:e}}),[f.result]),g="standby"===a?m:wn(y),b=p.useCallback((function(e){var t=f.fetchMore(e);return v([f.key,f.promise]),t}),[f]),_=p.useCallback((function(e){var t=f.refetch(e);return v([f.key,f.promise]),t}),[f]),k=f.observable.subscribeToMore;return p.useMemo((function(){return{client:n,data:g.data,error:ni(g),networkStatus:g.networkStatus,fetchMore:b,refetch:_,subscribeToMore:k}}),[n,b,_,g,k])}function ni(e){return we(e.errors)?new yt({graphQLErrors:e.errors}):e.error}function ii(t){var r=t.client,n=t.query,i=t.options;return gn((function(){var t;if(i===ti)return{query:n,fetchPolicy:"standby"};var o=i.fetchPolicy||(null===(t=r.defaultOptions.watchQuery)||void 0===t?void 0:t.fetchPolicy)||"cache-first",a=e.__assign(e.__assign({},i),{fetchPolicy:o,query:n,notifyOnNetworkStatusChange:!1,nextFetchPolicy:void 0});return i.skip&&(a.fetchPolicy="standby"),a}),[r,i,n])}function oi(t,r){var n=ln(r.client),i=ei(n),o=ii({client:n,query:t,options:r}),a=o.fetchPolicy,s=o.variables,u=r.queryKey,c=void 0===u?[]:u,l=p.useRef("standby"!==a);l.current||(l.current="standby"!==a);var f=e.__spreadArray([t,Y(s)],[].concat(c),!0),h=i.getQueryRef(f,(function(){return n.watchQuery(o)})),d=p.useState(Bn(h)),v=d[0],y=d[1];(Jn(v)!==h&&y(Bn(h)),h.didChangeOptions(o))&&Hn(v,h.applyOptions(o));p.useEffect((function(){var e=setTimeout((function(){h.disposed&&i.add(f,h)}));return function(){return clearTimeout(e)}}));var m=p.useCallback((function(e){var t=h.fetchMore(e);return y(Bn(h)),t}),[h]),g=p.useCallback((function(e){var t=h.refetch(e);return y(Bn(h)),t}),[h]);return p.useEffect((function(){return h.softRetain()}),[h]),[l.current?v:void 0,{fetchMore:m,refetch:g,subscribeToMore:h.observable.subscribeToMore}]}var ai=[];function si(t){var r=ln(t.client),n=t.from,i=t.variables,o=r.cache,a=p.useMemo((function(){return"string"==typeof n?n:null===n?null:o.identify(n)}),[o,n]),s=null===a?null:ei(r).getFragmentRef([a,t.fragment,Y(i)],r,e.__assign(e.__assign({},t),{variables:i,from:a})),u=p.useState(null===s?ai:[s.key,s.promise]),c=u[0],l=u[1];return p.useEffect((function(){if(null!==s){var e=s.retain(),t=s.listen((function(e){l([s.key,e])}));return function(){e(),t()}}}),[s]),null===s?{data:null}:(c[0]!==s.key&&(c[0]=s.key,c[1]=s.promise),{data:wn(c[1])})}function ui(e){Kn(e);var t=p.useState(e),r=t[0],n=t[1],i=p.useState(e),o=i[0],a=i[1],s=Jn(e);return r!==e?(n(e),a(e)):Hn(e,Un(o)),{refetch:p.useCallback((function(e){var t=s.refetch(e);return a(Bn(s)),t}),[s]),fetchMore:p.useCallback((function(e){var t=s.fetchMore(e);return a(Bn(s)),t}),[s]),subscribeToMore:s.observable.subscribeToMore}}function ci(e){Kn(e);var t=p.useMemo((function(){return Jn(e)}),[e]),r=p.useCallback((function(){return Un(e)}),[e]);t.disposed&&(t.reinitialize(),Hn(e,t.promise)),p.useEffect((function(){return t.retain()}),[t]);var n=pn(p.useCallback((function(r){return t.listen((function(t){Hn(e,t),r()}))}),[t,e]),r,r),i=wn(n);return p.useMemo((function(){return{data:i.data,networkStatus:i.networkStatus,error:ni(i)}}),[i])}var li=function(t){return function(r,n){var i,o;return void 0===n&&(n=Object.create(null)),Bn(new Yn(t.watchQuery(e.__assign(e.__assign({},n),{query:r})),{autoDisposeTimeoutMs:null===(o=null===(i=t.defaultOptions.react)||void 0===i?void 0:i.suspense)||void 0===o?void 0:o.autoDisposeTimeoutMs}))}};exports.setLogVerbosity=t.setVerbosity,exports.Observable=a.Observable,exports.disableExperimentalFragmentVariables=u.disableExperimentalFragmentVariables,exports.disableFragmentWarnings=u.disableFragmentWarnings,exports.enableExperimentalFragmentVariables=u.enableExperimentalFragmentVariables,exports.gql=u.gql,exports.resetCaches=u.resetCaches,exports.ApolloCache=Vt,exports.ApolloClient=an,exports.ApolloConsumer=function(e){var t=un();return p.createElement(t.Consumer,null,(function(t){return k(t&&t.client,53),e.children(t.client)}))},exports.ApolloError=yt,exports.ApolloLink=ot,exports.ApolloProvider=function(t){var r=t.client,n=t.children,i=un(),o=p.useContext(i),a=p.useMemo((function(){return e.__assign(e.__assign({},o),{client:r||o.client})}),[o,r]);return k(a.client,55),p.createElement(i.Provider,{value:a},n)},exports.DocumentTransform=be,exports.HttpLink=Tt,exports.InMemoryCache=Kr,exports.MissingFieldError=Wt,exports.ObservableQuery=Hr,exports.checkFetcher=xt,exports.concat=ct,exports.createHttpLink=Pt,exports.createQueryPreloader=function(e){return On("createQueryPreloader",li,e)(e)},exports.createSignalIfSupported=function(){if("undefined"==typeof AbortController)return{controller:!1,signal:!1};var e=new AbortController;return{controller:e,signal:e.signal}},exports.defaultDataIdFromObject=Kt,exports.defaultPrinter=St,exports.empty=at,exports.execute=lt,exports.fallbackHttpConfig=wt,exports.from=st,exports.fromError=et,exports.fromPromise=function(e){return new a.Observable((function(t){e.then((function(e){t.next(e),t.complete()})).catch(t.error.bind(t))}))},exports.getApolloContext=un,exports.isApolloError=vt,exports.isNetworkRequestSettled=function(e){return 7===e||8===e},exports.isReference=ee,exports.makeReference=Z,exports.makeVar=gr,exports.mergeOptions=Ze,exports.operationName=vn,exports.parseAndCheckHttpResponse=_t,exports.parser=yn,exports.resetApolloContext=cn,exports.rewriteURIForGET=Rt,exports.selectHttpOptionsAndBody=function(t,r){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return n.unshift(r),Ot.apply(void 0,e.__spreadArray([t,St],n,!1))},exports.selectHttpOptionsAndBodyInternal=Ot,exports.selectURI=qt,exports.serializeFetchParameter=kt,exports.skipToken=ti,exports.split=ut,exports.throwServerError=tt,exports.toPromise=function(e){var t=!1;return new Promise((function(r,n){e.subscribe({next:function(e){t||(t=!0,r(e))},error:n})}))},exports.useApolloClient=ln,exports.useBackgroundQuery=function(e,t){return void 0===t&&(t=Object.create(null)),On("useBackgroundQuery",oi,ln("object"==typeof t?t.client:void 0))(e,t)},exports.useFragment=function(e){return On("useFragment",Ln,ln(e.client))(e)},exports.useLazyQuery=function(t,r){var n,i=p.useRef(void 0),o=p.useRef(void 0),a=p.useRef(void 0),s=Ze(r,i.current||{}),u=null!==(n=null==s?void 0:s.query)&&void 0!==n?n:t;o.current=r,a.current=u;var c=e.__assign(e.__assign({},s),{skip:!i.current}),l=Pn(u,c),f=l.obsQueryFields,h=l.result,d=l.client,v=l.resultData,y=l.observable,m=l.onQueryExecuted,g=y.options.initialFetchPolicy||Mn(c.defaultOptions,d.defaultOptions),b=p.useReducer((function(e){return e+1}),0)[1],_=p.useMemo((function(){for(var e={},t=function(t){var r=f[t];e[t]=function(){return i.current||(i.current=Object.create(null),b()),r.apply(this,arguments)}},r=0,n=An;r<n.length;r++){t(n[r])}return e}),[b,f]),k=!!i.current,w=p.useMemo((function(){return e.__assign(e.__assign(e.__assign({},h),_),{called:k})}),[h,_,k]),S=p.useCallback((function(t){i.current=t?e.__assign(e.__assign({},t),{fetchPolicy:t.fetchPolicy||g}):{fetchPolicy:g};var r=Ze(o.current,e.__assign({query:a.current},i.current)),n=function(e,t,r,n,i,o){var a=i.query||n,s=Tn(r,a,i,!1)(t),u=t.reobserveAsConcast(Cn(t,r,i,s));return o(s),new Promise((function(n){var i;u.subscribe({next:function(e){i=e},error:function(){n(In(t.getCurrentResult(),e.previousData,t,r))},complete:function(){n(In(t.maskResult(i),e.previousData,t,r))}})}))}(v,y,d,u,e.__assign(e.__assign({},r),{skip:!1}),m).then((function(e){return Object.assign(e,_)}));return n.catch((function(){})),n}),[d,u,_,g,y,v,m]),O=p.useRef(S);return _n((function(){O.current=S})),[p.useCallback((function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return O.current.apply(O,e)}),[]),w]},exports.useLoadableQuery=function(t,r){void 0===r&&(r=Object.create(null));var n=ln(r.client),i=ei(n),o=ii({client:n,query:t,options:r}),a=r.queryKey,s=void 0===a?[]:a,u=p.useState(null),c=u[0],l=u[1];Kn(c);var f=c&&Jn(c);c&&(null==f?void 0:f.didChangeOptions(o))&&Hn(c,f.applyOptions(o));var h=(bn||(bn=p.createContext(null)),p.useCallback((function(){var e=console.error;try{return console.error=kn,p.useContext(bn),!0}catch(e){return!1}finally{console.error=e}}),[])),d=p.useCallback((function(e){if(!f)throw new Error("The query has not been loaded. Please load the query.");var t=f.fetchMore(e);return l(Bn(f)),t}),[f]),v=p.useCallback((function(e){if(!f)throw new Error("The query has not been loaded. Please load the query.");var t=f.refetch(e);return l(Bn(f)),t}),[f]),y=p.useCallback((function(){for(var r=[],a=0;a<arguments.length;a++)r[a]=arguments[a];k(!h(),59);var u=r[0],c=e.__spreadArray([t,Y(u)],[].concat(s),!0),f=i.getQueryRef(c,(function(){return n.watchQuery(e.__assign(e.__assign({},o),{variables:u}))}));l(Bn(f))}),[t,s,i,o,h,n]),m=p.useCallback((function(e){return k(f,60),f.observable.subscribeToMore(e)}),[f]);return[y,c,{fetchMore:d,refetch:v,reset:p.useCallback((function(){l(null)}),[]),subscribeToMore:m}]},exports.useMutation=function(t,n){var i=ln(null==n?void 0:n.client);mn(t,exports.DocumentType.Mutation);var o=p.useState({called:!1,loading:!1,client:i}),a=o[0],s=o[1],u=p.useRef({result:a,mutationId:0,isMounted:!0,client:i,mutation:t,options:n});_n((function(){Object.assign(u.current,{client:i,options:n,mutation:t})}));var c=p.useCallback((function(t){void 0===t&&(t={});var n=u.current,i=n.options,o=n.mutation,a=e.__assign(e.__assign({},i),{mutation:o}),c=t.client||u.current.client;u.current.result.loading||a.ignoreResults||!u.current.isMounted||s(u.current.result={loading:!0,error:void 0,data:void 0,called:!0,client:c});var l=++u.current.mutationId,f=Ze(a,t);return c.mutate(f).then((function(e){var n,i,o=e.data,a=e.errors,h=a&&a.length>0?new yt({graphQLErrors:a}):void 0,p=t.onError||(null===(n=u.current.options)||void 0===n?void 0:n.onError);if(h&&p&&p(h,f),l===u.current.mutationId&&!f.ignoreResults){var d={called:!0,loading:!1,data:o,error:h,client:c};u.current.isMounted&&!r.equal(u.current.result,d)&&s(u.current.result=d)}var v=t.onCompleted||(null===(i=u.current.options)||void 0===i?void 0:i.onCompleted);return h||null==v||v(e.data,f),e}),(function(e){var n;if(l===u.current.mutationId&&u.current.isMounted){var i={loading:!1,error:e,data:void 0,called:!0,client:c};r.equal(u.current.result,i)||s(u.current.result=i)}var o=t.onError||(null===(n=u.current.options)||void 0===n?void 0:n.onError);if(o)return o(e,f),{data:void 0,errors:e};throw e}))}),[]),l=p.useCallback((function(){if(u.current.isMounted){var e={called:!1,loading:!1,client:u.current.client};Object.assign(u.current,{mutationId:0,result:e}),s(e)}}),[]);return p.useEffect((function(){var e=u.current;return e.isMounted=!0,function(){e.isMounted=!1}}),[]),[c,e.__assign({reset:l},a)]},exports.useQuery=function(e,t){return void 0===t&&(t=Object.create(null)),On("useQuery",En,ln(t&&t.client))(e,t)},exports.useQueryRefHandlers=function(e){var t=Jn(e);return On("useQueryRefHandlers",ui,ln(t?t.observable:void 0))(e)},exports.useReactiveVar=function(e){return pn(p.useCallback((function(t){return e.onNextChange((function r(){t(),e.onNextChange(r)}))}),[e]),e,e)},exports.useReadQuery=function(e){var t=Jn(e);return On("useReadQuery",ci,ln(t?t.observable:void 0))(e)},exports.useSubscription=function(t,n){void 0===n&&(n=Object.create(null));var i=p.useRef(!1),o=ln(n.client);mn(t,exports.DocumentType.Subscription),i.current||(i.current=!0,n.onSubscriptionData,n.onSubscriptionComplete);var s=n.skip,u=n.fetchPolicy,c=n.errorPolicy,l=n.shouldResubscribe,f=n.context,h=n.extensions,d=n.ignoreResults,v=gn((function(){return n.variables}),[n.variables]),y=function(){return function(t,r,n,i,o,s,u){var c={query:r,variables:n,fetchPolicy:i,errorPolicy:o,context:s,extensions:u},l=e.__assign(e.__assign({},c),{client:t,result:{loading:!0,data:void 0,error:void 0,variables:n},setResult:function(e){l.result=e}}),f=null;return Object.assign(new a.Observable((function(e){f||(f=t.subscribe(c));var r=f.subscribe(e);return function(){return r.unsubscribe()}})),{__:l})}(o,t,v,u,c,f,h)},m=p.useState(n.skip?null:y),g=m[0],b=m[1],_=p.useRef(y);_n((function(){_.current=y})),s?g&&b(g=null):g&&(o===g.__.client&&t===g.__.query&&u===g.__.fetchPolicy&&c===g.__.errorPolicy&&r.equal(v,g.__.variables)||!1===("function"==typeof l?!!l(n):l))||b(g=y());var w=p.useRef(n);p.useEffect((function(){w.current=n}));var S=!s&&!d,O=p.useMemo((function(){return{loading:S,error:void 0,data:void 0,variables:v}}),[S,v]),x=p.useRef(d);_n((function(){x.current=d}));var q=pn(p.useCallback((function(e){if(!g)return function(){};var t=!1,r=g.__.variables,n=g.__.client,i=g.subscribe({next:function(i){var o,a;if(!t){var s={loading:!1,data:i.data,error:Qn(i),variables:r};g.__.setResult(s),x.current||e(),s.error?null===(a=(o=w.current).onError)||void 0===a||a.call(o,s.error):w.current.onData?w.current.onData({client:n,data:s}):w.current.onSubscriptionData&&w.current.onSubscriptionData({client:n,subscriptionData:s})}},error:function(n){var i,o;n=n instanceof yt?n:new yt({protocolErrors:[n]}),t||(g.__.setResult({loading:!1,data:void 0,error:n,variables:r}),x.current||e(),null===(o=(i=w.current).onError)||void 0===o||o.call(i,n))},complete:function(){t||(w.current.onComplete?w.current.onComplete():w.current.onSubscriptionComplete&&w.current.onSubscriptionComplete())}});return function(){t=!0,setTimeout((function(){i.unsubscribe()}))}}),[g]),(function(){return!g||s||d?O:g.__.result}),(function(){return O})),R=p.useCallback((function(){k(!w.current.skip,65),b(_.current())}),[w,_]);return p.useMemo((function(){return e.__assign(e.__assign({},q),{restart:R})}),[q,R])},exports.useSuspenseFragment=function(e){return On("useSuspenseFragment",si,ln("object"==typeof e?e.client:void 0))(e)},exports.useSuspenseQuery=function(e,t){return void 0===t&&(t=Object.create(null)),On("useSuspenseQuery",ri,ln("object"==typeof t?t.client:void 0))(e,t)};
