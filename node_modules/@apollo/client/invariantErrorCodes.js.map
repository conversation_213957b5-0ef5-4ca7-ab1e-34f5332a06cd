{"version": 3, "file": "invariantErrorCodes.js", "sourceRoot": "", "sources": ["../src/invariantErrorCodes.ts"], "names": [], "mappings": "AAIA,MAAM,CAAC,IAAM,UAAU,GAAe,EAAE,CAAC;AACzC,MAAM,CAAC,IAAM,QAAQ,GAAe,EAAE,CAAC;AACvC,MAAM,CAAC,IAAM,MAAM,GAAe,EAAE,CAAC;AACrC,MAAM,CAAC,IAAM,OAAO,GAAe,EAAE,CAAC;AACtC,MAAM,CAAC,IAAM,QAAQ,GAAe,EAAE,CAAC", "sourcesContent": ["export interface ErrorCodes {\n  [key: number]: { file: string; condition?: string; message?: string };\n}\n\nexport const errorCodes: ErrorCodes = {};\nexport const devDebug: ErrorCodes = {};\nexport const devLog: ErrorCodes = {};\nexport const devWarn: ErrorCodes = {};\nexport const devError: ErrorCodes = {};\n"]}