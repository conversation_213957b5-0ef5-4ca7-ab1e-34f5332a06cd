{"version": 3, "file": "decoration.js", "sourceRoot": "", "sources": ["../../../src/utilities/promises/decoration.ts"], "names": [], "mappings": "AAmBA,MAAM,UAAU,sBAAsB,CAAS,KAAa;IAC1D,IAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAA6B,CAAC;IAEnE,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;IAC7B,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;IAEtB,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,qBAAqB,CAAmB,MAAe;IACrE,IAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAA4B,CAAC;IAElE,kEAAkE;IAClE,OAAO,CAAC,KAAK,CAAC,cAAO,CAAC,CAAC,CAAC;IAExB,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC;IAC5B,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;IAExB,OAAO,OAAO,CAAC;AACjB,CAAC;AAED,MAAM,UAAU,iBAAiB,CAC/B,OAAwB;IAExB,OAAO,QAAQ,IAAI,OAAO,CAAC;AAC7B,CAAC;AAED,MAAM,UAAU,oBAAoB,CAClC,OAAwB;IAExB,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE,CAAC;QAC/B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,IAAM,cAAc,GAAG,OAAiC,CAAC;IACzD,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC;IAElC,cAAc,CAAC,IAAI,CACjB,UAAC,KAAK;QACJ,IAAI,cAAc,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACxC,IAAM,gBAAgB,GACpB,cAAqD,CAAC;YAExD,gBAAgB,CAAC,MAAM,GAAG,WAAW,CAAC;YACtC,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;QACjC,CAAC;IACH,CAAC,EACD,UAAC,MAAe;QACd,IAAI,cAAc,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YACxC,IAAM,eAAe,GACnB,cAAoD,CAAC;YAEvD,eAAe,CAAC,MAAM,GAAG,UAAU,CAAC;YACpC,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;QAClC,CAAC;IACH,CAAC,CACF,CAAC;IAEF,OAAO,OAAmC,CAAC;AAC7C,CAAC", "sourcesContent": ["export interface PendingPromise<TValue> extends Promise<TValue> {\n  status: \"pending\";\n}\n\nexport interface FulfilledPromise<TValue> extends Promise<TValue> {\n  status: \"fulfilled\";\n  value: TValue;\n}\n\nexport interface RejectedPromise<TValue> extends Promise<TValue> {\n  status: \"rejected\";\n  reason: unknown;\n}\n\nexport type PromiseWithState<TValue> =\n  | PendingPromise<TValue>\n  | FulfilledPromise<TValue>\n  | RejectedPromise<TValue>;\n\nexport function createFulfilledPromise<TValue>(value: TValue) {\n  const promise = Promise.resolve(value) as FulfilledPromise<TValue>;\n\n  promise.status = \"fulfilled\";\n  promise.value = value;\n\n  return promise;\n}\n\nexport function createRejectedPromise<TValue = unknown>(reason: unknown) {\n  const promise = Promise.reject(reason) as RejectedPromise<TValue>;\n\n  // prevent potential edge cases leaking unhandled error rejections\n  promise.catch(() => {});\n\n  promise.status = \"rejected\";\n  promise.reason = reason;\n\n  return promise;\n}\n\nexport function isStatefulPromise<TValue>(\n  promise: Promise<TValue>\n): promise is PromiseWithState<TValue> {\n  return \"status\" in promise;\n}\n\nexport function wrapPromiseWithState<TValue>(\n  promise: Promise<TValue>\n): PromiseWithState<TValue> {\n  if (isStatefulPromise(promise)) {\n    return promise;\n  }\n\n  const pendingPromise = promise as PendingPromise<TValue>;\n  pendingPromise.status = \"pending\";\n\n  pendingPromise.then(\n    (value) => {\n      if (pendingPromise.status === \"pending\") {\n        const fulfilledPromise =\n          pendingPromise as unknown as FulfilledPromise<TValue>;\n\n        fulfilledPromise.status = \"fulfilled\";\n        fulfilledPromise.value = value;\n      }\n    },\n    (reason: unknown) => {\n      if (pendingPromise.status === \"pending\") {\n        const rejectedPromise =\n          pendingPromise as unknown as RejectedPromise<TValue>;\n\n        rejectedPromise.status = \"rejected\";\n        rejectedPromise.reason = reason;\n      }\n    }\n  );\n\n  return promise as PromiseWithState<TValue>;\n}\n"]}