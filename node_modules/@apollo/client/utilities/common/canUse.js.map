{"version": 3, "file": "canUse.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/canUse.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AAE5C,IAAM,aAAa,GAAG,KAAK,CAAC,cAAM,OAAA,SAAS,CAAC,OAAO,EAAjB,CAAiB,CAAC,IAAI,aAAa,CAAC;AAEtE,MAAM,CAAC,IAAM,aAAa,GACxB,OAAO,OAAO,KAAK,UAAU;IAC7B,CAAC,CAAC,aAAa,IAAI,CAAE,MAAc,CAAC,cAAc,CAAC,CAAC;AAEtD,MAAM,CAAC,IAAM,aAAa,GAAG,OAAO,OAAO,KAAK,UAAU,CAAC;AAE3D,MAAM,CAAC,IAAM,YAAY,GACvB,OAAO,MAAM,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,UAAU,CAAC;AAEnE,MAAM,CAAC,IAAM,yBAAyB,GAAG,YAAY,IAAI,MAAM,CAAC,aAAa,CAAC;AAE9E,MAAM,CAAC,IAAM,SAAS,GACpB,OAAO,KAAK,CAAC,cAAM,OAAA,MAAM,CAAC,QAAQ,CAAC,aAAa,EAA7B,CAA6B,CAAC,KAAK,UAAU,CAAC;AAEnE,IAAM,UAAU;AACd,8EAA8E;AAC9E,oEAAoE;AACpE,EAAE;AACF,0EAA0E;AAC1E,6EAA6E;AAC7E,6EAA6E;AAC7E,2EAA2E;AAC3E,+DAA+D;AAC/D,KAAK,CAAC,cAAM,OAAA,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAzC,CAAyC,CAAC,IAAI,KAAK,CAAC;AAElE,sEAAsE;AACtE,+EAA+E;AAC/E,wEAAwE;AACxE,0EAA0E;AAC1E,gFAAgF;AAChF,iEAAiE;AACjE,MAAM,CAAC,IAAM,kBAAkB,GAAG,CAAC,SAAS,IAAI,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC", "sourcesContent": ["import { maybe } from \"../globals/index.js\";\n\nconst isReactNative = maybe(() => navigator.product) == \"ReactNative\";\n\nexport const canUseWeakMap =\n  typeof WeakMap === \"function\" &&\n  !(isReactNative && !(global as any).HermesInternal);\n\nexport const canUseWeakSet = typeof WeakSet === \"function\";\n\nexport const canUseSymbol =\n  typeof Symbol === \"function\" && typeof Symbol.for === \"function\";\n\nexport const canUseAsyncIteratorSymbol = canUseSymbol && Symbol.asyncIterator;\n\nexport const canUseDOM =\n  typeof maybe(() => window.document.createElement) === \"function\";\n\nconst usingJSDOM: boolean =\n  // Following advice found in this comment from @domenic (maintainer of jsdom):\n  // https://github.com/jsdom/jsdom/issues/1537#issuecomment-229405327\n  //\n  // Since we control the version of Jest and jsdom used when running Apollo\n  // Client tests, and that version is recent enought to include \" jsdom/x.y.z\"\n  // at the end of the user agent string, I believe this case is all we need to\n  // check. Testing for \"Node.js\" was recommended for backwards compatibility\n  // with older version of jsdom, but we don't have that problem.\n  maybe(() => navigator.userAgent.indexOf(\"jsdom\") >= 0) || false;\n\n// Our tests should all continue to pass if we remove this !usingJSDOM\n// condition, thereby allowing useLayoutEffect when using jsdom. Unfortunately,\n// if we allow useLayoutEffect, then useSyncExternalStore generates many\n// warnings about useLayoutEffect doing nothing on the server. While these\n// warnings are harmless, this !usingJSDOM condition seems to be the best way to\n// prevent them (i.e. skipping useLayoutEffect when using jsdom).\nexport const canUseLayoutEffect = (canUseDOM || isReactNative) && !usingJSDOM;\n"]}