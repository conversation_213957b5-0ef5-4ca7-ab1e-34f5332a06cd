{"version": 3, "file": "stripTypename.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/stripTypename.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAEzC,MAAM,UAAU,aAAa,CAAI,KAAQ;IACvC,OAAO,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACvC,CAAC", "sourcesContent": ["import { omitDeep } from \"./omitDeep.js\";\n\nexport function stripTypename<T>(value: T) {\n  return omitDeep(value, \"__typename\");\n}\n"]}