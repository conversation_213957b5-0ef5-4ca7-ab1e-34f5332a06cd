{"version": 3, "file": "makeUniqueId.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/makeUniqueId.ts"], "names": [], "mappings": "AAAA,IAAM,YAAY,GAAG,IAAI,GAAG,EAAkB,CAAC;AAE/C,0EAA0E;AAC1E,+EAA+E;AAC/E,MAAM,UAAU,YAAY,CAAC,MAAc;IACzC,IAAM,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC5C,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;IACpC,OAAO,UAAG,MAAM,cAAI,KAAK,cAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAE,CAAC;AACrE,CAAC", "sourcesContent": ["const prefixCounts = new Map<string, number>();\n\n// These IDs won't be globally unique, but they will be unique within this\n// process, thanks to the counter, and unguessable thanks to the random suffix.\nexport function makeUniqueId(prefix: string) {\n  const count = prefixCounts.get(prefix) || 1;\n  prefixCounts.set(prefix, count + 1);\n  return `${prefix}:${count}:${Math.random().toString(36).slice(2)}`;\n}\n"]}