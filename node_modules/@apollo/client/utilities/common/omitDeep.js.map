{"version": 3, "file": "omitDeep.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/omitDeep.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAE7C,MAAM,UAAU,QAAQ,CAAsB,KAAQ,EAAE,GAAM;IAC5D,OAAO,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAChC,CAAC;AAED,SAAS,UAAU,CACjB,KAAQ,EACR,GAAM,EACN,KAA2B;IAA3B,sBAAA,EAAA,YAAY,GAAG,EAAY;IAE3B,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QACrB,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAED,IAAI,QAAQ,GAAG,KAAK,CAAC;IAErB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QACzB,IAAM,OAAK,GAAU,EAAE,CAAC;QACxB,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,OAAK,CAAC,CAAC;QAExB,KAAK,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,KAAK;YACzB,IAAM,MAAM,GAAG,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC7C,QAAQ,KAAR,QAAQ,GAAK,MAAM,KAAK,KAAK,EAAC;YAE9B,OAAK,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,OAAuB,CAAC;QACjC,CAAC;IACH,CAAC;SAAM,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;QAChC,IAAM,KAAG,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;QACxD,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAG,CAAC,CAAC;QAEtB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAC,CAAC;YAC3B,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;gBACd,QAAQ,GAAG,IAAI,CAAC;gBAChB,OAAO;YACT,CAAC;YAED,IAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAChD,QAAQ,KAAR,QAAQ,GAAK,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,EAAC;YAEjC,KAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;QAClB,CAAC,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO,KAAG,CAAC;QACb,CAAC;IACH,CAAC;IAED,OAAO,KAAuB,CAAC;AACjC,CAAC", "sourcesContent": ["import type { DeepOmit } from \"../types/DeepOmit.js\";\nimport { isPlainObject } from \"./objects.js\";\n\nexport function omitDeep<T, K extends string>(value: T, key: K) {\n  return __omitDeep(value, key);\n}\n\nfunction __omitDeep<T, K extends string>(\n  value: T,\n  key: K,\n  known = new Map<any, any>()\n): DeepOmit<T, K> {\n  if (known.has(value)) {\n    return known.get(value);\n  }\n\n  let modified = false;\n\n  if (Array.isArray(value)) {\n    const array: any[] = [];\n    known.set(value, array);\n\n    value.forEach((value, index) => {\n      const result = __omitDeep(value, key, known);\n      modified ||= result !== value;\n\n      array[index] = result;\n    });\n\n    if (modified) {\n      return array as DeepOmit<T, K>;\n    }\n  } else if (isPlainObject(value)) {\n    const obj = Object.create(Object.getPrototypeOf(value));\n    known.set(value, obj);\n\n    Object.keys(value).forEach((k) => {\n      if (k === key) {\n        modified = true;\n        return;\n      }\n\n      const result = __omitDeep(value[k], key, known);\n      modified ||= result !== value[k];\n\n      obj[k] = result;\n    });\n\n    if (modified) {\n      return obj;\n    }\n  }\n\n  return value as DeepOmit<T, K>;\n}\n"]}