{"version": 3, "file": "mergeOptions.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/mergeOptions.ts"], "names": [], "mappings": ";AAOA,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAOvC,MAAM,UAAU,YAAY,CAI1B,QAAgE,EAChE,OAAqC;IAErC,OAAO,OAAO,CACZ,QAAQ,EACR,OAAO,EACP,OAAO,CAAC,SAAS,IAAI;QACnB,SAAS,EAAE,OAAO,uBACb,CAAC,QAAQ,IAAI,QAAQ,CAAC,SAAS,CAAC,GAChC,OAAO,CAAC,SAAS,EACpB;KACH,CACF,CAAC;AACJ,CAAC", "sourcesContent": ["import type {\n  QueryOptions,\n  WatchQueryOptions,\n  MutationOptions,\n  OperationVariables,\n} from \"../../core/index.js\";\n\nimport { compact } from \"./compact.js\";\n\ntype OptionsUnion<TData, TVariables extends OperationVariables, TContext> =\n  | WatchQueryOptions<TVariables, TData>\n  | QueryOptions<TVariables, TData>\n  | MutationOptions<TData, TVariables, TContext, any>;\n\nexport function mergeOptions<\n  TDefaultOptions extends Partial<OptionsUnion<any, any, any>>,\n  TOptions extends TDefaultOptions,\n>(\n  defaults: TDefaultOptions | Partial<TDefaultOptions> | undefined,\n  options: TOptions | Partial<TOptions>\n): TOptions & TDefaultOptions {\n  return compact(\n    defaults,\n    options,\n    options.variables && {\n      variables: compact({\n        ...(defaults && defaults.variables),\n        ...options.variables,\n      }),\n    }\n  );\n}\n"]}