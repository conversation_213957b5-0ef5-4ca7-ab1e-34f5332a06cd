{"version": 3, "file": "errorHandling.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/errorHandling.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAC9C,OAAO,EAAE,iCAAiC,EAAE,MAAM,wBAAwB,CAAC;AAE3E,MAAM,UAAU,qBAAqB,CAAI,MAAsB;IAC7D,IAAM,MAAM,GAAG,0BAA0B,CAAC,MAAM,CAAC,CAAC;IAClD,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC;AAED,MAAM,UAAU,0BAA0B,CAAI,MAAsB;IAClE,IAAM,aAAa,GACjB,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAE/D,IACE,iCAAiC,CAAC,MAAM,CAAC;QACzC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,EACnC,CAAC;QACD,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,iBAAiB;YAC3C,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;gBAC7B,aAAa,CAAC,IAAI,OAAlB,aAAa,EAAS,iBAAiB,CAAC,MAAM,EAAE;YAClD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IACD,OAAO,aAAa,CAAC;AACvB,CAAC", "sourcesContent": ["import type { FetchResult } from \"../../link/core/index.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { isExecutionPatchIncrementalResult } from \"./incrementalResult.js\";\n\nexport function graphQLResultHasError<T>(result: FetchResult<T>): boolean {\n  const errors = getGraphQLErrorsFromResult(result);\n  return isNonEmptyArray(errors);\n}\n\nexport function getGraphQLErrorsFromResult<T>(result: FetchResult<T>) {\n  const graphQLErrors =\n    isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n\n  if (\n    isExecutionPatchIncrementalResult(result) &&\n    isNonEmptyArray(result.incremental)\n  ) {\n    result.incremental.forEach((incrementalResult) => {\n      if (incrementalResult.errors) {\n        graphQLErrors.push(...incrementalResult.errors);\n      }\n    });\n  }\n  return graphQLErrors;\n}\n"]}