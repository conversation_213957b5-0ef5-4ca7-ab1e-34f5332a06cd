{"version": 3, "file": "maybeDeepFreeze.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/maybeDeepFreeze.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAE/C,MAAM,UAAU,UAAU,CAAC,KAAU;IACnC,IAAM,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;IACjC,OAAO,CAAC,OAAO,CAAC,UAAC,GAAG;QAClB,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE,CAAC;YACvD,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;gBAC3C,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;YACzD,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,aAAa,CAAmB,GAAM;IAC7C,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACrC,IAAI,CAAC;YACH,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACrB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,wEAAwE;YACxE,yEAAyE;YACzE,2DAA2D;YAC3D,IAAI,CAAC,YAAY,SAAS;gBAAE,OAAO,IAAI,CAAC;YACxC,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,MAAM,UAAU,eAAe,CAAI,GAAM;IACvC,IAAI,OAAO,EAAE,CAAC;QACZ,UAAU,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC", "sourcesContent": ["import { isNonNullObject } from \"./objects.js\";\n\nexport function deepFreeze(value: any) {\n  const workSet = new Set([value]);\n  workSet.forEach((obj) => {\n    if (isNonNullObject(obj) && shallowFreeze(obj) === obj) {\n      Object.getOwnPropertyNames(obj).forEach((name) => {\n        if (isNonNullObject(obj[name])) workSet.add(obj[name]);\n      });\n    }\n  });\n  return value;\n}\n\nfunction shallowFreeze<T extends object>(obj: T): T | null {\n  if (__DEV__ && !Object.isFrozen(obj)) {\n    try {\n      Object.freeze(obj);\n    } catch (e) {\n      // Some types like Uint8Array and Node.js's Buffer cannot be frozen, but\n      // they all throw a TypeError when you try, so we re-throw any exceptions\n      // that are not TypeErrors, since that would be unexpected.\n      if (e instanceof TypeError) return null;\n      throw e;\n    }\n  }\n  return obj;\n}\n\nexport function maybeDeepFreeze<T>(obj: T): T {\n  if (__DEV__) {\n    deepFreeze(obj);\n  }\n  return obj;\n}\n"]}