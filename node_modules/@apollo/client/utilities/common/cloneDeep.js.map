{"version": 3, "file": "cloneDeep.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/cloneDeep.ts"], "names": [], "mappings": "AAAQ,IAAA,QAAQ,GAAK,MAAM,CAAC,SAAS,SAArB,CAAsB;AAEtC;;GAEG;AACH,MAAM,UAAU,SAAS,CAAI,KAAQ;IACnC,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC;AAChC,CAAC;AAED,SAAS,eAAe,CAAI,GAAM,EAAE,IAAoB;IACtD,QAAQ,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QAC3B,KAAK,gBAAgB,CAAC,CAAC,CAAC;YACtB,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACxC,IAAM,MAAI,GAAe,GAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9C,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAI,CAAC,CAAC;YACpB,MAAI,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,CAAC;gBAC7B,MAAI,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YACH,OAAO,MAAI,CAAC;QACd,CAAC;QAED,KAAK,iBAAiB,CAAC,CAAC,CAAC;YACvB,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;gBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACxC,yEAAyE;YACzE,sEAAsE;YACtE,IAAM,MAAI,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;YACvD,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAI,CAAC,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,GAA8B,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;gBACtD,MAAI,CAAC,GAAG,CAAC,GAAG,eAAe,CAAE,GAAW,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YACvD,CAAC,CAAC,CAAC;YACH,OAAO,MAAI,CAAC;QACd,CAAC;QAED;YACE,OAAO,GAAG,CAAC;IACf,CAAC;AACH,CAAC", "sourcesContent": ["const { toString } = Object.prototype;\n\n/**\n * Deeply clones a value to create a new instance.\n */\nexport function cloneDeep<T>(value: T): T {\n  return cloneDeepHelper(value);\n}\n\nfunction cloneDeepHelper<T>(val: T, seen?: Map<any, any>): T {\n  switch (toString.call(val)) {\n    case \"[object Array]\": {\n      seen = seen || new Map();\n      if (seen.has(val)) return seen.get(val);\n      const copy: T & any[] = (val as any).slice(0);\n      seen.set(val, copy);\n      copy.forEach(function (child, i) {\n        copy[i] = cloneDeepHelper(child, seen);\n      });\n      return copy;\n    }\n\n    case \"[object Object]\": {\n      seen = seen || new Map();\n      if (seen.has(val)) return seen.get(val);\n      // High fidelity polyfills of Object.create and Object.getPrototypeOf are\n      // possible in all JS environments, so we will assume they exist/work.\n      const copy = Object.create(Object.getPrototypeOf(val));\n      seen.set(val, copy);\n      Object.keys(val as T & Record<string, any>).forEach((key) => {\n        copy[key] = cloneDeepHelper((val as any)[key], seen);\n      });\n      return copy;\n    }\n\n    default:\n      return val;\n  }\n}\n"]}