{"version": 3, "file": "arrays.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/arrays.ts"], "names": [], "mappings": "AAAA,qEAAqE;AACrE,MAAM,CAAC,IAAM,OAAO,GAA4C,KAAK,CAAC,OAAO,CAAC;AAE9E,MAAM,UAAU,eAAe,CAAI,KAAoB;IACrD,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,CAAC", "sourcesContent": ["// A version of Array.isArray that works better with readonly arrays.\nexport const isArray: (a: any) => a is any[] | readonly any[] = Array.isArray;\n\nexport function isNonEmptyArray<T>(value?: ArrayLike<T>): value is Array<T> {\n  return Array.isArray(value) && value.length > 0;\n}\n"]}