{"version": 3, "file": "incrementalResult.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/incrementalResult.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,aAAa,CAAC;AAC9C,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAE5C,MAAM,UAAU,iCAAiC,CAC/C,KAAqB;IAErB,OAAO,aAAa,IAAI,KAAK,CAAC;AAChC,CAAC;AAED,MAAM,UAAU,6BAA6B,CAC3C,KAAqB;IAErB,OAAO,SAAS,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,CAAC;AAC/C,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,KAAqB;IAErB,OAAO,CACL,iCAAiC,CAAC,KAAK,CAAC;QACxC,6BAA6B,CAAC,KAAK,CAAC,CACrC,CAAC;AACJ,CAAC;AAED,0EAA0E;AAC1E,2EAA2E;AAC3E,iCAAiC;AACjC,MAAM,UAAU,qBAAqB,CACnC,KAAc;IAEd,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC;AACtD,CAAC;AAED,MAAM,UAAU,oBAAoB,CAClC,UAAiB,EACjB,MAAmC;IAEnC,IAAI,UAAU,GAAG,UAAU,CAAC;IAC5B,IAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;IAChC,IACE,iCAAiC,CAAC,MAAM,CAAC;QACzC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,EACnC,CAAC;QACD,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,EAAc;gBAAZ,IAAI,UAAA,EAAE,IAAI,UAAA;YACtC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;gBAC1C,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAM,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;gBAClC,IAAM,QAAM,GAAiC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACpE,QAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;gBACnB,IAAI,GAAG,QAAqB,CAAC;YAC/B,CAAC;YACD,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC;IACD,OAAO,UAAmB,CAAC;AAC7B,CAAC", "sourcesContent": ["import type {\n  ExecutionPatchIncrementalResult,\n  ExecutionPatchInitialResult,\n  ExecutionPatchResult,\n  ApolloPayloadResult,\n  FetchResult,\n} from \"../../link/core/index.js\";\nimport { isNonNullObject } from \"./objects.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { DeepMerger } from \"./mergeDeep.js\";\n\nexport function isExecutionPatchIncrementalResult<T>(\n  value: FetchResult<T>\n): value is ExecutionPatchIncrementalResult {\n  return \"incremental\" in value;\n}\n\nexport function isExecutionPatchInitialResult<T>(\n  value: FetchResult<T>\n): value is ExecutionPatchInitialResult<T> {\n  return \"hasNext\" in value && \"data\" in value;\n}\n\nexport function isExecutionPatchResult<T>(\n  value: FetchResult<T>\n): value is ExecutionPatchResult<T> {\n  return (\n    isExecutionPatchIncrementalResult(value) ||\n    isExecutionPatchInitialResult(value)\n  );\n}\n\n// This function detects an Apollo payload result before it is transformed\n// into a FetchResult via HttpLink; it cannot detect an ApolloPayloadResult\n// once it leaves the link chain.\nexport function isApolloPayloadResult(\n  value: unknown\n): value is ApolloPayloadResult {\n  return isNonNullObject(value) && \"payload\" in value;\n}\n\nexport function mergeIncrementalData<TData extends object>(\n  prevResult: TData,\n  result: ExecutionPatchResult<TData>\n) {\n  let mergedData = prevResult;\n  const merger = new DeepMerger();\n  if (\n    isExecutionPatchIncrementalResult(result) &&\n    isNonEmptyArray(result.incremental)\n  ) {\n    result.incremental.forEach(({ data, path }) => {\n      for (let i = path.length - 1; i >= 0; --i) {\n        const key = path[i];\n        const isNumericKey = !isNaN(+key);\n        const parent: Record<string | number, any> = isNumericKey ? [] : {};\n        parent[key] = data;\n        data = parent as typeof data;\n      }\n      mergedData = merger.merge(mergedData, data);\n    });\n  }\n  return mergedData as TData;\n}\n"]}