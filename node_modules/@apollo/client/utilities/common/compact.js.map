{"version": 3, "file": "compact.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/compact.ts"], "names": [], "mappings": "AAEA;;;GAGG;AACH,MAAM,UAAU,OAAO;IACrB,iBAAiB;SAAjB,UAAiB,EAAjB,qBAAiB,EAAjB,IAAiB;QAAjB,4BAAiB;;IAEjB,IAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAEnC,OAAO,CAAC,OAAO,CAAC,UAAC,GAAG;QAClB,IAAI,CAAC,GAAG;YAAE,OAAO;QACjB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAC3B,IAAM,KAAK,GAAI,GAAW,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC;gBACrB,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import type { TupleToIntersection } from \"./mergeDeep.js\";\n\n/**\n * Merges the provided objects shallowly and removes\n * all properties with an `undefined` value\n */\nexport function compact<TArgs extends any[]>(\n  ...objects: TArgs\n): TupleToIntersection<TArgs> {\n  const result = Object.create(null);\n\n  objects.forEach((obj) => {\n    if (!obj) return;\n    Object.keys(obj).forEach((key) => {\n      const value = (obj as any)[key];\n      if (value !== void 0) {\n        result[key] = value;\n      }\n    });\n  });\n\n  return result;\n}\n"]}