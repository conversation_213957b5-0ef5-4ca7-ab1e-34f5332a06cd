{"version": 3, "file": "objects.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/objects.ts"], "names": [], "mappings": "AAAA,MAAM,UAAU,eAAe,CAAC,GAAQ;IACtC,OAAO,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC;AACjD,CAAC;AAED,MAAM,UAAU,aAAa,CAAC,GAAQ;IACpC,OAAO,CACL,GAAG,KAAK,IAAI;QACZ,OAAO,GAAG,KAAK,QAAQ;QACvB,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,SAAS;YAC9C,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CACvC,CAAC;AACJ,CAAC", "sourcesContent": ["export function isNonNullObject(obj: any): obj is Record<string | number, any> {\n  return obj !== null && typeof obj === \"object\";\n}\n\nexport function isPlainObject(obj: any): obj is Record<string | number, any> {\n  return (\n    obj !== null &&\n    typeof obj === \"object\" &&\n    (Object.getPrototypeOf(obj) === Object.prototype ||\n      Object.getPrototypeOf(obj) === null)\n  );\n}\n"]}