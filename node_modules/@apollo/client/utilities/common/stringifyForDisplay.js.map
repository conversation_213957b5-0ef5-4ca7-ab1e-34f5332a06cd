{"version": 3, "file": "stringifyForDisplay.js", "sourceRoot": "", "sources": ["../../../src/utilities/common/stringifyForDisplay.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAEjD,MAAM,UAAU,mBAAmB,CAAC,KAAU,EAAE,KAAS;IAAT,sBAAA,EAAA,SAAS;IACvD,IAAM,OAAO,GAAG,YAAY,CAAC,qBAAqB,CAAC,CAAC;IACpD,OAAO,IAAI,CAAC,SAAS,CACnB,KAAK,EACL,UAAC,GAAG,EAAE,KAAK;QACT,OAAO,KAAK,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC;IAC5C,CAAC,EACD,KAAK,CACN;SACE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;SAC9B,IAAI,CAAC,aAAa,CAAC,CAAC;AACzB,CAAC", "sourcesContent": ["import { makeUniqueId } from \"./makeUniqueId.js\";\n\nexport function stringifyForDisplay(value: any, space = 0): string {\n  const undefId = makeUniqueId(\"stringifyForDisplay\");\n  return JSON.stringify(\n    value,\n    (key, value) => {\n      return value === void 0 ? undefId : value;\n    },\n    space\n  )\n    .split(JSON.stringify(undefId))\n    .join(\"<undefined>\");\n}\n"]}