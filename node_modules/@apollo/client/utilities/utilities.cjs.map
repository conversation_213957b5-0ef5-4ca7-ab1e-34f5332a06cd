{"version": 3, "file": "utilities.cjs", "sources": ["graphql/directives.js", "common/canUse.js", "common/objects.js", "graphql/fragments.js", "caching/caches.js", "caching/sizes.js", "caching/getMemoryInternals.js", "common/canonicalStringify.js", "graphql/storeUtils.js", "graphql/getFromAST.js", "graphql/DocumentTransform.js", "graphql/print.js", "common/arrays.js", "graphql/transform.js", "graphql/operations.js", "common/mergeDeep.js", "policies/pagination.js", "promises/decoration.js", "promises/preventUnhandledRejection.js", "common/cloneDeep.js", "common/maybeDeepFreeze.js", "observables/iteration.js", "observables/asyncMap.js", "observables/subclassing.js", "observables/Concast.js", "common/incrementalResult.js", "common/errorHandling.js", "common/compact.js", "common/makeUniqueId.js", "common/stringifyForDisplay.js", "common/mergeOptions.js", "common/omitDeep.js", "common/stripTypename.js"], "sourcesContent": ["import { invariant } from \"../globals/index.js\";\nimport { visit, BREAK, Kind } from \"graphql\";\nexport function shouldInclude(_a, variables) {\n    var directives = _a.directives;\n    if (!directives || !directives.length) {\n        return true;\n    }\n    return getInclusionDirectives(directives).every(function (_a) {\n        var directive = _a.directive, ifArgument = _a.ifArgument;\n        var evaledValue = false;\n        if (ifArgument.value.kind === \"Variable\") {\n            evaledValue =\n                variables && variables[ifArgument.value.name.value];\n            invariant(evaledValue !== void 0, 78, directive.name.value);\n        }\n        else {\n            evaledValue = ifArgument.value.value;\n        }\n        return directive.name.value === \"skip\" ? !evaledValue : evaledValue;\n    });\n}\nexport function getDirectiveNames(root) {\n    var names = [];\n    visit(root, {\n        Directive: function (node) {\n            names.push(node.name.value);\n        },\n    });\n    return names;\n}\nexport var hasAnyDirectives = function (names, root) {\n    return hasDirectives(names, root, false);\n};\nexport var hasAllDirectives = function (names, root) {\n    return hasDirectives(names, root, true);\n};\nexport function hasDirectives(names, root, all) {\n    var nameSet = new Set(names);\n    var uniqueCount = nameSet.size;\n    visit(root, {\n        Directive: function (node) {\n            if (nameSet.delete(node.name.value) && (!all || !nameSet.size)) {\n                return BREAK;\n            }\n        },\n    });\n    // If we found all the names, nameSet will be empty. If we only care about\n    // finding some of them, the < condition is sufficient.\n    return all ? !nameSet.size : nameSet.size < uniqueCount;\n}\nexport function hasClientExports(document) {\n    return document && hasDirectives([\"client\", \"export\"], document, true);\n}\nfunction isInclusionDirective(_a) {\n    var value = _a.name.value;\n    return value === \"skip\" || value === \"include\";\n}\nexport function getInclusionDirectives(directives) {\n    var result = [];\n    if (directives && directives.length) {\n        directives.forEach(function (directive) {\n            if (!isInclusionDirective(directive))\n                return;\n            var directiveArguments = directive.arguments;\n            var directiveName = directive.name.value;\n            invariant(directiveArguments && directiveArguments.length === 1, 79, directiveName);\n            var ifArgument = directiveArguments[0];\n            invariant(ifArgument.name && ifArgument.name.value === \"if\", 80, directiveName);\n            var ifValue = ifArgument.value;\n            // means it has to be a variable value if this is a valid @skip or @include directive\n            invariant(ifValue &&\n                (ifValue.kind === \"Variable\" || ifValue.kind === \"BooleanValue\"), 81, directiveName);\n            result.push({ directive: directive, ifArgument: ifArgument });\n        });\n    }\n    return result;\n}\n/** @internal */\nexport function getFragmentMaskMode(fragment) {\n    var _a, _b;\n    var directive = (_a = fragment.directives) === null || _a === void 0 ? void 0 : _a.find(function (_a) {\n        var name = _a.name;\n        return name.value === \"unmask\";\n    });\n    if (!directive) {\n        return \"mask\";\n    }\n    var modeArg = (_b = directive.arguments) === null || _b === void 0 ? void 0 : _b.find(function (_a) {\n        var name = _a.name;\n        return name.value === \"mode\";\n    });\n    if (globalThis.__DEV__ !== false) {\n        if (modeArg) {\n            if (modeArg.value.kind === Kind.VARIABLE) {\n                globalThis.__DEV__ !== false && invariant.warn(82);\n            }\n            else if (modeArg.value.kind !== Kind.STRING) {\n                globalThis.__DEV__ !== false && invariant.warn(83);\n            }\n            else if (modeArg.value.value !== \"migrate\") {\n                globalThis.__DEV__ !== false && invariant.warn(84, modeArg.value.value);\n            }\n        }\n    }\n    if (modeArg &&\n        \"value\" in modeArg.value &&\n        modeArg.value.value === \"migrate\") {\n        return \"migrate\";\n    }\n    return \"unmask\";\n}\n//# sourceMappingURL=directives.js.map", "import { maybe } from \"../globals/index.js\";\nvar isReactNative = maybe(function () { return navigator.product; }) == \"ReactNative\";\nexport var canUseWeakMap = typeof WeakMap === \"function\" &&\n    !(isReactNative && !global.HermesInternal);\nexport var canUseWeakSet = typeof WeakSet === \"function\";\nexport var canUseSymbol = typeof Symbol === \"function\" && typeof Symbol.for === \"function\";\nexport var canUseAsyncIteratorSymbol = canUseSymbol && Symbol.asyncIterator;\nexport var canUseDOM = typeof maybe(function () { return window.document.createElement; }) === \"function\";\nvar usingJSDOM = \n// Following advice found in this comment from @domenic (maintainer of jsdom):\n// https://github.com/jsdom/jsdom/issues/1537#issuecomment-229405327\n//\n// Since we control the version of Jest and jsdom used when running Apollo\n// Client tests, and that version is recent enought to include \" jsdom/x.y.z\"\n// at the end of the user agent string, I believe this case is all we need to\n// check. Testing for \"Node.js\" was recommended for backwards compatibility\n// with older version of jsdom, but we don't have that problem.\nmaybe(function () { return navigator.userAgent.indexOf(\"jsdom\") >= 0; }) || false;\n// Our tests should all continue to pass if we remove this !usingJSDOM\n// condition, thereby allowing useLayoutEffect when using jsdom. Unfortunately,\n// if we allow useLayoutEffect, then useSyncExternalStore generates many\n// warnings about useLayoutEffect doing nothing on the server. While these\n// warnings are harmless, this !usingJSDOM condition seems to be the best way to\n// prevent them (i.e. skipping useLayoutEffect when using jsdom).\nexport var canUseLayoutEffect = (canUseDOM || isReactNative) && !usingJSDOM;\n//# sourceMappingURL=canUse.js.map", "export function isNonNullObject(obj) {\n    return obj !== null && typeof obj === \"object\";\n}\nexport function isPlainObject(obj) {\n    return (obj !== null &&\n        typeof obj === \"object\" &&\n        (Object.getPrototypeOf(obj) === Object.prototype ||\n            Object.getPrototypeOf(obj) === null));\n}\n//# sourceMappingURL=objects.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport { invariant, newInvariantError } from \"../globals/index.js\";\nimport { BREAK, visit } from \"graphql\";\n/**\n * Returns a query document which adds a single query operation that only\n * spreads the target fragment inside of it.\n *\n * So for example a document of:\n *\n * ```graphql\n * fragment foo on Foo { a b c }\n * ```\n *\n * Turns into:\n *\n * ```graphql\n * { ...foo }\n *\n * fragment foo on Foo { a b c }\n * ```\n *\n * The target fragment will either be the only fragment in the document, or a\n * fragment specified by the provided `fragmentName`. If there is more than one\n * fragment, but a `fragmentName` was not defined then an error will be thrown.\n */\nexport function getFragmentQueryDocument(document, fragmentName) {\n    var actualFragmentName = fragmentName;\n    // Build an array of all our fragment definitions that will be used for\n    // validations. We also do some validations on the other definitions in the\n    // document while building this list.\n    var fragments = [];\n    document.definitions.forEach(function (definition) {\n        // Throw an error if we encounter an operation definition because we will\n        // define our own operation definition later on.\n        if (definition.kind === \"OperationDefinition\") {\n            throw newInvariantError(\n                85,\n                definition.operation,\n                definition.name ? \" named '\".concat(definition.name.value, \"'\") : \"\"\n            );\n        }\n        // Add our definition to the fragments array if it is a fragment\n        // definition.\n        if (definition.kind === \"FragmentDefinition\") {\n            fragments.push(definition);\n        }\n    });\n    // If the user did not give us a fragment name then let us try to get a\n    // name from a single fragment in the definition.\n    if (typeof actualFragmentName === \"undefined\") {\n        invariant(fragments.length === 1, 86, fragments.length);\n        actualFragmentName = fragments[0].name.value;\n    }\n    // Generate a query document with an operation that simply spreads the\n    // fragment inside of it.\n    var query = __assign(__assign({}, document), { definitions: __spreadArray([\n            {\n                kind: \"OperationDefinition\",\n                // OperationTypeNode is an enum\n                operation: \"query\",\n                selectionSet: {\n                    kind: \"SelectionSet\",\n                    selections: [\n                        {\n                            kind: \"FragmentSpread\",\n                            name: {\n                                kind: \"Name\",\n                                value: actualFragmentName,\n                            },\n                        },\n                    ],\n                },\n            }\n        ], document.definitions, true) });\n    return query;\n}\n// Utility function that takes a list of fragment definitions and makes a hash out of them\n// that maps the name of the fragment to the fragment definition.\nexport function createFragmentMap(fragments) {\n    if (fragments === void 0) { fragments = []; }\n    var symTable = {};\n    fragments.forEach(function (fragment) {\n        symTable[fragment.name.value] = fragment;\n    });\n    return symTable;\n}\nexport function getFragmentFromSelection(selection, fragmentMap) {\n    switch (selection.kind) {\n        case \"InlineFragment\":\n            return selection;\n        case \"FragmentSpread\": {\n            var fragmentName = selection.name.value;\n            if (typeof fragmentMap === \"function\") {\n                return fragmentMap(fragmentName);\n            }\n            var fragment = fragmentMap && fragmentMap[fragmentName];\n            invariant(fragment, 87, fragmentName);\n            return fragment || null;\n        }\n        default:\n            return null;\n    }\n}\nexport function isFullyUnmaskedOperation(document) {\n    var isUnmasked = true;\n    visit(document, {\n        FragmentSpread: function (node) {\n            isUnmasked =\n                !!node.directives &&\n                    node.directives.some(function (directive) { return directive.name.value === \"unmask\"; });\n            if (!isUnmasked) {\n                return BREAK;\n            }\n        },\n    });\n    return isUnmasked;\n}\n//# sourceMappingURL=fragments.js.map", "import { WeakCache, StrongCache } from \"@wry/caches\";\nvar scheduledCleanup = new WeakSet();\nfunction schedule(cache) {\n    if (cache.size <= (cache.max || -1)) {\n        return;\n    }\n    if (!scheduledCleanup.has(cache)) {\n        scheduledCleanup.add(cache);\n        setTimeout(function () {\n            cache.clean();\n            scheduledCleanup.delete(cache);\n        }, 100);\n    }\n}\n/**\n * @internal\n * A version of WeakCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of WeakCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `WeakCache`.\n */\nexport var AutoCleanedWeakCache = function (max, dispose) {\n    /*\n    Some builds of `WeakCache` are function prototypes, some are classes.\n    This library still builds with an ES5 target, so we can't extend the\n    real classes.\n    Instead, we have to use this workaround until we switch to a newer build\n    target.\n    */\n    var cache = new WeakCache(max, dispose);\n    cache.set = function (key, value) {\n        var ret = WeakCache.prototype.set.call(this, key, value);\n        schedule(this);\n        return ret;\n    };\n    return cache;\n};\n/**\n * @internal\n * A version of StrongCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of StrongCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `StrongCache`.\n */\nexport var AutoCleanedStrongCache = function (max, dispose) {\n    /*\n    Some builds of `StrongCache` are function prototypes, some are classes.\n    This library still builds with an ES5 target, so we can't extend the\n    real classes.\n    Instead, we have to use this workaround until we switch to a newer build\n    target.\n    */\n    var cache = new StrongCache(max, dispose);\n    cache.set = function (key, value) {\n        var ret = StrongCache.prototype.set.call(this, key, value);\n        schedule(this);\n        return ret;\n    };\n    return cache;\n};\n//# sourceMappingURL=caches.js.map", "import { __assign } from \"tslib\";\nimport { global } from \"../globals/index.js\";\nvar cacheSizeSymbol = Symbol.for(\"apollo.cacheSize\");\n/**\n *\n * The global cache size configuration for Apollo Client.\n *\n * @remarks\n *\n * You can directly modify this object, but any modification will\n * only have an effect on caches that are created after the modification.\n *\n * So for global caches, such as `parser`, `canonicalStringify` and `print`,\n * you might need to call `.reset` on them, which will essentially re-create them.\n *\n * Alternatively, you can set `globalThis[Symbol.for(\"apollo.cacheSize\")]` before\n * you load the Apollo Client package:\n *\n * @example\n * ```ts\n * globalThis[Symbol.for(\"apollo.cacheSize\")] = {\n *   parser: 100\n * } satisfies Partial<CacheSizes> // the `satisfies` is optional if using TypeScript\n * ```\n */\nexport var cacheSizes = __assign({}, global[cacheSizeSymbol]);\n//# sourceMappingURL=sizes.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport { cacheSizes } from \"./sizes.js\";\nvar globalCaches = {};\nexport function registerGlobalCache(name, getSize) {\n    globalCaches[name] = getSize;\n}\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getApolloClientMemoryInternals = globalThis.__DEV__ !== false ?\n    _getApolloClientMemoryInternals\n    : undefined;\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getInMemoryCacheMemoryInternals = globalThis.__DEV__ !== false ?\n    _getInMemoryCacheMemoryInternals\n    : undefined;\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getApolloCacheMemoryInternals = globalThis.__DEV__ !== false ?\n    _getApolloCacheMemoryInternals\n    : undefined;\nfunction getCurrentCacheSizes() {\n    // `defaultCacheSizes` is a `const enum` that will be inlined during build, so we have to reconstruct it's shape here\n    var defaults = {\n        parser: 1000 /* defaultCacheSizes[\"parser\"] */,\n        canonicalStringify: 1000 /* defaultCacheSizes[\"canonicalStringify\"] */,\n        print: 2000 /* defaultCacheSizes[\"print\"] */,\n        \"documentTransform.cache\": 2000 /* defaultCacheSizes[\"documentTransform.cache\"] */,\n        \"queryManager.getDocumentInfo\": 2000 /* defaultCacheSizes[\"queryManager.getDocumentInfo\"] */,\n        \"PersistedQueryLink.persistedQueryHashes\": 2000 /* defaultCacheSizes[\"PersistedQueryLink.persistedQueryHashes\"] */,\n        \"fragmentRegistry.transform\": 2000 /* defaultCacheSizes[\"fragmentRegistry.transform\"] */,\n        \"fragmentRegistry.lookup\": 1000 /* defaultCacheSizes[\"fragmentRegistry.lookup\"] */,\n        \"fragmentRegistry.findFragmentSpreads\": 4000 /* defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"] */,\n        \"cache.fragmentQueryDocuments\": 1000 /* defaultCacheSizes[\"cache.fragmentQueryDocuments\"] */,\n        \"removeTypenameFromVariables.getVariableDefinitions\": 2000 /* defaultCacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"] */,\n        \"inMemoryCache.maybeBroadcastWatch\": 5000 /* defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] */,\n        \"inMemoryCache.executeSelectionSet\": 50000 /* defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"] */,\n        \"inMemoryCache.executeSubSelectedArray\": 10000 /* defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"] */,\n    };\n    return Object.fromEntries(Object.entries(defaults).map(function (_a) {\n        var k = _a[0], v = _a[1];\n        return [\n            k,\n            cacheSizes[k] || v,\n        ];\n    }));\n}\nfunction _getApolloClientMemoryInternals() {\n    var _a, _b, _c, _d, _e;\n    if (!(globalThis.__DEV__ !== false))\n        throw new Error(\"only supported in development mode\");\n    return {\n        limits: getCurrentCacheSizes(),\n        sizes: __assign({ print: (_a = globalCaches.print) === null || _a === void 0 ? void 0 : _a.call(globalCaches), parser: (_b = globalCaches.parser) === null || _b === void 0 ? void 0 : _b.call(globalCaches), canonicalStringify: (_c = globalCaches.canonicalStringify) === null || _c === void 0 ? void 0 : _c.call(globalCaches), links: linkInfo(this.link), queryManager: {\n                getDocumentInfo: this[\"queryManager\"][\"transformCache\"].size,\n                documentTransforms: transformInfo(this[\"queryManager\"].documentTransform),\n            } }, (_e = (_d = this.cache).getMemoryInternals) === null || _e === void 0 ? void 0 : _e.call(_d)),\n    };\n}\nfunction _getApolloCacheMemoryInternals() {\n    return {\n        cache: {\n            fragmentQueryDocuments: getWrapperInformation(this[\"getFragmentDoc\"]),\n        },\n    };\n}\nfunction _getInMemoryCacheMemoryInternals() {\n    var fragments = this.config.fragments;\n    return __assign(__assign({}, _getApolloCacheMemoryInternals.apply(this)), { addTypenameDocumentTransform: transformInfo(this[\"addTypenameTransform\"]), inMemoryCache: {\n            executeSelectionSet: getWrapperInformation(this[\"storeReader\"][\"executeSelectionSet\"]),\n            executeSubSelectedArray: getWrapperInformation(this[\"storeReader\"][\"executeSubSelectedArray\"]),\n            maybeBroadcastWatch: getWrapperInformation(this[\"maybeBroadcastWatch\"]),\n        }, fragmentRegistry: {\n            findFragmentSpreads: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.findFragmentSpreads),\n            lookup: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.lookup),\n            transform: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.transform),\n        } });\n}\nfunction isWrapper(f) {\n    return !!f && \"dirtyKey\" in f;\n}\nfunction getWrapperInformation(f) {\n    return isWrapper(f) ? f.size : undefined;\n}\nfunction isDefined(value) {\n    return value != null;\n}\nfunction transformInfo(transform) {\n    return recurseTransformInfo(transform).map(function (cache) { return ({ cache: cache }); });\n}\nfunction recurseTransformInfo(transform) {\n    return transform ?\n        __spreadArray(__spreadArray([\n            getWrapperInformation(transform === null || transform === void 0 ? void 0 : transform[\"performWork\"])\n        ], recurseTransformInfo(transform === null || transform === void 0 ? void 0 : transform[\"left\"]), true), recurseTransformInfo(transform === null || transform === void 0 ? void 0 : transform[\"right\"]), true).filter(isDefined)\n        : [];\n}\nfunction linkInfo(link) {\n    var _a;\n    return link ?\n        __spreadArray(__spreadArray([\n            (_a = link === null || link === void 0 ? void 0 : link.getMemoryInternals) === null || _a === void 0 ? void 0 : _a.call(link)\n        ], linkInfo(link === null || link === void 0 ? void 0 : link.left), true), linkInfo(link === null || link === void 0 ? void 0 : link.right), true).filter(isDefined)\n        : [];\n}\n//# sourceMappingURL=getMemoryInternals.js.map", "import { AutoCleanedStrongCache, cacheSizes, } from \"../../utilities/caching/index.js\";\nimport { registerGlobalCache } from \"../caching/getMemoryInternals.js\";\n/**\n * Like JSON.stringify, but with object keys always sorted in the same order.\n *\n * To achieve performant sorting, this function uses a Map from JSON-serialized\n * arrays of keys (in any order) to sorted arrays of the same keys, with a\n * single sorted array reference shared by all permutations of the keys.\n *\n * As a drawback, this function will add a little bit more memory for every\n * object encountered that has different (more, less, a different order of) keys\n * than in the past.\n *\n * In a typical application, this extra memory usage should not play a\n * significant role, as `canonicalStringify` will be called for only a limited\n * number of object shapes, and the cache will not grow beyond a certain point.\n * But in some edge cases, this could be a problem, so we provide\n * canonicalStringify.reset() as a way of clearing the cache.\n * */\nexport var canonicalStringify = Object.assign(function canonicalStringify(value) {\n    return JSON.stringify(value, stableObjectReplacer);\n}, {\n    reset: function () {\n        // Clearing the sortingMap will reclaim all cached memory, without\n        // affecting the logical results of canonicalStringify, but potentially\n        // sacrificing performance until the cache is refilled.\n        sortingMap = new AutoCleanedStrongCache(cacheSizes.canonicalStringify || 1000 /* defaultCacheSizes.canonicalStringify */);\n    },\n});\nif (globalThis.__DEV__ !== false) {\n    registerGlobalCache(\"canonicalStringify\", function () { return sortingMap.size; });\n}\n// Values are JSON-serialized arrays of object keys (in any order), and values\n// are sorted arrays of the same keys.\nvar sortingMap;\ncanonicalStringify.reset();\n// The JSON.stringify function takes an optional second argument called a\n// replacer function. This function is called for each key-value pair in the\n// object being stringified, and its return value is used instead of the\n// original value. If the replacer function returns a new value, that value is\n// stringified as JSON instead of the original value of the property.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#the_replacer_parameter\nfunction stableObjectReplacer(key, value) {\n    if (value && typeof value === \"object\") {\n        var proto = Object.getPrototypeOf(value);\n        // We don't want to mess with objects that are not \"plain\" objects, which\n        // means their prototype is either Object.prototype or null. This check also\n        // prevents needlessly rearranging the indices of arrays.\n        if (proto === Object.prototype || proto === null) {\n            var keys = Object.keys(value);\n            // If keys is already sorted, let JSON.stringify serialize the original\n            // value instead of creating a new object with keys in the same order.\n            if (keys.every(everyKeyInOrder))\n                return value;\n            var unsortedKey = JSON.stringify(keys);\n            var sortedKeys = sortingMap.get(unsortedKey);\n            if (!sortedKeys) {\n                keys.sort();\n                var sortedKey = JSON.stringify(keys);\n                // Checking for sortedKey in the sortingMap allows us to share the same\n                // sorted array reference for all permutations of the same set of keys.\n                sortedKeys = sortingMap.get(sortedKey) || keys;\n                sortingMap.set(unsortedKey, sortedKeys);\n                sortingMap.set(sortedKey, sortedKeys);\n            }\n            var sortedObject_1 = Object.create(proto);\n            // Reassigning the keys in sorted order will cause JSON.stringify to\n            // serialize them in sorted order.\n            sortedKeys.forEach(function (key) {\n                sortedObject_1[key] = value[key];\n            });\n            return sortedObject_1;\n        }\n    }\n    return value;\n}\n// Since everything that happens in stableObjectReplacer benefits from being as\n// efficient as possible, we use a static function as the callback for\n// keys.every in order to test if the provided keys are already sorted without\n// allocating extra memory for a callback.\nfunction everyKeyInOrder(key, i, keys) {\n    return i === 0 || keys[i - 1] <= key;\n}\n//# sourceMappingURL=canonicalStringify.js.map", "import { newInvariantError } from \"../globals/index.js\";\nimport { isNonNullObject } from \"../common/objects.js\";\nimport { getFragmentFromSelection } from \"./fragments.js\";\nimport { canonicalStringify } from \"../common/canonicalStringify.js\";\nexport function makeReference(id) {\n    return { __ref: String(id) };\n}\nexport function isReference(obj) {\n    return Boolean(obj && typeof obj === \"object\" && typeof obj.__ref === \"string\");\n}\nexport function isDocumentNode(value) {\n    return (isNonNullObject(value) &&\n        value.kind === \"Document\" &&\n        Array.isArray(value.definitions));\n}\nfunction isStringValue(value) {\n    return value.kind === \"StringValue\";\n}\nfunction isBooleanValue(value) {\n    return value.kind === \"BooleanValue\";\n}\nfunction isIntValue(value) {\n    return value.kind === \"IntValue\";\n}\nfunction isFloatValue(value) {\n    return value.kind === \"FloatValue\";\n}\nfunction isVariable(value) {\n    return value.kind === \"Variable\";\n}\nfunction isObjectValue(value) {\n    return value.kind === \"ObjectValue\";\n}\nfunction isListValue(value) {\n    return value.kind === \"ListValue\";\n}\nfunction isEnumValue(value) {\n    return value.kind === \"EnumValue\";\n}\nfunction isNullValue(value) {\n    return value.kind === \"NullValue\";\n}\nexport function valueToObjectRepresentation(argObj, name, value, variables) {\n    if (isIntValue(value) || isFloatValue(value)) {\n        argObj[name.value] = Number(value.value);\n    }\n    else if (isBooleanValue(value) || isStringValue(value)) {\n        argObj[name.value] = value.value;\n    }\n    else if (isObjectValue(value)) {\n        var nestedArgObj_1 = {};\n        value.fields.map(function (obj) {\n            return valueToObjectRepresentation(nestedArgObj_1, obj.name, obj.value, variables);\n        });\n        argObj[name.value] = nestedArgObj_1;\n    }\n    else if (isVariable(value)) {\n        var variableValue = (variables || {})[value.name.value];\n        argObj[name.value] = variableValue;\n    }\n    else if (isListValue(value)) {\n        argObj[name.value] = value.values.map(function (listValue) {\n            var nestedArgArrayObj = {};\n            valueToObjectRepresentation(nestedArgArrayObj, name, listValue, variables);\n            return nestedArgArrayObj[name.value];\n        });\n    }\n    else if (isEnumValue(value)) {\n        argObj[name.value] = value.value;\n    }\n    else if (isNullValue(value)) {\n        argObj[name.value] = null;\n    }\n    else {\n        throw newInvariantError(96, name.value, value.kind);\n    }\n}\nexport function storeKeyNameFromField(field, variables) {\n    var directivesObj = null;\n    if (field.directives) {\n        directivesObj = {};\n        field.directives.forEach(function (directive) {\n            directivesObj[directive.name.value] = {};\n            if (directive.arguments) {\n                directive.arguments.forEach(function (_a) {\n                    var name = _a.name, value = _a.value;\n                    return valueToObjectRepresentation(directivesObj[directive.name.value], name, value, variables);\n                });\n            }\n        });\n    }\n    var argObj = null;\n    if (field.arguments && field.arguments.length) {\n        argObj = {};\n        field.arguments.forEach(function (_a) {\n            var name = _a.name, value = _a.value;\n            return valueToObjectRepresentation(argObj, name, value, variables);\n        });\n    }\n    return getStoreKeyName(field.name.value, argObj, directivesObj);\n}\nvar KNOWN_DIRECTIVES = [\n    \"connection\",\n    \"include\",\n    \"skip\",\n    \"client\",\n    \"rest\",\n    \"export\",\n    \"nonreactive\",\n];\n// Default stable JSON.stringify implementation used by getStoreKeyName. Can be\n// updated/replaced with something better by calling\n// getStoreKeyName.setStringify(newStringifyFunction).\nvar storeKeyNameStringify = canonicalStringify;\nexport var getStoreKeyName = Object.assign(function (fieldName, args, directives) {\n    if (args &&\n        directives &&\n        directives[\"connection\"] &&\n        directives[\"connection\"][\"key\"]) {\n        if (directives[\"connection\"][\"filter\"] &&\n            directives[\"connection\"][\"filter\"].length > 0) {\n            var filterKeys = directives[\"connection\"][\"filter\"] ?\n                directives[\"connection\"][\"filter\"]\n                : [];\n            filterKeys.sort();\n            var filteredArgs_1 = {};\n            filterKeys.forEach(function (key) {\n                filteredArgs_1[key] = args[key];\n            });\n            return \"\".concat(directives[\"connection\"][\"key\"], \"(\").concat(storeKeyNameStringify(filteredArgs_1), \")\");\n        }\n        else {\n            return directives[\"connection\"][\"key\"];\n        }\n    }\n    var completeFieldName = fieldName;\n    if (args) {\n        // We can't use `JSON.stringify` here since it's non-deterministic,\n        // and can lead to different store key names being created even though\n        // the `args` object used during creation has the same properties/values.\n        var stringifiedArgs = storeKeyNameStringify(args);\n        completeFieldName += \"(\".concat(stringifiedArgs, \")\");\n    }\n    if (directives) {\n        Object.keys(directives).forEach(function (key) {\n            if (KNOWN_DIRECTIVES.indexOf(key) !== -1)\n                return;\n            if (directives[key] && Object.keys(directives[key]).length) {\n                completeFieldName += \"@\".concat(key, \"(\").concat(storeKeyNameStringify(directives[key]), \")\");\n            }\n            else {\n                completeFieldName += \"@\".concat(key);\n            }\n        });\n    }\n    return completeFieldName;\n}, {\n    setStringify: function (s) {\n        var previous = storeKeyNameStringify;\n        storeKeyNameStringify = s;\n        return previous;\n    },\n});\nexport function argumentsObjectFromField(field, variables) {\n    if (field.arguments && field.arguments.length) {\n        var argObj_1 = {};\n        field.arguments.forEach(function (_a) {\n            var name = _a.name, value = _a.value;\n            return valueToObjectRepresentation(argObj_1, name, value, variables);\n        });\n        return argObj_1;\n    }\n    return null;\n}\nexport function resultKeyNameFromField(field) {\n    return field.alias ? field.alias.value : field.name.value;\n}\nexport function getTypenameFromResult(result, selectionSet, fragmentMap) {\n    var fragments;\n    for (var _i = 0, _a = selectionSet.selections; _i < _a.length; _i++) {\n        var selection = _a[_i];\n        if (isField(selection)) {\n            if (selection.name.value === \"__typename\") {\n                return result[resultKeyNameFromField(selection)];\n            }\n        }\n        else if (fragments) {\n            fragments.push(selection);\n        }\n        else {\n            fragments = [selection];\n        }\n    }\n    if (typeof result.__typename === \"string\") {\n        return result.__typename;\n    }\n    if (fragments) {\n        for (var _b = 0, fragments_1 = fragments; _b < fragments_1.length; _b++) {\n            var selection = fragments_1[_b];\n            var typename = getTypenameFromResult(result, getFragmentFromSelection(selection, fragmentMap).selectionSet, fragmentMap);\n            if (typeof typename === \"string\") {\n                return typename;\n            }\n        }\n    }\n}\nexport function isField(selection) {\n    return selection.kind === \"Field\";\n}\nexport function isInlineFragment(selection) {\n    return selection.kind === \"InlineFragment\";\n}\n//# sourceMappingURL=storeUtils.js.map", "import { invariant, newInvariantError } from \"../globals/index.js\";\nimport { valueToObjectRepresentation } from \"./storeUtils.js\";\n// Checks the document for errors and throws an exception if there is an error.\nexport function checkDocument(doc) {\n    invariant(doc && doc.kind === \"Document\", 88);\n    var operations = doc.definitions\n        .filter(function (d) { return d.kind !== \"FragmentDefinition\"; })\n        .map(function (definition) {\n        if (definition.kind !== \"OperationDefinition\") {\n            throw newInvariantError(89, definition.kind);\n        }\n        return definition;\n    });\n    invariant(operations.length <= 1, 90, operations.length);\n    return doc;\n}\nexport function getOperationDefinition(doc) {\n    checkDocument(doc);\n    return doc.definitions.filter(function (definition) {\n        return definition.kind === \"OperationDefinition\";\n    })[0];\n}\nexport function getOperationName(doc) {\n    return (doc.definitions\n        .filter(function (definition) {\n        return definition.kind === \"OperationDefinition\" && !!definition.name;\n    })\n        .map(function (x) { return x.name.value; })[0] || null);\n}\n// Returns the FragmentDefinitions from a particular document as an array\nexport function getFragmentDefinitions(doc) {\n    return doc.definitions.filter(function (definition) {\n        return definition.kind === \"FragmentDefinition\";\n    });\n}\nexport function getQueryDefinition(doc) {\n    var queryDef = getOperationDefinition(doc);\n    invariant(queryDef && queryDef.operation === \"query\", 91);\n    return queryDef;\n}\nexport function getFragmentDefinition(doc) {\n    invariant(doc.kind === \"Document\", 92);\n    invariant(doc.definitions.length <= 1, 93);\n    var fragmentDef = doc.definitions[0];\n    invariant(fragmentDef.kind === \"FragmentDefinition\", 94);\n    return fragmentDef;\n}\n/**\n * Returns the first operation definition found in this document.\n * If no operation definition is found, the first fragment definition will be returned.\n * If no definitions are found, an error will be thrown.\n */\nexport function getMainDefinition(queryDoc) {\n    checkDocument(queryDoc);\n    var fragmentDefinition;\n    for (var _i = 0, _a = queryDoc.definitions; _i < _a.length; _i++) {\n        var definition = _a[_i];\n        if (definition.kind === \"OperationDefinition\") {\n            var operation = definition.operation;\n            if (operation === \"query\" ||\n                operation === \"mutation\" ||\n                operation === \"subscription\") {\n                return definition;\n            }\n        }\n        if (definition.kind === \"FragmentDefinition\" && !fragmentDefinition) {\n            // we do this because we want to allow multiple fragment definitions\n            // to precede an operation definition.\n            fragmentDefinition = definition;\n        }\n    }\n    if (fragmentDefinition) {\n        return fragmentDefinition;\n    }\n    throw newInvariantError(95);\n}\nexport function getDefaultValues(definition) {\n    var defaultValues = Object.create(null);\n    var defs = definition && definition.variableDefinitions;\n    if (defs && defs.length) {\n        defs.forEach(function (def) {\n            if (def.defaultValue) {\n                valueToObjectRepresentation(defaultValues, def.variable.name, def.defaultValue);\n            }\n        });\n    }\n    return defaultValues;\n}\n//# sourceMappingURL=getFromAST.js.map", "import { <PERSON>e } from \"@wry/trie\";\nimport { canUseWeakMap, canUseWeakSet } from \"../common/canUse.js\";\nimport { checkDocument } from \"./getFromAST.js\";\nimport { invariant } from \"../globals/index.js\";\nimport { WeakCache } from \"@wry/caches\";\nimport { wrap } from \"optimism\";\nimport { cacheSizes } from \"../caching/index.js\";\nfunction identity(document) {\n    return document;\n}\nvar DocumentTransform = /** @class */ (function () {\n    function DocumentTransform(transform, options) {\n        if (options === void 0) { options = Object.create(null); }\n        this.resultCache = canUseWeakSet ? new WeakSet() : new Set();\n        this.transform = transform;\n        if (options.getCacheKey) {\n            // Override default `getCacheKey` function, which returns [document].\n            this.getCacheKey = options.getCacheKey;\n        }\n        this.cached = options.cache !== false;\n        this.resetCache();\n    }\n    // This default implementation of getCacheKey can be overridden by providing\n    // options.getCacheKey to the DocumentTransform constructor. In general, a\n    // getCacheKey function may either return an array of keys (often including\n    // the document) to be used as a cache key, or undefined to indicate the\n    // transform for this document should not be cached.\n    DocumentTransform.prototype.getCacheKey = function (document) {\n        return [document];\n    };\n    DocumentTransform.identity = function () {\n        // No need to cache this transform since it just returns the document\n        // unchanged. This should save a bit of memory that would otherwise be\n        // needed to populate the `documentCache` of this transform.\n        return new DocumentTransform(identity, { cache: false });\n    };\n    DocumentTransform.split = function (predicate, left, right) {\n        if (right === void 0) { right = DocumentTransform.identity(); }\n        return Object.assign(new DocumentTransform(function (document) {\n            var documentTransform = predicate(document) ? left : right;\n            return documentTransform.transformDocument(document);\n        }, \n        // Reasonably assume both `left` and `right` transforms handle their own caching\n        { cache: false }), { left: left, right: right });\n    };\n    /**\n     * Resets the internal cache of this transform, if it has one.\n     */\n    DocumentTransform.prototype.resetCache = function () {\n        var _this = this;\n        if (this.cached) {\n            var stableCacheKeys_1 = new Trie(canUseWeakMap);\n            this.performWork = wrap(DocumentTransform.prototype.performWork.bind(this), {\n                makeCacheKey: function (document) {\n                    var cacheKeys = _this.getCacheKey(document);\n                    if (cacheKeys) {\n                        invariant(Array.isArray(cacheKeys), 77);\n                        return stableCacheKeys_1.lookupArray(cacheKeys);\n                    }\n                },\n                max: cacheSizes[\"documentTransform.cache\"],\n                cache: (WeakCache),\n            });\n        }\n    };\n    DocumentTransform.prototype.performWork = function (document) {\n        checkDocument(document);\n        return this.transform(document);\n    };\n    DocumentTransform.prototype.transformDocument = function (document) {\n        // If a user passes an already transformed result back to this function,\n        // immediately return it.\n        if (this.resultCache.has(document)) {\n            return document;\n        }\n        var transformedDocument = this.performWork(document);\n        this.resultCache.add(transformedDocument);\n        return transformedDocument;\n    };\n    DocumentTransform.prototype.concat = function (otherTransform) {\n        var _this = this;\n        return Object.assign(new DocumentTransform(function (document) {\n            return otherTransform.transformDocument(_this.transformDocument(document));\n        }, \n        // Reasonably assume both transforms handle their own caching\n        { cache: false }), {\n            left: this,\n            right: otherTransform,\n        });\n    };\n    return DocumentTransform;\n}());\nexport { DocumentTransform };\n//# sourceMappingURL=DocumentTransform.js.map", "import { print as origPrint } from \"graphql\";\nimport { AutoCleanedWeakCache, cacheSizes, } from \"../caching/index.js\";\nimport { registerGlobalCache } from \"../caching/getMemoryInternals.js\";\nvar printCache;\nexport var print = Object.assign(function (ast) {\n    var result = printCache.get(ast);\n    if (!result) {\n        result = origPrint(ast);\n        printCache.set(ast, result);\n    }\n    return result;\n}, {\n    reset: function () {\n        printCache = new AutoCleanedWeakCache(cacheSizes.print || 2000 /* defaultCacheSizes.print */);\n    },\n});\nprint.reset();\nif (globalThis.__DEV__ !== false) {\n    registerGlobalCache(\"print\", function () { return (printCache ? printCache.size : 0); });\n}\n//# sourceMappingURL=print.js.map", "// A version of Array.isArray that works better with readonly arrays.\nexport var isArray = Array.isArray;\nexport function isNonEmptyArray(value) {\n    return Array.isArray(value) && value.length > 0;\n}\n//# sourceMappingURL=arrays.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport { invariant } from \"../globals/index.js\";\nimport { visit, Kind } from \"graphql\";\nimport { checkDocument, getOperationDefinition, getFragmentDefinition, getFragmentDefinitions, getMainDefinition, } from \"./getFromAST.js\";\nimport { isField } from \"./storeUtils.js\";\nimport { createFragmentMap } from \"./fragments.js\";\nimport { isArray, isNonEmptyArray } from \"../common/arrays.js\";\nvar TYPENAME_FIELD = {\n    kind: Kind.FIELD,\n    name: {\n        kind: Kind.NAME,\n        value: \"__typename\",\n    },\n};\nfunction isEmpty(op, fragmentMap) {\n    return (!op ||\n        op.selectionSet.selections.every(function (selection) {\n            return selection.kind === Kind.FRAGMENT_SPREAD &&\n                isEmpty(fragmentMap[selection.name.value], fragmentMap);\n        }));\n}\nfunction nullIfDocIsEmpty(doc) {\n    return (isEmpty(getOperationDefinition(doc) || getFragmentDefinition(doc), createFragmentMap(getFragmentDefinitions(doc)))) ?\n        null\n        : doc;\n}\nfunction getDirectiveMatcher(configs) {\n    var names = new Map();\n    var tests = new Map();\n    configs.forEach(function (directive) {\n        if (directive) {\n            if (directive.name) {\n                names.set(directive.name, directive);\n            }\n            else if (directive.test) {\n                tests.set(directive.test, directive);\n            }\n        }\n    });\n    return function (directive) {\n        var config = names.get(directive.name.value);\n        if (!config && tests.size) {\n            tests.forEach(function (testConfig, test) {\n                if (test(directive)) {\n                    config = testConfig;\n                }\n            });\n        }\n        return config;\n    };\n}\nfunction makeInUseGetterFunction(defaultKey) {\n    var map = new Map();\n    return function inUseGetterFunction(key) {\n        if (key === void 0) { key = defaultKey; }\n        var inUse = map.get(key);\n        if (!inUse) {\n            map.set(key, (inUse = {\n                // Variable and fragment spread names used directly within this\n                // operation or fragment definition, as identified by key. These sets\n                // will be populated during the first traversal of the document in\n                // removeDirectivesFromDocument below.\n                variables: new Set(),\n                fragmentSpreads: new Set(),\n            }));\n        }\n        return inUse;\n    };\n}\nexport function removeDirectivesFromDocument(directives, doc) {\n    checkDocument(doc);\n    // Passing empty strings to makeInUseGetterFunction means we handle anonymous\n    // operations as if their names were \"\". Anonymous fragment definitions are\n    // not supposed to be possible, but the same default naming strategy seems\n    // appropriate for that case as well.\n    var getInUseByOperationName = makeInUseGetterFunction(\"\");\n    var getInUseByFragmentName = makeInUseGetterFunction(\"\");\n    var getInUse = function (ancestors) {\n        for (var p = 0, ancestor = void 0; p < ancestors.length && (ancestor = ancestors[p]); ++p) {\n            if (isArray(ancestor))\n                continue;\n            if (ancestor.kind === Kind.OPERATION_DEFINITION) {\n                // If an operation is anonymous, we use the empty string as its key.\n                return getInUseByOperationName(ancestor.name && ancestor.name.value);\n            }\n            if (ancestor.kind === Kind.FRAGMENT_DEFINITION) {\n                return getInUseByFragmentName(ancestor.name.value);\n            }\n        }\n        globalThis.__DEV__ !== false && invariant.error(97);\n        return null;\n    };\n    var operationCount = 0;\n    for (var i = doc.definitions.length - 1; i >= 0; --i) {\n        if (doc.definitions[i].kind === Kind.OPERATION_DEFINITION) {\n            ++operationCount;\n        }\n    }\n    var directiveMatcher = getDirectiveMatcher(directives);\n    var shouldRemoveField = function (nodeDirectives) {\n        return isNonEmptyArray(nodeDirectives) &&\n            nodeDirectives\n                .map(directiveMatcher)\n                .some(function (config) { return config && config.remove; });\n    };\n    var originalFragmentDefsByPath = new Map();\n    // Any time the first traversal of the document below makes a change like\n    // removing a fragment (by returning null), this variable should be set to\n    // true. Once it becomes true, it should never be set to false again. If this\n    // variable remains false throughout the traversal, then we can return the\n    // original doc immediately without any modifications.\n    var firstVisitMadeChanges = false;\n    var fieldOrInlineFragmentVisitor = {\n        enter: function (node) {\n            if (shouldRemoveField(node.directives)) {\n                firstVisitMadeChanges = true;\n                return null;\n            }\n        },\n    };\n    var docWithoutDirectiveSubtrees = visit(doc, {\n        // These two AST node types share the same implementation, defined above.\n        Field: fieldOrInlineFragmentVisitor,\n        InlineFragment: fieldOrInlineFragmentVisitor,\n        VariableDefinition: {\n            enter: function () {\n                // VariableDefinition nodes do not count as variables in use, though\n                // they do contain Variable nodes that might be visited below. To avoid\n                // counting variable declarations as usages, we skip visiting the\n                // contents of this VariableDefinition node by returning false.\n                return false;\n            },\n        },\n        Variable: {\n            enter: function (node, _key, _parent, _path, ancestors) {\n                var inUse = getInUse(ancestors);\n                if (inUse) {\n                    inUse.variables.add(node.name.value);\n                }\n            },\n        },\n        FragmentSpread: {\n            enter: function (node, _key, _parent, _path, ancestors) {\n                if (shouldRemoveField(node.directives)) {\n                    firstVisitMadeChanges = true;\n                    return null;\n                }\n                var inUse = getInUse(ancestors);\n                if (inUse) {\n                    inUse.fragmentSpreads.add(node.name.value);\n                }\n                // We might like to remove this FragmentSpread by returning null here if\n                // the corresponding FragmentDefinition node is also going to be removed\n                // by the logic below, but we can't control the relative order of those\n                // events, so we have to postpone the removal of dangling FragmentSpread\n                // nodes until after the current visit of the document has finished.\n            },\n        },\n        FragmentDefinition: {\n            enter: function (node, _key, _parent, path) {\n                originalFragmentDefsByPath.set(JSON.stringify(path), node);\n            },\n            leave: function (node, _key, _parent, path) {\n                var originalNode = originalFragmentDefsByPath.get(JSON.stringify(path));\n                if (node === originalNode) {\n                    // If the FragmentNode received by this leave function is identical to\n                    // the one received by the corresponding enter function (above), then\n                    // the visitor must not have made any changes within this\n                    // FragmentDefinition node. This fragment definition may still be\n                    // removed if there are no ...spread references to it, but it won't be\n                    // removed just because it has only a __typename field.\n                    return node;\n                }\n                if (\n                // This logic applies only if the document contains one or more\n                // operations, since removing all fragments from a document containing\n                // only fragments makes the document useless.\n                operationCount > 0 &&\n                    node.selectionSet.selections.every(function (selection) {\n                        return selection.kind === Kind.FIELD &&\n                            selection.name.value === \"__typename\";\n                    })) {\n                    // This is a somewhat opinionated choice: if a FragmentDefinition ends\n                    // up having no fields other than __typename, we remove the whole\n                    // fragment definition, and later prune ...spread references to it.\n                    getInUseByFragmentName(node.name.value).removed = true;\n                    firstVisitMadeChanges = true;\n                    return null;\n                }\n            },\n        },\n        Directive: {\n            leave: function (node) {\n                // If a matching directive is found, remove the directive itself. Note\n                // that this does not remove the target (field, argument, etc) of the\n                // directive, but only the directive itself.\n                if (directiveMatcher(node)) {\n                    firstVisitMadeChanges = true;\n                    return null;\n                }\n            },\n        },\n    });\n    if (!firstVisitMadeChanges) {\n        // If our first pass did not change anything about the document, then there\n        // is no cleanup we need to do, and we can return the original doc.\n        return doc;\n    }\n    // Utility for making sure inUse.transitiveVars is recursively populated.\n    // Because this logic assumes inUse.fragmentSpreads has been completely\n    // populated and inUse.removed has been set if appropriate,\n    // populateTransitiveVars must be called after that information has been\n    // collected by the first traversal of the document.\n    var populateTransitiveVars = function (inUse) {\n        if (!inUse.transitiveVars) {\n            inUse.transitiveVars = new Set(inUse.variables);\n            if (!inUse.removed) {\n                inUse.fragmentSpreads.forEach(function (childFragmentName) {\n                    populateTransitiveVars(getInUseByFragmentName(childFragmentName)).transitiveVars.forEach(function (varName) {\n                        inUse.transitiveVars.add(varName);\n                    });\n                });\n            }\n        }\n        return inUse;\n    };\n    // Since we've been keeping track of fragment spreads used by particular\n    // operations and fragment definitions, we now need to compute the set of all\n    // spreads used (transitively) by any operations in the document.\n    var allFragmentNamesUsed = new Set();\n    docWithoutDirectiveSubtrees.definitions.forEach(function (def) {\n        if (def.kind === Kind.OPERATION_DEFINITION) {\n            populateTransitiveVars(getInUseByOperationName(def.name && def.name.value)).fragmentSpreads.forEach(function (childFragmentName) {\n                allFragmentNamesUsed.add(childFragmentName);\n            });\n        }\n        else if (def.kind === Kind.FRAGMENT_DEFINITION &&\n            // If there are no operations in the document, then all fragment\n            // definitions count as usages of their own fragment names. This heuristic\n            // prevents accidentally removing all fragment definitions from the\n            // document just because it contains no operations that use the fragments.\n            operationCount === 0 &&\n            !getInUseByFragmentName(def.name.value).removed) {\n            allFragmentNamesUsed.add(def.name.value);\n        }\n    });\n    // Now that we have added all fragment spreads used by operations to the\n    // allFragmentNamesUsed set, we can complete the set by transitively adding\n    // all fragment spreads used by those fragments, and so on.\n    allFragmentNamesUsed.forEach(function (fragmentName) {\n        // Once all the childFragmentName strings added here have been seen already,\n        // the top-level allFragmentNamesUsed.forEach loop will terminate.\n        populateTransitiveVars(getInUseByFragmentName(fragmentName)).fragmentSpreads.forEach(function (childFragmentName) {\n            allFragmentNamesUsed.add(childFragmentName);\n        });\n    });\n    var fragmentWillBeRemoved = function (fragmentName) {\n        return !!(\n        // A fragment definition will be removed if there are no spreads that refer\n        // to it, or the fragment was explicitly removed because it had no fields\n        // other than __typename.\n        (!allFragmentNamesUsed.has(fragmentName) ||\n            getInUseByFragmentName(fragmentName).removed));\n    };\n    var enterVisitor = {\n        enter: function (node) {\n            if (fragmentWillBeRemoved(node.name.value)) {\n                return null;\n            }\n        },\n    };\n    return nullIfDocIsEmpty(visit(docWithoutDirectiveSubtrees, {\n        // If the fragment is going to be removed, then leaving any dangling\n        // FragmentSpread nodes with the same name would be a mistake.\n        FragmentSpread: enterVisitor,\n        // This is where the fragment definition is actually removed.\n        FragmentDefinition: enterVisitor,\n        OperationDefinition: {\n            leave: function (node) {\n                // Upon leaving each operation in the depth-first AST traversal, prune\n                // any variables that are declared by the operation but unused within.\n                if (node.variableDefinitions) {\n                    var usedVariableNames_1 = populateTransitiveVars(\n                    // If an operation is anonymous, we use the empty string as its key.\n                    getInUseByOperationName(node.name && node.name.value)).transitiveVars;\n                    // According to the GraphQL spec, all variables declared by an\n                    // operation must either be used by that operation or used by some\n                    // fragment included transitively into that operation:\n                    // https://spec.graphql.org/draft/#sec-All-Variables-Used\n                    //\n                    // To stay on the right side of this validation rule, if/when we\n                    // remove the last $var references from an operation or its fragments,\n                    // we must also remove the corresponding $var declaration from the\n                    // enclosing operation. This pruning applies only to operations and\n                    // not fragment definitions, at the moment. Fragments may be able to\n                    // declare variables eventually, but today they can only consume them.\n                    if (usedVariableNames_1.size < node.variableDefinitions.length) {\n                        return __assign(__assign({}, node), { variableDefinitions: node.variableDefinitions.filter(function (varDef) {\n                                return usedVariableNames_1.has(varDef.variable.name.value);\n                            }) });\n                    }\n                }\n            },\n        },\n    }));\n}\nexport var addTypenameToDocument = Object.assign(function (doc) {\n    return visit(doc, {\n        SelectionSet: {\n            enter: function (node, _key, parent) {\n                // Don't add __typename to OperationDefinitions.\n                if (parent &&\n                    parent.kind ===\n                        Kind.OPERATION_DEFINITION) {\n                    return;\n                }\n                // No changes if no selections.\n                var selections = node.selections;\n                if (!selections) {\n                    return;\n                }\n                // If selections already have a __typename, or are part of an\n                // introspection query, do nothing.\n                var skip = selections.some(function (selection) {\n                    return (isField(selection) &&\n                        (selection.name.value === \"__typename\" ||\n                            selection.name.value.lastIndexOf(\"__\", 0) === 0));\n                });\n                if (skip) {\n                    return;\n                }\n                // If this SelectionSet is @export-ed as an input variable, it should\n                // not have a __typename field (see issue #4691).\n                var field = parent;\n                if (isField(field) &&\n                    field.directives &&\n                    field.directives.some(function (d) { return d.name.value === \"export\"; })) {\n                    return;\n                }\n                // Create and return a new SelectionSet with a __typename Field.\n                return __assign(__assign({}, node), { selections: __spreadArray(__spreadArray([], selections, true), [TYPENAME_FIELD], false) });\n            },\n        },\n    });\n}, {\n    added: function (field) {\n        return field === TYPENAME_FIELD;\n    },\n});\nvar connectionRemoveConfig = {\n    test: function (directive) {\n        var willRemove = directive.name.value === \"connection\";\n        if (willRemove) {\n            if (!directive.arguments ||\n                !directive.arguments.some(function (arg) { return arg.name.value === \"key\"; })) {\n                globalThis.__DEV__ !== false && invariant.warn(98);\n            }\n        }\n        return willRemove;\n    },\n};\nexport function removeConnectionDirectiveFromDocument(doc) {\n    return removeDirectivesFromDocument([connectionRemoveConfig], checkDocument(doc));\n}\nfunction hasDirectivesInSelectionSet(directives, selectionSet, nestedCheck) {\n    if (nestedCheck === void 0) { nestedCheck = true; }\n    return (!!selectionSet &&\n        selectionSet.selections &&\n        selectionSet.selections.some(function (selection) {\n            return hasDirectivesInSelection(directives, selection, nestedCheck);\n        }));\n}\nfunction hasDirectivesInSelection(directives, selection, nestedCheck) {\n    if (nestedCheck === void 0) { nestedCheck = true; }\n    if (!isField(selection)) {\n        return true;\n    }\n    if (!selection.directives) {\n        return false;\n    }\n    return (selection.directives.some(getDirectiveMatcher(directives)) ||\n        (nestedCheck &&\n            hasDirectivesInSelectionSet(directives, selection.selectionSet, nestedCheck)));\n}\nfunction getArgumentMatcher(config) {\n    return function argumentMatcher(argument) {\n        return config.some(function (aConfig) {\n            return argument.value &&\n                argument.value.kind === Kind.VARIABLE &&\n                argument.value.name &&\n                (aConfig.name === argument.value.name.value ||\n                    (aConfig.test && aConfig.test(argument)));\n        });\n    };\n}\nexport function removeArgumentsFromDocument(config, doc) {\n    var argMatcher = getArgumentMatcher(config);\n    return nullIfDocIsEmpty(visit(doc, {\n        OperationDefinition: {\n            enter: function (node) {\n                return __assign(__assign({}, node), { \n                    // Remove matching top level variables definitions.\n                    variableDefinitions: node.variableDefinitions ?\n                        node.variableDefinitions.filter(function (varDef) {\n                            return !config.some(function (arg) { return arg.name === varDef.variable.name.value; });\n                        })\n                        : [] });\n            },\n        },\n        Field: {\n            enter: function (node) {\n                // If `remove` is set to true for an argument, and an argument match\n                // is found for a field, remove the field as well.\n                var shouldRemoveField = config.some(function (argConfig) { return argConfig.remove; });\n                if (shouldRemoveField) {\n                    var argMatchCount_1 = 0;\n                    if (node.arguments) {\n                        node.arguments.forEach(function (arg) {\n                            if (argMatcher(arg)) {\n                                argMatchCount_1 += 1;\n                            }\n                        });\n                    }\n                    if (argMatchCount_1 === 1) {\n                        return null;\n                    }\n                }\n            },\n        },\n        Argument: {\n            enter: function (node) {\n                // Remove all matching arguments.\n                if (argMatcher(node)) {\n                    return null;\n                }\n            },\n        },\n    }));\n}\nexport function removeFragmentSpreadFromDocument(config, doc) {\n    function enter(node) {\n        if (config.some(function (def) { return def.name === node.name.value; })) {\n            return null;\n        }\n    }\n    return nullIfDocIsEmpty(visit(doc, {\n        FragmentSpread: { enter: enter },\n        FragmentDefinition: { enter: enter },\n    }));\n}\n// If the incoming document is a query, return it as is. Otherwise, build a\n// new document containing a query operation based on the selection set\n// of the previous main operation.\nexport function buildQueryFromSelectionSet(document) {\n    var definition = getMainDefinition(document);\n    var definitionOperation = definition.operation;\n    if (definitionOperation === \"query\") {\n        // Already a query, so return the existing document.\n        return document;\n    }\n    // Build a new query using the selection set of the main operation.\n    var modifiedDoc = visit(document, {\n        OperationDefinition: {\n            enter: function (node) {\n                return __assign(__assign({}, node), { operation: \"query\" });\n            },\n        },\n    });\n    return modifiedDoc;\n}\n// Remove fields / selection sets that include an @client directive.\nexport function removeClientSetsFromDocument(document) {\n    checkDocument(document);\n    var modifiedDoc = removeDirectivesFromDocument([\n        {\n            test: function (directive) { return directive.name.value === \"client\"; },\n            remove: true,\n        },\n    ], document);\n    return modifiedDoc;\n}\nexport function addNonReactiveToNamedFragments(document) {\n    checkDocument(document);\n    return visit(document, {\n        FragmentSpread: function (node) {\n            var _a;\n            // Do not add `@nonreactive` if the fragment is marked with `@unmask`\n            // since we want to react to changes in this fragment.\n            if ((_a = node.directives) === null || _a === void 0 ? void 0 : _a.some(function (directive) { return directive.name.value === \"unmask\"; })) {\n                return;\n            }\n            return __assign(__assign({}, node), { directives: __spreadArray(__spreadArray([], (node.directives || []), true), [\n                    {\n                        kind: Kind.DIRECTIVE,\n                        name: { kind: Kind.NAME, value: \"nonreactive\" },\n                    },\n                ], false) });\n        },\n    });\n}\n//# sourceMappingURL=transform.js.map", "import { getOperationDefinition } from \"./getFromAST.js\";\nfunction isOperation(document, operation) {\n    var _a;\n    return ((_a = getOperationDefinition(document)) === null || _a === void 0 ? void 0 : _a.operation) === operation;\n}\nexport function isMutationOperation(document) {\n    return isOperation(document, \"mutation\");\n}\nexport function isQueryOperation(document) {\n    return isOperation(document, \"query\");\n}\nexport function isSubscriptionOperation(document) {\n    return isOperation(document, \"subscription\");\n}\n//# sourceMappingURL=operations.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport { isNonNullObject } from \"./objects.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function mergeDeep() {\n    var sources = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        sources[_i] = arguments[_i];\n    }\n    return mergeDeepArray(sources);\n}\n// In almost any situation where you could succeed in getting the\n// TypeScript compiler to infer a tuple type for the sources array, you\n// could just use mergeDeep instead of mergeDeepArray, so instead of\n// trying to convert T[] to an intersection type we just infer the array\n// element type, which works perfectly when the sources array has a\n// consistent element type.\nexport function mergeDeepArray(sources) {\n    var target = sources[0] || {};\n    var count = sources.length;\n    if (count > 1) {\n        var merger = new DeepMerger();\n        for (var i = 1; i < count; ++i) {\n            target = merger.merge(target, sources[i]);\n        }\n    }\n    return target;\n}\nvar defaultReconciler = function (target, source, property) {\n    return this.merge(target[property], source[property]);\n};\nvar DeepMerger = /** @class */ (function () {\n    function DeepMerger(reconciler) {\n        if (reconciler === void 0) { reconciler = defaultReconciler; }\n        this.reconciler = reconciler;\n        this.isObject = isNonNullObject;\n        this.pastCopies = new Set();\n    }\n    DeepMerger.prototype.merge = function (target, source) {\n        var _this = this;\n        var context = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            context[_i - 2] = arguments[_i];\n        }\n        if (isNonNullObject(source) && isNonNullObject(target)) {\n            Object.keys(source).forEach(function (sourceKey) {\n                if (hasOwnProperty.call(target, sourceKey)) {\n                    var targetValue = target[sourceKey];\n                    if (source[sourceKey] !== targetValue) {\n                        var result = _this.reconciler.apply(_this, __spreadArray([target,\n                            source,\n                            sourceKey], context, false));\n                        // A well-implemented reconciler may return targetValue to indicate\n                        // the merge changed nothing about the structure of the target.\n                        if (result !== targetValue) {\n                            target = _this.shallowCopyForMerge(target);\n                            target[sourceKey] = result;\n                        }\n                    }\n                }\n                else {\n                    // If there is no collision, the target can safely share memory with\n                    // the source, and the recursion can terminate here.\n                    target = _this.shallowCopyForMerge(target);\n                    target[sourceKey] = source[sourceKey];\n                }\n            });\n            return target;\n        }\n        // If source (or target) is not an object, let source replace target.\n        return source;\n    };\n    DeepMerger.prototype.shallowCopyForMerge = function (value) {\n        if (isNonNullObject(value)) {\n            if (!this.pastCopies.has(value)) {\n                if (Array.isArray(value)) {\n                    value = value.slice(0);\n                }\n                else {\n                    value = __assign({ __proto__: Object.getPrototypeOf(value) }, value);\n                }\n                this.pastCopies.add(value);\n            }\n        }\n        return value;\n    };\n    return DeepMerger;\n}());\nexport { DeepMerger };\n//# sourceMappingURL=mergeDeep.js.map", "import { __assign, __rest as __rest_1, __spreadArray } from \"tslib\";\nimport { __rest } from \"tslib\";\nimport { mergeDeep } from \"../common/mergeDeep.js\";\n// A very basic pagination field policy that always concatenates new\n// results onto the existing array, without examining options.args.\nexport function concatPagination(keyArgs) {\n    if (keyArgs === void 0) { keyArgs = false; }\n    return {\n        keyArgs: keyArgs,\n        merge: function (existing, incoming) {\n            return existing ? __spreadArray(__spreadArray([], existing, true), incoming, true) : incoming;\n        },\n    };\n}\n// A basic field policy that uses options.args.{offset,limit} to splice\n// the incoming data into the existing array. If your arguments are called\n// something different (like args.{start,count}), feel free to copy/paste\n// this implementation and make the appropriate changes.\nexport function offsetLimitPagination(keyArgs) {\n    if (keyArgs === void 0) { keyArgs = false; }\n    return {\n        keyArgs: keyArgs,\n        merge: function (existing, incoming, _a) {\n            var args = _a.args;\n            var merged = existing ? existing.slice(0) : [];\n            if (incoming) {\n                if (args) {\n                    // Assume an offset of 0 if args.offset omitted.\n                    var _b = args.offset, offset = _b === void 0 ? 0 : _b;\n                    for (var i = 0; i < incoming.length; ++i) {\n                        merged[offset + i] = incoming[i];\n                    }\n                }\n                else {\n                    // It's unusual (probably a mistake) for a paginated field not\n                    // to receive any arguments, so you might prefer to throw an\n                    // exception here, instead of recovering by appending incoming\n                    // onto the existing array.\n                    merged.push.apply(merged, incoming);\n                }\n            }\n            return merged;\n        },\n    };\n}\n// As proof of the flexibility of field policies, this function generates\n// one that handles Relay-style pagination, without Apollo Client knowing\n// anything about connections, edges, cursors, or pageInfo objects.\nexport function relayStylePagination(keyArgs) {\n    if (keyArgs === void 0) { keyArgs = false; }\n    return {\n        keyArgs: keyArgs,\n        read: function (existing, _a) {\n            var canRead = _a.canRead, readField = _a.readField;\n            if (!existing)\n                return existing;\n            var edges = [];\n            var firstEdgeCursor = \"\";\n            var lastEdgeCursor = \"\";\n            existing.edges.forEach(function (edge) {\n                // Edges themselves could be Reference objects, so it's important\n                // to use readField to access the edge.edge.node property.\n                if (canRead(readField(\"node\", edge))) {\n                    edges.push(edge);\n                    if (edge.cursor) {\n                        firstEdgeCursor = firstEdgeCursor || edge.cursor || \"\";\n                        lastEdgeCursor = edge.cursor || lastEdgeCursor;\n                    }\n                }\n            });\n            if (edges.length > 1 && firstEdgeCursor === lastEdgeCursor) {\n                firstEdgeCursor = \"\";\n            }\n            var _b = existing.pageInfo || {}, startCursor = _b.startCursor, endCursor = _b.endCursor;\n            return __assign(__assign({}, getExtras(existing)), { edges: edges, pageInfo: __assign(__assign({}, existing.pageInfo), { \n                    // If existing.pageInfo.{start,end}Cursor are undefined or \"\", default\n                    // to firstEdgeCursor and/or lastEdgeCursor.\n                    startCursor: startCursor || firstEdgeCursor, endCursor: endCursor || lastEdgeCursor }) });\n        },\n        merge: function (existing, incoming, _a) {\n            var args = _a.args, isReference = _a.isReference, readField = _a.readField;\n            if (!existing) {\n                existing = makeEmptyData();\n            }\n            if (!incoming) {\n                return existing;\n            }\n            var incomingEdges = incoming.edges ?\n                incoming.edges.map(function (edge) {\n                    if (isReference((edge = __assign({}, edge)))) {\n                        // In case edge is a Reference, we read out its cursor field and\n                        // store it as an extra property of the Reference object.\n                        edge.cursor = readField(\"cursor\", edge);\n                    }\n                    return edge;\n                })\n                : [];\n            if (incoming.pageInfo) {\n                var pageInfo_1 = incoming.pageInfo;\n                var startCursor = pageInfo_1.startCursor, endCursor = pageInfo_1.endCursor;\n                var firstEdge = incomingEdges[0];\n                var lastEdge = incomingEdges[incomingEdges.length - 1];\n                // In case we did not request the cursor field for edges in this\n                // query, we can still infer cursors from pageInfo.\n                if (firstEdge && startCursor) {\n                    firstEdge.cursor = startCursor;\n                }\n                if (lastEdge && endCursor) {\n                    lastEdge.cursor = endCursor;\n                }\n                // Cursors can also come from edges, so we default\n                // pageInfo.{start,end}Cursor to {first,last}Edge.cursor.\n                var firstCursor = firstEdge && firstEdge.cursor;\n                if (firstCursor && !startCursor) {\n                    incoming = mergeDeep(incoming, {\n                        pageInfo: {\n                            startCursor: firstCursor,\n                        },\n                    });\n                }\n                var lastCursor = lastEdge && lastEdge.cursor;\n                if (lastCursor && !endCursor) {\n                    incoming = mergeDeep(incoming, {\n                        pageInfo: {\n                            endCursor: lastCursor,\n                        },\n                    });\n                }\n            }\n            var prefix = existing.edges;\n            var suffix = [];\n            if (args && args.after) {\n                // This comparison does not need to use readField(\"cursor\", edge),\n                // because we stored the cursor field of any Reference edges as an\n                // extra property of the Reference object.\n                var index = prefix.findIndex(function (edge) { return edge.cursor === args.after; });\n                if (index >= 0) {\n                    prefix = prefix.slice(0, index + 1);\n                    // suffix = []; // already true\n                }\n            }\n            else if (args && args.before) {\n                var index = prefix.findIndex(function (edge) { return edge.cursor === args.before; });\n                suffix = index < 0 ? prefix : prefix.slice(index);\n                prefix = [];\n            }\n            else if (incoming.edges) {\n                // If we have neither args.after nor args.before, the incoming\n                // edges cannot be spliced into the existing edges, so they must\n                // replace the existing edges. See #6592 for a motivating example.\n                prefix = [];\n            }\n            var edges = __spreadArray(__spreadArray(__spreadArray([], prefix, true), incomingEdges, true), suffix, true);\n            var pageInfo = __assign(__assign({}, incoming.pageInfo), existing.pageInfo);\n            if (incoming.pageInfo) {\n                var _b = incoming.pageInfo, hasPreviousPage = _b.hasPreviousPage, hasNextPage = _b.hasNextPage, startCursor = _b.startCursor, endCursor = _b.endCursor, extras = __rest_1(_b, [\"hasPreviousPage\", \"hasNextPage\", \"startCursor\", \"endCursor\"]);\n                // If incoming.pageInfo had any extra non-standard properties,\n                // assume they should take precedence over any existing properties\n                // of the same name, regardless of where this page falls with\n                // respect to the existing data.\n                Object.assign(pageInfo, extras);\n                // Keep existing.pageInfo.has{Previous,Next}Page unless the\n                // placement of the incoming edges means incoming.hasPreviousPage\n                // or incoming.hasNextPage should become the new values for those\n                // properties in existing.pageInfo. Note that these updates are\n                // only permitted when the beginning or end of the incoming page\n                // coincides with the beginning or end of the existing data, as\n                // determined using prefix.length and suffix.length.\n                if (!prefix.length) {\n                    if (void 0 !== hasPreviousPage)\n                        pageInfo.hasPreviousPage = hasPreviousPage;\n                    if (void 0 !== startCursor)\n                        pageInfo.startCursor = startCursor;\n                }\n                if (!suffix.length) {\n                    if (void 0 !== hasNextPage)\n                        pageInfo.hasNextPage = hasNextPage;\n                    if (void 0 !== endCursor)\n                        pageInfo.endCursor = endCursor;\n                }\n            }\n            return __assign(__assign(__assign({}, getExtras(existing)), getExtras(incoming)), { edges: edges, pageInfo: pageInfo });\n        },\n    };\n}\n// Returns any unrecognized properties of the given object.\nvar getExtras = function (obj) { return __rest(obj, notExtras); };\nvar notExtras = [\"edges\", \"pageInfo\"];\nfunction makeEmptyData() {\n    return {\n        edges: [],\n        pageInfo: {\n            hasPreviousPage: false,\n            hasNextPage: true,\n            startCursor: \"\",\n            endCursor: \"\",\n        },\n    };\n}\n//# sourceMappingURL=pagination.js.map", "export function createFulfilledPromise(value) {\n    var promise = Promise.resolve(value);\n    promise.status = \"fulfilled\";\n    promise.value = value;\n    return promise;\n}\nexport function createRejectedPromise(reason) {\n    var promise = Promise.reject(reason);\n    // prevent potential edge cases leaking unhandled error rejections\n    promise.catch(function () { });\n    promise.status = \"rejected\";\n    promise.reason = reason;\n    return promise;\n}\nexport function isStatefulPromise(promise) {\n    return \"status\" in promise;\n}\nexport function wrapPromiseWithState(promise) {\n    if (isStatefulPromise(promise)) {\n        return promise;\n    }\n    var pendingPromise = promise;\n    pendingPromise.status = \"pending\";\n    pendingPromise.then(function (value) {\n        if (pendingPromise.status === \"pending\") {\n            var fulfilledPromise = pendingPromise;\n            fulfilledPromise.status = \"fulfilled\";\n            fulfilledPromise.value = value;\n        }\n    }, function (reason) {\n        if (pendingPromise.status === \"pending\") {\n            var rejectedPromise = pendingPromise;\n            rejectedPromise.status = \"rejected\";\n            rejectedPromise.reason = reason;\n        }\n    });\n    return promise;\n}\n//# sourceMappingURL=decoration.js.map", "export function preventUnhandledRejection(promise) {\n    promise.catch(function () { });\n    return promise;\n}\n//# sourceMappingURL=preventUnhandledRejection.js.map", "var toString = Object.prototype.toString;\n/**\n * Deeply clones a value to create a new instance.\n */\nexport function cloneDeep(value) {\n    return cloneDeepHelper(value);\n}\nfunction cloneDeepHelper(val, seen) {\n    switch (toString.call(val)) {\n        case \"[object Array]\": {\n            seen = seen || new Map();\n            if (seen.has(val))\n                return seen.get(val);\n            var copy_1 = val.slice(0);\n            seen.set(val, copy_1);\n            copy_1.forEach(function (child, i) {\n                copy_1[i] = cloneDeepHelper(child, seen);\n            });\n            return copy_1;\n        }\n        case \"[object Object]\": {\n            seen = seen || new Map();\n            if (seen.has(val))\n                return seen.get(val);\n            // High fidelity polyfills of Object.create and Object.getPrototypeOf are\n            // possible in all JS environments, so we will assume they exist/work.\n            var copy_2 = Object.create(Object.getPrototypeOf(val));\n            seen.set(val, copy_2);\n            Object.keys(val).forEach(function (key) {\n                copy_2[key] = cloneDeepHelper(val[key], seen);\n            });\n            return copy_2;\n        }\n        default:\n            return val;\n    }\n}\n//# sourceMappingURL=cloneDeep.js.map", "import { isNonNullObject } from \"./objects.js\";\nexport function deepFreeze(value) {\n    var workSet = new Set([value]);\n    workSet.forEach(function (obj) {\n        if (isNonNullObject(obj) && shallowFreeze(obj) === obj) {\n            Object.getOwnPropertyNames(obj).forEach(function (name) {\n                if (isNonNullObject(obj[name]))\n                    workSet.add(obj[name]);\n            });\n        }\n    });\n    return value;\n}\nfunction shallowFreeze(obj) {\n    if (globalThis.__DEV__ !== false && !Object.isFrozen(obj)) {\n        try {\n            Object.freeze(obj);\n        }\n        catch (e) {\n            // Some types like Uint8Array and Node.js's Buffer cannot be frozen, but\n            // they all throw a TypeError when you try, so we re-throw any exceptions\n            // that are not TypeErrors, since that would be unexpected.\n            if (e instanceof TypeError)\n                return null;\n            throw e;\n        }\n    }\n    return obj;\n}\nexport function maybeDeepFreeze(obj) {\n    if (globalThis.__DEV__ !== false) {\n        deepFreeze(obj);\n    }\n    return obj;\n}\n//# sourceMappingURL=maybeDeepFreeze.js.map", "export function iterateObserversSafely(observers, method, argument) {\n    // In case observers is modified during iteration, we need to commit to the\n    // original elements, which also provides an opportunity to filter them down\n    // to just the observers with the given method.\n    var observersWithMethod = [];\n    observers.forEach(function (obs) { return obs[method] && observersWithMethod.push(obs); });\n    observersWithMethod.forEach(function (obs) { return obs[method](argument); });\n}\n//# sourceMappingURL=iteration.js.map", "import { Observable } from \"./Observable.js\";\n// Like Observable.prototype.map, except that the mapping function can\n// optionally return a Promise (or be async).\nexport function asyncMap(observable, mapFn, catchFn) {\n    return new Observable(function (observer) {\n        var promiseQueue = {\n            // Normally we would initialize promiseQueue to Promise.resolve(), but\n            // in this case, for backwards compatibility, we need to be careful to\n            // invoke the first callback synchronously.\n            then: function (callback) {\n                return new Promise(function (resolve) { return resolve(callback()); });\n            },\n        };\n        function makeCallback(examiner, key) {\n            return function (arg) {\n                if (examiner) {\n                    var both = function () {\n                        // If the observer is closed, we don't want to continue calling the\n                        // mapping function - it's result will be swallowed anyways.\n                        return observer.closed ?\n                            /* will be swallowed */ 0\n                            : examiner(arg);\n                    };\n                    promiseQueue = promiseQueue.then(both, both).then(function (result) { return observer.next(result); }, function (error) { return observer.error(error); });\n                }\n                else {\n                    observer[key](arg);\n                }\n            };\n        }\n        var handler = {\n            next: makeCallback(mapFn, \"next\"),\n            error: makeCallback(catchFn, \"error\"),\n            complete: function () {\n                // no need to reassign `promiseQueue`, after `observer.complete`,\n                // the observer will be closed and short-circuit everything anyways\n                /*promiseQueue = */ promiseQueue.then(function () { return observer.complete(); });\n            },\n        };\n        var sub = observable.subscribe(handler);\n        return function () { return sub.unsubscribe(); };\n    });\n}\n//# sourceMappingURL=asyncMap.js.map", "import { Observable } from \"./Observable.js\";\nimport { canUseSymbol } from \"../common/canUse.js\";\n// Generic implementations of Observable.prototype methods like map and\n// filter need to know how to create a new Observable from an Observable\n// subclass (like Concast or ObservableQuery). Those methods assume\n// (perhaps unwisely?) that they can call the subtype's constructor with a\n// Subscriber function, even though the subclass constructor might expect\n// different parameters. Defining this static Symbol.species property on\n// the subclass is a hint to generic Observable code to use the default\n// constructor instead of trying to do `new Subclass(observer => ...)`.\nexport function fixObservableSubclass(subclass) {\n    function set(key) {\n        // Object.defineProperty is necessary because the Symbol.species\n        // property is a getter by default in modern JS environments, so we\n        // can't assign to it with a normal assignment expression.\n        Object.defineProperty(subclass, key, { value: Observable });\n    }\n    if (canUseSymbol && Symbol.species) {\n        set(Symbol.species);\n    }\n    // The \"@@species\" string is used as a fake Symbol.species value in some\n    // polyfill systems (including the SymbolSpecies variable used by\n    // zen-observable), so we should set it as well, to be safe.\n    set(\"@@species\");\n    return subclass;\n}\n//# sourceMappingURL=subclassing.js.map", "import { __extends } from \"tslib\";\nimport { Observable } from \"./Observable.js\";\nimport { iterateObserversSafely } from \"./iteration.js\";\nimport { fixObservableSubclass } from \"./subclassing.js\";\nfunction isPromiseLike(value) {\n    return value && typeof value.then === \"function\";\n}\n// A Concast<T> observable concatenates the given sources into a single\n// non-overlapping sequence of Ts, automatically unwrapping any promises,\n// and broadcasts the T elements of that sequence to any number of\n// subscribers, all without creating a bunch of intermediary Observable\n// wrapper objects.\n//\n// Even though any number of observers can subscribe to the Concast, each\n// source observable is guaranteed to receive at most one subscribe call,\n// and the results are multicast to all observers.\n//\n// In addition to broadcasting every next/error message to this.observers,\n// the Concast stores the most recent message using this.latest, so any\n// new observers can immediately receive the latest message, even if it\n// was originally delivered in the past. This behavior means we can assume\n// every active observer in this.observers has received the same most\n// recent message.\n//\n// With the exception of this.latest replay, a Concast is a \"hot\"\n// observable in the sense that it does not replay past results from the\n// beginning of time for each new observer.\n//\n// Could we have used some existing RxJS class instead? Concast<T> is\n// similar to a BehaviorSubject<T>, because it is multicast and redelivers\n// the latest next/error message to new subscribers. Unlike Subject<T>,\n// Concast<T> does not expose an Observer<T> interface (this.handlers is\n// intentionally private), since Concast<T> gets its inputs from the\n// concatenated sources. If we ever switch to RxJS, there may be some\n// value in reusing their code, but for now we use zen-observable, which\n// does not contain any Subject implementations.\nvar Concast = /** @class */ (function (_super) {\n    __extends(Concast, _super);\n    // Not only can the individual elements of the iterable be promises, but\n    // also the iterable itself can be wrapped in a promise.\n    function Concast(sources) {\n        var _this = _super.call(this, function (observer) {\n            _this.addObserver(observer);\n            return function () { return _this.removeObserver(observer); };\n        }) || this;\n        // Active observers receiving broadcast messages. Thanks to this.latest,\n        // we can assume all observers in this Set have received the same most\n        // recent message, though possibly at different times in the past.\n        _this.observers = new Set();\n        _this.promise = new Promise(function (resolve, reject) {\n            _this.resolve = resolve;\n            _this.reject = reject;\n        });\n        // Bound handler functions that can be reused for every internal\n        // subscription.\n        _this.handlers = {\n            next: function (result) {\n                if (_this.sub !== null) {\n                    _this.latest = [\"next\", result];\n                    _this.notify(\"next\", result);\n                    iterateObserversSafely(_this.observers, \"next\", result);\n                }\n            },\n            error: function (error) {\n                var sub = _this.sub;\n                if (sub !== null) {\n                    // Delay unsubscribing from the underlying subscription slightly,\n                    // so that immediately subscribing another observer can keep the\n                    // subscription active.\n                    if (sub)\n                        setTimeout(function () { return sub.unsubscribe(); });\n                    _this.sub = null;\n                    _this.latest = [\"error\", error];\n                    _this.reject(error);\n                    _this.notify(\"error\", error);\n                    iterateObserversSafely(_this.observers, \"error\", error);\n                }\n            },\n            complete: function () {\n                var _a = _this, sub = _a.sub, _b = _a.sources, sources = _b === void 0 ? [] : _b;\n                if (sub !== null) {\n                    // If complete is called before concast.start, this.sources may be\n                    // undefined, so we use a default value of [] for sources. That works\n                    // here because it falls into the if (!value) {...} block, which\n                    // appropriately terminates the Concast, even if this.sources might\n                    // eventually have been initialized to a non-empty array.\n                    var value = sources.shift();\n                    if (!value) {\n                        if (sub)\n                            setTimeout(function () { return sub.unsubscribe(); });\n                        _this.sub = null;\n                        if (_this.latest && _this.latest[0] === \"next\") {\n                            _this.resolve(_this.latest[1]);\n                        }\n                        else {\n                            _this.resolve();\n                        }\n                        _this.notify(\"complete\");\n                        // We do not store this.latest = [\"complete\"], because doing so\n                        // discards useful information about the previous next (or\n                        // error) message. Instead, if new observers subscribe after\n                        // this Concast has completed, they will receive the final\n                        // 'next' message (unless there was an error) immediately\n                        // followed by a 'complete' message (see addObserver).\n                        iterateObserversSafely(_this.observers, \"complete\");\n                    }\n                    else if (isPromiseLike(value)) {\n                        value.then(function (obs) { return (_this.sub = obs.subscribe(_this.handlers)); }, _this.handlers.error);\n                    }\n                    else {\n                        _this.sub = value.subscribe(_this.handlers);\n                    }\n                }\n            },\n        };\n        _this.nextResultListeners = new Set();\n        // A public way to abort observation and broadcast.\n        _this.cancel = function (reason) {\n            _this.reject(reason);\n            _this.sources = [];\n            _this.handlers.error(reason);\n        };\n        // Suppress rejection warnings for this.promise, since it's perfectly\n        // acceptable to pay no attention to this.promise if you're consuming\n        // the results through the normal observable API.\n        _this.promise.catch(function (_) { });\n        // If someone accidentally tries to create a Concast using a subscriber\n        // function, recover by creating an Observable from that subscriber and\n        // using it as the source.\n        if (typeof sources === \"function\") {\n            sources = [new Observable(sources)];\n        }\n        if (isPromiseLike(sources)) {\n            sources.then(function (iterable) { return _this.start(iterable); }, _this.handlers.error);\n        }\n        else {\n            _this.start(sources);\n        }\n        return _this;\n    }\n    Concast.prototype.start = function (sources) {\n        if (this.sub !== void 0)\n            return;\n        // In practice, sources is most often simply an Array of observables.\n        // TODO Consider using sources[Symbol.iterator]() to take advantage\n        // of the laziness of non-Array iterables.\n        this.sources = Array.from(sources);\n        // Calling this.handlers.complete() kicks off consumption of the first\n        // source observable. It's tempting to do this step lazily in\n        // addObserver, but this.promise can be accessed without calling\n        // addObserver, so consumption needs to begin eagerly.\n        this.handlers.complete();\n    };\n    Concast.prototype.deliverLastMessage = function (observer) {\n        if (this.latest) {\n            var nextOrError = this.latest[0];\n            var method = observer[nextOrError];\n            if (method) {\n                method.call(observer, this.latest[1]);\n            }\n            // If the subscription is already closed, and the last message was\n            // a 'next' message, simulate delivery of the final 'complete'\n            // message again.\n            if (this.sub === null && nextOrError === \"next\" && observer.complete) {\n                observer.complete();\n            }\n        }\n    };\n    Concast.prototype.addObserver = function (observer) {\n        if (!this.observers.has(observer)) {\n            // Immediately deliver the most recent message, so we can always\n            // be sure all observers have the latest information.\n            this.deliverLastMessage(observer);\n            this.observers.add(observer);\n        }\n    };\n    Concast.prototype.removeObserver = function (observer) {\n        if (this.observers.delete(observer) && this.observers.size < 1) {\n            // In case there are still any listeners in this.nextResultListeners, and\n            // no error or completion has been broadcast yet, make sure those\n            // observers have a chance to run and then remove themselves from\n            // this.observers.\n            this.handlers.complete();\n        }\n    };\n    Concast.prototype.notify = function (method, arg) {\n        var nextResultListeners = this.nextResultListeners;\n        if (nextResultListeners.size) {\n            // Replacing this.nextResultListeners first ensures it does not grow while\n            // we are iterating over it, potentially leading to infinite loops.\n            this.nextResultListeners = new Set();\n            nextResultListeners.forEach(function (listener) { return listener(method, arg); });\n        }\n    };\n    // We need a way to run callbacks just *before* the next result (or error or\n    // completion) is delivered by this Concast, so we can be sure any code that\n    // runs as a result of delivering that result/error observes the effects of\n    // running the callback(s). It was tempting to reuse the Observer type instead\n    // of introducing NextResultListener, but that messes with the sizing and\n    // maintenance of this.observers, and ends up being more code overall.\n    Concast.prototype.beforeNext = function (callback) {\n        var called = false;\n        this.nextResultListeners.add(function (method, arg) {\n            if (!called) {\n                called = true;\n                callback(method, arg);\n            }\n        });\n    };\n    return Concast;\n}(Observable));\nexport { Concast };\n// Necessary because the Concast constructor has a different signature\n// than the Observable constructor.\nfixObservableSubclass(Concast);\n//# sourceMappingURL=Concast.js.map", "import { isNonNullObject } from \"./objects.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { DeepMerger } from \"./mergeDeep.js\";\nexport function isExecutionPatchIncrementalResult(value) {\n    return \"incremental\" in value;\n}\nexport function isExecutionPatchInitialResult(value) {\n    return \"hasNext\" in value && \"data\" in value;\n}\nexport function isExecutionPatchResult(value) {\n    return (isExecutionPatchIncrementalResult(value) ||\n        isExecutionPatchInitialResult(value));\n}\n// This function detects an Apollo payload result before it is transformed\n// into a FetchResult via HttpLink; it cannot detect an ApolloPayloadResult\n// once it leaves the link chain.\nexport function isApolloPayloadResult(value) {\n    return isNonNullObject(value) && \"payload\" in value;\n}\nexport function mergeIncrementalData(prevResult, result) {\n    var mergedData = prevResult;\n    var merger = new DeepMerger();\n    if (isExecutionPatchIncrementalResult(result) &&\n        isNonEmptyArray(result.incremental)) {\n        result.incremental.forEach(function (_a) {\n            var data = _a.data, path = _a.path;\n            for (var i = path.length - 1; i >= 0; --i) {\n                var key = path[i];\n                var isNumericKey = !isNaN(+key);\n                var parent_1 = isNumericKey ? [] : {};\n                parent_1[key] = data;\n                data = parent_1;\n            }\n            mergedData = merger.merge(mergedData, data);\n        });\n    }\n    return mergedData;\n}\n//# sourceMappingURL=incrementalResult.js.map", "import { isNonEmptyArray } from \"./arrays.js\";\nimport { isExecutionPatchIncrementalResult } from \"./incrementalResult.js\";\nexport function graphQLResultHasError(result) {\n    var errors = getGraphQLErrorsFromResult(result);\n    return isNonEmptyArray(errors);\n}\nexport function getGraphQLErrorsFromResult(result) {\n    var graphQLErrors = isNonEmptyArray(result.errors) ? result.errors.slice(0) : [];\n    if (isExecutionPatchIncrementalResult(result) &&\n        isNonEmptyArray(result.incremental)) {\n        result.incremental.forEach(function (incrementalResult) {\n            if (incrementalResult.errors) {\n                graphQLErrors.push.apply(graphQLErrors, incrementalResult.errors);\n            }\n        });\n    }\n    return graphQLErrors;\n}\n//# sourceMappingURL=errorHandling.js.map", "/**\n * Merges the provided objects shallowly and removes\n * all properties with an `undefined` value\n */\nexport function compact() {\n    var objects = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        objects[_i] = arguments[_i];\n    }\n    var result = Object.create(null);\n    objects.forEach(function (obj) {\n        if (!obj)\n            return;\n        Object.keys(obj).forEach(function (key) {\n            var value = obj[key];\n            if (value !== void 0) {\n                result[key] = value;\n            }\n        });\n    });\n    return result;\n}\n//# sourceMappingURL=compact.js.map", "var prefixCounts = new Map();\n// These IDs won't be globally unique, but they will be unique within this\n// process, thanks to the counter, and unguessable thanks to the random suffix.\nexport function makeUniqueId(prefix) {\n    var count = prefixCounts.get(prefix) || 1;\n    prefixCounts.set(prefix, count + 1);\n    return \"\".concat(prefix, \":\").concat(count, \":\").concat(Math.random().toString(36).slice(2));\n}\n//# sourceMappingURL=makeUniqueId.js.map", "import { makeUniqueId } from \"./makeUniqueId.js\";\nexport function stringifyForDisplay(value, space) {\n    if (space === void 0) { space = 0; }\n    var undefId = makeUniqueId(\"stringifyForDisplay\");\n    return JSON.stringify(value, function (key, value) {\n        return value === void 0 ? undefId : value;\n    }, space)\n        .split(JSON.stringify(undefId))\n        .join(\"<undefined>\");\n}\n//# sourceMappingURL=stringifyForDisplay.js.map", "import { __assign } from \"tslib\";\nimport { compact } from \"./compact.js\";\nexport function mergeOptions(defaults, options) {\n    return compact(defaults, options, options.variables && {\n        variables: compact(__assign(__assign({}, (defaults && defaults.variables)), options.variables)),\n    });\n}\n//# sourceMappingURL=mergeOptions.js.map", "import { isPlainObject } from \"./objects.js\";\nexport function omitDeep(value, key) {\n    return __omitDeep(value, key);\n}\nfunction __omitDeep(value, key, known) {\n    if (known === void 0) { known = new Map(); }\n    if (known.has(value)) {\n        return known.get(value);\n    }\n    var modified = false;\n    if (Array.isArray(value)) {\n        var array_1 = [];\n        known.set(value, array_1);\n        value.forEach(function (value, index) {\n            var result = __omitDeep(value, key, known);\n            modified || (modified = result !== value);\n            array_1[index] = result;\n        });\n        if (modified) {\n            return array_1;\n        }\n    }\n    else if (isPlainObject(value)) {\n        var obj_1 = Object.create(Object.getPrototypeOf(value));\n        known.set(value, obj_1);\n        Object.keys(value).forEach(function (k) {\n            if (k === key) {\n                modified = true;\n                return;\n            }\n            var result = __omitDeep(value[k], key, known);\n            modified || (modified = result !== value[k]);\n            obj_1[k] = result;\n        });\n        if (modified) {\n            return obj_1;\n        }\n    }\n    return value;\n}\n//# sourceMappingURL=omitDeep.js.map", "import { omitDeep } from \"./omitDeep.js\";\nexport function stripTypename(value) {\n    return omitDeep(value, \"__typename\");\n}\n//# sourceMappingURL=stripTypename.js.map"], "names": ["invariant", "visit", "BREAK", "Kind", "maybe", "newInvariantError", "__assign", "__spread<PERSON><PERSON>y", "<PERSON>ak<PERSON><PERSON>", "StrongCache", "global", "<PERSON><PERSON>", "wrap", "origPrint", "__rest_1", "__rest", "Observable", "__extends"], "mappings": ";;;;;;;;;;;;;AAEO,SAAS,aAAa,CAAC,EAAE,EAAE,SAAS,EAAE;AAC7C,IAAI,IAAI,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC;AACnC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAC3C,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,sBAAsB,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE;AAClE,QAAQ,IAAI,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC;AACjE,QAAQ,IAAI,WAAW,GAAG,KAAK,CAAC;AAChC,QAAQ,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;AAClD,YAAY,WAAW;AACvB,gBAAgB,SAAS,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACpE,YAAYA,iBAAS,CAAC,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxE,SAAS;AACT,aAAa;AACb,YAAY,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC;AACjD,SAAS;AACT,QAAQ,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,MAAM,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;AAC5E,KAAK,CAAC,CAAC;AACP,CAAC;AACM,SAAS,iBAAiB,CAAC,IAAI,EAAE;AACxC,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;AACnB,IAAIC,aAAK,CAAC,IAAI,EAAE;AAChB,QAAQ,SAAS,EAAE,UAAU,IAAI,EAAE;AACnC,YAAY,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxC,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACS,IAAC,gBAAgB,GAAG,UAAU,KAAK,EAAE,IAAI,EAAE;AACrD,IAAI,OAAO,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC7C,EAAE;AACQ,IAAC,gBAAgB,GAAG,UAAU,KAAK,EAAE,IAAI,EAAE;AACrD,IAAI,OAAO,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5C,EAAE;AACK,SAAS,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE;AAChD,IAAI,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;AACjC,IAAI,IAAI,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;AACnC,IAAIA,aAAK,CAAC,IAAI,EAAE;AAChB,QAAQ,SAAS,EAAE,UAAU,IAAI,EAAE;AACnC,YAAY,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC5E,gBAAgB,OAAOC,aAAK,CAAC;AAC7B,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AAGP,IAAI,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;AAC5D,CAAC;AACM,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AAC3C,IAAI,OAAO,QAAQ,IAAI,aAAa,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC3E,CAAC;AACD,SAAS,oBAAoB,CAAC,EAAE,EAAE;AAClC,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9B,IAAI,OAAO,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,SAAS,CAAC;AACnD,CAAC;AACM,SAAS,sBAAsB,CAAC,UAAU,EAAE;AACnD,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE;AACzC,QAAQ,UAAU,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAChD,YAAY,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;AAChD,gBAAgB,OAAO;AACvB,YAAY,IAAI,kBAAkB,GAAG,SAAS,CAAC,SAAS,CAAC;AACzD,YAAY,IAAI,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;AACrD,YAAYF,iBAAS,CAAC,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC;AAChG,YAAY,IAAI,UAAU,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;AACnD,YAAYA,iBAAS,CAAC,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC;AAC5F,YAAY,IAAI,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC;AAE3C,YAAYA,iBAAS,CAAC,OAAO;AAC7B,iBAAiB,OAAO,CAAC,IAAI,KAAK,UAAU,IAAI,OAAO,CAAC,IAAI,KAAK,cAAc,CAAC,EAAE,EAAE,EAAE,aAAa,CAAC,CAAC;AACrG,YAAY,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;AAC1E,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AAEM,SAAS,mBAAmB,CAAC,QAAQ,EAAE;AAC9C,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,IAAI,SAAS,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,UAAU,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AAC1G,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC3B,QAAQ,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;AACvC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,CAAC,EAAE,GAAG,SAAS,CAAC,SAAS,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE;AACxG,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC3B,QAAQ,OAAO,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC;AACrC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AACtC,QAAQ,IAAI,OAAO,EAAE;AACrB,YAAY,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,KAAKG,YAAI,CAAC,QAAQ,EAAE;AACtD,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIH,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnE,aAAa;AACb,iBAAiB,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,KAAKG,YAAI,CAAC,MAAM,EAAE;AACzD,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIH,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnE,aAAa;AACb,iBAAiB,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE;AACxD,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIA,iBAAS,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACxF,aAAa;AACb,SAAS;AACT,KAAK;AACL,IAAI,IAAI,OAAO;AACf,QAAQ,OAAO,IAAI,OAAO,CAAC,KAAK;AAChC,QAAQ,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE;AAC3C,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB;;AC7GA,IAAI,aAAa,GAAGI,aAAK,CAAC,YAAY,EAAE,OAAO,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,aAAa,CAAC;AAC5E,IAAC,aAAa,GAAG,OAAO,OAAO,KAAK,UAAU;AACxD,IAAI,EAAE,aAAa,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;AACrC,IAAC,aAAa,GAAG,OAAO,OAAO,KAAK,WAAW;AAC/C,IAAC,YAAY,GAAG,OAAO,MAAM,KAAK,UAAU,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,WAAW;AACjF,IAAC,yBAAyB,GAAG,YAAY,IAAI,MAAM,CAAC,cAAc;AAClE,IAAC,SAAS,GAAG,OAAOA,aAAK,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC,KAAK,WAAW;AAC1G,IAAI,UAAU;AASdA,aAAK,CAAC,YAAY,EAAE,OAAO,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC;AAOxE,IAAC,kBAAkB,GAAG,CAAC,SAAS,IAAI,aAAa,KAAK,CAAC;;ACxB1D,SAAS,eAAe,CAAC,GAAG,EAAE;AACrC,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC;AACnD,CAAC;AACM,SAAS,aAAa,CAAC,GAAG,EAAE;AACnC,IAAI,QAAQ,GAAG,KAAK,IAAI;AACxB,QAAQ,OAAO,GAAG,KAAK,QAAQ;AAC/B,SAAS,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,SAAS;AACxD,YAAY,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE;AAClD;;ACiBO,SAAS,wBAAwB,CAAC,QAAQ,EAAE,YAAY,EAAE;AACjE,IAAI,IAAI,kBAAkB,GAAG,YAAY,CAAC;AAI1C,IAAI,IAAI,SAAS,GAAG,EAAE,CAAC;AACvB,IAAI,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,UAAU,EAAE;AAGvD,QAAQ,IAAI,UAAU,CAAC,IAAI,KAAK,qBAAqB,EAAE;AACvD,YAAY,MAAMC,yBAAiB;AACnC,gBAAgB,EAAE;AAClB,gBAAgB,UAAU,CAAC,SAAS;AACpC,gBAAgB,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,EAAE;AACpF,aAAa,CAAC;AACd,SAAS;AAGT,QAAQ,IAAI,UAAU,CAAC,IAAI,KAAK,oBAAoB,EAAE;AACtD,YAAY,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACvC,SAAS;AACT,KAAK,CAAC,CAAC;AAGP,IAAI,IAAI,OAAO,kBAAkB,KAAK,WAAW,EAAE;AACnD,QAAQL,iBAAS,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAChE,QAAQ,kBAAkB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AACrD,KAAK;AAGL,IAAI,IAAI,KAAK,GAAGM,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,WAAW,EAAEC,mBAAa,CAAC;AAC9E,YAAY;AACZ,gBAAgB,IAAI,EAAE,qBAAqB;AAE3C,gBAAgB,SAAS,EAAE,OAAO;AAClC,gBAAgB,YAAY,EAAE;AAC9B,oBAAoB,IAAI,EAAE,cAAc;AACxC,oBAAoB,UAAU,EAAE;AAChC,wBAAwB;AACxB,4BAA4B,IAAI,EAAE,gBAAgB;AAClD,4BAA4B,IAAI,EAAE;AAClC,gCAAgC,IAAI,EAAE,MAAM;AAC5C,gCAAgC,KAAK,EAAE,kBAAkB;AACzD,6BAA6B;AAC7B,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS,EAAE,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;AAC1C,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AAGM,SAAS,iBAAiB,CAAC,SAAS,EAAE;AAC7C,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,EAAE,CAAC,EAAE;AACjD,IAAI,IAAI,QAAQ,GAAG,EAAE,CAAC;AACtB,IAAI,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;AAC1C,QAAQ,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;AACjD,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC;AACM,SAAS,wBAAwB,CAAC,SAAS,EAAE,WAAW,EAAE;AACjE,IAAI,QAAQ,SAAS,CAAC,IAAI;AAC1B,QAAQ,KAAK,gBAAgB;AAC7B,YAAY,OAAO,SAAS,CAAC;AAC7B,QAAQ,KAAK,gBAAgB,EAAE;AAC/B,YAAY,IAAI,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;AACpD,YAAY,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE;AACnD,gBAAgB,OAAO,WAAW,CAAC,YAAY,CAAC,CAAC;AACjD,aAAa;AACb,YAAY,IAAI,QAAQ,GAAG,WAAW,IAAI,WAAW,CAAC,YAAY,CAAC,CAAC;AACpE,YAAYP,iBAAS,CAAC,QAAQ,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;AAClD,YAAY,OAAO,QAAQ,IAAI,IAAI,CAAC;AACpC,SAAS;AACT,QAAQ;AACR,YAAY,OAAO,IAAI,CAAC;AACxB,KAAK;AACL,CAAC;AACM,SAAS,wBAAwB,CAAC,QAAQ,EAAE;AACnD,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC;AAC1B,IAAIC,aAAK,CAAC,QAAQ,EAAE;AACpB,QAAQ,cAAc,EAAE,UAAU,IAAI,EAAE;AACxC,YAAY,UAAU;AACtB,gBAAgB,CAAC,CAAC,IAAI,CAAC,UAAU;AACjC,oBAAoB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,SAAS,EAAE,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC7G,YAAY,IAAI,CAAC,UAAU,EAAE;AAC7B,gBAAgB,OAAOC,aAAK,CAAC;AAC7B,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,UAAU,CAAC;AACtB;;ACnHA,IAAI,gBAAgB,GAAG,IAAI,OAAO,EAAE,CAAC;AACrC,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE;AACzC,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACtC,QAAQ,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACpC,QAAQ,UAAU,CAAC,YAAY;AAC/B,YAAY,KAAK,CAAC,KAAK,EAAE,CAAC;AAC1B,YAAY,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC3C,SAAS,EAAE,GAAG,CAAC,CAAC;AAChB,KAAK;AACL,CAAC;AAYS,IAAC,oBAAoB,GAAG,UAAU,GAAG,EAAE,OAAO,EAAE;AAQ1D,IAAI,IAAI,KAAK,GAAG,IAAIM,gBAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAC5C,IAAI,KAAK,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE,KAAK,EAAE;AACtC,QAAQ,IAAI,GAAG,GAAGA,gBAAS,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACjE,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvB,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE;AAYQ,IAAC,sBAAsB,GAAG,UAAU,GAAG,EAAE,OAAO,EAAE;AAQ5D,IAAI,IAAI,KAAK,GAAG,IAAIC,kBAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAC9C,IAAI,KAAK,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE,KAAK,EAAE;AACtC,QAAQ,IAAI,GAAG,GAAGA,kBAAW,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACnE,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvB,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,OAAO,KAAK,CAAC;AACjB;;ACjEA,IAAI,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AAuB3C,IAAC,UAAU,GAAGH,cAAQ,CAAC,EAAE,EAAEI,cAAM,CAAC,eAAe,CAAC;;ACvB5D,IAAI,YAAY,GAAG,EAAE,CAAC;AACf,SAAS,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE;AACnD,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;AACjC;;ACcU,IAAC,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,kBAAkB,CAAC,KAAK,EAAE;AACjF,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,oBAAoB,CAAC,CAAC;AACvD,CAAC,EAAE;AACH,IAAI,KAAK,EAAE,YAAY;AAIvB,QAAQ,UAAU,GAAG,IAAI,sBAAsB,CAAC,UAAU,CAAC,kBAAkB,IAAI,IAAI,EAA4C,CAAC;AAClI,KAAK;AACL,CAAC,EAAE;AACH,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAClC,IAAI,mBAAmB,CAAC,oBAAoB,EAAE,YAAY,EAAE,OAAO,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvF,CAAC;AAGD,IAAI,UAAU,CAAC;AACf,kBAAkB,CAAC,KAAK,EAAE,CAAC;AAO3B,SAAS,oBAAoB,CAAC,GAAG,EAAE,KAAK,EAAE;AAC1C,IAAI,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC5C,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAIjD,QAAQ,IAAI,KAAK,KAAK,MAAM,CAAC,SAAS,IAAI,KAAK,KAAK,IAAI,EAAE;AAC1D,YAAY,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAG1C,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;AAC3C,gBAAgB,OAAO,KAAK,CAAC;AAC7B,YAAY,IAAI,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACnD,YAAY,IAAI,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACzD,YAAY,IAAI,CAAC,UAAU,EAAE;AAC7B,gBAAgB,IAAI,CAAC,IAAI,EAAE,CAAC;AAC5B,gBAAgB,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAGrD,gBAAgB,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;AAC/D,gBAAgB,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;AACxD,gBAAgB,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AACtD,aAAa;AACb,YAAY,IAAI,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAGtD,YAAY,UAAU,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AAC9C,gBAAgB,cAAc,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;AACjD,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,cAAc,CAAC;AAClC,SAAS;AACT,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AAKD,SAAS,eAAe,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE;AACvC,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC;AACzC;;AC9EO,SAAS,aAAa,CAAC,EAAE,EAAE;AAClC,IAAI,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;AACjC,CAAC;AACM,SAAS,WAAW,CAAC,GAAG,EAAE;AACjC,IAAI,OAAO,OAAO,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC;AACpF,CAAC;AACM,SAAS,cAAc,CAAC,KAAK,EAAE;AACtC,IAAI,QAAQ,eAAe,CAAC,KAAK,CAAC;AAClC,QAAQ,KAAK,CAAC,IAAI,KAAK,UAAU;AACjC,QAAQ,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;AAC1C,CAAC;AACD,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC;AACxC,CAAC;AACD,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,cAAc,CAAC;AACzC,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AACrC,CAAC;AACD,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC;AACvC,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AACrC,CAAC;AACD,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC;AACxC,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,WAAW,CAAC;AACtC,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,WAAW,CAAC;AACtC,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,WAAW,CAAC;AACtC,CAAC;AACM,SAAS,2BAA2B,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;AAC5E,IAAI,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;AAClD,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACjD,KAAK;AACL,SAAS,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;AAC5D,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;AACzC,KAAK;AACL,SAAS,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;AACnC,QAAQ,IAAI,cAAc,GAAG,EAAE,CAAC;AAChC,QAAQ,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;AACxC,YAAY,OAAO,2BAA2B,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;AAC/F,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC;AAC5C,KAAK;AACL,SAAS,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE;AAChC,QAAQ,IAAI,aAAa,GAAG,CAAC,SAAS,IAAI,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChE,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC;AAC3C,KAAK;AACL,SAAS,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;AACjC,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,SAAS,EAAE;AACnE,YAAY,IAAI,iBAAiB,GAAG,EAAE,CAAC;AACvC,YAAY,2BAA2B,CAAC,iBAAiB,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AACvF,YAAY,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACjD,SAAS,CAAC,CAAC;AACX,KAAK;AACL,SAAS,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;AACjC,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;AACzC,KAAK;AACL,SAAS,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE;AACjC,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAClC,KAAK;AACL,SAAS;AACT,QAAQ,MAAML,yBAAiB,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;AAC5D,KAAK;AACL,CAAC;AACM,SAAS,qBAAqB,CAAC,KAAK,EAAE,SAAS,EAAE;AACxD,IAAI,IAAI,aAAa,GAAG,IAAI,CAAC;AAC7B,IAAI,IAAI,KAAK,CAAC,UAAU,EAAE;AAC1B,QAAQ,aAAa,GAAG,EAAE,CAAC;AAC3B,QAAQ,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AACtD,YAAY,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;AACrD,YAAY,IAAI,SAAS,CAAC,SAAS,EAAE;AACrC,gBAAgB,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;AAC1D,oBAAoB,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;AACzD,oBAAoB,OAAO,2BAA2B,CAAC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;AACpH,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC;AACtB,IAAI,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE;AACnD,QAAQ,MAAM,GAAG,EAAE,CAAC;AACpB,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;AAC9C,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;AACjD,YAAY,OAAO,2BAA2B,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;AAC/E,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;AACpE,CAAC;AACD,IAAI,gBAAgB,GAAG;AACvB,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,CAAC,CAAC;AAIF,IAAI,qBAAqB,GAAG,kBAAkB,CAAC;AACrC,IAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE;AAClF,IAAI,IAAI,IAAI;AACZ,QAAQ,UAAU;AAClB,QAAQ,UAAU,CAAC,YAAY,CAAC;AAChC,QAAQ,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,EAAE;AACzC,QAAQ,IAAI,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC;AAC9C,YAAY,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3D,YAAY,IAAI,UAAU,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC;AAC/D,gBAAgB,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC;AAClD,kBAAkB,EAAE,CAAC;AACrB,YAAY,UAAU,CAAC,IAAI,EAAE,CAAC;AAC9B,YAAY,IAAI,cAAc,GAAG,EAAE,CAAC;AACpC,YAAY,UAAU,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AAC9C,gBAAgB,cAAc,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AAChD,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC,CAAC;AACtH,SAAS;AACT,aAAa;AACb,YAAY,OAAO,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC;AACnD,SAAS;AACT,KAAK;AACL,IAAI,IAAI,iBAAiB,GAAG,SAAS,CAAC;AACtC,IAAI,IAAI,IAAI,EAAE;AAId,QAAQ,IAAI,eAAe,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;AAC1D,QAAQ,iBAAiB,IAAI,GAAG,CAAC,MAAM,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;AAC9D,KAAK;AACL,IAAI,IAAI,UAAU,EAAE;AACpB,QAAQ,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AACvD,YAAY,IAAI,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACpD,gBAAgB,OAAO;AACvB,YAAY,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE;AACxE,gBAAgB,iBAAiB,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC9G,aAAa;AACb,iBAAiB;AACjB,gBAAgB,iBAAiB,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACrD,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,iBAAiB,CAAC;AAC7B,CAAC,EAAE;AACH,IAAI,YAAY,EAAE,UAAU,CAAC,EAAE;AAC/B,QAAQ,IAAI,QAAQ,GAAG,qBAAqB,CAAC;AAC7C,QAAQ,qBAAqB,GAAG,CAAC,CAAC;AAClC,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK;AACL,CAAC,EAAE;AACI,SAAS,wBAAwB,CAAC,KAAK,EAAE,SAAS,EAAE;AAC3D,IAAI,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE;AACnD,QAAQ,IAAI,QAAQ,GAAG,EAAE,CAAC;AAC1B,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;AAC9C,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;AACjD,YAAY,OAAO,2BAA2B,CAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;AACjF,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACM,SAAS,sBAAsB,CAAC,KAAK,EAAE;AAC9C,IAAI,OAAO,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9D,CAAC;AACM,SAAS,qBAAqB,CAAC,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE;AACzE,IAAI,IAAI,SAAS,CAAC;AAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,YAAY,CAAC,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACzE,QAAQ,IAAI,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/B,QAAQ,IAAI,OAAO,CAAC,SAAS,CAAC,EAAE;AAChC,YAAY,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY,EAAE;AACvD,gBAAgB,OAAO,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAC;AACjE,aAAa;AACb,SAAS;AACT,aAAa,IAAI,SAAS,EAAE;AAC5B,YAAY,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACtC,SAAS;AACT,aAAa;AACb,YAAY,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;AACpC,SAAS;AACT,KAAK;AACL,IAAI,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,EAAE;AAC/C,QAAQ,OAAO,MAAM,CAAC,UAAU,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,SAAS,EAAE;AACnB,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,WAAW,GAAG,SAAS,EAAE,EAAE,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACjF,YAAY,IAAI,SAAS,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;AAC5C,YAAY,IAAI,QAAQ,GAAG,qBAAqB,CAAC,MAAM,EAAE,wBAAwB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AACrI,YAAY,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AAC9C,gBAAgB,OAAO,QAAQ,CAAC;AAChC,aAAa;AACb,SAAS;AACT,KAAK;AACL,CAAC;AACM,SAAS,OAAO,CAAC,SAAS,EAAE;AACnC,IAAI,OAAO,SAAS,CAAC,IAAI,KAAK,OAAO,CAAC;AACtC,CAAC;AACM,SAAS,gBAAgB,CAAC,SAAS,EAAE;AAC5C,IAAI,OAAO,SAAS,CAAC,IAAI,KAAK,gBAAgB,CAAC;AAC/C;;AChNO,SAAS,aAAa,CAAC,GAAG,EAAE;AACnC,IAAIL,iBAAS,CAAC,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,UAAU,EAAE,EAAE,CAAC,CAAC;AAClD,IAAI,IAAI,UAAU,GAAG,GAAG,CAAC,WAAW;AACpC,SAAS,MAAM,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,EAAE,CAAC;AACzE,SAAS,GAAG,CAAC,UAAU,UAAU,EAAE;AACnC,QAAQ,IAAI,UAAU,CAAC,IAAI,KAAK,qBAAqB,EAAE;AACvD,YAAY,MAAMK,yBAAiB,CAAC,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;AACzD,SAAS;AACT,QAAQ,OAAO,UAAU,CAAC;AAC1B,KAAK,CAAC,CAAC;AACP,IAAIL,iBAAS,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;AAC7D,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACM,SAAS,sBAAsB,CAAC,GAAG,EAAE;AAC5C,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC;AACvB,IAAI,OAAO,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,UAAU,EAAE;AACxD,QAAQ,OAAO,UAAU,CAAC,IAAI,KAAK,qBAAqB,CAAC;AACzD,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,CAAC;AACM,SAAS,gBAAgB,CAAC,GAAG,EAAE;AACtC,IAAI,QAAQ,GAAG,CAAC,WAAW;AAC3B,SAAS,MAAM,CAAC,UAAU,UAAU,EAAE;AACtC,QAAQ,OAAO,UAAU,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC;AAC9E,KAAK,CAAC;AACN,SAAS,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;AAChE,CAAC;AAEM,SAAS,sBAAsB,CAAC,GAAG,EAAE;AAC5C,IAAI,OAAO,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,UAAU,EAAE;AACxD,QAAQ,OAAO,UAAU,CAAC,IAAI,KAAK,oBAAoB,CAAC;AACxD,KAAK,CAAC,CAAC;AACP,CAAC;AACM,SAAS,kBAAkB,CAAC,GAAG,EAAE;AACxC,IAAI,IAAI,QAAQ,GAAG,sBAAsB,CAAC,GAAG,CAAC,CAAC;AAC/C,IAAIA,iBAAS,CAAC,QAAQ,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAAE,EAAE,CAAC,CAAC;AAC9D,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC;AACM,SAAS,qBAAqB,CAAC,GAAG,EAAE;AAC3C,IAAIA,iBAAS,CAAC,GAAG,CAAC,IAAI,KAAK,UAAU,EAAE,EAAE,CAAC,CAAC;AAC3C,IAAIA,iBAAS,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/C,IAAI,IAAI,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;AACzC,IAAIA,iBAAS,CAAC,WAAW,CAAC,IAAI,KAAK,oBAAoB,EAAE,EAAE,CAAC,CAAC;AAC7D,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC;AAMM,SAAS,iBAAiB,CAAC,QAAQ,EAAE;AAC5C,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC5B,IAAI,IAAI,kBAAkB,CAAC;AAC3B,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,QAAQ,CAAC,WAAW,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtE,QAAQ,IAAI,UAAU,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;AAChC,QAAQ,IAAI,UAAU,CAAC,IAAI,KAAK,qBAAqB,EAAE;AACvD,YAAY,IAAI,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;AACjD,YAAY,IAAI,SAAS,KAAK,OAAO;AACrC,gBAAgB,SAAS,KAAK,UAAU;AACxC,gBAAgB,SAAS,KAAK,cAAc,EAAE;AAC9C,gBAAgB,OAAO,UAAU,CAAC;AAClC,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,UAAU,CAAC,IAAI,KAAK,oBAAoB,IAAI,CAAC,kBAAkB,EAAE;AAG7E,YAAY,kBAAkB,GAAG,UAAU,CAAC;AAC5C,SAAS;AACT,KAAK;AACL,IAAI,IAAI,kBAAkB,EAAE;AAC5B,QAAQ,OAAO,kBAAkB,CAAC;AAClC,KAAK;AACL,IAAI,MAAMK,yBAAiB,CAAC,EAAE,CAAC,CAAC;AAChC,CAAC;AACM,SAAS,gBAAgB,CAAC,UAAU,EAAE;AAC7C,IAAI,IAAI,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,IAAI,IAAI,IAAI,GAAG,UAAU,IAAI,UAAU,CAAC,mBAAmB,CAAC;AAC5D,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AAC7B,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AACpC,YAAY,IAAI,GAAG,CAAC,YAAY,EAAE;AAClC,gBAAgB,2BAA2B,CAAC,aAAa,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC;AAChG,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,aAAa,CAAC;AACzB;;AChFA,SAAS,QAAQ,CAAC,QAAQ,EAAE;AAC5B,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC;AACE,IAAC,iBAAiB,KAAkB,YAAY;AACnD,IAAI,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AACnD,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAClE,QAAQ,IAAI,CAAC,WAAW,GAAG,aAAa,GAAG,IAAI,OAAO,EAAE,GAAG,IAAI,GAAG,EAAE,CAAC;AACrE,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,OAAO,CAAC,WAAW,EAAE;AAEjC,YAAY,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AACnD,SAAS;AACT,QAAQ,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;AAC9C,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,KAAK;AAML,IAAI,iBAAiB,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,QAAQ,EAAE;AAClE,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC1B,KAAK,CAAC;AACN,IAAI,iBAAiB,CAAC,QAAQ,GAAG,YAAY;AAI7C,QAAQ,OAAO,IAAI,iBAAiB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACjE,KAAK,CAAC;AACN,IAAI,iBAAiB,CAAC,KAAK,GAAG,UAAU,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE;AAChE,QAAQ,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,iBAAiB,CAAC,QAAQ,EAAE,CAAC,EAAE;AACvE,QAAQ,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,iBAAiB,CAAC,UAAU,QAAQ,EAAE;AACvE,YAAY,IAAI,iBAAiB,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC;AACvE,YAAY,OAAO,iBAAiB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACjE,SAAS;AAET,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACzD,KAAK,CAAC;AAIN,IAAI,iBAAiB,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;AACzD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE;AACzB,YAAY,IAAI,iBAAiB,GAAG,IAAIM,SAAI,CAAC,aAAa,CAAC,CAAC;AAC5D,YAAY,IAAI,CAAC,WAAW,GAAGC,aAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACxF,gBAAgB,YAAY,EAAE,UAAU,QAAQ,EAAE;AAClD,oBAAoB,IAAI,SAAS,GAAG,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAChE,oBAAoB,IAAI,SAAS,EAAE;AACnC,wBAAwBZ,iBAAS,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC,CAAC;AAChE,wBAAwB,OAAO,iBAAiB,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACxE,qBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,GAAG,EAAE,UAAU,CAAC,yBAAyB,CAAC;AAC1D,gBAAgB,KAAK,GAAGQ,gBAAS,CAAC;AAClC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,KAAK,CAAC;AACN,IAAI,iBAAiB,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,QAAQ,EAAE;AAClE,QAAQ,aAAa,CAAC,QAAQ,CAAC,CAAC;AAChC,QAAQ,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACxC,KAAK,CAAC;AACN,IAAI,iBAAiB,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,QAAQ,EAAE;AAGxE,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC5C,YAAY,OAAO,QAAQ,CAAC;AAC5B,SAAS;AACT,QAAQ,IAAI,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC7D,QAAQ,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AAClD,QAAQ,OAAO,mBAAmB,CAAC;AACnC,KAAK,CAAC;AACN,IAAI,iBAAiB,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,cAAc,EAAE;AACnE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,iBAAiB,CAAC,UAAU,QAAQ,EAAE;AACvE,YAAY,OAAO,cAAc,CAAC,iBAAiB,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvF,SAAS;AAET,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE;AAC3B,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,KAAK,EAAE,cAAc;AACjC,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,OAAO,iBAAiB,CAAC;AAC7B,CAAC,EAAE;;ACxFH,IAAI,UAAU,CAAC;AACL,IAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE;AAChD,IAAI,IAAI,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,QAAQ,MAAM,GAAGK,aAAS,CAAC,GAAG,CAAC,CAAC;AAChC,QAAQ,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC,EAAE;AACH,IAAI,KAAK,EAAE,YAAY;AACvB,QAAQ,UAAU,GAAG,IAAI,oBAAoB,CAAC,UAAU,CAAC,KAAK,IAAI,IAAI,EAA+B,CAAC;AACtG,KAAK;AACL,CAAC,EAAE;AACH,KAAK,CAAC,KAAK,EAAE,CAAC;AACd,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAClC,IAAI,mBAAmB,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,UAAU,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;AAC7F;;AClBU,IAAC,OAAO,GAAG,KAAK,CAAC,QAAQ;AAC5B,SAAS,eAAe,CAAC,KAAK,EAAE;AACvC,IAAI,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACpD;;ACGA,IAAI,cAAc,GAAG;AACrB,IAAI,IAAI,EAAEV,YAAI,CAAC,KAAK;AACpB,IAAI,IAAI,EAAE;AACV,QAAQ,IAAI,EAAEA,YAAI,CAAC,IAAI;AACvB,QAAQ,KAAK,EAAE,YAAY;AAC3B,KAAK;AACL,CAAC,CAAC;AACF,SAAS,OAAO,CAAC,EAAE,EAAE,WAAW,EAAE;AAClC,IAAI,QAAQ,CAAC,EAAE;AACf,QAAQ,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,SAAS,EAAE;AAC9D,YAAY,OAAO,SAAS,CAAC,IAAI,KAAKA,YAAI,CAAC,eAAe;AAC1D,gBAAgB,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC,CAAC;AACxE,SAAS,CAAC,EAAE;AACZ,CAAC;AACD,SAAS,gBAAgB,CAAC,GAAG,EAAE;AAC/B,IAAI,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,CAAC,EAAE,iBAAiB,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9H,QAAQ,IAAI;AACZ,UAAU,GAAG,CAAC;AACd,CAAC;AACD,SAAS,mBAAmB,CAAC,OAAO,EAAE;AACtC,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;AAC1B,IAAI,IAAI,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;AAC1B,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AACzC,QAAQ,IAAI,SAAS,EAAE;AACvB,YAAY,IAAI,SAAS,CAAC,IAAI,EAAE;AAChC,gBAAgB,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACrD,aAAa;AACb,iBAAiB,IAAI,SAAS,CAAC,IAAI,EAAE;AACrC,gBAAgB,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACrD,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,UAAU,SAAS,EAAE;AAChC,QAAQ,IAAI,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrD,QAAQ,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE;AACnC,YAAY,KAAK,CAAC,OAAO,CAAC,UAAU,UAAU,EAAE,IAAI,EAAE;AACtD,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE;AACrC,oBAAoB,MAAM,GAAG,UAAU,CAAC;AACxC,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,CAAC;AACN,CAAC;AACD,SAAS,uBAAuB,CAAC,UAAU,EAAE;AAC7C,IAAI,IAAI,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC;AACxB,IAAI,OAAO,SAAS,mBAAmB,CAAC,GAAG,EAAE;AAC7C,QAAQ,IAAI,GAAG,KAAK,KAAK,CAAC,EAAE,EAAE,GAAG,GAAG,UAAU,CAAC,EAAE;AACjD,QAAQ,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,KAAK,EAAE;AACpB,YAAY,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,GAAG;AAKlC,gBAAgB,SAAS,EAAE,IAAI,GAAG,EAAE;AACpC,gBAAgB,eAAe,EAAE,IAAI,GAAG,EAAE;AAC1C,aAAa,EAAE,CAAC;AAChB,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,CAAC;AACM,SAAS,4BAA4B,CAAC,UAAU,EAAE,GAAG,EAAE;AAC9D,IAAI,aAAa,CAAC,GAAG,CAAC,CAAC;AAKvB,IAAI,IAAI,uBAAuB,GAAG,uBAAuB,CAAC,EAAE,CAAC,CAAC;AAC9D,IAAI,IAAI,sBAAsB,GAAG,uBAAuB,CAAC,EAAE,CAAC,CAAC;AAC7D,IAAI,IAAI,QAAQ,GAAG,UAAU,SAAS,EAAE;AACxC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,KAAK,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;AACnG,YAAY,IAAI,OAAO,CAAC,QAAQ,CAAC;AACjC,gBAAgB,SAAS;AACzB,YAAY,IAAI,QAAQ,CAAC,IAAI,KAAKA,YAAI,CAAC,oBAAoB,EAAE;AAE7D,gBAAgB,OAAO,uBAAuB,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrF,aAAa;AACb,YAAY,IAAI,QAAQ,CAAC,IAAI,KAAKA,YAAI,CAAC,mBAAmB,EAAE;AAC5D,gBAAgB,OAAO,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnE,aAAa;AACb,SAAS;AACT,QAAQ,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIH,iBAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAC5D,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC;AAC3B,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AAC1D,QAAQ,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,KAAKG,YAAI,CAAC,oBAAoB,EAAE;AACnE,YAAY,EAAE,cAAc,CAAC;AAC7B,SAAS;AACT,KAAK;AACL,IAAI,IAAI,gBAAgB,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;AAC3D,IAAI,IAAI,iBAAiB,GAAG,UAAU,cAAc,EAAE;AACtD,QAAQ,OAAO,eAAe,CAAC,cAAc,CAAC;AAC9C,YAAY,cAAc;AAC1B,iBAAiB,GAAG,CAAC,gBAAgB,CAAC;AACtC,iBAAiB,IAAI,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC7E,KAAK,CAAC;AACN,IAAI,IAAI,0BAA0B,GAAG,IAAI,GAAG,EAAE,CAAC;AAM/C,IAAI,IAAI,qBAAqB,GAAG,KAAK,CAAC;AACtC,IAAI,IAAI,4BAA4B,GAAG;AACvC,QAAQ,KAAK,EAAE,UAAU,IAAI,EAAE;AAC/B,YAAY,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACpD,gBAAgB,qBAAqB,GAAG,IAAI,CAAC;AAC7C,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAI,2BAA2B,GAAGF,aAAK,CAAC,GAAG,EAAE;AAEjD,QAAQ,KAAK,EAAE,4BAA4B;AAC3C,QAAQ,cAAc,EAAE,4BAA4B;AACpD,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,KAAK,EAAE,YAAY;AAK/B,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa;AACb,SAAS;AACT,QAAQ,QAAQ,EAAE;AAClB,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;AACpE,gBAAgB,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;AAChD,gBAAgB,IAAI,KAAK,EAAE;AAC3B,oBAAoB,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzD,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,cAAc,EAAE;AACxB,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;AACpE,gBAAgB,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;AACxD,oBAAoB,qBAAqB,GAAG,IAAI,CAAC;AACjD,oBAAoB,OAAO,IAAI,CAAC;AAChC,iBAAiB;AACjB,gBAAgB,IAAI,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;AAChD,gBAAgB,IAAI,KAAK,EAAE;AAC3B,oBAAoB,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/D,iBAAiB;AAMjB,aAAa;AACb,SAAS;AACT,QAAQ,kBAAkB,EAAE;AAC5B,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;AACxD,gBAAgB,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;AAC3E,aAAa;AACb,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;AACxD,gBAAgB,IAAI,YAAY,GAAG,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AACxF,gBAAgB,IAAI,IAAI,KAAK,YAAY,EAAE;AAO3C,oBAAoB,OAAO,IAAI,CAAC;AAChC,iBAAiB;AACjB,gBAAgB;AAIhB,gBAAgB,cAAc,GAAG,CAAC;AAClC,oBAAoB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,SAAS,EAAE;AAC5E,wBAAwB,OAAO,SAAS,CAAC,IAAI,KAAKE,YAAI,CAAC,KAAK;AAC5D,4BAA4B,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC;AAClE,qBAAqB,CAAC,EAAE;AAIxB,oBAAoB,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3E,oBAAoB,qBAAqB,GAAG,IAAI,CAAC;AACjD,oBAAoB,OAAO,IAAI,CAAC;AAChC,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,SAAS,EAAE;AACnB,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE;AAInC,gBAAgB,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAC5C,oBAAoB,qBAAqB,GAAG,IAAI,CAAC;AACjD,oBAAoB,OAAO,IAAI,CAAC;AAChC,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAGhC,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK;AAML,IAAI,IAAI,sBAAsB,GAAG,UAAU,KAAK,EAAE;AAClD,QAAQ,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE;AACnC,YAAY,KAAK,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;AAC5D,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AAChC,gBAAgB,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,iBAAiB,EAAE;AAC3E,oBAAoB,sBAAsB,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE;AAChI,wBAAwB,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC1D,qBAAqB,CAAC,CAAC;AACvB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AAIN,IAAI,IAAI,oBAAoB,GAAG,IAAI,GAAG,EAAE,CAAC;AACzC,IAAI,2BAA2B,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AACnE,QAAQ,IAAI,GAAG,CAAC,IAAI,KAAKA,YAAI,CAAC,oBAAoB,EAAE;AACpD,YAAY,sBAAsB,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,iBAAiB,EAAE;AAC7I,gBAAgB,oBAAoB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AAC5D,aAAa,CAAC,CAAC;AACf,SAAS;AACT,aAAa,IAAI,GAAG,CAAC,IAAI,KAAKA,YAAI,CAAC,mBAAmB;AAKtD,YAAY,cAAc,KAAK,CAAC;AAChC,YAAY,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE;AAC7D,YAAY,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACrD,SAAS;AACT,KAAK,CAAC,CAAC;AAIP,IAAI,oBAAoB,CAAC,OAAO,CAAC,UAAU,YAAY,EAAE;AAGzD,QAAQ,sBAAsB,CAAC,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,iBAAiB,EAAE;AAC1H,YAAY,oBAAoB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACxD,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,qBAAqB,GAAG,UAAU,YAAY,EAAE;AACxD,QAAQ,OAAO,CAAC;AAIhB,SAAS,CAAC,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC;AAChD,YAAY,sBAAsB,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC;AAC3D,KAAK,CAAC;AACN,IAAI,IAAI,YAAY,GAAG;AACvB,QAAQ,KAAK,EAAE,UAAU,IAAI,EAAE;AAC/B,YAAY,IAAI,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACxD,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,OAAO,gBAAgB,CAACF,aAAK,CAAC,2BAA2B,EAAE;AAG/D,QAAQ,cAAc,EAAE,YAAY;AAEpC,QAAQ,kBAAkB,EAAE,YAAY;AACxC,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE;AAGnC,gBAAgB,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC9C,oBAAoB,IAAI,mBAAmB,GAAG,sBAAsB;AAEpE,oBAAoB,uBAAuB,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,cAAc,CAAC;AAY1F,oBAAoB,IAAI,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE;AACpF,wBAAwB,OAAOK,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,MAAM,EAAE;AACrI,gCAAgC,OAAO,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3F,6BAA6B,CAAC,EAAE,CAAC,CAAC;AAClC,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC,CAAC;AACR,CAAC;AACS,IAAC,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE;AAChE,IAAI,OAAOL,aAAK,CAAC,GAAG,EAAE;AACtB,QAAQ,YAAY,EAAE;AACtB,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAEjD,gBAAgB,IAAI,MAAM;AAC1B,oBAAoB,MAAM,CAAC,IAAI;AAC/B,wBAAwBE,YAAI,CAAC,oBAAoB,EAAE;AACnD,oBAAoB,OAAO;AAC3B,iBAAiB;AAEjB,gBAAgB,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACjD,gBAAgB,IAAI,CAAC,UAAU,EAAE;AACjC,oBAAoB,OAAO;AAC3B,iBAAiB;AAGjB,gBAAgB,IAAI,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,SAAS,EAAE;AAChE,oBAAoB,QAAQ,OAAO,CAAC,SAAS,CAAC;AAC9C,yBAAyB,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY;AAC9D,4BAA4B,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;AAC9E,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,IAAI,IAAI,EAAE;AAC1B,oBAAoB,OAAO;AAC3B,iBAAiB;AAGjB,gBAAgB,IAAI,KAAK,GAAG,MAAM,CAAC;AACnC,gBAAgB,IAAI,OAAO,CAAC,KAAK,CAAC;AAClC,oBAAoB,KAAK,CAAC,UAAU;AACpC,oBAAoB,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC,EAAE;AAC/F,oBAAoB,OAAO;AAC3B,iBAAiB;AAEjB,gBAAgB,OAAOG,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAEC,mBAAa,CAACA,mBAAa,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;AACjJ,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AACP,CAAC,EAAE;AACH,IAAI,KAAK,EAAE,UAAU,KAAK,EAAE;AAC5B,QAAQ,OAAO,KAAK,KAAK,cAAc,CAAC;AACxC,KAAK;AACL,CAAC,EAAE;AACH,IAAI,sBAAsB,GAAG;AAC7B,IAAI,IAAI,EAAE,UAAU,SAAS,EAAE;AAC/B,QAAQ,IAAI,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC;AAC/D,QAAQ,IAAI,UAAU,EAAE;AACxB,YAAY,IAAI,CAAC,SAAS,CAAC,SAAS;AACpC,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE;AAChG,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIP,iBAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnE,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,UAAU,CAAC;AAC1B,KAAK;AACL,CAAC,CAAC;AACK,SAAS,qCAAqC,CAAC,GAAG,EAAE;AAC3D,IAAI,OAAO,4BAA4B,CAAC,CAAC,sBAAsB,CAAC,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;AACtF,CAAC;AAqBD,SAAS,kBAAkB,CAAC,MAAM,EAAE;AACpC,IAAI,OAAO,SAAS,eAAe,CAAC,QAAQ,EAAE;AAC9C,QAAQ,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,OAAO,EAAE;AAC9C,YAAY,OAAO,QAAQ,CAAC,KAAK;AACjC,gBAAgB,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAKG,YAAI,CAAC,QAAQ;AACrD,gBAAgB,QAAQ,CAAC,KAAK,CAAC,IAAI;AACnC,iBAAiB,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;AAC3D,qBAAqB,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9D,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,CAAC;AACM,SAAS,2BAA2B,CAAC,MAAM,EAAE,GAAG,EAAE;AACzD,IAAI,IAAI,UAAU,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;AAChD,IAAI,OAAO,gBAAgB,CAACF,aAAK,CAAC,GAAG,EAAE;AACvC,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE;AACnC,gBAAgB,OAAOK,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;AAEpD,oBAAoB,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;AACjE,wBAAwB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAU,MAAM,EAAE;AAC1E,4BAA4B,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACpH,yBAAyB,CAAC;AAC1B,0BAA0B,EAAE,EAAE,CAAC,CAAC;AAChC,aAAa;AACb,SAAS;AACT,QAAQ,KAAK,EAAE;AACf,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE;AAGnC,gBAAgB,IAAI,iBAAiB,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,SAAS,EAAE,EAAE,OAAO,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACvG,gBAAgB,IAAI,iBAAiB,EAAE;AACvC,oBAAoB,IAAI,eAAe,GAAG,CAAC,CAAC;AAC5C,oBAAoB,IAAI,IAAI,CAAC,SAAS,EAAE;AACxC,wBAAwB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AAC9D,4BAA4B,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE;AACjD,gCAAgC,eAAe,IAAI,CAAC,CAAC;AACrD,6BAA6B;AAC7B,yBAAyB,CAAC,CAAC;AAC3B,qBAAqB;AACrB,oBAAoB,IAAI,eAAe,KAAK,CAAC,EAAE;AAC/C,wBAAwB,OAAO,IAAI,CAAC;AACpC,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,QAAQ,EAAE;AAClB,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE;AAEnC,gBAAgB,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE;AACtC,oBAAoB,OAAO,IAAI,CAAC;AAChC,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC,CAAC;AACR,CAAC;AACM,SAAS,gCAAgC,CAAC,MAAM,EAAE,GAAG,EAAE;AAC9D,IAAI,SAAS,KAAK,CAAC,IAAI,EAAE;AACzB,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;AAClF,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,KAAK;AACL,IAAI,OAAO,gBAAgB,CAACL,aAAK,CAAC,GAAG,EAAE;AACvC,QAAQ,cAAc,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;AACxC,QAAQ,kBAAkB,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE;AAC5C,KAAK,CAAC,CAAC,CAAC;AACR,CAAC;AAIM,SAAS,0BAA0B,CAAC,QAAQ,EAAE;AACrD,IAAI,IAAI,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACjD,IAAI,IAAI,mBAAmB,GAAG,UAAU,CAAC,SAAS,CAAC;AACnD,IAAI,IAAI,mBAAmB,KAAK,OAAO,EAAE;AAEzC,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK;AAEL,IAAI,IAAI,WAAW,GAAGA,aAAK,CAAC,QAAQ,EAAE;AACtC,QAAQ,mBAAmB,EAAE;AAC7B,YAAY,KAAK,EAAE,UAAU,IAAI,EAAE;AACnC,gBAAgB,OAAOK,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;AAC5E,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC;AAEM,SAAS,4BAA4B,CAAC,QAAQ,EAAE;AACvD,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC5B,IAAI,IAAI,WAAW,GAAG,4BAA4B,CAAC;AACnD,QAAQ;AACR,YAAY,IAAI,EAAE,UAAU,SAAS,EAAE,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,EAAE;AACpF,YAAY,MAAM,EAAE,IAAI;AACxB,SAAS;AACT,KAAK,EAAE,QAAQ,CAAC,CAAC;AACjB,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC;AACM,SAAS,8BAA8B,CAAC,QAAQ,EAAE;AACzD,IAAI,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC5B,IAAI,OAAOL,aAAK,CAAC,QAAQ,EAAE;AAC3B,QAAQ,cAAc,EAAE,UAAU,IAAI,EAAE;AACxC,YAAY,IAAI,EAAE,CAAC;AAGnB,YAAY,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,UAAU,SAAS,EAAE,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC,EAAE;AACzJ,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,OAAOK,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAEC,mBAAa,CAACA,mBAAa,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE;AAC9H,oBAAoB;AACpB,wBAAwB,IAAI,EAAEJ,YAAI,CAAC,SAAS;AAC5C,wBAAwB,IAAI,EAAE,EAAE,IAAI,EAAEA,YAAI,CAAC,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE;AACvE,qBAAqB;AACrB,iBAAiB,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;AAC7B,SAAS;AACT,KAAK,CAAC,CAAC;AACP;;AClfA,SAAS,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE;AAC1C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAO,CAAC,CAAC,EAAE,GAAG,sBAAsB,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,MAAM,SAAS,CAAC;AACrH,CAAC;AACM,SAAS,mBAAmB,CAAC,QAAQ,EAAE;AAC9C,IAAI,OAAO,WAAW,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC7C,CAAC;AACM,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AAC3C,IAAI,OAAO,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAC1C,CAAC;AACM,SAAS,uBAAuB,CAAC,QAAQ,EAAE;AAClD,IAAI,OAAO,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AACjD;;ACXA,IAAI,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AAC9C,SAAS,SAAS,GAAG;AAC5B,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,OAAO,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,OAAO,cAAc,CAAC,OAAO,CAAC,CAAC;AACnC,CAAC;AAOM,SAAS,cAAc,CAAC,OAAO,EAAE;AACxC,IAAI,IAAI,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAClC,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;AAC/B,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE;AACnB,QAAQ,IAAI,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AACtC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE;AACxC,YAAY,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,SAAS;AACT,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AACD,IAAI,iBAAiB,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE;AAC5D,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC1D,CAAC,CAAC;AACC,IAAC,UAAU,KAAkB,YAAY;AAC5C,IAAI,SAAS,UAAU,CAAC,UAAU,EAAE;AACpC,QAAQ,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,iBAAiB,CAAC,EAAE;AACtE,QAAQ,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACrC,QAAQ,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAC;AACxC,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;AACpC,KAAK;AACL,IAAI,UAAU,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE;AAC3D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,OAAO,GAAG,EAAE,CAAC;AACzB,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,OAAO,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AAC5C,SAAS;AACT,QAAQ,IAAI,eAAe,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,MAAM,CAAC,EAAE;AAChE,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAC7D,gBAAgB,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE;AAC5D,oBAAoB,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AACxD,oBAAoB,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,WAAW,EAAE;AAC3D,wBAAwB,IAAI,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAEI,mBAAa,CAAC,CAAC,MAAM;AACxF,4BAA4B,MAAM;AAClC,4BAA4B,SAAS,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;AAGzD,wBAAwB,IAAI,MAAM,KAAK,WAAW,EAAE;AACpD,4BAA4B,MAAM,GAAG,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACvE,4BAA4B,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC;AACvD,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,qBAAqB;AAGrB,oBAAoB,MAAM,GAAG,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;AAC/D,oBAAoB,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAC1D,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,MAAM,CAAC;AAC1B,SAAS;AAET,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,KAAK,EAAE;AAChE,QAAQ,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE;AACpC,YAAY,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC7C,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC1C,oBAAoB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC3C,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,KAAK,GAAGD,cAAQ,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AACzF,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC3C,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,OAAO,UAAU,CAAC;AACtB,CAAC,EAAE;;ACjFI,SAAS,gBAAgB,CAAC,OAAO,EAAE;AAC1C,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,KAAK,CAAC,EAAE;AAChD,IAAI,OAAO;AACX,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,KAAK,EAAE,UAAU,QAAQ,EAAE,QAAQ,EAAE;AAC7C,YAAY,OAAO,QAAQ,GAAGC,mBAAa,CAACA,mBAAa,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC;AAC1G,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AAKM,SAAS,qBAAqB,CAAC,OAAO,EAAE;AAC/C,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,KAAK,CAAC,EAAE;AAChD,IAAI,OAAO;AACX,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,KAAK,EAAE,UAAU,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE;AACjD,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC/B,YAAY,IAAI,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAC3D,YAAY,IAAI,QAAQ,EAAE;AAC1B,gBAAgB,IAAI,IAAI,EAAE;AAE1B,oBAAoB,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC1E,oBAAoB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AAC9D,wBAAwB,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzD,qBAAqB;AACrB,iBAAiB;AACjB,qBAAqB;AAKrB,oBAAoB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACxD,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,MAAM,CAAC;AAC1B,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AAIM,SAAS,oBAAoB,CAAC,OAAO,EAAE;AAC9C,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,KAAK,CAAC,EAAE;AAChD,IAAI,OAAO;AACX,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,IAAI,EAAE,UAAU,QAAQ,EAAE,EAAE,EAAE;AACtC,YAAY,IAAI,OAAO,GAAG,EAAE,CAAC,OAAO,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AAC/D,YAAY,IAAI,CAAC,QAAQ;AACzB,gBAAgB,OAAO,QAAQ,CAAC;AAChC,YAAY,IAAI,KAAK,GAAG,EAAE,CAAC;AAC3B,YAAY,IAAI,eAAe,GAAG,EAAE,CAAC;AACrC,YAAY,IAAI,cAAc,GAAG,EAAE,CAAC;AACpC,YAAY,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AAGnD,gBAAgB,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE;AACtD,oBAAoB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrC,oBAAoB,IAAI,IAAI,CAAC,MAAM,EAAE;AACrC,wBAAwB,eAAe,GAAG,eAAe,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;AAC/E,wBAAwB,cAAc,GAAG,IAAI,CAAC,MAAM,IAAI,cAAc,CAAC;AACvE,qBAAqB;AACrB,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,YAAY,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,KAAK,cAAc,EAAE;AACxE,gBAAgB,eAAe,GAAG,EAAE,CAAC;AACrC,aAAa;AACb,YAAY,IAAI,EAAE,GAAG,QAAQ,CAAC,QAAQ,IAAI,EAAE,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AACrG,YAAY,OAAOD,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAEA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAGnI,oBAAoB,WAAW,EAAE,WAAW,IAAI,eAAe,EAAE,SAAS,EAAE,SAAS,IAAI,cAAc,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9G,SAAS;AACT,QAAQ,KAAK,EAAE,UAAU,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE;AACjD,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AACvF,YAAY,IAAI,CAAC,QAAQ,EAAE;AAC3B,gBAAgB,QAAQ,GAAG,aAAa,EAAE,CAAC;AAC3C,aAAa;AACb,YAAY,IAAI,CAAC,QAAQ,EAAE;AAC3B,gBAAgB,OAAO,QAAQ,CAAC;AAChC,aAAa;AACb,YAAY,IAAI,aAAa,GAAG,QAAQ,CAAC,KAAK;AAC9C,gBAAgB,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;AACnD,oBAAoB,IAAI,WAAW,EAAE,IAAI,GAAGA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAGlE,wBAAwB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAChE,qBAAqB;AACrB,oBAAoB,OAAO,IAAI,CAAC;AAChC,iBAAiB,CAAC;AAClB,kBAAkB,EAAE,CAAC;AACrB,YAAY,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACnC,gBAAgB,IAAI,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC;AACnD,gBAAgB,IAAI,WAAW,GAAG,UAAU,CAAC,WAAW,EAAE,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;AAC3F,gBAAgB,IAAI,SAAS,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AACjD,gBAAgB,IAAI,QAAQ,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAGvE,gBAAgB,IAAI,SAAS,IAAI,WAAW,EAAE;AAC9C,oBAAoB,SAAS,CAAC,MAAM,GAAG,WAAW,CAAC;AACnD,iBAAiB;AACjB,gBAAgB,IAAI,QAAQ,IAAI,SAAS,EAAE;AAC3C,oBAAoB,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;AAChD,iBAAiB;AAGjB,gBAAgB,IAAI,WAAW,GAAG,SAAS,IAAI,SAAS,CAAC,MAAM,CAAC;AAChE,gBAAgB,IAAI,WAAW,IAAI,CAAC,WAAW,EAAE;AACjD,oBAAoB,QAAQ,GAAG,SAAS,CAAC,QAAQ,EAAE;AACnD,wBAAwB,QAAQ,EAAE;AAClC,4BAA4B,WAAW,EAAE,WAAW;AACpD,yBAAyB;AACzB,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,gBAAgB,IAAI,UAAU,GAAG,QAAQ,IAAI,QAAQ,CAAC,MAAM,CAAC;AAC7D,gBAAgB,IAAI,UAAU,IAAI,CAAC,SAAS,EAAE;AAC9C,oBAAoB,QAAQ,GAAG,SAAS,CAAC,QAAQ,EAAE;AACnD,wBAAwB,QAAQ,EAAE;AAClC,4BAA4B,SAAS,EAAE,UAAU;AACjD,yBAAyB;AACzB,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,aAAa;AACb,YAAY,IAAI,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC;AACxC,YAAY,IAAI,MAAM,GAAG,EAAE,CAAC;AAC5B,YAAY,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE;AAIpC,gBAAgB,IAAI,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACrG,gBAAgB,IAAI,KAAK,IAAI,CAAC,EAAE;AAChC,oBAAoB,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AAExD,iBAAiB;AACjB,aAAa;AACb,iBAAiB,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AAC1C,gBAAgB,IAAI,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,UAAU,IAAI,EAAE,EAAE,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACtG,gBAAgB,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAClE,gBAAgB,MAAM,GAAG,EAAE,CAAC;AAC5B,aAAa;AACb,iBAAiB,IAAI,QAAQ,CAAC,KAAK,EAAE;AAIrC,gBAAgB,MAAM,GAAG,EAAE,CAAC;AAC5B,aAAa;AACb,YAAY,IAAI,KAAK,GAAGC,mBAAa,CAACA,mBAAa,CAACA,mBAAa,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AACzH,YAAY,IAAI,QAAQ,GAAGD,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACxF,YAAY,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACnC,gBAAgB,IAAI,EAAE,GAAG,QAAQ,CAAC,QAAQ,EAAE,eAAe,GAAG,EAAE,CAAC,eAAe,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,MAAM,GAAGQ,YAAQ,CAAC,EAAE,EAAE,CAAC,iBAAiB,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC,CAAC;AAK9P,gBAAgB,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAQhD,gBAAgB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACpC,oBAAoB,IAAI,KAAK,CAAC,KAAK,eAAe;AAClD,wBAAwB,QAAQ,CAAC,eAAe,GAAG,eAAe,CAAC;AACnE,oBAAoB,IAAI,KAAK,CAAC,KAAK,WAAW;AAC9C,wBAAwB,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;AAC3D,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACpC,oBAAoB,IAAI,KAAK,CAAC,KAAK,WAAW;AAC9C,wBAAwB,QAAQ,CAAC,WAAW,GAAG,WAAW,CAAC;AAC3D,oBAAoB,IAAI,KAAK,CAAC,KAAK,SAAS;AAC5C,wBAAwB,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;AACvD,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAOR,cAAQ,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC;AACpI,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AAED,IAAI,SAAS,GAAG,UAAU,GAAG,EAAE,EAAE,OAAOS,YAAM,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;AAClE,IAAI,SAAS,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;AACtC,SAAS,aAAa,GAAG;AACzB,IAAI,OAAO;AACX,QAAQ,KAAK,EAAE,EAAE;AACjB,QAAQ,QAAQ,EAAE;AAClB,YAAY,eAAe,EAAE,KAAK;AAClC,YAAY,WAAW,EAAE,IAAI;AAC7B,YAAY,WAAW,EAAE,EAAE;AAC3B,YAAY,SAAS,EAAE,EAAE;AACzB,SAAS;AACT,KAAK,CAAC;AACN;;ACtMO,SAAS,sBAAsB,CAAC,KAAK,EAAE;AAC9C,IAAI,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACzC,IAAI,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;AACjC,IAAI,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAC1B,IAAI,OAAO,OAAO,CAAC;AACnB,CAAC;AACM,SAAS,qBAAqB,CAAC,MAAM,EAAE;AAC9C,IAAI,IAAI,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAEzC,IAAI,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;AACnC,IAAI,OAAO,CAAC,MAAM,GAAG,UAAU,CAAC;AAChC,IAAI,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAC5B,IAAI,OAAO,OAAO,CAAC;AACnB,CAAC;AACM,SAAS,iBAAiB,CAAC,OAAO,EAAE;AAC3C,IAAI,OAAO,QAAQ,IAAI,OAAO,CAAC;AAC/B,CAAC;AACM,SAAS,oBAAoB,CAAC,OAAO,EAAE;AAC9C,IAAI,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE;AACpC,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,cAAc,GAAG,OAAO,CAAC;AACjC,IAAI,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC;AACtC,IAAI,cAAc,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE;AACzC,QAAQ,IAAI,cAAc,CAAC,MAAM,KAAK,SAAS,EAAE;AACjD,YAAY,IAAI,gBAAgB,GAAG,cAAc,CAAC;AAClD,YAAY,gBAAgB,CAAC,MAAM,GAAG,WAAW,CAAC;AAClD,YAAY,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3C,SAAS;AACT,KAAK,EAAE,UAAU,MAAM,EAAE;AACzB,QAAQ,IAAI,cAAc,CAAC,MAAM,KAAK,SAAS,EAAE;AACjD,YAAY,IAAI,eAAe,GAAG,cAAc,CAAC;AACjD,YAAY,eAAe,CAAC,MAAM,GAAG,UAAU,CAAC;AAChD,YAAY,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;AAC5C,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,OAAO,CAAC;AACnB;;ACrCO,SAAS,yBAAyB,CAAC,OAAO,EAAE;AACnD,IAAI,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,CAAC,CAAC;AACnC,IAAI,OAAO,OAAO,CAAC;AACnB;;ACHA,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAIlC,SAAS,SAAS,CAAC,KAAK,EAAE;AACjC,IAAI,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC;AAClC,CAAC;AACD,SAAS,eAAe,CAAC,GAAG,EAAE,IAAI,EAAE;AACpC,IAAI,QAAQ,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;AAC9B,QAAQ,KAAK,gBAAgB,EAAE;AAC/B,YAAY,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AACrC,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAC7B,gBAAgB,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC,YAAY,IAAI,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACtC,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAClC,YAAY,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,CAAC,EAAE;AAC/C,gBAAgB,MAAM,CAAC,CAAC,CAAC,GAAG,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACzD,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,MAAM,CAAC;AAC1B,SAAS;AACT,QAAQ,KAAK,iBAAiB,EAAE;AAChC,YAAY,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AACrC,YAAY,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AAC7B,gBAAgB,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAGrC,YAAY,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;AACnE,YAAY,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAClC,YAAY,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AACpD,gBAAgB,MAAM,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9D,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,MAAM,CAAC;AAC1B,SAAS;AACT,QAAQ;AACR,YAAY,OAAO,GAAG,CAAC;AACvB,KAAK;AACL;;ACnCO,SAAS,UAAU,CAAC,KAAK,EAAE;AAClC,IAAI,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACnC,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AACnC,QAAQ,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;AAChE,YAAY,MAAM,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACpE,gBAAgB,IAAI,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9C,oBAAoB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3C,aAAa,CAAC,CAAC;AACf,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD,SAAS,aAAa,CAAC,GAAG,EAAE;AAC5B,IAAI,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC/D,QAAQ,IAAI;AACZ,YAAY,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC/B,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE;AAIlB,YAAY,IAAI,CAAC,YAAY,SAAS;AACtC,gBAAgB,OAAO,IAAI,CAAC;AAC5B,YAAY,MAAM,CAAC,CAAC;AACpB,SAAS;AACT,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf,CAAC;AACM,SAAS,eAAe,CAAC,GAAG,EAAE;AACrC,IAAI,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AACtC,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,OAAO,GAAG,CAAC;AACf;;AClCO,SAAS,sBAAsB,CAAC,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE;AAIpE,IAAI,IAAI,mBAAmB,GAAG,EAAE,CAAC;AACjC,IAAI,SAAS,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,MAAM,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/F,IAAI,mBAAmB,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;AAClF;;ACJO,SAAS,QAAQ,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;AACrD,IAAI,OAAO,IAAIC,0BAAU,CAAC,UAAU,QAAQ,EAAE;AAC9C,QAAQ,IAAI,YAAY,GAAG;AAI3B,YAAY,IAAI,EAAE,UAAU,QAAQ,EAAE;AACtC,gBAAgB,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACvF,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,SAAS,YAAY,CAAC,QAAQ,EAAE,GAAG,EAAE;AAC7C,YAAY,OAAO,UAAU,GAAG,EAAE;AAClC,gBAAgB,IAAI,QAAQ,EAAE;AAC9B,oBAAoB,IAAI,IAAI,GAAG,YAAY;AAG3C,wBAAwB,OAAO,QAAQ,CAAC,MAAM;AAC9C,6BAAoD,CAAC;AACrD,8BAA8B,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC5C,qBAAqB,CAAC;AACtB,oBAAoB,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,MAAM,EAAE,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,UAAU,KAAK,EAAE,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/K,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACvC,iBAAiB;AACjB,aAAa,CAAC;AACd,SAAS;AACT,QAAQ,IAAI,OAAO,GAAG;AACtB,YAAY,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC;AAC7C,YAAY,KAAK,EAAE,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC;AACjD,YAAY,QAAQ,EAAE,YAAY;AAGlC,iBAAoC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AACnG,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAChD,QAAQ,OAAO,YAAY,EAAE,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;AACzD,KAAK,CAAC,CAAC;AACP;;AChCO,SAAS,qBAAqB,CAAC,QAAQ,EAAE;AAChD,IAAI,SAAS,GAAG,CAAC,GAAG,EAAE;AAItB,QAAQ,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE,EAAE,KAAK,EAAEA,0BAAU,EAAE,CAAC,CAAC;AACpE,KAAK;AACL,IAAI,IAAI,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE;AACxC,QAAQ,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC5B,KAAK;AAIL,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC;AACrB,IAAI,OAAO,QAAQ,CAAC;AACpB;;ACrBA,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AACrD,CAAC;AA8BE,IAAC,OAAO,KAAkB,UAAU,MAAM,EAAE;AAC/C,IAAIC,eAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAG/B,IAAI,SAAS,OAAO,CAAC,OAAO,EAAE;AAC9B,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,QAAQ,EAAE;AAC1D,YAAY,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACxC,YAAY,OAAO,YAAY,EAAE,OAAO,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;AAC1E,SAAS,CAAC,IAAI,IAAI,CAAC;AAInB,QAAQ,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AACpC,QAAQ,KAAK,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AAC/D,YAAY,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AACpC,YAAY,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAClC,SAAS,CAAC,CAAC;AAGX,QAAQ,KAAK,CAAC,QAAQ,GAAG;AACzB,YAAY,IAAI,EAAE,UAAU,MAAM,EAAE;AACpC,gBAAgB,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,EAAE;AACxC,oBAAoB,KAAK,CAAC,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACpD,oBAAoB,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACjD,oBAAoB,sBAAsB,CAAC,KAAK,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAC5E,iBAAiB;AACjB,aAAa;AACb,YAAY,KAAK,EAAE,UAAU,KAAK,EAAE;AACpC,gBAAgB,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;AACpC,gBAAgB,IAAI,GAAG,KAAK,IAAI,EAAE;AAIlC,oBAAoB,IAAI,GAAG;AAC3B,wBAAwB,UAAU,CAAC,YAAY,EAAE,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;AAC9E,oBAAoB,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;AACrC,oBAAoB,KAAK,CAAC,MAAM,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACpD,oBAAoB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACxC,oBAAoB,KAAK,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACjD,oBAAoB,sBAAsB,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC5E,iBAAiB;AACjB,aAAa;AACb,YAAY,QAAQ,EAAE,YAAY;AAClC,gBAAgB,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,OAAO,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AACjG,gBAAgB,IAAI,GAAG,KAAK,IAAI,EAAE;AAMlC,oBAAoB,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;AAChD,oBAAoB,IAAI,CAAC,KAAK,EAAE;AAChC,wBAAwB,IAAI,GAAG;AAC/B,4BAA4B,UAAU,CAAC,YAAY,EAAE,OAAO,GAAG,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC,CAAC;AAClF,wBAAwB,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;AACzC,wBAAwB,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;AACxE,4BAA4B,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3D,yBAAyB;AACzB,6BAA6B;AAC7B,4BAA4B,KAAK,CAAC,OAAO,EAAE,CAAC;AAC5C,yBAAyB;AACzB,wBAAwB,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAOjD,wBAAwB,sBAAsB,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AAC5E,qBAAqB;AACrB,yBAAyB,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;AACnD,wBAAwB,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,EAAE,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACjI,qBAAqB;AACrB,yBAAyB;AACzB,wBAAwB,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpE,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,KAAK,CAAC,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;AAE9C,QAAQ,KAAK,CAAC,MAAM,GAAG,UAAU,MAAM,EAAE;AACzC,YAAY,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjC,YAAY,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;AAC/B,YAAY,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACzC,SAAS,CAAC;AAIV,QAAQ,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,CAAC;AAI9C,QAAQ,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;AAC3C,YAAY,OAAO,GAAG,CAAC,IAAID,0BAAU,CAAC,OAAO,CAAC,CAAC,CAAC;AAChD,SAAS;AACT,QAAQ,IAAI,aAAa,CAAC,OAAO,CAAC,EAAE;AACpC,YAAY,OAAO,CAAC,IAAI,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACtG,SAAS;AACT,aAAa;AACb,YAAY,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACjC,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,EAAE;AACjD,QAAQ,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC;AAC/B,YAAY,OAAO;AAInB,QAAQ,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAK3C,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACjC,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,QAAQ,EAAE;AAC/D,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE;AACzB,YAAY,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7C,YAAY,IAAI,MAAM,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC/C,YAAY,IAAI,MAAM,EAAE;AACxB,gBAAgB,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,aAAa;AAIb,YAAY,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,WAAW,KAAK,MAAM,IAAI,QAAQ,CAAC,QAAQ,EAAE;AAClF,gBAAgB,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACpC,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,QAAQ,EAAE;AACxD,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAG3C,YAAY,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAC9C,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACzC,SAAS;AACT,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,QAAQ,EAAE;AAC3D,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,EAAE;AAKxE,YAAY,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACrC,SAAS;AACT,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,MAAM,EAAE,GAAG,EAAE;AACtD,QAAQ,IAAI,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC3D,QAAQ,IAAI,mBAAmB,CAAC,IAAI,EAAE;AAGtC,YAAY,IAAI,CAAC,mBAAmB,GAAG,IAAI,GAAG,EAAE,CAAC;AACjD,YAAY,mBAAmB,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AAC/F,SAAS;AACT,KAAK,CAAC;AAON,IAAI,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,QAAQ,EAAE;AACvD,QAAQ,IAAI,MAAM,GAAG,KAAK,CAAC;AAC3B,QAAQ,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,MAAM,EAAE,GAAG,EAAE;AAC5D,YAAY,IAAI,CAAC,MAAM,EAAE;AACzB,gBAAgB,MAAM,GAAG,IAAI,CAAC;AAC9B,gBAAgB,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AACtC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,OAAO,OAAO,CAAC;AACnB,CAAC,CAACA,0BAAU,CAAC,EAAE;AAIf,qBAAqB,CAAC,OAAO,CAAC;;ACnNvB,SAAS,iCAAiC,CAAC,KAAK,EAAE;AACzD,IAAI,OAAO,aAAa,IAAI,KAAK,CAAC;AAClC,CAAC;AACM,SAAS,6BAA6B,CAAC,KAAK,EAAE;AACrD,IAAI,OAAO,SAAS,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,CAAC;AACjD,CAAC;AACM,SAAS,sBAAsB,CAAC,KAAK,EAAE;AAC9C,IAAI,QAAQ,iCAAiC,CAAC,KAAK,CAAC;AACpD,QAAQ,6BAA6B,CAAC,KAAK,CAAC,EAAE;AAC9C,CAAC;AAIM,SAAS,qBAAqB,CAAC,KAAK,EAAE;AAC7C,IAAI,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC;AACxD,CAAC;AACM,SAAS,oBAAoB,CAAC,UAAU,EAAE,MAAM,EAAE;AACzD,IAAI,IAAI,UAAU,GAAG,UAAU,CAAC;AAChC,IAAI,IAAI,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AAClC,IAAI,IAAI,iCAAiC,CAAC,MAAM,CAAC;AACjD,QAAQ,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;AAC7C,QAAQ,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;AACjD,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC/C,YAAY,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AACvD,gBAAgB,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAClC,gBAAgB,IAAI,YAAY,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;AAChD,gBAAgB,IAAI,QAAQ,GAAG,YAAY,GAAG,EAAE,GAAG,EAAE,CAAC;AACtD,gBAAgB,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACrC,gBAAgB,IAAI,GAAG,QAAQ,CAAC;AAChC,aAAa;AACb,YAAY,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AACxD,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,UAAU,CAAC;AACtB;;ACnCO,SAAS,qBAAqB,CAAC,MAAM,EAAE;AAC9C,IAAI,IAAI,MAAM,GAAG,0BAA0B,CAAC,MAAM,CAAC,CAAC;AACpD,IAAI,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC;AACnC,CAAC;AACM,SAAS,0BAA0B,CAAC,MAAM,EAAE;AACnD,IAAI,IAAI,aAAa,GAAG,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACrF,IAAI,IAAI,iCAAiC,CAAC,MAAM,CAAC;AACjD,QAAQ,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;AAC7C,QAAQ,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,iBAAiB,EAAE;AAChE,YAAY,IAAI,iBAAiB,CAAC,MAAM,EAAE;AAC1C,gBAAgB,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAClF,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,aAAa,CAAC;AACzB;;ACbO,SAAS,OAAO,GAAG;AAC1B,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,OAAO,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrC,IAAI,OAAO,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AACnC,QAAQ,IAAI,CAAC,GAAG;AAChB,YAAY,OAAO;AACnB,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AAChD,YAAY,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AACjC,YAAY,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE;AAClC,gBAAgB,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;AACpC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM,CAAC;AAClB;;ACrBA,IAAI,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;AAGtB,SAAS,YAAY,CAAC,MAAM,EAAE;AACrC,IAAI,IAAI,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;AACxC,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACjG;;ACNO,SAAS,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE;AAClD,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;AACxC,IAAI,IAAI,OAAO,GAAG,YAAY,CAAC,qBAAqB,CAAC,CAAC;AACtD,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,EAAE,KAAK,EAAE;AACvD,QAAQ,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,KAAK,CAAC;AAClD,KAAK,EAAE,KAAK,CAAC;AACb,SAAS,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACvC,SAAS,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7B;;ACPO,SAAS,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE;AAChD,IAAI,OAAO,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,SAAS,IAAI;AAC3D,QAAQ,SAAS,EAAE,OAAO,CAACV,cAAQ,CAACA,cAAQ,CAAC,EAAE,GAAG,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACvG,KAAK,CAAC,CAAC;AACP;;ACLO,SAAS,QAAQ,CAAC,KAAK,EAAE,GAAG,EAAE;AACrC,IAAI,OAAO,UAAU,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AACD,SAAS,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;AACvC,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE;AAChD,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAC1B,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC9B,QAAQ,IAAI,OAAO,GAAG,EAAE,CAAC;AACzB,QAAQ,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAClC,QAAQ,KAAK,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,KAAK,EAAE;AAC9C,YAAY,IAAI,MAAM,GAAG,UAAU,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AACvD,YAAY,QAAQ,KAAK,QAAQ,GAAG,MAAM,KAAK,KAAK,CAAC,CAAC;AACtD,YAAY,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;AACpC,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,QAAQ,EAAE;AACtB,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS;AACT,KAAK;AACL,SAAS,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE;AACnC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;AAChE,QAAQ,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAChC,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AAChD,YAAY,IAAI,CAAC,KAAK,GAAG,EAAE;AAC3B,gBAAgB,QAAQ,GAAG,IAAI,CAAC;AAChC,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,IAAI,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;AAC1D,YAAY,QAAQ,KAAK,QAAQ,GAAG,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;AAC9B,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,QAAQ,EAAE;AACtB,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB;;ACtCO,SAAS,aAAa,CAAC,KAAK,EAAE;AACrC,IAAI,OAAO,QAAQ,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACzC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}