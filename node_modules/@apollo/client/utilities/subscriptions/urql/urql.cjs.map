{"version": 3, "file": "urql.cjs", "sources": ["../../../link/http/iterators/async.js", "../../../link/http/iterators/nodeStream.js", "../../../link/http/iterators/promise.js", "../../../link/http/iterators/reader.js", "../../../link/http/responseIterator.js", "../../common/objects.js", "../../common/incrementalResult.js", "../../../link/http/parseAndCheckHttpResponse.js", "../../../link/http/selectHttpOptionsAndBody.js", "../shared.js", "index.js"], "sourcesContent": ["/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/async.ts\n */\nexport default function asyncIterator(source) {\n    var _a;\n    var iterator = source[Symbol.asyncIterator]();\n    return _a = {\n            next: function () {\n                return iterator.next();\n            }\n        },\n        _a[Symbol.asyncIterator] = function () {\n            return this;\n        },\n        _a;\n}\n//# sourceMappingURL=async.js.map", "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/nodeStream.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function nodeStreamIterator(stream) {\n    var cleanup = null;\n    var error = null;\n    var done = false;\n    var data = [];\n    var waiting = [];\n    function onData(chunk) {\n        if (error)\n            return;\n        if (waiting.length) {\n            var shiftedArr = waiting.shift();\n            if (Array.isArray(shiftedArr) && shiftedArr[0]) {\n                return shiftedArr[0]({ value: chunk, done: false });\n            }\n        }\n        data.push(chunk);\n    }\n    function onError(err) {\n        error = err;\n        var all = waiting.slice();\n        all.forEach(function (pair) {\n            pair[1](err);\n        });\n        !cleanup || cleanup();\n    }\n    function onEnd() {\n        done = true;\n        var all = waiting.slice();\n        all.forEach(function (pair) {\n            pair[0]({ value: undefined, done: true });\n        });\n        !cleanup || cleanup();\n    }\n    cleanup = function () {\n        cleanup = null;\n        stream.removeListener(\"data\", onData);\n        stream.removeListener(\"error\", onError);\n        stream.removeListener(\"end\", onEnd);\n        stream.removeListener(\"finish\", onEnd);\n        stream.removeListener(\"close\", onEnd);\n    };\n    stream.on(\"data\", onData);\n    stream.on(\"error\", onError);\n    stream.on(\"end\", onEnd);\n    stream.on(\"finish\", onEnd);\n    stream.on(\"close\", onEnd);\n    function getNext() {\n        return new Promise(function (resolve, reject) {\n            if (error)\n                return reject(error);\n            if (data.length)\n                return resolve({ value: data.shift(), done: false });\n            if (done)\n                return resolve({ value: undefined, done: true });\n            waiting.push([resolve, reject]);\n        });\n    }\n    var iterator = {\n        next: function () {\n            return getNext();\n        },\n    };\n    if (canUseAsyncIteratorSymbol) {\n        iterator[Symbol.asyncIterator] = function () {\n            return this;\n        };\n    }\n    return iterator;\n}\n//# sourceMappingURL=nodeStream.js.map", "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/promise.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function promiseIterator(promise) {\n    var resolved = false;\n    var iterator = {\n        next: function () {\n            if (resolved)\n                return Promise.resolve({\n                    value: undefined,\n                    done: true,\n                });\n            resolved = true;\n            return new Promise(function (resolve, reject) {\n                promise\n                    .then(function (value) {\n                    resolve({ value: value, done: false });\n                })\n                    .catch(reject);\n            });\n        },\n    };\n    if (canUseAsyncIteratorSymbol) {\n        iterator[Symbol.asyncIterator] = function () {\n            return this;\n        };\n    }\n    return iterator;\n}\n//# sourceMappingURL=promise.js.map", "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/iterators/reader.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../../utilities/index.js\";\nexport default function readerIterator(reader) {\n    var iterator = {\n        next: function () {\n            return reader.read();\n        },\n    };\n    if (canUseAsyncIteratorSymbol) {\n        iterator[Symbol.asyncIterator] = function () {\n            return this;\n        };\n    }\n    return iterator;\n}\n//# sourceMappingURL=reader.js.map", "/**\n * Original source:\n * https://github.com/kmalakoff/response-iterator/blob/master/src/index.ts\n */\nimport { canUseAsyncIteratorSymbol } from \"../../utilities/index.js\";\nimport asyncIterator from \"./iterators/async.js\";\nimport nodeStreamIterator from \"./iterators/nodeStream.js\";\nimport promiseIterator from \"./iterators/promise.js\";\nimport readerIterator from \"./iterators/reader.js\";\nfunction isNodeResponse(value) {\n    return !!value.body;\n}\nfunction isReadableStream(value) {\n    return !!value.getReader;\n}\nfunction isAsyncIterableIterator(value) {\n    return !!(canUseAsyncIteratorSymbol &&\n        value[Symbol.asyncIterator]);\n}\nfunction isStreamableBlob(value) {\n    return !!value.stream;\n}\nfunction isBlob(value) {\n    return !!value.arrayBuffer;\n}\nfunction isNodeReadableStream(value) {\n    return !!value.pipe;\n}\nexport function responseIterator(response) {\n    var body = response;\n    if (isNodeResponse(response))\n        body = response.body;\n    if (isAsyncIterableIterator(body))\n        return asyncIterator(body);\n    if (isReadableStream(body))\n        return readerIterator(body.getReader());\n    // this errors without casting to ReadableStream<T>\n    // because Blob.stream() returns a NodeJS ReadableStream\n    if (isStreamableBlob(body)) {\n        return readerIterator(body.stream().getReader());\n    }\n    if (isBlob(body))\n        return promiseIterator(body.arrayBuffer());\n    if (isNodeReadableStream(body))\n        return nodeStreamIterator(body);\n    throw new Error(\"Unknown body type for responseIterator. Please pass a streamable response.\");\n}\n//# sourceMappingURL=responseIterator.js.map", "export function isNonNullObject(obj) {\n    return obj !== null && typeof obj === \"object\";\n}\nexport function isPlainObject(obj) {\n    return (obj !== null &&\n        typeof obj === \"object\" &&\n        (Object.getPrototypeOf(obj) === Object.prototype ||\n            Object.getPrototypeOf(obj) === null));\n}\n//# sourceMappingURL=objects.js.map", "import { isNonNullObject } from \"./objects.js\";\nimport { isNonEmptyArray } from \"./arrays.js\";\nimport { DeepMerger } from \"./mergeDeep.js\";\nexport function isExecutionPatchIncrementalResult(value) {\n    return \"incremental\" in value;\n}\nexport function isExecutionPatchInitialResult(value) {\n    return \"hasNext\" in value && \"data\" in value;\n}\nexport function isExecutionPatchResult(value) {\n    return (isExecutionPatchIncrementalResult(value) ||\n        isExecutionPatchInitialResult(value));\n}\n// This function detects an Apollo payload result before it is transformed\n// into a FetchResult via HttpLink; it cannot detect an ApolloPayloadResult\n// once it leaves the link chain.\nexport function isApolloPayloadResult(value) {\n    return isNonNullObject(value) && \"payload\" in value;\n}\nexport function mergeIncrementalData(prevResult, result) {\n    var mergedData = prevResult;\n    var merger = new DeepMerger();\n    if (isExecutionPatchIncrementalResult(result) &&\n        isNonEmptyArray(result.incremental)) {\n        result.incremental.forEach(function (_a) {\n            var data = _a.data, path = _a.path;\n            for (var i = path.length - 1; i >= 0; --i) {\n                var key = path[i];\n                var isNumericKey = !isNaN(+key);\n                var parent_1 = isNumericKey ? [] : {};\n                parent_1[key] = data;\n                data = parent_1;\n            }\n            mergedData = merger.merge(mergedData, data);\n        });\n    }\n    return mergedData;\n}\n//# sourceMappingURL=incrementalResult.js.map", "import { __assign, __awaiter, __generator } from \"tslib\";\nimport { responseIterator } from \"./responseIterator.js\";\nimport { throwServerError } from \"../utils/index.js\";\nimport { PROTOCOL_ERRORS_SYMBOL } from \"../../errors/index.js\";\nimport { isApolloPayloadResult } from \"../../utilities/common/incrementalResult.js\";\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nexport function readMultipartBody(response, nextValue) {\n    return __awaiter(this, void 0, void 0, function () {\n        var decoder, contentType, delimiter, boundaryVal, boundary, buffer, iterator, running, _a, value, done, chunk, searchFrom, bi, message, i, headers, contentType_1, body, result, next;\n        var _b, _c;\n        var _d;\n        return __generator(this, function (_e) {\n            switch (_e.label) {\n                case 0:\n                    if (TextDecoder === undefined) {\n                        throw new Error(\"TextDecoder must be defined in the environment: please import a polyfill.\");\n                    }\n                    decoder = new TextDecoder(\"utf-8\");\n                    contentType = (_d = response.headers) === null || _d === void 0 ? void 0 : _d.get(\"content-type\");\n                    delimiter = \"boundary=\";\n                    boundaryVal = (contentType === null || contentType === void 0 ? void 0 : contentType.includes(delimiter)) ?\n                        contentType === null || contentType === void 0 ? void 0 : contentType.substring((contentType === null || contentType === void 0 ? void 0 : contentType.indexOf(delimiter)) + delimiter.length).replace(/['\"]/g, \"\").replace(/\\;(.*)/gm, \"\").trim()\n                        : \"-\";\n                    boundary = \"\\r\\n--\".concat(boundaryVal);\n                    buffer = \"\";\n                    iterator = responseIterator(response);\n                    running = true;\n                    _e.label = 1;\n                case 1:\n                    if (!running) return [3 /*break*/, 3];\n                    return [4 /*yield*/, iterator.next()];\n                case 2:\n                    _a = _e.sent(), value = _a.value, done = _a.done;\n                    chunk = typeof value === \"string\" ? value : decoder.decode(value);\n                    searchFrom = buffer.length - boundary.length + 1;\n                    running = !done;\n                    buffer += chunk;\n                    bi = buffer.indexOf(boundary, searchFrom);\n                    while (bi > -1) {\n                        message = void 0;\n                        _b = [\n                            buffer.slice(0, bi),\n                            buffer.slice(bi + boundary.length),\n                        ], message = _b[0], buffer = _b[1];\n                        i = message.indexOf(\"\\r\\n\\r\\n\");\n                        headers = parseHeaders(message.slice(0, i));\n                        contentType_1 = headers[\"content-type\"];\n                        if (contentType_1 &&\n                            contentType_1.toLowerCase().indexOf(\"application/json\") === -1) {\n                            throw new Error(\"Unsupported patch content type: application/json is required.\");\n                        }\n                        body = message.slice(i);\n                        if (body) {\n                            result = parseJsonBody(response, body);\n                            if (Object.keys(result).length > 1 ||\n                                \"data\" in result ||\n                                \"incremental\" in result ||\n                                \"errors\" in result ||\n                                \"payload\" in result) {\n                                if (isApolloPayloadResult(result)) {\n                                    next = {};\n                                    if (\"payload\" in result) {\n                                        if (Object.keys(result).length === 1 && result.payload === null) {\n                                            return [2 /*return*/];\n                                        }\n                                        next = __assign({}, result.payload);\n                                    }\n                                    if (\"errors\" in result) {\n                                        next = __assign(__assign({}, next), { extensions: __assign(__assign({}, (\"extensions\" in next ? next.extensions : null)), (_c = {}, _c[PROTOCOL_ERRORS_SYMBOL] = result.errors, _c)) });\n                                    }\n                                    nextValue(next);\n                                }\n                                else {\n                                    // for the last chunk with only `hasNext: false`\n                                    // we don't need to call observer.next as there is no data/errors\n                                    nextValue(result);\n                                }\n                            }\n                            else if (\n                            // If the chunk contains only a \"hasNext: false\", we can call\n                            // observer.complete() immediately.\n                            Object.keys(result).length === 1 &&\n                                \"hasNext\" in result &&\n                                !result.hasNext) {\n                                return [2 /*return*/];\n                            }\n                        }\n                        bi = buffer.indexOf(boundary);\n                    }\n                    return [3 /*break*/, 1];\n                case 3: return [2 /*return*/];\n            }\n        });\n    });\n}\nexport function parseHeaders(headerText) {\n    var headersInit = {};\n    headerText.split(\"\\n\").forEach(function (line) {\n        var i = line.indexOf(\":\");\n        if (i > -1) {\n            // normalize headers to lowercase\n            var name_1 = line.slice(0, i).trim().toLowerCase();\n            var value = line.slice(i + 1).trim();\n            headersInit[name_1] = value;\n        }\n    });\n    return headersInit;\n}\nexport function parseJsonBody(response, bodyText) {\n    if (response.status >= 300) {\n        // Network error\n        var getResult = function () {\n            try {\n                return JSON.parse(bodyText);\n            }\n            catch (err) {\n                return bodyText;\n            }\n        };\n        throwServerError(response, getResult(), \"Response not successful: Received status code \".concat(response.status));\n    }\n    try {\n        return JSON.parse(bodyText);\n    }\n    catch (err) {\n        var parseError = err;\n        parseError.name = \"ServerParseError\";\n        parseError.response = response;\n        parseError.statusCode = response.status;\n        parseError.bodyText = bodyText;\n        throw parseError;\n    }\n}\nexport function handleError(err, observer) {\n    // if it is a network error, BUT there is graphql result info fire\n    // the next observer before calling error this gives apollo-client\n    // (and react-apollo) the `graphqlErrors` and `networkErrors` to\n    // pass to UI this should only happen if we *also* have data as\n    // part of the response key per the spec\n    if (err.result && err.result.errors && err.result.data) {\n        // if we don't call next, the UI can only show networkError\n        // because AC didn't get any graphqlErrors this is graphql\n        // execution result info (i.e errors and possibly data) this is\n        // because there is no formal spec how errors should translate to\n        // http status codes. So an auth error (401) could have both data\n        // from a public field, errors from a private field, and a status\n        // of 401\n        // {\n        //  user { // this will have errors\n        //    firstName\n        //  }\n        //  products { // this is public so will have data\n        //    cost\n        //  }\n        // }\n        //\n        // the result of above *could* look like this:\n        // {\n        //   data: { products: [{ cost: \"$10\" }] },\n        //   errors: [{\n        //      message: 'your session has timed out',\n        //      path: []\n        //   }]\n        // }\n        // status code of above would be a 401\n        // in the UI you want to show data where you can, errors as data where you can\n        // and use correct http status codes\n        observer.next(err.result);\n    }\n    observer.error(err);\n}\nexport function parseAndCheckHttpResponse(operations) {\n    return function (response) {\n        return response\n            .text()\n            .then(function (bodyText) { return parseJsonBody(response, bodyText); })\n            .then(function (result) {\n            if (!Array.isArray(result) &&\n                !hasOwnProperty.call(result, \"data\") &&\n                !hasOwnProperty.call(result, \"errors\")) {\n                // Data error\n                throwServerError(response, result, \"Server response was missing for query '\".concat(Array.isArray(operations) ?\n                    operations.map(function (op) { return op.operationName; })\n                    : operations.operationName, \"'.\"));\n            }\n            return result;\n        });\n    };\n}\n//# sourceMappingURL=parseAndCheckHttpResponse.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport { print } from \"../../utilities/index.js\";\nvar defaultHttpOptions = {\n    includeQuery: true,\n    includeExtensions: false,\n    preserveHeaderCase: false,\n};\nvar defaultHeaders = {\n    // headers are case insensitive (https://stackoverflow.com/a/5259004)\n    accept: \"*/*\",\n    // The content-type header describes the type of the body of the request, and\n    // so it typically only is sent with requests that actually have bodies. One\n    // could imagine that Apollo Client would remove this header when constructing\n    // a GET request (which has no body), but we historically have not done that.\n    // This means that browsers will preflight all Apollo Client requests (even\n    // GET requests). Apollo Server's CSRF prevention feature (introduced in\n    // AS3.7) takes advantage of this fact and does not block requests with this\n    // header. If you want to drop this header from GET requests, then you should\n    // probably replace it with a `apollo-require-preflight` header, or servers\n    // with CSRF prevention enabled might block your GET request. See\n    // https://www.apollographql.com/docs/apollo-server/security/cors/#preventing-cross-site-request-forgery-csrf\n    // for more details.\n    \"content-type\": \"application/json\",\n};\nvar defaultOptions = {\n    method: \"POST\",\n};\nexport var fallbackHttpConfig = {\n    http: defaultHttpOptions,\n    headers: defaultHeaders,\n    options: defaultOptions,\n};\nexport var defaultPrinter = function (ast, printer) { return printer(ast); };\nexport function selectHttpOptionsAndBody(operation, fallbackConfig) {\n    var configs = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        configs[_i - 2] = arguments[_i];\n    }\n    configs.unshift(fallbackConfig);\n    return selectHttpOptionsAndBodyInternal.apply(void 0, __spreadArray([operation,\n        defaultPrinter], configs, false));\n}\nexport function selectHttpOptionsAndBodyInternal(operation, printer) {\n    var configs = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        configs[_i - 2] = arguments[_i];\n    }\n    var options = {};\n    var http = {};\n    configs.forEach(function (config) {\n        options = __assign(__assign(__assign({}, options), config.options), { headers: __assign(__assign({}, options.headers), config.headers) });\n        if (config.credentials) {\n            options.credentials = config.credentials;\n        }\n        http = __assign(__assign({}, http), config.http);\n    });\n    if (options.headers) {\n        options.headers = removeDuplicateHeaders(options.headers, http.preserveHeaderCase);\n    }\n    //The body depends on the http options\n    var operationName = operation.operationName, extensions = operation.extensions, variables = operation.variables, query = operation.query;\n    var body = { operationName: operationName, variables: variables };\n    if (http.includeExtensions)\n        body.extensions = extensions;\n    // not sending the query (i.e persisted queries)\n    if (http.includeQuery)\n        body.query = printer(query, print);\n    return {\n        options: options,\n        body: body,\n    };\n}\n// Remove potential duplicate header names, preserving last (by insertion order).\n// This is done to prevent unintentionally duplicating a header instead of\n// overwriting it (See #8447 and #8449).\nfunction removeDuplicateHeaders(headers, preserveHeaderCase) {\n    // If we're not preserving the case, just remove duplicates w/ normalization.\n    if (!preserveHeaderCase) {\n        var normalizedHeaders_1 = {};\n        Object.keys(Object(headers)).forEach(function (name) {\n            normalizedHeaders_1[name.toLowerCase()] = headers[name];\n        });\n        return normalizedHeaders_1;\n    }\n    // If we are preserving the case, remove duplicates w/ normalization,\n    // preserving the original name.\n    // This allows for non-http-spec-compliant servers that expect intentionally\n    // capitalized header names (See #6741).\n    var headerData = {};\n    Object.keys(Object(headers)).forEach(function (name) {\n        headerData[name.toLowerCase()] = {\n            originalName: name,\n            value: headers[name],\n        };\n    });\n    var normalizedHeaders = {};\n    Object.keys(headerData).forEach(function (name) {\n        normalizedHeaders[headerData[name].originalName] = headerData[name].value;\n    });\n    return normalizedHeaders;\n}\n//# sourceMappingURL=selectHttpOptionsAndBody.js.map", "import { __assign } from \"tslib\";\nimport { fallbackHttpConfig } from \"../../link/http/selectHttpOptionsAndBody.js\";\nexport function generateOptionsForMultipartSubscription(headers) {\n    var options = __assign(__assign({}, fallbackHttpConfig.options), { headers: __assign(__assign(__assign({}, (headers || {})), fallbackHttpConfig.headers), { accept: \"multipart/mixed;boundary=graphql;subscriptionSpec=1.0,application/json\" }) });\n    return options;\n}\n//# sourceMappingURL=shared.js.map", "import { __assign } from \"tslib\";\nimport { Observable } from \"../../index.js\";\nimport { handleError, readMultipartBody, } from \"../../../link/http/parseAndCheckHttpResponse.js\";\nimport { maybe } from \"../../index.js\";\nimport { serializeFetchParameter } from \"../../../core/index.js\";\nimport { generateOptionsForMultipartSubscription } from \"../shared.js\";\nvar backupFetch = maybe(function () { return fetch; });\nexport function createFetchMultipartSubscription(uri, _a) {\n    var _b = _a === void 0 ? {} : _a, preferredFetch = _b.fetch, headers = _b.headers;\n    return function multipartSubscriptionForwarder(_a) {\n        var query = _a.query, variables = _a.variables;\n        var body = { variables: variables, query: query };\n        var options = generateOptionsForMultipartSubscription(headers || {});\n        return new Observable(function (observer) {\n            try {\n                options.body = serializeFetchParameter(body, \"Payload\");\n            }\n            catch (parseError) {\n                observer.error(parseError);\n            }\n            var currentFetch = preferredFetch || maybe(function () { return fetch; }) || backupFetch;\n            var observerNext = observer.next.bind(observer);\n            var abortController = new AbortController();\n            currentFetch(uri, __assign(__assign({}, options), { signal: abortController.signal }))\n                .then(function (response) {\n                var _a;\n                var ctype = (_a = response.headers) === null || _a === void 0 ? void 0 : _a.get(\"content-type\");\n                if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n                    return readMultipartBody(response, observerNext);\n                }\n                observer.error(new Error(\"Expected multipart response\"));\n            })\n                .then(function () {\n                observer.complete();\n            })\n                .catch(function (err) {\n                handleError(err, observer);\n            });\n            return function () {\n                abortController.abort();\n            };\n        });\n    };\n}\n//# sourceMappingURL=index.js.map"], "names": ["canUseAsyncIteratorSymbol", "__awaiter", "__generator", "__assign", "PROTOCOL_ERRORS_SYMBOL", "throwServerError", "maybe", "Observable", "serializeFetchParameter"], "mappings": ";;;;;;;;;;AAIe,SAAS,aAAa,CAAC,MAAM,EAAE;AAC9C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,CAAC;AAClD,IAAI,OAAO,EAAE,GAAG;AAChB,YAAY,IAAI,EAAE,YAAY;AAC9B,gBAAgB,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AACvC,aAAa;AACb,SAAS;AACT,QAAQ,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY;AAC/C,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,EAAE,CAAC;AACX;;ACXe,SAAS,kBAAkB,CAAC,MAAM,EAAE;AACnD,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC;AACrB,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,SAAS,MAAM,CAAC,KAAK,EAAE;AAC3B,QAAQ,IAAI,KAAK;AACjB,YAAY,OAAO;AACnB,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AAC5B,YAAY,IAAI,UAAU,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;AAC7C,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE;AAC5D,gBAAgB,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AACpE,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,SAAS,OAAO,CAAC,GAAG,EAAE;AAC1B,QAAQ,KAAK,GAAG,GAAG,CAAC;AACpB,QAAQ,IAAI,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;AAClC,QAAQ,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACpC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACzB,SAAS,CAAC,CAAC;AACX,QAAQ,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AAC9B,KAAK;AACL,IAAI,SAAS,KAAK,GAAG;AACrB,QAAQ,IAAI,GAAG,IAAI,CAAC;AACpB,QAAQ,IAAI,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;AAClC,QAAQ,GAAG,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACpC,YAAY,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACtD,SAAS,CAAC,CAAC;AACX,QAAQ,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AAC9B,KAAK;AACL,IAAI,OAAO,GAAG,YAAY;AAC1B,QAAQ,OAAO,GAAG,IAAI,CAAC;AACvB,QAAQ,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC9C,QAAQ,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAChD,QAAQ,MAAM,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC5C,QAAQ,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC/C,QAAQ,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC9C,KAAK,CAAC;AACN,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC9B,IAAI,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAChC,IAAI,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC5B,IAAI,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC/B,IAAI,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC9B,IAAI,SAAS,OAAO,GAAG;AACvB,QAAQ,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AACtD,YAAY,IAAI,KAAK;AACrB,gBAAgB,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;AACrC,YAAY,IAAI,IAAI,CAAC,MAAM;AAC3B,gBAAgB,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AACrE,YAAY,IAAI,IAAI;AACpB,gBAAgB,OAAO,OAAO,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACjE,YAAY,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AAC5C,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,IAAI,QAAQ,GAAG;AACnB,QAAQ,IAAI,EAAE,YAAY;AAC1B,YAAY,OAAO,OAAO,EAAE,CAAC;AAC7B,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAIA,mCAAyB,EAAE;AACnC,QAAQ,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY;AACrD,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB;;ACpEe,SAAS,eAAe,CAAC,OAAO,EAAE;AACjD,IAAI,IAAI,QAAQ,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,QAAQ,GAAG;AACnB,QAAQ,IAAI,EAAE,YAAY;AAC1B,YAAY,IAAI,QAAQ;AACxB,gBAAgB,OAAO,OAAO,CAAC,OAAO,CAAC;AACvC,oBAAoB,KAAK,EAAE,SAAS;AACpC,oBAAoB,IAAI,EAAE,IAAI;AAC9B,iBAAiB,CAAC,CAAC;AACnB,YAAY,QAAQ,GAAG,IAAI,CAAC;AAC5B,YAAY,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AAC1D,gBAAgB,OAAO;AACvB,qBAAqB,IAAI,CAAC,UAAU,KAAK,EAAE;AAC3C,oBAAoB,OAAO,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC;AAC3D,iBAAiB,CAAC;AAClB,qBAAqB,KAAK,CAAC,MAAM,CAAC,CAAC;AACnC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAIA,mCAAyB,EAAE;AACnC,QAAQ,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY;AACrD,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB;;ACzBe,SAAS,cAAc,CAAC,MAAM,EAAE;AAC/C,IAAI,IAAI,QAAQ,GAAG;AACnB,QAAQ,IAAI,EAAE,YAAY;AAC1B,YAAY,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;AACjC,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAIA,mCAAyB,EAAE;AACnC,QAAQ,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,YAAY;AACrD,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB;;ACRA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;AACxB,CAAC;AACD,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;AAC7B,CAAC;AACD,SAAS,uBAAuB,CAAC,KAAK,EAAE;AACxC,IAAI,OAAO,CAAC,EAAEA,mCAAyB;AACvC,QAAQ,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC;AACrC,CAAC;AACD,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC;AAC1B,CAAC;AACD,SAAS,MAAM,CAAC,KAAK,EAAE;AACvB,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC;AAC/B,CAAC;AACD,SAAS,oBAAoB,CAAC,KAAK,EAAE;AACrC,IAAI,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;AACxB,CAAC;AACM,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AAC3C,IAAI,IAAI,IAAI,GAAG,QAAQ,CAAC;AACxB,IAAI,IAAI,cAAc,CAAC,QAAQ,CAAC;AAChC,QAAQ,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC7B,IAAI,IAAI,uBAAuB,CAAC,IAAI,CAAC;AACrC,QAAQ,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;AACnC,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC;AAC9B,QAAQ,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;AAGhD,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAChC,QAAQ,OAAO,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,EAAE,CAAC,CAAC;AACzD,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC;AACpB,QAAQ,OAAO,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACnD,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC;AAClC,QAAQ,OAAO,kBAAkB,CAAC,IAAI,CAAC,CAAC;AACxC,IAAI,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;AAClG;;AC9CO,SAAS,eAAe,CAAC,GAAG,EAAE;AACrC,IAAI,OAAO,GAAG,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,CAAC;AACnD;;ACcO,SAAS,qBAAqB,CAAC,KAAK,EAAE;AAC7C,IAAI,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,SAAS,IAAI,KAAK,CAAC;AACxD;;ACZO,SAAS,iBAAiB,CAAC,QAAQ,EAAE,SAAS,EAAE;AACvD,IAAI,OAAOC,eAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AACvD,QAAQ,IAAI,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC;AAC9L,QAAQ,IAAI,EAAE,EAAE,EAAE,CAAC;AACnB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AAC/C,YAAY,QAAQ,EAAE,CAAC,KAAK;AAC5B,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,IAAI,WAAW,KAAK,SAAS,EAAE;AACnD,wBAAwB,MAAM,IAAI,KAAK,CAAC,2EAA2E,CAAC,CAAC;AACrH,qBAAqB;AACrB,oBAAoB,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;AACvD,oBAAoB,WAAW,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACtH,oBAAoB,SAAS,GAAG,WAAW,CAAC;AAC5C,oBAAoB,WAAW,GAAG,CAAC,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC;AAC5H,wBAAwB,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE;AAC1Q,0BAA0B,GAAG,CAAC;AAC9B,oBAAoB,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAC5D,oBAAoB,MAAM,GAAG,EAAE,CAAC;AAChC,oBAAoB,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AAC1D,oBAAoB,OAAO,GAAG,IAAI,CAAC;AACnC,oBAAoB,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;AACjC,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,GAAY,CAAC,CAAC,CAAC;AAC1D,oBAAoB,OAAO,CAAC,CAAC,GAAY,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;AAC1D,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACrE,oBAAoB,KAAK,GAAG,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACtF,oBAAoB,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;AACrE,oBAAoB,OAAO,GAAG,CAAC,IAAI,CAAC;AACpC,oBAAoB,MAAM,IAAI,KAAK,CAAC;AACpC,oBAAoB,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC9D,oBAAoB,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE;AACpC,wBAAwB,OAAO,GAAG,KAAK,CAAC,CAAC;AACzC,wBAAwB,EAAE,GAAG;AAC7B,4BAA4B,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;AAC/C,4BAA4B,MAAM,CAAC,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC;AAC9D,yBAAyB,EAAE,OAAO,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3D,wBAAwB,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACxD,wBAAwB,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACpE,wBAAwB,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;AAChE,wBAAwB,IAAI,aAAa;AACzC,4BAA4B,aAAa,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE;AAC5F,4BAA4B,MAAM,IAAI,KAAK,CAAC,+DAA+D,CAAC,CAAC;AAC7G,yBAAyB;AACzB,wBAAwB,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChD,wBAAwB,IAAI,IAAI,EAAE;AAClC,4BAA4B,MAAM,GAAG,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AACnE,4BAA4B,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;AAC9D,gCAAgC,MAAM,IAAI,MAAM;AAChD,gCAAgC,aAAa,IAAI,MAAM;AACvD,gCAAgC,QAAQ,IAAI,MAAM;AAClD,gCAAgC,SAAS,IAAI,MAAM,EAAE;AACrD,gCAAgC,IAAI,qBAAqB,CAAC,MAAM,CAAC,EAAE;AACnE,oCAAoC,IAAI,GAAG,EAAE,CAAC;AAC9C,oCAAoC,IAAI,SAAS,IAAI,MAAM,EAAE;AAC7D,wCAAwC,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE;AACzG,4CAA4C,OAAO,CAAC,CAAC,EAAY,CAAC;AAClE,yCAAyC;AACzC,wCAAwC,IAAI,GAAGC,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AAC5E,qCAAqC;AACrC,oCAAoC,IAAI,QAAQ,IAAI,MAAM,EAAE;AAC5D,wCAAwC,IAAI,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAEA,cAAQ,CAACA,cAAQ,CAAC,EAAE,GAAG,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAACC,6BAAsB,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;AAChO,qCAAqC;AACrC,oCAAoC,SAAS,CAAC,IAAI,CAAC,CAAC;AACpD,iCAAiC;AACjC,qCAAqC;AAGrC,oCAAoC,SAAS,CAAC,MAAM,CAAC,CAAC;AACtD,iCAAiC;AACjC,6BAA6B;AAC7B,iCAAiC;AAGjC,4BAA4B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,CAAC;AAC5D,gCAAgC,SAAS,IAAI,MAAM;AACnD,gCAAgC,CAAC,MAAM,CAAC,OAAO,EAAE;AACjD,gCAAgC,OAAO,CAAC,CAAC,EAAY,CAAC;AACtD,6BAA6B;AAC7B,yBAAyB;AACzB,wBAAwB,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACtD,qBAAqB;AACrB,oBAAoB,OAAO,CAAC,CAAC,GAAY,CAAC,CAAC,CAAC;AAC5C,gBAAgB,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,EAAY,CAAC;AAC9C,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,CAAC;AACM,SAAS,YAAY,CAAC,UAAU,EAAE;AACzC,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC;AACzB,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AACnD,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;AAEpB,YAAY,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;AAC/D,YAAY,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACjD,YAAY,WAAW,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC;AACxC,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC;AACM,SAAS,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE;AAClD,IAAI,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE;AAEhC,QAAQ,IAAI,SAAS,GAAG,YAAY;AACpC,YAAY,IAAI;AAChB,gBAAgB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC5C,aAAa;AACb,YAAY,OAAO,GAAG,EAAE;AACxB,gBAAgB,OAAO,QAAQ,CAAC;AAChC,aAAa;AACb,SAAS,CAAC;AACV,QAAQC,sBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,EAAE,gDAAgD,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;AAC1H,KAAK;AACL,IAAI,IAAI;AACR,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,OAAO,GAAG,EAAE;AAChB,QAAQ,IAAI,UAAU,GAAG,GAAG,CAAC;AAC7B,QAAQ,UAAU,CAAC,IAAI,GAAG,kBAAkB,CAAC;AAC7C,QAAQ,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACvC,QAAQ,UAAU,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;AAChD,QAAQ,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACvC,QAAQ,MAAM,UAAU,CAAC;AACzB,KAAK;AACL,CAAC;AACM,SAAS,WAAW,CAAC,GAAG,EAAE,QAAQ,EAAE;AAM3C,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;AA4B5D,QAAQ,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAClC,KAAK;AACL,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACxB;;ACxKA,IAAI,kBAAkB,GAAG;AACzB,IAAI,YAAY,EAAE,IAAI;AACtB,IAAI,iBAAiB,EAAE,KAAK;AAC5B,IAAI,kBAAkB,EAAE,KAAK;AAC7B,CAAC,CAAC;AACF,IAAI,cAAc,GAAG;AAErB,IAAI,MAAM,EAAE,KAAK;AAajB,IAAI,cAAc,EAAE,kBAAkB;AACtC,CAAC,CAAC;AACF,IAAI,cAAc,GAAG;AACrB,IAAI,MAAM,EAAE,MAAM;AAClB,CAAC,CAAC;AACK,IAAI,kBAAkB,GAAG;AAChC,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,OAAO,EAAE,cAAc;AAC3B,IAAI,OAAO,EAAE,cAAc;AAC3B,CAAC;;AC7BM,SAAS,uCAAuC,CAAC,OAAO,EAAE;AACjE,IAAI,IAAI,OAAO,GAAGF,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,kBAAkB,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,EAAEA,cAAQ,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,GAAG,OAAO,IAAI,EAAE,EAAE,EAAE,kBAAkB,CAAC,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,wEAAwE,EAAE,CAAC,EAAE,CAAC,CAAC;AACvP,IAAI,OAAO,OAAO,CAAC;AACnB;;ACCA,IAAI,WAAW,GAAGG,eAAK,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC,CAAC;AAChD,SAAS,gCAAgC,CAAC,GAAG,EAAE,EAAE,EAAE;AAC1D,IAAI,IAAI,EAAE,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,cAAc,GAAG,EAAE,CAAC,KAAK,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AACtF,IAAI,OAAO,SAAS,8BAA8B,CAAC,EAAE,EAAE;AACvD,QAAQ,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AACvD,QAAQ,IAAI,IAAI,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAC1D,QAAQ,IAAI,OAAO,GAAG,uCAAuC,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;AAC7E,QAAQ,OAAO,IAAIC,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAClD,YAAY,IAAI;AAChB,gBAAgB,OAAO,CAAC,IAAI,GAAGC,4BAAuB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACxE,aAAa;AACb,YAAY,OAAO,UAAU,EAAE;AAC/B,gBAAgB,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC3C,aAAa;AACb,YAAY,IAAI,YAAY,GAAG,cAAc,IAAIF,eAAK,CAAC,YAAY,EAAE,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI,WAAW,CAAC;AACrG,YAAY,IAAI,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5D,YAAY,IAAI,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AACxD,YAAY,YAAY,CAAC,GAAG,EAAEH,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;AAClG,iBAAiB,IAAI,CAAC,UAAU,QAAQ,EAAE;AAC1C,gBAAgB,IAAI,EAAE,CAAC;AACvB,gBAAgB,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAChH,gBAAgB,IAAI,KAAK,KAAK,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACxE,oBAAoB,OAAO,iBAAiB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACrE,iBAAiB;AACjB,gBAAgB,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;AACzE,aAAa,CAAC;AACd,iBAAiB,IAAI,CAAC,YAAY;AAClC,gBAAgB,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACpC,aAAa,CAAC;AACd,iBAAiB,KAAK,CAAC,UAAU,GAAG,EAAE;AACtC,gBAAgB,WAAW,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC3C,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,YAAY;AAC/B,gBAAgB,eAAe,CAAC,KAAK,EAAE,CAAC;AACxC,aAAa,CAAC;AACd,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN;;;;"}