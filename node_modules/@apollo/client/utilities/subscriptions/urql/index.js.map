{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/utilities/subscriptions/urql/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAC5C,OAAO,EACL,WAAW,EACX,iBAAiB,GAClB,MAAM,iDAAiD,CAAC;AACzD,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAC;AACvC,OAAO,EAAE,uBAAuB,EAAE,MAAM,wBAAwB,CAAC;AAEjE,OAAO,EAAE,uCAAuC,EAAE,MAAM,cAAc,CAAC;AAGvE,IAAM,WAAW,GAAG,KAAK,CAAC,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,CAAC;AAEvC,MAAM,UAAU,gCAAgC,CAC9C,GAAW,EACX,EAA2E;QAA3E,qBAAyE,EAAE,KAAA,EAAlE,cAAc,WAAA,EAAE,OAAO,aAAA;IAEhC,OAAO,SAAS,8BAA8B,CAAC,EAM9C;YALC,KAAK,WAAA,EACL,SAAS,eAAA;QAKT,IAAM,IAAI,GAAS,EAAE,SAAS,WAAA,EAAE,KAAK,OAAA,EAAE,CAAC;QACxC,IAAM,OAAO,GAAG,uCAAuC,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;QAEvE,OAAO,IAAI,UAAU,CAAC,UAAC,QAAQ;YAC7B,IAAI,CAAC;gBACH,OAAO,CAAC,IAAI,GAAG,uBAAuB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC7B,CAAC;YAED,IAAM,YAAY,GAAG,cAAc,IAAI,KAAK,CAAC,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,IAAI,WAAW,CAAC;YACzE,IAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAElD,IAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;YAE9C,YAAa,CAAC,GAAG,wBAAO,OAAO,KAAE,MAAM,EAAE,eAAe,CAAC,MAAM,IAAG;iBAC/D,IAAI,CAAC,UAAC,QAAQ;;gBACb,IAAM,KAAK,GAAG,MAAA,QAAQ,CAAC,OAAO,0CAAE,GAAG,CAAC,cAAc,CAAC,CAAC;gBAEpD,IAAI,KAAK,KAAK,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACvD,OAAO,iBAAiB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;gBACnD,CAAC;gBAED,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC;iBACD,IAAI,CAAC;gBACJ,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,CAAC,CAAC;iBACD,KAAK,CAAC,UAAC,GAAQ;gBACd,WAAW,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;YAEL,OAAO;gBACL,eAAe,CAAC,KAAK,EAAE,CAAC;YAC1B,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { Observable } from \"../../index.js\";\nimport {\n  handleError,\n  readMultipartBody,\n} from \"../../../link/http/parseAndCheckHttpResponse.js\";\nimport { maybe } from \"../../index.js\";\nimport { serializeFetchParameter } from \"../../../core/index.js\";\nimport type { Body } from \"../../../link/http/selectHttpOptionsAndBody.js\";\nimport { generateOptionsForMultipartSubscription } from \"../shared.js\";\nimport type { CreateMultipartSubscriptionOptions } from \"../shared.js\";\n\nconst backupFetch = maybe(() => fetch);\n\nexport function createFetchMultipartSubscription(\n  uri: string,\n  { fetch: preferredFetch, headers }: CreateMultipartSubscriptionOptions = {}\n) {\n  return function multipartSubscriptionForwarder({\n    query,\n    variables,\n  }: {\n    query?: string;\n    variables: undefined | Record<string, any>;\n  }) {\n    const body: Body = { variables, query };\n    const options = generateOptionsForMultipartSubscription(headers || {});\n\n    return new Observable((observer) => {\n      try {\n        options.body = serializeFetchParameter(body, \"Payload\");\n      } catch (parseError) {\n        observer.error(parseError);\n      }\n\n      const currentFetch = preferredFetch || maybe(() => fetch) || backupFetch;\n      const observerNext = observer.next.bind(observer);\n\n      const abortController = new AbortController();\n\n      currentFetch!(uri, { ...options, signal: abortController.signal })\n        .then((response) => {\n          const ctype = response.headers?.get(\"content-type\");\n\n          if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n            return readMultipartBody(response, observerNext);\n          }\n\n          observer.error(new Error(\"Expected multipart response\"));\n        })\n        .then(() => {\n          observer.complete();\n        })\n        .catch((err: any) => {\n          handleError(err, observer);\n        });\n\n      return () => {\n        abortController.abort();\n      };\n    });\n  };\n}\n"]}