{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/utilities/subscriptions/relay/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EACL,WAAW,EACX,iBAAiB,GAClB,MAAM,iDAAiD,CAAC;AACzD,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAC;AACvC,OAAO,EAAE,uBAAuB,EAAE,MAAM,wBAAwB,CAAC;AAIjE,OAAO,EAAE,uCAAuC,EAAE,MAAM,cAAc,CAAC;AAGvE,IAAM,WAAW,GAAG,KAAK,CAAC,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,CAAC;AAEvC,MAAM,UAAU,gCAAgC,CAC9C,GAAW,EACX,EAA2E;QAA3E,qBAAyE,EAAE,KAAA,EAAlE,cAAc,WAAA,EAAE,OAAO,aAAA;IAEhC,OAAO,SAAS,0BAA0B,CACxC,SAA4B,EAC5B,SAA6B;QAE7B,IAAM,IAAI,GAAS;YACjB,aAAa,EAAE,SAAS,CAAC,IAAI;YAC7B,SAAS,WAAA;YACT,KAAK,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE;SAC5B,CAAC;QACF,IAAM,OAAO,GAAG,uCAAuC,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;QAEvE,OAAO,UAAU,CAAC,MAAM,CAAC,UAAC,IAAI;YAC5B,IAAI,CAAC;gBACH,OAAO,CAAC,IAAI,GAAG,uBAAuB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAC1D,CAAC;YAAC,OAAO,UAAU,EAAE,CAAC;gBACpB,IAAI,CAAC,KAAK,CAAC,UAAmB,CAAC,CAAC;YAClC,CAAC;YAED,IAAM,YAAY,GAAG,cAAc,IAAI,KAAK,CAAC,cAAM,OAAA,KAAK,EAAL,CAAK,CAAC,IAAI,WAAW,CAAC;YACzE,IAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAE1C,YAAa,CAAC,GAAG,EAAE,OAAO,CAAC;iBACxB,IAAI,CAAC,UAAC,QAAQ;;gBACb,IAAM,KAAK,GAAG,MAAA,QAAQ,CAAC,OAAO,0CAAE,GAAG,CAAC,cAAc,CAAC,CAAC;gBAEpD,IAAI,KAAK,KAAK,IAAI,IAAI,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACvD,OAAO,iBAAiB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;gBACnD,CAAC;gBAED,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC,CAAC;YACvD,CAAC,CAAC;iBACD,IAAI,CAAC;gBACJ,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,CAAC,CAAC;iBACD,KAAK,CAAC,UAAC,GAAQ;gBACd,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;YACzB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { Observable } from \"relay-runtime\";\nimport type { RequestParameters, GraphQLResponse } from \"relay-runtime\";\nimport {\n  handleError,\n  readMultipartBody,\n} from \"../../../link/http/parseAndCheckHttpResponse.js\";\nimport { maybe } from \"../../index.js\";\nimport { serializeFetchParameter } from \"../../../core/index.js\";\n\nimport type { OperationVariables } from \"../../../core/index.js\";\nimport type { Body } from \"../../../link/http/selectHttpOptionsAndBody.js\";\nimport { generateOptionsForMultipartSubscription } from \"../shared.js\";\nimport type { CreateMultipartSubscriptionOptions } from \"../shared.js\";\n\nconst backupFetch = maybe(() => fetch);\n\nexport function createFetchMultipartSubscription(\n  uri: string,\n  { fetch: preferredFetch, headers }: CreateMultipartSubscriptionOptions = {}\n) {\n  return function fetchMultipartSubscription(\n    operation: RequestParameters,\n    variables: OperationVariables\n  ): Observable<GraphQLResponse> {\n    const body: Body = {\n      operationName: operation.name,\n      variables,\n      query: operation.text || \"\",\n    };\n    const options = generateOptionsForMultipartSubscription(headers || {});\n\n    return Observable.create((sink) => {\n      try {\n        options.body = serializeFetchParameter(body, \"Payload\");\n      } catch (parseError) {\n        sink.error(parseError as Error);\n      }\n\n      const currentFetch = preferredFetch || maybe(() => fetch) || backupFetch;\n      const observerNext = sink.next.bind(sink);\n\n      currentFetch!(uri, options)\n        .then((response) => {\n          const ctype = response.headers?.get(\"content-type\");\n\n          if (ctype !== null && /^multipart\\/mixed/i.test(ctype)) {\n            return readMultipartBody(response, observerNext);\n          }\n\n          sink.error(new Error(\"Expected multipart response\"));\n        })\n        .then(() => {\n          sink.complete();\n        })\n        .catch((err: any) => {\n          handleError(err, sink);\n        });\n    });\n  };\n}\n"]}