{"version": 3, "file": "shared.js", "sourceRoot": "", "sources": ["../../../src/utilities/subscriptions/shared.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,6CAA6C,CAAC;AAOjF,MAAM,UAAU,uCAAuC,CACrD,OAA+B;IAE/B,IAAM,OAAO,yBACR,kBAAkB,CAAC,OAAO,KAC7B,OAAO,iCACF,CAAC,OAAO,IAAI,EAAE,CAAC,GACf,kBAAkB,CAAC,OAAO,KAC7B,MAAM,EACJ,wEAAwE,MAE7E,CAAC;IACF,OAAO,OAAO,CAAC;AACjB,CAAC", "sourcesContent": ["import { fallbackHttpConfig } from \"../../link/http/selectHttpOptionsAndBody.js\";\n\nexport type CreateMultipartSubscriptionOptions = {\n  fetch?: WindowOrWorkerGlobalScope[\"fetch\"];\n  headers?: Record<string, string>;\n};\n\nexport function generateOptionsForMultipartSubscription(\n  headers: Record<string, string>\n) {\n  const options: { headers: Record<string, any>; body?: string } = {\n    ...fallbackHttpConfig.options,\n    headers: {\n      ...(headers || {}),\n      ...fallbackHttpConfig.headers,\n      accept:\n        \"multipart/mixed;boundary=graphql;subscriptionSpec=1.0,application/json\",\n    },\n  };\n  return options;\n}\n"]}