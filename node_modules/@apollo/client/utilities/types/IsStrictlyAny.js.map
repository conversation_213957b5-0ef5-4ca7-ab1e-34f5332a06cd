{"version": 3, "file": "IsStrictlyAny.js", "sourceRoot": "", "sources": ["../../../src/utilities/types/IsStrictlyAny.ts"], "names": [], "mappings": "", "sourcesContent": ["// Returns true if T is any, or false for any other type.\n// Inspired by https://stackoverflow.com/a/61625296/128454.\nexport type IsStrictlyAny<T> =\n  UnionToIntersection<UnionForAny<T>> extends never ? true : false;\n\n// If (and only if) T is any, the union 'a' | 1 is returned here, representing\n// both branches of this conditional type. Only UnionForAny<any> produces this\n// union type; all other inputs produce the 1 literal type.\ntype UnionForAny<T> = T extends never ? \"a\" : 1;\n\n// If that 'a' | 1 union is then passed to UnionToIntersection, the result\n// should be 'a' & 1, which TypeScript simplifies to the never type, since the\n// literal type 'a' and the literal type 1 are incompatible. More explanation of\n// this helper type: https://stackoverflow.com/a/50375286/62076.\ntype UnionToIntersection<U> =\n  (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I\n  : never;\n"]}