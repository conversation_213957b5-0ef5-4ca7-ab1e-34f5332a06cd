{"version": 3, "file": "NoInfer.js", "sourceRoot": "", "sources": ["../../../src/utilities/types/NoInfer.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\nHelper type that allows using a type in a way that cannot be \"widened\" by inference on the value it is used on.\n\nThis type was first suggested [in this Github discussion](https://github.com/microsoft/TypeScript/issues/14829#issuecomment-504042546).\n\nExample usage:\n```ts\nexport function useQuery<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n>(\n  query: DocumentNode | TypedDocumentNode<TData, TVariables>,\n  options: QueryHookOptions<NoInfer<TData>, NoInfer<TVariables>> = Object.create(null),\n)\n```\nIn this case, `TData` and `TVariables` should be inferred from `query`, but never widened from something in `options`.\n\nSo, in this code example:\n```ts\ndeclare const typedNode: TypedDocumentNode<{ foo: string}, { bar: number }>\nconst { variables } = useQuery(typedNode, { variables: { bar: 4, nonExistingVariable: \"string\" } });\n```\nWithout the use of `NoInfer`, `variables` would now be of the type `{ bar: number, nonExistingVariable: \"string\" }`.\nWith `NoInfer`, it will instead give an error on `nonExistingVariable`.\n */\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\n"]}