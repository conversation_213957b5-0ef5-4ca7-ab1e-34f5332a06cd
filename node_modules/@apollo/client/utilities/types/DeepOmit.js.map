{"version": 3, "file": "DeepOmit.js", "sourceRoot": "", "sources": ["../../../src/utilities/types/DeepOmit.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Primitive } from \"./Primitive.js\";\n\n// DeepOmit primitives include functions since these are unmodified.\ntype DeepOmitPrimitive = Primitive | Function;\n\nexport type DeepOmitArray<T extends any[], K> = {\n  [P in keyof T]: DeepOmit<T[P], K>;\n};\n\n// Unfortunately there is one major flaw in this type: This will omit properties\n// from class instances in the return type even though our omitDeep helper\n// ignores class instances, therefore resulting in a type mismatch between\n// the return value and the runtime value.\n//\n// It is not currently possible with TypeScript to distinguish between plain\n// objects and class instances.\n// https://github.com/microsoft/TypeScript/issues/29063\n//\n// This should be fine as of the time of this writing until omitDeep gets\n// broader use since this utility is only used to strip __typename from\n// `variables`; a case in which class instances are invalid anyways.\nexport type DeepOmit<T, K> =\n  T extends DeepOmitPrimitive ? T\n  : {\n      [P in keyof T as P extends K ? never : P]: T[P] extends infer TP ?\n        TP extends DeepOmitPrimitive ? TP\n        : TP extends any[] ? DeepOmitArray<TP, K>\n        : DeepOmit<TP, K>\n      : never;\n    };\n"]}