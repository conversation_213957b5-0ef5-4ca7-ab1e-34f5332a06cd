{"version": 3, "file": "DocumentTransform.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/DocumentTransform.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AACjC,OAAO,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AACnE,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAEhD,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AACxC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAChC,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AAyBjD,SAAS,QAAQ,CAAC,QAAsB;IACtC,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;IA4CE,2BACE,SAAsB,EACtB,OAAuD;QAAvD,wBAAA,EAAA,UAAoC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QA1CxC,gBAAW,GAC1B,aAAa,CAAC,CAAC,CAAC,IAAI,OAAO,EAAgB,CAAC,CAAC,CAAC,IAAI,GAAG,EAAgB,CAAC;QA2CtE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACxB,qEAAqE;YACrE,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;QAEtC,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAlDD,4EAA4E;IAC5E,0EAA0E;IAC1E,2EAA2E;IAC3E,wEAAwE;IACxE,oDAAoD;IAC5C,uCAAW,GAAnB,UACE,QAAsB;QAEtB,OAAO,CAAC,QAAQ,CAAC,CAAC;IACpB,CAAC;IAEM,0BAAQ,GAAf;QACE,qEAAqE;QACrE,sEAAsE;QACtE,4DAA4D;QAC5D,OAAO,IAAI,iBAAiB,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAC3D,CAAC;IAEM,uBAAK,GAAZ,UACE,SAA8C,EAC9C,IAAuB,EACvB,KAAuD;QAAvD,sBAAA,EAAA,QAA2B,iBAAiB,CAAC,QAAQ,EAAE;QAEvD,OAAO,MAAM,CAAC,MAAM,CAClB,IAAI,iBAAiB,CACnB,UAAC,QAAQ;YACP,IAAM,iBAAiB,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;YAE7D,OAAO,iBAAiB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QACvD,CAAC;QACD,gFAAgF;QAChF,EAAE,KAAK,EAAE,KAAK,EAAE,CACjB,EACD,EAAE,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,CAChB,CAAC;IACJ,CAAC;IAiBD;;OAEG;IACH,sCAAU,GAAV;QAAA,iBAqBC;QApBC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAM,iBAAe,GAAG,IAAI,IAAI,CAAU,aAAa,CAAC,CAAC;YACzD,IAAI,CAAC,WAAW,GAAG,IAAI,CACrB,iBAAiB,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAClD;gBACE,YAAY,EAAE,UAAC,QAAQ;oBACrB,IAAM,SAAS,GAAG,KAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;oBAC7C,IAAI,SAAS,EAAE,CAAC;wBACd,SAAS,CACP,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EACxB,iDAAiD,CAClD,CAAC;wBACF,OAAO,iBAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;oBAChD,CAAC;gBACH,CAAC;gBACD,GAAG,EAAE,UAAU,CAAC,yBAAyB,CAAC;gBAC1C,KAAK,EAAE,CAAA,SAAmB,CAAA;aAC3B,CACF,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,uCAAW,GAAnB,UAAoB,QAAsB;QACxC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC;IAED,6CAAiB,GAAjB,UAAkB,QAAsB;QACtC,wEAAwE;QACxE,yBAAyB;QACzB,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnC,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED,IAAM,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEvD,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAE1C,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED,kCAAM,GAAN,UAAO,cAAiC;QAAxC,iBAgBC;QAfC,OAAO,MAAM,CAAC,MAAM,CAClB,IAAI,iBAAiB,CACnB,UAAC,QAAQ;YACP,OAAO,cAAc,CAAC,iBAAiB,CACrC,KAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CACjC,CAAC;QACJ,CAAC;QACD,6DAA6D;QAC7D,EAAE,KAAK,EAAE,KAAK,EAAE,CACjB,EACD;YACE,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,cAAc;SACtB,CACF,CAAC;IACJ,CAAC;IAYH,wBAAC;AAAD,CAAC,AApID,IAoIC", "sourcesContent": ["import { <PERSON>e } from \"@wry/trie\";\nimport { canUseWeakMap, canUseWeakSet } from \"../common/canUse.js\";\nimport { checkDocument } from \"./getFromAST.js\";\nimport { invariant } from \"../globals/index.js\";\nimport type { DocumentNode } from \"graphql\";\nimport { WeakCache } from \"@wry/caches\";\nimport { wrap } from \"optimism\";\nimport { cacheSizes } from \"../caching/index.js\";\n\nexport type DocumentTransformCacheKey = ReadonlyArray<unknown>;\n\ntype TransformFn = (document: DocumentNode) => DocumentNode;\n\ninterface DocumentTransformOptions {\n  /**\n   * Determines whether to cache the transformed GraphQL document. Caching can speed up repeated calls to the document transform for the same input document. Set to `false` to completely disable caching for the document transform. When disabled, this option takes precedence over the [`getCacheKey`](#getcachekey) option.\n   *\n   * The default value is `true`.\n   */\n  cache?: boolean;\n  /**\n   * Defines a custom cache key for a GraphQL document that will determine whether to re-run the document transform when given the same input GraphQL document. Returns an array that defines the cache key. Return `undefined` to disable caching for that GraphQL document.\n   *\n   * > **Note:** The items in the array may be any type, but also need to be referentially stable to guarantee a stable cache key.\n   *\n   * The default implementation of this function returns the `document` as the cache key.\n   */\n  getCacheKey?: (\n    document: DocumentNode\n  ) => DocumentTransformCacheKey | undefined;\n}\n\nfunction identity(document: DocumentNode) {\n  return document;\n}\n\nexport class DocumentTransform {\n  private readonly transform: TransformFn;\n  private cached: boolean;\n\n  private readonly resultCache =\n    canUseWeakSet ? new WeakSet<DocumentNode>() : new Set<DocumentNode>();\n\n  // This default implementation of getCacheKey can be overridden by providing\n  // options.getCacheKey to the DocumentTransform constructor. In general, a\n  // getCacheKey function may either return an array of keys (often including\n  // the document) to be used as a cache key, or undefined to indicate the\n  // transform for this document should not be cached.\n  private getCacheKey(\n    document: DocumentNode\n  ): DocumentTransformCacheKey | undefined {\n    return [document];\n  }\n\n  static identity() {\n    // No need to cache this transform since it just returns the document\n    // unchanged. This should save a bit of memory that would otherwise be\n    // needed to populate the `documentCache` of this transform.\n    return new DocumentTransform(identity, { cache: false });\n  }\n\n  static split(\n    predicate: (document: DocumentNode) => boolean,\n    left: DocumentTransform,\n    right: DocumentTransform = DocumentTransform.identity()\n  ) {\n    return Object.assign(\n      new DocumentTransform(\n        (document) => {\n          const documentTransform = predicate(document) ? left : right;\n\n          return documentTransform.transformDocument(document);\n        },\n        // Reasonably assume both `left` and `right` transforms handle their own caching\n        { cache: false }\n      ),\n      { left, right }\n    );\n  }\n\n  constructor(\n    transform: TransformFn,\n    options: DocumentTransformOptions = Object.create(null)\n  ) {\n    this.transform = transform;\n\n    if (options.getCacheKey) {\n      // Override default `getCacheKey` function, which returns [document].\n      this.getCacheKey = options.getCacheKey;\n    }\n    this.cached = options.cache !== false;\n\n    this.resetCache();\n  }\n\n  /**\n   * Resets the internal cache of this transform, if it has one.\n   */\n  resetCache() {\n    if (this.cached) {\n      const stableCacheKeys = new Trie<WeakKey>(canUseWeakMap);\n      this.performWork = wrap(\n        DocumentTransform.prototype.performWork.bind(this),\n        {\n          makeCacheKey: (document) => {\n            const cacheKeys = this.getCacheKey(document);\n            if (cacheKeys) {\n              invariant(\n                Array.isArray(cacheKeys),\n                \"`getCacheKey` must return an array or undefined\"\n              );\n              return stableCacheKeys.lookupArray(cacheKeys);\n            }\n          },\n          max: cacheSizes[\"documentTransform.cache\"],\n          cache: WeakCache<any, any>,\n        }\n      );\n    }\n  }\n\n  private performWork(document: DocumentNode) {\n    checkDocument(document);\n    return this.transform(document);\n  }\n\n  transformDocument(document: DocumentNode) {\n    // If a user passes an already transformed result back to this function,\n    // immediately return it.\n    if (this.resultCache.has(document)) {\n      return document;\n    }\n\n    const transformedDocument = this.performWork(document);\n\n    this.resultCache.add(transformedDocument);\n\n    return transformedDocument;\n  }\n\n  concat(otherTransform: DocumentTransform): DocumentTransform {\n    return Object.assign(\n      new DocumentTransform(\n        (document) => {\n          return otherTransform.transformDocument(\n            this.transformDocument(document)\n          );\n        },\n        // Reasonably assume both transforms handle their own caching\n        { cache: false }\n      ),\n      {\n        left: this,\n        right: otherTransform,\n      }\n    );\n  }\n\n  /**\n   * @internal\n   * Used to iterate through all transforms that are concatenations or `split` links.\n   */\n  readonly left?: DocumentTransform;\n  /**\n   * @internal\n   * Used to iterate through all transforms that are concatenations or `split` links.\n   */\n  readonly right?: DocumentTransform;\n}\n"]}