{"version": 3, "file": "getFromAST.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/getFromAST.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AASnE,OAAO,EAAE,2BAA2B,EAAE,MAAM,iBAAiB,CAAC;AAM9D,+EAA+E;AAC/E,MAAM,UAAU,aAAa,CAAC,GAAiB;IAC7C,SAAS,CACP,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,UAAU,EAC9B,0JAC2E,CAC5E,CAAC;IAEF,IAAM,UAAU,GAAG,GAAG,CAAC,WAAW;SAC/B,MAAM,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,KAAK,oBAAoB,EAA/B,CAA+B,CAAC;SAC9C,GAAG,CAAC,UAAC,UAAU;QACd,IAAI,UAAU,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;YAC9C,MAAM,iBAAiB,CACrB,+DAA6D,EAC7D,UAAU,CAAC,IAAI,CAChB,CAAC;QACJ,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC,CAAC;IAEL,SAAS,CACP,UAAU,CAAC,MAAM,IAAI,CAAC,EACtB,oDAAoD,EACpD,UAAU,CAAC,MAAM,CAClB,CAAC;IAEF,OAAO,GAAG,CAAC;AACb,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,GAAiB;IAEjB,aAAa,CAAC,GAAG,CAAC,CAAC;IACnB,OAAO,GAAG,CAAC,WAAW,CAAC,MAAM,CAC3B,UAAC,UAAU;QACT,OAAA,UAAU,CAAC,IAAI,KAAK,qBAAqB;IAAzC,CAAyC,CAC5C,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,GAAiB;IAChD,OAAO,CACL,GAAG,CAAC,WAAW;SACZ,MAAM,CACL,UAAC,UAAU;QACT,OAAA,UAAU,CAAC,IAAI,KAAK,qBAAqB,IAAI,CAAC,CAAC,UAAU,CAAC,IAAI;IAA9D,CAA8D,CACjE;SACA,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,CAAC,KAAK,EAAZ,CAAY,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CACvC,CAAC;AACJ,CAAC;AAED,yEAAyE;AACzE,MAAM,UAAU,sBAAsB,CACpC,GAAiB;IAEjB,OAAO,GAAG,CAAC,WAAW,CAAC,MAAM,CAC3B,UAAC,UAAU;QACT,OAAA,UAAU,CAAC,IAAI,KAAK,oBAAoB;IAAxC,CAAwC,CAC3C,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,GAAiB;IAClD,IAAM,QAAQ,GAAG,sBAAsB,CAAC,GAAG,CAAE,CAAC;IAE9C,SAAS,CACP,QAAQ,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAC1C,kCAAkC,CACnC,CAAC;IAEF,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,GAAiB;IAEjB,SAAS,CACP,GAAG,CAAC,IAAI,KAAK,UAAU,EACvB,0JAC2E,CAC5E,CAAC;IAEF,SAAS,CACP,GAAG,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,EAC3B,4CAA4C,CAC7C,CAAC;IAEF,IAAM,WAAW,GAAG,GAAG,CAAC,WAAW,CAAC,CAAC,CAA2B,CAAC;IAEjE,SAAS,CACP,WAAW,CAAC,IAAI,KAAK,oBAAoB,EACzC,gCAAgC,CACjC,CAAC;IAEF,OAAO,WAAqC,CAAC;AAC/C,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,iBAAiB,CAC/B,QAAsB;IAEtB,aAAa,CAAC,QAAQ,CAAC,CAAC;IAExB,IAAI,kBAAkB,CAAC;IAEvB,KAAuB,UAAoB,EAApB,KAAA,QAAQ,CAAC,WAAW,EAApB,cAAoB,EAApB,IAAoB,EAAE,CAAC;QAAzC,IAAI,UAAU,SAAA;QACjB,IAAI,UAAU,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;YAC9C,IAAM,SAAS,GAAI,UAAsC,CAAC,SAAS,CAAC;YACpE,IACE,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,UAAU;gBACxB,SAAS,KAAK,cAAc,EAC5B,CAAC;gBACD,OAAO,UAAqC,CAAC;YAC/C,CAAC;QACH,CAAC;QACD,IAAI,UAAU,CAAC,IAAI,KAAK,oBAAoB,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACpE,oEAAoE;YACpE,sCAAsC;YACtC,kBAAkB,GAAG,UAAoC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,IAAI,kBAAkB,EAAE,CAAC;QACvB,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IAED,MAAM,iBAAiB,CACrB,sFAAsF,CACvF,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,UAA+C;IAE/C,IAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC1C,IAAM,IAAI,GAAG,UAAU,IAAI,UAAU,CAAC,mBAAmB,CAAC;IAC1D,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;QACxB,IAAI,CAAC,OAAO,CAAC,UAAC,GAAG;YACf,IAAI,GAAG,CAAC,YAAY,EAAE,CAAC;gBACrB,2BAA2B,CACzB,aAAa,EACb,GAAG,CAAC,QAAQ,CAAC,IAAI,EACjB,GAAG,CAAC,YAAyB,CAC9B,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IACD,OAAO,aAAa,CAAC;AACvB,CAAC", "sourcesContent": ["import { invariant, newInvariantError } from \"../globals/index.js\";\n\nimport type {\n  DocumentNode,\n  OperationDefinitionNode,\n  FragmentDefinitionNode,\n  ValueNode,\n} from \"graphql\";\n\nimport { valueToObjectRepresentation } from \"./storeUtils.js\";\n\ntype OperationDefinitionWithName = OperationDefinitionNode & {\n  name: NonNullable<OperationDefinitionNode[\"name\"]>;\n};\n\n// Checks the document for errors and throws an exception if there is an error.\nexport function checkDocument(doc: DocumentNode) {\n  invariant(\n    doc && doc.kind === \"Document\",\n    `Expecting a parsed GraphQL document. Perhaps you need to wrap the query \\\nstring in a \"gql\" tag? http://docs.apollostack.com/apollo-client/core.html#gql`\n  );\n\n  const operations = doc.definitions\n    .filter((d) => d.kind !== \"FragmentDefinition\")\n    .map((definition) => {\n      if (definition.kind !== \"OperationDefinition\") {\n        throw newInvariantError(\n          `Schema type definitions not allowed in queries. Found: \"%s\"`,\n          definition.kind\n        );\n      }\n      return definition;\n    });\n\n  invariant(\n    operations.length <= 1,\n    `Ambiguous GraphQL document: contains %s operations`,\n    operations.length\n  );\n\n  return doc;\n}\n\nexport function getOperationDefinition(\n  doc: DocumentNode\n): OperationDefinitionNode | undefined {\n  checkDocument(doc);\n  return doc.definitions.filter(\n    (definition): definition is OperationDefinitionNode =>\n      definition.kind === \"OperationDefinition\"\n  )[0];\n}\n\nexport function getOperationName(doc: DocumentNode): string | null {\n  return (\n    doc.definitions\n      .filter(\n        (definition): definition is OperationDefinitionWithName =>\n          definition.kind === \"OperationDefinition\" && !!definition.name\n      )\n      .map((x) => x.name.value)[0] || null\n  );\n}\n\n// Returns the FragmentDefinitions from a particular document as an array\nexport function getFragmentDefinitions(\n  doc: DocumentNode\n): FragmentDefinitionNode[] {\n  return doc.definitions.filter(\n    (definition): definition is FragmentDefinitionNode =>\n      definition.kind === \"FragmentDefinition\"\n  );\n}\n\nexport function getQueryDefinition(doc: DocumentNode): OperationDefinitionNode {\n  const queryDef = getOperationDefinition(doc)!;\n\n  invariant(\n    queryDef && queryDef.operation === \"query\",\n    \"Must contain a query definition.\"\n  );\n\n  return queryDef;\n}\n\nexport function getFragmentDefinition(\n  doc: DocumentNode\n): FragmentDefinitionNode {\n  invariant(\n    doc.kind === \"Document\",\n    `Expecting a parsed GraphQL document. Perhaps you need to wrap the query \\\nstring in a \"gql\" tag? http://docs.apollostack.com/apollo-client/core.html#gql`\n  );\n\n  invariant(\n    doc.definitions.length <= 1,\n    \"Fragment must have exactly one definition.\"\n  );\n\n  const fragmentDef = doc.definitions[0] as FragmentDefinitionNode;\n\n  invariant(\n    fragmentDef.kind === \"FragmentDefinition\",\n    \"Must be a fragment definition.\"\n  );\n\n  return fragmentDef as FragmentDefinitionNode;\n}\n\n/**\n * Returns the first operation definition found in this document.\n * If no operation definition is found, the first fragment definition will be returned.\n * If no definitions are found, an error will be thrown.\n */\nexport function getMainDefinition(\n  queryDoc: DocumentNode\n): OperationDefinitionNode | FragmentDefinitionNode {\n  checkDocument(queryDoc);\n\n  let fragmentDefinition;\n\n  for (let definition of queryDoc.definitions) {\n    if (definition.kind === \"OperationDefinition\") {\n      const operation = (definition as OperationDefinitionNode).operation;\n      if (\n        operation === \"query\" ||\n        operation === \"mutation\" ||\n        operation === \"subscription\"\n      ) {\n        return definition as OperationDefinitionNode;\n      }\n    }\n    if (definition.kind === \"FragmentDefinition\" && !fragmentDefinition) {\n      // we do this because we want to allow multiple fragment definitions\n      // to precede an operation definition.\n      fragmentDefinition = definition as FragmentDefinitionNode;\n    }\n  }\n\n  if (fragmentDefinition) {\n    return fragmentDefinition;\n  }\n\n  throw newInvariantError(\n    \"Expected a parsed GraphQL query with a query, mutation, subscription, or a fragment.\"\n  );\n}\n\nexport function getDefaultValues(\n  definition: OperationDefinitionNode | undefined\n): Record<string, any> {\n  const defaultValues = Object.create(null);\n  const defs = definition && definition.variableDefinitions;\n  if (defs && defs.length) {\n    defs.forEach((def) => {\n      if (def.defaultValue) {\n        valueToObjectRepresentation(\n          defaultValues,\n          def.variable.name,\n          def.defaultValue as ValueNode\n        );\n      }\n    });\n  }\n  return defaultValues;\n}\n"]}