{"version": 3, "file": "storeUtils.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/storeUtils.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAuBxD,OAAO,EAAE,eAAe,EAAE,MAAM,sBAAsB,CAAC;AAEvD,OAAO,EAAE,wBAAwB,EAAE,MAAM,gBAAgB,CAAC;AAC1D,OAAO,EAAE,kBAAkB,EAAE,MAAM,iCAAiC,CAAC;AAMrE,MAAM,UAAU,aAAa,CAAC,EAAU;IACtC,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC;AAC/B,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,GAAQ;IAClC,OAAO,OAAO,CACZ,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,CAChE,CAAC;AACJ,CAAC;AAoCD,MAAM,UAAU,cAAc,CAAC,KAAU;IACvC,OAAO,CACL,eAAe,CAAC,KAAK,CAAC;QACrB,KAAsB,CAAC,IAAI,KAAK,UAAU;QAC3C,KAAK,CAAC,OAAO,CAAE,KAAsB,CAAC,WAAW,CAAC,CACnD,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,KAAgB;IACrC,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC;AACtC,CAAC;AAED,SAAS,cAAc,CAAC,KAAgB;IACtC,OAAO,KAAK,CAAC,IAAI,KAAK,cAAc,CAAC;AACvC,CAAC;AAED,SAAS,UAAU,CAAC,KAAgB;IAClC,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AACnC,CAAC;AAED,SAAS,YAAY,CAAC,KAAgB;IACpC,OAAO,KAAK,CAAC,IAAI,KAAK,YAAY,CAAC;AACrC,CAAC;AAED,SAAS,UAAU,CAAC,KAAgB;IAClC,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AACnC,CAAC;AAED,SAAS,aAAa,CAAC,KAAgB;IACrC,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC;AACtC,CAAC;AAED,SAAS,WAAW,CAAC,KAAgB;IACnC,OAAO,KAAK,CAAC,IAAI,KAAK,WAAW,CAAC;AACpC,CAAC;AAED,SAAS,WAAW,CAAC,KAAgB;IACnC,OAAO,KAAK,CAAC,IAAI,KAAK,WAAW,CAAC;AACpC,CAAC;AAED,SAAS,WAAW,CAAC,KAAgB;IACnC,OAAO,KAAK,CAAC,IAAI,KAAK,WAAW,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,2BAA2B,CACzC,MAAW,EACX,IAAc,EACd,KAAgB,EAChB,SAAkB;IAElB,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3C,CAAC;SAAM,IAAI,cAAc,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;QACzD,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;IACnC,CAAC;SAAM,IAAI,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;QAChC,IAAM,cAAY,GAAG,EAAE,CAAC;QACxB,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,UAAC,GAAG;YACnB,OAAA,2BAA2B,CAAC,cAAY,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC;QAAzE,CAAyE,CAC1E,CAAC;QACF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,cAAY,CAAC;IACpC,CAAC;SAAM,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;QAC7B,IAAM,aAAa,GAAG,CAAC,SAAS,IAAK,EAAU,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,aAAa,CAAC;IACrC,CAAC;SAAM,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,UAAC,SAAS;YAC9C,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAC7B,2BAA2B,CACzB,iBAAiB,EACjB,IAAI,EACJ,SAAS,EACT,SAAS,CACV,CAAC;YACF,OAAQ,iBAAyB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;SAAM,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAI,KAAuB,CAAC,KAAK,CAAC;IACtD,CAAC;SAAM,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;QAC9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;IAC5B,CAAC;SAAM,CAAC;QACN,MAAM,iBAAiB,CACrB,2CAAuC;YACrC,iEAAiE;YACjE,2BAA2B,EAC7B,IAAI,CAAC,KAAK,EACT,KAAa,CAAC,IAAI,CACpB,CAAC;IACJ,CAAC;AACH,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,KAAgB,EAChB,SAAkB;IAElB,IAAI,aAAa,GAAQ,IAAI,CAAC;IAC9B,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;QACrB,aAAa,GAAG,EAAE,CAAC;QACnB,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS;YACjC,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;YAEzC,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;gBACxB,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,EAAe;wBAAb,IAAI,UAAA,EAAE,KAAK,WAAA;oBACxC,OAAA,2BAA2B,CACzB,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EACnC,IAAI,EACJ,KAAK,EACL,SAAS,CACV;gBALD,CAKC,CACF,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,MAAM,GAAQ,IAAI,CAAC;IACvB,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QAC9C,MAAM,GAAG,EAAE,CAAC;QACZ,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,EAAe;gBAAb,IAAI,UAAA,EAAE,KAAK,WAAA;YACpC,OAAA,2BAA2B,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC;QAA3D,CAA2D,CAC5D,CAAC;IACJ,CAAC;IAED,OAAO,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;AAClE,CAAC;AAQD,IAAM,gBAAgB,GAAa;IACjC,YAAY;IACZ,SAAS;IACT,MAAM;IACN,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,aAAa;CACd,CAAC;AAEF,+EAA+E;AAC/E,oDAAoD;AACpD,sDAAsD;AACtD,IAAI,qBAAqB,GAA2B,kBAAkB,CAAC;AAEvE,MAAM,CAAC,IAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CAC1C,UACE,SAAiB,EACjB,IAAiC,EACjC,UAAuB;IAEvB,IACE,IAAI;QACJ,UAAU;QACV,UAAU,CAAC,YAAY,CAAC;QACxB,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,EAC/B,CAAC;QACD,IACE,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC;YACjC,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAc,CAAC,MAAM,GAAG,CAAC,EAC3D,CAAC;YACD,IAAM,UAAU,GACd,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACjC,UAAU,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAc;gBAClD,CAAC,CAAC,EAAE,CAAC;YACP,UAAU,CAAC,IAAI,EAAE,CAAC;YAElB,IAAM,cAAY,GAAG,EAA4B,CAAC;YAClD,UAAU,CAAC,OAAO,CAAC,UAAC,GAAG;gBACrB,cAAY,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;YAEH,OAAO,UAAG,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,cAAI,qBAAqB,CAChE,cAAY,CACb,MAAG,CAAC;QACP,CAAC;aAAM,CAAC;YACN,OAAO,UAAU,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,IAAI,iBAAiB,GAAW,SAAS,CAAC;IAE1C,IAAI,IAAI,EAAE,CAAC;QACT,mEAAmE;QACnE,sEAAsE;QACtE,yEAAyE;QACzE,IAAM,eAAe,GAAW,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC5D,iBAAiB,IAAI,WAAI,eAAe,MAAG,CAAC;IAC9C,CAAC;IAED,IAAI,UAAU,EAAE,CAAC;QACf,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAClC,IAAI,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBAAE,OAAO;YACjD,IAAI,UAAU,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;gBAC3D,iBAAiB,IAAI,WAAI,GAAG,cAAI,qBAAqB,CACnD,UAAU,CAAC,GAAG,CAAC,CAChB,MAAG,CAAC;YACP,CAAC;iBAAM,CAAC;gBACN,iBAAiB,IAAI,WAAI,GAAG,CAAE,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,iBAAiB,CAAC;AAC3B,CAAC,EACD;IACE,YAAY,YAAC,CAA+B;QAC1C,IAAM,QAAQ,GAAG,qBAAqB,CAAC;QACvC,qBAAqB,GAAG,CAAC,CAAC;QAC1B,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF,CACF,CAAC;AAEF,MAAM,UAAU,wBAAwB,CACtC,KAAgC,EAChC,SAA+B;IAE/B,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;QAC9C,IAAM,QAAM,GAAW,EAAE,CAAC;QAC1B,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,EAAe;gBAAb,IAAI,UAAA,EAAE,KAAK,WAAA;YACpC,OAAA,2BAA2B,CAAC,QAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC;QAA3D,CAA2D,CAC5D,CAAC;QACF,OAAO,QAAM,CAAC;IAChB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,UAAU,sBAAsB,CAAC,KAAgB;IACrD,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AAC5D,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,MAA2B,EAC3B,YAA8B,EAC9B,WAAyB;IAEzB,IAAI,SAAqE,CAAC;IAC1E,KAAwB,UAAuB,EAAvB,KAAA,YAAY,CAAC,UAAU,EAAvB,cAAuB,EAAvB,IAAuB,EAAE,CAAC;QAA7C,IAAM,SAAS,SAAA;QAClB,IAAI,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;YACvB,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY,EAAE,CAAC;gBAC1C,OAAO,MAAM,CAAC,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,EAAE,CAAC;YACrB,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,SAAS,GAAG,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC;IACH,CAAC;IACD,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;QAC1C,OAAO,MAAM,CAAC,UAAU,CAAC;IAC3B,CAAC;IACD,IAAI,SAAS,EAAE,CAAC;QACd,KAAwB,UAAS,EAAT,uBAAS,EAAT,uBAAS,EAAT,IAAS,EAAE,CAAC;YAA/B,IAAM,SAAS,kBAAA;YAClB,IAAM,QAAQ,GAAG,qBAAqB,CACpC,MAAM,EACN,wBAAwB,CAAC,SAAS,EAAE,WAAW,CAAE,CAAC,YAAY,EAC9D,WAAW,CACZ,CAAC;YACF,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,OAAO,QAAQ,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;AACH,CAAC;AAED,MAAM,UAAU,OAAO,CAAC,SAAwB;IAC9C,OAAO,SAAS,CAAC,IAAI,KAAK,OAAO,CAAC;AACpC,CAAC;AAED,MAAM,UAAU,gBAAgB,CAC9B,SAAwB;IAExB,OAAO,SAAS,CAAC,IAAI,KAAK,gBAAgB,CAAC;AAC7C,CAAC", "sourcesContent": ["import { newInvariantError } from \"../globals/index.js\";\n\nimport type {\n  DirectiveNode,\n  FieldNode,\n  IntValueNode,\n  FloatValueNode,\n  StringValueNode,\n  BooleanValueNode,\n  ObjectValueNode,\n  ListValueNode,\n  EnumValueNode,\n  NullValueNode,\n  VariableNode,\n  InlineFragmentNode,\n  ValueNode,\n  SelectionNode,\n  NameNode,\n  SelectionSetNode,\n  DocumentNode,\n  FragmentSpreadNode,\n} from \"graphql\";\n\nimport { isNonNullObject } from \"../common/objects.js\";\nimport type { FragmentMap } from \"./fragments.js\";\nimport { getFragmentFromSelection } from \"./fragments.js\";\nimport { canonicalStringify } from \"../common/canonicalStringify.js\";\n\nexport interface Reference {\n  readonly __ref: string;\n}\n\nexport function makeReference(id: string): Reference {\n  return { __ref: String(id) };\n}\n\nexport function isReference(obj: any): obj is Reference {\n  return Boolean(\n    obj && typeof obj === \"object\" && typeof obj.__ref === \"string\"\n  );\n}\n\nexport type StoreValue =\n  | number\n  | string\n  | string[]\n  | Reference\n  | Reference[]\n  | null\n  | undefined\n  | void\n  | Object;\n\nexport interface StoreObject {\n  __typename?: string;\n  [storeFieldName: string]: StoreValue;\n}\n\n/**\n * Workaround for a TypeScript quirk:\n * types per default have an implicit index signature that makes them\n * assignable to `StoreObject`.\n * interfaces do not have that implicit index signature, so they cannot\n * be assigned to `StoreObject`.\n * This type just maps over a type or interface that is passed in,\n * implicitly adding the index signature.\n * That way, the result can be assigned to `StoreObject`.\n *\n * This is important if some user-defined interface is used e.g.\n * in cache.modify, where the `toReference` method expects a\n * `StoreObject` as input.\n */\nexport type AsStoreObject<T extends { __typename?: string }> = {\n  [K in keyof T]: T[K];\n};\n\nexport function isDocumentNode(value: any): value is DocumentNode {\n  return (\n    isNonNullObject(value) &&\n    (value as DocumentNode).kind === \"Document\" &&\n    Array.isArray((value as DocumentNode).definitions)\n  );\n}\n\nfunction isStringValue(value: ValueNode): value is StringValueNode {\n  return value.kind === \"StringValue\";\n}\n\nfunction isBooleanValue(value: ValueNode): value is BooleanValueNode {\n  return value.kind === \"BooleanValue\";\n}\n\nfunction isIntValue(value: ValueNode): value is IntValueNode {\n  return value.kind === \"IntValue\";\n}\n\nfunction isFloatValue(value: ValueNode): value is FloatValueNode {\n  return value.kind === \"FloatValue\";\n}\n\nfunction isVariable(value: ValueNode): value is VariableNode {\n  return value.kind === \"Variable\";\n}\n\nfunction isObjectValue(value: ValueNode): value is ObjectValueNode {\n  return value.kind === \"ObjectValue\";\n}\n\nfunction isListValue(value: ValueNode): value is ListValueNode {\n  return value.kind === \"ListValue\";\n}\n\nfunction isEnumValue(value: ValueNode): value is EnumValueNode {\n  return value.kind === \"EnumValue\";\n}\n\nfunction isNullValue(value: ValueNode): value is NullValueNode {\n  return value.kind === \"NullValue\";\n}\n\nexport function valueToObjectRepresentation(\n  argObj: any,\n  name: NameNode,\n  value: ValueNode,\n  variables?: Object\n) {\n  if (isIntValue(value) || isFloatValue(value)) {\n    argObj[name.value] = Number(value.value);\n  } else if (isBooleanValue(value) || isStringValue(value)) {\n    argObj[name.value] = value.value;\n  } else if (isObjectValue(value)) {\n    const nestedArgObj = {};\n    value.fields.map((obj) =>\n      valueToObjectRepresentation(nestedArgObj, obj.name, obj.value, variables)\n    );\n    argObj[name.value] = nestedArgObj;\n  } else if (isVariable(value)) {\n    const variableValue = (variables || ({} as any))[value.name.value];\n    argObj[name.value] = variableValue;\n  } else if (isListValue(value)) {\n    argObj[name.value] = value.values.map((listValue) => {\n      const nestedArgArrayObj = {};\n      valueToObjectRepresentation(\n        nestedArgArrayObj,\n        name,\n        listValue,\n        variables\n      );\n      return (nestedArgArrayObj as any)[name.value];\n    });\n  } else if (isEnumValue(value)) {\n    argObj[name.value] = (value as EnumValueNode).value;\n  } else if (isNullValue(value)) {\n    argObj[name.value] = null;\n  } else {\n    throw newInvariantError(\n      `The inline argument \"%s\" of kind \"%s\"` +\n        \"is not supported. Use variables instead of inline arguments to \" +\n        \"overcome this limitation.\",\n      name.value,\n      (value as any).kind\n    );\n  }\n}\n\nexport function storeKeyNameFromField(\n  field: FieldNode,\n  variables?: Object\n): string {\n  let directivesObj: any = null;\n  if (field.directives) {\n    directivesObj = {};\n    field.directives.forEach((directive) => {\n      directivesObj[directive.name.value] = {};\n\n      if (directive.arguments) {\n        directive.arguments.forEach(({ name, value }) =>\n          valueToObjectRepresentation(\n            directivesObj[directive.name.value],\n            name,\n            value,\n            variables\n          )\n        );\n      }\n    });\n  }\n\n  let argObj: any = null;\n  if (field.arguments && field.arguments.length) {\n    argObj = {};\n    field.arguments.forEach(({ name, value }) =>\n      valueToObjectRepresentation(argObj, name, value, variables)\n    );\n  }\n\n  return getStoreKeyName(field.name.value, argObj, directivesObj);\n}\n\nexport type Directives = {\n  [directiveName: string]: {\n    [argName: string]: any;\n  };\n};\n\nconst KNOWN_DIRECTIVES: string[] = [\n  \"connection\",\n  \"include\",\n  \"skip\",\n  \"client\",\n  \"rest\",\n  \"export\",\n  \"nonreactive\",\n];\n\n// Default stable JSON.stringify implementation used by getStoreKeyName. Can be\n// updated/replaced with something better by calling\n// getStoreKeyName.setStringify(newStringifyFunction).\nlet storeKeyNameStringify: (value: any) => string = canonicalStringify;\n\nexport const getStoreKeyName = Object.assign(\n  function (\n    fieldName: string,\n    args?: Record<string, any> | null,\n    directives?: Directives\n  ): string {\n    if (\n      args &&\n      directives &&\n      directives[\"connection\"] &&\n      directives[\"connection\"][\"key\"]\n    ) {\n      if (\n        directives[\"connection\"][\"filter\"] &&\n        (directives[\"connection\"][\"filter\"] as string[]).length > 0\n      ) {\n        const filterKeys =\n          directives[\"connection\"][\"filter\"] ?\n            (directives[\"connection\"][\"filter\"] as string[])\n          : [];\n        filterKeys.sort();\n\n        const filteredArgs = {} as { [key: string]: any };\n        filterKeys.forEach((key) => {\n          filteredArgs[key] = args[key];\n        });\n\n        return `${directives[\"connection\"][\"key\"]}(${storeKeyNameStringify(\n          filteredArgs\n        )})`;\n      } else {\n        return directives[\"connection\"][\"key\"];\n      }\n    }\n\n    let completeFieldName: string = fieldName;\n\n    if (args) {\n      // We can't use `JSON.stringify` here since it's non-deterministic,\n      // and can lead to different store key names being created even though\n      // the `args` object used during creation has the same properties/values.\n      const stringifiedArgs: string = storeKeyNameStringify(args);\n      completeFieldName += `(${stringifiedArgs})`;\n    }\n\n    if (directives) {\n      Object.keys(directives).forEach((key) => {\n        if (KNOWN_DIRECTIVES.indexOf(key) !== -1) return;\n        if (directives[key] && Object.keys(directives[key]).length) {\n          completeFieldName += `@${key}(${storeKeyNameStringify(\n            directives[key]\n          )})`;\n        } else {\n          completeFieldName += `@${key}`;\n        }\n      });\n    }\n\n    return completeFieldName;\n  },\n  {\n    setStringify(s: typeof storeKeyNameStringify) {\n      const previous = storeKeyNameStringify;\n      storeKeyNameStringify = s;\n      return previous;\n    },\n  }\n);\n\nexport function argumentsObjectFromField(\n  field: FieldNode | DirectiveNode,\n  variables?: Record<string, any>\n): Object | null {\n  if (field.arguments && field.arguments.length) {\n    const argObj: Object = {};\n    field.arguments.forEach(({ name, value }) =>\n      valueToObjectRepresentation(argObj, name, value, variables)\n    );\n    return argObj;\n  }\n  return null;\n}\n\nexport function resultKeyNameFromField(field: FieldNode): string {\n  return field.alias ? field.alias.value : field.name.value;\n}\n\nexport function getTypenameFromResult(\n  result: Record<string, any>,\n  selectionSet: SelectionSetNode,\n  fragmentMap?: FragmentMap\n): string | undefined {\n  let fragments: undefined | Array<InlineFragmentNode | FragmentSpreadNode>;\n  for (const selection of selectionSet.selections) {\n    if (isField(selection)) {\n      if (selection.name.value === \"__typename\") {\n        return result[resultKeyNameFromField(selection)];\n      }\n    } else if (fragments) {\n      fragments.push(selection);\n    } else {\n      fragments = [selection];\n    }\n  }\n  if (typeof result.__typename === \"string\") {\n    return result.__typename;\n  }\n  if (fragments) {\n    for (const selection of fragments) {\n      const typename = getTypenameFromResult(\n        result,\n        getFragmentFromSelection(selection, fragmentMap)!.selectionSet,\n        fragmentMap\n      );\n      if (typeof typename === \"string\") {\n        return typename;\n      }\n    }\n  }\n}\n\nexport function isField(selection: SelectionNode): selection is FieldNode {\n  return selection.kind === \"Field\";\n}\n\nexport function isInlineFragment(\n  selection: SelectionNode\n): selection is InlineFragmentNode {\n  return selection.kind === \"InlineFragment\";\n}\n\nexport type VariableValue = (node: VariableNode) => any;\n"]}