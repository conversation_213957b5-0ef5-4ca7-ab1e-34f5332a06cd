{"version": 3, "file": "directives.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/directives.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAehD,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAM7C,MAAM,UAAU,aAAa,CAC3B,EAA6B,EAC7B,SAA+B;QAD7B,UAAU,gBAAA;IAGZ,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,sBAAsB,CAAC,UAAU,CAAC,CAAC,KAAK,CAC7C,UAAC,EAAyB;YAAvB,SAAS,eAAA,EAAE,UAAU,gBAAA;QACtB,IAAI,WAAW,GAAY,KAAK,CAAC;QACjC,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACzC,WAAW;gBACT,SAAS,IAAI,SAAS,CAAE,UAAU,CAAC,KAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxE,SAAS,CACP,WAAW,KAAK,KAAK,CAAC,EACtB,+CAA+C,EAC/C,SAAS,CAAC,IAAI,CAAC,KAAK,CACrB,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,WAAW,GAAI,UAAU,CAAC,KAA0B,CAAC,KAAK,CAAC;QAC7D,CAAC;QACD,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC;IACtE,CAAC,CACF,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,IAAa;IAC7C,IAAM,KAAK,GAAa,EAAE,CAAC;IAE3B,KAAK,CAAC,IAAI,EAAE;QACV,SAAS,YAAC,IAAmB;YAC3B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9B,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC;AAED,MAAM,CAAC,IAAM,gBAAgB,GAAG,UAAC,KAAe,EAAE,IAAa;IAC7D,OAAA,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,KAAK,CAAC;AAAjC,CAAiC,CAAC;AAEpC,MAAM,CAAC,IAAM,gBAAgB,GAAG,UAAC,KAAe,EAAE,IAAa;IAC7D,OAAA,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;AAAhC,CAAgC,CAAC;AAEnC,MAAM,UAAU,aAAa,CAAC,KAAe,EAAE,IAAa,EAAE,GAAa;IACzE,IAAM,OAAO,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;IAEjC,KAAK,CAAC,IAAI,EAAE;QACV,SAAS,YAAC,IAAI;YACZ,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;KACF,CAAC,CAAC;IAEH,0EAA0E;IAC1E,uDAAuD;IACvD,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,GAAG,WAAW,CAAC;AAC1D,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,QAAsB;IACrD,OAAO,QAAQ,IAAI,aAAa,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AACzE,CAAC;AAOD,SAAS,oBAAoB,CAAC,EAAkC;QAAxB,KAAK,gBAAA;IAC3C,OAAO,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,SAAS,CAAC;AACjD,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,UAAwC;IAExC,IAAM,MAAM,GAAwB,EAAE,CAAC;IAEvC,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;QACpC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS;YAC3B,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC;gBAAE,OAAO;YAE7C,IAAM,kBAAkB,GAAG,SAAS,CAAC,SAAS,CAAC;YAC/C,IAAM,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;YAE3C,SAAS,CACP,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EACrD,sDAAsD,EACtD,aAAa,CACd,CAAC;YAEF,IAAM,UAAU,GAAG,kBAAmB,CAAC,CAAC,CAAC,CAAC;YAC1C,SAAS,CACP,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,EACjD,yCAAyC,EACzC,aAAa,CACd,CAAC;YAEF,IAAM,OAAO,GAAc,UAAU,CAAC,KAAK,CAAC;YAE5C,qFAAqF;YACrF,SAAS,CACP,OAAO;gBACL,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,IAAI,OAAO,CAAC,IAAI,KAAK,cAAc,CAAC,EAClE,uEAAuE,EACvE,aAAa,CACd,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,UAAU,YAAA,EAAE,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,gBAAgB;AAChB,MAAM,UAAU,mBAAmB,CACjC,QAA4B;;IAE5B,IAAM,SAAS,GAAG,MAAA,QAAQ,CAAC,UAAU,0CAAE,IAAI,CACzC,UAAC,EAAQ;YAAN,IAAI,UAAA;QAAO,OAAA,IAAI,CAAC,KAAK,KAAK,QAAQ;IAAvB,CAAuB,CACtC,CAAC;IAEF,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAM,OAAO,GAAG,MAAA,SAAS,CAAC,SAAS,0CAAE,IAAI,CACvC,UAAC,EAAQ;YAAN,IAAI,UAAA;QAAO,OAAA,IAAI,CAAC,KAAK,KAAK,MAAM;IAArB,CAAqB,CACpC,CAAC;IAEF,IAAI,OAAO,EAAE,CAAC;QACZ,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACzC,SAAS,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACxE,CAAC;iBAAM,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC9C,SAAS,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;YACpE,CAAC;iBAAM,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7C,SAAS,CAAC,IAAI,CACZ,wDAAwD,EACxD,OAAO,CAAC,KAAK,CAAC,KAAK,CACpB,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,IACE,OAAO;QACP,OAAO,IAAI,OAAO,CAAC,KAAK;QACxB,OAAO,CAAC,KAAK,CAAC,KAAK,KAAK,SAAS,EACjC,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["import { invariant } from \"../globals/index.js\";\n\n// Provides the methods that allow QueryManager to handle the `skip` and\n// `include` directives within GraphQL.\nimport type {\n  SelectionNode,\n  VariableNode,\n  BooleanValueNode,\n  DirectiveNode,\n  DocumentNode,\n  ArgumentNode,\n  ValueNode,\n  ASTNode,\n  FragmentSpreadNode,\n} from \"graphql\";\nimport { visit, BREAK, Kind } from \"graphql\";\n\nexport type DirectiveInfo = {\n  [fieldName: string]: { [argName: string]: any };\n};\n\nexport function shouldInclude(\n  { directives }: SelectionNode,\n  variables?: Record<string, any>\n): boolean {\n  if (!directives || !directives.length) {\n    return true;\n  }\n  return getInclusionDirectives(directives).every(\n    ({ directive, ifArgument }) => {\n      let evaledValue: boolean = false;\n      if (ifArgument.value.kind === \"Variable\") {\n        evaledValue =\n          variables && variables[(ifArgument.value as VariableNode).name.value];\n        invariant(\n          evaledValue !== void 0,\n          `Invalid variable referenced in @%s directive.`,\n          directive.name.value\n        );\n      } else {\n        evaledValue = (ifArgument.value as BooleanValueNode).value;\n      }\n      return directive.name.value === \"skip\" ? !evaledValue : evaledValue;\n    }\n  );\n}\n\nexport function getDirectiveNames(root: ASTNode) {\n  const names: string[] = [];\n\n  visit(root, {\n    Directive(node: DirectiveNode) {\n      names.push(node.name.value);\n    },\n  });\n\n  return names;\n}\n\nexport const hasAnyDirectives = (names: string[], root: ASTNode) =>\n  hasDirectives(names, root, false);\n\nexport const hasAllDirectives = (names: string[], root: ASTNode) =>\n  hasDirectives(names, root, true);\n\nexport function hasDirectives(names: string[], root: ASTNode, all?: boolean) {\n  const nameSet = new Set(names);\n  const uniqueCount = nameSet.size;\n\n  visit(root, {\n    Directive(node) {\n      if (nameSet.delete(node.name.value) && (!all || !nameSet.size)) {\n        return BREAK;\n      }\n    },\n  });\n\n  // If we found all the names, nameSet will be empty. If we only care about\n  // finding some of them, the < condition is sufficient.\n  return all ? !nameSet.size : nameSet.size < uniqueCount;\n}\n\nexport function hasClientExports(document: DocumentNode) {\n  return document && hasDirectives([\"client\", \"export\"], document, true);\n}\n\nexport type InclusionDirectives = Array<{\n  directive: DirectiveNode;\n  ifArgument: ArgumentNode;\n}>;\n\nfunction isInclusionDirective({ name: { value } }: DirectiveNode): boolean {\n  return value === \"skip\" || value === \"include\";\n}\n\nexport function getInclusionDirectives(\n  directives: ReadonlyArray<DirectiveNode>\n): InclusionDirectives {\n  const result: InclusionDirectives = [];\n\n  if (directives && directives.length) {\n    directives.forEach((directive) => {\n      if (!isInclusionDirective(directive)) return;\n\n      const directiveArguments = directive.arguments;\n      const directiveName = directive.name.value;\n\n      invariant(\n        directiveArguments && directiveArguments.length === 1,\n        `Incorrect number of arguments for the @%s directive.`,\n        directiveName\n      );\n\n      const ifArgument = directiveArguments![0];\n      invariant(\n        ifArgument.name && ifArgument.name.value === \"if\",\n        `Invalid argument for the @%s directive.`,\n        directiveName\n      );\n\n      const ifValue: ValueNode = ifArgument.value;\n\n      // means it has to be a variable value if this is a valid @skip or @include directive\n      invariant(\n        ifValue &&\n          (ifValue.kind === \"Variable\" || ifValue.kind === \"BooleanValue\"),\n        `Argument for the @%s directive must be a variable or a boolean value.`,\n        directiveName\n      );\n\n      result.push({ directive, ifArgument });\n    });\n  }\n\n  return result;\n}\n\n/** @internal */\nexport function getFragmentMaskMode(\n  fragment: FragmentSpreadNode\n): \"mask\" | \"migrate\" | \"unmask\" {\n  const directive = fragment.directives?.find(\n    ({ name }) => name.value === \"unmask\"\n  );\n\n  if (!directive) {\n    return \"mask\";\n  }\n\n  const modeArg = directive.arguments?.find(\n    ({ name }) => name.value === \"mode\"\n  );\n\n  if (__DEV__) {\n    if (modeArg) {\n      if (modeArg.value.kind === Kind.VARIABLE) {\n        invariant.warn(\"@unmask 'mode' argument does not support variables.\");\n      } else if (modeArg.value.kind !== Kind.STRING) {\n        invariant.warn(\"@unmask 'mode' argument must be of type string.\");\n      } else if (modeArg.value.value !== \"migrate\") {\n        invariant.warn(\n          \"@unmask 'mode' argument does not recognize value '%s'.\",\n          modeArg.value.value\n        );\n      }\n    }\n  }\n\n  if (\n    modeArg &&\n    \"value\" in modeArg.value &&\n    modeArg.value.value === \"migrate\"\n  ) {\n    return \"migrate\";\n  }\n\n  return \"unmask\";\n}\n"]}