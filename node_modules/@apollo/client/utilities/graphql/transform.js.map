{"version": 3, "file": "transform.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/transform.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,qBAAqB,CAAC;AAiBhD,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAEtC,OAAO,EACL,aAAa,EACb,sBAAsB,EACtB,qBAAqB,EACrB,sBAAsB,EACtB,iBAAiB,GAClB,MAAM,iBAAiB,CAAC;AACzB,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAE1C,OAAO,EAAE,iBAAiB,EAAE,MAAM,gBAAgB,CAAC;AACnD,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AA6B/D,IAAM,cAAc,GAAc;IAChC,IAAI,EAAE,IAAI,CAAC,KAAK;IAChB,IAAI,EAAE;QACJ,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,KAAK,EAAE,YAAY;KACpB;CACF,CAAC;AAEF,SAAS,OAAO,CACd,EAAoD,EACpD,WAAwB;IAExB,OAAO,CACL,CAAC,EAAE;QACH,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAC9B,UAAC,SAAS;YACR,OAAA,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe;gBACvC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,WAAW,CAAC;QADvD,CACuD,CAC1D,CACF,CAAC;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,GAAiB;IACzC,OAAO,CACH,OAAO,CACL,sBAAsB,CAAC,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,CAAC,EACzD,iBAAiB,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAC/C,CACF,CAAC,CAAC;QACD,IAAI;QACN,CAAC,CAAC,GAAG,CAAC;AACV,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAAuD;IAEvD,IAAM,KAAK,GAAG,IAAI,GAAG,EAAsD,CAAC;IAE5E,IAAM,KAAK,GAAG,IAAI,GAAG,EAGlB,CAAC;IAEJ,OAAO,CAAC,OAAO,CAAC,UAAC,SAAS;QACxB,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACvC,CAAC;iBAAM,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;gBAC1B,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,UAAC,SAAwB;QAC9B,IAAI,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YAC1B,KAAK,CAAC,OAAO,CAAC,UAAC,UAAU,EAAE,IAAI;gBAC7B,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;oBACpB,MAAM,GAAG,UAAU,CAAC;gBACtB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAcD,SAAS,uBAAuB,CAAO,UAAgB;IACrD,IAAM,GAAG,GAAG,IAAI,GAAG,EAA2B,CAAC;IAE/C,OAAO,SAAS,mBAAmB,CACjC,GAAsB;QAAtB,oBAAA,EAAA,gBAAsB;QAEtB,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,GAAG,CAAC,GAAG,CACL,GAAG,EACH,CAAC,KAAK,GAAG;gBACP,+DAA+D;gBAC/D,qEAAqE;gBACrE,kEAAkE;gBAClE,sCAAsC;gBACtC,SAAS,EAAE,IAAI,GAAG,EAAE;gBACpB,eAAe,EAAE,IAAI,GAAG,EAAE;aAC3B,CAAC,CACH,CAAC;QACJ,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,4BAA4B,CAC1C,UAAmC,EACnC,GAAiB;IAEjB,aAAa,CAAC,GAAG,CAAC,CAAC;IAEnB,6EAA6E;IAC7E,2EAA2E;IAC3E,0EAA0E;IAC1E,qCAAqC;IACrC,IAAM,uBAAuB,GAAG,uBAAuB,CAAS,EAAE,CAAC,CAAC;IACpE,IAAM,sBAAsB,GAAG,uBAAuB,CAAS,EAAE,CAAC,CAAC;IACnE,IAAM,QAAQ,GAAG,UACf,SAAoD;QAEpD,KACE,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ,SAA8B,EACjD,CAAC,GAAG,SAAS,CAAC,MAAM,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,EACjD,EAAE,CAAC,EACH,CAAC;YACD,IAAI,OAAO,CAAC,QAAQ,CAAC;gBAAE,SAAS;YAChC,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,oBAAoB,EAAE,CAAC;gBAChD,oEAAoE;gBACpE,OAAO,uBAAuB,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvE,CAAC;YACD,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC/C,OAAO,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;QACD,SAAS,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;QACxD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IAEF,IAAI,cAAc,GAAG,CAAC,CAAC;IACvB,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;QACrD,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1D,EAAE,cAAc,CAAC;QACnB,CAAC;IACH,CAAC;IAED,IAAM,gBAAgB,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;IACzD,IAAM,iBAAiB,GAAG,UAAC,cAAuC;QAChE,OAAA,eAAe,CAAC,cAAc,CAAC;YAC/B,cAAc;iBACX,GAAG,CAAC,gBAAgB,CAAC;iBACrB,IAAI,CACH,UAAC,MAAyC,IAAK,OAAA,MAAM,IAAI,MAAM,CAAC,MAAM,EAAvB,CAAuB,CACvE;IALH,CAKG,CAAC;IAEN,IAAM,0BAA0B,GAAG,IAAI,GAAG,EAAkC,CAAC;IAE7E,yEAAyE;IACzE,0EAA0E;IAC1E,6EAA6E;IAC7E,0EAA0E;IAC1E,sDAAsD;IACtD,IAAI,qBAAqB,GAAG,KAAK,CAAC;IAElC,IAAM,4BAA4B,GAE9B;QACF,KAAK,YAAC,IAAI;YACR,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBACvC,qBAAqB,GAAG,IAAI,CAAC;gBAC7B,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;KACF,CAAC;IAEF,IAAM,2BAA2B,GAAG,KAAK,CAAC,GAAG,EAAE;QAC7C,yEAAyE;QACzE,KAAK,EAAE,4BAA4B;QACnC,cAAc,EAAE,4BAA4B;QAE5C,kBAAkB,EAAE;YAClB,KAAK;gBACH,oEAAoE;gBACpE,uEAAuE;gBACvE,iEAAiE;gBACjE,+DAA+D;gBAC/D,OAAO,KAAK,CAAC;YACf,CAAC;SACF;QAED,QAAQ,EAAE;YACR,KAAK,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS;gBACzC,IAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAClC,IAAI,KAAK,EAAE,CAAC;oBACV,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;SACF;QAED,cAAc,EAAE;YACd,KAAK,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS;gBACzC,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBACvC,qBAAqB,GAAG,IAAI,CAAC;oBAC7B,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,IAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;gBAClC,IAAI,KAAK,EAAE,CAAC;oBACV,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7C,CAAC;gBACD,wEAAwE;gBACxE,wEAAwE;gBACxE,uEAAuE;gBACvE,wEAAwE;gBACxE,oEAAoE;YACtE,CAAC;SACF;QAED,kBAAkB,EAAE;YAClB,KAAK,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;gBAC7B,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC;YAC7D,CAAC;YACD,KAAK,YAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI;gBAC7B,IAAM,YAAY,GAAG,0BAA0B,CAAC,GAAG,CACjD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CACrB,CAAC;gBACF,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;oBAC1B,sEAAsE;oBACtE,qEAAqE;oBACrE,yDAAyD;oBACzD,iEAAiE;oBACjE,sEAAsE;oBACtE,uDAAuD;oBACvD,OAAO,IAAI,CAAC;gBACd,CAAC;gBAED;gBACE,+DAA+D;gBAC/D,sEAAsE;gBACtE,6CAA6C;gBAC7C,cAAc,GAAG,CAAC;oBAClB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAChC,UAAC,SAAS;wBACR,OAAA,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK;4BAC7B,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY;oBADrC,CACqC,CACxC,EACD,CAAC;oBACD,sEAAsE;oBACtE,iEAAiE;oBACjE,mEAAmE;oBACnE,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC;oBACvD,qBAAqB,GAAG,IAAI,CAAC;oBAC7B,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;SACF;QAED,SAAS,EAAE;YACT,KAAK,YAAC,IAAI;gBACR,sEAAsE;gBACtE,qEAAqE;gBACrE,4CAA4C;gBAC5C,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3B,qBAAqB,GAAG,IAAI,CAAC;oBAC7B,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;SACF;KACF,CAAC,CAAC;IAEH,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC3B,2EAA2E;QAC3E,mEAAmE;QACnE,OAAO,GAAG,CAAC;IACb,CAAC;IAED,yEAAyE;IACzE,uEAAuE;IACvE,2DAA2D;IAC3D,wEAAwE;IACxE,oDAAoD;IACpD,IAAM,sBAAsB,GAAG,UAAC,KAAwB;QACtD,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC;YAC1B,KAAK,CAAC,cAAc,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACnB,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC,UAAC,iBAAiB;oBAC9C,sBAAsB,CACpB,sBAAsB,CAAC,iBAAiB,CAAC,CAC1C,CAAC,cAAe,CAAC,OAAO,CAAC,UAAC,OAAO;wBAChC,KAAK,CAAC,cAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;oBACrC,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAEF,wEAAwE;IACxE,6EAA6E;IAC7E,iEAAiE;IACjE,IAAM,oBAAoB,GAAG,IAAI,GAAG,EAAU,CAAC;IAC/C,2BAA2B,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,GAAG;QAClD,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC3C,sBAAsB,CACpB,uBAAuB,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CACpD,CAAC,eAAe,CAAC,OAAO,CAAC,UAAC,iBAAiB;gBAC1C,oBAAoB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;QACL,CAAC;aAAM,IACL,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,mBAAmB;YACrC,gEAAgE;YAChE,0EAA0E;YAC1E,mEAAmE;YACnE,0EAA0E;YAC1E,cAAc,KAAK,CAAC;YACpB,CAAC,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,EAC/C,CAAC;YACD,oBAAoB,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC,CAAC,CAAC;IACH,wEAAwE;IACxE,2EAA2E;IAC3E,2DAA2D;IAC3D,oBAAoB,CAAC,OAAO,CAAC,UAAC,YAAY;QACxC,4EAA4E;QAC5E,kEAAkE;QAClE,sBAAsB,CACpB,sBAAsB,CAAC,YAAY,CAAC,CACrC,CAAC,eAAe,CAAC,OAAO,CAAC,UAAC,iBAAiB;YAC1C,oBAAoB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAM,qBAAqB,GAAG,UAAC,YAAoB;QACjD,OAAA,CAAC,CAAC;QACA,2EAA2E;QAC3E,yEAAyE;QACzE,yBAAyB;QACzB,CACE,CAAC,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC;YACvC,sBAAsB,CAAC,YAAY,CAAC,CAAC,OAAO,CAC7C,CACF;IARD,CAQC,CAAC;IAEJ,IAAM,YAAY,GAEd;QACF,KAAK,YAAC,IAAI;YACR,IAAI,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3C,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;KACF,CAAC;IAEF,OAAO,gBAAgB,CACrB,KAAK,CAAC,2BAA2B,EAAE;QACjC,oEAAoE;QACpE,8DAA8D;QAC9D,cAAc,EAAE,YAAY;QAE5B,6DAA6D;QAC7D,kBAAkB,EAAE,YAAY;QAEhC,mBAAmB,EAAE;YACnB,KAAK,YAAC,IAAI;gBACR,sEAAsE;gBACtE,sEAAsE;gBACtE,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBAC7B,IAAM,mBAAiB,GAAG,sBAAsB;oBAC9C,oEAAoE;oBACpE,uBAAuB,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CACtD,CAAC,cAAe,CAAC;oBAElB,8DAA8D;oBAC9D,kEAAkE;oBAClE,sDAAsD;oBACtD,yDAAyD;oBACzD,EAAE;oBACF,gEAAgE;oBAChE,sEAAsE;oBACtE,kEAAkE;oBAClE,mEAAmE;oBACnE,oEAAoE;oBACpE,sEAAsE;oBACtE,IAAI,mBAAiB,CAAC,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;wBAC7D,6BACK,IAAI,KACP,mBAAmB,EAAE,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,UAAC,MAAM;gCAC1D,OAAA,mBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;4BAAjD,CAAiD,CAClD,IACD;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;SACF;KACF,CAAC,CACH,CAAC;AACJ,CAAC;AAED,MAAM,CAAC,IAAM,qBAAqB,GAAG,MAAM,CAAC,MAAM,CAChD,UAAiC,GAAU;IACzC,OAAO,KAAK,CAAC,GAAG,EAAE;QAChB,YAAY,EAAE;YACZ,KAAK,YAAC,IAAI,EAAE,IAAI,EAAE,MAAM;gBACtB,gDAAgD;gBAChD,IACE,MAAM;oBACL,MAAkC,CAAC,IAAI;wBACtC,IAAI,CAAC,oBAAoB,EAC3B,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,+BAA+B;gBACvB,IAAA,UAAU,GAAK,IAAI,WAAT,CAAU;gBAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,OAAO;gBACT,CAAC;gBAED,6DAA6D;gBAC7D,mCAAmC;gBACnC,IAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,UAAC,SAAS;oBACrC,OAAO,CACL,OAAO,CAAC,SAAS,CAAC;wBAClB,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY;4BACpC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CACnD,CAAC;gBACJ,CAAC,CAAC,CAAC;gBACH,IAAI,IAAI,EAAE,CAAC;oBACT,OAAO;gBACT,CAAC;gBAED,qEAAqE;gBACrE,iDAAiD;gBACjD,IAAM,KAAK,GAAG,MAAmB,CAAC;gBAClC,IACE,OAAO,CAAC,KAAK,CAAC;oBACd,KAAK,CAAC,UAAU;oBAChB,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAzB,CAAyB,CAAC,EACvD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,gEAAgE;gBAChE,6BACK,IAAI,KACP,UAAU,kCAAM,UAAU,UAAE,cAAc,aAC1C;YACJ,CAAC;SACF;KACF,CAAC,CAAC;AACL,CAAC,EACD;IACE,KAAK,YAAC,KAAgB;QACpB,OAAO,KAAK,KAAK,cAAc,CAAC;IAClC,CAAC;CACF,CACF,CAAC;AAEF,IAAM,sBAAsB,GAAG;IAC7B,IAAI,EAAE,UAAC,SAAwB;QAC7B,IAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,YAAY,CAAC;QACzD,IAAI,UAAU,EAAE,CAAC;YACf,IACE,CAAC,SAAS,CAAC,SAAS;gBACpB,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,EAAxB,CAAwB,CAAC,EAC5D,CAAC;gBACD,SAAS,CAAC,IAAI,CACZ,wEAAwE;oBACtE,+DAA+D,CAClE,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;CACF,CAAC;AAEF,MAAM,UAAU,qCAAqC,CAAC,GAAiB;IACrE,OAAO,4BAA4B,CACjC,CAAC,sBAAsB,CAAC,EACxB,aAAa,CAAC,GAAG,CAAC,CACnB,CAAC;AACJ,CAAC;AAED,SAAS,2BAA2B,CAClC,UAAgC,EAChC,YAA0C,EAC1C,WAAkB;IAAlB,4BAAA,EAAA,kBAAkB;IAElB,OAAO,CACL,CAAC,CAAC,YAAY;QACd,YAAY,CAAC,UAAU;QACvB,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,UAAC,SAAS;YACrC,OAAA,wBAAwB,CAAC,UAAU,EAAE,SAAS,EAAE,WAAW,CAAC;QAA5D,CAA4D,CAC7D,CACF,CAAC;AACJ,CAAC;AAED,SAAS,wBAAwB,CAC/B,UAAgC,EAChC,SAAwB,EACxB,WAAkB;IAAlB,4BAAA,EAAA,kBAAkB;IAElB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CACL,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC,WAAW;YACV,2BAA2B,CACzB,UAAU,EACV,SAAS,CAAC,YAAY,EACtB,WAAW,CACZ,CAAC,CACL,CAAC;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,MAA+B;IACzD,OAAO,SAAS,eAAe,CAAC,QAAsB;QACpD,OAAO,MAAM,CAAC,IAAI,CAChB,UAAC,OAA8B;YAC7B,OAAA,QAAQ,CAAC,KAAK;gBACd,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,QAAQ;gBACrC,QAAQ,CAAC,KAAK,CAAC,IAAI;gBACnB,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK;oBACzC,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAJ3C,CAI2C,CAC9C,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,2BAA2B,CACzC,MAA+B,EAC/B,GAAiB;IAEjB,IAAM,UAAU,GAAG,kBAAkB,CAAC,MAAM,CAAC,CAAC;IAE9C,OAAO,gBAAgB,CACrB,KAAK,CAAC,GAAG,EAAE;QACT,mBAAmB,EAAE;YACnB,KAAK,YAAC,IAAI;gBACR,6BACK,IAAI;oBACP,mDAAmD;oBACnD,mBAAmB,EACjB,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBACxB,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAC7B,UAAC,MAAM;4BACL,OAAA,CAAC,MAAM,CAAC,IAAI,CACV,UAAC,GAAG,IAAK,OAAA,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAvC,CAAuC,CACjD;wBAFD,CAEC,CACJ;wBACH,CAAC,CAAC,EAAE,IACN;YACJ,CAAC;SACF;QAED,KAAK,EAAE;YACL,KAAK,YAAC,IAAI;gBACR,oEAAoE;gBACpE,kDAAkD;gBAClD,IAAM,iBAAiB,GAAG,MAAM,CAAC,IAAI,CACnC,UAAC,SAAS,IAAK,OAAA,SAAS,CAAC,MAAM,EAAhB,CAAgB,CAChC,CAAC;gBAEF,IAAI,iBAAiB,EAAE,CAAC;oBACtB,IAAI,eAAa,GAAG,CAAC,CAAC;oBACtB,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;wBACnB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAC,GAAG;4BACzB,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;gCACpB,eAAa,IAAI,CAAC,CAAC;4BACrB,CAAC;wBACH,CAAC,CAAC,CAAC;oBACL,CAAC;oBAED,IAAI,eAAa,KAAK,CAAC,EAAE,CAAC;wBACxB,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;YACH,CAAC;SACF;QAED,QAAQ,EAAE;YACR,KAAK,YAAC,IAAI;gBACR,iCAAiC;gBACjC,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;oBACrB,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;SACF;KACF,CAAC,CACH,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,gCAAgC,CAC9C,MAAoC,EACpC,GAAiB;IAEjB,SAAS,KAAK,CACZ,IAAiD;QAEjD,IAAI,MAAM,CAAC,IAAI,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,EAA5B,CAA4B,CAAC,EAAE,CAAC;YACvD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,OAAO,gBAAgB,CACrB,KAAK,CAAC,GAAG,EAAE;QACT,cAAc,EAAE,EAAE,KAAK,OAAA,EAAE;QACzB,kBAAkB,EAAE,EAAE,KAAK,OAAA,EAAE;KAC9B,CAAC,CACH,CAAC;AACJ,CAAC;AAED,2EAA2E;AAC3E,uEAAuE;AACvE,kCAAkC;AAClC,MAAM,UAAU,0BAA0B,CACxC,QAAsB;IAEtB,IAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC/C,IAAM,mBAAmB,GAA6B,UAAW,CAAC,SAAS,CAAC;IAE5E,IAAI,mBAAmB,KAAK,OAAO,EAAE,CAAC;QACpC,oDAAoD;QACpD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,mEAAmE;IACnE,IAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,EAAE;QAClC,mBAAmB,EAAE;YACnB,KAAK,YAAC,IAAI;gBACR,6BACK,IAAI,KACP,SAAS,EAAE,OAAO,IAClB;YACJ,CAAC;SACF;KACF,CAAC,CAAC;IACH,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,oEAAoE;AACpE,MAAM,UAAU,4BAA4B,CAC1C,QAAsB;IAEtB,aAAa,CAAC,QAAQ,CAAC,CAAC;IAExB,IAAI,WAAW,GAAG,4BAA4B,CAC5C;QACE;YACE,IAAI,EAAE,UAAC,SAAwB,IAAK,OAAA,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAjC,CAAiC;YACrE,MAAM,EAAE,IAAI;SACb;KACF,EACD,QAAQ,CACT,CAAC;IAEF,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,MAAM,UAAU,8BAA8B,CAAC,QAAsB;IACnE,aAAa,CAAC,QAAQ,CAAC,CAAC;IAExB,OAAO,KAAK,CAAC,QAAQ,EAAE;QACrB,cAAc,EAAE,UAAC,IAAI;;YACnB,qEAAqE;YACrE,sDAAsD;YACtD,IACE,MAAA,IAAI,CAAC,UAAU,0CAAE,IAAI,CAAC,UAAC,SAAS,IAAK,OAAA,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAjC,CAAiC,CAAC,EACvE,CAAC;gBACD,OAAO;YACT,CAAC;YAED,6BACK,IAAI,KACP,UAAU,kCACL,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC;oBAC1B;wBACE,IAAI,EAAE,IAAI,CAAC,SAAS;wBACpB,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE;qBACxB;6BAE3B;QACJ,CAAC;KACF,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { invariant } from \"../globals/index.js\";\n\nimport type {\n  DocumentNode,\n  SelectionNode,\n  SelectionSetNode,\n  OperationDefinitionNode,\n  FieldNode,\n  DirectiveNode,\n  FragmentDefinitionNode,\n  ArgumentNode,\n  FragmentSpreadNode,\n  VariableDefinitionNode,\n  ASTNode,\n  ASTVisitFn,\n  InlineFragmentNode,\n} from \"graphql\";\nimport { visit, Kind } from \"graphql\";\n\nimport {\n  checkDocument,\n  getOperationDefinition,\n  getFragmentDefinition,\n  getFragmentDefinitions,\n  getMainDefinition,\n} from \"./getFromAST.js\";\nimport { isField } from \"./storeUtils.js\";\nimport type { FragmentMap } from \"./fragments.js\";\nimport { createFragmentMap } from \"./fragments.js\";\nimport { isArray, isNonEmptyArray } from \"../common/arrays.js\";\n\n// https://github.com/graphql/graphql-js/blob/8d7c8fccf5a9846a50785de04abda58a7eb13fc0/src/language/visitor.ts#L20-L23\ninterface EnterLeaveVisitor<TVisitedNode extends ASTNode> {\n  readonly enter?: ASTVisitFn<TVisitedNode>;\n  readonly leave?: ASTVisitFn<TVisitedNode>;\n}\n\nexport type RemoveNodeConfig<N> = {\n  name?: string;\n  test?: (node: N) => boolean;\n  remove?: boolean;\n};\n\nexport type GetNodeConfig<N> = {\n  name?: string;\n  test?: (node: N) => boolean;\n};\n\nexport type RemoveDirectiveConfig = RemoveNodeConfig<DirectiveNode>;\nexport type GetDirectiveConfig = GetNodeConfig<DirectiveNode>;\nexport type RemoveArgumentsConfig = RemoveNodeConfig<ArgumentNode>;\nexport type GetFragmentSpreadConfig = GetNodeConfig<FragmentSpreadNode>;\nexport type RemoveFragmentSpreadConfig = RemoveNodeConfig<FragmentSpreadNode>;\nexport type RemoveFragmentDefinitionConfig =\n  RemoveNodeConfig<FragmentDefinitionNode>;\nexport type RemoveVariableDefinitionConfig =\n  RemoveNodeConfig<VariableDefinitionNode>;\n\nconst TYPENAME_FIELD: FieldNode = {\n  kind: Kind.FIELD,\n  name: {\n    kind: Kind.NAME,\n    value: \"__typename\",\n  },\n};\n\nfunction isEmpty(\n  op: OperationDefinitionNode | FragmentDefinitionNode,\n  fragmentMap: FragmentMap\n): boolean {\n  return (\n    !op ||\n    op.selectionSet.selections.every(\n      (selection) =>\n        selection.kind === Kind.FRAGMENT_SPREAD &&\n        isEmpty(fragmentMap[selection.name.value], fragmentMap)\n    )\n  );\n}\n\nfunction nullIfDocIsEmpty(doc: DocumentNode) {\n  return (\n      isEmpty(\n        getOperationDefinition(doc) || getFragmentDefinition(doc),\n        createFragmentMap(getFragmentDefinitions(doc))\n      )\n    ) ?\n      null\n    : doc;\n}\n\nfunction getDirectiveMatcher(\n  configs: (RemoveDirectiveConfig | GetDirectiveConfig)[]\n) {\n  const names = new Map<string, RemoveDirectiveConfig | GetDirectiveConfig>();\n\n  const tests = new Map<\n    (directive: DirectiveNode) => boolean,\n    RemoveDirectiveConfig | GetDirectiveConfig\n  >();\n\n  configs.forEach((directive) => {\n    if (directive) {\n      if (directive.name) {\n        names.set(directive.name, directive);\n      } else if (directive.test) {\n        tests.set(directive.test, directive);\n      }\n    }\n  });\n\n  return (directive: DirectiveNode) => {\n    let config = names.get(directive.name.value);\n    if (!config && tests.size) {\n      tests.forEach((testConfig, test) => {\n        if (test(directive)) {\n          config = testConfig;\n        }\n      });\n    }\n    return config;\n  };\n}\n\n// Helper interface and function used by removeDirectivesFromDocument to keep\n// track of variable references and fragments spreads found within a given\n// operation or fragment definition.\ninterface InternalInUseInfo {\n  variables: Set<string>;\n  fragmentSpreads: Set<string>;\n  // Set to true when we deliberately remove a fragment definition, so we can\n  // make sure also to remove dangling ...spreads that refer to it.\n  removed?: boolean;\n  // Populated by the populateTransitiveVars helper function below.\n  transitiveVars?: Set<string>;\n}\nfunction makeInUseGetterFunction<TKey>(defaultKey: TKey) {\n  const map = new Map<TKey, InternalInUseInfo>();\n\n  return function inUseGetterFunction(\n    key: TKey = defaultKey\n  ): InternalInUseInfo {\n    let inUse = map.get(key);\n    if (!inUse) {\n      map.set(\n        key,\n        (inUse = {\n          // Variable and fragment spread names used directly within this\n          // operation or fragment definition, as identified by key. These sets\n          // will be populated during the first traversal of the document in\n          // removeDirectivesFromDocument below.\n          variables: new Set(),\n          fragmentSpreads: new Set(),\n        })\n      );\n    }\n    return inUse;\n  };\n}\n\nexport function removeDirectivesFromDocument(\n  directives: RemoveDirectiveConfig[],\n  doc: DocumentNode\n): DocumentNode | null {\n  checkDocument(doc);\n\n  // Passing empty strings to makeInUseGetterFunction means we handle anonymous\n  // operations as if their names were \"\". Anonymous fragment definitions are\n  // not supposed to be possible, but the same default naming strategy seems\n  // appropriate for that case as well.\n  const getInUseByOperationName = makeInUseGetterFunction<string>(\"\");\n  const getInUseByFragmentName = makeInUseGetterFunction<string>(\"\");\n  const getInUse = (\n    ancestors: readonly (ASTNode | readonly ASTNode[])[]\n  ): InternalInUseInfo | null => {\n    for (\n      let p = 0, ancestor: ASTNode | readonly ASTNode[];\n      p < ancestors.length && (ancestor = ancestors[p]);\n      ++p\n    ) {\n      if (isArray(ancestor)) continue;\n      if (ancestor.kind === Kind.OPERATION_DEFINITION) {\n        // If an operation is anonymous, we use the empty string as its key.\n        return getInUseByOperationName(ancestor.name && ancestor.name.value);\n      }\n      if (ancestor.kind === Kind.FRAGMENT_DEFINITION) {\n        return getInUseByFragmentName(ancestor.name.value);\n      }\n    }\n    invariant.error(`Could not find operation or fragment`);\n    return null;\n  };\n\n  let operationCount = 0;\n  for (let i = doc.definitions.length - 1; i >= 0; --i) {\n    if (doc.definitions[i].kind === Kind.OPERATION_DEFINITION) {\n      ++operationCount;\n    }\n  }\n\n  const directiveMatcher = getDirectiveMatcher(directives);\n  const shouldRemoveField = (nodeDirectives: FieldNode[\"directives\"]) =>\n    isNonEmptyArray(nodeDirectives) &&\n    nodeDirectives\n      .map(directiveMatcher)\n      .some(\n        (config: RemoveDirectiveConfig | undefined) => config && config.remove\n      );\n\n  const originalFragmentDefsByPath = new Map<string, FragmentDefinitionNode>();\n\n  // Any time the first traversal of the document below makes a change like\n  // removing a fragment (by returning null), this variable should be set to\n  // true. Once it becomes true, it should never be set to false again. If this\n  // variable remains false throughout the traversal, then we can return the\n  // original doc immediately without any modifications.\n  let firstVisitMadeChanges = false;\n\n  const fieldOrInlineFragmentVisitor: EnterLeaveVisitor<\n    FieldNode | InlineFragmentNode\n  > = {\n    enter(node) {\n      if (shouldRemoveField(node.directives)) {\n        firstVisitMadeChanges = true;\n        return null;\n      }\n    },\n  };\n\n  const docWithoutDirectiveSubtrees = visit(doc, {\n    // These two AST node types share the same implementation, defined above.\n    Field: fieldOrInlineFragmentVisitor,\n    InlineFragment: fieldOrInlineFragmentVisitor,\n\n    VariableDefinition: {\n      enter() {\n        // VariableDefinition nodes do not count as variables in use, though\n        // they do contain Variable nodes that might be visited below. To avoid\n        // counting variable declarations as usages, we skip visiting the\n        // contents of this VariableDefinition node by returning false.\n        return false;\n      },\n    },\n\n    Variable: {\n      enter(node, _key, _parent, _path, ancestors) {\n        const inUse = getInUse(ancestors);\n        if (inUse) {\n          inUse.variables.add(node.name.value);\n        }\n      },\n    },\n\n    FragmentSpread: {\n      enter(node, _key, _parent, _path, ancestors) {\n        if (shouldRemoveField(node.directives)) {\n          firstVisitMadeChanges = true;\n          return null;\n        }\n        const inUse = getInUse(ancestors);\n        if (inUse) {\n          inUse.fragmentSpreads.add(node.name.value);\n        }\n        // We might like to remove this FragmentSpread by returning null here if\n        // the corresponding FragmentDefinition node is also going to be removed\n        // by the logic below, but we can't control the relative order of those\n        // events, so we have to postpone the removal of dangling FragmentSpread\n        // nodes until after the current visit of the document has finished.\n      },\n    },\n\n    FragmentDefinition: {\n      enter(node, _key, _parent, path) {\n        originalFragmentDefsByPath.set(JSON.stringify(path), node);\n      },\n      leave(node, _key, _parent, path) {\n        const originalNode = originalFragmentDefsByPath.get(\n          JSON.stringify(path)\n        );\n        if (node === originalNode) {\n          // If the FragmentNode received by this leave function is identical to\n          // the one received by the corresponding enter function (above), then\n          // the visitor must not have made any changes within this\n          // FragmentDefinition node. This fragment definition may still be\n          // removed if there are no ...spread references to it, but it won't be\n          // removed just because it has only a __typename field.\n          return node;\n        }\n\n        if (\n          // This logic applies only if the document contains one or more\n          // operations, since removing all fragments from a document containing\n          // only fragments makes the document useless.\n          operationCount > 0 &&\n          node.selectionSet.selections.every(\n            (selection) =>\n              selection.kind === Kind.FIELD &&\n              selection.name.value === \"__typename\"\n          )\n        ) {\n          // This is a somewhat opinionated choice: if a FragmentDefinition ends\n          // up having no fields other than __typename, we remove the whole\n          // fragment definition, and later prune ...spread references to it.\n          getInUseByFragmentName(node.name.value).removed = true;\n          firstVisitMadeChanges = true;\n          return null;\n        }\n      },\n    },\n\n    Directive: {\n      leave(node) {\n        // If a matching directive is found, remove the directive itself. Note\n        // that this does not remove the target (field, argument, etc) of the\n        // directive, but only the directive itself.\n        if (directiveMatcher(node)) {\n          firstVisitMadeChanges = true;\n          return null;\n        }\n      },\n    },\n  });\n\n  if (!firstVisitMadeChanges) {\n    // If our first pass did not change anything about the document, then there\n    // is no cleanup we need to do, and we can return the original doc.\n    return doc;\n  }\n\n  // Utility for making sure inUse.transitiveVars is recursively populated.\n  // Because this logic assumes inUse.fragmentSpreads has been completely\n  // populated and inUse.removed has been set if appropriate,\n  // populateTransitiveVars must be called after that information has been\n  // collected by the first traversal of the document.\n  const populateTransitiveVars = (inUse: InternalInUseInfo) => {\n    if (!inUse.transitiveVars) {\n      inUse.transitiveVars = new Set(inUse.variables);\n      if (!inUse.removed) {\n        inUse.fragmentSpreads.forEach((childFragmentName) => {\n          populateTransitiveVars(\n            getInUseByFragmentName(childFragmentName)\n          ).transitiveVars!.forEach((varName) => {\n            inUse.transitiveVars!.add(varName);\n          });\n        });\n      }\n    }\n    return inUse;\n  };\n\n  // Since we've been keeping track of fragment spreads used by particular\n  // operations and fragment definitions, we now need to compute the set of all\n  // spreads used (transitively) by any operations in the document.\n  const allFragmentNamesUsed = new Set<string>();\n  docWithoutDirectiveSubtrees.definitions.forEach((def) => {\n    if (def.kind === Kind.OPERATION_DEFINITION) {\n      populateTransitiveVars(\n        getInUseByOperationName(def.name && def.name.value)\n      ).fragmentSpreads.forEach((childFragmentName) => {\n        allFragmentNamesUsed.add(childFragmentName);\n      });\n    } else if (\n      def.kind === Kind.FRAGMENT_DEFINITION &&\n      // If there are no operations in the document, then all fragment\n      // definitions count as usages of their own fragment names. This heuristic\n      // prevents accidentally removing all fragment definitions from the\n      // document just because it contains no operations that use the fragments.\n      operationCount === 0 &&\n      !getInUseByFragmentName(def.name.value).removed\n    ) {\n      allFragmentNamesUsed.add(def.name.value);\n    }\n  });\n  // Now that we have added all fragment spreads used by operations to the\n  // allFragmentNamesUsed set, we can complete the set by transitively adding\n  // all fragment spreads used by those fragments, and so on.\n  allFragmentNamesUsed.forEach((fragmentName) => {\n    // Once all the childFragmentName strings added here have been seen already,\n    // the top-level allFragmentNamesUsed.forEach loop will terminate.\n    populateTransitiveVars(\n      getInUseByFragmentName(fragmentName)\n    ).fragmentSpreads.forEach((childFragmentName) => {\n      allFragmentNamesUsed.add(childFragmentName);\n    });\n  });\n\n  const fragmentWillBeRemoved = (fragmentName: string) =>\n    !!(\n      // A fragment definition will be removed if there are no spreads that refer\n      // to it, or the fragment was explicitly removed because it had no fields\n      // other than __typename.\n      (\n        !allFragmentNamesUsed.has(fragmentName) ||\n        getInUseByFragmentName(fragmentName).removed\n      )\n    );\n\n  const enterVisitor: EnterLeaveVisitor<\n    FragmentSpreadNode | FragmentDefinitionNode\n  > = {\n    enter(node) {\n      if (fragmentWillBeRemoved(node.name.value)) {\n        return null;\n      }\n    },\n  };\n\n  return nullIfDocIsEmpty(\n    visit(docWithoutDirectiveSubtrees, {\n      // If the fragment is going to be removed, then leaving any dangling\n      // FragmentSpread nodes with the same name would be a mistake.\n      FragmentSpread: enterVisitor,\n\n      // This is where the fragment definition is actually removed.\n      FragmentDefinition: enterVisitor,\n\n      OperationDefinition: {\n        leave(node) {\n          // Upon leaving each operation in the depth-first AST traversal, prune\n          // any variables that are declared by the operation but unused within.\n          if (node.variableDefinitions) {\n            const usedVariableNames = populateTransitiveVars(\n              // If an operation is anonymous, we use the empty string as its key.\n              getInUseByOperationName(node.name && node.name.value)\n            ).transitiveVars!;\n\n            // According to the GraphQL spec, all variables declared by an\n            // operation must either be used by that operation or used by some\n            // fragment included transitively into that operation:\n            // https://spec.graphql.org/draft/#sec-All-Variables-Used\n            //\n            // To stay on the right side of this validation rule, if/when we\n            // remove the last $var references from an operation or its fragments,\n            // we must also remove the corresponding $var declaration from the\n            // enclosing operation. This pruning applies only to operations and\n            // not fragment definitions, at the moment. Fragments may be able to\n            // declare variables eventually, but today they can only consume them.\n            if (usedVariableNames.size < node.variableDefinitions.length) {\n              return {\n                ...node,\n                variableDefinitions: node.variableDefinitions.filter((varDef) =>\n                  usedVariableNames.has(varDef.variable.name.value)\n                ),\n              };\n            }\n          }\n        },\n      },\n    })\n  );\n}\n\nexport const addTypenameToDocument = Object.assign(\n  function <TNode extends ASTNode>(doc: TNode): TNode {\n    return visit(doc, {\n      SelectionSet: {\n        enter(node, _key, parent) {\n          // Don't add __typename to OperationDefinitions.\n          if (\n            parent &&\n            (parent as OperationDefinitionNode).kind ===\n              Kind.OPERATION_DEFINITION\n          ) {\n            return;\n          }\n\n          // No changes if no selections.\n          const { selections } = node;\n          if (!selections) {\n            return;\n          }\n\n          // If selections already have a __typename, or are part of an\n          // introspection query, do nothing.\n          const skip = selections.some((selection) => {\n            return (\n              isField(selection) &&\n              (selection.name.value === \"__typename\" ||\n                selection.name.value.lastIndexOf(\"__\", 0) === 0)\n            );\n          });\n          if (skip) {\n            return;\n          }\n\n          // If this SelectionSet is @export-ed as an input variable, it should\n          // not have a __typename field (see issue #4691).\n          const field = parent as FieldNode;\n          if (\n            isField(field) &&\n            field.directives &&\n            field.directives.some((d) => d.name.value === \"export\")\n          ) {\n            return;\n          }\n\n          // Create and return a new SelectionSet with a __typename Field.\n          return {\n            ...node,\n            selections: [...selections, TYPENAME_FIELD],\n          };\n        },\n      },\n    });\n  },\n  {\n    added(field: FieldNode): boolean {\n      return field === TYPENAME_FIELD;\n    },\n  }\n);\n\nconst connectionRemoveConfig = {\n  test: (directive: DirectiveNode) => {\n    const willRemove = directive.name.value === \"connection\";\n    if (willRemove) {\n      if (\n        !directive.arguments ||\n        !directive.arguments.some((arg) => arg.name.value === \"key\")\n      ) {\n        invariant.warn(\n          \"Removing an @connection directive even though it does not have a key. \" +\n            \"You may want to use the key parameter to specify a store key.\"\n        );\n      }\n    }\n\n    return willRemove;\n  },\n};\n\nexport function removeConnectionDirectiveFromDocument(doc: DocumentNode) {\n  return removeDirectivesFromDocument(\n    [connectionRemoveConfig],\n    checkDocument(doc)\n  );\n}\n\nfunction hasDirectivesInSelectionSet(\n  directives: GetDirectiveConfig[],\n  selectionSet: SelectionSetNode | undefined,\n  nestedCheck = true\n): boolean {\n  return (\n    !!selectionSet &&\n    selectionSet.selections &&\n    selectionSet.selections.some((selection) =>\n      hasDirectivesInSelection(directives, selection, nestedCheck)\n    )\n  );\n}\n\nfunction hasDirectivesInSelection(\n  directives: GetDirectiveConfig[],\n  selection: SelectionNode,\n  nestedCheck = true\n): boolean {\n  if (!isField(selection)) {\n    return true;\n  }\n\n  if (!selection.directives) {\n    return false;\n  }\n\n  return (\n    selection.directives.some(getDirectiveMatcher(directives)) ||\n    (nestedCheck &&\n      hasDirectivesInSelectionSet(\n        directives,\n        selection.selectionSet,\n        nestedCheck\n      ))\n  );\n}\n\nfunction getArgumentMatcher(config: RemoveArgumentsConfig[]) {\n  return function argumentMatcher(argument: ArgumentNode) {\n    return config.some(\n      (aConfig: RemoveArgumentsConfig) =>\n        argument.value &&\n        argument.value.kind === Kind.VARIABLE &&\n        argument.value.name &&\n        (aConfig.name === argument.value.name.value ||\n          (aConfig.test && aConfig.test(argument)))\n    );\n  };\n}\n\nexport function removeArgumentsFromDocument(\n  config: RemoveArgumentsConfig[],\n  doc: DocumentNode\n): DocumentNode | null {\n  const argMatcher = getArgumentMatcher(config);\n\n  return nullIfDocIsEmpty(\n    visit(doc, {\n      OperationDefinition: {\n        enter(node) {\n          return {\n            ...node,\n            // Remove matching top level variables definitions.\n            variableDefinitions:\n              node.variableDefinitions ?\n                node.variableDefinitions.filter(\n                  (varDef) =>\n                    !config.some(\n                      (arg) => arg.name === varDef.variable.name.value\n                    )\n                )\n              : [],\n          };\n        },\n      },\n\n      Field: {\n        enter(node) {\n          // If `remove` is set to true for an argument, and an argument match\n          // is found for a field, remove the field as well.\n          const shouldRemoveField = config.some(\n            (argConfig) => argConfig.remove\n          );\n\n          if (shouldRemoveField) {\n            let argMatchCount = 0;\n            if (node.arguments) {\n              node.arguments.forEach((arg) => {\n                if (argMatcher(arg)) {\n                  argMatchCount += 1;\n                }\n              });\n            }\n\n            if (argMatchCount === 1) {\n              return null;\n            }\n          }\n        },\n      },\n\n      Argument: {\n        enter(node) {\n          // Remove all matching arguments.\n          if (argMatcher(node)) {\n            return null;\n          }\n        },\n      },\n    })\n  );\n}\n\nexport function removeFragmentSpreadFromDocument(\n  config: RemoveFragmentSpreadConfig[],\n  doc: DocumentNode\n): DocumentNode | null {\n  function enter(\n    node: FragmentSpreadNode | FragmentDefinitionNode\n  ): null | void {\n    if (config.some((def) => def.name === node.name.value)) {\n      return null;\n    }\n  }\n\n  return nullIfDocIsEmpty(\n    visit(doc, {\n      FragmentSpread: { enter },\n      FragmentDefinition: { enter },\n    })\n  );\n}\n\n// If the incoming document is a query, return it as is. Otherwise, build a\n// new document containing a query operation based on the selection set\n// of the previous main operation.\nexport function buildQueryFromSelectionSet(\n  document: DocumentNode\n): DocumentNode {\n  const definition = getMainDefinition(document);\n  const definitionOperation = (<OperationDefinitionNode>definition).operation;\n\n  if (definitionOperation === \"query\") {\n    // Already a query, so return the existing document.\n    return document;\n  }\n\n  // Build a new query using the selection set of the main operation.\n  const modifiedDoc = visit(document, {\n    OperationDefinition: {\n      enter(node) {\n        return {\n          ...node,\n          operation: \"query\",\n        };\n      },\n    },\n  });\n  return modifiedDoc;\n}\n\n// Remove fields / selection sets that include an @client directive.\nexport function removeClientSetsFromDocument(\n  document: DocumentNode\n): DocumentNode | null {\n  checkDocument(document);\n\n  let modifiedDoc = removeDirectivesFromDocument(\n    [\n      {\n        test: (directive: DirectiveNode) => directive.name.value === \"client\",\n        remove: true,\n      },\n    ],\n    document\n  );\n\n  return modifiedDoc;\n}\n\nexport function addNonReactiveToNamedFragments(document: DocumentNode) {\n  checkDocument(document);\n\n  return visit(document, {\n    FragmentSpread: (node) => {\n      // Do not add `@nonreactive` if the fragment is marked with `@unmask`\n      // since we want to react to changes in this fragment.\n      if (\n        node.directives?.some((directive) => directive.name.value === \"unmask\")\n      ) {\n        return;\n      }\n\n      return {\n        ...node,\n        directives: [\n          ...(node.directives || []),\n          {\n            kind: Kind.DIRECTIVE,\n            name: { kind: Kind.NAME, value: \"nonreactive\" },\n          } satisfies DirectiveNode,\n        ],\n      };\n    },\n  });\n}\n"]}