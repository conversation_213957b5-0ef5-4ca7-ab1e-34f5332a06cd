{"version": 3, "file": "fragments.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/fragments.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AAEnE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAWvC;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,UAAU,wBAAwB,CACtC,QAAsB,EACtB,YAAqB;IAErB,IAAI,kBAAkB,GAAG,YAAY,CAAC;IAEtC,uEAAuE;IACvE,2EAA2E;IAC3E,qCAAqC;IACrC,IAAM,SAAS,GAAkC,EAAE,CAAC;IACpD,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,UAAU;QACtC,yEAAyE;QACzE,gDAAgD;QAChD,IAAI,UAAU,CAAC,IAAI,KAAK,qBAAqB,EAAE,CAAC;YAC9C,MAAM,iBAAiB,CACrB,0BAA0B;gBACxB,yFAAyF,EAC3F,UAAU,CAAC,SAAS,EACpB,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,kBAAW,UAAU,CAAC,IAAI,CAAC,KAAK,MAAG,CAAC,CAAC,CAAC,EAAE,CAC3D,CAAC;QACJ,CAAC;QACD,gEAAgE;QAChE,cAAc;QACd,IAAI,UAAU,CAAC,IAAI,KAAK,oBAAoB,EAAE,CAAC;YAC7C,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,uEAAuE;IACvE,iDAAiD;IACjD,IAAI,OAAO,kBAAkB,KAAK,WAAW,EAAE,CAAC;QAC9C,SAAS,CACP,SAAS,CAAC,MAAM,KAAK,CAAC,EACtB,2FAA6F,EAC7F,SAAS,CAAC,MAAM,CACjB,CAAC;QACF,kBAAkB,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;IAC/C,CAAC;IAED,sEAAsE;IACtE,yBAAyB;IACzB,IAAM,KAAK,yBACN,QAAQ,KACX,WAAW;YACT;gBACE,IAAI,EAAE,qBAA6B;gBACnC,+BAA+B;gBAC/B,SAAS,EAAE,OAA4B;gBACvC,YAAY,EAAE;oBACZ,IAAI,EAAE,cAAsB;oBAC5B,UAAU,EAAE;wBACV;4BACE,IAAI,EAAE,gBAAwB;4BAC9B,IAAI,EAAE;gCACJ,IAAI,EAAE,MAAc;gCACpB,KAAK,EAAE,kBAAkB;6BAC1B;yBACF;qBACF;iBACF;aACF;WACE,QAAQ,CAAC,WAAW,UAE1B,CAAC;IAEF,OAAO,KAAK,CAAC;AACf,CAAC;AAaD,0FAA0F;AAC1F,iEAAiE;AACjE,MAAM,UAAU,iBAAiB,CAC/B,SAAwC;IAAxC,0BAAA,EAAA,cAAwC;IAExC,IAAM,QAAQ,GAAgB,EAAE,CAAC;IACjC,SAAS,CAAC,OAAO,CAAC,UAAC,QAAQ;QACzB,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAC;IAC3C,CAAC,CAAC,CAAC;IACH,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,wBAAwB,CACtC,SAAwB,EACxB,WAA+C;IAE/C,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;QACvB,KAAK,gBAAgB;YACnB,OAAO,SAAS,CAAC;QACnB,KAAK,gBAAgB,CAAC,CAAC,CAAC;YACtB,IAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;YAC1C,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE,CAAC;gBACtC,OAAO,WAAW,CAAC,YAAY,CAAC,CAAC;YACnC,CAAC;YACD,IAAM,QAAQ,GAAG,WAAW,IAAI,WAAW,CAAC,YAAY,CAAC,CAAC;YAC1D,SAAS,CAAC,QAAQ,EAAE,sBAAsB,EAAE,YAAY,CAAC,CAAC;YAC1D,OAAO,QAAQ,IAAI,IAAI,CAAC;QAC1B,CAAC;QACD;YACE,OAAO,IAAI,CAAC;IAChB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,wBAAwB,CAAC,QAAsB;IAC7D,IAAI,UAAU,GAAG,IAAI,CAAC;IAEtB,KAAK,CAAC,QAAQ,EAAE;QACd,cAAc,EAAE,UAAC,IAAI;YACnB,UAAU;gBACR,CAAC,CAAC,IAAI,CAAC,UAAU;oBACjB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAC,SAAS,IAAK,OAAA,SAAS,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAjC,CAAiC,CAAC,CAAC;YAEzE,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC;AACpB,CAAC", "sourcesContent": ["import { invariant, newInvariantError } from \"../globals/index.js\";\n\nimport { BREA<PERSON>, visit } from \"graphql\";\nimport type {\n  DocumentNode,\n  FragmentDefinitionNode,\n  InlineFragmentNode,\n  SelectionNode,\n} from \"graphql\";\n\n// TODO(brian): A hack until this issue is resolved (https://github.com/graphql/graphql-js/issues/3356)\ntype Kind = any;\ntype OperationTypeNode = any;\n/**\n * Returns a query document which adds a single query operation that only\n * spreads the target fragment inside of it.\n *\n * So for example a document of:\n *\n * ```graphql\n * fragment foo on Foo { a b c }\n * ```\n *\n * Turns into:\n *\n * ```graphql\n * { ...foo }\n *\n * fragment foo on Foo { a b c }\n * ```\n *\n * The target fragment will either be the only fragment in the document, or a\n * fragment specified by the provided `fragmentName`. If there is more than one\n * fragment, but a `fragmentName` was not defined then an error will be thrown.\n */\nexport function getFragmentQueryDocument(\n  document: DocumentNode,\n  fragmentName?: string\n): DocumentNode {\n  let actualFragmentName = fragmentName;\n\n  // Build an array of all our fragment definitions that will be used for\n  // validations. We also do some validations on the other definitions in the\n  // document while building this list.\n  const fragments: Array<FragmentDefinitionNode> = [];\n  document.definitions.forEach((definition) => {\n    // Throw an error if we encounter an operation definition because we will\n    // define our own operation definition later on.\n    if (definition.kind === \"OperationDefinition\") {\n      throw newInvariantError(\n        `Found a %s operation%s. ` +\n          \"No operations are allowed when using a fragment as a query. Only fragments are allowed.\",\n        definition.operation,\n        definition.name ? ` named '${definition.name.value}'` : \"\"\n      );\n    }\n    // Add our definition to the fragments array if it is a fragment\n    // definition.\n    if (definition.kind === \"FragmentDefinition\") {\n      fragments.push(definition);\n    }\n  });\n\n  // If the user did not give us a fragment name then let us try to get a\n  // name from a single fragment in the definition.\n  if (typeof actualFragmentName === \"undefined\") {\n    invariant(\n      fragments.length === 1,\n      `Found %s fragments. \\`fragmentName\\` must be provided when there is not exactly 1 fragment.`,\n      fragments.length\n    );\n    actualFragmentName = fragments[0].name.value;\n  }\n\n  // Generate a query document with an operation that simply spreads the\n  // fragment inside of it.\n  const query: DocumentNode = {\n    ...document,\n    definitions: [\n      {\n        kind: \"OperationDefinition\" as Kind,\n        // OperationTypeNode is an enum\n        operation: \"query\" as OperationTypeNode,\n        selectionSet: {\n          kind: \"SelectionSet\" as Kind,\n          selections: [\n            {\n              kind: \"FragmentSpread\" as Kind,\n              name: {\n                kind: \"Name\" as Kind,\n                value: actualFragmentName,\n              },\n            },\n          ],\n        },\n      },\n      ...document.definitions,\n    ],\n  };\n\n  return query;\n}\n\n/**\n * This is an interface that describes a map from fragment names to fragment definitions.\n */\nexport interface FragmentMap {\n  [fragmentName: string]: FragmentDefinitionNode;\n}\n\nexport type FragmentMapFunction = (\n  fragmentName: string\n) => FragmentDefinitionNode | null;\n\n// Utility function that takes a list of fragment definitions and makes a hash out of them\n// that maps the name of the fragment to the fragment definition.\nexport function createFragmentMap(\n  fragments: FragmentDefinitionNode[] = []\n): FragmentMap {\n  const symTable: FragmentMap = {};\n  fragments.forEach((fragment) => {\n    symTable[fragment.name.value] = fragment;\n  });\n  return symTable;\n}\n\nexport function getFragmentFromSelection(\n  selection: SelectionNode,\n  fragmentMap?: FragmentMap | FragmentMapFunction\n): InlineFragmentNode | FragmentDefinitionNode | null {\n  switch (selection.kind) {\n    case \"InlineFragment\":\n      return selection;\n    case \"FragmentSpread\": {\n      const fragmentName = selection.name.value;\n      if (typeof fragmentMap === \"function\") {\n        return fragmentMap(fragmentName);\n      }\n      const fragment = fragmentMap && fragmentMap[fragmentName];\n      invariant(fragment, `No fragment named %s`, fragmentName);\n      return fragment || null;\n    }\n    default:\n      return null;\n  }\n}\n\nexport function isFullyUnmaskedOperation(document: DocumentNode) {\n  let isUnmasked = true;\n\n  visit(document, {\n    FragmentSpread: (node) => {\n      isUnmasked =\n        !!node.directives &&\n        node.directives.some((directive) => directive.name.value === \"unmask\");\n\n      if (!isUnmasked) {\n        return BREAK;\n      }\n    },\n  });\n\n  return isUnmasked;\n}\n"]}