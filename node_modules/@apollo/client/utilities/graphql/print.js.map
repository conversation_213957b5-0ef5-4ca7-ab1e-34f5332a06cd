{"version": 3, "file": "print.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/print.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,IAAI,SAAS,EAAE,MAAM,SAAS,CAAC;AAC7C,OAAO,EACL,oBAAoB,EACpB,UAAU,GAEX,MAAM,qBAAqB,CAAC;AAC7B,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAEvE,IAAI,UAAkD,CAAC;AACvD,MAAM,CAAC,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAChC,UAAC,GAAY;IACX,IAAI,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEjC,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QACxB,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAC9B,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC,EACD;IACE,KAAK;QACH,UAAU,GAAG,IAAI,oBAAoB,CACnC,UAAU,CAAC,KAAK,sCAA2B,CAC5C,CAAC;IACJ,CAAC;CACF,CACF,CAAC;AACF,KAAK,CAAC,KAAK,EAAE,CAAC;AAEd,IAAI,OAAO,EAAE,CAAC;IACZ,mBAAmB,CAAC,OAAO,EAAE,cAAM,OAAA,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAlC,CAAkC,CAAC,CAAC;AACzE,CAAC", "sourcesContent": ["import type { ASTNode } from \"graphql\";\nimport { print as origPrint } from \"graphql\";\nimport {\n  AutoCleanedWeakCache,\n  cacheSizes,\n  defaultCacheSizes,\n} from \"../caching/index.js\";\nimport { registerGlobalCache } from \"../caching/getMemoryInternals.js\";\n\nlet printCache!: AutoCleanedWeakCache<ASTNode, string>;\nexport const print = Object.assign(\n  (ast: ASTNode) => {\n    let result = printCache.get(ast);\n\n    if (!result) {\n      result = origPrint(ast);\n      printCache.set(ast, result);\n    }\n    return result;\n  },\n  {\n    reset() {\n      printCache = new AutoCleanedWeakCache<ASTNode, string>(\n        cacheSizes.print || defaultCacheSizes.print\n      );\n    },\n  }\n);\nprint.reset();\n\nif (__DEV__) {\n  registerGlobalCache(\"print\", () => (printCache ? printCache.size : 0));\n}\n"]}