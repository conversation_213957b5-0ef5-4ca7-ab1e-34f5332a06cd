{"version": 3, "file": "operations.js", "sourceRoot": "", "sources": ["../../../src/utilities/graphql/operations.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,sBAAsB,EAAE,MAAM,iBAAiB,CAAC;AAEzD,SAAS,WAAW,CAClB,QAAsB,EACtB,SAAgD;;IAEhD,OAAO,CAAA,MAAA,sBAAsB,CAAC,QAAQ,CAAC,0CAAE,SAAS,MAAK,SAAS,CAAC;AACnE,CAAC;AAED,MAAM,UAAU,mBAAmB,CAAC,QAAsB;IACxD,OAAO,WAAW,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAC3C,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,QAAsB;IACrD,OAAO,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AACxC,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,QAAsB;IAC5D,OAAO,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AAC/C,CAAC", "sourcesContent": ["import type { DocumentNode } from \"../../core/index.js\";\nimport { getOperationDefinition } from \"./getFromAST.js\";\n\nfunction isOperation(\n  document: DocumentNode,\n  operation: \"query\" | \"mutation\" | \"subscription\"\n) {\n  return getOperationDefinition(document)?.operation === operation;\n}\n\nexport function isMutationOperation(document: DocumentNode) {\n  return isOperation(document, \"mutation\");\n}\n\nexport function isQueryOperation(document: DocumentNode) {\n  return isOperation(document, \"query\");\n}\n\nexport function isSubscriptionOperation(document: DocumentNode) {\n  return isOperation(document, \"subscription\");\n}\n"]}