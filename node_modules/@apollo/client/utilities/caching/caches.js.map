{"version": 3, "file": "caches.js", "sourceRoot": "", "sources": ["../../../src/utilities/caching/caches.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,aAAa,CAAC;AAOrD,IAAM,gBAAgB,GAAG,IAAI,OAAO,EAAkB,CAAC;AACvD,SAAS,QAAQ,CAAC,KAAqB;IACrC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;QACpC,OAAO;IACT,CAAC;IACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;QACjC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAC5B,UAAU,CAAC;YACT,KAAK,CAAC,KAAK,EAAE,CAAC;YACd,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACjC,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC;AACH,CAAC;AACD;;;;;;;;;;GAUG;AACH,MAAM,CAAC,IAAM,oBAAoB,GAAG,UAClC,GAAwB,EACxB,OAAsD;IAEtD;;;;;;MAME;IACF,IAAM,KAAK,GAAG,IAAI,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC1C,KAAK,CAAC,GAAG,GAAG,UAAU,GAAQ,EAAE,KAAU;QACxC,IAAM,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3D,QAAQ,CAAC,IAA6B,CAAC,CAAC;QACxC,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;IACF,OAAO,KAAK,CAAC;AACf,CAA4B,CAAC;AAM7B;;;;;;;;;;GAUG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,UACpC,GAAwB,EACxB,OAAsD;IAEtD;;;;;;MAME;IACF,IAAM,KAAK,GAAG,IAAI,WAAW,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC5C,KAAK,CAAC,GAAG,GAAG,UAAU,GAAQ,EAAE,KAAU;QACxC,IAAM,GAAG,GAAG,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;QAC7D,QAAQ,CAAC,IAA6B,CAAC,CAAC;QACxC,OAAO,GAAG,CAAC;IACb,CAAC,CAAC;IACF,OAAO,KAAK,CAAC;AACf,CAA8B,CAAC", "sourcesContent": ["import { WeakCache, StrongCache } from \"@wry/caches\";\n\ninterface CleanableCache {\n  size: number;\n  max?: number;\n  clean: () => void;\n}\nconst scheduledCleanup = new WeakSet<CleanableCache>();\nfunction schedule(cache: CleanableCache) {\n  if (cache.size <= (cache.max || -1)) {\n    return;\n  }\n  if (!scheduledCleanup.has(cache)) {\n    scheduledCleanup.add(cache);\n    setTimeout(() => {\n      cache.clean();\n      scheduledCleanup.delete(cache);\n    }, 100);\n  }\n}\n/**\n * @internal\n * A version of WeakCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of WeakCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `WeakCache`.\n */\nexport const AutoCleanedWeakCache = function (\n  max?: number | undefined,\n  dispose?: ((value: any, key: any) => void) | undefined\n) {\n  /*\n  Some builds of `WeakCache` are function prototypes, some are classes.\n  This library still builds with an ES5 target, so we can't extend the\n  real classes.\n  Instead, we have to use this workaround until we switch to a newer build\n  target.\n  */\n  const cache = new WeakCache(max, dispose);\n  cache.set = function (key: any, value: any) {\n    const ret = WeakCache.prototype.set.call(this, key, value);\n    schedule(this as any as CleanableCache);\n    return ret;\n  };\n  return cache;\n} as any as typeof WeakCache;\n/**\n * @internal\n */\nexport type AutoCleanedWeakCache<K extends object, V> = WeakCache<K, V>;\n\n/**\n * @internal\n * A version of StrongCache that will auto-schedule a cleanup of the cache when\n * a new item is added and the cache reached maximum size.\n * Throttled to once per 100ms.\n *\n * @privateRemarks\n * Should be used throughout the rest of the codebase instead of StrongCache,\n * with the notable exception of usage in `wrap` from `optimism` - that one\n * already handles cleanup and should remain a `StrongCache`.\n */\nexport const AutoCleanedStrongCache = function (\n  max?: number | undefined,\n  dispose?: ((value: any, key: any) => void) | undefined\n) {\n  /*\n  Some builds of `StrongCache` are function prototypes, some are classes.\n  This library still builds with an ES5 target, so we can't extend the\n  real classes.\n  Instead, we have to use this workaround until we switch to a newer build\n  target.\n  */\n  const cache = new StrongCache(max, dispose);\n  cache.set = function (key: any, value: any) {\n    const ret = StrongCache.prototype.set.call(this, key, value);\n    schedule(this as any as CleanableCache);\n    return ret;\n  };\n  return cache;\n} as any as typeof StrongCache;\n/**\n * @internal\n */\nexport type AutoCleanedStrongCache<K, V> = StrongCache<K, V>;\n"]}