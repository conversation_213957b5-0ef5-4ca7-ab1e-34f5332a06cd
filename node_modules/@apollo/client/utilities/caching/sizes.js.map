{"version": 3, "file": "sizes.js", "sourceRoot": "", "sources": ["../../../src/utilities/caching/sizes.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAsR7C,IAAM,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AACvD;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,CAAC,IAAM,UAAU,gBAA6B,MAAM,CAAC,eAAe,CAAC,CAAE,CAAC", "sourcesContent": ["import { global } from \"../globals/index.js\";\n\ndeclare global {\n  interface Window {\n    [cacheSizeSymbol]?: Partial<CacheSizes>;\n  }\n}\n\n/**\n * The cache sizes used by various Apollo Client caches.\n *\n * @remarks\n * All configurable caches hold memoized values. If an item is\n * cache-collected, it incurs only a small performance impact and\n * doesn't cause data loss. A smaller cache size might save you memory.\n *\n * You should choose cache sizes appropriate for storing a reasonable\n * number of values rather than every value. To prevent too much recalculation,\n * choose cache sizes that are at least large enough to hold memoized values for\n * all hooks/queries on the screen at any given time.\n */\n/*\n * We assume a \"base value\" of 1000 here, which is already very generous.\n * In most applications, it will be very unlikely that 1000 different queries\n * are on screen at the same time.\n */\nexport interface CacheSizes {\n  /**\n   * Cache size for the [`print`](https://github.com/apollographql/apollo-client/blob/main/src/utilities/graphql/print.ts) function.\n   *\n   * It is called with transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @remarks\n   * This method is called to transform a GraphQL query AST parsed by `gql`\n   * back into a GraphQL string.\n   *\n   * @privateRemarks\n   * This method is called from the `QueryManager` and various `ApolloLink`s,\n   * always with the \"serverQuery\", so the server-facing part of a transformed\n   * `DocumentNode`.\n   */\n  print: number;\n  /**\n   * Cache size for the [`parser`](https://github.com/apollographql/apollo-client/blob/main/src/react/parser/index.ts) function.\n   *\n   * It is called with user-provided `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `1000`.\n   *\n   * @remarks\n   * This method is called by HOCs and hooks.\n   *\n   * @privateRemarks\n   * This function is used directly in HOCs, and nowadays mainly accessed by\n   * calling `verifyDocumentType` from various hooks.\n   * It is called with a user-provided DocumentNode.\n   */\n  parser: number;\n  /**\n   * Cache size for the cache of [`DocumentTransform`](https://github.com/apollographql/apollo-client/blob/main/src/utilities/graphql/DocumentTransform.ts)\n   * instances with the `cache` option set to `true`.\n   *\n   * Can be called with user-defined or already-transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @remarks\n   * The cache size here should be chosen with other `DocumentTransform`s in mind.\n   * For example, if there was a `DocumentTransform` that would take `x` `DocumentNode`s,\n   * and returned a differently-transformed `DocumentNode` depending if the app is\n   * online or offline, then we assume that the cache returns `2*x` documents.\n   * If that were concatenated with another `DocumentTransform` that would\n   * also duplicate the cache size, you'd need to account for `4*x` documents\n   * returned by the second transform.\n   *\n   * Due to an implementation detail of Apollo Client, if you use custom document\n   * transforms you should always add `n` (the \"base\" number of user-provided\n   * Documents) to the resulting cache size.\n   *\n   * If we assume that the user-provided transforms receive `n` documents and\n   * return `n` documents, the cache size should be `2*n`.\n   *\n   * If we assume that the chain of user-provided transforms receive `n` documents and\n   * return `4*n` documents, the cache size should be `5*n`.\n   *\n   * This size should also then be used in every other cache that mentions that\n   * it operates on a \"transformed\" `DocumentNode`.\n   *\n   * @privateRemarks\n   * Cache size for the `performWork` method of each [`DocumentTransform`](https://github.com/apollographql/apollo-client/blob/main/src/utilities/graphql/DocumentTransform.ts).\n   *\n   * No user-provided DocumentNode will actually be \"the last one\", as we run the\n   * `defaultDocumentTransform` before *and* after the user-provided transforms.\n   * For that reason, we need the extra `n` here - `n` for \"before transformation\"\n   * plus the actual maximum cache size of the user-provided transform chain.\n   *\n   * This method is called from `transformDocument`, which is called from\n   * `QueryManager` with a user-provided DocumentNode.\n   * It is also called with already-transformed DocumentNodes, assuming the\n   * user provided additional transforms.\n   *\n   */\n  \"documentTransform.cache\": number;\n  /**\n   * A cache inside of [`QueryManager`](https://github.com/apollographql/apollo-client/blob/main/src/core/QueryManager.ts).\n   *\n   * It is called with transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @privateRemarks\n   * Cache size for the `transformCache` used in the `getDocumentInfo` method of `QueryManager`.\n   * Called throughout the `QueryManager` with transformed DocumentNodes.\n   */\n  \"queryManager.getDocumentInfo\": number;\n  /**\n   * A cache inside of [`PersistedQueryLink`](https://github.com/apollographql/apollo-client/blob/main/src/link/persisted-queries/index.ts).\n   *\n   * It is called with transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @remarks\n   * This cache is used to cache the hashes of persisted queries.\n   *\n   * @privateRemarks\n   * Cache size for the `hashesByQuery` cache in the `PersistedQueryLink`.\n   */\n  \"PersistedQueryLink.persistedQueryHashes\": number;\n  /**\n   * Cache used by [`canonicalStringify`](https://github.com/apollographql/apollo-client/blob/main/src/utilities/common/canonicalStringify.ts).\n   *\n   * @defaultValue\n   * Defaults to `1000`.\n   *\n   * @remarks\n   * This cache contains the sorted keys of objects that are stringified by\n   * `canonicalStringify`.\n   * It uses the stringified unsorted keys of objects as keys.\n   * The cache will not grow beyond the size of different object **shapes**\n   * encountered in an application, no matter how much actual data gets stringified.\n   *\n   * @privateRemarks\n   * Cache size for the `sortingMap` in `canonicalStringify`.\n   */\n  canonicalStringify: number;\n  /**\n   * A cache inside of [`FragmentRegistry`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/fragmentRegistry.ts).\n   *\n   * Can be called with user-defined or already-transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @privateRemarks\n   *\n   * Cache size for the `transform` method of FragmentRegistry.\n   * This function is called as part of the `defaultDocumentTransform` which will be called with\n   * user-provided and already-transformed DocumentNodes.\n   *\n   */\n  \"fragmentRegistry.transform\": number;\n  /**\n   * A cache inside of [`FragmentRegistry`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/fragmentRegistry.ts).\n   *\n   * This function is called with fragment names in the form of a string.\n   *\n   * @defaultValue\n   * Defaults to `1000`.\n   *\n   * @remarks\n   * The size of this case should be chosen with the number of fragments in\n   * your application in mind.\n   *\n   * Note:\n   * This function is a dependency of `fragmentRegistry.transform`, so having too small of a cache size here\n   * might involuntarily invalidate values in the `transform` cache.\n   *\n   * @privateRemarks\n   * Cache size for the `lookup` method of FragmentRegistry.\n   */\n  \"fragmentRegistry.lookup\": number;\n  /**\n   * Cache size for the `findFragmentSpreads` method of [`FragmentRegistry`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/fragmentRegistry.ts).\n   *\n   * This function is called with transformed `DocumentNode`s, as well as recursively\n   * with every fragment spread referenced within that, or a fragment referenced by a\n   * fragment spread.\n   *\n   * @defaultValue\n   * Defaults to `4000`.\n   *\n   * @remarks\n   *\n   * Note: This function is a dependency of `fragmentRegistry.transform`, so having too small of cache size here\n   * might involuntarily invalidate values in the `transform` cache.\n   */\n  \"fragmentRegistry.findFragmentSpreads\": number;\n  /**\n   * Cache size for the `getFragmentDoc` method of [`ApolloCache`](https://github.com/apollographql/apollo-client/blob/main/src/cache/core/cache.ts).\n   *\n   * This function is called with user-provided fragment definitions.\n   *\n   * @defaultValue\n   * Defaults to `1000`.\n   *\n   * @remarks\n   * This function is called from `readFragment` with user-provided fragment definitions.\n   */\n  \"cache.fragmentQueryDocuments\": number;\n  /**\n   * Cache used in [`removeTypenameFromVariables`](https://github.com/apollographql/apollo-client/blob/main/src/link/remove-typename/removeTypenameFromVariables.ts).\n   *\n   * This function is called transformed `DocumentNode`s.\n   *\n   * @defaultValue\n   * Defaults to `2000`.\n   *\n   * @privateRemarks\n   * Cache size for the `getVariableDefinitions` function of `removeTypenameFromVariables`.\n   */\n  \"removeTypenameFromVariables.getVariableDefinitions\": number;\n  /**\n   * Cache size for the `maybeBroadcastWatch` method on [`InMemoryCache`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/inMemoryCache.ts).\n   *\n   * Note: `maybeBroadcastWatch` will be set to the `resultCacheMaxSize` option and\n   * will fall back to this configuration value if the option is not set.\n   *\n   * @defaultValue\n   * Defaults to `5000`.\n   *\n   * @remarks\n   * This method is used for dependency tracking in the `InMemoryCache` and\n   * prevents from unnecessary re-renders.\n   * It is recommended to keep this value significantly higher than the number of\n   * possible subscribers you will have active at the same time in your application\n   * at any time.\n   */\n  \"inMemoryCache.maybeBroadcastWatch\": number;\n  /**\n   * Cache size for the `executeSelectionSet` method on [`StoreReader`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/readFromStore.ts).\n   *\n   * Note:\n   * `executeSelectionSet` will be set to the `resultCacheMaxSize` option and\n   * will fall back to this configuration value if the option is not set.\n   *\n   * @defaultValue\n   * Defaults to `50000`.\n   *\n   * @remarks\n   * Every object that is read from the cache will be cached here, so it is\n   * recommended to set this to a high value.\n   */\n  \"inMemoryCache.executeSelectionSet\": number;\n  /**\n   * Cache size for the `executeSubSelectedArray` method on [`StoreReader`](https://github.com/apollographql/apollo-client/blob/main/src/cache/inmemory/readFromStore.ts).\n   *\n   * Note:\n   * `executeSubSelectedArray` will be set to the `resultCacheMaxSize` option and\n   * will fall back to this configuration value if the option is not set.\n   *\n   * @defaultValue\n   * Defaults to `10000`.\n   *\n   * @remarks\n   * Every array that is read from the cache will be cached here, so it is\n   * recommended to set this to a high value.\n   */\n  \"inMemoryCache.executeSubSelectedArray\": number;\n}\n\nconst cacheSizeSymbol = Symbol.for(\"apollo.cacheSize\");\n/**\n *\n * The global cache size configuration for Apollo Client.\n *\n * @remarks\n *\n * You can directly modify this object, but any modification will\n * only have an effect on caches that are created after the modification.\n *\n * So for global caches, such as `parser`, `canonicalStringify` and `print`,\n * you might need to call `.reset` on them, which will essentially re-create them.\n *\n * Alternatively, you can set `globalThis[Symbol.for(\"apollo.cacheSize\")]` before\n * you load the Apollo Client package:\n *\n * @example\n * ```ts\n * globalThis[Symbol.for(\"apollo.cacheSize\")] = {\n *   parser: 100\n * } satisfies Partial<CacheSizes> // the `satisfies` is optional if using TypeScript\n * ```\n */\nexport const cacheSizes: Partial<CacheSizes> = { ...global[cacheSizeSymbol] };\n\nexport const enum defaultCacheSizes {\n  parser = 1000,\n  canonicalStringify = 1000,\n  print = 2000,\n  \"documentTransform.cache\" = 2000,\n  \"queryManager.getDocumentInfo\" = 2000,\n  \"PersistedQueryLink.persistedQueryHashes\" = 2000,\n  \"fragmentRegistry.transform\" = 2000,\n  \"fragmentRegistry.lookup\" = 1000,\n  \"fragmentRegistry.findFragmentSpreads\" = 4000,\n  \"cache.fragmentQueryDocuments\" = 1000,\n  \"removeTypenameFromVariables.getVariableDefinitions\" = 2000,\n  \"inMemoryCache.maybeBroadcastWatch\" = 5000,\n  \"inMemoryCache.executeSelectionSet\" = 50000,\n  \"inMemoryCache.executeSubSelectedArray\" = 10000,\n}\n"]}