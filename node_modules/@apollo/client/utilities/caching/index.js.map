{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/utilities/caching/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,oBAAoB,EAAE,MAAM,aAAa,CAAC;AAE3E,OAAO,EAAE,UAAU,EAAqB,MAAM,YAAY,CAAC", "sourcesContent": ["export { AutoCleanedStrongCache, AutoCleanedWeakCache } from \"./caches.js\";\nexport type { CacheSizes } from \"./sizes.js\";\nexport { cacheSizes, defaultCacheSizes } from \"./sizes.js\";\n"]}