{"version": 3, "file": "getMemoryInternals.js", "sourceRoot": "", "sources": ["../../../src/utilities/caching/getMemoryInternals.ts"], "names": [], "mappings": ";AASA,OAAO,EAAE,UAAU,EAAqB,MAAM,YAAY,CAAC;AAE3D,IAAM,YAAY,GAId,EAAE,CAAC;AAEP,MAAM,UAAU,mBAAmB,CACjC,IAA+B,EAC/B,OAAqB;IAErB,YAAY,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;AAC/B,CAAC;AAwCD;;;GAGG;AACH,MAAM,CAAC,IAAM,8BAA8B,GACzC,OAAO,CAAC,CAAC;IACN,+BAEC;IACJ,CAAC,CAAC,SAAS,CAAC;AAEd;;;GAGG;AACH,MAAM,CAAC,IAAM,+BAA+B,GAC1C,OAAO,CAAC,CAAC;IACN,gCAEC;IACJ,CAAC,CAAC,SAAS,CAAC;AAEd;;;GAGG;AACH,MAAM,CAAC,IAAM,6BAA6B,GACxC,OAAO,CAAC,CAAC;IACN,8BAEC;IACJ,CAAC,CAAC,SAAS,CAAC;AAEd,SAAS,oBAAoB;IAC3B,qHAAqH;IACrH,IAAM,QAAQ,GAAqC;QACjD,MAAM,wCAA6B;QACnC,kBAAkB,oDAAyC;QAC3D,KAAK,uCAA4B;QACjC,yBAAyB,yDAA8C;QACvE,8BAA8B,8DACqB;QACnD,yCAAyC,yEACqB;QAC9D,4BAA4B,4DACqB;QACjD,yBAAyB,yDAA8C;QACvE,sCAAsC,sEACqB;QAC3D,8BAA8B,8DACqB;QACnD,oDAAoD,oFACqB;QACzE,mCAAmC,mEACqB;QACxD,mCAAmC,oEACqB;QACxD,uCAAuC,wEACqB;KAC7D,CAAC;IACF,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,UAAC,EAAM;YAAL,CAAC,QAAA,EAAE,CAAC,QAAA;QAAM,OAAA;YACvC,CAAC;YACD,UAAU,CAAC,CAAqB,CAAC,IAAI,CAAC;SACvC;IAHwC,CAGxC,CAAC,CACH,CAAC;AACJ,CAAC;AAED,SAAS,+BAA+B;;IACtC,IAAI,CAAC,OAAO;QAAE,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;IAEpE,OAAO;QACL,MAAM,EAAE,oBAAoB,EAAE;QAC9B,KAAK,aACH,KAAK,EAAE,MAAA,YAAY,CAAC,KAAK,4DAAI,EAC7B,MAAM,EAAE,MAAA,YAAY,CAAC,MAAM,4DAAI,EAC/B,kBAAkB,EAAE,MAAA,YAAY,CAAC,kBAAkB,4DAAI,EACvD,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAC1B,YAAY,EAAE;gBACZ,eAAe,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,gBAAgB,CAAC,CAAC,IAAI;gBAC5D,kBAAkB,EAAE,aAAa,CAC/B,IAAI,CAAC,cAAc,CAAC,CAAC,iBAAiB,CACvC;aACF,IACG,MAAA,MAAA,IAAI,CAAC,KAAK,EAAC,kBAAkB,kDAG8B,CAChE;KACF,CAAC;AACJ,CAAC;AAED,SAAS,8BAA8B;IACrC,OAAO;QACL,KAAK,EAAE;YACL,sBAAsB,EAAE,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SACtE;KACF,CAAC;AACJ,CAAC;AAED,SAAS,gCAAgC;IACvC,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAMzB,CAAC;IAEN,6BACK,8BAA8B,CAAC,KAAK,CAAC,IAAW,CAAC,KACpD,4BAA4B,EAAE,aAAa,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,EACzE,aAAa,EAAE;YACb,mBAAmB,EAAE,qBAAqB,CACxC,IAAI,CAAC,aAAa,CAAC,CAAC,qBAAqB,CAAC,CAC3C;YACD,uBAAuB,EAAE,qBAAqB,CAC5C,IAAI,CAAC,aAAa,CAAC,CAAC,yBAAyB,CAAC,CAC/C;YACD,mBAAmB,EAAE,qBAAqB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACxE,EACD,gBAAgB,EAAE;YAChB,mBAAmB,EAAE,qBAAqB,CACxC,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,mBAAmB,CAC/B;YACD,MAAM,EAAE,qBAAqB,CAAC,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,MAAM,CAAC;YAChD,SAAS,EAAE,qBAAqB,CAAC,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,SAAS,CAAC;SACvD,IACD;AACJ,CAAC;AAED,SAAS,SAAS,CAAC,CAAY;IAC7B,OAAO,CAAC,CAAC,CAAC,IAAI,UAAU,IAAI,CAAC,CAAC;AAChC,CAAC;AAED,SAAS,qBAAqB,CAAC,CAAY;IACzC,OAAO,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC;AAC3C,CAAC;AAED,SAAS,SAAS,CAAI,KAA2B;IAC/C,OAAO,KAAK,IAAI,IAAI,CAAC;AACvB,CAAC;AAED,SAAS,aAAa,CAAC,SAA6B;IAClD,OAAO,oBAAoB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,EAAX,CAAW,CAAC,CAAC;AACrE,CAAC;AAED,SAAS,oBAAoB,CAAC,SAA6B;IACzD,OAAO,SAAS,CAAC,CAAC;QACd;YACE,qBAAqB,CAAC,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAG,aAAa,CAAC,CAAC;WAC9C,oBAAoB,CAAC,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAG,MAAM,CAAC,CAAC,SACzC,oBAAoB,CAAC,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAG,OAAO,CAAC,CAAC,QAC7C,MAAM,CAAC,SAAS,CAAC;QACrB,CAAC,CAAC,EAAE,CAAC;AACT,CAAC;AAED,SAAS,QAAQ,CAAC,IAAiB;;IACjC,OAAO,IAAI,CAAC,CAAC;QACT;YACE,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,kBAAkB,oDAAI;WACzB,QAAQ,CAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,IAAI,CAAC,SACpB,QAAQ,CAAC,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,KAAK,CAAC,QACxB,MAAM,CAAC,SAAS,CAAC;QACrB,CAAC,CAAC,EAAE,CAAC;AACT,CAAC", "sourcesContent": ["import type { OptimisticWrapperFunction } from \"optimism\";\nimport type {\n  InMemoryCache,\n  DocumentTransform,\n  ApolloLink,\n  ApolloCache,\n} from \"../../core/index.js\";\nimport type { ApolloClient } from \"../../core/index.js\";\nimport type { CacheSizes } from \"./sizes.js\";\nimport { cacheSizes, defaultCacheSizes } from \"./sizes.js\";\n\nconst globalCaches: {\n  print?: () => number;\n  parser?: () => number;\n  canonicalStringify?: () => number;\n} = {};\n\nexport function registerGlobalCache(\n  name: keyof typeof globalCaches,\n  getSize: () => number\n) {\n  globalCaches[name] = getSize;\n}\n\n/**\n * Transformative helper type to turn a function of the form\n * ```ts\n * (this: any) => R\n * ```\n * into a function of the form\n * ```ts\n * () => R\n * ```\n * preserving the return type, but removing the `this` parameter.\n *\n * @remarks\n *\n * Further down in the definitions of `_getApolloClientMemoryInternals`,\n * `_getApolloCacheMemoryInternals` and `_getInMemoryCacheMemoryInternals`,\n * having the `this` parameter annotation is extremely useful for type checking\n * inside the function.\n *\n * If this is preserved in the exported types, though, it leads to a situation\n * where `ApolloCache.getMemoryInternals` is a function that requires a `this`\n * of the type `ApolloCache`, while the extending class `InMemoryCache` has a\n * `getMemoryInternals` function that requires a `this` of the type\n * `InMemoryCache`.\n * This is not compatible with TypeScript's inheritence system (although it is\n * perfectly correct), and so TypeScript will complain loudly.\n *\n * We still want to define our functions with the `this` annotation, though,\n * and have the return type inferred.\n * (This requirement for return type inference here makes it impossible to use\n * a function overload that is more explicit on the inner overload than it is\n * on the external overload.)\n *\n * So in the end, we use this helper to remove the `this` annotation from the\n * exported function types, while keeping it in the internal implementation.\n *\n */\ntype RemoveThis<T> = T extends (this: any) => infer R ? () => R : never;\n\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport const getApolloClientMemoryInternals =\n  __DEV__ ?\n    (_getApolloClientMemoryInternals as RemoveThis<\n      typeof _getApolloClientMemoryInternals\n    >)\n  : undefined;\n\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport const getInMemoryCacheMemoryInternals =\n  __DEV__ ?\n    (_getInMemoryCacheMemoryInternals as RemoveThis<\n      typeof _getInMemoryCacheMemoryInternals\n    >)\n  : undefined;\n\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport const getApolloCacheMemoryInternals =\n  __DEV__ ?\n    (_getApolloCacheMemoryInternals as RemoveThis<\n      typeof _getApolloCacheMemoryInternals\n    >)\n  : undefined;\n\nfunction getCurrentCacheSizes() {\n  // `defaultCacheSizes` is a `const enum` that will be inlined during build, so we have to reconstruct it's shape here\n  const defaults: Record<keyof CacheSizes, number> = {\n    parser: defaultCacheSizes[\"parser\"],\n    canonicalStringify: defaultCacheSizes[\"canonicalStringify\"],\n    print: defaultCacheSizes[\"print\"],\n    \"documentTransform.cache\": defaultCacheSizes[\"documentTransform.cache\"],\n    \"queryManager.getDocumentInfo\":\n      defaultCacheSizes[\"queryManager.getDocumentInfo\"],\n    \"PersistedQueryLink.persistedQueryHashes\":\n      defaultCacheSizes[\"PersistedQueryLink.persistedQueryHashes\"],\n    \"fragmentRegistry.transform\":\n      defaultCacheSizes[\"fragmentRegistry.transform\"],\n    \"fragmentRegistry.lookup\": defaultCacheSizes[\"fragmentRegistry.lookup\"],\n    \"fragmentRegistry.findFragmentSpreads\":\n      defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"],\n    \"cache.fragmentQueryDocuments\":\n      defaultCacheSizes[\"cache.fragmentQueryDocuments\"],\n    \"removeTypenameFromVariables.getVariableDefinitions\":\n      defaultCacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"],\n    \"inMemoryCache.maybeBroadcastWatch\":\n      defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"],\n    \"inMemoryCache.executeSelectionSet\":\n      defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"],\n    \"inMemoryCache.executeSubSelectedArray\":\n      defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"],\n  };\n  return Object.fromEntries(\n    Object.entries(defaults).map(([k, v]) => [\n      k,\n      cacheSizes[k as keyof CacheSizes] || v,\n    ])\n  );\n}\n\nfunction _getApolloClientMemoryInternals(this: ApolloClient<any>) {\n  if (!__DEV__) throw new Error(\"only supported in development mode\");\n\n  return {\n    limits: getCurrentCacheSizes(),\n    sizes: {\n      print: globalCaches.print?.(),\n      parser: globalCaches.parser?.(),\n      canonicalStringify: globalCaches.canonicalStringify?.(),\n      links: linkInfo(this.link),\n      queryManager: {\n        getDocumentInfo: this[\"queryManager\"][\"transformCache\"].size,\n        documentTransforms: transformInfo(\n          this[\"queryManager\"].documentTransform\n        ),\n      },\n      ...(this.cache.getMemoryInternals?.() as Partial<\n        ReturnType<typeof _getApolloCacheMemoryInternals>\n      > &\n        Partial<ReturnType<typeof _getInMemoryCacheMemoryInternals>>),\n    },\n  };\n}\n\nfunction _getApolloCacheMemoryInternals(this: ApolloCache<any>) {\n  return {\n    cache: {\n      fragmentQueryDocuments: getWrapperInformation(this[\"getFragmentDoc\"]),\n    },\n  };\n}\n\nfunction _getInMemoryCacheMemoryInternals(this: InMemoryCache) {\n  const fragments = this.config.fragments as\n    | undefined\n    | {\n        findFragmentSpreads?: Function;\n        transform?: Function;\n        lookup?: Function;\n      };\n\n  return {\n    ..._getApolloCacheMemoryInternals.apply(this as any),\n    addTypenameDocumentTransform: transformInfo(this[\"addTypenameTransform\"]),\n    inMemoryCache: {\n      executeSelectionSet: getWrapperInformation(\n        this[\"storeReader\"][\"executeSelectionSet\"]\n      ),\n      executeSubSelectedArray: getWrapperInformation(\n        this[\"storeReader\"][\"executeSubSelectedArray\"]\n      ),\n      maybeBroadcastWatch: getWrapperInformation(this[\"maybeBroadcastWatch\"]),\n    },\n    fragmentRegistry: {\n      findFragmentSpreads: getWrapperInformation(\n        fragments?.findFragmentSpreads\n      ),\n      lookup: getWrapperInformation(fragments?.lookup),\n      transform: getWrapperInformation(fragments?.transform),\n    },\n  };\n}\n\nfunction isWrapper(f?: Function): f is OptimisticWrapperFunction<any, any> {\n  return !!f && \"dirtyKey\" in f;\n}\n\nfunction getWrapperInformation(f?: Function) {\n  return isWrapper(f) ? f.size : undefined;\n}\n\nfunction isDefined<T>(value: T | undefined | null): value is T {\n  return value != null;\n}\n\nfunction transformInfo(transform?: DocumentTransform) {\n  return recurseTransformInfo(transform).map((cache) => ({ cache }));\n}\n\nfunction recurseTransformInfo(transform?: DocumentTransform): number[] {\n  return transform ?\n      [\n        getWrapperInformation(transform?.[\"performWork\"]),\n        ...recurseTransformInfo(transform?.[\"left\"]),\n        ...recurseTransformInfo(transform?.[\"right\"]),\n      ].filter(isDefined)\n    : [];\n}\n\nfunction linkInfo(link?: ApolloLink): unknown[] {\n  return link ?\n      [\n        link?.getMemoryInternals?.(),\n        ...linkInfo(link?.left),\n        ...linkInfo(link?.right),\n      ].filter(isDefined)\n    : [];\n}\n"]}