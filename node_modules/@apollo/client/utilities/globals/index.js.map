{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/utilities/globals/index.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,SAAS,EACT,iBAAiB,EACjB,cAAc,GACf,MAAM,wBAAwB,CAAC;AAEhC,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AACnC,OAAO,EAAE,OAAO,IAAI,MAAM,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,cAAc,EAAE,CAAC;AAExD;;;GAGG;AACH,4DAA4D;AAC5D,kCAAkC;AAClC,aAAa;AACb,MAAM,CAAC,IAAM,GAAG,GAAG,UAAU,CAAC,OAAO,KAAK,KAAK,CAAC;AAChD,OAAO,EAAE,GAAG,IAAI,OAAO,EAAE,CAAC", "sourcesContent": ["import {\n  invariant,\n  newInvariantError,\n  InvariantError,\n} from \"./invariantWrappers.js\";\n\nexport { maybe } from \"./maybe.js\";\nexport { default as global } from \"./global.js\";\nexport { invariant, newInvariantError, InvariantError };\n\n/**\n * @deprecated we do not use this internally anymore,\n * it is just exported for backwards compatibility\n */\n// this file is extempt from automatic `__DEV__` replacement\n// so we have to write it out here\n// @ts-ignore\nexport const DEV = globalThis.__DEV__ !== false;\nexport { DEV as __DEV__ };\n"]}