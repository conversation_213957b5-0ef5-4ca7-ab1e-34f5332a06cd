import { invariant, newInvariantError, InvariantError } from "./invariantWrappers.js";
export { maybe } from "./maybe.js";
export { default as global } from "./global.js";
export { invariant, newInvariantError, InvariantError };
/**
 * @deprecated we do not use this internally anymore,
 * it is just exported for backwards compatibility
 */
export declare const DEV: boolean;
export { DEV as __DEV__ };
//# sourceMappingURL=index.d.ts.map