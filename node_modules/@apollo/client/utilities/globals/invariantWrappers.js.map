{"version": 3, "file": "invariantWrappers.js", "sourceRoot": "", "sources": ["../../../src/utilities/globals/invariantWrappers.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,IAAI,iBAAiB,EAAE,cAAc,EAAE,MAAM,cAAc,CAAC;AAC9E,OAAO,EAAE,OAAO,EAAE,MAAM,kBAAkB,CAAC;AAC3C,OAAO,MAAM,MAAM,aAAa,CAAC;AAEjC,OAAO,EAAE,mBAAmB,EAAE,MAAM,kCAAkC,CAAC;AAEvE,SAAS,IAAI,CAAC,EAA0C;IACtD,OAAO,UAAU,OAAyB;QAAE,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,6BAAc;;QACxD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;YAChC,IAAM,IAAI,GAAG,OAAO,CAAC;YACrB,OAAO,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO,GAAG,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC1C,IAAI,GAAG,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;QACD,EAAE,eAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;IAChC,CAAC,CAAC;AACJ,CAAC;AAgDD,IAAM,SAAS,GAAqB,MAAM,CAAC,MAAM,CAC/C,SAAS,SAAS,CAChB,SAAc,EACd,OAAyB;IACzB,cAAkB;SAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;QAAlB,6BAAkB;;IAElB,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,iBAAiB,CACf,SAAS,EACT,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CACxE,CAAC;IACJ,CAAC;AACH,CAAC,EACD;IACE,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;IACpC,GAAG,EAAE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;IAChC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;IAClC,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;CACrC,CACF,CAAC;AAEF;;;;;;;;;GASG;AACH,SAAS,iBAAiB,CACxB,OAAyB;IACzB,wBAA4B;SAA5B,UAA4B,EAA5B,qBAA4B,EAA5B,IAA4B;QAA5B,uCAA4B;;IAE5B,OAAO,IAAI,cAAc,CACvB,kBAAkB,CAAC,OAAO,EAAE,cAAc,CAAC;QACzC,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,CAC/C,CAAC;AACJ,CAAC;AAED,IAAM,yBAAyB,GAAG,MAAM,CAAC,GAAG,CAC1C,4BAA4B,GAAG,OAAO,CACvC,CAAC;AASF,SAAS,SAAS,CAAC,GAAQ;IACzB,IAAI,OAAO,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC3B,OAAO,GAAG,CAAC;IACb,CAAC;IAED,IAAI,CAAC;QACH,OAAO,mBAAmB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAAC,WAAM,CAAC;QACP,OAAO,oBAAoB,CAAC;IAC9B,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CACzB,OAAyB,EACzB,WAA2B;IAA3B,4BAAA,EAAA,gBAA2B;IAE3B,IAAI,CAAC,OAAO;QAAE,OAAO;IACrB,OAAO,CACL,MAAM,CAAC,yBAAyB,CAAC;QACjC,MAAM,CAAC,yBAAyB,CAAC,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CACvE,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAC1B,OAAyB,EACzB,WAA2B;IAA3B,4BAAA,EAAA,gBAA2B;IAE3B,IAAI,CAAC,OAAO;QAAE,OAAO;IACrB,OAAO,sGAA+F,kBAAkB,CACtH,IAAI,CAAC,SAAS,CAAC;QACb,OAAO,SAAA;QACP,OAAO,SAAA;QACP,IAAI,EAAE,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC;KACjC,CAAC,CACH,CAAE,CAAC;AACN,CAAC;AAED,OAAO,EACL,SAAS,EACT,cAAc,EACd,iBAAiB,EACjB,yBAAyB,GAC1B,CAAC", "sourcesContent": ["import { invariant as originalInvariant, InvariantError } from \"ts-invariant\";\nimport { version } from \"../../version.js\";\nimport global from \"./global.js\";\nimport type { ErrorCodes } from \"../../invariantErrorCodes.js\";\nimport { stringifyForDisplay } from \"../common/stringifyForDisplay.js\";\n\nfunction wrap(fn: (msg?: string, ...args: any[]) => void) {\n  return function (message?: string | number, ...args: any[]) {\n    if (typeof message === \"number\") {\n      const arg0 = message;\n      message = getHandledErrorMsg(arg0);\n      if (!message) {\n        message = getFallbackErrorMsg(arg0, args);\n        args = [];\n      }\n    }\n    fn(...[message].concat(args));\n  };\n}\n\ntype LogFunction = {\n  /**\n   * Logs a `$level` message if the user used `ts-invariant`'s `setVerbosity` to set\n   * a verbosity level of `$level` or lower. (defaults to `\"log\"`).\n   *\n   * The user will either be presented with a link to the documentation for the message,\n   * or they can use the `loadDevMessages` to add the message strings to the bundle.\n   * The documentation will display the message without argument substitution.\n   * Instead, the arguments will be printed on the console after the link.\n   *\n   * `message` can only be a string, a concatenation of strings, or a ternary statement\n   * that results in a string. This will be enforced on build, where the message will\n   * be replaced with a message number.\n   *\n   * String substitutions like %s, %o, %d or %f are supported.\n   */\n  (message?: any, ...optionalParams: unknown[]): void;\n};\n\ntype WrappedInvariant = {\n  /**\n   * Throws and InvariantError with the given message if the condition is false.\n   *\n   * `message` can only be a string, a concatenation of strings, or a ternary statement\n   * that results in a string. This will be enforced on build, where the message will\n   * be replaced with a message number.\n   *\n   * The user will either be presented with a link to the documentation for the message,\n   * or they can use the `loadErrorMessages` to add the message strings to the bundle.\n   * The documentation will display the message with the arguments substituted.\n   *\n   * String substitutions with %s are supported and will also return\n   * pretty-stringified objects.\n   * Excess `optionalParams` will be swallowed.\n   */\n  (\n    condition: any,\n    message?: string | number,\n    ...optionalParams: unknown[]\n  ): asserts condition;\n\n  debug: LogFunction;\n  log: LogFunction;\n  warn: LogFunction;\n  error: LogFunction;\n};\nconst invariant: WrappedInvariant = Object.assign(\n  function invariant(\n    condition: any,\n    message?: string | number,\n    ...args: unknown[]\n  ): asserts condition {\n    if (!condition) {\n      originalInvariant(\n        condition,\n        getHandledErrorMsg(message, args) || getFallbackErrorMsg(message, args)\n      );\n    }\n  },\n  {\n    debug: wrap(originalInvariant.debug),\n    log: wrap(originalInvariant.log),\n    warn: wrap(originalInvariant.warn),\n    error: wrap(originalInvariant.error),\n  }\n);\n\n/**\n * Returns an InvariantError.\n *\n * `message` can only be a string, a concatenation of strings, or a ternary statement\n * that results in a string. This will be enforced on build, where the message will\n * be replaced with a message number.\n * String substitutions with %s are supported and will also return\n * pretty-stringified objects.\n * Excess `optionalParams` will be swallowed.\n */\nfunction newInvariantError(\n  message?: string | number,\n  ...optionalParams: unknown[]\n) {\n  return new InvariantError(\n    getHandledErrorMsg(message, optionalParams) ||\n      getFallbackErrorMsg(message, optionalParams)\n  );\n}\n\nconst ApolloErrorMessageHandler = Symbol.for(\n  \"ApolloErrorMessageHandler_\" + version\n);\ndeclare global {\n  interface Window {\n    [ApolloErrorMessageHandler]?: {\n      (message: string | number, args: string[]): string | undefined;\n    } & ErrorCodes;\n  }\n}\n\nfunction stringify(arg: any) {\n  if (typeof arg == \"string\") {\n    return arg;\n  }\n\n  try {\n    return stringifyForDisplay(arg, 2).slice(0, 1000);\n  } catch {\n    return \"<non-serializable>\";\n  }\n}\n\nfunction getHandledErrorMsg(\n  message?: string | number,\n  messageArgs: unknown[] = []\n) {\n  if (!message) return;\n  return (\n    global[ApolloErrorMessageHandler] &&\n    global[ApolloErrorMessageHandler](message, messageArgs.map(stringify))\n  );\n}\n\nfunction getFallbackErrorMsg(\n  message?: string | number,\n  messageArgs: unknown[] = []\n) {\n  if (!message) return;\n  return `An error occurred! For more details, see the full error text at https://go.apollo.dev/c/err#${encodeURIComponent(\n    JSON.stringify({\n      version,\n      message,\n      args: messageArgs.map(stringify),\n    })\n  )}`;\n}\n\nexport {\n  invariant,\n  InvariantError,\n  newInvariantError,\n  ApolloErrorMessageHandler,\n};\n"]}