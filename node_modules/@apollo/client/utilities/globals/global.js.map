{"version": 3, "file": "global.js", "sourceRoot": "", "sources": ["../../../src/utilities/globals/global.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,YAAY,CAAC;AASnC,eAAe,CAAC,KAAK,CAAC,cAAM,OAAA,UAAU,EAAV,CAAU,CAAC;IACrC,KAAK,CAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC;IACnB,KAAK,CAAC,cAAM,OAAA,IAAI,EAAJ,CAAI,CAAC;IACjB,KAAK,CAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC;IACnB,6EAA6E;IAC7E,6EAA6E;IAC7E,0EAA0E;IAC1E,2EAA2E;IAC3E,6EAA6E;IAC7E,8DAA8D;IAC9D,KAAK,CAAC;QACJ,OAAO,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,EAAE,CAAC;IAC5C,CAAC,CAAC,CAA+B,CAAC", "sourcesContent": ["import { maybe } from \"./maybe.js\";\n\ndeclare global {\n  const __DEV__: boolean; // will be removed in `dist` by the `postprocessDist` script\n  interface Window {\n    __DEV__?: boolean;\n  }\n}\n\nexport default (maybe(() => globalThis) ||\n  maybe(() => window) ||\n  maybe(() => self) ||\n  maybe(() => global) ||\n  // We don't expect the Function constructor ever to be invoked at runtime, as\n  // long as at least one of globalThis, window, self, or global is defined, so\n  // we are under no obligation to make it easy for static analysis tools to\n  // detect syntactic usage of the Function constructor. If you think you can\n  // improve your static analysis to detect this obfuscation, think again. This\n  // is an arms race you cannot win, at least not in JavaScript.\n  maybe(function () {\n    return maybe.constructor(\"return this\")();\n  })) as typeof globalThis & Window;\n"]}