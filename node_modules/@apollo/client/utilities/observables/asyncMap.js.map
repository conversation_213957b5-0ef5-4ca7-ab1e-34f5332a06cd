{"version": 3, "file": "asyncMap.js", "sourceRoot": "", "sources": ["../../../src/utilities/observables/asyncMap.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAE7C,sEAAsE;AACtE,6CAA6C;AAC7C,MAAM,UAAU,QAAQ,CACtB,UAAyB,EACzB,KAAuC,EACvC,OAA4C;IAE5C,OAAO,IAAI,UAAU,CAAI,UAAC,QAAQ;QAChC,IAAI,YAAY,GAAG;YACjB,sEAAsE;YACtE,sEAAsE;YACtE,2CAA2C;YAC3C,IAAI,YAAC,QAAmB;gBACtB,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,IAAK,OAAA,OAAO,CAAC,QAAQ,EAAE,CAAC,EAAnB,CAAmB,CAAC,CAAC;YACvD,CAAC;SACe,CAAC;QAEnB,SAAS,YAAY,CACnB,QAAuC,EACvC,GAAqB;YAErB,OAAO,UAAC,GAAG;gBACT,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAM,IAAI,GAAG;wBACX,mEAAmE;wBACnE,4DAA4D;wBAC5D,OAAA,QAAQ,CAAC,MAAM,CAAC,CAAC;4BACf,uBAAuB,CAAE,CAAS;4BACpC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;oBAFf,CAEe,CAAC;oBAElB,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,IAAI,CAC/C,UAAC,MAAM,IAAK,OAAA,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAArB,CAAqB,EACjC,UAAC,KAAK,IAAK,OAAA,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAArB,CAAqB,CACjC,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;gBACrB,CAAC;YACH,CAAC,CAAC;QACJ,CAAC;QAED,IAAM,OAAO,GAAgB;YAC3B,IAAI,EAAE,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC;YACjC,KAAK,EAAE,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC;YACrC,QAAQ;gBACN,iEAAiE;gBACjE,mEAAmE;gBACnE,mBAAmB,CAAC,YAAY,CAAC,IAAI,CAAC,cAAM,OAAA,QAAQ,CAAC,QAAQ,EAAE,EAAnB,CAAmB,CAAC,CAAC;YACnE,CAAC;SACF,CAAC;QAEF,IAAM,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC1C,OAAO,cAAM,OAAA,GAAG,CAAC,WAAW,EAAE,EAAjB,CAAiB,CAAC;IACjC,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import type { Observer } from \"./Observable.js\";\nimport { Observable } from \"./Observable.js\";\n\n// Like Observable.prototype.map, except that the mapping function can\n// optionally return a Promise (or be async).\nexport function asyncMap<V, R>(\n  observable: Observable<V>,\n  mapFn: (value: V) => R | PromiseLike<R>,\n  catchFn?: (error: any) => R | PromiseLike<R>\n): Observable<R> {\n  return new Observable<R>((observer) => {\n    let promiseQueue = {\n      // Normally we would initialize promiseQueue to Promise.resolve(), but\n      // in this case, for backwards compatibility, we need to be careful to\n      // invoke the first callback synchronously.\n      then(callback: () => any) {\n        return new Promise((resolve) => resolve(callback()));\n      },\n    } as Promise<void>;\n\n    function makeCallback(\n      examiner: typeof mapFn | typeof catchFn,\n      key: \"next\" | \"error\"\n    ): (arg: any) => void {\n      return (arg) => {\n        if (examiner) {\n          const both = () =>\n            // If the observer is closed, we don't want to continue calling the\n            // mapping function - it's result will be swallowed anyways.\n            observer.closed ?\n              /* will be swallowed */ (0 as any)\n            : examiner(arg);\n\n          promiseQueue = promiseQueue.then(both, both).then(\n            (result) => observer.next(result),\n            (error) => observer.error(error)\n          );\n        } else {\n          observer[key](arg);\n        }\n      };\n    }\n\n    const handler: Observer<V> = {\n      next: makeCallback(mapFn, \"next\"),\n      error: makeCallback(catchFn, \"error\"),\n      complete() {\n        // no need to reassign `promiseQueue`, after `observer.complete`,\n        // the observer will be closed and short-circuit everything anyways\n        /*promiseQueue = */ promiseQueue.then(() => observer.complete());\n      },\n    };\n\n    const sub = observable.subscribe(handler);\n    return () => sub.unsubscribe();\n  });\n}\n"]}