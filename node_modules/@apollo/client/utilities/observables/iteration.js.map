{"version": 3, "file": "iteration.js", "sourceRoot": "", "sources": ["../../../src/utilities/observables/iteration.ts"], "names": [], "mappings": "AAEA,MAAM,UAAU,sBAAsB,CACpC,SAA2B,EAC3B,MAAyB,EACzB,QAAY;IAEZ,2EAA2E;IAC3E,4EAA4E;IAC5E,+CAA+C;IAC/C,IAAM,mBAAmB,GAAkB,EAAE,CAAC;IAC9C,SAAS,CAAC,OAAO,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,CAAC,MAAM,CAAC,IAAI,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,EAA5C,CAA4C,CAAC,CAAC;IACzE,mBAAmB,CAAC,OAAO,CAAC,UAAC,GAAG,IAAK,OAAC,GAAW,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,EAA9B,CAA8B,CAAC,CAAC;AACvE,CAAC", "sourcesContent": ["import type { Observer } from \"./Observable.js\";\n\nexport function iterateObserversSafely<E, A>(\n  observers: Set<Observer<E>>,\n  method: keyof Observer<E>,\n  argument?: A\n) {\n  // In case observers is modified during iteration, we need to commit to the\n  // original elements, which also provides an opportunity to filter them down\n  // to just the observers with the given method.\n  const observersWithMethod: Observer<E>[] = [];\n  observers.forEach((obs) => obs[method] && observersWithMethod.push(obs));\n  observersWithMethod.forEach((obs) => (obs as any)[method](argument));\n}\n"]}