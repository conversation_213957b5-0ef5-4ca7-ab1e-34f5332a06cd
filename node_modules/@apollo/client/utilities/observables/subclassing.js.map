{"version": 3, "file": "subclassing.js", "sourceRoot": "", "sources": ["../../../src/utilities/observables/subclassing.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAC7C,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAEnD,uEAAuE;AACvE,wEAAwE;AACxE,mEAAmE;AACnE,0EAA0E;AAC1E,yEAAyE;AACzE,wEAAwE;AACxE,uEAAuE;AACvE,uEAAuE;AACvE,MAAM,UAAU,qBAAqB,CAEnC,QAAW;IACX,SAAS,GAAG,CAAC,GAAoB;QAC/B,gEAAgE;QAChE,mEAAmE;QACnE,0DAA0D;QAC1D,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,GAAG,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;IAC9D,CAAC;IACD,IAAI,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;QACnC,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACtB,CAAC;IACD,wEAAwE;IACxE,iEAAiE;IACjE,4DAA4D;IAC5D,GAAG,CAAC,WAAW,CAAC,CAAC;IACjB,OAAO,QAAQ,CAAC;AAClB,CAAC", "sourcesContent": ["import { Observable } from \"./Observable.js\";\nimport { canUseSymbol } from \"../common/canUse.js\";\n\n// Generic implementations of Observable.prototype methods like map and\n// filter need to know how to create a new Observable from an Observable\n// subclass (like Concast or ObservableQuery). Those methods assume\n// (perhaps unwisely?) that they can call the subtype's constructor with a\n// Subscriber function, even though the subclass constructor might expect\n// different parameters. Defining this static Symbol.species property on\n// the subclass is a hint to generic Observable code to use the default\n// constructor instead of trying to do `new Subclass(observer => ...)`.\nexport function fixObservableSubclass<\n  S extends new (...args: any[]) => Observable<any>,\n>(subclass: S): S {\n  function set(key: symbol | string) {\n    // Object.defineProperty is necessary because the Symbol.species\n    // property is a getter by default in modern JS environments, so we\n    // can't assign to it with a normal assignment expression.\n    Object.defineProperty(subclass, key, { value: Observable });\n  }\n  if (canUseSymbol && Symbol.species) {\n    set(Symbol.species);\n  }\n  // The \"@@species\" string is used as a fake Symbol.species value in some\n  // polyfill systems (including the SymbolSpecies variable used by\n  // zen-observable), so we should set it as well, to be safe.\n  set(\"@@species\");\n  return subclass;\n}\n"]}