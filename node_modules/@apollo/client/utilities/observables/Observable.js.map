{"version": 3, "file": "Observable.js", "sourceRoot": "", "sources": ["../../../src/utilities/observables/Observable.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C,wEAAwE;AACxE,yDAAyD;AACzD,OAAO,mBAAmB,CAAC;AAI3B,6EAA6E;AAC7E,+EAA+E;AAC/E,6DAA6D;AACrD,IAAA,SAAS,GAAK,UAAU,UAAf,CAAgB;AACjC,IAAM,aAAa,GAAG,cAAwC,CAAC;AAC/D,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC;IAC9B,mBAAmB;IACnB,SAAS,CAAC,aAAa,CAAC,GAAG;QACzB,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;AACJ,CAAC;AAED,OAAO,EAAE,UAAU,EAAE,CAAC", "sourcesContent": ["import type {\n  Observer,\n  Subscription as ObservableSubscription,\n  Subscriber,\n} from \"zen-observable-ts\";\nimport { Observable } from \"zen-observable-ts\";\n\n// This simplified polyfill attempts to follow the ECMAScript Observable\n// proposal (https://github.com/zenparsing/es-observable)\nimport \"symbol-observable\";\n\nexport type { Observer, ObservableSubscription, Subscriber };\n\n// The zen-observable package defines Observable.prototype[Symbol.observable]\n// when Symbol is supported, but RxJS interop depends on also setting this fake\n// '@@observable' string as a polyfill for Symbol.observable.\nconst { prototype } = Observable;\nconst fakeObsSymbol = \"@@observable\" as keyof typeof prototype;\nif (!prototype[fakeObsSymbol]) {\n  // @ts-expect-error\n  prototype[fakeObsSymbol] = function () {\n    return this;\n  };\n}\n\nexport { Observable };\n"]}