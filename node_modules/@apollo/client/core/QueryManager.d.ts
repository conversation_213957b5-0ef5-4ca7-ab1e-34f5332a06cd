import type { DocumentNode } from "graphql";
import type { <PERSON><PERSON>ink, FetchResult } from "../link/core/index.js";
import type { Cache, ApolloCache } from "../cache/index.js";
import { Observable, DocumentTransform } from "../utilities/index.js";
import type { QueryOptions, WatchQueryOptions, SubscriptionOptions, MutationOptions, ErrorPolicy, MutationFetchPolicy, WatchQueryFetchPolicy } from "./watchQueryOptions.js";
import { ObservableQuery } from "./ObservableQuery.js";
import { NetworkStatus } from "./networkStatus.js";
import type { ApolloQueryResult, OperationVariables, MutationUpdaterFunction, OnQueryUpdated, InternalRefetchQueriesInclude, InternalRefetchQueriesOptions, InternalRefetchQueriesMap, DefaultContext } from "./types.js";
import type { LocalState } from "./LocalState.js";
import type { QueryStoreValue } from "./QueryInfo.js";
import { QueryInfo } from "./QueryInfo.js";
interface MutationStoreValue {
    mutation: DocumentNode;
    variables: Record<string, any>;
    loading: boolean;
    error: Error | null;
}
type UpdateQueries<TData> = MutationOptions<TData, any, any>["updateQueries"];
interface TransformCacheEntry {
    hasClientExports: boolean;
    hasForcedResolvers: boolean;
    hasNonreactiveDirective: boolean;
    nonReactiveQuery: DocumentNode;
    clientQuery: DocumentNode | null;
    serverQuery: DocumentNode | null;
    defaultVars: OperationVariables;
    asQuery: DocumentNode;
}
import type { DefaultOptions } from "./ApolloClient.js";
import { Trie } from "@wry/trie";
import type { MaybeMasked } from "../masking/index.js";
interface MaskFragmentOptions<TData> {
    fragment: DocumentNode;
    data: TData;
    fragmentName?: string;
}
interface MaskOperationOptions<TData> {
    document: DocumentNode;
    data: TData;
    id: string;
    fetchPolicy?: WatchQueryFetchPolicy;
}
export interface QueryManagerOptions<TStore> {
    cache: ApolloCache<TStore>;
    link: ApolloLink;
    defaultOptions: DefaultOptions;
    documentTransform: DocumentTransform | null | undefined;
    queryDeduplication: boolean;
    onBroadcast: undefined | (() => void);
    ssrMode: boolean;
    clientAwareness: Record<string, string>;
    localState: LocalState<TStore>;
    assumeImmutableResults: boolean;
    defaultContext: Partial<DefaultContext> | undefined;
    dataMasking: boolean;
}
export declare class QueryManager<TStore> {
    cache: ApolloCache<TStore>;
    link: ApolloLink;
    defaultOptions: DefaultOptions;
    readonly assumeImmutableResults: boolean;
    readonly documentTransform: DocumentTransform;
    readonly ssrMode: boolean;
    readonly defaultContext: Partial<DefaultContext>;
    readonly dataMasking: boolean;
    private queryDeduplication;
    private clientAwareness;
    private localState;
    private onBroadcast?;
    mutationStore?: {
        [mutationId: string]: MutationStoreValue;
    };
    private queries;
    protected fetchCancelFns: Map<string, (error: any) => any>;
    constructor(options: QueryManagerOptions<TStore>);
    /**
     * Call this method to terminate any active query processes, making it safe
     * to dispose of this QueryManager instance.
     */
    stop(): void;
    private cancelPendingFetches;
    mutate<TData, TVariables extends OperationVariables, TContext extends Record<string, any>, TCache extends ApolloCache<any>>({ mutation, variables, optimisticResponse, updateQueries, refetchQueries, awaitRefetchQueries, update: updateWithProxyFn, onQueryUpdated, fetchPolicy, errorPolicy, keepRootFields, context, }: MutationOptions<TData, TVariables, TContext>): Promise<FetchResult<MaybeMasked<TData>>>;
    markMutationResult<TData, TVariables, TContext, TCache extends ApolloCache<any>>(mutation: {
        mutationId: string;
        result: FetchResult<TData>;
        document: DocumentNode;
        variables?: TVariables;
        fetchPolicy?: MutationFetchPolicy;
        errorPolicy: ErrorPolicy;
        context?: TContext;
        updateQueries: UpdateQueries<TData>;
        update?: MutationUpdaterFunction<TData, TVariables, TContext, TCache>;
        awaitRefetchQueries?: boolean;
        refetchQueries?: InternalRefetchQueriesInclude;
        removeOptimistic?: string;
        onQueryUpdated?: OnQueryUpdated<any>;
        keepRootFields?: boolean;
    }, cache?: ApolloCache<TStore>): Promise<FetchResult<TData>>;
    markMutationOptimistic<TData, TVariables, TContext, TCache extends ApolloCache<any>>(optimisticResponse: any, mutation: {
        mutationId: string;
        document: DocumentNode;
        variables?: TVariables;
        fetchPolicy?: MutationFetchPolicy;
        errorPolicy: ErrorPolicy;
        context?: TContext;
        updateQueries: UpdateQueries<TData>;
        update?: MutationUpdaterFunction<TData, TVariables, TContext, TCache>;
        keepRootFields?: boolean;
    }): boolean;
    fetchQuery<TData, TVars extends OperationVariables>(queryId: string, options: WatchQueryOptions<TVars, TData>, networkStatus?: NetworkStatus): Promise<ApolloQueryResult<TData>>;
    getQueryStore(): Record<string, QueryStoreValue>;
    resetErrors(queryId: string): void;
    transform(document: DocumentNode): DocumentNode;
    private transformCache;
    getDocumentInfo(document: DocumentNode): TransformCacheEntry;
    private getVariables;
    watchQuery<T, TVariables extends OperationVariables = OperationVariables>(options: WatchQueryOptions<TVariables, T>): ObservableQuery<T, TVariables>;
    query<TData, TVars extends OperationVariables = OperationVariables>(options: QueryOptions<TVars, TData>, queryId?: string): Promise<ApolloQueryResult<MaybeMasked<TData>>>;
    private queryIdCounter;
    generateQueryId(): string;
    private requestIdCounter;
    generateRequestId(): number;
    private mutationIdCounter;
    generateMutationId(): string;
    stopQueryInStore(queryId: string): void;
    private stopQueryInStoreNoBroadcast;
    clearStore(options?: Cache.ResetOptions): Promise<void>;
    getObservableQueries(include?: InternalRefetchQueriesInclude): Map<string, ObservableQuery<any, OperationVariables>>;
    reFetchObservableQueries(includeStandby?: boolean): Promise<ApolloQueryResult<any>[]>;
    startGraphQLSubscription<T = any>(options: SubscriptionOptions): Observable<FetchResult<T>>;
    stopQuery(queryId: string): void;
    private stopQueryNoBroadcast;
    removeQuery(queryId: string): void;
    broadcastQueries(): void;
    getLocalState(): LocalState<TStore>;
    protected inFlightLinkObservables: Trie<{
        observable?: Observable<FetchResult<any>>;
    }>;
    private getObservableFromLink;
    private getResultsFromLink;
    private fetchConcastWithInfo;
    refetchQueries<TResult>({ updateCache, include, optimistic, removeOptimistic, onQueryUpdated, }: InternalRefetchQueriesOptions<ApolloCache<TStore>, TResult>): InternalRefetchQueriesMap<TResult>;
    private noCacheWarningsByQueryId;
    maskOperation<TData = unknown>(options: MaskOperationOptions<TData>): MaybeMasked<TData>;
    maskFragment<TData = unknown>(options: MaskFragmentOptions<TData>): TData;
    private fetchQueryByPolicy;
    getOrCreateQuery(queryId: string): QueryInfo;
    private prepareContext;
}
export {};
//# sourceMappingURL=QueryManager.d.ts.map