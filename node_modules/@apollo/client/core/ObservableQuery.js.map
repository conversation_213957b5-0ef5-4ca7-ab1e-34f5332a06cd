{"version": 3, "file": "ObservableQuery.js", "sourceRoot": "", "sources": ["../../src/core/ObservableQuery.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,+BAA+B,CAAC;AAE1D,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,OAAO,EAAE,aAAa,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;AAM7E,OAAO,EACL,SAAS,EACT,OAAO,EACP,sBAAsB,EACtB,UAAU,EACV,sBAAsB,EACtB,qBAAqB,EACrB,kBAAkB,EAClB,yBAAyB,GAC1B,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAmBhE,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAGjD,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAExB,IAAA,MAAM,GAAqB,MAAM,OAA3B,EAAE,cAAc,GAAK,MAAM,eAAX,CAAY;AAqB1C;IAGU,mCAAiD;IAsDzD,yBAAY,EAQX;YAPC,YAAY,kBAAA,EACZ,SAAS,eAAA,EACT,OAAO,aAAA;QAHT,iBA6FC;QApFC,IAAI,eAAe,GAAG,eAAe,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;QACpE,QAAA,MAAK,YAAC,UAAC,QAAyD;YAC9D,IAAI,eAAe,EAAE,CAAC;gBACpB,YAAY,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,KAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;gBACrD,eAAe,GAAG,KAAK,CAAC;YAC1B,CAAC;YAED,0EAA0E;YAC1E,8CAA8C;YAC9C,IAAI,CAAC;gBACH,IAAI,WAAW,GAAI,QAAgB,CAAC,aAAa,CAAC,SAAS,CAAC;gBAC5D,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;oBACtC,WAAW,CAAC,KAAK,GAAG,wCAAwC,CAAC;gBAC/D,CAAC;YACH,CAAC;YAAC,WAAM,CAAC,CAAA,CAAC;YAEV,IAAM,KAAK,GAAG,CAAC,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YACnC,KAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAE7B,uCAAuC;YACvC,IAAM,IAAI,GAAG,KAAI,CAAC,IAAI,CAAC;YACvB,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACvB,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,CAAC;iBAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC/B,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAC/D,CAAC;YAED,mEAAmE;YACnE,wBAAwB;YACxB,IAAI,KAAK,EAAE,CAAC;gBACV,+DAA+D;gBAC/D,kEAAkE;gBAClE,iEAAiE;gBACjE,oBAAoB;gBACpB,KAAI,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,cAAO,CAAC,CAAC,CAAC;YACnC,CAAC;YAED,OAAO;gBACL,IAAI,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;oBAC5D,KAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,CAAC;YACH,CAAC,CAAC;QACJ,CAAC,CAAC,SAAC;QAxEG,eAAS,GAAG,IAAI,GAAG,EAExB,CAAC;QACI,mBAAa,GAAG,IAAI,GAAG,EAA0B,CAAC;QAkjClD,WAAK,GAAY,KAAK,CAAC;QA3+B7B,kBAAkB;QAClB,KAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,KAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QAEjC,eAAe;QACf,KAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC9D,KAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,KAAI,CAAC,eAAe,GAAG,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QACvD,KAAI,CAAC,UAAU,GAAG,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAI,CAAC,CAAC;QAG3C,IAAA,KACE,YAAY,CAAC,cAAc,WADuC,EAApE,qBAAkE,EAAE,KAAA,EAAtD,mBAA+C,EAAlC,kBAAkB,mBAAG,aAAa,KAAO,CACtC;QAG9B,IAAA,KAKE,OAAO,YALuB,EAAhC,WAAW,mBAAG,kBAAkB,KAAA;QAChC,gEAAgE;QAChE,KAGE,OAAO,mBADR;QAHD,gEAAgE;QAChE,kBAAkB,mBAAG,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,CACpE,WAAW,CACZ,KAAA,CACS;QAEZ,KAAI,CAAC,OAAO,yBACP,OAAO;YAEV,yEAAyE;YACzE,uEAAuE;YACvE,sEAAsE;YACtE,kBAAkB,oBAAA;YAElB,sEAAsE;YACtE,6CAA6C;YAC7C,WAAW,aAAA,GACZ,CAAC;QAEF,KAAI,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,IAAI,YAAY,CAAC,eAAe,EAAE,CAAC;QAEnE,IAAM,KAAK,GAAG,sBAAsB,CAAC,KAAI,CAAC,KAAK,CAAC,CAAC;QACjD,KAAI,CAAC,SAAS,GAAG,KAAK,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;;IAC3D,CAAC;IAjID,sBAAW,kCAAK;QAJhB,6EAA6E;QAC7E,0EAA0E;QAC1E,8EAA8E;QAC9E,oCAAoC;aACpC;YACE,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;QAC9C,CAAC;;;OAAA;IAOD,sBAAW,sCAAS;QALpB,+DAA+D;QAC/D,2BAA2B;QAC3B;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAChC,CAAC;;;OAAA;IAwHM,gCAAM,GAAb;QAAA,iBAgCC;QA/BC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YACjC,sEAAsE;YACtE,+EAA+E;YAC/E,kFAAkF;YAClF,IAAM,QAAQ,GAAoD;gBAChE,IAAI,EAAE,UAAC,MAAM;oBACX,OAAO,CAAC,MAAM,CAAC,CAAC;oBAEhB,0DAA0D;oBAC1D,yBAAyB;oBACzB,EAAE;oBACF,4DAA4D;oBAC5D,0DAA0D;oBAC1D,qEAAqE;oBACrE,sEAAsE;oBACtE,qEAAqE;oBACrE,sEAAsE;oBACtE,mBAAmB;oBACnB,KAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;oBAChC,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;wBACzB,KAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAI,CAAC,OAAO,CAAC,CAAC;oBAC9C,CAAC;oBAED,UAAU,CAAC;wBACT,YAAY,CAAC,WAAW,EAAE,CAAC;oBAC7B,CAAC,EAAE,CAAC,CAAC,CAAC;gBACR,CAAC;gBACD,KAAK,EAAE,MAAM;aACd,CAAC;YACF,IAAM,YAAY,GAAG,KAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB;IACT,mCAAS,GAAhB;QACE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;IAC7B,CAAC;IAEO,8CAAoB,GAA5B,UACE,gBAAuB;QAAvB,iCAAA,EAAA,uBAAuB;QAEvB,qEAAqE;QACrE,IAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE5C,IAAM,aAAa,GACjB,IAAI,CAAC,SAAS,CAAC,aAAa;YAC5B,CAAC,UAAU,IAAI,UAAU,CAAC,aAAa,CAAC;YACxC,aAAa,CAAC,KAAK,CAAC;QAEtB,IAAM,MAAM,GAAG,sBACV,UAAU,KACb,OAAO,EAAE,wBAAwB,CAAC,aAAa,CAAC,EAChD,aAAa,eAAA,GACc,CAAC;QAEtB,IAAA,KAAgC,IAAI,CAAC,OAAO,YAAjB,EAA3B,WAAW,mBAAG,aAAa,KAAA,CAAkB;QACrD;QACE,wEAAwE;QACxE,8CAA8C;QAC9C,gBAAgB,CAAC,WAAW,CAAC;YAC7B,oEAAoE;YACpE,sEAAsE;YACtE,yEAAyE;YACzE,kCAAkC;YAClC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAChE,CAAC;YACD,gBAAgB;QAClB,CAAC;aAAM,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACjC,yDAAyD;YACzD,8DAA8D;YAC9D,0BAA0B;YAC1B,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC;QAClC,CAAC;aAAM,CAAC;YACN,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YAEtC,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;gBACpD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YAC5B,CAAC;YAED,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC;gBAC3B,MAAM,CAAC,IAAI,GAAG,KAAK,CAAQ,CAAC;YAC9B,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,0EAA0E;gBAC1E,+BAA+B;gBAC/B,OAAO,MAAM,CAAC,OAAO,CAAC;gBAEtB,8DAA8D;gBAC9D,wEAAwE;gBACxE,yDAAyD;gBACzD,IACE,IAAI,CAAC,QAAQ;oBACb,MAAM,CAAC,aAAa,KAAK,aAAa,CAAC,OAAO;oBAC9C,CAAC,WAAW,KAAK,aAAa,IAAI,WAAW,KAAK,YAAY,CAAC,EAC/D,CAAC;oBACD,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC;oBAC3C,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;gBACzB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YACxB,CAAC;YAED,0EAA0E;YAC1E,iEAAiE;YACjE,kEAAkE;YAClE,8CAA8C;YAC9C,IACE,MAAM,CAAC,aAAa,KAAK,aAAa,CAAC,KAAK;gBAC5C,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,MAAM,CAAC,EAC/B,CAAC;gBACD,MAAM,CAAC,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC;YAC7C,CAAC;YAED,IACE,OAAO;gBACP,CAAC,IAAI,CAAC,QAAQ;gBACd,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc;gBAC5B,CAAC,MAAM,CAAC,OAAO;gBACf,CAAC,MAAM,CAAC,IAAI;gBACZ,CAAC,MAAM,CAAC,KAAK,EACb,CAAC;gBACD,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QAED,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEM,0CAAgB,GAAvB,UACE,gBAAuB;QAAvB,iCAAA,EAAA,uBAAuB;QAEvB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,4EAA4E;IAC5E,kBAAkB;IACX,mDAAyB,GAAhC,UACE,SAAmC,EACnC,SAAsB;QAEtB,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACnE,IAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;QAClD,IAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;QAEvE,IAAM,iBAAiB,GACrB,WAAW,IAAI,YAAY,CAAC,uBAAuB,CAAC,CAAC;YACnD,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC;YACnE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAExC,OAAO,CACL,iBAAiB,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAC3E,CAAC;IACJ,CAAC;IAEO,iCAAO,GAAf,UACE,GAAM,EACN,kBAA4B;QAE5B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IACE,IAAI;YACJ,IAAI,CAAC,GAAG,CAAC;YACT,CAAC,CAAC,kBAAkB,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAC9D,CAAC;YACD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IAEM,uCAAa,GAApB,UACE,kBAA4B;QAE5B,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;IACpD,CAAC;IAEM,sCAAY,GAAnB,UAAoB,kBAA4B;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;IACnD,CAAC;IAEM,0CAAgB,GAAvB;QACE,OAAO,IAAI,CAAC,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC1B,CAAC;IAEM,+CAAqB,GAA5B;QACE,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;;;;;OAMG;IACI,iCAAO,GAAd,UACE,SAA+B;;QAE/B,IAAM,gBAAgB,GAAkD;YACtE,wCAAwC;YACxC,YAAY,EAAE,CAAC;SAChB,CAAC;QAEF,8DAA8D;QAC9D,mEAAmE;QACnE,8DAA8D;QACtD,IAAA,WAAW,GAAK,IAAI,CAAC,OAAO,YAAjB,CAAkB;QACrC,IAAI,WAAW,KAAK,UAAU,EAAE,CAAC;YAC/B,gBAAgB,CAAC,WAAW,GAAG,UAAU,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,gBAAgB,CAAC,WAAW,GAAG,cAAc,CAAC;QAChD,CAAC;QAED,IAAI,OAAO,IAAI,SAAS,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,CAAC;YACxE,IAAM,QAAQ,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChD,IAAM,IAAI,GAAG,QAAQ,CAAC,mBAAmB,CAAC;YAC1C,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,KAAK,WAAW,EAArC,CAAqC,CAAC,EAAE,CAAC;gBACtE,SAAS,CAAC,IAAI,CACZ,4JACiE,EACjE,SAAS,EACT,CAAA,MAAA,QAAQ,CAAC,IAAI,0CAAE,KAAK,KAAI,QAAQ,CACjC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;YAC3D,iDAAiD;YACjD,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,sBACjD,IAAI,CAAC,OAAO,CAAC,SAAS,GACtB,SAAS,CACC,CAAC;QAClB,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACI,mCAAS,GAAhB,UAIE,gBAQC;QAZH,iBAqKC;QAvJC,IAAM,eAAe,GAAG,sBACnB,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,yCAEzC,IAAI,CAAC,OAAO,KACf,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,KACtB,gBAAgB,KACnB,SAAS,wBACJ,IAAI,CAAC,OAAO,CAAC,SAAS,GACtB,gBAAgB,CAAC,SAAS,KAGlC,CAAC;YACF,iEAAiE;YACjE,kEAAkE;YAClE,2DAA2D;YAC3D,mEAAmE;YACnE,sCAAsC;YACtC,WAAW,EAAE,UAAU,GACqB,CAAC;QAE/C,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAEtE,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QAEhD,wEAAwE;QACxE,qEAAqE;QACrE,0EAA0E;QAC1E,2EAA2E;QAC3E,4EAA4E;QAC5E,IAAI,CAAC,SAAS;YACZ,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACtB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC5C,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC;QAE1B,wDAAwD;QACxD,oDAAoD;QAC5C,IAAA,SAAS,GAAK,IAAI,UAAT,CAAU;QAC3B,IAAM,qBAAqB,GAAG,SAAS,CAAC,aAAa,CAAC;QACtD,SAAS,CAAC,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC;QAClD,IAAI,eAAe,CAAC,2BAA2B,EAAE,CAAC;YAChD,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;QAED,IAAM,eAAe,GAAG,IAAI,GAAG,EAAgB,CAAC;QAEhD,IAAM,WAAW,GAAG,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,WAAW,CAAC;QAClD,IAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,UAAU,CAAC;QAEzD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,SAAS,CACP,WAAW,EACX,mGAAmG,CACpG,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC,YAAY;aACrB,UAAU,CAAC,GAAG,EAAE,eAAe,EAAE,aAAa,CAAC,SAAS,CAAC;aACzD,IAAI,CAAC,UAAC,eAAe;YACpB,KAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAEnC,IAAI,SAAS,CAAC,aAAa,KAAK,aAAa,CAAC,SAAS,EAAE,CAAC;gBACxD,SAAS,CAAC,aAAa,GAAG,qBAAqB,CAAC;YAClD,CAAC;YAED,IAAI,QAAQ,EAAE,CAAC;gBACb,wEAAwE;gBACxE,wEAAwE;gBACxE,kEAAkE;gBAClE,sEAAsE;gBACtE,wDAAwD;gBACxD,KAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC;oBAC5B,MAAM,EAAE,UAAC,KAAK;wBACJ,IAAA,WAAW,GAAK,gBAAgB,YAArB,CAAsB;wBACzC,IAAI,WAAW,EAAE,CAAC;4BAChB,KAAK,CAAC,WAAW,CACf;gCACE,KAAK,EAAE,KAAI,CAAC,KAAK;gCACjB,SAAS,EAAE,KAAI,CAAC,SAAS;gCACzB,iBAAiB,EAAE,IAAI;gCACvB,UAAU,EAAE,KAAK;6BAClB,EACD,UAAC,QAAQ;gCACP,OAAA,WAAW,CAAC,QAAgB,EAAE;oCAC5B,eAAe,EAAE,eAAe,CAAC,IAAW;oCAC5C,SAAS,EAAE,eAAe,CAAC,SAAuB;iCACnD,CAAC;4BAHF,CAGE,CACL,CAAC;wBACJ,CAAC;6BAAM,CAAC;4BACN,iEAAiE;4BACjE,+DAA+D;4BAC/D,iEAAiE;4BACjE,8DAA8D;4BAC9D,0DAA0D;4BAC1D,KAAK,CAAC,UAAU,CAAC;gCACf,KAAK,EAAE,eAAe,CAAC,KAAK;gCAC5B,SAAS,EAAE,eAAe,CAAC,SAAS;gCACpC,IAAI,EAAE,eAAe,CAAC,IAA4B;6BACnD,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;oBAED,cAAc,EAAE,UAAC,KAAK;wBACpB,kEAAkE;wBAClE,+CAA+C;wBAC/C,eAAe,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACnC,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,0DAA0D;gBAC1D,qEAAqE;gBACrE,mEAAmE;gBACnE,qEAAqE;gBACrE,mEAAmE;gBACnE,2CAA2C;gBAC3C,EAAE;gBACF,6DAA6D;gBAC7D,mEAAmE;gBACnE,qEAAqE;gBACrE,+DAA+D;gBAC/D,+DAA+D;gBAC/D,kEAAkE;gBAClE,6BAA6B;gBAC7B,IAAM,UAAU,GAAG,KAAI,CAAC,OAAO,CAAC,QAAQ,CAAE,CAAC;gBAC3C,IAAM,IAAI,GAAG,WAAY,CAAC,UAAU,CAAC,IAAuB,EAAE;oBAC5D,eAAe,EAAE,eAAe,CAAC,IAA4B;oBAC7D,SAAS,EAAE,eAAe,CAAC,SAAuB;iBACnD,CAAC,CAAC;gBAEH,KAAI,CAAC,YAAY,uBAEV,UAAU,KACb,aAAa,EAAE,qBAAsB,EACrC,OAAO,EAAE,wBAAwB,CAAC,qBAAqB,CAAC,EACxD,IAAI,EAAE,IAAa,KAErB,KAAI,CAAC,SAAS,CACf,CAAC;YACJ,CAAC;YAED,OAAO,KAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;QAC1C,CAAC,CAAC;aACD,OAAO,CAAC;YACP,8DAA8D;YAC9D,sEAAsE;YACtE,uEAAuE;YACvE,wEAAwE;YACxE,kCAAkC;YAClC,IAAI,QAAQ,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACjD,KAAI,CAAC,mBAAmB,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC,CAAC;IACP,CAAC;IAED,wEAAwE;IACxE,0FAA0F;IAC1F,qGAAqG;IACrG;;;;OAIG;IACI,yCAAe,GAAtB,UAIE,OAKC;QATH,iBA6CC;QAlCC,IAAM,YAAY,GAAG,IAAI,CAAC,YAAY;aACnC,wBAAwB,CAAC;YACxB,KAAK,EAAE,OAAO,CAAC,QAAQ;YACvB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC;aACD,SAAS,CAAC;YACT,IAAI,EAAE,UAAC,gBAAuD;gBACpD,IAAA,WAAW,GAAK,OAAO,YAAZ,CAAa;gBAChC,IAAI,WAAW,EAAE,CAAC;oBAChB,KAAI,CAAC,WAAW,CAAC,UAAC,QAAQ,EAAE,aAAa;wBACvC,OAAA,WAAW,CAAC,QAAQ,aAClB,gBAAgB,kBAAA,IACb,aAAa,EAChB;oBAHF,CAGE,CACH,CAAC;gBACJ,CAAC;YACH,CAAC;YACD,KAAK,EAAE,UAAC,GAAQ;gBACd,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;oBACpB,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;oBACrB,OAAO;gBACT,CAAC;gBACD,SAAS,CAAC,KAAK,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAAC;YAC/D,CAAC;SACF,CAAC,CAAC;QAEL,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAErC,OAAO;YACL,IAAI,KAAI,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC5C,YAAY,CAAC,WAAW,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAEM,oCAAU,GAAjB,UACE,UAAyD;QAEzD,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;IACpC,CAAC;IAEM,0CAAgB,GAAvB,UACE,UAAyD;QAEzD,IAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;QAC9D,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IACtC,CAAC;IAED;;;;;;;;;;;;;;;;;OAiBG;IACI,sCAAY,GAAnB,UACE,SAAqB;QAErB,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;YACrC,yEAAyE;YACzE,wEAAwE;YACxE,uDAAuD;YACvD,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;QACjE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;QAEnC,oBAAoB;QACpB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,CACnB;YACE,mDAAmD;YACnD,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB;YAC5C,SAAS,WAAA;SACV,EACD,aAAa,CAAC,YAAY,CAC3B,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,qCAAW,GAAlB,UAAmB,KAA0C;QACnD,IAAA,YAAY,GAAK,IAAI,aAAT,CAAU;QACxB,IAAA,KAAuB,YAAY,CAAC,KAAK,CAAC,IAAI,CAAQ;YAC1D,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;YACzB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,iBAAiB,EAAE,IAAI;YACvB,UAAU,EAAE,KAAK;SAClB,CAAC,EALM,MAAM,YAAA,EAAE,QAAQ,cAKtB,CAAC;QAEH,IAAM,SAAS,GAAG,KAAK,CACrB,MAA0B,EAC1B;YACE,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,CAAC,CAAC,QAAQ;YACpB,YAAY,EAAE,MAAM;SACoB,CAC3C,CAAC;QAEF,IAAI,SAAS,EAAE,CAAC;YACd,YAAY,CAAC,KAAK,CAAC,UAAU,CAAC;gBAC5B,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;gBACzB,IAAI,EAAE,SAAS;gBACf,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAC,CAAC;YAEH,YAAY,CAAC,gBAAgB,EAAE,CAAC;QAClC,CAAC;IACH,CAAC;IAED;;OAEG;IACI,sCAAY,GAAnB,UAAoB,YAAoB;QACtC,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;QACzC,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACI,qCAAW,GAAlB;QACE,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC;QAC9B,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,mEAAmE;IAC3D,8CAAoB,GAA5B,UACE,MAA2D;IAC3D,uEAAuE;IACvE,4EAA4E;IAC5E,2EAA2E;IAC3E,iEAAiE;IACjE,OAA6C;QAE7C,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YACpB,IAAA,KACN,OAAO,YAD0B,EAA3B,WAAW,mBAAG,aAAa,KAAA,EAAE,KACnC,OAAO,mBAD4D,EAAhC,kBAAkB,mBAAG,WAAW,KAAA,CAC3D;YAEV,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;gBAC9B,qDAAqD;YACvD,CAAC;iBAAM,IAAI,OAAO,OAAO,CAAC,eAAe,KAAK,UAAU,EAAE,CAAC;gBACzD,sEAAsE;gBACtE,sEAAsE;gBACtE,mEAAmE;gBACnE,oEAAoE;gBACpE,qEAAqE;gBACrE,sEAAsE;gBACtE,sEAAsE;gBACtE,uEAAuE;gBACvE,wEAAwE;gBACxE,6CAA6C;gBAC7C,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE;oBACzD,MAAM,QAAA;oBACN,OAAO,SAAA;oBACP,UAAU,EAAE,IAAI;oBAChB,kBAAkB,oBAAA;iBACnB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,MAAM,KAAK,mBAAmB,EAAE,CAAC;gBAC1C,OAAO,CAAC,WAAW,GAAG,kBAAkB,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC;YAChD,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC,WAAW,CAAC;IAC7B,CAAC;IAEO,+BAAK,GAAb,UACE,OAA6C,EAC7C,gBAAgC,EAChC,KAAoB;QAEpB,wEAAwE;QACxE,2CAA2C;QAC3C,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACnE,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACnC,OAAO,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAC9C,SAAS,EACT,OAAO,EACP,gBAAgB,EAChB,KAAK,CACN,CAAC;IACJ,CAAC;IAED,8DAA8D;IACtD,uCAAa,GAArB;QAAA,iBAiEC;QAhEC,4BAA4B;QAC5B,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;YAC9B,OAAO;QACT,CAAC;QAEK,IAAA,KAGF,IAAI,EAFN,WAAW,iBAAA,EACA,YAAY,0BACjB,CAAC;QAET,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;YAC1C,IAAI,WAAW,EAAE,CAAC;gBAChB,YAAY,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAClC,OAAO,IAAI,CAAC,WAAW,CAAC;YAC1B,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,WAAW,IAAI,WAAW,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YACzD,OAAO;QACT,CAAC;QAED,SAAS,CACP,YAAY,EACZ,gEAAgE,CACjE,CAAC;QAEF,IAAM,IAAI,GAAG,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,EAAS,CAAC,CAAC;QAC3D,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC;QAE7B,IAAM,UAAU,GAAG;;YACjB,IAAI,KAAI,CAAC,WAAW,EAAE,CAAC;gBACrB,IACE,CAAC,wBAAwB,CAAC,KAAI,CAAC,SAAS,CAAC,aAAa,CAAC;oBACvD,CAAC,CAAA,MAAA,MAAA,KAAI,CAAC,OAAO,EAAC,eAAe,kDAAI,CAAA,EACjC,CAAC;oBACD,KAAI,CAAC,SAAS,CACZ;wBACE,4EAA4E;wBAC5E,kFAAkF;wBAClF,iFAAiF;wBACjF,2CAA2C;wBAC3C,WAAW,EACT,KAAI,CAAC,OAAO,CAAC,kBAAkB,KAAK,UAAU,CAAC,CAAC;4BAC9C,UAAU;4BACZ,CAAC,CAAC,cAAc;qBACnB,EACD,aAAa,CAAC,IAAI,CACnB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACrB,CAAC;qBAAM,CAAC;oBACN,IAAI,EAAE,CAAC;gBACT,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEF,IAAM,IAAI,GAAG;YACX,IAAM,IAAI,GAAG,KAAI,CAAC,WAAW,CAAC;YAC9B,IAAI,IAAI,EAAE,CAAC;gBACT,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC3B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvD,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAEO,0CAAgB,GAAxB,UACE,SAAmC,EACnC,SAA0B;QAA1B,0BAAA,EAAA,YAAY,IAAI,CAAC,SAAS;QAE1B,IAAI,KAAK,GAA4B,IAAI,CAAC,YAAY,EAAE,CAAC;QACzD,8DAA8D;QAC9D,IAAI,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACjE,KAAK,GAAG,KAAK,CAAC,CAAC;QACjB,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,IAAI,cACf,MAAM,EACJ,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;gBACxC,SAAS;gBACX,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,EACxB,SAAS,WAAA,IACN,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAC9B,CAAC,CAAC;IACL,CAAC;IAEM,4CAAkB,GAAzB,UACE,UAA0D,EAC1D,gBAAgC;QAFlC,iBA4GC;QAxGC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAM,oBAAoB;QACxB,0EAA0E;QAC1E,qEAAqE;QACrE,4BAA4B;QAC5B,gBAAgB,KAAK,aAAa,CAAC,OAAO;YAC1C,yEAAyE;YACzE,2DAA2D;YAC3D,gBAAgB,KAAK,aAAa,CAAC,SAAS;YAC5C,wEAAwE;YACxE,uFAAuF;YACvF,gBAAgB,KAAK,aAAa,CAAC,IAAI,CAAC;QAE1C,qEAAqE;QACrE,IAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QAC5C,IAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;QAEhD,IAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE,CAAC,CAAC;QAC9D,IAAM,OAAO,GACX,oBAAoB,CAAC,CAAC;YACpB,oEAAoE;YACpE,6DAA6D;YAC7D,aAAa;YACf,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;QAExC,iEAAiE;QACjE,4EAA4E;QAC5E,0EAA0E;QAC1E,qDAAqD;QACrD,IAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAEpD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC1B,wEAAwE;YACxE,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,yEAAyE;YACzE,uDAAuD;YACvD,IACE,UAAU;gBACV,UAAU,CAAC,SAAS;gBACpB,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC;gBAC1C,+DAA+D;gBAC/D,OAAO,CAAC,WAAW,KAAK,SAAS;gBACjC,wEAAwE;gBACxE,qEAAqE;gBACrE,CAAC,OAAO,CAAC,WAAW,KAAK,cAAc;oBACrC,iEAAiE;oBACjE,yDAAyD;oBACzD,OAAO,OAAO,CAAC,eAAe,KAAK,UAAU,CAAC,EAChD,CAAC;gBACD,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBACxD,IAAI,gBAAgB,KAAK,KAAK,CAAC,EAAE,CAAC;oBAChC,gBAAgB,GAAG,aAAa,CAAC,YAAY,CAAC;gBAChD,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,gBAAgB,KAArB,IAAI,CAAC,gBAAgB,GAAK,gBAAgB,CAAC,OAAO,CAAC,WAAW,CAAC,EAAC;QAChE,IAAM,yBAAyB,GAAG;YAChC,IAAI,KAAI,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;gBAC7B,KAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAChC,CAAC;QACH,CAAC,CAAC;QAEF,IAAM,SAAS,GAAG,OAAO,CAAC,SAAS,iBAAS,OAAO,CAAC,SAAS,CAAE,CAAC;QAC1D,IAAA,KAAwB,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,gBAAgB,EAAE,KAAK,CAAC,EAAlE,OAAO,aAAA,EAAE,QAAQ,cAAiD,CAAC;QAC3E,IAAM,QAAQ,GAAuC;YACnD,IAAI,EAAE,UAAC,MAAM;gBACX,IAAI,KAAK,CAAC,KAAI,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;oBACrC,yBAAyB,EAAE,CAAC;oBAC5B,KAAI,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;gBACvC,CAAC;YACH,CAAC;YACD,KAAK,EAAE,UAAC,KAAK;gBACX,IAAI,KAAK,CAAC,KAAI,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;oBACrC,oFAAoF;oBACpF,oFAAoF;oBACpF,sBAAsB;oBACtB,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;wBAC1B,KAAK,GAAG,IAAI,WAAW,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,CAAC;oBACnD,CAAC;oBACD,yBAAyB,EAAE,CAAC;oBAC5B,KAAI,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC;SACF,CAAC;QAEF,IAAI,CAAC,oBAAoB,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;YACzD,qEAAqE;YACrE,4DAA4D;YAC5D,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC7C,CAAC;YAED,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC3B,CAAC;QAED,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE9B,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,mCAAS,GAAhB,UACE,UAA0D,EAC1D,gBAAgC;QAEhC,OAAO,yBAAyB,CAC9B,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC,OAAO,CAAC,IAAI,CAChE,IAAI,CAAC,UAAkB,CACxB,CACF,CAAC;IACJ,CAAC;IAYM,+CAAqB,GAA5B;QAA6B,cAA0B;aAA1B,UAA0B,EAA1B,qBAA0B,EAA1B,IAA0B;YAA1B,yBAA0B;;QACrD,4EAA4E;QAC5E,kEAAkE;QAClE,6DAA6D;QAC7D,sEAAsE;QACtE,sEAAsE;QACtE,0CAA0C;QAC1C,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,OAAd,IAAI,EAAc,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,0EAA0E;IAC1E,uCAAuC;IAC/B,iCAAO,GAAf;QACE,IAAI,CAAC,YAAY;QACf,mEAAmE;QACnE,iEAAiE;QACjE,6DAA6D;QAC7D,qBAAqB;QACrB,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,EAChC,IAAI,CAAC,SAAS,CACf,CAAC;IACJ,CAAC;IAEO,sCAAY,GAApB,UACE,MAAgC,EAChC,SAAiC;QAEjC,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACtC,IAAM,WAAW,GAAG,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QACtE,4EAA4E;QAC5E,4EAA4E;QAC5E,mEAAmE;QACnE,yEAAyE;QACzE,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACnE,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAC3C,CAAC;QACD,IAAI,SAAS,IAAI,WAAW,EAAE,CAAC;YAC7B,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAEO,qCAAW,GAAnB,UAAoB,KAAkB,EAAE,SAAiC;QACvE,sEAAsE;QACtE,uEAAuE;QACvE,IAAM,WAAW,GAAG,sBACf,IAAI,CAAC,aAAa,EAAE,KACvB,KAAK,OAAA,EACL,MAAM,EAAE,KAAK,CAAC,aAAa,EAC3B,aAAa,EAAE,aAAa,CAAC,KAAK,EAClC,OAAO,EAAE,KAAK,GACa,CAAC;QAE9B,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAE9C,sBAAsB,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,IAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;IAC9E,CAAC;IAEM,sCAAY,GAAnB;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC;IACjC,CAAC;IAEO,uCAAa,GAArB;QACE,IAAI,IAAI,CAAC,UAAU;YAAE,OAAO;QAC5B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC,OAAO,CAAC;YACpB,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,wCAAwC;QACxC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,GAAG,IAAK,OAAA,GAAG,CAAC,WAAW,EAAE,EAAjB,CAAiB,CAAC,CAAC;QACvD,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;IACzB,CAAC;IAEO,2CAAiB,GAAzB,UAA0B,QAAsB;QAC9C,OAAO,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAEO,oCAAU,GAAlB,UACE,MAA4B;QAE5B,OAAO,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,uBAE1B,MAAM,KACT,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;gBACpC,QAAQ,EAAE,IAAI,CAAC,KAAK;gBACpB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;gBACrC,EAAE,EAAE,IAAI,CAAC,OAAO;aACjB,CAAC,IAEN,CAAC,CAAC,MAAM,CAAC;IACb,CAAC;IAMD,gBAAgB;IACN,4CAAkB,GAA5B;QACE,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAEO,6CAAmB,GAA3B;QACE,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACjC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAED,gBAAgB;IACN,wCAAc,GAAxB;QAAA,iBAMC;QALC,IAAI,IAAI,CAAC,KAAK;YAAE,OAAO;QACvB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,cAAM,OAAA,KAAI,CAAC,MAAM,EAAE,EAAb,CAAa,EAAE,CAAC,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,gBAAgB;IACN,gCAAM,GAAhB;QACE,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;YACf,IACE,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,YAAY;gBACxC,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,mBAAmB;gBAC/C,CAAC,wBAAwB,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,EACvD,CAAC;gBACD,IAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;gBACtC,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;oBACnC,gEAAgE;oBAChE,iEAAiE;oBACjE,mEAAmE;oBACnE,gEAAgE;oBAChE,gCAAgC;oBAChC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC;qBAAM,CAAC;oBACN,kEAAkE;oBAClE,qEAAqE;oBACrE,sEAAsE;oBACtE,oEAAoE;oBACpE,oEAAoE;oBACpE,kEAAkE;oBAClE,mEAAmE;oBACnE,qEAAqE;oBACrE,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAED,0EAA0E;IAC1E,gFAAgF;IAChF,4EAA4E;IAC5E,uEAAuE;IACvE,0EAA0E;IAC1E,6EAA6E;IAC7E,gEAAgE;IACxD,6CAAmB,GAA3B;QACQ,IAAA,KAAmC,IAAI,CAAC,OAAO,EAA7C,WAAW,iBAAA,EAAE,eAAe,qBAAiB,CAAC;QAEtD,IAAI,WAAW,KAAK,mBAAmB,IAAI,WAAW,KAAK,cAAc,EAAE,CAAC;YAC1E,OAAO,IAAI,CAAC,SAAS,CAAC;gBACpB,WAAW,EAAE,aAAa;gBAC1B,yEAAyE;gBACzE,uEAAuE;gBACvE,eAAe,YAEb,kBAAyC,EACzC,OAAkD;oBAElD,uEAAuE;oBACvE,+CAA+C;oBAC/C,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;oBACvC,kEAAkE;oBAClE,sCAAsC;oBACtC,IAAI,OAAO,IAAI,CAAC,eAAe,KAAK,UAAU,EAAE,CAAC;wBAC/C,OAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;oBAC3D,CAAC;oBACD,8DAA8D;oBAC9D,OAAO,WAAY,CAAC;gBACtB,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC1B,CAAC;IArrCD;;;;;;OAMG;IACY,kCAAkB,GAAG,IAAI,IAAI,EAAW,AAAtB,CAAuB;IA+qC1D,sBAAC;CAAA,AA1rCD,CAGU,UAAU,GAurCnB;SA1rCY,eAAe;AA4rC5B,oEAAoE;AACpE,6CAA6C;AAC7C,qBAAqB,CAAC,eAAe,CAAC,CAAC;AAEvC,SAAS,wCAAwC,CAAC,KAAkB;IAClE,SAAS,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AACjE,CAAC;AAED,MAAM,UAAU,qBAAqB,CACnC,OAAsD;IAEtD,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;QACvB,SAAS,CAAC,KAAK,CAAC,iCAAiC,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;AACH,CAAC;AAED,SAAS,gBAAgB,CACvB,WAAmC,CAAC,4CAA4C;IAEhF,OAAO,CACL,WAAW,KAAK,cAAc;QAC9B,WAAW,KAAK,UAAU;QAC1B,WAAW,KAAK,SAAS,CAC1B,CAAC;AACJ,CAAC", "sourcesContent": ["import { invariant } from \"../utilities/globals/index.js\";\nimport type { DocumentNode } from \"graphql\";\nimport { equal } from \"@wry/equality\";\n\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nimport type {\n  Concast,\n  Observer,\n  ObservableSubscription,\n} from \"../utilities/index.js\";\nimport {\n  cloneDeep,\n  compact,\n  getOperationDefinition,\n  Observable,\n  iterateObserversSafely,\n  fixObservableSubclass,\n  getQueryDefinition,\n  preventUnhandledRejection,\n} from \"../utilities/index.js\";\nimport { ApolloError, isApolloError } from \"../errors/index.js\";\nimport type { QueryManager } from \"./QueryManager.js\";\nimport type {\n  ApolloQueryResult,\n  OperationVariables,\n  TypedDocumentNode,\n} from \"./types.js\";\nimport type {\n  WatchQueryOptions,\n  FetchMoreQueryOptions,\n  SubscribeToMoreOptions,\n  NextFetchPolicyContext,\n  WatchQueryFetchPolicy,\n  UpdateQueryMapFn,\n  UpdateQueryOptions,\n} from \"./watchQueryOptions.js\";\nimport type { QueryInfo } from \"./QueryInfo.js\";\nimport type { MissingFieldError } from \"../cache/index.js\";\nimport type { MissingTree } from \"../cache/core/types/common.js\";\nimport { equalByQuery } from \"./equalByQuery.js\";\nimport type { TODO } from \"../utilities/types/TODO.js\";\nimport type { MaybeMasked, Unmasked } from \"../masking/index.js\";\nimport { Slot } from \"optimism\";\n\nconst { assign, hasOwnProperty } = Object;\n\nexport interface FetchMoreOptions<\n  TData = any,\n  TVariables = OperationVariables,\n> {\n  updateQuery?: (\n    previousQueryResult: TData,\n    options: {\n      fetchMoreResult?: TData;\n      variables?: TVariables;\n    }\n  ) => TData;\n}\n\ninterface Last<TData, TVariables> {\n  result: ApolloQueryResult<TData>;\n  variables?: TVariables;\n  error?: ApolloError;\n}\n\nexport class ObservableQuery<\n  TData = any,\n  TVariables extends OperationVariables = OperationVariables,\n> extends Observable<ApolloQueryResult<MaybeMasked<TData>>> {\n  /**\n   * @internal\n   * A slot used by the `useQuery` hook to indicate that `client.watchQuery`\n   * should not register the query immediately, but instead wait for the query to\n   * be started registered with the `QueryManager` when `useSyncExternalStore`\n   * actively subscribes to it.\n   */\n  private static inactiveOnCreation = new Slot<boolean>();\n\n  public readonly options: WatchQueryOptions<TVariables, TData>;\n  public readonly queryId: string;\n  public readonly queryName?: string;\n\n  // The `query` computed property will always reflect the document transformed\n  // by the last run query. `this.options.query` will always reflect the raw\n  // untransformed query to ensure document transforms with runtime conditionals\n  // are run on the original document.\n  public get query(): TypedDocumentNode<TData, TVariables> {\n    return this.lastQuery || this.options.query;\n  }\n\n  // Computed shorthand for this.options.variables, preserved for\n  // backwards compatibility.\n  /**\n   * An object containing the variables that were provided for the query.\n   */\n  public get variables(): TVariables | undefined {\n    return this.options.variables;\n  }\n\n  private isTornDown: boolean;\n  private queryManager: QueryManager<any>;\n  private observers = new Set<\n    Observer<ApolloQueryResult<MaybeMasked<TData>>>\n  >();\n  private subscriptions = new Set<ObservableSubscription>();\n\n  private waitForOwnResult: boolean;\n  private last?: Last<TData, TVariables>;\n  private lastQuery?: DocumentNode;\n\n  private queryInfo: QueryInfo;\n\n  // When this.concast is defined, this.observer is the Observer currently\n  // subscribed to that Concast.\n  private concast?: Concast<ApolloQueryResult<TData>>;\n  private observer?: Observer<ApolloQueryResult<TData>>;\n\n  private pollingInfo?: {\n    interval: number;\n    timeout: ReturnType<typeof setTimeout>;\n  };\n\n  constructor({\n    queryManager,\n    queryInfo,\n    options,\n  }: {\n    queryManager: QueryManager<any>;\n    queryInfo: QueryInfo;\n    options: WatchQueryOptions<TVariables, TData>;\n  }) {\n    let startedInactive = ObservableQuery.inactiveOnCreation.getValue();\n    super((observer: Observer<ApolloQueryResult<MaybeMasked<TData>>>) => {\n      if (startedInactive) {\n        queryManager[\"queries\"].set(this.queryId, queryInfo);\n        startedInactive = false;\n      }\n\n      // Zen Observable has its own error function, so in order to log correctly\n      // we need to provide a custom error callback.\n      try {\n        var subObserver = (observer as any)._subscription._observer;\n        if (subObserver && !subObserver.error) {\n          subObserver.error = defaultSubscriptionObserverErrorCallback;\n        }\n      } catch {}\n\n      const first = !this.observers.size;\n      this.observers.add(observer);\n\n      // Deliver most recent error or result.\n      const last = this.last;\n      if (last && last.error) {\n        observer.error && observer.error(last.error);\n      } else if (last && last.result) {\n        observer.next && observer.next(this.maskResult(last.result));\n      }\n\n      // Initiate observation of this query if it hasn't been reported to\n      // the QueryManager yet.\n      if (first) {\n        // Blindly catching here prevents unhandled promise rejections,\n        // and is safe because the ObservableQuery handles this error with\n        // this.observer.error, so we're not just swallowing the error by\n        // ignoring it here.\n        this.reobserve().catch(() => {});\n      }\n\n      return () => {\n        if (this.observers.delete(observer) && !this.observers.size) {\n          this.tearDownQuery();\n        }\n      };\n    });\n\n    // related classes\n    this.queryInfo = queryInfo;\n    this.queryManager = queryManager;\n\n    // active state\n    this.waitForOwnResult = skipCacheDataFor(options.fetchPolicy);\n    this.isTornDown = false;\n\n    this.subscribeToMore = this.subscribeToMore.bind(this);\n    this.maskResult = this.maskResult.bind(this);\n\n    const {\n      watchQuery: { fetchPolicy: defaultFetchPolicy = \"cache-first\" } = {},\n    } = queryManager.defaultOptions;\n\n    const {\n      fetchPolicy = defaultFetchPolicy,\n      // Make sure we don't store \"standby\" as the initialFetchPolicy.\n      initialFetchPolicy = fetchPolicy === \"standby\" ? defaultFetchPolicy : (\n        fetchPolicy\n      ),\n    } = options;\n\n    this.options = {\n      ...options,\n\n      // Remember the initial options.fetchPolicy so we can revert back to this\n      // policy when variables change. This information can also be specified\n      // (or overridden) by providing options.initialFetchPolicy explicitly.\n      initialFetchPolicy,\n\n      // This ensures this.options.fetchPolicy always has a string value, in\n      // case options.fetchPolicy was not provided.\n      fetchPolicy,\n    };\n\n    this.queryId = queryInfo.queryId || queryManager.generateQueryId();\n\n    const opDef = getOperationDefinition(this.query);\n    this.queryName = opDef && opDef.name && opDef.name.value;\n  }\n\n  public result(): Promise<ApolloQueryResult<MaybeMasked<TData>>> {\n    return new Promise((resolve, reject) => {\n      // TODO: this code doesn’t actually make sense insofar as the observer\n      // will never exist in this.observers due how zen-observable wraps observables.\n      // https://github.com/zenparsing/zen-observable/blob/master/src/Observable.js#L169\n      const observer: Observer<ApolloQueryResult<MaybeMasked<TData>>> = {\n        next: (result) => {\n          resolve(result);\n\n          // Stop the query within the QueryManager if we can before\n          // this function returns.\n          //\n          // We do this in order to prevent observers piling up within\n          // the QueryManager. Notice that we only fully unsubscribe\n          // from the subscription in a setTimeout(..., 0)  call. This call can\n          // actually be handled by the browser at a much later time. If queries\n          // are fired in the meantime, observers that should have been removed\n          // from the QueryManager will continue to fire, causing an unnecessary\n          // performance hit.\n          this.observers.delete(observer);\n          if (!this.observers.size) {\n            this.queryManager.removeQuery(this.queryId);\n          }\n\n          setTimeout(() => {\n            subscription.unsubscribe();\n          }, 0);\n        },\n        error: reject,\n      };\n      const subscription = this.subscribe(observer);\n    });\n  }\n\n  /** @internal */\n  public resetDiff() {\n    this.queryInfo.resetDiff();\n  }\n\n  private getCurrentFullResult(\n    saveAsLastResult = true\n  ): ApolloQueryResult<TData> {\n    // Use the last result as long as the variables match this.variables.\n    const lastResult = this.getLastResult(true);\n\n    const networkStatus =\n      this.queryInfo.networkStatus ||\n      (lastResult && lastResult.networkStatus) ||\n      NetworkStatus.ready;\n\n    const result = {\n      ...lastResult,\n      loading: isNetworkRequestInFlight(networkStatus),\n      networkStatus,\n    } as ApolloQueryResult<TData>;\n\n    const { fetchPolicy = \"cache-first\" } = this.options;\n    if (\n      // These fetch policies should never deliver data from the cache, unless\n      // redelivering a previously delivered result.\n      skipCacheDataFor(fetchPolicy) ||\n      // If this.options.query has @client(always: true) fields, we cannot\n      // trust diff.result, since it was read from the cache without running\n      // local resolvers (and it's too late to run resolvers now, since we must\n      // return a result synchronously).\n      this.queryManager.getDocumentInfo(this.query).hasForcedResolvers\n    ) {\n      // Fall through.\n    } else if (this.waitForOwnResult) {\n      // This would usually be a part of `QueryInfo.getDiff()`.\n      // which we skip in the waitForOwnResult case since we are not\n      // interested in the diff.\n      this.queryInfo[\"updateWatch\"]();\n    } else {\n      const diff = this.queryInfo.getDiff();\n\n      if (diff.complete || this.options.returnPartialData) {\n        result.data = diff.result;\n      }\n\n      if (equal(result.data, {})) {\n        result.data = void 0 as any;\n      }\n\n      if (diff.complete) {\n        // Similar to setting result.partial to false, but taking advantage of the\n        // falsiness of missing fields.\n        delete result.partial;\n\n        // If the diff is complete, and we're using a FetchPolicy that\n        // terminates after a complete cache read, we can assume the next result\n        // we receive will have NetworkStatus.ready and !loading.\n        if (\n          diff.complete &&\n          result.networkStatus === NetworkStatus.loading &&\n          (fetchPolicy === \"cache-first\" || fetchPolicy === \"cache-only\")\n        ) {\n          result.networkStatus = NetworkStatus.ready;\n          result.loading = false;\n        }\n      } else {\n        result.partial = true;\n      }\n\n      // We need to check for both both `error` and `errors` field because there\n      // are cases where sometimes `error` is set, but not `errors` and\n      // vice-versa. This will be updated in the next major version when\n      // `errors` is deprecated in favor of `error`.\n      if (\n        result.networkStatus === NetworkStatus.ready &&\n        (result.error || result.errors)\n      ) {\n        result.networkStatus = NetworkStatus.error;\n      }\n\n      if (\n        __DEV__ &&\n        !diff.complete &&\n        !this.options.partialRefetch &&\n        !result.loading &&\n        !result.data &&\n        !result.error\n      ) {\n        logMissingFieldErrors(diff.missing);\n      }\n    }\n\n    if (saveAsLastResult) {\n      this.updateLastResult(result);\n    }\n\n    return result;\n  }\n\n  public getCurrentResult(\n    saveAsLastResult = true\n  ): ApolloQueryResult<MaybeMasked<TData>> {\n    return this.maskResult(this.getCurrentFullResult(saveAsLastResult));\n  }\n\n  // Compares newResult to the snapshot we took of this.lastResult when it was\n  // first received.\n  public isDifferentFromLastResult(\n    newResult: ApolloQueryResult<TData>,\n    variables?: TVariables\n  ) {\n    if (!this.last) {\n      return true;\n    }\n\n    const documentInfo = this.queryManager.getDocumentInfo(this.query);\n    const dataMasking = this.queryManager.dataMasking;\n    const query = dataMasking ? documentInfo.nonReactiveQuery : this.query;\n\n    const resultIsDifferent =\n      dataMasking || documentInfo.hasNonreactiveDirective ?\n        !equalByQuery(query, this.last.result, newResult, this.variables)\n      : !equal(this.last.result, newResult);\n\n    return (\n      resultIsDifferent || (variables && !equal(this.last.variables, variables))\n    );\n  }\n\n  private getLast<K extends keyof Last<TData, TVariables>>(\n    key: K,\n    variablesMustMatch?: boolean\n  ) {\n    const last = this.last;\n    if (\n      last &&\n      last[key] &&\n      (!variablesMustMatch || equal(last.variables, this.variables))\n    ) {\n      return last[key];\n    }\n  }\n\n  public getLastResult(\n    variablesMustMatch?: boolean\n  ): ApolloQueryResult<TData> | undefined {\n    return this.getLast(\"result\", variablesMustMatch);\n  }\n\n  public getLastError(variablesMustMatch?: boolean): ApolloError | undefined {\n    return this.getLast(\"error\", variablesMustMatch);\n  }\n\n  public resetLastResults(): void {\n    delete this.last;\n    this.isTornDown = false;\n  }\n\n  public resetQueryStoreErrors() {\n    this.queryManager.resetErrors(this.queryId);\n  }\n\n  /**\n   * Update the variables of this observable query, and fetch the new results.\n   * This method should be preferred over `setVariables` in most use cases.\n   *\n   * @param variables - The new set of variables. If there are missing variables,\n   * the previous values of those variables will be used.\n   */\n  public refetch(\n    variables?: Partial<TVariables>\n  ): Promise<ApolloQueryResult<MaybeMasked<TData>>> {\n    const reobserveOptions: Partial<WatchQueryOptions<TVariables, TData>> = {\n      // Always disable polling for refetches.\n      pollInterval: 0,\n    };\n\n    // Unless the provided fetchPolicy always consults the network\n    // (no-cache, network-only, or cache-and-network), override it with\n    // network-only to force the refetch for this fetchQuery call.\n    const { fetchPolicy } = this.options;\n    if (fetchPolicy === \"no-cache\") {\n      reobserveOptions.fetchPolicy = \"no-cache\";\n    } else {\n      reobserveOptions.fetchPolicy = \"network-only\";\n    }\n\n    if (__DEV__ && variables && hasOwnProperty.call(variables, \"variables\")) {\n      const queryDef = getQueryDefinition(this.query);\n      const vars = queryDef.variableDefinitions;\n      if (!vars || !vars.some((v) => v.variable.name.value === \"variables\")) {\n        invariant.warn(\n          `Called refetch(%o) for query %o, which does not declare a $variables variable.\nDid you mean to call refetch(variables) instead of refetch({ variables })?`,\n          variables,\n          queryDef.name?.value || queryDef\n        );\n      }\n    }\n\n    if (variables && !equal(this.options.variables, variables)) {\n      // Update the existing options with new variables\n      reobserveOptions.variables = this.options.variables = {\n        ...this.options.variables,\n        ...variables,\n      } as TVariables;\n    }\n\n    this.queryInfo.resetLastWrite();\n    return this.reobserve(reobserveOptions, NetworkStatus.refetch);\n  }\n\n  /**\n   * A function that helps you fetch the next set of results for a [paginated list field](https://www.apollographql.com/docs/react/pagination/core-api/).\n   */\n  public fetchMore<\n    TFetchData = TData,\n    TFetchVars extends OperationVariables = TVariables,\n  >(\n    fetchMoreOptions: FetchMoreQueryOptions<TFetchVars, TFetchData> & {\n      updateQuery?: (\n        previousQueryResult: Unmasked<TData>,\n        options: {\n          fetchMoreResult: Unmasked<TFetchData>;\n          variables: TFetchVars;\n        }\n      ) => Unmasked<TData>;\n    }\n  ): Promise<ApolloQueryResult<MaybeMasked<TFetchData>>> {\n    const combinedOptions = {\n      ...(fetchMoreOptions.query ? fetchMoreOptions : (\n        {\n          ...this.options,\n          query: this.options.query,\n          ...fetchMoreOptions,\n          variables: {\n            ...this.options.variables,\n            ...fetchMoreOptions.variables,\n          },\n        }\n      )),\n      // The fetchMore request goes immediately to the network and does\n      // not automatically write its result to the cache (hence no-cache\n      // instead of network-only), because we allow the caller of\n      // fetchMore to provide an updateQuery callback that determines how\n      // the data gets written to the cache.\n      fetchPolicy: \"no-cache\",\n    } as WatchQueryOptions<TFetchVars, TFetchData>;\n\n    combinedOptions.query = this.transformDocument(combinedOptions.query);\n\n    const qid = this.queryManager.generateQueryId();\n\n    // If a temporary query is passed to `fetchMore`, we don't want to store\n    // it as the last query result since it may be an optimized query for\n    // pagination. We will however run the transforms on the original document\n    // as well as the document passed in `fetchMoreOptions` to ensure the cache\n    // uses the most up-to-date document which may rely on runtime conditionals.\n    this.lastQuery =\n      fetchMoreOptions.query ?\n        this.transformDocument(this.options.query)\n      : combinedOptions.query;\n\n    // Simulate a loading result for the original query with\n    // result.networkStatus === NetworkStatus.fetchMore.\n    const { queryInfo } = this;\n    const originalNetworkStatus = queryInfo.networkStatus;\n    queryInfo.networkStatus = NetworkStatus.fetchMore;\n    if (combinedOptions.notifyOnNetworkStatusChange) {\n      this.observe();\n    }\n\n    const updatedQuerySet = new Set<DocumentNode>();\n\n    const updateQuery = fetchMoreOptions?.updateQuery;\n    const isCached = this.options.fetchPolicy !== \"no-cache\";\n\n    if (!isCached) {\n      invariant(\n        updateQuery,\n        \"You must provide an `updateQuery` function when using `fetchMore` with a `no-cache` fetch policy.\"\n      );\n    }\n\n    return this.queryManager\n      .fetchQuery(qid, combinedOptions, NetworkStatus.fetchMore)\n      .then((fetchMoreResult) => {\n        this.queryManager.removeQuery(qid);\n\n        if (queryInfo.networkStatus === NetworkStatus.fetchMore) {\n          queryInfo.networkStatus = originalNetworkStatus;\n        }\n\n        if (isCached) {\n          // Performing this cache update inside a cache.batch transaction ensures\n          // any affected cache.watch watchers are notified at most once about any\n          // updates. Most watchers will be using the QueryInfo class, which\n          // responds to notifications by calling reobserveCacheFirst to deliver\n          // fetchMore cache results back to this ObservableQuery.\n          this.queryManager.cache.batch({\n            update: (cache) => {\n              const { updateQuery } = fetchMoreOptions;\n              if (updateQuery) {\n                cache.updateQuery(\n                  {\n                    query: this.query,\n                    variables: this.variables,\n                    returnPartialData: true,\n                    optimistic: false,\n                  },\n                  (previous) =>\n                    updateQuery(previous! as any, {\n                      fetchMoreResult: fetchMoreResult.data as any,\n                      variables: combinedOptions.variables as TFetchVars,\n                    })\n                );\n              } else {\n                // If we're using a field policy instead of updateQuery, the only\n                // thing we need to do is write the new data to the cache using\n                // combinedOptions.variables (instead of this.variables, which is\n                // what this.updateQuery uses, because it works by abusing the\n                // original field value, keyed by the original variables).\n                cache.writeQuery({\n                  query: combinedOptions.query,\n                  variables: combinedOptions.variables,\n                  data: fetchMoreResult.data as Unmasked<TFetchData>,\n                });\n              }\n            },\n\n            onWatchUpdated: (watch) => {\n              // Record the DocumentNode associated with any watched query whose\n              // data were updated by the cache writes above.\n              updatedQuerySet.add(watch.query);\n            },\n          });\n        } else {\n          // There is a possibility `lastResult` may not be set when\n          // `fetchMore` is called which would cause this to crash. This should\n          // only happen if we haven't previously reported a result. We don't\n          // quite know what the right behavior should be here since this block\n          // of code runs after the fetch result has executed on the network.\n          // We plan to let it crash in the meantime.\n          //\n          // If we get bug reports due to the `data` property access on\n          // undefined, this should give us a real-world scenario that we can\n          // use to test against and determine the right behavior. If we do end\n          // up changing this behavior, this may require, for example, an\n          // adjustment to the types on `updateQuery` since that function\n          // expects that the first argument always contains previous result\n          // data, but not `undefined`.\n          const lastResult = this.getLast(\"result\")!;\n          const data = updateQuery!(lastResult.data as Unmasked<TData>, {\n            fetchMoreResult: fetchMoreResult.data as Unmasked<TFetchData>,\n            variables: combinedOptions.variables as TFetchVars,\n          });\n\n          this.reportResult(\n            {\n              ...lastResult,\n              networkStatus: originalNetworkStatus!,\n              loading: isNetworkRequestInFlight(originalNetworkStatus),\n              data: data as TData,\n            },\n            this.variables\n          );\n        }\n\n        return this.maskResult(fetchMoreResult);\n      })\n      .finally(() => {\n        // In case the cache writes above did not generate a broadcast\n        // notification (which would have been intercepted by onWatchUpdated),\n        // likely because the written data were the same as what was already in\n        // the cache, we still want fetchMore to deliver its final loading:false\n        // result with the unchanged data.\n        if (isCached && !updatedQuerySet.has(this.query)) {\n          this.reobserveCacheFirst();\n        }\n      });\n  }\n\n  // XXX the subscription variables are separate from the query variables.\n  // if you want to update subscription variables, right now you have to do that separately,\n  // and you can only do it by stopping the subscription and then subscribing again with new variables.\n  /**\n   * A function that enables you to execute a [subscription](https://www.apollographql.com/docs/react/data/subscriptions/), usually to subscribe to specific fields that were included in the query.\n   *\n   * This function returns _another_ function that you can call to terminate the subscription.\n   */\n  public subscribeToMore<\n    TSubscriptionData = TData,\n    TSubscriptionVariables extends OperationVariables = TVariables,\n  >(\n    options: SubscribeToMoreOptions<\n      TData,\n      TSubscriptionVariables,\n      TSubscriptionData,\n      TVariables\n    >\n  ): () => void {\n    const subscription = this.queryManager\n      .startGraphQLSubscription({\n        query: options.document,\n        variables: options.variables,\n        context: options.context,\n      })\n      .subscribe({\n        next: (subscriptionData: { data: Unmasked<TSubscriptionData> }) => {\n          const { updateQuery } = options;\n          if (updateQuery) {\n            this.updateQuery((previous, updateOptions) =>\n              updateQuery(previous, {\n                subscriptionData,\n                ...updateOptions,\n              })\n            );\n          }\n        },\n        error: (err: any) => {\n          if (options.onError) {\n            options.onError(err);\n            return;\n          }\n          invariant.error(\"Unhandled GraphQL subscription error\", err);\n        },\n      });\n\n    this.subscriptions.add(subscription);\n\n    return () => {\n      if (this.subscriptions.delete(subscription)) {\n        subscription.unsubscribe();\n      }\n    };\n  }\n\n  public setOptions(\n    newOptions: Partial<WatchQueryOptions<TVariables, TData>>\n  ): Promise<ApolloQueryResult<MaybeMasked<TData>>> {\n    return this.reobserve(newOptions);\n  }\n\n  public silentSetOptions(\n    newOptions: Partial<WatchQueryOptions<TVariables, TData>>\n  ) {\n    const mergedOptions = compact(this.options, newOptions || {});\n    assign(this.options, mergedOptions);\n  }\n\n  /**\n   * Update the variables of this observable query, and fetch the new results\n   * if they've changed. Most users should prefer `refetch` instead of\n   * `setVariables` in order to to be properly notified of results even when\n   * they come from the cache.\n   *\n   * Note: the `next` callback will *not* fire if the variables have not changed\n   * or if the result is coming from cache.\n   *\n   * Note: the promise will return the old results immediately if the variables\n   * have not changed.\n   *\n   * Note: the promise will return null immediately if the query is not active\n   * (there are no subscribers).\n   *\n   * @param variables - The new set of variables. If there are missing variables,\n   * the previous values of those variables will be used.\n   */\n  public setVariables(\n    variables: TVariables\n  ): Promise<ApolloQueryResult<MaybeMasked<TData>> | void> {\n    if (equal(this.variables, variables)) {\n      // If we have no observers, then we don't actually want to make a network\n      // request. As soon as someone observes the query, the request will kick\n      // off. For now, we just store any changes. (See #1077)\n      return this.observers.size ? this.result() : Promise.resolve();\n    }\n\n    this.options.variables = variables;\n\n    // See comment above\n    if (!this.observers.size) {\n      return Promise.resolve();\n    }\n\n    return this.reobserve(\n      {\n        // Reset options.fetchPolicy to its original value.\n        fetchPolicy: this.options.initialFetchPolicy,\n        variables,\n      },\n      NetworkStatus.setVariables\n    );\n  }\n\n  /**\n   * A function that enables you to update the query's cached result without executing a followup GraphQL operation.\n   *\n   * See [using updateQuery and updateFragment](https://www.apollographql.com/docs/react/caching/cache-interaction/#using-updatequery-and-updatefragment) for additional information.\n   */\n  public updateQuery(mapFn: UpdateQueryMapFn<TData, TVariables>): void {\n    const { queryManager } = this;\n    const { result, complete } = queryManager.cache.diff<TData>({\n      query: this.options.query,\n      variables: this.variables,\n      returnPartialData: true,\n      optimistic: false,\n    });\n\n    const newResult = mapFn(\n      result! as Unmasked<TData>,\n      {\n        variables: this.variables,\n        complete: !!complete,\n        previousData: result,\n      } as UpdateQueryOptions<TData, TVariables>\n    );\n\n    if (newResult) {\n      queryManager.cache.writeQuery({\n        query: this.options.query,\n        data: newResult,\n        variables: this.variables,\n      });\n\n      queryManager.broadcastQueries();\n    }\n  }\n\n  /**\n   * A function that instructs the query to begin re-executing at a specified interval (in milliseconds).\n   */\n  public startPolling(pollInterval: number) {\n    this.options.pollInterval = pollInterval;\n    this.updatePolling();\n  }\n\n  /**\n   * A function that instructs the query to stop polling after a previous call to `startPolling`.\n   */\n  public stopPolling() {\n    this.options.pollInterval = 0;\n    this.updatePolling();\n  }\n\n  // Update options.fetchPolicy according to options.nextFetchPolicy.\n  private applyNextFetchPolicy(\n    reason: NextFetchPolicyContext<TData, TVariables>[\"reason\"],\n    // It's possible to use this method to apply options.nextFetchPolicy to\n    // options.fetchPolicy even if options !== this.options, though that happens\n    // most often when the options are temporary, used for only one request and\n    // then thrown away, so nextFetchPolicy may not end up mattering.\n    options: WatchQueryOptions<TVariables, TData>\n  ) {\n    if (options.nextFetchPolicy) {\n      const { fetchPolicy = \"cache-first\", initialFetchPolicy = fetchPolicy } =\n        options;\n\n      if (fetchPolicy === \"standby\") {\n        // Do nothing, leaving options.fetchPolicy unchanged.\n      } else if (typeof options.nextFetchPolicy === \"function\") {\n        // When someone chooses \"cache-and-network\" or \"network-only\" as their\n        // initial FetchPolicy, they often do not want future cache updates to\n        // trigger unconditional network requests, which is what repeatedly\n        // applying the \"cache-and-network\" or \"network-only\" policies would\n        // seem to imply. Instead, when the cache reports an update after the\n        // initial network request, it may be desirable for subsequent network\n        // requests to be triggered only if the cache result is incomplete. To\n        // that end, the options.nextFetchPolicy option provides an easy way to\n        // update options.fetchPolicy after the initial network request, without\n        // having to call observableQuery.setOptions.\n        options.fetchPolicy = options.nextFetchPolicy(fetchPolicy, {\n          reason,\n          options,\n          observable: this,\n          initialFetchPolicy,\n        });\n      } else if (reason === \"variables-changed\") {\n        options.fetchPolicy = initialFetchPolicy;\n      } else {\n        options.fetchPolicy = options.nextFetchPolicy;\n      }\n    }\n\n    return options.fetchPolicy;\n  }\n\n  private fetch(\n    options: WatchQueryOptions<TVariables, TData>,\n    newNetworkStatus?: NetworkStatus,\n    query?: DocumentNode\n  ) {\n    // TODO Make sure we update the networkStatus (and infer fetchVariables)\n    // before actually committing to the fetch.\n    const queryInfo = this.queryManager.getOrCreateQuery(this.queryId);\n    queryInfo.setObservableQuery(this);\n    return this.queryManager[\"fetchConcastWithInfo\"](\n      queryInfo,\n      options,\n      newNetworkStatus,\n      query\n    );\n  }\n\n  // Turns polling on or off based on this.options.pollInterval.\n  private updatePolling() {\n    // Avoid polling in SSR mode\n    if (this.queryManager.ssrMode) {\n      return;\n    }\n\n    const {\n      pollingInfo,\n      options: { pollInterval },\n    } = this;\n\n    if (!pollInterval || !this.hasObservers()) {\n      if (pollingInfo) {\n        clearTimeout(pollingInfo.timeout);\n        delete this.pollingInfo;\n      }\n      return;\n    }\n\n    if (pollingInfo && pollingInfo.interval === pollInterval) {\n      return;\n    }\n\n    invariant(\n      pollInterval,\n      \"Attempted to start a polling query without a polling interval.\"\n    );\n\n    const info = pollingInfo || (this.pollingInfo = {} as any);\n    info.interval = pollInterval;\n\n    const maybeFetch = () => {\n      if (this.pollingInfo) {\n        if (\n          !isNetworkRequestInFlight(this.queryInfo.networkStatus) &&\n          !this.options.skipPollAttempt?.()\n        ) {\n          this.reobserve(\n            {\n              // Most fetchPolicy options don't make sense to use in a polling context, as\n              // users wouldn't want to be polling the cache directly. However, network-only and\n              // no-cache are both useful for when the user wants to control whether or not the\n              // polled results are written to the cache.\n              fetchPolicy:\n                this.options.initialFetchPolicy === \"no-cache\" ?\n                  \"no-cache\"\n                : \"network-only\",\n            },\n            NetworkStatus.poll\n          ).then(poll, poll);\n        } else {\n          poll();\n        }\n      }\n    };\n\n    const poll = () => {\n      const info = this.pollingInfo;\n      if (info) {\n        clearTimeout(info.timeout);\n        info.timeout = setTimeout(maybeFetch, info.interval);\n      }\n    };\n\n    poll();\n  }\n\n  private updateLastResult(\n    newResult: ApolloQueryResult<TData>,\n    variables = this.variables\n  ) {\n    let error: ApolloError | undefined = this.getLastError();\n    // Preserve this.last.error unless the variables have changed.\n    if (error && this.last && !equal(variables, this.last.variables)) {\n      error = void 0;\n    }\n    return (this.last = {\n      result:\n        this.queryManager.assumeImmutableResults ?\n          newResult\n        : cloneDeep(newResult),\n      variables,\n      ...(error ? { error } : null),\n    });\n  }\n\n  public reobserveAsConcast(\n    newOptions?: Partial<WatchQueryOptions<TVariables, TData>>,\n    newNetworkStatus?: NetworkStatus\n  ): Concast<ApolloQueryResult<TData>> {\n    this.isTornDown = false;\n\n    const useDisposableConcast =\n      // Refetching uses a disposable Concast to allow refetches using different\n      // options/variables, without permanently altering the options of the\n      // original ObservableQuery.\n      newNetworkStatus === NetworkStatus.refetch ||\n      // The fetchMore method does not actually call the reobserve method, but,\n      // if it did, it would definitely use a disposable Concast.\n      newNetworkStatus === NetworkStatus.fetchMore ||\n      // Polling uses a disposable Concast so the polling options (which force\n      // fetchPolicy to be \"network-only\" or \"no-cache\") won't override the original options.\n      newNetworkStatus === NetworkStatus.poll;\n\n    // Save the old variables, since Object.assign may modify them below.\n    const oldVariables = this.options.variables;\n    const oldFetchPolicy = this.options.fetchPolicy;\n\n    const mergedOptions = compact(this.options, newOptions || {});\n    const options =\n      useDisposableConcast ?\n        // Disposable Concast fetches receive a shallow copy of this.options\n        // (merged with newOptions), leaving this.options unmodified.\n        mergedOptions\n      : assign(this.options, mergedOptions);\n\n    // Don't update options.query with the transformed query to avoid\n    // overwriting this.options.query when we aren't using a disposable concast.\n    // We want to ensure we can re-run the custom document transforms the next\n    // time a request is made against the original query.\n    const query = this.transformDocument(options.query);\n\n    this.lastQuery = query;\n\n    if (!useDisposableConcast) {\n      // We can skip calling updatePolling if we're not changing this.options.\n      this.updatePolling();\n\n      // Reset options.fetchPolicy to its original value when variables change,\n      // unless a new fetchPolicy was provided by newOptions.\n      if (\n        newOptions &&\n        newOptions.variables &&\n        !equal(newOptions.variables, oldVariables) &&\n        // Don't mess with the fetchPolicy if it's currently \"standby\".\n        options.fetchPolicy !== \"standby\" &&\n        // If we're changing the fetchPolicy anyway, don't try to change it here\n        // using applyNextFetchPolicy. The explicit options.fetchPolicy wins.\n        (options.fetchPolicy === oldFetchPolicy ||\n          // A `nextFetchPolicy` function has even higher priority, though,\n          // so in that case `applyNextFetchPolicy` must be called.\n          typeof options.nextFetchPolicy === \"function\")\n      ) {\n        this.applyNextFetchPolicy(\"variables-changed\", options);\n        if (newNetworkStatus === void 0) {\n          newNetworkStatus = NetworkStatus.setVariables;\n        }\n      }\n    }\n\n    this.waitForOwnResult &&= skipCacheDataFor(options.fetchPolicy);\n    const finishWaitingForOwnResult = () => {\n      if (this.concast === concast) {\n        this.waitForOwnResult = false;\n      }\n    };\n\n    const variables = options.variables && { ...options.variables };\n    const { concast, fromLink } = this.fetch(options, newNetworkStatus, query);\n    const observer: Observer<ApolloQueryResult<TData>> = {\n      next: (result) => {\n        if (equal(this.variables, variables)) {\n          finishWaitingForOwnResult();\n          this.reportResult(result, variables);\n        }\n      },\n      error: (error) => {\n        if (equal(this.variables, variables)) {\n          // Coming from `getResultsFromLink`, `error` here should always be an `ApolloError`.\n          // However, calling `concast.cancel` can inject another type of error, so we have to\n          // wrap it again here.\n          if (!isApolloError(error)) {\n            error = new ApolloError({ networkError: error });\n          }\n          finishWaitingForOwnResult();\n          this.reportError(error, variables);\n        }\n      },\n    };\n\n    if (!useDisposableConcast && (fromLink || !this.concast)) {\n      // We use the {add,remove}Observer methods directly to avoid wrapping\n      // observer with an unnecessary SubscriptionObserver object.\n      if (this.concast && this.observer) {\n        this.concast.removeObserver(this.observer);\n      }\n\n      this.concast = concast;\n      this.observer = observer;\n    }\n\n    concast.addObserver(observer);\n\n    return concast;\n  }\n\n  public reobserve(\n    newOptions?: Partial<WatchQueryOptions<TVariables, TData>>,\n    newNetworkStatus?: NetworkStatus\n  ): Promise<ApolloQueryResult<MaybeMasked<TData>>> {\n    return preventUnhandledRejection(\n      this.reobserveAsConcast(newOptions, newNetworkStatus).promise.then(\n        this.maskResult as TODO\n      )\n    );\n  }\n\n  public resubscribeAfterError(\n    onNext: (value: ApolloQueryResult<MaybeMasked<TData>>) => void,\n    onError?: (error: any) => void,\n    onComplete?: () => void\n  ): ObservableSubscription;\n\n  public resubscribeAfterError(\n    observer: Observer<ApolloQueryResult<TData>>\n  ): ObservableSubscription;\n\n  public resubscribeAfterError(...args: [any, any?, any?]) {\n    // If `lastError` is set in the current when the subscription is re-created,\n    // the subscription will immediately receive the error, which will\n    // cause it to terminate again. To avoid this, we first clear\n    // the last error/result from the `observableQuery` before re-starting\n    // the subscription, and restore the last value afterwards so that the\n    // subscription has a chance to stay open.\n    const last = this.last;\n    this.resetLastResults();\n\n    const subscription = this.subscribe(...args);\n    this.last = last;\n\n    return subscription;\n  }\n\n  // (Re)deliver the current result to this.observers without applying fetch\n  // policies or making network requests.\n  private observe() {\n    this.reportResult(\n      // Passing false is important so that this.getCurrentResult doesn't\n      // save the fetchMore result as this.lastResult, causing it to be\n      // ignored due to the this.isDifferentFromLastResult check in\n      // this.reportResult.\n      this.getCurrentFullResult(false),\n      this.variables\n    );\n  }\n\n  private reportResult(\n    result: ApolloQueryResult<TData>,\n    variables: TVariables | undefined\n  ) {\n    const lastError = this.getLastError();\n    const isDifferent = this.isDifferentFromLastResult(result, variables);\n    // Update the last result even when isDifferentFromLastResult returns false,\n    // because the query may be using the @nonreactive directive, and we want to\n    // save the the latest version of any nonreactive subtrees (in case\n    // getCurrentResult is called), even though we skip broadcasting changes.\n    if (lastError || !result.partial || this.options.returnPartialData) {\n      this.updateLastResult(result, variables);\n    }\n    if (lastError || isDifferent) {\n      iterateObserversSafely(this.observers, \"next\", this.maskResult(result));\n    }\n  }\n\n  private reportError(error: ApolloError, variables: TVariables | undefined) {\n    // Since we don't get the current result on errors, only the error, we\n    // must mirror the updates that occur in QueryStore.markQueryError here\n    const errorResult = {\n      ...this.getLastResult(),\n      error,\n      errors: error.graphQLErrors,\n      networkStatus: NetworkStatus.error,\n      loading: false,\n    } as ApolloQueryResult<TData>;\n\n    this.updateLastResult(errorResult, variables);\n\n    iterateObserversSafely(this.observers, \"error\", (this.last!.error = error));\n  }\n\n  public hasObservers() {\n    return this.observers.size > 0;\n  }\n\n  private tearDownQuery() {\n    if (this.isTornDown) return;\n    if (this.concast && this.observer) {\n      this.concast.removeObserver(this.observer);\n      delete this.concast;\n      delete this.observer;\n    }\n\n    this.stopPolling();\n    // stop all active GraphQL subscriptions\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    this.subscriptions.clear();\n    this.queryManager.stopQuery(this.queryId);\n    this.observers.clear();\n    this.isTornDown = true;\n  }\n\n  private transformDocument(document: DocumentNode) {\n    return this.queryManager.transform(document);\n  }\n\n  private maskResult<T = TData>(\n    result: ApolloQueryResult<T>\n  ): ApolloQueryResult<MaybeMasked<T>> {\n    return result && \"data\" in result ?\n        {\n          ...result,\n          data: this.queryManager.maskOperation({\n            document: this.query,\n            data: result.data,\n            fetchPolicy: this.options.fetchPolicy,\n            id: this.queryId,\n          }),\n        }\n      : result;\n  }\n\n  private dirty: boolean = false;\n\n  private notifyTimeout?: ReturnType<typeof setTimeout>;\n\n  /** @internal */\n  protected resetNotifications() {\n    this.cancelNotifyTimeout();\n    this.dirty = false;\n  }\n\n  private cancelNotifyTimeout() {\n    if (this.notifyTimeout) {\n      clearTimeout(this.notifyTimeout);\n      this.notifyTimeout = void 0;\n    }\n  }\n\n  /** @internal */\n  protected scheduleNotify() {\n    if (this.dirty) return;\n    this.dirty = true;\n    if (!this.notifyTimeout) {\n      this.notifyTimeout = setTimeout(() => this.notify(), 0);\n    }\n  }\n\n  /** @internal */\n  protected notify() {\n    this.cancelNotifyTimeout();\n\n    if (this.dirty) {\n      if (\n        this.options.fetchPolicy == \"cache-only\" ||\n        this.options.fetchPolicy == \"cache-and-network\" ||\n        !isNetworkRequestInFlight(this.queryInfo.networkStatus)\n      ) {\n        const diff = this.queryInfo.getDiff();\n        if (diff.fromOptimisticTransaction) {\n          // If this diff came from an optimistic transaction, deliver the\n          // current cache data to the ObservableQuery, but don't perform a\n          // reobservation, since oq.reobserveCacheFirst might make a network\n          // request, and we never want to trigger network requests in the\n          // middle of optimistic updates.\n          this.observe();\n        } else {\n          // Otherwise, make the ObservableQuery \"reobserve\" the latest data\n          // using a temporary fetch policy of \"cache-first\", so complete cache\n          // results have a chance to be delivered without triggering additional\n          // network requests, even when options.fetchPolicy is \"network-only\"\n          // or \"cache-and-network\". All other fetch policies are preserved by\n          // this method, and are handled by calling oq.reobserve(). If this\n          // reobservation is spurious, isDifferentFromLastResult still has a\n          // chance to catch it before delivery to ObservableQuery subscribers.\n          this.reobserveCacheFirst();\n        }\n      }\n    }\n\n    this.dirty = false;\n  }\n\n  // Reobserve with fetchPolicy effectively set to \"cache-first\", triggering\n  // delivery of any new data from the cache, possibly falling back to the network\n  // if any cache data are missing. This allows _complete_ cache results to be\n  // delivered without also kicking off unnecessary network requests when\n  // this.options.fetchPolicy is \"cache-and-network\" or \"network-only\". When\n  // this.options.fetchPolicy is any other policy (\"cache-first\", \"cache-only\",\n  // \"standby\", or \"no-cache\"), we call this.reobserve() as usual.\n  private reobserveCacheFirst() {\n    const { fetchPolicy, nextFetchPolicy } = this.options;\n\n    if (fetchPolicy === \"cache-and-network\" || fetchPolicy === \"network-only\") {\n      return this.reobserve({\n        fetchPolicy: \"cache-first\",\n        // Use a temporary nextFetchPolicy function that replaces itself with the\n        // previous nextFetchPolicy value and returns the original fetchPolicy.\n        nextFetchPolicy(\n          this: WatchQueryOptions<TVariables, TData>,\n          currentFetchPolicy: WatchQueryFetchPolicy,\n          context: NextFetchPolicyContext<TData, TVariables>\n        ) {\n          // Replace this nextFetchPolicy function in the options object with the\n          // original this.options.nextFetchPolicy value.\n          this.nextFetchPolicy = nextFetchPolicy;\n          // If the original nextFetchPolicy value was a function, give it a\n          // chance to decide what happens here.\n          if (typeof this.nextFetchPolicy === \"function\") {\n            return this.nextFetchPolicy(currentFetchPolicy, context);\n          }\n          // Otherwise go back to the original this.options.fetchPolicy.\n          return fetchPolicy!;\n        },\n      });\n    }\n\n    return this.reobserve();\n  }\n}\n\n// Necessary because the ObservableQuery constructor has a different\n// signature than the Observable constructor.\nfixObservableSubclass(ObservableQuery);\n\nfunction defaultSubscriptionObserverErrorCallback(error: ApolloError) {\n  invariant.error(\"Unhandled error\", error.message, error.stack);\n}\n\nexport function logMissingFieldErrors(\n  missing: MissingFieldError[] | MissingTree | undefined\n) {\n  if (__DEV__ && missing) {\n    invariant.debug(`Missing cache result fields: %o`, missing);\n  }\n}\n\nfunction skipCacheDataFor(\n  fetchPolicy?: WatchQueryFetchPolicy /* `undefined` would mean `\"cache-first\"` */\n) {\n  return (\n    fetchPolicy === \"network-only\" ||\n    fetchPolicy === \"no-cache\" ||\n    fetchPolicy === \"standby\"\n  );\n}\n"]}