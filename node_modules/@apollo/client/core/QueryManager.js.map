{"version": 3, "file": "QueryManager.js", "sourceRoot": "", "sources": ["../../src/core/QueryManager.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAK7E,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAGtC,OAAO,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAChD,OAAO,EACL,8BAA8B,EAE9B,aAAa,EACb,iCAAiC,EACjC,sBAAsB,EACtB,wBAAwB,EACxB,4BAA4B,GAC7B,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAMvD,OAAO,EACL,gBAAgB,EAChB,sBAAsB,EACtB,gBAAgB,EAChB,gBAAgB,EAChB,qBAAqB,EACrB,0BAA0B,EAC1B,UAAU,EACV,QAAQ,EACR,eAAe,EACf,OAAO,EACP,YAAY,EACZ,cAAc,EACd,eAAe,EACf,iBAAiB,GAClB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,oBAAoB,EAAE,MAAM,0CAA0C,CAAC;AAChF,OAAO,EACL,WAAW,EACX,aAAa,EACb,8BAA8B,GAC/B,MAAM,oBAAoB,CAAC;AAU5B,OAAO,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,sBAAsB,CAAC;AAC9E,OAAO,EAAE,aAAa,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;AAe7E,OAAO,EACL,SAAS,EACT,iBAAiB,GAElB,MAAM,gBAAgB,CAAC;AAExB,OAAO,EAAE,sBAAsB,EAAE,MAAM,oBAAoB,CAAC;AAC5D,OAAO,EAAE,KAAK,EAAE,MAAM,uBAAuB,CAAC;AAItC,IAAA,cAAc,GAAK,MAAM,CAAC,SAAS,eAArB,CAAsB;AAE5C,IAAM,MAAM,GAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAuBnD,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AACjC,OAAO,EAAE,oBAAoB,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACzE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;AA+BlE;IA8BE,sBAAY,OAAoC;QAAhD,iBAgCC;QAlDO,oBAAe,GAA2B,EAAE,CAAC;QAQrD,mEAAmE;QACnE,0CAA0C;QAClC,YAAO,GAAG,IAAI,GAAG,EAAqB,CAAC;QAE/C,+DAA+D;QAC/D,wCAAwC;QACxC,4CAA4C;QAC5C,gEAAgE;QACtD,mBAAc,GAAG,IAAI,GAAG,EAA+B,CAAC;QA2gB1D,mBAAc,GAAG,IAAI,oBAAoB,CAI/C,UAAU,CAAC,8BAA8B,CAAC;wEACS,CACpD,CAAC;QAiJM,mBAAc,GAAG,CAAC,CAAC;QAKnB,qBAAgB,GAAG,CAAC,CAAC;QAKrB,sBAAiB,GAAG,CAAC,CAAC;QA4R9B,4CAA4C;QAC5C,gEAAgE;QACtD,4BAAuB,GAAG,IAAI,IAAI,CAEzC,KAAK,CAAC,CAAC;QAwbF,6BAAwB,GAAG,IAAI,GAAG,EAAU,CAAC;QAj4CnD,IAAM,wBAAwB,GAAG,IAAI,iBAAiB,CACpD,UAAC,QAAQ,IAAK,OAAA,KAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAtC,CAAsC;QACpD,4DAA4D;QAC5D,EAAE,KAAK,EAAE,KAAK,EAAE,CACjB,CAAC;QAEF,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;QAC7C,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACrD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;QAC/C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC/B,IAAI,CAAC,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAC;QAC7D,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACvC,IAAM,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;QACpD,IAAI,CAAC,iBAAiB;YACpB,iBAAiB,CAAC,CAAC;gBACjB,wBAAwB;qBACrB,MAAM,CAAC,iBAAiB,CAAC;oBAC1B,oEAAoE;oBACpE,iEAAiE;oBACjE,iEAAiE;oBACjE,uDAAuD;qBACtD,MAAM,CAAC,wBAAwB,CAAC;gBACrC,CAAC,CAAC,wBAAwB,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEpE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,2BAAI,GAAX;QAAA,iBAQC;QAPC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,OAAO;YAClC,KAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,CACvB,iBAAiB,CAAC,gDAAgD,CAAC,CACpE,CAAC;IACJ,CAAC;IAEO,2CAAoB,GAA5B,UAA6B,KAAY;QACvC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAC,MAAM,IAAK,OAAA,MAAM,CAAC,KAAK,CAAC,EAAb,CAAa,CAAC,CAAC;QACvD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;IAC9B,CAAC;IAEY,6BAAM,GAAnB;4DAKE,EAa6C;;;gBAZ7C,QAAQ,cAAA,EACR,SAAS,eAAA,EACT,kBAAkB,wBAAA,EAClB,aAAa,mBAAA,EACb,sBAAmB,EAAnB,cAAc,mBAAG,EAAE,KAAA,EACnB,2BAA2B,EAA3B,mBAAmB,mBAAG,KAAK,KAAA,EACnB,iBAAiB,YAAA,EACzB,cAAc,oBAAA,EACd,mBAAuE,EAAvE,WAAW,mBAAG,CAAA,MAAA,IAAI,CAAC,cAAc,CAAC,MAAM,0CAAE,WAAW,KAAI,cAAc,KAAA,EACvE,mBAA+D,EAA/D,WAAW,mBAAG,CAAA,MAAA,IAAI,CAAC,cAAc,CAAC,MAAM,0CAAE,WAAW,KAAI,MAAM,KAAA,EAC/D,cAAc,oBAAA,EACd,OAAO,aAAA;;;;wBAIP,SAAS,CACP,QAAQ,EACR,6FAA6F,CAC9F,CAAC;wBAEF,SAAS,CACP,WAAW,KAAK,cAAc,IAAI,WAAW,KAAK,UAAU,EAC5D,4MAA4M,CAC7M,CAAC;wBAEI,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;wBAE7C,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;wBACzD,gBAAgB,GAAK,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,iBAAnC,CAAoC;wBAE5D,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,SAAS,CAAe,CAAC;6BAC7D,gBAAgB,EAAhB,wBAAgB;wBACL,qBAAM,IAAI,CAAC,UAAU,CAAC,oBAAoB,CACrD,QAAQ,EACR,SAAS,EACT,OAAO,CACR,EAAA;;wBAJD,SAAS,GAAG,CAAC,SAIZ,CAAe,CAAC;;;wBAGb,kBAAkB,GACtB,IAAI,CAAC,aAAa;4BAClB,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG;gCAChC,QAAQ,UAAA;gCACR,SAAS,WAAA;gCACT,OAAO,EAAE,IAAI;gCACb,KAAK,EAAE,IAAI;6BACU,CAAC,CAAC;wBAErB,YAAY,GAChB,kBAAkB;4BAClB,IAAI,CAAC,sBAAsB,CACzB,kBAAkB,EAClB;gCACE,UAAU,YAAA;gCACV,QAAQ,EAAE,QAAQ;gCAClB,SAAS,WAAA;gCACT,WAAW,aAAA;gCACX,WAAW,aAAA;gCACX,OAAO,SAAA;gCACP,aAAa,eAAA;gCACb,MAAM,EAAE,iBAAiB;gCACzB,cAAc,gBAAA;6BACf,CACF,CAAC;wBAEJ,IAAI,CAAC,gBAAgB,EAAE,CAAC;wBAElB,IAAI,GAAG,IAAI,CAAC;wBAElB,sBAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;gCACjC,OAAO,QAAQ,CACb,IAAI,CAAC,qBAAqB,CACxB,QAAQ,wBAEH,OAAO,KACV,kBAAkB,EAAE,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,KAEhE,SAAS,EACT,EAAE,EACF,KAAK,CACN,EAED,UAAC,MAA0B;oCACzB,IAAI,qBAAqB,CAAC,MAAM,CAAC,IAAI,WAAW,KAAK,MAAM,EAAE,CAAC;wCAC5D,MAAM,IAAI,WAAW,CAAC;4CACpB,aAAa,EAAE,0BAA0B,CAAC,MAAM,CAAC;yCAClD,CAAC,CAAC;oCACL,CAAC;oCAED,IAAI,kBAAkB,EAAE,CAAC;wCACvB,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;wCACnC,kBAAkB,CAAC,KAAK,GAAG,IAAI,CAAC;oCAClC,CAAC;oCAED,IAAM,WAAW,gBAAuB,MAAM,CAAE,CAAC;oCAEjD,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE,CAAC;wCACzC,cAAc,GAAG,cAAc,CAC7B,WAA2C,CAC5C,CAAC;oCACJ,CAAC;oCAED,IAAI,WAAW,KAAK,QAAQ,IAAI,qBAAqB,CAAC,WAAW,CAAC,EAAE,CAAC;wCACnE,OAAO,WAAW,CAAC,MAAM,CAAC;oCAC5B,CAAC;oCAED,OAAO,IAAI,CAAC,kBAAkB,CAAsC;wCAClE,UAAU,YAAA;wCACV,MAAM,EAAE,WAAW;wCACnB,QAAQ,EAAE,QAAQ;wCAClB,SAAS,WAAA;wCACT,WAAW,aAAA;wCACX,WAAW,aAAA;wCACX,OAAO,SAAA;wCACP,MAAM,EAAE,iBAAiB;wCACzB,aAAa,eAAA;wCACb,mBAAmB,qBAAA;wCACnB,cAAc,gBAAA;wCACd,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC;wCACpD,cAAc,gBAAA;wCACd,cAAc,gBAAA;qCACf,CAAC,CAAC;gCACL,CAAC,CACF,CAAC,SAAS,CAAC;oCACV,IAAI,YAAC,WAAW;wCACd,IAAI,CAAC,gBAAgB,EAAE,CAAC;wCAExB,2DAA2D;wCAC3D,iDAAiD;wCACjD,6DAA6D;wCAC7D,6DAA6D;wCAC7D,2CAA2C;wCAC3C,IAAI,CAAC,CAAC,SAAS,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;4CACjE,OAAO,uBACF,WAAW,KACd,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC;oDACvB,QAAQ,EAAE,QAAQ;oDAClB,IAAI,EAAE,WAAW,CAAC,IAAI;oDACtB,WAAW,aAAA;oDACX,EAAE,EAAE,UAAU;iDACf,CAAQ,IACT,CAAC;wCACL,CAAC;oCACH,CAAC;oCAED,KAAK,YAAC,GAAU;wCACd,IAAI,kBAAkB,EAAE,CAAC;4CACvB,kBAAkB,CAAC,OAAO,GAAG,KAAK,CAAC;4CACnC,kBAAkB,CAAC,KAAK,GAAG,GAAG,CAAC;wCACjC,CAAC;wCAED,IAAI,YAAY,EAAE,CAAC;4CACjB,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;wCAC1C,CAAC;wCAED,IAAI,CAAC,gBAAgB,EAAE,CAAC;wCAExB,MAAM,CACJ,GAAG,YAAY,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CACjC,IAAI,WAAW,CAAC;4CACd,YAAY,EAAE,GAAG;yCAClB,CAAC,CACH,CACF,CAAC;oCACJ,CAAC;iCACF,CAAC,CAAC;4BACL,CAAC,CAAC,EAAC;;;;KACJ;IAEM,yCAAkB,GAAzB,UAME,QAeC,EACD,KAAkB;QAtBpB,iBAiNC;QA3LC,sBAAA,EAAA,QAAQ,IAAI,CAAC,KAAK;QAEZ,IAAA,MAAM,GAAK,QAAQ,OAAb,CAAc;QAC1B,IAAM,WAAW,GAAyB,EAAE,CAAC;QAC7C,IAAM,SAAS,GAAG,QAAQ,CAAC,WAAW,KAAK,UAAU,CAAC;QAEtD,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAClE,IAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC/C,WAAW,CAAC,IAAI,CAAC;oBACf,MAAM,EAAE,MAAM,CAAC,IAAI;oBACnB,MAAM,EAAE,eAAe;oBACvB,KAAK,EAAE,QAAQ,CAAC,QAAQ;oBACxB,SAAS,EAAE,QAAQ,CAAC,SAAS;iBAC9B,CAAC,CAAC;YACL,CAAC;YACD,IACE,iCAAiC,CAAC,MAAM,CAAC;gBACzC,eAAe,CAAC,MAAM,CAAC,WAAW,CAAC,EACnC,CAAC;gBACD,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAQ;oBAC7B,EAAE,EAAE,eAAe;oBACnB,8DAA8D;oBAC9D,gEAAgE;oBAChE,8CAA8C;oBAC9C,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO;oBACtD,SAAS,EAAE,QAAQ,CAAC,SAAS;oBAC7B,UAAU,EAAE,KAAK;oBACjB,iBAAiB,EAAE,IAAI;iBACxB,CAAC,CAAC;gBACH,IAAI,UAAU,SAAA,CAAC;gBACf,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBAChB,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;gBACzD,CAAC;gBACD,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE,CAAC;oBACtC,0DAA0D;oBAC1D,sEAAsE;oBACrE,MAAsB,CAAC,IAAI,GAAG,UAAU,CAAC;oBAC1C,WAAW,CAAC,IAAI,CAAC;wBACf,MAAM,EAAE,UAAU;wBAClB,MAAM,EAAE,eAAe;wBACvB,KAAK,EAAE,QAAQ,CAAC,QAAQ;wBACxB,SAAS,EAAE,QAAQ,CAAC,SAAS;qBAC9B,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAEO,IAAA,eAAa,GAAK,QAAQ,cAAb,CAAc;YACnC,IAAI,eAAa,EAAE,CAAC;gBAClB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,EAAmB,EAAE,OAAO;wBAA1B,eAAe,qBAAA;oBACrC,IAAM,SAAS,GAAG,eAAe,IAAI,eAAe,CAAC,SAAS,CAAC;oBAC/D,IAAI,CAAC,SAAS,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAa,EAAE,SAAS,CAAC,EAAE,CAAC;wBACjE,OAAO;oBACT,CAAC;oBACD,IAAM,OAAO,GAAG,eAAa,CAAC,SAAS,CAAC,CAAC;oBACnC,IAAA,KAA0B,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAE,EAAlD,QAAQ,cAAA,EAAE,SAAS,eAA+B,CAAC;oBAE3D,gDAAgD;oBAC1C,IAAA,KAA2C,KAAK,CAAC,IAAI,CAAQ;wBACjE,KAAK,EAAE,QAAS;wBAChB,SAAS,WAAA;wBACT,iBAAiB,EAAE,IAAI;wBACvB,UAAU,EAAE,KAAK;qBAClB,CAAC,EALc,kBAAkB,YAAA,EAAE,QAAQ,cAK1C,CAAC;oBAEH,IAAI,QAAQ,IAAI,kBAAkB,EAAE,CAAC;wBACnC,0EAA0E;wBAC1E,IAAM,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE;4BAClD,cAAc,EAAE,MAAsC;4BACtD,SAAS,EAAE,CAAC,QAAQ,IAAI,gBAAgB,CAAC,QAAQ,CAAC,CAAC,IAAI,KAAK,CAAC;4BAC7D,cAAc,EAAE,SAAU;yBAC3B,CAAC,CAAC;wBAEH,wEAAwE;wBACxE,IAAI,eAAe,EAAE,CAAC;4BACpB,WAAW,CAAC,IAAI,CAAC;gCACf,MAAM,EAAE,eAAe;gCACvB,MAAM,EAAE,YAAY;gCACpB,KAAK,EAAE,QAAS;gCAChB,SAAS,WAAA;6BACV,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,IACE,WAAW,CAAC,MAAM,GAAG,CAAC;YACtB,CAAC,QAAQ,CAAC,cAAc,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;YAC1C,QAAQ,CAAC,MAAM;YACf,QAAQ,CAAC,cAAc;YACvB,QAAQ,CAAC,gBAAgB,EACzB,CAAC;YACD,IAAM,SAAO,GAAU,EAAE,CAAC;YAE1B,IAAI,CAAC,cAAc,CAAC;gBAClB,WAAW,EAAE,UAAC,KAAK;oBACjB,IAAI,CAAC,SAAS,EAAE,CAAC;wBACf,WAAW,CAAC,OAAO,CAAC,UAAC,KAAK,IAAK,OAAA,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,EAAlB,CAAkB,CAAC,CAAC;oBACrD,CAAC;oBAED,qEAAqE;oBACrE,qEAAqE;oBACrE,kBAAkB;oBACV,IAAA,MAAM,GAAK,QAAQ,OAAb,CAAc;oBAC5B,uDAAuD;oBACvD,qCAAqC;oBACrC,IAAM,aAAa,GACjB,CAAC,sBAAsB,CAAC,MAAM,CAAC;wBAC/B,CAAC,iCAAiC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAEjE,IAAI,MAAM,EAAE,CAAC;wBACX,IAAI,CAAC,SAAS,EAAE,CAAC;4BACf,8DAA8D;4BAC9D,8DAA8D;4BAC9D,2DAA2D;4BAC3D,yCAAyC;4BACzC,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAQ;gCAC7B,EAAE,EAAE,eAAe;gCACnB,8DAA8D;gCAC9D,gEAAgE;gCAChE,8CAA8C;gCAC9C,KAAK,EAAE,KAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO;gCACtD,SAAS,EAAE,QAAQ,CAAC,SAAS;gCAC7B,UAAU,EAAE,KAAK;gCACjB,iBAAiB,EAAE,IAAI;6BACxB,CAAC,CAAC;4BAEH,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gCAClB,MAAM,yBAAS,MAAsB,KAAE,IAAI,EAAE,IAAI,CAAC,MAAM,GAAE,CAAC;gCAC3D,IAAI,aAAa,IAAI,MAAM,EAAE,CAAC;oCAC5B,OAAO,MAAM,CAAC,WAAW,CAAC;gCAC5B,CAAC;gCACD,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;oCACxB,OAAO,MAAM,CAAC,OAAO,CAAC;gCACxB,CAAC;4BACH,CAAC;wBACH,CAAC;wBAED,wCAAwC;wBACxC,oEAAoE;wBACpE,4BAA4B;wBAC5B,IAAI,aAAa,EAAE,CAAC;4BAClB,MAAM,CAAC,KAAe,EAAE,MAAsC,EAAE;gCAC9D,OAAO,EAAE,QAAQ,CAAC,OAAO;gCACzB,SAAS,EAAE,QAAQ,CAAC,SAAS;6BAC9B,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;oBAED,qEAAqE;oBACrE,sDAAsD;oBACtD,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,aAAa,EAAE,CAAC;wBAC5D,KAAK,CAAC,MAAM,CAAC;4BACX,EAAE,EAAE,eAAe;4BACnB,MAAM,YAAC,KAAK,EAAE,EAAqB;oCAAnB,SAAS,eAAA,EAAE,MAAM,YAAA;gCAC/B,OAAO,SAAS,KAAK,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;4BACrD,CAAC;yBACF,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,OAAO,EAAE,QAAQ,CAAC,cAAc;gBAEhC,kEAAkE;gBAClE,UAAU,EAAE,KAAK;gBAEjB,mEAAmE;gBACnE,yCAAyC;gBACzC,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;gBAE3C,sEAAsE;gBACtE,wEAAwE;gBACxE,qEAAqE;gBACrE,wEAAwE;gBACxE,cAAc,EAAE,QAAQ,CAAC,cAAc,IAAI,IAAI;aAChD,CAAC,CAAC,OAAO,CAAC,UAAC,MAAM,IAAK,OAAA,SAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAApB,CAAoB,CAAC,CAAC;YAE7C,IAAI,QAAQ,CAAC,mBAAmB,IAAI,QAAQ,CAAC,cAAc,EAAE,CAAC;gBAC5D,wEAAwE;gBACxE,sEAAsE;gBACtE,yCAAyC;gBACzC,OAAO,OAAO,CAAC,GAAG,CAAC,SAAO,CAAC,CAAC,IAAI,CAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IAEM,6CAAsB,GAA7B,UAME,kBAAuB,EACvB,QAUC;QAjBH,iBA2CC;QAxBC,IAAM,IAAI,GACR,OAAO,kBAAkB,KAAK,UAAU,CAAC,CAAC;YACxC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,EAAE,EAAE,MAAM,QAAA,EAAE,CAAC;YACpD,CAAC,CAAC,kBAAkB,CAAC;QAEvB,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;YACpB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,UAAC,KAAK;YAC3C,IAAI,CAAC;gBACH,KAAI,CAAC,kBAAkB,uBAEhB,QAAQ,KACX,MAAM,EAAE,EAAE,IAAI,MAAA,EAAE,KAElB,KAAK,CACN,CAAC;YACJ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACzB,CAAC;QACH,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC,CAAC;QAExB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,iCAAU,GAAjB,UACE,OAAe,EACf,OAAwC,EACxC,aAA6B;QAE7B,OAAO,IAAI,CAAC,oBAAoB,CAC9B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAC9B,OAAO,EACP,aAAa,CACd,CAAC,OAAO,CAAC,OAAe,CAAC;IAC5B,CAAC;IAEM,oCAAa,GAApB;QACE,IAAM,KAAK,GAAoC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACnE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,OAAO;YACjC,KAAK,CAAC,OAAO,CAAC,GAAG;gBACf,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,aAAa,EAAE,IAAI,CAAC,aAAa;aAClC,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,OAAO,KAAK,CAAC;IACf,CAAC;IAEM,kCAAW,GAAlB,UAAmB,OAAe;QAChC,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,SAAS,EAAE,CAAC;YACd,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC;YACnC,SAAS,CAAC,aAAa,GAAG,EAAE,CAAC;QAC/B,CAAC;IACH,CAAC;IAEM,gCAAS,GAAhB,UAAiB,QAAsB;QACrC,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAUM,sCAAe,GAAtB,UAAuB,QAAsB;QACnC,IAAA,cAAc,GAAK,IAAI,eAAT,CAAU;QAEhC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,IAAM,UAAU,GAAwB;gBACtC,sEAAsE;gBACtE,uEAAuE;gBACvE,gEAAgE;gBAChE,kEAAkE;gBAClE,+BAA+B;gBAC/B,gBAAgB,EAAE,gBAAgB,CAAC,QAAQ,CAAC;gBAC5C,kBAAkB,EAAE,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,CAAC;gBAClE,uBAAuB,EAAE,aAAa,CAAC,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC;gBACjE,gBAAgB,EAAE,8BAA8B,CAAC,QAAQ,CAAC;gBAC1D,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC;gBAClD,WAAW,EAAE,4BAA4B,CACvC;oBACE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE;oBAChC,EAAE,IAAI,EAAE,YAAY,EAAE;oBACtB,EAAE,IAAI,EAAE,aAAa,EAAE;oBACvB,EAAE,IAAI,EAAE,QAAQ,EAAE;iBACnB,EACD,QAAQ,CACT;gBACD,WAAW,EAAE,gBAAgB,CAC3B,sBAAsB,CAAC,QAAQ,CAAC,CACX;gBACvB,wEAAwE;gBACxE,+CAA+C;gBAC/C,OAAO,wBACF,QAAQ,KACX,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,UAAC,GAAG;wBACxC,IACE,GAAG,CAAC,IAAI,KAAK,qBAAqB;4BAClC,GAAG,CAAC,SAAS,KAAK,OAAO,EACzB,CAAC;4BACD,6BAAY,GAAG,KAAE,SAAS,EAAE,OAA4B,IAAG;wBAC7D,CAAC;wBACD,OAAO,GAAG,CAAC;oBACb,CAAC,CAAC,GACH;aACF,CAAC;YAEF,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;IACvC,CAAC;IAEO,mCAAY,GAApB,UACE,QAAsB,EACtB,SAAsB;QAEtB,6BACK,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,WAAW,GAC1C,SAAS,EACZ;IACJ,CAAC;IAEM,iCAAU,GAAjB,UAGE,OAAyC;QACzC,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE5C,6CAA6C;QAC7C,yEAAyE;QACzE,yEAAyE;QACzE,OAAO,yBACF,OAAO,KACV,SAAS,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAe,GACrE,CAAC;QAEF,IAAI,OAAO,OAAO,CAAC,2BAA2B,KAAK,WAAW,EAAE,CAAC;YAC/D,OAAO,CAAC,2BAA2B,GAAG,KAAK,CAAC;QAC9C,CAAC;QAED,IAAM,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;QACtC,IAAM,UAAU,GAAG,IAAI,eAAe,CAAgB;YACpD,YAAY,EAAE,IAAI;YAClB,SAAS,WAAA;YACT,OAAO,SAAA;SACR,CAAC,CAAC;QACH,UAAU,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;QAEhC,IAAI,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;YACtD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;QAClD,CAAC;QAED,yEAAyE;QACzE,sDAAsD;QACtD,SAAS,CAAC,IAAI,CAAC;YACb,QAAQ,EAAE,KAAK;YACf,eAAe,EAAE,UAAU;YAC3B,SAAS,EAAE,UAAU,CAAC,SAAS;SAChC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAEM,4BAAK,GAAZ,UACE,OAAmC,EACnC,OAAgC;QAFlC,iBAyCC;QAvCC,wBAAA,EAAA,UAAU,IAAI,CAAC,eAAe,EAAE;QAEhC,SAAS,CACP,OAAO,CAAC,KAAK,EACb,mEAAmE;YACjE,sBAAsB,CACzB,CAAC;QAEF,SAAS,CACP,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,EACjC,gDAAgD,CACjD,CAAC;QAEF,SAAS,CACP,CAAE,OAAe,CAAC,iBAAiB,EACnC,wDAAwD,CACzD,CAAC;QAEF,SAAS,CACP,CAAE,OAAe,CAAC,YAAY,EAC9B,mDAAmD,CACpD,CAAC;QAEF,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAE5C,OAAO,IAAI,CAAC,UAAU,CAAe,OAAO,wBAAO,OAAO,KAAE,KAAK,OAAA,IAAG;aACjE,IAAI,CACH,UAAC,MAAM;YACL,OAAA,MAAM,0BACD,MAAM,KACT,IAAI,EAAE,KAAI,CAAC,aAAa,CAAC;oBACvB,QAAQ,EAAE,KAAK;oBACf,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,WAAW,EAAE,OAAO,CAAC,WAAW;oBAChC,EAAE,EAAE,OAAO;iBACZ,CAAC,GACH;QARD,CAQC,CACJ;aACA,OAAO,CAAC,cAAM,OAAA,KAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAvB,CAAuB,CAAC,CAAC;IAC5C,CAAC;IAGM,sCAAe,GAAtB;QACE,OAAO,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACvC,CAAC;IAGM,wCAAiB,GAAxB;QACE,OAAO,IAAI,CAAC,gBAAgB,EAAE,CAAC;IACjC,CAAC;IAGM,yCAAkB,GAAzB;QACE,OAAO,MAAM,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;IAC1C,CAAC;IAEM,uCAAgB,GAAvB,UAAwB,OAAe;QACrC,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,kDAA2B,GAAnC,UAAoC,OAAe;QACjD,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,SAAS;YAAE,SAAS,CAAC,IAAI,EAAE,CAAC;IAClC,CAAC;IAEM,iCAAU,GAAjB,UACE,OAEC;QAFD,wBAAA,EAAA;YACE,cAAc,EAAE,IAAI;SACrB;QAED,sEAAsE;QACtE,qEAAqE;QACrE,sEAAsE;QACtE,sEAAsE;QACtE,wDAAwD;QACxD,IAAI,CAAC,oBAAoB,CACvB,iBAAiB,CACf,qEAAqE,CACtE,CACF,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,SAAS;YAC7B,IAAI,SAAS,CAAC,eAAe,EAAE,CAAC;gBAC9B,kEAAkE;gBAClE,6BAA6B;gBAC7B,SAAS,CAAC,aAAa,GAAG,aAAa,CAAC,OAAO,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,IAAI,EAAE,CAAC;YACnB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC3C,CAAC;QAED,qCAAqC;QACrC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAEM,2CAAoB,GAA3B,UACE,OAAiD;QADnD,iBAkGC;QAjGC,wBAAA,EAAA,kBAAiD;QAEjD,IAAM,OAAO,GAAG,IAAI,GAAG,EAAgC,CAAC;QACxD,IAAM,UAAU,GAAG,IAAI,GAAG,EAAyB,CAAC;QACpD,IAAM,yBAAyB,GAAG,IAAI,GAAG,EAAmB,CAAC;QAC7D,IAAM,kBAAkB,GAAG,IAAI,GAAG,EAAgB,CAAC;QAEnD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3B,OAAO,CAAC,OAAO,CAAC,UAAC,IAAI;gBACnB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC7B,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAC3B,yBAAyB,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;gBAC7C,CAAC;qBAAM,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;oBAChC,IAAM,WAAW,GAAG,KAAK,CAAC,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;oBAChD,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;oBACpD,yBAAyB,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBACpD,CAAC;qBAAM,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBAC/C,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC/B,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,EAAiC,EAAE,OAAO;gBAAvB,EAAE,qBAAA,EAAE,QAAQ,cAAA;YACnD,IAAI,EAAE,EAAE,CAAC;gBACP,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;oBACtB,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;oBACzB,OAAO;gBACT,CAAC;gBAGC,IAAA,SAAS,GAEP,EAAE,UAFK,EACE,WAAW,GACpB,EAAE,oBADkB,CACjB;gBAEP,IACE,WAAW,KAAK,SAAS;oBACzB,CAAC,OAAO,KAAK,QAAQ,IAAI,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,EAC5C,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,IACE,OAAO,KAAK,QAAQ;oBACpB,CAAC,SAAS,IAAI,yBAAyB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBACvD,CAAC,QAAQ,IAAI,yBAAyB,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC5D,CAAC;oBACD,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;oBACzB,IAAI,SAAS;wBAAE,yBAAyB,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;oBAC9D,IAAI,QAAQ;wBAAE,yBAAyB,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,kBAAkB,CAAC,IAAI,EAAE,CAAC;YAC5B,kBAAkB,CAAC,OAAO,CAAC,UAAC,OAAqB;gBAC/C,mEAAmE;gBACnE,qEAAqE;gBACrE,6DAA6D;gBAC7D,IAAM,OAAO,GAAG,YAAY,CAAC,oBAAoB,CAAC,CAAC;gBACnD,IAAM,SAAS,GAAG,KAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;oBACpD,QAAQ,EAAE,OAAO,CAAC,KAAK;oBACvB,SAAS,EAAE,OAAO,CAAC,SAAS;iBAC7B,CAAC,CAAC;gBACH,IAAM,EAAE,GAAG,IAAI,eAAe,CAAC;oBAC7B,YAAY,EAAE,KAAI;oBAClB,SAAS,WAAA;oBACT,OAAO,wBACF,OAAO,KACV,WAAW,EAAE,cAAc,GAC5B;iBACF,CAAC,CAAC;gBACH,SAAS,CAAC,EAAE,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC;gBAClC,SAAS,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;gBACjC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAC3B,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,OAAO,IAAI,yBAAyB,CAAC,IAAI,EAAE,CAAC;YAC9C,yBAAyB,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,iBAAiB;gBAC5D,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,IAAM,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;oBAEpD,IAAI,SAAS,EAAE,CAAC;wBACd,SAAS,CAAC,IAAI,CACZ,8EAA4E,EAC5E,SAAS,CACV,CAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,SAAS,CAAC,IAAI,CACZ,2EAA2E,CAC5E,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,+CAAwB,GAA/B,UACE,cAA+B;QADjC,iBAwBC;QAvBC,+BAAA,EAAA,sBAA+B;QAE/B,IAAM,uBAAuB,GAAsC,EAAE,CAAC;QAEtE,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAClE,UAAC,eAAe,EAAE,OAAO;YACf,IAAA,WAAW,GAAK,eAAe,CAAC,OAAO,YAA5B,CAA6B;YAChD,eAAe,CAAC,gBAAgB,EAAE,CAAC;YACnC,IACE,cAAc;gBACd,CAAC,WAAW,KAAK,SAAS,IAAI,WAAW,KAAK,YAAY,CAAC,EAC3D,CAAC;gBACD,uBAAuB,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1D,CAAC;YACD,CAAC,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,eAAe,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CACjE,IAAI,CACL,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,OAAO,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IAC9C,CAAC;IAEM,+CAAwB,GAA/B,UACE,OAA4B;QAD9B,iBA2EC;QAxEO,IAAA,KAAK,GAAgB,OAAO,MAAvB,EAAE,SAAS,GAAK,OAAO,UAAZ,CAAa;QAEjC,IAAA,WAAW,GAIT,OAAO,YAJE,EACX,KAGE,OAAO,YAHW,EAApB,WAAW,mBAAG,MAAM,KAAA,EACpB,KAEE,OAAO,QAFG,EAAZ,OAAO,mBAAG,EAAE,KAAA,EACZ,KACE,OAAO,WADM,EAAf,UAAU,mBAAG,EAAE,KAAA,CACL;QAEZ,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC9B,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAEhD,IAAM,cAAc,GAAG,UAAC,SAA6B;YACnD,OAAA,KAAI,CAAC,qBAAqB,CAAI,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,GAAG,CACtE,UAAC,MAAM;gBACL,IAAI,WAAW,KAAK,UAAU,EAAE,CAAC;oBAC/B,6FAA6F;oBAC7F,yFAAyF;oBACzF,IAAI,iBAAiB,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,CAAC;wBAC3C,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC;4BACf,KAAK,OAAA;4BACL,MAAM,EAAE,MAAM,CAAC,IAAI;4BACnB,MAAM,EAAE,mBAAmB;4BAC3B,SAAS,EAAE,SAAS;yBACrB,CAAC,CAAC;oBACL,CAAC;oBAED,KAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC1B,CAAC;gBAED,IAAM,SAAS,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC;gBAChD,IAAM,iBAAiB,GAAG,8BAA8B,CAAC,MAAM,CAAC,CAAC;gBACjE,IAAI,SAAS,IAAI,iBAAiB,EAAE,CAAC;oBACnC,IAAM,MAAM,GAAuB,EAAE,CAAC;oBACtC,IAAI,SAAS,EAAE,CAAC;wBACd,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;oBACvC,CAAC;oBACD,IAAI,iBAAiB,EAAE,CAAC;wBACtB,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC,CAAC;oBACpE,CAAC;oBAED,sEAAsE;oBACtE,sEAAsE;oBACtE,oBAAoB;oBACpB,IAAI,WAAW,KAAK,MAAM,IAAI,iBAAiB,EAAE,CAAC;wBAChD,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC;oBAChC,CAAC;gBACH,CAAC;gBAED,IAAI,WAAW,KAAK,QAAQ,EAAE,CAAC;oBAC7B,OAAO,MAAM,CAAC,MAAM,CAAC;gBACvB,CAAC;gBAED,OAAO,MAAM,CAAC;YAChB,CAAC,CACF;QA1CD,CA0CC,CAAC;QAEJ,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;YACjD,IAAM,mBAAiB,GAAG,IAAI,CAAC,UAAU;iBACtC,oBAAoB,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC;iBAC/C,IAAI,CAAC,cAAc,CAAC,CAAC;YAExB,OAAO,IAAI,UAAU,CAAiB,UAAC,QAAQ;gBAC7C,IAAI,GAAG,GAAkC,IAAI,CAAC;gBAC9C,mBAAiB,CAAC,IAAI,CACpB,UAAC,UAAU,IAAK,OAAA,CAAC,GAAG,GAAG,UAAU,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAtC,CAAsC,EACtD,QAAQ,CAAC,KAAK,CACf,CAAC;gBACF,OAAO,cAAM,OAAA,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE,EAAxB,CAAwB,CAAC;YACxC,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,cAAc,CAAC,SAAS,CAAC,CAAC;IACnC,CAAC;IAEM,gCAAS,GAAhB,UAAiB,OAAe;QAC9B,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QACnC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,2CAAoB,GAA5B,UAA6B,OAAe;QAC1C,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC5B,CAAC;IAEM,kCAAW,GAAlB,UAAmB,OAAe;;QAChC,qBAAqB;QACrB,qFAAqF;QACrF,0DAA0D;QAC1D,2FAA2F;QAC3F,iEAAiE;QACjE,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9B,MAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,0CAAE,IAAI,EAAE,CAAC;YAClC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAEM,uCAAgB,GAAvB;QACE,IAAI,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,EAAE,CAAC;QACzC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,IAAI,YAAK,OAAA,MAAA,IAAI,CAAC,eAAe,0CAAG,QAAQ,GAAG,CAAA,EAAA,CAAC,CAAC;IACrE,CAAC;IAEM,oCAAa,GAApB;QACE,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAQO,4CAAqB,GAA7B,UACE,KAAmB,EACnB,OAAY,EACZ,SAA8B,EAC9B,UAAgC;IAChC,kDAAkD;IAClD,aACyB;QAP3B,iBA0EC;;QApEC,8BAAA,EAAA,sBAAyB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,kBAAkB,mCAClD,IAAI,CAAC,kBAAkB;QAEzB,IAAI,UAAkD,CAAC;QAEjD,IAAA,KAA+B,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAxD,WAAW,iBAAA,EAAE,WAAW,iBAAgC,CAAC;QACjE,IAAI,WAAW,EAAE,CAAC;YACV,IAAA,KAAoC,IAAI,EAAtC,yBAAuB,6BAAA,EAAE,IAAI,UAAS,CAAC;YAE/C,IAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,WAAW;gBAClB,SAAS,WAAA;gBACT,aAAa,EAAE,gBAAgB,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;gBACtD,OAAO,EAAE,IAAI,CAAC,cAAc,uBACvB,OAAO,KACV,UAAU,EAAE,CAAC,aAAa,IAC1B;gBACF,UAAU,YAAA;aACX,CAAC;YAEF,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC;YAE5B,IAAI,aAAa,EAAE,CAAC;gBAClB,IAAM,oBAAkB,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;gBAC9C,IAAM,SAAO,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;gBAE9C,IAAM,KAAK,GAAG,yBAAuB,CAAC,MAAM,CAC1C,oBAAkB,EAClB,SAAO,CACR,CAAC;gBAEF,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;gBAC9B,IAAI,CAAC,UAAU,EAAE,CAAC;oBAChB,IAAM,SAAO,GAAG,IAAI,OAAO,CAAC;wBAC1B,OAAO,CAAC,IAAI,EAAE,SAAS,CAA+B;qBACvD,CAAC,CAAC;oBACH,UAAU,GAAG,KAAK,CAAC,UAAU,GAAG,SAAO,CAAC;oBAExC,SAAO,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,GAAgB;wBACrD,IAAI,MAAM,KAAK,MAAM,IAAI,SAAS,IAAI,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;4BACzD,SAAO,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;wBACzB,CAAC;6BAAM,CAAC;4BACN,yBAAuB,CAAC,MAAM,CAAC,oBAAkB,EAAE,SAAO,CAAC,CAAC;wBAC9D,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,IAAI,OAAO,CAAC;oBACvB,OAAO,CAAC,IAAI,EAAE,SAAS,CAA+B;iBACvD,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAoB,CAAC,CAAC,CAAC,CAAC;YAC1E,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,WAAW,EAAE,CAAC;YAChB,UAAU,GAAG,QAAQ,CAAC,UAAU,EAAE,UAAC,MAAM;gBACvC,OAAO,KAAI,CAAC,UAAU,CAAC,YAAY,CAAC;oBAClC,QAAQ,EAAE,WAAW;oBACrB,YAAY,EAAE,MAAM;oBACpB,OAAO,SAAA;oBACP,SAAS,WAAA;iBACV,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,yCAAkB,GAA1B,UACE,SAAoB,EACpB,kBAAsC,EACtC,OAGC;QAED,IAAM,SAAS,GAAG,CAAC,SAAS,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QAEvE,wEAAwE;QACxE,0EAA0E;QAC1E,0BAA0B;QAC1B,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAEhE,OAAO,QAAQ,CACb,IAAI,CAAC,qBAAqB,CACxB,YAAY,EACZ,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,SAAS,CAClB,EAED,UAAC,MAAM;YACL,IAAM,aAAa,GAAG,0BAA0B,CAAC,MAAM,CAAC,CAAC;YACzD,IAAM,SAAS,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;YACnC,IAAA,WAAW,GAAK,OAAO,YAAZ,CAAa;YAEhC,qEAAqE;YACrE,6DAA6D;YAC7D,IAAI,SAAS,IAAI,SAAS,CAAC,aAAa,EAAE,CAAC;gBACzC,IAAI,SAAS,IAAI,WAAW,KAAK,MAAM,EAAE,CAAC;oBACxC,kDAAkD;oBAClD,MAAM,SAAS,CAAC,SAAS,CACvB,IAAI,WAAW,CAAC;wBACd,aAAa,eAAA;qBACd,CAAC,CACH,CAAC;gBACJ,CAAC;gBACD,yDAAyD;gBACzD,mEAAmE;gBACnE,wCAAwC;gBACxC,SAAS,CAAC,UAAU,CAClB,MAAM,EACN,YAAY,EACZ,OAAO,EACP,kBAAkB,CACnB,CAAC;gBACF,SAAS,CAAC,SAAS,EAAE,CAAC;YACxB,CAAC;YAED,IAAM,GAAG,GAA6B;gBACpC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,KAAK;gBACd,aAAa,EAAE,aAAa,CAAC,KAAK;aACnC,CAAC;YAEF,oEAAoE;YACpE,qEAAqE;YACrE,wEAAwE;YACxE,iCAAiC;YACjC,IAAI,SAAS,IAAI,WAAW,KAAK,MAAM,EAAE,CAAC;gBACxC,GAAG,CAAC,IAAI,GAAG,KAAK,CAAU,CAAC;YAC7B,CAAC;YAED,IAAI,SAAS,IAAI,WAAW,KAAK,QAAQ,EAAE,CAAC;gBAC1C,GAAG,CAAC,MAAM,GAAG,aAAa,CAAC;gBAC3B,GAAG,CAAC,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC;YAC1C,CAAC;YAED,OAAO,GAAG,CAAC;QACb,CAAC,EAED,UAAC,YAAY;YACX,IAAM,KAAK,GACT,aAAa,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAC3C,IAAI,WAAW,CAAC,EAAE,YAAY,cAAA,EAAE,CAAC,CAClC,CAAC;YAEJ,uDAAuD;YACvD,IAAI,SAAS,IAAI,SAAS,CAAC,aAAa,EAAE,CAAC;gBACzC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC7B,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC,CACF,CAAC;IACJ,CAAC;IAEO,2CAAoB,GAA5B,UACE,SAAoB,EACpB,OAAwC;IACxC,uDAAuD;IACvD,qEAAqE;IACrE,mBAAmB;IACnB,aAAqC,EACrC,KAAqB;QAPvB,iBA2GC;QArGC,8BAAA,EAAA,gBAAgB,aAAa,CAAC,OAAO;QACrC,sBAAA,EAAA,QAAQ,OAAO,CAAC,KAAK;QAErB,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAU,CAAC;QAEvE,IAAM,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;QAE9C,IAAA,KAKE,OAAO,YALwD,EAAjE,WAAW,mBAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,aAAa,KAAA,EACjE,KAIE,OAAO,YAJiD,EAA1D,WAAW,mBAAG,CAAC,QAAQ,IAAI,QAAQ,CAAC,WAAW,CAAC,IAAI,MAAM,KAAA,EAC1D,KAGE,OAAO,kBAHgB,EAAzB,iBAAiB,mBAAG,KAAK,KAAA,EACzB,KAEE,OAAO,4BAF0B,EAAnC,2BAA2B,mBAAG,KAAK,KAAA,EACnC,KACE,OAAO,QADG,EAAZ,OAAO,mBAAG,EAAE,KAAA,CACF;QAEZ,IAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,EAAE;YAC5C,KAAK,OAAA;YACL,SAAS,WAAA;YACT,WAAW,aAAA;YACX,WAAW,aAAA;YACX,iBAAiB,mBAAA;YACjB,2BAA2B,6BAAA;YAC3B,OAAO,SAAA;SACR,CAAC,CAAC;QAEH,IAAM,aAAa,GAAG,UAAC,SAAgB;YACrC,mEAAmE;YACnE,mEAAmE;YACnE,4BAA4B;YAC5B,UAAU,CAAC,SAAS,GAAG,SAAS,CAAC;YAEjC,IAAM,eAAe,GAAG,KAAI,CAAC,kBAAkB,CAC7C,SAAS,EACT,UAAU,EACV,aAAa,CACd,CAAC;YAEF;YACE,oEAAoE;YACpE,wBAAwB;YACxB,UAAU,CAAC,WAAW,KAAK,SAAS;gBACpC,wEAAwE;gBACxE,+DAA+D;gBAC/D,eAAe,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;gBAClC,SAAS,CAAC,eAAe,EACzB,CAAC;gBACD,SAAS,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAC/C,aAAa,EACb,OAAO,CACR,CAAC;YACJ,CAAC;YAED,OAAO,eAAe,CAAC;QACzB,CAAC,CAAC;QAEF,sEAAsE;QACtE,8DAA8D;QAC9D,IAAM,eAAe,GAAG,cAAM,OAAA,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,EAA7C,CAA6C,CAAC;QAC5E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,OAAO,EAAE,UAAC,MAAM;YAChD,eAAe,EAAE,CAAC;YAClB,gEAAgE;YAChE,UAAU,CAAC,cAAM,OAAA,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAtB,CAAsB,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAI,OAA0C,EAC5C,oBAA6B,CAAC;QAChC,gEAAgE;QAChE,6DAA6D;QAC7D,8DAA8D;QAC9D,+DAA+D;QAC/D,mEAAmE;QACnE,kEAAkE;QAClE,wCAAwC;QACxC,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAC5D,OAAO,GAAG,IAAI,OAAO,CACnB,IAAI,CAAC,UAAU;iBACZ,oBAAoB,CACnB,UAAU,CAAC,KAAK,EAChB,UAAU,CAAC,SAAS,EACpB,UAAU,CAAC,OAAO,CACnB;iBACA,IAAI,CAAC,aAAa,CAAC;iBACnB,IAAI,CAAC,UAAC,eAAe,IAAK,OAAA,eAAe,CAAC,OAAO,EAAvB,CAAuB,CAAC,CACtD,CAAC;YACF,wEAAwE;YACxE,yEAAyE;YACzE,yEAAyE;YACzE,oEAAoE;YACpE,cAAc;YACd,oBAAoB,GAAG,IAAI,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,IAAM,eAAe,GAAG,aAAa,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YAC5D,oBAAoB,GAAG,eAAe,CAAC,QAAQ,CAAC;YAChD,OAAO,GAAG,IAAI,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;QAEvD,OAAO;YACL,OAAO,SAAA;YACP,QAAQ,EAAE,oBAAoB;SAC/B,CAAC;IACJ,CAAC;IAEM,qCAAc,GAArB,UAA+B,EAS9B;QATD,iBAmKC;YAlKC,WAAW,iBAAA,EACX,OAAO,aAAA,EACP,kBAAkB,EAAlB,UAAU,mBAAG,KAAK,KAAA,EAClB,wBAAuE,EAAvE,gBAAgB,mBAAG,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAA,EACvE,cAAc,oBAAA;QAKd,IAAM,mBAAmB,GAAG,IAAI,GAAG,EAOhC,CAAC;QAEJ,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,EAAE,EAAE,OAAO;gBACrD,mBAAmB,CAAC,GAAG,CAAC,OAAO,EAAE;oBAC/B,EAAE,IAAA;oBACF,QAAQ,EAAE,CAAC,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,EAAE;iBACnE,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAM,OAAO,GAAuC,IAAI,GAAG,EAAE,CAAC;QAE9D,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;gBACf,MAAM,EAAE,WAAW;gBAEnB,wEAAwE;gBACxE,uEAAuE;gBACvE,8DAA8D;gBAC9D,aAAa;gBACb,EAAE;gBACF,0CAA0C;gBAC1C,uCAAuC;gBACvC,oEAAoE;gBACpE,EAAE;gBACF,sEAAsE;gBACtE,wEAAwE;gBACxE,uEAAuE;gBACvE,iEAAiE;gBACjE,mEAAmE;gBACnE,EAAE;gBACF,wEAAwE;gBACxE,wEAAwE;gBACxE,wEAAwE;gBACxE,mEAAmE;gBACnE,uEAAuE;gBACvE,4CAA4C;gBAC5C,EAAE;gBACF,qEAAqE;gBACrE,sEAAsE;gBACtE,qEAAqE;gBACrE,oEAAoE;gBACpE,sEAAsE;gBACtE,kEAAkE;gBAClE,qDAAqD;gBACrD,UAAU,EAAE,CAAC,UAAU,IAAI,gBAAgB,CAAC,IAAI,KAAK;gBAErD,sEAAsE;gBACtE,mEAAmE;gBACnE,kEAAkE;gBAClE,EAAE;gBACF,sEAAsE;gBACtE,iEAAiE;gBACjE,yDAAyD;gBACzD,gBAAgB,kBAAA;gBAEhB,cAAc,YAAC,KAAK,EAAE,IAAI,EAAE,QAAQ;oBAClC,IAAM,EAAE,GACN,KAAK,CAAC,OAAO,YAAY,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC;oBAEtE,IAAI,EAAE,EAAE,CAAC;wBACP,IAAI,cAAc,EAAE,CAAC;4BACnB,6DAA6D;4BAC7D,+DAA+D;4BAC/D,mBAAmB;4BACnB,mBAAmB,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;4BAEvC,IAAI,MAAM,GACR,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;4BAErC,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;gCACpB,+DAA+D;gCAC/D,8BAA8B;gCAC9B,MAAM,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;4BACxB,CAAC;4BAED,kEAAkE;4BAClE,mDAAmD;4BACnD,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;gCACrB,OAAO,CAAC,GAAG,CACT,EAAE,EACF,MAA+C,CAChD,CAAC;4BACJ,CAAC;4BAED,2DAA2D;4BAC3D,gCAAgC;4BAChC,OAAO,MAAM,CAAC;wBAChB,CAAC;wBAED,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;4BAC5B,kEAAkE;4BAClE,4DAA4D;4BAC5D,6DAA6D;4BAC7D,mBAAmB,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,EAAE,IAAA,EAAE,QAAQ,UAAA,EAAE,IAAI,MAAA,EAAE,CAAC,CAAC;wBAC9D,CAAC;oBACH,CAAC;gBACH,CAAC;aACF,CAAC,CAAC;QACL,CAAC;QAED,IAAI,mBAAmB,CAAC,IAAI,EAAE,CAAC;YAC7B,mBAAmB,CAAC,OAAO,CAAC,UAAC,EAAsB,EAAE,OAAO;oBAA7B,EAAE,QAAA,EAAE,QAAQ,cAAA,EAAE,IAAI,UAAA;gBAC/C,IAAI,MAIS,CAAC;gBAEd,oEAAoE;gBACpE,uCAAuC;gBACvC,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAI,CAAC,IAAI,EAAE,CAAC;wBACV,IAAI,GAAG,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;oBAC9D,CAAC;oBACD,MAAM,GAAG,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;gBAC9C,CAAC;gBAED,yCAAyC;gBACzC,IAAI,CAAC,cAAc,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;oBACvC,MAAM,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;gBACxB,CAAC;gBAED,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;oBACrB,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,MAA+C,CAAC,CAAC;gBACnE,CAAC;gBAED,IAAI,OAAO,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC/C,KAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBACrC,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,IAAI,gBAAgB,EAAE,CAAC;YACrB,uEAAuE;YACvE,uEAAuE;YACvE,wEAAwE;YACxE,oEAAoE;YACpE,wEAAwE;YACxE,sEAAsE;YACtE,0BAA0B;YAC1B,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAIM,oCAAa,GAApB,UACE,OAAoC;;QAE5B,IAAA,QAAQ,GAAW,OAAO,SAAlB,EAAE,IAAI,GAAK,OAAO,KAAZ,CAAa;QAEnC,IAAI,OAAO,EAAE,CAAC;YACJ,IAAA,WAAW,GAAS,OAAO,YAAhB,EAAE,EAAE,GAAK,OAAO,GAAZ,CAAa;YACpC,IAAM,aAAa,GAAG,MAAA,sBAAsB,CAAC,QAAQ,CAAC,0CAAE,SAAS,CAAC;YAClE,IAAM,WAAW,GAAG,CAAC,MAAA,aAAa,aAAb,aAAa,uBAAb,aAAa,CAAG,CAAC,CAAC,mCAAI,GAAG,CAAC,GAAG,EAAE,CAAC;YAErD,IACE,IAAI,CAAC,WAAW;gBAChB,WAAW,KAAK,UAAU;gBAC1B,CAAC,wBAAwB,CAAC,QAAQ,CAAC;gBACnC,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,WAAW,CAAC,EAC/C,CAAC;gBACD,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAE/C,SAAS,CAAC,IAAI,CACZ,8JAA8J,EAC9J,MAAA,gBAAgB,CAAC,QAAQ,CAAC,mCACxB,kBAAW,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,WAAW,CAAE,CAC5C,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,CACL,IAAI,CAAC,WAAW,CAAC,CAAC;YAChB,aAAa,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC;YAC3C,CAAC,CAAC,IAAI,CAAuB,CAAC;IAClC,CAAC;IAEM,mCAAY,GAAnB,UAAqC,OAAmC;QAC9D,IAAA,IAAI,GAA6B,OAAO,KAApC,EAAE,QAAQ,GAAmB,OAAO,SAA1B,EAAE,YAAY,GAAK,OAAO,aAAZ,CAAa;QAEjD,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC;YACrB,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,KAAK,EAAE,YAAY,CAAC;YACxD,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAEO,yCAAkB,GAA1B,UACE,SAAoB,EACpB,EASkC;IAClC,uDAAuD;IACvD,qEAAqE;IACrE,mBAAmB;IACnB,aAA4B;QAf9B,iBAqKC;YAlKG,KAAK,WAAA,EACL,SAAS,eAAA,EACT,WAAW,iBAAA,EACX,kBAAkB,wBAAA,EAClB,WAAW,iBAAA,EACX,iBAAiB,uBAAA,EACjB,OAAO,aAAA,EACP,2BAA2B,iCAAA;QAO7B,IAAM,gBAAgB,GAAG,SAAS,CAAC,aAAa,CAAC;QAEjD,SAAS,CAAC,IAAI,CAAC;YACb,QAAQ,EAAE,KAAK;YACf,SAAS,WAAA;YACT,aAAa,eAAA;SACd,CAAC,CAAC;QAEH,IAAM,SAAS,GAAG,cAAM,OAAA,SAAS,CAAC,OAAO,EAAE,EAAnB,CAAmB,CAAC;QAE5C,IAAM,gBAAgB,GAAG,UACvB,IAA6B,EAC7B,aAAgE;YAAhE,8BAAA,EAAA,gBAAgB,SAAS,CAAC,aAAa,IAAI,aAAa,CAAC,OAAO;YAEhE,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YAEzB,IAAI,OAAO,IAAI,CAAC,iBAAiB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC;gBACtD,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtC,CAAC;YAED,IAAM,QAAQ,GAAG,UAAC,IAAuB;gBACvC,OAAA,UAAU,CAAC,EAAE,CAAC,WACZ,IAAI,MAAA,EACJ,OAAO,EAAE,wBAAwB,CAAC,aAAa,CAAC,EAChD,aAAa,eAAA,IACV,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAClB,CAAC;YAL9B,CAK8B,CAAC;YAEjC,IAAI,IAAI,IAAI,KAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,CAAC;gBAC3D,OAAO,KAAI,CAAC,UAAU;qBACnB,YAAY,CAAC;oBACZ,QAAQ,EAAE,KAAK;oBACf,YAAY,EAAE,EAAE,IAAI,MAAA,EAAE;oBACtB,OAAO,SAAA;oBACP,SAAS,WAAA;oBACT,sBAAsB,EAAE,IAAI;iBAC7B,CAAC;qBACD,IAAI,CAAC,UAAC,QAAQ,IAAK,OAAA,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,EAAjC,CAAiC,CAAC,CAAC;YAC3D,CAAC;YAED,wEAAwE;YACxE,oEAAoE;YACpE,2DAA2D;YAC3D,2DAA2D;YAC3D,IACE,WAAW,KAAK,MAAM;gBACtB,aAAa,KAAK,aAAa,CAAC,OAAO;gBACvC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAC3B,CAAC;gBACD,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1B,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC,CAAC;QAEF,IAAM,kBAAkB,GACtB,WAAW,KAAK,UAAU,CAAC,CAAC;YAC1B,sEAAsE;YACtE,yEAAyE;YAC3E,CAAC,CAAC,CACA,aAAa,KAAK,aAAa,CAAC,OAAO;gBACvC,kBAAkB,KAAK,OAAO,CAC/B,CAAC,CAAC;;gBAEH,CAAC,iCAAyB,CAAC;QAE7B,IAAM,eAAe,GAAG;YACtB,OAAA,KAAI,CAAC,kBAAkB,CAAe,SAAS,EAAE,kBAAkB,EAAE;gBACnE,KAAK,OAAA;gBACL,SAAS,WAAA;gBACT,OAAO,SAAA;gBACP,WAAW,aAAA;gBACX,WAAW,aAAA;aACZ,CAAC;QANF,CAME,CAAC;QAEL,IAAM,YAAY,GAChB,2BAA2B;YAC3B,OAAO,gBAAgB,KAAK,QAAQ;YACpC,gBAAgB,KAAK,aAAa;YAClC,wBAAwB,CAAC,aAAa,CAAC,CAAC;QAE1C,QAAQ,WAAW,EAAE,CAAC;YACpB,QAAQ;YACR,KAAK,aAAa,CAAC,CAAC,CAAC;gBACnB,IAAM,IAAI,GAAG,SAAS,EAAE,CAAC;gBAEzB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAClB,OAAO;wBACL,QAAQ,EAAE,KAAK;wBACf,OAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;qBACzD,CAAC;gBACJ,CAAC;gBAED,IAAI,iBAAiB,IAAI,YAAY,EAAE,CAAC;oBACtC,OAAO;wBACL,QAAQ,EAAE,IAAI;wBACd,OAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,eAAe,EAAE,CAAC;qBACrD,CAAC;gBACJ,CAAC;gBAED,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC;YAC1D,CAAC;YAED,KAAK,mBAAmB,CAAC,CAAC,CAAC;gBACzB,IAAM,IAAI,GAAG,SAAS,EAAE,CAAC;gBAEzB,IAAI,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,YAAY,EAAE,CAAC;oBACvD,OAAO;wBACL,QAAQ,EAAE,IAAI;wBACd,OAAO,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE,eAAe,EAAE,CAAC;qBACrD,CAAC;gBACJ,CAAC;gBAED,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC;YAC1D,CAAC;YAED,KAAK,YAAY;gBACf,OAAO;oBACL,QAAQ,EAAE,KAAK;oBACf,OAAO,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;iBAChE,CAAC;YAEJ,KAAK,cAAc;gBACjB,IAAI,YAAY,EAAE,CAAC;oBACjB,OAAO;wBACL,QAAQ,EAAE,IAAI;wBACd,OAAO,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC;qBAC5D,CAAC;gBACJ,CAAC;gBAED,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC;YAE1D,KAAK,UAAU;gBACb,IAAI,YAAY,EAAE,CAAC;oBACjB,OAAO;wBACL,QAAQ,EAAE,IAAI;wBACd,mEAAmE;wBACnE,oEAAoE;wBACpE,mDAAmD;wBACnD,OAAO,EAAE,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC;qBACpE,CAAC;gBACJ,CAAC;gBAED,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,eAAe,EAAE,CAAC,EAAE,CAAC;YAE1D,KAAK,SAAS;gBACZ,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;IAEM,uCAAgB,GAAvB,UAAwB,OAAe;QACrC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;IACpC,CAAC;IAEO,qCAAc,GAAtB,UAAuB,OAAY;QAAZ,wBAAA,EAAA,YAAY;QACjC,IAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QAC3D,sCACK,IAAI,CAAC,cAAc,GACnB,UAAU,KACb,eAAe,EAAE,IAAI,CAAC,eAAe,IACrC;IACJ,CAAC;IACH,mBAAC;AAAD,CAAC,AAhoDD,IAgoDC", "sourcesContent": ["import { invariant, newInvariantError } from \"../utilities/globals/index.js\";\n\nimport type { DocumentNode } from \"graphql\";\n// TODO(brian): A hack until this issue is resolved (https://github.com/graphql/graphql-js/issues/3356)\ntype OperationTypeNode = any;\nimport { equal } from \"@wry/equality\";\n\nimport type { ApolloLink, FetchResult } from \"../link/core/index.js\";\nimport { execute } from \"../link/core/index.js\";\nimport {\n  addNonReactiveToNamedFragments,\n  defaultCacheSizes,\n  hasDirectives,\n  isExecutionPatchIncrementalResult,\n  isExecutionPatchResult,\n  isFullyUnmaskedOperation,\n  removeDirectivesFromDocument,\n} from \"../utilities/index.js\";\nimport type { Cache, ApolloCache } from \"../cache/index.js\";\nimport { canonicalStringify } from \"../cache/index.js\";\n\nimport type {\n  ObservableSubscription,\n  ConcastSourcesArray,\n} from \"../utilities/index.js\";\nimport {\n  getDefaultValues,\n  getOperationDefinition,\n  getOperationName,\n  hasClientExports,\n  graphQLResultHasError,\n  getGraphQLErrorsFromResult,\n  Observable,\n  asyncMap,\n  isNonEmptyArray,\n  Concast,\n  makeUniqueId,\n  isDocumentNode,\n  isNonNullObject,\n  DocumentTransform,\n} from \"../utilities/index.js\";\nimport { mergeIncrementalData } from \"../utilities/common/incrementalResult.js\";\nimport {\n  ApolloError,\n  isApolloError,\n  graphQLResultHasProtocolErrors,\n} from \"../errors/index.js\";\nimport type {\n  QueryOptions,\n  WatchQueryOptions,\n  SubscriptionOptions,\n  MutationOptions,\n  ErrorPolicy,\n  MutationFetchPolicy,\n  WatchQueryFetchPolicy,\n} from \"./watchQueryOptions.js\";\nimport { ObservableQuery, logMissingFieldErrors } from \"./ObservableQuery.js\";\nimport { NetworkStatus, isNetworkRequestInFlight } from \"./networkStatus.js\";\nimport type {\n  ApolloQueryResult,\n  OperationVariables,\n  MutationUpdaterFunction,\n  OnQueryUpdated,\n  InternalRefetchQueriesInclude,\n  InternalRefetchQueriesOptions,\n  InternalRefetchQueriesResult,\n  InternalRefetchQueriesMap,\n  DefaultContext,\n} from \"./types.js\";\nimport type { LocalState } from \"./LocalState.js\";\n\nimport type { QueryStoreValue } from \"./QueryInfo.js\";\nimport {\n  QueryInfo,\n  shouldWriteResult,\n  CacheWriteBehavior,\n} from \"./QueryInfo.js\";\nimport type { ApolloErrorOptions } from \"../errors/index.js\";\nimport { PROTOCOL_ERRORS_SYMBOL } from \"../errors/index.js\";\nimport { print } from \"../utilities/index.js\";\nimport type { IgnoreModifier } from \"../cache/core/types/common.js\";\nimport type { TODO } from \"../utilities/types/TODO.js\";\n\nconst { hasOwnProperty } = Object.prototype;\n\nconst IGNORE: IgnoreModifier = Object.create(null);\n\ninterface MutationStoreValue {\n  mutation: DocumentNode;\n  variables: Record<string, any>;\n  loading: boolean;\n  error: Error | null;\n}\n\ntype UpdateQueries<TData> = MutationOptions<TData, any, any>[\"updateQueries\"];\n\ninterface TransformCacheEntry {\n  hasClientExports: boolean;\n  hasForcedResolvers: boolean;\n  hasNonreactiveDirective: boolean;\n  nonReactiveQuery: DocumentNode;\n  clientQuery: DocumentNode | null;\n  serverQuery: DocumentNode | null;\n  defaultVars: OperationVariables;\n  asQuery: DocumentNode;\n}\n\nimport type { DefaultOptions } from \"./ApolloClient.js\";\nimport { Trie } from \"@wry/trie\";\nimport { AutoCleanedWeakCache, cacheSizes } from \"../utilities/index.js\";\nimport { maskFragment, maskOperation } from \"../masking/index.js\";\nimport type { MaybeMasked, Unmasked } from \"../masking/index.js\";\n\ninterface MaskFragmentOptions<TData> {\n  fragment: DocumentNode;\n  data: TData;\n  fragmentName?: string;\n}\n\ninterface MaskOperationOptions<TData> {\n  document: DocumentNode;\n  data: TData;\n  id: string;\n  fetchPolicy?: WatchQueryFetchPolicy;\n}\n\nexport interface QueryManagerOptions<TStore> {\n  cache: ApolloCache<TStore>;\n  link: ApolloLink;\n  defaultOptions: DefaultOptions;\n  documentTransform: DocumentTransform | null | undefined;\n  queryDeduplication: boolean;\n  onBroadcast: undefined | (() => void);\n  ssrMode: boolean;\n  clientAwareness: Record<string, string>;\n  localState: LocalState<TStore>;\n  assumeImmutableResults: boolean;\n  defaultContext: Partial<DefaultContext> | undefined;\n  dataMasking: boolean;\n}\n\nexport class QueryManager<TStore> {\n  public cache: ApolloCache<TStore>;\n  public link: ApolloLink;\n  public defaultOptions: DefaultOptions;\n\n  public readonly assumeImmutableResults: boolean;\n  public readonly documentTransform: DocumentTransform;\n  public readonly ssrMode: boolean;\n  public readonly defaultContext: Partial<DefaultContext>;\n  public readonly dataMasking: boolean;\n\n  private queryDeduplication: boolean;\n  private clientAwareness: Record<string, string> = {};\n  private localState: LocalState<TStore>;\n\n  private onBroadcast?: () => void;\n  public mutationStore?: {\n    [mutationId: string]: MutationStoreValue;\n  };\n\n  // All the queries that the QueryManager is currently managing (not\n  // including mutations and subscriptions).\n  private queries = new Map<string, QueryInfo>();\n\n  // Maps from queryId strings to Promise rejection functions for\n  // currently active queries and fetches.\n  // Use protected instead of private field so\n  // @apollo/experimental-nextjs-app-support can access type info.\n  protected fetchCancelFns = new Map<string, (error: any) => any>();\n\n  constructor(options: QueryManagerOptions<TStore>) {\n    const defaultDocumentTransform = new DocumentTransform(\n      (document) => this.cache.transformDocument(document),\n      // Allow the apollo cache to manage its own transform caches\n      { cache: false }\n    );\n\n    this.cache = options.cache;\n    this.link = options.link;\n    this.defaultOptions = options.defaultOptions;\n    this.queryDeduplication = options.queryDeduplication;\n    this.clientAwareness = options.clientAwareness;\n    this.localState = options.localState;\n    this.ssrMode = options.ssrMode;\n    this.assumeImmutableResults = options.assumeImmutableResults;\n    this.dataMasking = options.dataMasking;\n    const documentTransform = options.documentTransform;\n    this.documentTransform =\n      documentTransform ?\n        defaultDocumentTransform\n          .concat(documentTransform)\n          // The custom document transform may add new fragment spreads or new\n          // field selections, so we want to give the cache a chance to run\n          // again. For example, the InMemoryCache adds __typename to field\n          // selections and fragments from the fragment registry.\n          .concat(defaultDocumentTransform)\n      : defaultDocumentTransform;\n    this.defaultContext = options.defaultContext || Object.create(null);\n\n    if ((this.onBroadcast = options.onBroadcast)) {\n      this.mutationStore = Object.create(null);\n    }\n  }\n\n  /**\n   * Call this method to terminate any active query processes, making it safe\n   * to dispose of this QueryManager instance.\n   */\n  public stop() {\n    this.queries.forEach((_info, queryId) => {\n      this.stopQueryNoBroadcast(queryId);\n    });\n\n    this.cancelPendingFetches(\n      newInvariantError(\"QueryManager stopped while query was in flight\")\n    );\n  }\n\n  private cancelPendingFetches(error: Error) {\n    this.fetchCancelFns.forEach((cancel) => cancel(error));\n    this.fetchCancelFns.clear();\n  }\n\n  public async mutate<\n    TData,\n    TVariables extends OperationVariables,\n    TContext extends Record<string, any>,\n    TCache extends ApolloCache<any>,\n  >({\n    mutation,\n    variables,\n    optimisticResponse,\n    updateQueries,\n    refetchQueries = [],\n    awaitRefetchQueries = false,\n    update: updateWithProxyFn,\n    onQueryUpdated,\n    fetchPolicy = this.defaultOptions.mutate?.fetchPolicy || \"network-only\",\n    errorPolicy = this.defaultOptions.mutate?.errorPolicy || \"none\",\n    keepRootFields,\n    context,\n  }: MutationOptions<TData, TVariables, TContext>): Promise<\n    FetchResult<MaybeMasked<TData>>\n  > {\n    invariant(\n      mutation,\n      \"mutation option is required. You must specify your GraphQL document in the mutation option.\"\n    );\n\n    invariant(\n      fetchPolicy === \"network-only\" || fetchPolicy === \"no-cache\",\n      \"Mutations support only 'network-only' or 'no-cache' fetchPolicy strings. The default `network-only` behavior automatically writes mutation results to the cache. Passing `no-cache` skips the cache write.\"\n    );\n\n    const mutationId = this.generateMutationId();\n\n    mutation = this.cache.transformForLink(this.transform(mutation));\n    const { hasClientExports } = this.getDocumentInfo(mutation);\n\n    variables = this.getVariables(mutation, variables) as TVariables;\n    if (hasClientExports) {\n      variables = (await this.localState.addExportedVariables(\n        mutation,\n        variables,\n        context\n      )) as TVariables;\n    }\n\n    const mutationStoreValue =\n      this.mutationStore &&\n      (this.mutationStore[mutationId] = {\n        mutation,\n        variables,\n        loading: true,\n        error: null,\n      } as MutationStoreValue);\n\n    const isOptimistic =\n      optimisticResponse &&\n      this.markMutationOptimistic<TData, TVariables, TContext, TCache>(\n        optimisticResponse,\n        {\n          mutationId,\n          document: mutation,\n          variables,\n          fetchPolicy,\n          errorPolicy,\n          context,\n          updateQueries,\n          update: updateWithProxyFn,\n          keepRootFields,\n        }\n      );\n\n    this.broadcastQueries();\n\n    const self = this;\n\n    return new Promise((resolve, reject) => {\n      return asyncMap(\n        self.getObservableFromLink(\n          mutation,\n          {\n            ...context,\n            optimisticResponse: isOptimistic ? optimisticResponse : void 0,\n          },\n          variables,\n          {},\n          false\n        ),\n\n        (result: FetchResult<TData>) => {\n          if (graphQLResultHasError(result) && errorPolicy === \"none\") {\n            throw new ApolloError({\n              graphQLErrors: getGraphQLErrorsFromResult(result),\n            });\n          }\n\n          if (mutationStoreValue) {\n            mutationStoreValue.loading = false;\n            mutationStoreValue.error = null;\n          }\n\n          const storeResult: typeof result = { ...result };\n\n          if (typeof refetchQueries === \"function\") {\n            refetchQueries = refetchQueries(\n              storeResult as FetchResult<Unmasked<TData>>\n            );\n          }\n\n          if (errorPolicy === \"ignore\" && graphQLResultHasError(storeResult)) {\n            delete storeResult.errors;\n          }\n\n          return self.markMutationResult<TData, TVariables, TContext, TCache>({\n            mutationId,\n            result: storeResult,\n            document: mutation,\n            variables,\n            fetchPolicy,\n            errorPolicy,\n            context,\n            update: updateWithProxyFn,\n            updateQueries,\n            awaitRefetchQueries,\n            refetchQueries,\n            removeOptimistic: isOptimistic ? mutationId : void 0,\n            onQueryUpdated,\n            keepRootFields,\n          });\n        }\n      ).subscribe({\n        next(storeResult) {\n          self.broadcastQueries();\n\n          // Since mutations might receive multiple payloads from the\n          // ApolloLink chain (e.g. when used with @defer),\n          // we resolve with a SingleExecutionResult or after the final\n          // ExecutionPatchResult has arrived and we have assembled the\n          // multipart response into a single result.\n          if (!(\"hasNext\" in storeResult) || storeResult.hasNext === false) {\n            resolve({\n              ...storeResult,\n              data: self.maskOperation({\n                document: mutation,\n                data: storeResult.data,\n                fetchPolicy,\n                id: mutationId,\n              }) as any,\n            });\n          }\n        },\n\n        error(err: Error) {\n          if (mutationStoreValue) {\n            mutationStoreValue.loading = false;\n            mutationStoreValue.error = err;\n          }\n\n          if (isOptimistic) {\n            self.cache.removeOptimistic(mutationId);\n          }\n\n          self.broadcastQueries();\n\n          reject(\n            err instanceof ApolloError ? err : (\n              new ApolloError({\n                networkError: err,\n              })\n            )\n          );\n        },\n      });\n    });\n  }\n\n  public markMutationResult<\n    TData,\n    TVariables,\n    TContext,\n    TCache extends ApolloCache<any>,\n  >(\n    mutation: {\n      mutationId: string;\n      result: FetchResult<TData>;\n      document: DocumentNode;\n      variables?: TVariables;\n      fetchPolicy?: MutationFetchPolicy;\n      errorPolicy: ErrorPolicy;\n      context?: TContext;\n      updateQueries: UpdateQueries<TData>;\n      update?: MutationUpdaterFunction<TData, TVariables, TContext, TCache>;\n      awaitRefetchQueries?: boolean;\n      refetchQueries?: InternalRefetchQueriesInclude;\n      removeOptimistic?: string;\n      onQueryUpdated?: OnQueryUpdated<any>;\n      keepRootFields?: boolean;\n    },\n    cache = this.cache\n  ): Promise<FetchResult<TData>> {\n    let { result } = mutation;\n    const cacheWrites: Cache.WriteOptions[] = [];\n    const skipCache = mutation.fetchPolicy === \"no-cache\";\n\n    if (!skipCache && shouldWriteResult(result, mutation.errorPolicy)) {\n      if (!isExecutionPatchIncrementalResult(result)) {\n        cacheWrites.push({\n          result: result.data,\n          dataId: \"ROOT_MUTATION\",\n          query: mutation.document,\n          variables: mutation.variables,\n        });\n      }\n      if (\n        isExecutionPatchIncrementalResult(result) &&\n        isNonEmptyArray(result.incremental)\n      ) {\n        const diff = cache.diff<TData>({\n          id: \"ROOT_MUTATION\",\n          // The cache complains if passed a mutation where it expects a\n          // query, so we transform mutations and subscriptions to queries\n          // (only once, thanks to this.transformCache).\n          query: this.getDocumentInfo(mutation.document).asQuery,\n          variables: mutation.variables,\n          optimistic: false,\n          returnPartialData: true,\n        });\n        let mergedData;\n        if (diff.result) {\n          mergedData = mergeIncrementalData(diff.result, result);\n        }\n        if (typeof mergedData !== \"undefined\") {\n          // cast the ExecutionPatchResult to FetchResult here since\n          // ExecutionPatchResult never has `data` when returned from the server\n          (result as FetchResult).data = mergedData;\n          cacheWrites.push({\n            result: mergedData,\n            dataId: \"ROOT_MUTATION\",\n            query: mutation.document,\n            variables: mutation.variables,\n          });\n        }\n      }\n\n      const { updateQueries } = mutation;\n      if (updateQueries) {\n        this.queries.forEach(({ observableQuery }, queryId) => {\n          const queryName = observableQuery && observableQuery.queryName;\n          if (!queryName || !hasOwnProperty.call(updateQueries, queryName)) {\n            return;\n          }\n          const updater = updateQueries[queryName];\n          const { document, variables } = this.queries.get(queryId)!;\n\n          // Read the current query result from the store.\n          const { result: currentQueryResult, complete } = cache.diff<TData>({\n            query: document!,\n            variables,\n            returnPartialData: true,\n            optimistic: false,\n          });\n\n          if (complete && currentQueryResult) {\n            // Run our reducer using the current query result and the mutation result.\n            const nextQueryResult = updater(currentQueryResult, {\n              mutationResult: result as FetchResult<Unmasked<TData>>,\n              queryName: (document && getOperationName(document)) || void 0,\n              queryVariables: variables!,\n            });\n\n            // Write the modified result back into the store if we got a new result.\n            if (nextQueryResult) {\n              cacheWrites.push({\n                result: nextQueryResult,\n                dataId: \"ROOT_QUERY\",\n                query: document!,\n                variables,\n              });\n            }\n          }\n        });\n      }\n    }\n\n    if (\n      cacheWrites.length > 0 ||\n      (mutation.refetchQueries || \"\").length > 0 ||\n      mutation.update ||\n      mutation.onQueryUpdated ||\n      mutation.removeOptimistic\n    ) {\n      const results: any[] = [];\n\n      this.refetchQueries({\n        updateCache: (cache) => {\n          if (!skipCache) {\n            cacheWrites.forEach((write) => cache.write(write));\n          }\n\n          // If the mutation has some writes associated with it then we need to\n          // apply those writes to the store by running this reducer again with\n          // a write action.\n          const { update } = mutation;\n          // Determine whether result is a SingleExecutionResult,\n          // or the final ExecutionPatchResult.\n          const isFinalResult =\n            !isExecutionPatchResult(result) ||\n            (isExecutionPatchIncrementalResult(result) && !result.hasNext);\n\n          if (update) {\n            if (!skipCache) {\n              // Re-read the ROOT_MUTATION data we just wrote into the cache\n              // (the first cache.write call in the cacheWrites.forEach loop\n              // above), so field read functions have a chance to run for\n              // fields within mutation result objects.\n              const diff = cache.diff<TData>({\n                id: \"ROOT_MUTATION\",\n                // The cache complains if passed a mutation where it expects a\n                // query, so we transform mutations and subscriptions to queries\n                // (only once, thanks to this.transformCache).\n                query: this.getDocumentInfo(mutation.document).asQuery,\n                variables: mutation.variables,\n                optimistic: false,\n                returnPartialData: true,\n              });\n\n              if (diff.complete) {\n                result = { ...(result as FetchResult), data: diff.result };\n                if (\"incremental\" in result) {\n                  delete result.incremental;\n                }\n                if (\"hasNext\" in result) {\n                  delete result.hasNext;\n                }\n              }\n            }\n\n            // If we've received the whole response,\n            // either a SingleExecutionResult or the final ExecutionPatchResult,\n            // call the update function.\n            if (isFinalResult) {\n              update(cache as TCache, result as FetchResult<Unmasked<TData>>, {\n                context: mutation.context,\n                variables: mutation.variables,\n              });\n            }\n          }\n\n          // TODO Do this with cache.evict({ id: 'ROOT_MUTATION' }) but make it\n          // shallow to allow rolling back optimistic evictions.\n          if (!skipCache && !mutation.keepRootFields && isFinalResult) {\n            cache.modify({\n              id: \"ROOT_MUTATION\",\n              fields(value, { fieldName, DELETE }) {\n                return fieldName === \"__typename\" ? value : DELETE;\n              },\n            });\n          }\n        },\n\n        include: mutation.refetchQueries,\n\n        // Write the final mutation.result to the root layer of the cache.\n        optimistic: false,\n\n        // Remove the corresponding optimistic layer at the same time as we\n        // write the final non-optimistic result.\n        removeOptimistic: mutation.removeOptimistic,\n\n        // Let the caller of client.mutate optionally determine the refetching\n        // behavior for watched queries after the mutation.update function runs.\n        // If no onQueryUpdated function was provided for this mutation, pass\n        // null instead of undefined to disable the default refetching behavior.\n        onQueryUpdated: mutation.onQueryUpdated || null,\n      }).forEach((result) => results.push(result));\n\n      if (mutation.awaitRefetchQueries || mutation.onQueryUpdated) {\n        // Returning a promise here makes the mutation await that promise, so we\n        // include results in that promise's work if awaitRefetchQueries or an\n        // onQueryUpdated function was specified.\n        return Promise.all(results).then(() => result);\n      }\n    }\n\n    return Promise.resolve(result);\n  }\n\n  public markMutationOptimistic<\n    TData,\n    TVariables,\n    TContext,\n    TCache extends ApolloCache<any>,\n  >(\n    optimisticResponse: any,\n    mutation: {\n      mutationId: string;\n      document: DocumentNode;\n      variables?: TVariables;\n      fetchPolicy?: MutationFetchPolicy;\n      errorPolicy: ErrorPolicy;\n      context?: TContext;\n      updateQueries: UpdateQueries<TData>;\n      update?: MutationUpdaterFunction<TData, TVariables, TContext, TCache>;\n      keepRootFields?: boolean;\n    }\n  ) {\n    const data =\n      typeof optimisticResponse === \"function\" ?\n        optimisticResponse(mutation.variables, { IGNORE })\n      : optimisticResponse;\n\n    if (data === IGNORE) {\n      return false;\n    }\n\n    this.cache.recordOptimisticTransaction((cache) => {\n      try {\n        this.markMutationResult<TData, TVariables, TContext, TCache>(\n          {\n            ...mutation,\n            result: { data },\n          },\n          cache\n        );\n      } catch (error) {\n        invariant.error(error);\n      }\n    }, mutation.mutationId);\n\n    return true;\n  }\n\n  public fetchQuery<TData, TVars extends OperationVariables>(\n    queryId: string,\n    options: WatchQueryOptions<TVars, TData>,\n    networkStatus?: NetworkStatus\n  ): Promise<ApolloQueryResult<TData>> {\n    return this.fetchConcastWithInfo(\n      this.getOrCreateQuery(queryId),\n      options,\n      networkStatus\n    ).concast.promise as TODO;\n  }\n\n  public getQueryStore() {\n    const store: Record<string, QueryStoreValue> = Object.create(null);\n    this.queries.forEach((info, queryId) => {\n      store[queryId] = {\n        variables: info.variables,\n        networkStatus: info.networkStatus,\n        networkError: info.networkError,\n        graphQLErrors: info.graphQLErrors,\n      };\n    });\n    return store;\n  }\n\n  public resetErrors(queryId: string) {\n    const queryInfo = this.queries.get(queryId);\n    if (queryInfo) {\n      queryInfo.networkError = undefined;\n      queryInfo.graphQLErrors = [];\n    }\n  }\n\n  public transform(document: DocumentNode) {\n    return this.documentTransform.transformDocument(document);\n  }\n\n  private transformCache = new AutoCleanedWeakCache<\n    DocumentNode,\n    TransformCacheEntry\n  >(\n    cacheSizes[\"queryManager.getDocumentInfo\"] ||\n      defaultCacheSizes[\"queryManager.getDocumentInfo\"]\n  );\n\n  public getDocumentInfo(document: DocumentNode) {\n    const { transformCache } = this;\n\n    if (!transformCache.has(document)) {\n      const cacheEntry: TransformCacheEntry = {\n        // TODO These three calls (hasClientExports, shouldForceResolvers, and\n        // usesNonreactiveDirective) are performing independent full traversals\n        // of the transformed document. We should consider merging these\n        // traversals into a single pass in the future, though the work is\n        // cached after the first time.\n        hasClientExports: hasClientExports(document),\n        hasForcedResolvers: this.localState.shouldForceResolvers(document),\n        hasNonreactiveDirective: hasDirectives([\"nonreactive\"], document),\n        nonReactiveQuery: addNonReactiveToNamedFragments(document),\n        clientQuery: this.localState.clientQuery(document),\n        serverQuery: removeDirectivesFromDocument(\n          [\n            { name: \"client\", remove: true },\n            { name: \"connection\" },\n            { name: \"nonreactive\" },\n            { name: \"unmask\" },\n          ],\n          document\n        ),\n        defaultVars: getDefaultValues(\n          getOperationDefinition(document)\n        ) as OperationVariables,\n        // Transform any mutation or subscription operations to query operations\n        // so we can read/write them from/to the cache.\n        asQuery: {\n          ...document,\n          definitions: document.definitions.map((def) => {\n            if (\n              def.kind === \"OperationDefinition\" &&\n              def.operation !== \"query\"\n            ) {\n              return { ...def, operation: \"query\" as OperationTypeNode };\n            }\n            return def;\n          }),\n        },\n      };\n\n      transformCache.set(document, cacheEntry);\n    }\n\n    return transformCache.get(document)!;\n  }\n\n  private getVariables<TVariables>(\n    document: DocumentNode,\n    variables?: TVariables\n  ): OperationVariables {\n    return {\n      ...this.getDocumentInfo(document).defaultVars,\n      ...variables,\n    };\n  }\n\n  public watchQuery<\n    T,\n    TVariables extends OperationVariables = OperationVariables,\n  >(options: WatchQueryOptions<TVariables, T>): ObservableQuery<T, TVariables> {\n    const query = this.transform(options.query);\n\n    // assign variable default values if supplied\n    // NOTE: We don't modify options.query here with the transformed query to\n    // ensure observable.options.query is set to the raw untransformed query.\n    options = {\n      ...options,\n      variables: this.getVariables(query, options.variables) as TVariables,\n    };\n\n    if (typeof options.notifyOnNetworkStatusChange === \"undefined\") {\n      options.notifyOnNetworkStatusChange = false;\n    }\n\n    const queryInfo = new QueryInfo(this);\n    const observable = new ObservableQuery<T, TVariables>({\n      queryManager: this,\n      queryInfo,\n      options,\n    });\n    observable[\"lastQuery\"] = query;\n\n    if (!ObservableQuery[\"inactiveOnCreation\"].getValue()) {\n      this.queries.set(observable.queryId, queryInfo);\n    }\n\n    // We give queryInfo the transformed query to ensure the first cache diff\n    // uses the transformed query instead of the raw query\n    queryInfo.init({\n      document: query,\n      observableQuery: observable,\n      variables: observable.variables,\n    });\n\n    return observable;\n  }\n\n  public query<TData, TVars extends OperationVariables = OperationVariables>(\n    options: QueryOptions<TVars, TData>,\n    queryId = this.generateQueryId()\n  ): Promise<ApolloQueryResult<MaybeMasked<TData>>> {\n    invariant(\n      options.query,\n      \"query option is required. You must specify your GraphQL document \" +\n        \"in the query option.\"\n    );\n\n    invariant(\n      options.query.kind === \"Document\",\n      'You must wrap the query string in a \"gql\" tag.'\n    );\n\n    invariant(\n      !(options as any).returnPartialData,\n      \"returnPartialData option only supported on watchQuery.\"\n    );\n\n    invariant(\n      !(options as any).pollInterval,\n      \"pollInterval option only supported on watchQuery.\"\n    );\n\n    const query = this.transform(options.query);\n\n    return this.fetchQuery<TData, TVars>(queryId, { ...options, query })\n      .then(\n        (result) =>\n          result && {\n            ...result,\n            data: this.maskOperation({\n              document: query,\n              data: result.data,\n              fetchPolicy: options.fetchPolicy,\n              id: queryId,\n            }),\n          }\n      )\n      .finally(() => this.stopQuery(queryId));\n  }\n\n  private queryIdCounter = 1;\n  public generateQueryId() {\n    return String(this.queryIdCounter++);\n  }\n\n  private requestIdCounter = 1;\n  public generateRequestId() {\n    return this.requestIdCounter++;\n  }\n\n  private mutationIdCounter = 1;\n  public generateMutationId() {\n    return String(this.mutationIdCounter++);\n  }\n\n  public stopQueryInStore(queryId: string) {\n    this.stopQueryInStoreNoBroadcast(queryId);\n    this.broadcastQueries();\n  }\n\n  private stopQueryInStoreNoBroadcast(queryId: string) {\n    const queryInfo = this.queries.get(queryId);\n    if (queryInfo) queryInfo.stop();\n  }\n\n  public clearStore(\n    options: Cache.ResetOptions = {\n      discardWatches: true,\n    }\n  ): Promise<void> {\n    // Before we have sent the reset action to the store, we can no longer\n    // rely on the results returned by in-flight requests since these may\n    // depend on values that previously existed in the data portion of the\n    // store. So, we cancel the promises and observers that we have issued\n    // so far and not yet resolved (in the case of queries).\n    this.cancelPendingFetches(\n      newInvariantError(\n        \"Store reset while query was in flight (not completed in link chain)\"\n      )\n    );\n\n    this.queries.forEach((queryInfo) => {\n      if (queryInfo.observableQuery) {\n        // Set loading to true so listeners don't trigger unless they want\n        // results with partial data.\n        queryInfo.networkStatus = NetworkStatus.loading;\n      } else {\n        queryInfo.stop();\n      }\n    });\n\n    if (this.mutationStore) {\n      this.mutationStore = Object.create(null);\n    }\n\n    // begin removing data from the store\n    return this.cache.reset(options);\n  }\n\n  public getObservableQueries(\n    include: InternalRefetchQueriesInclude = \"active\"\n  ) {\n    const queries = new Map<string, ObservableQuery<any>>();\n    const queryNames = new Map<string, string | null>();\n    const queryNamesAndQueryStrings = new Map<string, boolean>();\n    const legacyQueryOptions = new Set<QueryOptions>();\n\n    if (Array.isArray(include)) {\n      include.forEach((desc) => {\n        if (typeof desc === \"string\") {\n          queryNames.set(desc, desc);\n          queryNamesAndQueryStrings.set(desc, false);\n        } else if (isDocumentNode(desc)) {\n          const queryString = print(this.transform(desc));\n          queryNames.set(queryString, getOperationName(desc));\n          queryNamesAndQueryStrings.set(queryString, false);\n        } else if (isNonNullObject(desc) && desc.query) {\n          legacyQueryOptions.add(desc);\n        }\n      });\n    }\n\n    this.queries.forEach(({ observableQuery: oq, document }, queryId) => {\n      if (oq) {\n        if (include === \"all\") {\n          queries.set(queryId, oq);\n          return;\n        }\n\n        const {\n          queryName,\n          options: { fetchPolicy },\n        } = oq;\n\n        if (\n          fetchPolicy === \"standby\" ||\n          (include === \"active\" && !oq.hasObservers())\n        ) {\n          return;\n        }\n\n        if (\n          include === \"active\" ||\n          (queryName && queryNamesAndQueryStrings.has(queryName)) ||\n          (document && queryNamesAndQueryStrings.has(print(document)))\n        ) {\n          queries.set(queryId, oq);\n          if (queryName) queryNamesAndQueryStrings.set(queryName, true);\n          if (document) queryNamesAndQueryStrings.set(print(document), true);\n        }\n      }\n    });\n\n    if (legacyQueryOptions.size) {\n      legacyQueryOptions.forEach((options: QueryOptions) => {\n        // We will be issuing a fresh network request for this query, so we\n        // pre-allocate a new query ID here, using a special prefix to enable\n        // cleaning up these temporary queries later, after fetching.\n        const queryId = makeUniqueId(\"legacyOneTimeQuery\");\n        const queryInfo = this.getOrCreateQuery(queryId).init({\n          document: options.query,\n          variables: options.variables,\n        });\n        const oq = new ObservableQuery({\n          queryManager: this,\n          queryInfo,\n          options: {\n            ...options,\n            fetchPolicy: \"network-only\",\n          },\n        });\n        invariant(oq.queryId === queryId);\n        queryInfo.setObservableQuery(oq);\n        queries.set(queryId, oq);\n      });\n    }\n\n    if (__DEV__ && queryNamesAndQueryStrings.size) {\n      queryNamesAndQueryStrings.forEach((included, nameOrQueryString) => {\n        if (!included) {\n          const queryName = queryNames.get(nameOrQueryString);\n\n          if (queryName) {\n            invariant.warn(\n              `Unknown query named \"%s\" requested in refetchQueries options.include array`,\n              queryName\n            );\n          } else {\n            invariant.warn(\n              `Unknown anonymous query requested in refetchQueries options.include array`\n            );\n          }\n        }\n      });\n    }\n\n    return queries;\n  }\n\n  public reFetchObservableQueries(\n    includeStandby: boolean = false\n  ): Promise<ApolloQueryResult<any>[]> {\n    const observableQueryPromises: Promise<ApolloQueryResult<any>>[] = [];\n\n    this.getObservableQueries(includeStandby ? \"all\" : \"active\").forEach(\n      (observableQuery, queryId) => {\n        const { fetchPolicy } = observableQuery.options;\n        observableQuery.resetLastResults();\n        if (\n          includeStandby ||\n          (fetchPolicy !== \"standby\" && fetchPolicy !== \"cache-only\")\n        ) {\n          observableQueryPromises.push(observableQuery.refetch());\n        }\n        (this.queries.get(queryId) || observableQuery[\"queryInfo\"]).setDiff(\n          null\n        );\n      }\n    );\n\n    this.broadcastQueries();\n\n    return Promise.all(observableQueryPromises);\n  }\n\n  public startGraphQLSubscription<T = any>(\n    options: SubscriptionOptions\n  ): Observable<FetchResult<T>> {\n    let { query, variables } = options;\n    const {\n      fetchPolicy,\n      errorPolicy = \"none\",\n      context = {},\n      extensions = {},\n    } = options;\n\n    query = this.transform(query);\n    variables = this.getVariables(query, variables);\n\n    const makeObservable = (variables: OperationVariables) =>\n      this.getObservableFromLink<T>(query, context, variables, extensions).map(\n        (result) => {\n          if (fetchPolicy !== \"no-cache\") {\n            // the subscription interface should handle not sending us results we no longer subscribe to.\n            // XXX I don't think we ever send in an object with errors, but we might in the future...\n            if (shouldWriteResult(result, errorPolicy)) {\n              this.cache.write({\n                query,\n                result: result.data,\n                dataId: \"ROOT_SUBSCRIPTION\",\n                variables: variables,\n              });\n            }\n\n            this.broadcastQueries();\n          }\n\n          const hasErrors = graphQLResultHasError(result);\n          const hasProtocolErrors = graphQLResultHasProtocolErrors(result);\n          if (hasErrors || hasProtocolErrors) {\n            const errors: ApolloErrorOptions = {};\n            if (hasErrors) {\n              errors.graphQLErrors = result.errors;\n            }\n            if (hasProtocolErrors) {\n              errors.protocolErrors = result.extensions[PROTOCOL_ERRORS_SYMBOL];\n            }\n\n            // `errorPolicy` is a mechanism for handling GraphQL errors, according\n            // to our documentation, so we throw protocol errors regardless of the\n            // set error policy.\n            if (errorPolicy === \"none\" || hasProtocolErrors) {\n              throw new ApolloError(errors);\n            }\n          }\n\n          if (errorPolicy === \"ignore\") {\n            delete result.errors;\n          }\n\n          return result;\n        }\n      );\n\n    if (this.getDocumentInfo(query).hasClientExports) {\n      const observablePromise = this.localState\n        .addExportedVariables(query, variables, context)\n        .then(makeObservable);\n\n      return new Observable<FetchResult<T>>((observer) => {\n        let sub: ObservableSubscription | null = null;\n        observablePromise.then(\n          (observable) => (sub = observable.subscribe(observer)),\n          observer.error\n        );\n        return () => sub && sub.unsubscribe();\n      });\n    }\n\n    return makeObservable(variables);\n  }\n\n  public stopQuery(queryId: string) {\n    this.stopQueryNoBroadcast(queryId);\n    this.broadcastQueries();\n  }\n\n  private stopQueryNoBroadcast(queryId: string) {\n    this.stopQueryInStoreNoBroadcast(queryId);\n    this.removeQuery(queryId);\n  }\n\n  public removeQuery(queryId: string) {\n    // teardown all links\n    // Both `QueryManager.fetchRequest` and `QueryManager.query` create separate promises\n    // that each add their reject functions to fetchCancelFns.\n    // A query created with `QueryManager.query()` could trigger a `QueryManager.fetchRequest`.\n    // The same queryId could have two rejection fns for two promises\n    this.fetchCancelFns.delete(queryId);\n    if (this.queries.has(queryId)) {\n      this.queries.get(queryId)?.stop();\n      this.queries.delete(queryId);\n    }\n  }\n\n  public broadcastQueries() {\n    if (this.onBroadcast) this.onBroadcast();\n    this.queries.forEach((info) => info.observableQuery?.[\"notify\"]());\n  }\n\n  public getLocalState(): LocalState<TStore> {\n    return this.localState;\n  }\n\n  // Use protected instead of private field so\n  // @apollo/experimental-nextjs-app-support can access type info.\n  protected inFlightLinkObservables = new Trie<{\n    observable?: Observable<FetchResult<any>>;\n  }>(false);\n\n  private getObservableFromLink<T = any>(\n    query: DocumentNode,\n    context: any,\n    variables?: OperationVariables,\n    extensions?: Record<string, any>,\n    // Prefer context.queryDeduplication if specified.\n    deduplication: boolean = context?.queryDeduplication ??\n      this.queryDeduplication\n  ): Observable<FetchResult<T>> {\n    let observable: Observable<FetchResult<T>> | undefined;\n\n    const { serverQuery, clientQuery } = this.getDocumentInfo(query);\n    if (serverQuery) {\n      const { inFlightLinkObservables, link } = this;\n\n      const operation = {\n        query: serverQuery,\n        variables,\n        operationName: getOperationName(serverQuery) || void 0,\n        context: this.prepareContext({\n          ...context,\n          forceFetch: !deduplication,\n        }),\n        extensions,\n      };\n\n      context = operation.context;\n\n      if (deduplication) {\n        const printedServerQuery = print(serverQuery);\n        const varJson = canonicalStringify(variables);\n\n        const entry = inFlightLinkObservables.lookup(\n          printedServerQuery,\n          varJson\n        );\n\n        observable = entry.observable;\n        if (!observable) {\n          const concast = new Concast([\n            execute(link, operation) as Observable<FetchResult<T>>,\n          ]);\n          observable = entry.observable = concast;\n\n          concast.beforeNext(function cb(method, arg: FetchResult) {\n            if (method === \"next\" && \"hasNext\" in arg && arg.hasNext) {\n              concast.beforeNext(cb);\n            } else {\n              inFlightLinkObservables.remove(printedServerQuery, varJson);\n            }\n          });\n        }\n      } else {\n        observable = new Concast([\n          execute(link, operation) as Observable<FetchResult<T>>,\n        ]);\n      }\n    } else {\n      observable = new Concast([Observable.of({ data: {} } as FetchResult<T>)]);\n      context = this.prepareContext(context);\n    }\n\n    if (clientQuery) {\n      observable = asyncMap(observable, (result) => {\n        return this.localState.runResolvers({\n          document: clientQuery,\n          remoteResult: result,\n          context,\n          variables,\n        });\n      });\n    }\n\n    return observable;\n  }\n\n  private getResultsFromLink<TData, TVars extends OperationVariables>(\n    queryInfo: QueryInfo,\n    cacheWriteBehavior: CacheWriteBehavior,\n    options: Pick<\n      WatchQueryOptions<TVars, TData>,\n      \"query\" | \"variables\" | \"context\" | \"fetchPolicy\" | \"errorPolicy\"\n    >\n  ): Observable<ApolloQueryResult<TData>> {\n    const requestId = (queryInfo.lastRequestId = this.generateRequestId());\n\n    // Performing transformForLink here gives this.cache a chance to fill in\n    // missing fragment definitions (for example) before sending this document\n    // through the link chain.\n    const linkDocument = this.cache.transformForLink(options.query);\n\n    return asyncMap(\n      this.getObservableFromLink(\n        linkDocument,\n        options.context,\n        options.variables\n      ),\n\n      (result) => {\n        const graphQLErrors = getGraphQLErrorsFromResult(result);\n        const hasErrors = graphQLErrors.length > 0;\n        const { errorPolicy } = options;\n\n        // If we interrupted this request by calling getResultsFromLink again\n        // with the same QueryInfo object, we ignore the old results.\n        if (requestId >= queryInfo.lastRequestId) {\n          if (hasErrors && errorPolicy === \"none\") {\n            // Throwing here effectively calls observer.error.\n            throw queryInfo.markError(\n              new ApolloError({\n                graphQLErrors,\n              })\n            );\n          }\n          // Use linkDocument rather than queryInfo.document so the\n          // operation/fragments used to write the result are the same as the\n          // ones used to obtain it from the link.\n          queryInfo.markResult(\n            result,\n            linkDocument,\n            options,\n            cacheWriteBehavior\n          );\n          queryInfo.markReady();\n        }\n\n        const aqr: ApolloQueryResult<TData> = {\n          data: result.data,\n          loading: false,\n          networkStatus: NetworkStatus.ready,\n        };\n\n        // In the case we start multiple network requests simulatenously, we\n        // want to ensure we properly set `data` if we're reporting on an old\n        // result which will not be caught by the conditional above that ends up\n        // throwing the markError result.\n        if (hasErrors && errorPolicy === \"none\") {\n          aqr.data = void 0 as TData;\n        }\n\n        if (hasErrors && errorPolicy !== \"ignore\") {\n          aqr.errors = graphQLErrors;\n          aqr.networkStatus = NetworkStatus.error;\n        }\n\n        return aqr;\n      },\n\n      (networkError) => {\n        const error =\n          isApolloError(networkError) ? networkError : (\n            new ApolloError({ networkError })\n          );\n\n        // Avoid storing errors from older interrupted queries.\n        if (requestId >= queryInfo.lastRequestId) {\n          queryInfo.markError(error);\n        }\n\n        throw error;\n      }\n    );\n  }\n\n  private fetchConcastWithInfo<TData, TVars extends OperationVariables>(\n    queryInfo: QueryInfo,\n    options: WatchQueryOptions<TVars, TData>,\n    // The initial networkStatus for this fetch, most often\n    // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n    // or setVariables.\n    networkStatus = NetworkStatus.loading,\n    query = options.query\n  ): ConcastAndInfo<TData> {\n    const variables = this.getVariables(query, options.variables) as TVars;\n\n    const defaults = this.defaultOptions.watchQuery;\n    let {\n      fetchPolicy = (defaults && defaults.fetchPolicy) || \"cache-first\",\n      errorPolicy = (defaults && defaults.errorPolicy) || \"none\",\n      returnPartialData = false,\n      notifyOnNetworkStatusChange = false,\n      context = {},\n    } = options;\n\n    const normalized = Object.assign({}, options, {\n      query,\n      variables,\n      fetchPolicy,\n      errorPolicy,\n      returnPartialData,\n      notifyOnNetworkStatusChange,\n      context,\n    });\n\n    const fromVariables = (variables: TVars) => {\n      // Since normalized is always a fresh copy of options, it's safe to\n      // modify its properties here, rather than creating yet another new\n      // WatchQueryOptions object.\n      normalized.variables = variables;\n\n      const sourcesWithInfo = this.fetchQueryByPolicy<TData, TVars>(\n        queryInfo,\n        normalized,\n        networkStatus\n      );\n\n      if (\n        // If we're in standby, postpone advancing options.fetchPolicy using\n        // applyNextFetchPolicy.\n        normalized.fetchPolicy !== \"standby\" &&\n        // The \"standby\" policy currently returns [] from fetchQueryByPolicy, so\n        // this is another way to detect when nothing was done/fetched.\n        sourcesWithInfo.sources.length > 0 &&\n        queryInfo.observableQuery\n      ) {\n        queryInfo.observableQuery[\"applyNextFetchPolicy\"](\n          \"after-fetch\",\n          options\n        );\n      }\n\n      return sourcesWithInfo;\n    };\n\n    // This cancel function needs to be set before the concast is created,\n    // in case concast creation synchronously cancels the request.\n    const cleanupCancelFn = () => this.fetchCancelFns.delete(queryInfo.queryId);\n    this.fetchCancelFns.set(queryInfo.queryId, (reason) => {\n      cleanupCancelFn();\n      // This delay ensures the concast variable has been initialized.\n      setTimeout(() => concast.cancel(reason));\n    });\n\n    let concast: Concast<ApolloQueryResult<TData>>,\n      containsDataFromLink: boolean;\n    // If the query has @export(as: ...) directives, then we need to\n    // process those directives asynchronously. When there are no\n    // @export directives (the common case), we deliberately avoid\n    // wrapping the result of this.fetchQueryByPolicy in a Promise,\n    // since the timing of result delivery is (unfortunately) important\n    // for backwards compatibility. TODO This code could be simpler if\n    // we deprecated and removed LocalState.\n    if (this.getDocumentInfo(normalized.query).hasClientExports) {\n      concast = new Concast(\n        this.localState\n          .addExportedVariables(\n            normalized.query,\n            normalized.variables,\n            normalized.context\n          )\n          .then(fromVariables)\n          .then((sourcesWithInfo) => sourcesWithInfo.sources)\n      );\n      // there is just no way we can synchronously get the *right* value here,\n      // so we will assume `true`, which is the behaviour before the bug fix in\n      // #10597. This means that bug is not fixed in that case, and is probably\n      // un-fixable with reasonable effort for the edge case of @export as\n      // directives.\n      containsDataFromLink = true;\n    } else {\n      const sourcesWithInfo = fromVariables(normalized.variables);\n      containsDataFromLink = sourcesWithInfo.fromLink;\n      concast = new Concast(sourcesWithInfo.sources);\n    }\n\n    concast.promise.then(cleanupCancelFn, cleanupCancelFn);\n\n    return {\n      concast,\n      fromLink: containsDataFromLink,\n    };\n  }\n\n  public refetchQueries<TResult>({\n    updateCache,\n    include,\n    optimistic = false,\n    removeOptimistic = optimistic ? makeUniqueId(\"refetchQueries\") : void 0,\n    onQueryUpdated,\n  }: InternalRefetchQueriesOptions<\n    ApolloCache<TStore>,\n    TResult\n  >): InternalRefetchQueriesMap<TResult> {\n    const includedQueriesById = new Map<\n      string,\n      {\n        oq: ObservableQuery<any>;\n        lastDiff?: Cache.DiffResult<any>;\n        diff?: Cache.DiffResult<any>;\n      }\n    >();\n\n    if (include) {\n      this.getObservableQueries(include).forEach((oq, queryId) => {\n        includedQueriesById.set(queryId, {\n          oq,\n          lastDiff: (this.queries.get(queryId) || oq[\"queryInfo\"]).getDiff(),\n        });\n      });\n    }\n\n    const results: InternalRefetchQueriesMap<TResult> = new Map();\n\n    if (updateCache) {\n      this.cache.batch({\n        update: updateCache,\n\n        // Since you can perform any combination of cache reads and/or writes in\n        // the cache.batch update function, its optimistic option can be either\n        // a boolean or a string, representing three distinct modes of\n        // operation:\n        //\n        // * false: read/write only the root layer\n        // * true: read/write the topmost layer\n        // * string: read/write a fresh optimistic layer with that ID string\n        //\n        // When typeof optimistic === \"string\", a new optimistic layer will be\n        // temporarily created within cache.batch with that string as its ID. If\n        // we then pass that same string as the removeOptimistic option, we can\n        // make cache.batch immediately remove the optimistic layer after\n        // running the updateCache function, triggering only one broadcast.\n        //\n        // However, the refetchQueries method accepts only true or false for its\n        // optimistic option (not string). We interpret true to mean a temporary\n        // optimistic layer should be created, to allow efficiently rolling back\n        // the effect of the updateCache function, which involves passing a\n        // string instead of true as the optimistic option to cache.batch, when\n        // refetchQueries receives optimistic: true.\n        //\n        // In other words, we are deliberately not supporting the use case of\n        // writing to an *existing* optimistic layer (using the refetchQueries\n        // updateCache function), since that would potentially interfere with\n        // other optimistic updates in progress. Instead, you can read/write\n        // only the root layer by passing optimistic: false to refetchQueries,\n        // or you can read/write a brand new optimistic layer that will be\n        // automatically removed by passing optimistic: true.\n        optimistic: (optimistic && removeOptimistic) || false,\n\n        // The removeOptimistic option can also be provided by itself, even if\n        // optimistic === false, to remove some previously-added optimistic\n        // layer safely and efficiently, like we do in markMutationResult.\n        //\n        // If an explicit removeOptimistic string is provided with optimistic:\n        // true, the removeOptimistic string will determine the ID of the\n        // temporary optimistic layer, in case that ever matters.\n        removeOptimistic,\n\n        onWatchUpdated(watch, diff, lastDiff) {\n          const oq =\n            watch.watcher instanceof QueryInfo && watch.watcher.observableQuery;\n\n          if (oq) {\n            if (onQueryUpdated) {\n              // Since we're about to handle this query now, remove it from\n              // includedQueriesById, in case it was added earlier because of\n              // options.include.\n              includedQueriesById.delete(oq.queryId);\n\n              let result: TResult | boolean | Promise<ApolloQueryResult<any>> =\n                onQueryUpdated(oq, diff, lastDiff);\n\n              if (result === true) {\n                // The onQueryUpdated function requested the default refetching\n                // behavior by returning true.\n                result = oq.refetch();\n              }\n\n              // Record the result in the results Map, as long as onQueryUpdated\n              // did not return false to skip/ignore this result.\n              if (result !== false) {\n                results.set(\n                  oq,\n                  result as InternalRefetchQueriesResult<TResult>\n                );\n              }\n\n              // Allow the default cache broadcast to happen, except when\n              // onQueryUpdated returns false.\n              return result;\n            }\n\n            if (onQueryUpdated !== null) {\n              // If we don't have an onQueryUpdated function, and onQueryUpdated\n              // was not disabled by passing null, make sure this query is\n              // \"included\" like any other options.include-specified query.\n              includedQueriesById.set(oq.queryId, { oq, lastDiff, diff });\n            }\n          }\n        },\n      });\n    }\n\n    if (includedQueriesById.size) {\n      includedQueriesById.forEach(({ oq, lastDiff, diff }, queryId) => {\n        let result:\n          | TResult\n          | boolean\n          | Promise<ApolloQueryResult<any>>\n          | undefined;\n\n        // If onQueryUpdated is provided, we want to use it for all included\n        // queries, even the QueryOptions ones.\n        if (onQueryUpdated) {\n          if (!diff) {\n            diff = this.cache.diff(oq[\"queryInfo\"][\"getDiffOptions\"]());\n          }\n          result = onQueryUpdated(oq, diff, lastDiff);\n        }\n\n        // Otherwise, we fall back to refetching.\n        if (!onQueryUpdated || result === true) {\n          result = oq.refetch();\n        }\n\n        if (result !== false) {\n          results.set(oq, result as InternalRefetchQueriesResult<TResult>);\n        }\n\n        if (queryId.indexOf(\"legacyOneTimeQuery\") >= 0) {\n          this.stopQueryNoBroadcast(queryId);\n        }\n      });\n    }\n\n    if (removeOptimistic) {\n      // In case no updateCache callback was provided (so cache.batch was not\n      // called above, and thus did not already remove the optimistic layer),\n      // remove it here. Since this is a no-op when the layer has already been\n      // removed, we do it even if we called cache.batch above, since it's\n      // possible this.cache is an instance of some ApolloCache subclass other\n      // than InMemoryCache, and does not fully support the removeOptimistic\n      // option for cache.batch.\n      this.cache.removeOptimistic(removeOptimistic);\n    }\n\n    return results;\n  }\n\n  private noCacheWarningsByQueryId = new Set<string>();\n\n  public maskOperation<TData = unknown>(\n    options: MaskOperationOptions<TData>\n  ): MaybeMasked<TData> {\n    const { document, data } = options;\n\n    if (__DEV__) {\n      const { fetchPolicy, id } = options;\n      const operationType = getOperationDefinition(document)?.operation;\n      const operationId = (operationType?.[0] ?? \"o\") + id;\n\n      if (\n        this.dataMasking &&\n        fetchPolicy === \"no-cache\" &&\n        !isFullyUnmaskedOperation(document) &&\n        !this.noCacheWarningsByQueryId.has(operationId)\n      ) {\n        this.noCacheWarningsByQueryId.add(operationId);\n\n        invariant.warn(\n          '[%s]: Fragments masked by data masking are inaccessible when using fetch policy \"no-cache\". Please add `@unmask` to each fragment spread to access the data.',\n          getOperationName(document) ??\n            `Unnamed ${operationType ?? \"operation\"}`\n        );\n      }\n    }\n\n    return (\n      this.dataMasking ?\n        maskOperation(data, document, this.cache)\n      : data) as MaybeMasked<TData>;\n  }\n\n  public maskFragment<TData = unknown>(options: MaskFragmentOptions<TData>) {\n    const { data, fragment, fragmentName } = options;\n\n    return this.dataMasking ?\n        maskFragment(data, fragment, this.cache, fragmentName)\n      : data;\n  }\n\n  private fetchQueryByPolicy<TData, TVars extends OperationVariables>(\n    queryInfo: QueryInfo,\n    {\n      query,\n      variables,\n      fetchPolicy,\n      refetchWritePolicy,\n      errorPolicy,\n      returnPartialData,\n      context,\n      notifyOnNetworkStatusChange,\n    }: WatchQueryOptions<TVars, TData>,\n    // The initial networkStatus for this fetch, most often\n    // NetworkStatus.loading, but also possibly fetchMore, poll, refetch,\n    // or setVariables.\n    networkStatus: NetworkStatus\n  ): SourcesAndInfo<TData> {\n    const oldNetworkStatus = queryInfo.networkStatus;\n\n    queryInfo.init({\n      document: query,\n      variables,\n      networkStatus,\n    });\n\n    const readCache = () => queryInfo.getDiff();\n\n    const resultsFromCache = (\n      diff: Cache.DiffResult<TData>,\n      networkStatus = queryInfo.networkStatus || NetworkStatus.loading\n    ) => {\n      const data = diff.result;\n\n      if (__DEV__ && !returnPartialData && !equal(data, {})) {\n        logMissingFieldErrors(diff.missing);\n      }\n\n      const fromData = (data: TData | undefined) =>\n        Observable.of({\n          data,\n          loading: isNetworkRequestInFlight(networkStatus),\n          networkStatus,\n          ...(diff.complete ? null : { partial: true }),\n        } as ApolloQueryResult<TData>);\n\n      if (data && this.getDocumentInfo(query).hasForcedResolvers) {\n        return this.localState\n          .runResolvers({\n            document: query,\n            remoteResult: { data },\n            context,\n            variables,\n            onlyRunForcedResolvers: true,\n          })\n          .then((resolved) => fromData(resolved.data || void 0));\n      }\n\n      // Resolves https://github.com/apollographql/apollo-client/issues/10317.\n      // If errorPolicy is 'none' and notifyOnNetworkStatusChange is true,\n      // data was incorrectly returned from the cache on refetch:\n      // if diff.missing exists, we should not return cache data.\n      if (\n        errorPolicy === \"none\" &&\n        networkStatus === NetworkStatus.refetch &&\n        Array.isArray(diff.missing)\n      ) {\n        return fromData(void 0);\n      }\n\n      return fromData(data);\n    };\n\n    const cacheWriteBehavior =\n      fetchPolicy === \"no-cache\" ? CacheWriteBehavior.FORBID\n        // Watched queries must opt into overwriting existing data on refetch,\n        // by passing refetchWritePolicy: \"overwrite\" in their WatchQueryOptions.\n      : (\n        networkStatus === NetworkStatus.refetch &&\n        refetchWritePolicy !== \"merge\"\n      ) ?\n        CacheWriteBehavior.OVERWRITE\n      : CacheWriteBehavior.MERGE;\n\n    const resultsFromLink = () =>\n      this.getResultsFromLink<TData, TVars>(queryInfo, cacheWriteBehavior, {\n        query,\n        variables,\n        context,\n        fetchPolicy,\n        errorPolicy,\n      });\n\n    const shouldNotify =\n      notifyOnNetworkStatusChange &&\n      typeof oldNetworkStatus === \"number\" &&\n      oldNetworkStatus !== networkStatus &&\n      isNetworkRequestInFlight(networkStatus);\n\n    switch (fetchPolicy) {\n      default:\n      case \"cache-first\": {\n        const diff = readCache();\n\n        if (diff.complete) {\n          return {\n            fromLink: false,\n            sources: [resultsFromCache(diff, queryInfo.markReady())],\n          };\n        }\n\n        if (returnPartialData || shouldNotify) {\n          return {\n            fromLink: true,\n            sources: [resultsFromCache(diff), resultsFromLink()],\n          };\n        }\n\n        return { fromLink: true, sources: [resultsFromLink()] };\n      }\n\n      case \"cache-and-network\": {\n        const diff = readCache();\n\n        if (diff.complete || returnPartialData || shouldNotify) {\n          return {\n            fromLink: true,\n            sources: [resultsFromCache(diff), resultsFromLink()],\n          };\n        }\n\n        return { fromLink: true, sources: [resultsFromLink()] };\n      }\n\n      case \"cache-only\":\n        return {\n          fromLink: false,\n          sources: [resultsFromCache(readCache(), queryInfo.markReady())],\n        };\n\n      case \"network-only\":\n        if (shouldNotify) {\n          return {\n            fromLink: true,\n            sources: [resultsFromCache(readCache()), resultsFromLink()],\n          };\n        }\n\n        return { fromLink: true, sources: [resultsFromLink()] };\n\n      case \"no-cache\":\n        if (shouldNotify) {\n          return {\n            fromLink: true,\n            // Note that queryInfo.getDiff() for no-cache queries does not call\n            // cache.diff, but instead returns a { complete: false } stub result\n            // when there is no queryInfo.diff already defined.\n            sources: [resultsFromCache(queryInfo.getDiff()), resultsFromLink()],\n          };\n        }\n\n        return { fromLink: true, sources: [resultsFromLink()] };\n\n      case \"standby\":\n        return { fromLink: false, sources: [] };\n    }\n  }\n\n  public getOrCreateQuery(queryId: string): QueryInfo {\n    if (queryId && !this.queries.has(queryId)) {\n      this.queries.set(queryId, new QueryInfo(this, queryId));\n    }\n    return this.queries.get(queryId)!;\n  }\n\n  private prepareContext(context = {}) {\n    const newContext = this.localState.prepareContext(context);\n    return {\n      ...this.defaultContext,\n      ...newContext,\n      clientAwareness: this.clientAwareness,\n    };\n  }\n}\n\n// Return types used by fetchQueryByPolicy and other private methods above.\ninterface FetchConcastInfo {\n  // Metadata properties that can be returned in addition to the Concast.\n  fromLink: boolean;\n}\ninterface SourcesAndInfo<TData> extends FetchConcastInfo {\n  sources: ConcastSourcesArray<ApolloQueryResult<TData>>;\n}\ninterface ConcastAndInfo<TData> extends FetchConcastInfo {\n  concast: Concast<ApolloQueryResult<TData>>;\n}\n"]}