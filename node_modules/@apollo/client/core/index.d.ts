export type { ApolloClientOptions, DefaultOptions } from "./ApolloClient.js";
export { ApolloClient, mergeOptions } from "./ApolloClient.js";
export type { FetchMoreOptions } from "./ObservableQuery.js";
export { ObservableQuery } from "./ObservableQuery.js";
export type { QueryOptions, WatchQueryOptions, MutationOptions, SubscriptionOptions, FetchPolicy, WatchQueryFetchPolicy, MutationFetchPolicy, RefetchWritePolicy, ErrorPolicy, FetchMoreQueryOptions, SubscribeToMoreOptions, SubscribeToMoreFunction, UpdateQueryMapFn, UpdateQueryOptions, SubscribeToMoreUpdateQueryFn, } from "./watchQueryOptions.js";
export { NetworkStatus, isNetworkRequestSettled } from "./networkStatus.js";
export type * from "./types.js";
export type { Resolver, FragmentMatcher } from "./LocalState.js";
export { isApolloError, ApolloError } from "../errors/index.js";
export type { Transaction, DataProxy, InMemoryCacheConfig, ReactiveVar, TypePolicies, TypePolicy, FieldPolicy, FieldReadFunction, FieldMergeFunction, FieldFunctionOptions, PossibleTypesMap, WatchFragmentOptions, WatchFragmentResult, } from "../cache/index.js";
export { Cache, ApolloCache, InMemoryCache, MissingFieldError, defaultDataIdFromObject, makeVar, } from "../cache/index.js";
export type * from "../cache/inmemory/types.js";
export * from "../link/core/index.js";
export * from "../link/http/index.js";
export type { ServerError } from "../link/utils/index.js";
export { fromError, toPromise, fromPromise, throwServerError, } from "../link/utils/index.js";
export type { DataMasking, FragmentType, Masked, MaskedDocumentNode, MaybeMasked, Unmasked, } from "../masking/index.js";
export type { DocumentTransformCacheKey, Observer, ObservableSubscription, Reference, StoreObject, } from "../utilities/index.js";
export { DocumentTransform, Observable, isReference, makeReference, } from "../utilities/index.js";
import { setVerbosity } from "ts-invariant";
export { setVerbosity as setLogVerbosity };
export { gql, resetCaches, disableFragmentWarnings, enableExperimentalFragmentVariables, disableExperimentalFragmentVariables, } from "graphql-tag";
//# sourceMappingURL=index.d.ts.map