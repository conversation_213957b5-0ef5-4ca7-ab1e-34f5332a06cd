{"version": 3, "file": "ApolloClient.js", "sourceRoot": "", "sources": ["../../src/core/ApolloClient.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAK7E,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,uBAAuB,CAAC;AAI5D,OAAO,EAAE,OAAO,EAAE,MAAM,eAAe,CAAC;AAExC,OAAO,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAEjD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAuBjD,OAAO,EAAE,UAAU,EAAE,MAAM,iBAAiB,CAAC;AAwB7C,IAAI,oBAAoB,GAAG,KAAK,CAAC;AAgGjC,sEAAsE;AACtE,8EAA8E;AAC9E,8EAA8E;AAC9E,gFAAgF;AAChF,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACrD,OAAO,EAAE,8BAA8B,EAAE,MAAM,4CAA4C,CAAC;AAM5F,OAAO,EAAE,YAAY,EAAE,CAAC;AAExB;;;;;GAKG;AACH;IAgBE;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,sBAAY,OAAyC;QAArD,iBAgHC;;QA9IO,wBAAmB,GAA8B,EAAE,CAAC;QACpD,wBAAmB,GAA8B,EAAE,CAAC;QA8B1D,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACnB,MAAM,iBAAiB,CACrB,mEAAmE;gBACjE,2BAA2B;gBAC3B,kEAAkE,CACrE,CAAC;QACJ,CAAC;QAGC,IAAA,GAAG,GAsBD,OAAO,IAtBN,EACH,WAAW,GAqBT,OAAO,YArBE,EACX,OAAO,GAoBL,OAAO,QApBF,EACP,KAAK,GAmBH,OAAO,MAnBJ,EACL,iBAAiB,GAkBf,OAAO,kBAlBQ,EACjB,KAiBE,OAAO,QAjBM,EAAf,OAAO,mBAAG,KAAK,KAAA,EACf,KAgBE,OAAO,mBAhBa,EAAtB,kBAAkB,mBAAG,CAAC,KAAA;QACtB,kEAAkE;QAClE,iEAAiE;QACjE,uDAAuD;QACvD,iBAAiB,GAYf,OAAO,kBAZQ,EACjB,KAWE,OAAO,mBAXgB,EAAzB,kBAAkB,mBAAG,IAAI,KAAA,EACzB,cAAc,GAUZ,OAAO,eAVK,EACd,cAAc,GASZ,OAAO,eATK,EACd,KAQE,OAAO,uBAR4C,EAArD,sBAAsB,mBAAG,KAAK,CAAC,sBAAsB,KAAA,EACrD,SAAS,GAOP,OAAO,UAPA,EACT,QAAQ,GAMN,OAAO,SAND,EACR,eAAe,GAKb,OAAO,gBALM,EACT,mBAAmB,GAIvB,OAAO,KAJgB,EAChB,sBAAsB,GAG7B,OAAO,QAHsB,EAC/B,QAAQ,GAEN,OAAO,SAFD,EACR,WAAW,GACT,OAAO,YADE,CACD;QAEN,IAAA,IAAI,GAAK,OAAO,KAAZ,CAAa;QAEvB,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI;gBACF,GAAG,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,EAAE,GAAG,KAAA,EAAE,WAAW,aAAA,EAAE,OAAO,SAAA,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;QAC3E,CAAC;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,qBAAqB,GAAG,OAAO,IAAI,kBAAkB,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC5D,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,cAAc,yBACd,QAAQ,KACX,OAAO,EAAE,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,OAAO,mCAAI,iBAAiB,GAChD,CAAC;QAEF,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,OAAO,CAAC;QACxC,CAAC;QAED,IAAI,kBAAkB,EAAE,CAAC;YACvB,UAAU,CACR,cAAM,OAAA,CAAC,KAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,EAApC,CAAoC,EAC1C,kBAAkB,CACnB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEzE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU,CAAC;YAC/B,KAAK,OAAA;YACL,MAAM,EAAE,IAAI;YACZ,SAAS,WAAA;YACT,eAAe,iBAAA;SAChB,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC;YACnC,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,cAAc,gBAAA;YACd,iBAAiB,mBAAA;YACjB,kBAAkB,oBAAA;YAClB,OAAO,SAAA;YACP,WAAW,EAAE,CAAC,CAAC,WAAW;YAC1B,eAAe,EAAE;gBACf,IAAI,EAAE,mBAAoB;gBAC1B,OAAO,EAAE,sBAAuB;aACjC;YACD,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,sBAAsB,wBAAA;YACtB,WAAW,EACT,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;gBAC3B;oBACE,IAAI,KAAI,CAAC,cAAc,EAAE,CAAC;wBACxB,KAAI,CAAC,cAAc,CAAC;4BAClB,MAAM,EAAE,EAAE;4BACV,KAAK,EAAE;gCACL,OAAO,EAAE,KAAI,CAAC,YAAY,CAAC,aAAa,EAAE;gCAC1C,SAAS,EAAE,KAAI,CAAC,YAAY,CAAC,aAAa,IAAI,EAAE;6BACjD;4BACD,yBAAyB,EAAE,KAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;yBACpD,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBACH,CAAC,CAAC,KAAK,CAAC;SACX,CAAC,CAAC;QAEH,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO;YAAE,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC5D,CAAC;IAEO,wCAAiB,GAAzB;QACE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QAKD,IAAM,kBAAkB,GAAG,MAG1B,CAAC;QACF,IAAM,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACrD,CAAC,kBAAkB,CAAC,cAAc,CAAC;YACjC,kBAAkB,CAAC,cAAc,CAAC,IAAK,EAAwB,CAAC,CAAC,IAAI,CACrE,IAAI,CACL,CAAC;QACF,kBAAkB,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAE5C;;WAEG;QACH,IAAI,CAAC,oBAAoB,IAAI,OAAO,EAAE,CAAC;YACrC,oBAAoB,GAAG,IAAI,CAAC;YAC5B,IACE,MAAM,CAAC,QAAQ;gBACf,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI;gBAC1B,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACjD,CAAC;gBACD,UAAU,CAAC;oBACT,IAAI,CAAE,MAAc,CAAC,+BAA+B,EAAE,CAAC;wBACrD,IAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC;wBAC7B,IAAM,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC;wBAChC,IAAI,GAAG,SAAoB,CAAC;wBAC5B,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE,CAAC;4BAC3B,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gCAC/B,GAAG;oCACD,4CAA4C;wCAC5C,4DAA4D,CAAC;4BACjE,CAAC;iCAAM,IAAI,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gCACvC,GAAG;oCACD,wEAAwE,CAAC;4BAC7E,CAAC;wBACH,CAAC;wBACD,IAAI,GAAG,EAAE,CAAC;4BACR,SAAS,CAAC,GAAG,CACX,wDAAwD;gCACtD,gBAAgB,EAClB,GAAG,CACJ,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC,EAAE,KAAK,CAAC,CAAC;YACZ,CAAC;QACH,CAAC;IACH,CAAC;IAOD,sBAAI,2CAAiB;QALrB;;;;WAIG;aACH;YACE,OAAO,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC;QAC7C,CAAC;;;OAAA;IAED;;;OAGG;IACI,2BAAI,GAAX;QACE,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IAC3B,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACI,iCAAU,GAAjB,UAGE,OAAyC;QACzC,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,CAAC;YACnC,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAClE,CAAC;QAED,+EAA+E;QAC/E,IACE,IAAI,CAAC,qBAAqB;YAC1B,CAAC,OAAO,CAAC,WAAW,KAAK,cAAc;gBACrC,OAAO,CAAC,WAAW,KAAK,mBAAmB,CAAC,EAC9C,CAAC;YACD,OAAO,yBAAQ,OAAO,KAAE,WAAW,EAAE,aAAa,GAAE,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,CAAgB,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;;;;OAQG;IACI,4BAAK,GAAZ,UAIE,OAAoC;QAEpC,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAC7D,CAAC;QAED,SAAS,CACN,OAAO,CAAC,WAAqC,KAAK,mBAAmB,EACtE,6EAA6E;YAC3E,6EAA6E;YAC7E,0EAA0E;YAC1E,qEAAqE,CACxE,CAAC;QAEF,IAAI,IAAI,CAAC,qBAAqB,IAAI,OAAO,CAAC,WAAW,KAAK,cAAc,EAAE,CAAC;YACzE,OAAO,yBAAQ,OAAO,KAAE,WAAW,EAAE,aAAa,GAAE,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAgB,OAAO,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;;OAOG;IACI,6BAAM,GAAb,UAME,OAAqD;QAErD,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YAC/B,OAAO,GAAG,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC9D,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAC7B,OAAO,CACR,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,gCAAS,GAAhB,UAIE,OAA2C;QAJ7C,iBAmBC;QAbC,IAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;QAE/C,OAAO,IAAI,CAAC,YAAY;aACrB,wBAAwB,CAAI,OAAO,CAAC;aACpC,GAAG,CAAC,UAAC,MAAM,IAAK,OAAA,uBACZ,MAAM,KACT,IAAI,EAAE,KAAI,CAAC,YAAY,CAAC,aAAa,CAAC;gBACpC,QAAQ,EAAE,OAAO,CAAC,KAAK;gBACvB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,EAAE,IAAA;aACH,CAAC,IACF,EARe,CAQf,CAAC,CAAC;IACR,CAAC;IAED;;;;;;;;OAQG;IACI,gCAAS,GAAhB,UACE,OAAuC,EACvC,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QAE3B,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAgB,OAAO,EAAE,UAAU,CAAC,CAAC;IAClE,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IAEI,oCAAa,GAApB,UAIE,OAAwD;;QAExD,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,uBAC1B,OAAO,gBACT,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAG,IAAI,CAAC,YAAY,CAAC,WAAW,OACjE,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,mCAAY,GAAnB,UACE,OAA0C,EAC1C,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QAE3B,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAgB,OAAO,EAAE,UAAU,CAAC,CAAC;IACrE,CAAC;IAED;;;;OAIG;IACI,iCAAU,GAAjB,UACE,OAAuD;QAEvD,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAoB,OAAO,CAAC,CAAC;QAE9D,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;QACvC,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAED;;;;;;;;;;OAUG;IACI,oCAAa,GAApB,UACE,OAA0D;QAE1D,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAoB,OAAO,CAAC,CAAC;QAEjE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;YAChC,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;QACvC,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,8CAAuB,GAA9B,UAA+B,EAAa;QAC1C,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAC3B,CAAC;IAEM,mCAAY,GAAnB,UACE,OAAuB;QAEvB,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,iCAAU,GAAjB;QAAA,iBASC;QARC,OAAO,OAAO,CAAC,OAAO,EAAE;aACrB,IAAI,CAAC;YACJ,OAAA,KAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBAC3B,cAAc,EAAE,KAAK;aACtB,CAAC;QAFF,CAEE,CACH;aACA,IAAI,CAAC,cAAM,OAAA,OAAO,CAAC,GAAG,CAAC,KAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAC,EAAE,IAAK,OAAA,EAAE,EAAE,EAAJ,CAAI,CAAC,CAAC,EAAvD,CAAuD,CAAC;aACnE,IAAI,CAAC,cAAM,OAAA,KAAI,CAAC,wBAAwB,EAAE,EAA/B,CAA+B,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACI,iCAAU,GAAjB;QAAA,iBAQC;QAPC,OAAO,OAAO,CAAC,OAAO,EAAE;aACrB,IAAI,CAAC;YACJ,OAAA,KAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBAC3B,cAAc,EAAE,IAAI;aACrB,CAAC;QAFF,CAEE,CACH;aACA,IAAI,CAAC,cAAM,OAAA,OAAO,CAAC,GAAG,CAAC,KAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAC,EAAE,IAAK,OAAA,EAAE,EAAE,EAAJ,CAAI,CAAC,CAAC,EAAvD,CAAuD,CAAC,CAAC;IACzE,CAAC;IAED;;;;OAIG;IACI,mCAAY,GAAnB,UAAoB,EAAsB;QAA1C,iBAOC;QANC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClC,OAAO;YACL,KAAI,CAAC,mBAAmB,GAAG,KAAI,CAAC,mBAAmB,CAAC,MAAM,CACxD,UAAC,CAAC,IAAK,OAAA,CAAC,KAAK,EAAE,EAAR,CAAQ,CAChB,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACI,mCAAY,GAAnB,UAAoB,EAAsB;QAA1C,iBAOC;QANC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAClC,OAAO;YACL,KAAI,CAAC,mBAAmB,GAAG,KAAI,CAAC,mBAAmB,CAAC,MAAM,CACxD,UAAC,CAAC,IAAK,OAAA,CAAC,KAAK,EAAE,EAAR,CAAQ,CAChB,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAED;;;;;;;;;;;OAWG;IACI,+CAAwB,GAA/B,UACE,cAAwB;QAExB,OAAO,IAAI,CAAC,YAAY,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAC;IACpE,CAAC;IAED;;;;;;;;;;OAUG;IACI,qCAAc,GAArB,UAIE,OAA+C;QAE/C,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAC1C,OAAmE,CACpE,CAAC;QACF,IAAM,OAAO,GAA2B,EAAE,CAAC;QAC3C,IAAM,OAAO,GAA4C,EAAE,CAAC;QAE5D,GAAG,CAAC,OAAO,CAAC,UAAC,MAAM,EAAE,QAAQ;YAC3B,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvB,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CACxB,OAAoB,CACY,CAAC;QAEnC,iEAAiE;QACjE,wBAAwB;QACxB,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;QACzB,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;QAEzB,kEAAkE;QAClE,yEAAyE;QACzE,mEAAmE;QACnE,MAAM,CAAC,KAAK,CAAC,UAAC,KAAK;YACjB,SAAS,CAAC,KAAK,CACb,sEAAsE,EACtE,KAAK,CACN,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;;;;;;OAUG;IACI,2CAAoB,GAA3B,UACE,OAAyC;QAAzC,wBAAA,EAAA,kBAAyC;QAEzC,OAAO,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,8BAAO,GAAd,UAAe,UAAoB;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACI,8BAAO,GAAd,UAAe,eAA4B;QACzC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,mCAAY,GAAnB,UAAoB,SAAkC;QACpD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,mCAAY,GAAnB,UAAoB,SAAkC;QACpD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,mCAAY,GAAnB;QACE,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;IACxC,CAAC;IAED;;OAEG;IACI,mDAA4B,GAAnC,UAAoC,eAAgC;QAClE,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,8BAAO,GAAd,UAAe,OAAmB;QAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC;IAC/C,CAAC;IAED,sBAAW,wCAAc;aAAzB;YACE,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;QAC1C,CAAC;;;OAAA;IAmFH,mBAAC;AAAD,CAAC,AAxvBD,IAwvBC;;AAED,IAAI,OAAO,EAAE,CAAC;IACZ,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,8BAA8B,CAAC;AAC7E,CAAC", "sourcesContent": ["import { invariant, newInvariantError } from \"../utilities/globals/index.js\";\n\nimport type { DocumentNode, FormattedExecutionResult } from \"graphql\";\n\nimport type { FetchResult, GraphQLRequest } from \"../link/core/index.js\";\nimport { ApolloLink, execute } from \"../link/core/index.js\";\nimport type { ApolloCache, DataProxy, Reference } from \"../cache/index.js\";\nimport type { DocumentTransform } from \"../utilities/index.js\";\nimport type { Observable } from \"../utilities/index.js\";\nimport { version } from \"../version.js\";\nimport type { UriFunction } from \"../link/http/index.js\";\nimport { HttpLink } from \"../link/http/index.js\";\n\nimport { QueryManager } from \"./QueryManager.js\";\nimport type { ObservableQuery } from \"./ObservableQuery.js\";\n\nimport type {\n  ApolloQueryResult,\n  DefaultContext,\n  OperationVariables,\n  Resolvers,\n  RefetchQueriesOptions,\n  RefetchQueriesResult,\n  InternalRefetchQueriesResult,\n  RefetchQueriesInclude,\n} from \"./types.js\";\n\nimport type {\n  QueryOptions,\n  WatchQueryOptions,\n  MutationOptions,\n  SubscriptionOptions,\n  WatchQueryFetchPolicy,\n} from \"./watchQueryOptions.js\";\n\nimport type { FragmentMatcher } from \"./LocalState.js\";\nimport { LocalState } from \"./LocalState.js\";\n\nexport interface DefaultOptions {\n  watchQuery?: Partial<WatchQueryOptions<any, any>>;\n  query?: Partial<QueryOptions<any, any>>;\n  mutate?: Partial<MutationOptions<any, any, any>>;\n}\n\nexport interface DevtoolsOptions {\n  /**\n   * If `true`, the [Apollo Client Devtools](https://www.apollographql.com/docs/react/development-testing/developer-tooling/#apollo-client-devtools) browser extension can connect to this `ApolloClient` instance.\n   *\n   * The default value is `false` in production and `true` in development if there is a `window` object.\n   */\n  enabled?: boolean;\n\n  /**\n   * Optional name for this `ApolloClient` instance in the devtools. This is\n   * useful when you instantiate multiple clients and want to be able to\n   * identify them by name.\n   */\n  name?: string;\n}\n\nlet hasSuggestedDevtools = false;\n\nexport interface ApolloClientOptions<TCacheShape> {\n  /**\n   * The URI of the GraphQL endpoint that Apollo Client will communicate with.\n   *\n   * One of `uri` or `link` is **required**. If you provide both, `link` takes precedence.\n   */\n  uri?: string | UriFunction;\n  credentials?: string;\n  /**\n   * An object representing headers to include in every HTTP request, such as `{Authorization: 'Bearer 1234'}`\n   *\n   * This value will be ignored when using the `link` option.\n   */\n  headers?: Record<string, string>;\n  /**\n   * You can provide an `ApolloLink` instance to serve as Apollo Client's network layer. For more information, see [Advanced HTTP networking](https://www.apollographql.com/docs/react/networking/advanced-http-networking/).\n   *\n   * One of `uri` or `link` is **required**. If you provide both, `link` takes precedence.\n   */\n  link?: ApolloLink;\n  /**\n   * The cache that Apollo Client should use to store query results locally. The recommended cache is `InMemoryCache`, which is provided by the `@apollo/client` package.\n   *\n   * For more information, see [Configuring the cache](https://www.apollographql.com/docs/react/caching/cache-configuration/).\n   */\n  cache: ApolloCache<TCacheShape>;\n  /**\n   * The time interval (in milliseconds) before Apollo Client force-fetches queries after a server-side render.\n   *\n   * @defaultValue `0` (no delay)\n   */\n  ssrForceFetchDelay?: number;\n  /**\n   * When using Apollo Client for [server-side rendering](https://www.apollographql.com/docs/react/performance/server-side-rendering/), set this to `true` so that the [`getDataFromTree` function](../react/ssr/#getdatafromtree) can work effectively.\n   *\n   * @defaultValue `false`\n   */\n  ssrMode?: boolean;\n  /**\n   * If `true`, the [Apollo Client Devtools](https://www.apollographql.com/docs/react/development-testing/developer-tooling/#apollo-client-devtools) browser extension can connect to Apollo Client.\n   *\n   * The default value is `false` in production and `true` in development (if there is a `window` object).\n   * @deprecated Please use the `devtools.enabled` option.\n   */\n  connectToDevTools?: boolean;\n  /**\n   * If `false`, Apollo Client sends every created query to the server, even if a _completely_ identical query (identical in terms of query string, variable values, and operationName) is already in flight.\n   *\n   * @defaultValue `true`\n   */\n  queryDeduplication?: boolean;\n  /**\n   * Provide this object to set application-wide default values for options you can provide to the `watchQuery`, `query`, and `mutate` functions. See below for an example object.\n   *\n   * See this [example object](https://www.apollographql.com/docs/react/api/core/ApolloClient#example-defaultoptions-object).\n   */\n  defaultOptions?: DefaultOptions;\n  defaultContext?: Partial<DefaultContext>;\n  /**\n   * If `true`, Apollo Client will assume results read from the cache are never mutated by application code, which enables substantial performance optimizations.\n   *\n   * @defaultValue `false`\n   */\n  assumeImmutableResults?: boolean;\n  resolvers?: Resolvers | Resolvers[];\n  typeDefs?: string | string[] | DocumentNode | DocumentNode[];\n  fragmentMatcher?: FragmentMatcher;\n  /**\n   * A custom name (e.g., `iOS`) that identifies this particular client among your set of clients. Apollo Server and Apollo Studio use this property as part of the [client awareness](https://www.apollographql.com/docs/apollo-server/monitoring/metrics#identifying-distinct-clients) feature.\n   */\n  name?: string;\n  /**\n   * A custom version that identifies the current version of this particular client (e.g., `1.2`). Apollo Server and Apollo Studio use this property as part of the [client awareness](https://www.apollographql.com/docs/apollo-server/monitoring/metrics#identifying-distinct-clients) feature.\n   *\n   * This is **not** the version of Apollo Client that you are using, but rather any version string that helps you differentiate between versions of your client.\n   */\n  version?: string;\n  documentTransform?: DocumentTransform;\n\n  /**\n   * Configuration used by the [Apollo Client Devtools extension](https://www.apollographql.com/docs/react/development-testing/developer-tooling/#apollo-client-devtools) for this client.\n   *\n   * @since 3.11.0\n   */\n  devtools?: DevtoolsOptions;\n\n  /**\n   * Determines if data masking is enabled for the client.\n   *\n   * @defaultValue false\n   */\n  dataMasking?: boolean;\n}\n\n// Though mergeOptions now resides in @apollo/client/utilities, it was\n// previously declared and exported from this module, and then reexported from\n// @apollo/client/core. Since we need to preserve that API anyway, the easiest\n// solution is to reexport mergeOptions where it was previously declared (here).\nimport { mergeOptions } from \"../utilities/index.js\";\nimport { getApolloClientMemoryInternals } from \"../utilities/caching/getMemoryInternals.js\";\nimport type {\n  WatchFragmentOptions,\n  WatchFragmentResult,\n} from \"../cache/core/cache.js\";\nimport type { MaybeMasked, Unmasked } from \"../masking/index.js\";\nexport { mergeOptions };\n\n/**\n * This is the primary Apollo Client class. It is used to send GraphQL documents (i.e. queries\n * and mutations) to a GraphQL spec-compliant server over an `ApolloLink` instance,\n * receive results from the server and cache the results in a store. It also delivers updates\n * to GraphQL queries through `Observable` instances.\n */\nexport class ApolloClient<TCacheShape> implements DataProxy {\n  public link: ApolloLink;\n  public cache: ApolloCache<TCacheShape>;\n  public disableNetworkFetches: boolean;\n  public version: string;\n  public queryDeduplication: boolean;\n  public defaultOptions: DefaultOptions;\n  public readonly typeDefs: ApolloClientOptions<TCacheShape>[\"typeDefs\"];\n  public readonly devtoolsConfig: DevtoolsOptions;\n\n  private queryManager: QueryManager<TCacheShape>;\n  private devToolsHookCb?: Function;\n  private resetStoreCallbacks: Array<() => Promise<any>> = [];\n  private clearStoreCallbacks: Array<() => Promise<any>> = [];\n  private localState: LocalState<TCacheShape>;\n\n  /**\n   * Constructs an instance of `ApolloClient`.\n   *\n   * @example\n   * ```js\n   * import { ApolloClient, InMemoryCache } from '@apollo/client';\n   *\n   * const cache = new InMemoryCache();\n   *\n   * const client = new ApolloClient({\n   *   // Provide required constructor fields\n   *   cache: cache,\n   *   uri: 'http://localhost:4000/',\n   *\n   *   // Provide some optional constructor fields\n   *   name: 'react-web-client',\n   *   version: '1.3',\n   *   queryDeduplication: false,\n   *   defaultOptions: {\n   *     watchQuery: {\n   *       fetchPolicy: 'cache-and-network',\n   *     },\n   *   },\n   * });\n   * ```\n   */\n  constructor(options: ApolloClientOptions<TCacheShape>) {\n    if (!options.cache) {\n      throw newInvariantError(\n        \"To initialize Apollo Client, you must specify a 'cache' property \" +\n          \"in the options object. \\n\" +\n          \"For more information, please visit: https://go.apollo.dev/c/docs\"\n      );\n    }\n\n    const {\n      uri,\n      credentials,\n      headers,\n      cache,\n      documentTransform,\n      ssrMode = false,\n      ssrForceFetchDelay = 0,\n      // Expose the client instance as window.__APOLLO_CLIENT__ and call\n      // onBroadcast in queryManager.broadcastQueries to enable browser\n      // devtools, but disable them by default in production.\n      connectToDevTools,\n      queryDeduplication = true,\n      defaultOptions,\n      defaultContext,\n      assumeImmutableResults = cache.assumeImmutableResults,\n      resolvers,\n      typeDefs,\n      fragmentMatcher,\n      name: clientAwarenessName,\n      version: clientAwarenessVersion,\n      devtools,\n      dataMasking,\n    } = options;\n\n    let { link } = options;\n\n    if (!link) {\n      link =\n        uri ? new HttpLink({ uri, credentials, headers }) : ApolloLink.empty();\n    }\n\n    this.link = link;\n    this.cache = cache;\n    this.disableNetworkFetches = ssrMode || ssrForceFetchDelay > 0;\n    this.queryDeduplication = queryDeduplication;\n    this.defaultOptions = defaultOptions || Object.create(null);\n    this.typeDefs = typeDefs;\n    this.devtoolsConfig = {\n      ...devtools,\n      enabled: devtools?.enabled ?? connectToDevTools,\n    };\n\n    if (this.devtoolsConfig.enabled === undefined) {\n      this.devtoolsConfig.enabled = __DEV__;\n    }\n\n    if (ssrForceFetchDelay) {\n      setTimeout(\n        () => (this.disableNetworkFetches = false),\n        ssrForceFetchDelay\n      );\n    }\n\n    this.watchQuery = this.watchQuery.bind(this);\n    this.query = this.query.bind(this);\n    this.mutate = this.mutate.bind(this);\n    this.watchFragment = this.watchFragment.bind(this);\n    this.resetStore = this.resetStore.bind(this);\n    this.reFetchObservableQueries = this.reFetchObservableQueries.bind(this);\n\n    this.version = version;\n\n    this.localState = new LocalState({\n      cache,\n      client: this,\n      resolvers,\n      fragmentMatcher,\n    });\n\n    this.queryManager = new QueryManager({\n      cache: this.cache,\n      link: this.link,\n      defaultOptions: this.defaultOptions,\n      defaultContext,\n      documentTransform,\n      queryDeduplication,\n      ssrMode,\n      dataMasking: !!dataMasking,\n      clientAwareness: {\n        name: clientAwarenessName!,\n        version: clientAwarenessVersion!,\n      },\n      localState: this.localState,\n      assumeImmutableResults,\n      onBroadcast:\n        this.devtoolsConfig.enabled ?\n          () => {\n            if (this.devToolsHookCb) {\n              this.devToolsHookCb({\n                action: {},\n                state: {\n                  queries: this.queryManager.getQueryStore(),\n                  mutations: this.queryManager.mutationStore || {},\n                },\n                dataWithOptimisticResults: this.cache.extract(true),\n              });\n            }\n          }\n        : void 0,\n    });\n\n    if (this.devtoolsConfig.enabled) this.connectToDevTools();\n  }\n\n  private connectToDevTools() {\n    if (typeof window === \"undefined\") {\n      return;\n    }\n\n    type DevToolsConnector = {\n      push(client: ApolloClient<any>): void;\n    };\n    const windowWithDevTools = window as Window & {\n      [devtoolsSymbol]?: DevToolsConnector;\n      __APOLLO_CLIENT__?: ApolloClient<any>;\n    };\n    const devtoolsSymbol = Symbol.for(\"apollo.devtools\");\n    (windowWithDevTools[devtoolsSymbol] =\n      windowWithDevTools[devtoolsSymbol] || ([] as DevToolsConnector)).push(\n      this\n    );\n    windowWithDevTools.__APOLLO_CLIENT__ = this;\n\n    /**\n     * Suggest installing the devtools for developers who don't have them\n     */\n    if (!hasSuggestedDevtools && __DEV__) {\n      hasSuggestedDevtools = true;\n      if (\n        window.document &&\n        window.top === window.self &&\n        /^(https?|file):$/.test(window.location.protocol)\n      ) {\n        setTimeout(() => {\n          if (!(window as any).__APOLLO_DEVTOOLS_GLOBAL_HOOK__) {\n            const nav = window.navigator;\n            const ua = nav && nav.userAgent;\n            let url: string | undefined;\n            if (typeof ua === \"string\") {\n              if (ua.indexOf(\"Chrome/\") > -1) {\n                url =\n                  \"https://chrome.google.com/webstore/detail/\" +\n                  \"apollo-client-developer-t/jdkknkkbebbapilgoeccciglkfbmbnfm\";\n              } else if (ua.indexOf(\"Firefox/\") > -1) {\n                url =\n                  \"https://addons.mozilla.org/en-US/firefox/addon/apollo-developer-tools/\";\n              }\n            }\n            if (url) {\n              invariant.log(\n                \"Download the Apollo DevTools for a better development \" +\n                  \"experience: %s\",\n                url\n              );\n            }\n          }\n        }, 10000);\n      }\n    }\n  }\n\n  /**\n   * The `DocumentTransform` used to modify GraphQL documents before a request\n   * is made. If a custom `DocumentTransform` is not provided, this will be the\n   * default document transform.\n   */\n  get documentTransform() {\n    return this.queryManager.documentTransform;\n  }\n\n  /**\n   * Call this method to terminate any active client processes, making it safe\n   * to dispose of this `ApolloClient` instance.\n   */\n  public stop() {\n    this.queryManager.stop();\n  }\n\n  /**\n   * This watches the cache store of the query according to the options specified and\n   * returns an `ObservableQuery`. We can subscribe to this `ObservableQuery` and\n   * receive updated results through an observer when the cache store changes.\n   *\n   * Note that this method is not an implementation of GraphQL subscriptions. Rather,\n   * it uses Apollo's store in order to reactively deliver updates to your query results.\n   *\n   * For example, suppose you call watchQuery on a GraphQL query that fetches a person's\n   * first and last name and this person has a particular object identifier, provided by\n   * dataIdFromObject. Later, a different query fetches that same person's\n   * first and last name and the first name has now changed. Then, any observers associated\n   * with the results of the first query will be updated with a new result object.\n   *\n   * Note that if the cache does not change, the subscriber will *not* be notified.\n   *\n   * See [here](https://medium.com/apollo-stack/the-concepts-of-graphql-bc68bd819be3#.3mb0cbcmc) for\n   * a description of store reactivity.\n   */\n  public watchQuery<\n    T = any,\n    TVariables extends OperationVariables = OperationVariables,\n  >(options: WatchQueryOptions<TVariables, T>): ObservableQuery<T, TVariables> {\n    if (this.defaultOptions.watchQuery) {\n      options = mergeOptions(this.defaultOptions.watchQuery, options);\n    }\n\n    // XXX Overwriting options is probably not the best way to do this long term...\n    if (\n      this.disableNetworkFetches &&\n      (options.fetchPolicy === \"network-only\" ||\n        options.fetchPolicy === \"cache-and-network\")\n    ) {\n      options = { ...options, fetchPolicy: \"cache-first\" };\n    }\n\n    return this.queryManager.watchQuery<T, TVariables>(options);\n  }\n\n  /**\n   * This resolves a single query according to the options specified and\n   * returns a `Promise` which is either resolved with the resulting data\n   * or rejected with an error.\n   *\n   * @param options - An object of type `QueryOptions` that allows us to\n   * describe how this query should be treated e.g. whether it should hit the\n   * server at all or just resolve from the cache, etc.\n   */\n  public query<\n    T = any,\n    TVariables extends OperationVariables = OperationVariables,\n  >(\n    options: QueryOptions<TVariables, T>\n  ): Promise<ApolloQueryResult<MaybeMasked<T>>> {\n    if (this.defaultOptions.query) {\n      options = mergeOptions(this.defaultOptions.query, options);\n    }\n\n    invariant(\n      (options.fetchPolicy as WatchQueryFetchPolicy) !== \"cache-and-network\",\n      \"The cache-and-network fetchPolicy does not work with client.query, because \" +\n        \"client.query can only return a single result. Please use client.watchQuery \" +\n        \"to receive multiple results from the cache and the network, or consider \" +\n        \"using a different fetchPolicy, such as cache-first or network-only.\"\n    );\n\n    if (this.disableNetworkFetches && options.fetchPolicy === \"network-only\") {\n      options = { ...options, fetchPolicy: \"cache-first\" };\n    }\n\n    return this.queryManager.query<T, TVariables>(options);\n  }\n\n  /**\n   * This resolves a single mutation according to the options specified and returns a\n   * Promise which is either resolved with the resulting data or rejected with an\n   * error. In some cases both `data` and `errors` might be undefined, for example\n   * when `errorPolicy` is set to `'ignore'`.\n   *\n   * It takes options as an object with the following keys and values:\n   */\n  public mutate<\n    TData = any,\n    TVariables extends OperationVariables = OperationVariables,\n    TContext extends Record<string, any> = DefaultContext,\n    TCache extends ApolloCache<any> = ApolloCache<any>,\n  >(\n    options: MutationOptions<TData, TVariables, TContext>\n  ): Promise<FetchResult<MaybeMasked<TData>>> {\n    if (this.defaultOptions.mutate) {\n      options = mergeOptions(this.defaultOptions.mutate, options);\n    }\n    return this.queryManager.mutate<TData, TVariables, TContext, TCache>(\n      options\n    );\n  }\n\n  /**\n   * This subscribes to a graphql subscription according to the options specified and returns an\n   * `Observable` which either emits received data or an error.\n   */\n  public subscribe<\n    T = any,\n    TVariables extends OperationVariables = OperationVariables,\n  >(\n    options: SubscriptionOptions<TVariables, T>\n  ): Observable<FetchResult<MaybeMasked<T>>> {\n    const id = this.queryManager.generateQueryId();\n\n    return this.queryManager\n      .startGraphQLSubscription<T>(options)\n      .map((result) => ({\n        ...result,\n        data: this.queryManager.maskOperation({\n          document: options.query,\n          data: result.data,\n          fetchPolicy: options.fetchPolicy,\n          id,\n        }),\n      }));\n  }\n\n  /**\n   * Tries to read some data from the store in the shape of the provided\n   * GraphQL query without making a network request. This method will start at\n   * the root query. To start at a specific id returned by `dataIdFromObject`\n   * use `readFragment`.\n   *\n   * @param optimistic - Set to `true` to allow `readQuery` to return\n   * optimistic results. Is `false` by default.\n   */\n  public readQuery<T = any, TVariables = OperationVariables>(\n    options: DataProxy.Query<TVariables, T>,\n    optimistic: boolean = false\n  ): Unmasked<T> | null {\n    return this.cache.readQuery<T, TVariables>(options, optimistic);\n  }\n\n  /**\n   * Watches the cache store of the fragment according to the options specified\n   * and returns an `Observable`. We can subscribe to this\n   * `Observable` and receive updated results through an\n   * observer when the cache store changes.\n   *\n   * You must pass in a GraphQL document with a single fragment or a document\n   * with multiple fragments that represent what you are reading. If you pass\n   * in a document with multiple fragments then you must also specify a\n   * `fragmentName`.\n   *\n   * @since 3.10.0\n   * @param options - An object of type `WatchFragmentOptions` that allows\n   * the cache to identify the fragment and optionally specify whether to react\n   * to optimistic updates.\n   */\n\n  public watchFragment<\n    TFragmentData = unknown,\n    TVariables = OperationVariables,\n  >(\n    options: WatchFragmentOptions<TFragmentData, TVariables>\n  ): Observable<WatchFragmentResult<TFragmentData>> {\n    return this.cache.watchFragment({\n      ...options,\n      [Symbol.for(\"apollo.dataMasking\")]: this.queryManager.dataMasking,\n    });\n  }\n\n  /**\n   * Tries to read some data from the store in the shape of the provided\n   * GraphQL fragment without making a network request. This method will read a\n   * GraphQL fragment from any arbitrary id that is currently cached, unlike\n   * `readQuery` which will only read from the root query.\n   *\n   * You must pass in a GraphQL document with a single fragment or a document\n   * with multiple fragments that represent what you are reading. If you pass\n   * in a document with multiple fragments then you must also specify a\n   * `fragmentName`.\n   *\n   * @param optimistic - Set to `true` to allow `readFragment` to return\n   * optimistic results. Is `false` by default.\n   */\n  public readFragment<T = any, TVariables = OperationVariables>(\n    options: DataProxy.Fragment<TVariables, T>,\n    optimistic: boolean = false\n  ): Unmasked<T> | null {\n    return this.cache.readFragment<T, TVariables>(options, optimistic);\n  }\n\n  /**\n   * Writes some data in the shape of the provided GraphQL query directly to\n   * the store. This method will start at the root query. To start at a\n   * specific id returned by `dataIdFromObject` then use `writeFragment`.\n   */\n  public writeQuery<TData = any, TVariables = OperationVariables>(\n    options: DataProxy.WriteQueryOptions<TData, TVariables>\n  ): Reference | undefined {\n    const ref = this.cache.writeQuery<TData, TVariables>(options);\n\n    if (options.broadcast !== false) {\n      this.queryManager.broadcastQueries();\n    }\n\n    return ref;\n  }\n\n  /**\n   * Writes some data in the shape of the provided GraphQL fragment directly to\n   * the store. This method will write to a GraphQL fragment from any arbitrary\n   * id that is currently cached, unlike `writeQuery` which will only write\n   * from the root query.\n   *\n   * You must pass in a GraphQL document with a single fragment or a document\n   * with multiple fragments that represent what you are writing. If you pass\n   * in a document with multiple fragments then you must also specify a\n   * `fragmentName`.\n   */\n  public writeFragment<TData = any, TVariables = OperationVariables>(\n    options: DataProxy.WriteFragmentOptions<TData, TVariables>\n  ): Reference | undefined {\n    const ref = this.cache.writeFragment<TData, TVariables>(options);\n\n    if (options.broadcast !== false) {\n      this.queryManager.broadcastQueries();\n    }\n\n    return ref;\n  }\n\n  public __actionHookForDevTools(cb: () => any) {\n    this.devToolsHookCb = cb;\n  }\n\n  public __requestRaw(\n    payload: GraphQLRequest\n  ): Observable<FormattedExecutionResult> {\n    return execute(this.link, payload);\n  }\n\n  /**\n   * Resets your entire store by clearing out your cache and then re-executing\n   * all of your active queries. This makes it so that you may guarantee that\n   * there is no data left in your store from a time before you called this\n   * method.\n   *\n   * `resetStore()` is useful when your user just logged out. You’ve removed the\n   * user session, and you now want to make sure that any references to data you\n   * might have fetched while the user session was active is gone.\n   *\n   * It is important to remember that `resetStore()` *will* refetch any active\n   * queries. This means that any components that might be mounted will execute\n   * their queries again using your network interface. If you do not want to\n   * re-execute any queries then you should make sure to stop watching any\n   * active queries.\n   */\n  public resetStore(): Promise<ApolloQueryResult<any>[] | null> {\n    return Promise.resolve()\n      .then(() =>\n        this.queryManager.clearStore({\n          discardWatches: false,\n        })\n      )\n      .then(() => Promise.all(this.resetStoreCallbacks.map((fn) => fn())))\n      .then(() => this.reFetchObservableQueries());\n  }\n\n  /**\n   * Remove all data from the store. Unlike `resetStore`, `clearStore` will\n   * not refetch any active queries.\n   */\n  public clearStore(): Promise<any[]> {\n    return Promise.resolve()\n      .then(() =>\n        this.queryManager.clearStore({\n          discardWatches: true,\n        })\n      )\n      .then(() => Promise.all(this.clearStoreCallbacks.map((fn) => fn())));\n  }\n\n  /**\n   * Allows callbacks to be registered that are executed when the store is\n   * reset. `onResetStore` returns an unsubscribe function that can be used\n   * to remove registered callbacks.\n   */\n  public onResetStore(cb: () => Promise<any>): () => void {\n    this.resetStoreCallbacks.push(cb);\n    return () => {\n      this.resetStoreCallbacks = this.resetStoreCallbacks.filter(\n        (c) => c !== cb\n      );\n    };\n  }\n\n  /**\n   * Allows callbacks to be registered that are executed when the store is\n   * cleared. `onClearStore` returns an unsubscribe function that can be used\n   * to remove registered callbacks.\n   */\n  public onClearStore(cb: () => Promise<any>): () => void {\n    this.clearStoreCallbacks.push(cb);\n    return () => {\n      this.clearStoreCallbacks = this.clearStoreCallbacks.filter(\n        (c) => c !== cb\n      );\n    };\n  }\n\n  /**\n   * Refetches all of your active queries.\n   *\n   * `reFetchObservableQueries()` is useful if you want to bring the client back to proper state in case of a network outage\n   *\n   * It is important to remember that `reFetchObservableQueries()` *will* refetch any active\n   * queries. This means that any components that might be mounted will execute\n   * their queries again using your network interface. If you do not want to\n   * re-execute any queries then you should make sure to stop watching any\n   * active queries.\n   * Takes optional parameter `includeStandby` which will include queries in standby-mode when refetching.\n   */\n  public reFetchObservableQueries(\n    includeStandby?: boolean\n  ): Promise<ApolloQueryResult<any>[]> {\n    return this.queryManager.reFetchObservableQueries(includeStandby);\n  }\n\n  /**\n   * Refetches specified active queries. Similar to \"reFetchObservableQueries()\" but with a specific list of queries.\n   *\n   * `refetchQueries()` is useful for use cases to imperatively refresh a selection of queries.\n   *\n   * It is important to remember that `refetchQueries()` *will* refetch specified active\n   * queries. This means that any components that might be mounted will execute\n   * their queries again using your network interface. If you do not want to\n   * re-execute any queries then you should make sure to stop watching any\n   * active queries.\n   */\n  public refetchQueries<\n    TCache extends ApolloCache<any> = ApolloCache<TCacheShape>,\n    TResult = Promise<ApolloQueryResult<any>>,\n  >(\n    options: RefetchQueriesOptions<TCache, TResult>\n  ): RefetchQueriesResult<TResult> {\n    const map = this.queryManager.refetchQueries(\n      options as RefetchQueriesOptions<ApolloCache<TCacheShape>, TResult>\n    );\n    const queries: ObservableQuery<any>[] = [];\n    const results: InternalRefetchQueriesResult<TResult>[] = [];\n\n    map.forEach((result, obsQuery) => {\n      queries.push(obsQuery);\n      results.push(result);\n    });\n\n    const result = Promise.all<TResult>(\n      results as TResult[]\n    ) as RefetchQueriesResult<TResult>;\n\n    // In case you need the raw results immediately, without awaiting\n    // Promise.all(results):\n    result.queries = queries;\n    result.results = results;\n\n    // If you decide to ignore the result Promise because you're using\n    // result.queries and result.results instead, you shouldn't have to worry\n    // about preventing uncaught rejections for the Promise.all result.\n    result.catch((error) => {\n      invariant.debug(\n        `In client.refetchQueries, Promise.all promise rejected with error %o`,\n        error\n      );\n    });\n\n    return result;\n  }\n\n  /**\n   * Get all currently active `ObservableQuery` objects, in a `Map` keyed by\n   * query ID strings.\n   *\n   * An \"active\" query is one that has observers and a `fetchPolicy` other than\n   * \"standby\" or \"cache-only\".\n   *\n   * You can include all `ObservableQuery` objects (including the inactive ones)\n   * by passing \"all\" instead of \"active\", or you can include just a subset of\n   * active queries by passing an array of query names or DocumentNode objects.\n   */\n  public getObservableQueries(\n    include: RefetchQueriesInclude = \"active\"\n  ): Map<string, ObservableQuery<any>> {\n    return this.queryManager.getObservableQueries(include);\n  }\n\n  /**\n   * Exposes the cache's complete state, in a serializable format for later restoration.\n   */\n  public extract(optimistic?: boolean): TCacheShape {\n    return this.cache.extract(optimistic);\n  }\n\n  /**\n   * Replaces existing state in the cache (if any) with the values expressed by\n   * `serializedState`.\n   *\n   * Called when hydrating a cache (server side rendering, or offline storage),\n   * and also (potentially) during hot reloads.\n   */\n  public restore(serializedState: TCacheShape): ApolloCache<TCacheShape> {\n    return this.cache.restore(serializedState);\n  }\n\n  /**\n   * Add additional local resolvers.\n   */\n  public addResolvers(resolvers: Resolvers | Resolvers[]) {\n    this.localState.addResolvers(resolvers);\n  }\n\n  /**\n   * Set (override existing) local resolvers.\n   */\n  public setResolvers(resolvers: Resolvers | Resolvers[]) {\n    this.localState.setResolvers(resolvers);\n  }\n\n  /**\n   * Get all registered local resolvers.\n   */\n  public getResolvers() {\n    return this.localState.getResolvers();\n  }\n\n  /**\n   * Set a custom local state fragment matcher.\n   */\n  public setLocalStateFragmentMatcher(fragmentMatcher: FragmentMatcher) {\n    this.localState.setFragmentMatcher(fragmentMatcher);\n  }\n\n  /**\n   * Define a new ApolloLink (or link chain) that Apollo Client will use.\n   */\n  public setLink(newLink: ApolloLink) {\n    this.link = this.queryManager.link = newLink;\n  }\n\n  public get defaultContext() {\n    return this.queryManager.defaultContext;\n  }\n\n  /**\n   * @experimental\n   * This is not a stable API - it is used in development builds to expose\n   * information to the DevTools.\n   * Use at your own risk!\n   * For more details, see [Memory Management](https://www.apollographql.com/docs/react/caching/memory-management/#measuring-cache-usage)\n   *\n   * @example\n   * ```ts\n   * console.log(client.getMemoryInternals())\n   * ```\n   * Logs output in the following JSON format:\n   * @example\n   * ```json\n   *{\n   *  limits:     {\n   *    parser: 1000,\n   *    canonicalStringify: 1000,\n   *    print: 2000,\n   *    'documentTransform.cache': 2000,\n   *    'queryManager.getDocumentInfo': 2000,\n   *    'PersistedQueryLink.persistedQueryHashes': 2000,\n   *    'fragmentRegistry.transform': 2000,\n   *    'fragmentRegistry.lookup': 1000,\n   *    'fragmentRegistry.findFragmentSpreads': 4000,\n   *    'cache.fragmentQueryDocuments': 1000,\n   *    'removeTypenameFromVariables.getVariableDefinitions': 2000,\n   *    'inMemoryCache.maybeBroadcastWatch': 5000,\n   *    'inMemoryCache.executeSelectionSet': 10000,\n   *    'inMemoryCache.executeSubSelectedArray': 5000\n   *  },\n   *  sizes: {\n   *    parser: 26,\n   *    canonicalStringify: 4,\n   *    print: 14,\n   *    addTypenameDocumentTransform: [\n   *      {\n   *        cache: 14,\n   *      },\n   *    ],\n   *    queryManager: {\n   *      getDocumentInfo: 14,\n   *      documentTransforms: [\n   *        {\n   *          cache: 14,\n   *        },\n   *        {\n   *          cache: 14,\n   *        },\n   *      ],\n   *    },\n   *    fragmentRegistry: {\n   *      findFragmentSpreads: 34,\n   *      lookup: 20,\n   *      transform: 14,\n   *    },\n   *    cache: {\n   *      fragmentQueryDocuments: 22,\n   *    },\n   *    inMemoryCache: {\n   *      executeSelectionSet: 4345,\n   *      executeSubSelectedArray: 1206,\n   *      maybeBroadcastWatch: 32,\n   *    },\n   *    links: [\n   *      {\n   *        PersistedQueryLink: {\n   *          persistedQueryHashes: 14,\n   *        },\n   *      },\n   *      {\n   *        removeTypenameFromVariables: {\n   *          getVariableDefinitions: 14,\n   *        },\n   *      },\n   *    ],\n   *  },\n   * }\n   *```\n   */\n  public getMemoryInternals?: typeof getApolloClientMemoryInternals;\n}\n\nif (__DEV__) {\n  ApolloClient.prototype.getMemoryInternals = getApolloClientMemoryInternals;\n}\n"]}