{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/core/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { DocumentNode, GraphQLFormattedError } from \"graphql\";\n\nimport type { ApolloCache } from \"../cache/index.js\";\nimport type { FetchResult } from \"../link/core/index.js\";\nimport type { ApolloError } from \"../errors/index.js\";\nimport type { NetworkStatus } from \"./networkStatus.js\";\nimport type { Resolver } from \"./LocalState.js\";\nimport type { ObservableQuery } from \"./ObservableQuery.js\";\nimport type { QueryOptions } from \"./watchQueryOptions.js\";\nimport type { Cache } from \"../cache/index.js\";\nimport type { IsStrictlyAny } from \"../utilities/index.js\";\nimport type { Unmasked } from \"../masking/index.js\";\n\nexport type { TypedDocumentNode } from \"@graphql-typed-document-node/core\";\n\nexport type MethodKeys<T> = {\n  [P in keyof T]: T[P] extends Function ? P : never;\n}[keyof T];\n\nexport interface DefaultContext extends Record<string, any> {}\n\nexport type OnQueryUpdated<TResult> = (\n  observableQuery: ObservableQuery<any>,\n  diff: Cache.DiffResult<any>,\n  lastDiff: Cache.DiffResult<any> | undefined\n) => boolean | TResult;\n\nexport type RefetchQueryDescriptor = string | DocumentNode;\nexport type InternalRefetchQueryDescriptor =\n  | RefetchQueryDescriptor\n  | QueryOptions;\n\ntype RefetchQueriesIncludeShorthand = \"all\" | \"active\";\n\nexport type RefetchQueriesInclude =\n  | RefetchQueryDescriptor[]\n  | RefetchQueriesIncludeShorthand;\n\nexport type InternalRefetchQueriesInclude =\n  | InternalRefetchQueryDescriptor[]\n  | RefetchQueriesIncludeShorthand;\n\n// Used by ApolloClient[\"refetchQueries\"]\n// TODO Improve documentation comments for this public type.\nexport interface RefetchQueriesOptions<\n  TCache extends ApolloCache<any>,\n  TResult,\n> {\n  updateCache?: (cache: TCache) => void;\n  // The client.refetchQueries method discourages passing QueryOptions, by\n  // restricting the public type of options.include to exclude QueryOptions as\n  // an available array element type (see InternalRefetchQueriesInclude for a\n  // version of RefetchQueriesInclude that allows legacy QueryOptions objects).\n  include?: RefetchQueriesInclude;\n  optimistic?: boolean;\n  // If no onQueryUpdated function is provided, any queries affected by the\n  // updateCache function or included in the options.include array will be\n  // refetched by default. Passing null instead of undefined disables this\n  // default refetching behavior for affected queries, though included queries\n  // will still be refetched.\n  onQueryUpdated?: OnQueryUpdated<TResult> | null;\n}\n\n// The client.refetchQueries method returns a thenable (PromiseLike) object\n// whose result is an array of Promise.resolve'd TResult values, where TResult\n// is whatever type the (optional) onQueryUpdated function returns. When no\n// onQueryUpdated function is given, TResult defaults to ApolloQueryResult<any>\n// (thanks to default type parameters for client.refetchQueries).\nexport type RefetchQueriesPromiseResults<TResult> =\n  // If onQueryUpdated returns any, all bets are off, so the results array must\n  // be a generic any[] array, which is much less confusing than the union type\n  // we get if we don't check for any. I hoped `any extends TResult` would do\n  // the trick here, instead of IsStrictlyAny, but you can see for yourself what\n  // fails in the refetchQueries tests if you try making that simplification.\n  IsStrictlyAny<TResult> extends true ? any[]\n  : // If the onQueryUpdated function passed to client.refetchQueries returns true\n  // or false, that means either to refetch the query (true) or to skip the\n  // query (false). Since refetching produces an ApolloQueryResult<any>, and\n  // skipping produces nothing, the fully-resolved array of all results produced\n  // will be an ApolloQueryResult<any>[], when TResult extends boolean.\n  TResult extends boolean ? ApolloQueryResult<any>[]\n  : // If onQueryUpdated returns a PromiseLike<U>, that thenable will be passed as\n  // an array element to Promise.all, so we infer/unwrap the array type U here.\n  TResult extends PromiseLike<infer U> ? U[]\n  : // All other onQueryUpdated results end up in the final Promise.all array as\n    // themselves, with their original TResult type. Note that TResult will\n    // default to ApolloQueryResult<any> if no onQueryUpdated function is passed\n    // to client.refetchQueries.\n    TResult[];\n\n// The result of client.refetchQueries is thenable/awaitable, if you just want\n// an array of fully resolved results, but you can also access the raw results\n// immediately by examining the additional { queries, results } properties of\n// the RefetchQueriesResult<TResult> object.\nexport interface RefetchQueriesResult<TResult>\n  extends Promise<RefetchQueriesPromiseResults<TResult>> {\n  // An array of ObservableQuery objects corresponding 1:1 to TResult values\n  // in the results arrays (both the TResult[] array below, and the results\n  // array resolved by the Promise above).\n  queries: ObservableQuery<any>[];\n  // These are the raw TResult values returned by any onQueryUpdated functions\n  // that were invoked by client.refetchQueries.\n  results: InternalRefetchQueriesResult<TResult>[];\n}\n\n// Used by QueryManager[\"refetchQueries\"]\nexport interface InternalRefetchQueriesOptions<\n  TCache extends ApolloCache<any>,\n  TResult,\n> extends Omit<RefetchQueriesOptions<TCache, TResult>, \"include\"> {\n  // Just like the refetchQueries option for a mutation, an array of strings,\n  // DocumentNode objects, and/or QueryOptions objects, or one of the shorthand\n  // strings \"all\" or \"active\", to select every (active) query.\n  include?: InternalRefetchQueriesInclude;\n  // This part of the API is a (useful) implementation detail, but need not be\n  // exposed in the public client.refetchQueries API (above).\n  removeOptimistic?: string;\n}\n\nexport type InternalRefetchQueriesResult<TResult> =\n  // If onQueryUpdated returns a boolean, that's equivalent to refetching the\n  // query when the boolean is true and skipping the query when false, so the\n  // internal type of refetched results is Promise<ApolloQueryResult<any>>.\n  TResult extends boolean ? Promise<ApolloQueryResult<any>>\n  : // Otherwise, onQueryUpdated returns whatever it returns. If onQueryUpdated is\n    // not provided, TResult defaults to Promise<ApolloQueryResult<any>> (see the\n    // generic type parameters of client.refetchQueries).\n    TResult;\n\nexport type InternalRefetchQueriesMap<TResult> = Map<\n  ObservableQuery<any>,\n  InternalRefetchQueriesResult<TResult>\n>;\n\n// TODO Remove this unnecessary type in Apollo Client 4.\nexport type { QueryOptions as PureQueryOptions };\n\nexport type OperationVariables = Record<string, any>;\n\nexport interface ApolloQueryResult<T> {\n  data: T;\n  /**\n   * A list of any errors that occurred during server-side execution of a GraphQL operation.\n   * See https://www.apollographql.com/docs/react/data/error-handling/ for more information.\n   */\n  errors?: ReadonlyArray<GraphQLFormattedError>;\n  /**\n   * The single Error object that is passed to onError and useQuery hooks, and is often thrown during manual `client.query` calls.\n   * This will contain both a NetworkError field and any GraphQLErrors.\n   * See https://www.apollographql.com/docs/react/data/error-handling/ for more information.\n   */\n  error?: ApolloError;\n  loading: boolean;\n  networkStatus: NetworkStatus;\n  // If result.data was read from the cache with missing fields,\n  // result.partial will be true. Otherwise, result.partial will be falsy\n  // (usually because the property is absent from the result object).\n  partial?: boolean;\n}\n\n// This is part of the public API, people write these functions in `updateQueries`.\nexport type MutationQueryReducer<T> = (\n  previousResult: Record<string, any>,\n  options: {\n    mutationResult: FetchResult<Unmasked<T>>;\n    queryName: string | undefined;\n    queryVariables: Record<string, any>;\n  }\n) => Record<string, any>;\n\nexport type MutationQueryReducersMap<T = { [key: string]: any }> = {\n  [queryName: string]: MutationQueryReducer<T>;\n};\n\n/**\n * @deprecated Use `MutationUpdaterFunction` instead.\n */\nexport type MutationUpdaterFn<T = { [key: string]: any }> = (\n  // The MutationUpdaterFn type is broken because it mistakenly uses the same\n  // type parameter T for both the cache and the mutationResult. Do not use this\n  // type unless you absolutely need it for backwards compatibility.\n  cache: ApolloCache<T>,\n  mutationResult: FetchResult<T>\n) => void;\n\nexport type MutationUpdaterFunction<\n  TData,\n  TVariables,\n  TContext,\n  TCache extends ApolloCache<any>,\n> = (\n  cache: TCache,\n  result: Omit<FetchResult<Unmasked<TData>>, \"context\">,\n  options: {\n    context?: TContext;\n    variables?: TVariables;\n  }\n) => void;\nexport interface Resolvers {\n  [key: string]: {\n    [field: string]: Resolver;\n  };\n}\n"]}