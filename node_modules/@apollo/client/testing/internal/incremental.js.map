{"version": 3, "file": "incremental.js", "sourceRoot": "", "sources": ["../../../src/testing/internal/incremental.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAOpD,OAAO,EACL,cAAc,IAAI,kBAAkB,EACpC,iBAAiB,EACjB,eAAe,GAChB,MAAM,iBAAiB,CAAC;AAGzB,IAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAExC,MAAM,UAAU,qBAAqB,CAAS,EAI7C;QAHC,eAAe,qBAAA;IAKf,IAAM,KAAK,GAAG,MAAM,EAAE,CAAC;IACvB,IAAI,gBAAgB,GAAoD,IAAI,CAAC;IAC7E,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAE7B,IAAM,KAAK,GAAkC,EAAE,CAAC;IAEhD,SAAS,YAAY;QACnB,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,IAAI,KAAK,CAAC;QACV,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC;YAC/B,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;gBACpB,gBAAgB,CAAC,KAAK,EAAE,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;IACH,CAAC;IAED,SAAS,YAAY;QACnB,OAAO,IAAI,kBAAkB,CAAwC;YACnE,KAAK,YAAC,CAAC;gBACL,gBAAgB,GAAG,CAAC,CAAC;gBACrB,YAAY,EAAE,CAAC;YACjB,CAAC;SACF,CAAC;aACC,WAAW,CACV,IAAI,eAAe,CAAgD;YACjE,SAAS,EAAE,UAAC,KAAK,EAAE,UAAU;gBAC3B,UAAU,CAAC,OAAO,CAChB,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC;oBACtC,uDAAuD;oBACvD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;oBACrB,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC,CAC3D,CAAC;gBACF,gBAAgB,GAAG,IAAI,CAAC;YAC1B,CAAC;SACF,CAAC,CACH;aACA,WAAW,CAAC,IAAI,iBAAiB,EAAE,CAAC,CAAC;IAC1C,CAAC;IAED,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC;QAC5B,KAAK,YAAC,KAAK,EAAE,IAAI;YACf,OAAO,OAAO,CAAC,OAAO,CACpB,IAAI,QAAQ,CACV,YAAY,EAAyE,EACrF;gBACE,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,eAAe;aACzB,CACF,CACF,CAAC;QACJ,CAAC;KACF,CAAC,CAAC;IAEH,SAAS,SAAS,CAAC,KAA6B;QAC9C,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAElB,IAAI,gBAAgB,EAAE,CAAC;YACrB,YAAY,EAAE,CAAC;QACjB,CAAC;IACH,CAAC;IAED,SAAS,KAAK;QACZ,SAAS,CAAC,KAAK,CAAC,CAAC;QAEjB,gBAAgB,GAAG,IAAI,CAAC;QACxB,gBAAgB,GAAG,KAAK,CAAC;IAC3B,CAAC;IAED,SAAS,OAAO,CAAC,KAAa,EAAE,OAAgB;;QAC9C,SAAS,uBAAM,KAAK,gBAAG,aAAa,IAAG,OAAO,OAAG,CAAC;QAElD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,KAAK,EAAE,CAAC;QACV,CAAC;IACH,CAAC;IAED,OAAO;QACL,QAAQ,UAAA;QACR,OAAO,SAAA;QACP,KAAK,OAAA;KACN,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,eAAe;IAIvB,IAAA,KAAwB,qBAAqB,CAGjD;QACA,eAAe,EAAE,IAAI,OAAO,CAAC;YAC3B,cAAc,EAAE,mDAAmD;SACpE,CAAC;KACH,CAAC,EAPM,QAAQ,cAAA,EAAE,OAAO,aAOvB,CAAC;IACH,OAAO;QACL,QAAQ,UAAA;QACR,mBAAmB,YACjB,KAA4D;YAE5D,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;QACD,sBAAsB,YACpB,KAA+D;YAE/D,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAChC,CAAC;QACD,iBAAiB,YAAC,MAA+B;YAC/C,OAAO,CACL;gBACE,OAAO,EAAE,IAAI;gBACb,WAAW,EAAE;oBACX;wBACE,kEAAkE;wBAClE,MAAM,EAAE,MAAwB;qBACjC;iBACF;aACiE,EACpE,IAAI,CACL,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,+BAA+B;IAIvC,IAAA,KAAwB,qBAAqB,CAEjD;QACA,eAAe,EAAE,IAAI,OAAO,CAAC;YAC3B,cAAc,EAAE,iBAAiB;SAClC,CAAC;KACH,CAAC,EANM,QAAQ,cAAA,EAAE,OAAO,aAMvB,CAAC;IAEH,gBAAgB,EAAE,CAAC;IAEnB,SAAS,gBAAgB;QACvB,OAAO,CAAC,EAAS,EAAE,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,OAAO;QACL,QAAQ,UAAA;QACR,gBAAgB,kBAAA;QAChB,oBAAoB,YAClB,OAA2D,EAC3D,OAAc;YAAd,wBAAA,EAAA,cAAc;YAEd,OAAO,CAAC,EAAE,OAAO,SAAA,EAAE,EAAE,OAAO,CAAC,CAAC;QAChC,CAAC;QACD,qBAAqB,YAAC,MAAqC;YACzD,OAAO,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,QAAA,EAAE,EAAE,KAAK,CAAC,CAAC;QAC5C,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import { HttpLink } from \"../../link/http/index.js\";\nimport type {\n  GraphQLFormattedError,\n  InitialIncrementalExecutionResult,\n  SubsequentIncrementalExecutionResult,\n} from \"graphql-17-alpha2\";\nimport type { GraphQLError } from \"graphql\";\nimport {\n  ReadableStream as NodeReadableStream,\n  TextEncoderStream,\n  TransformStream,\n} from \"node:stream/web\";\nimport type { ApolloPayloadResult } from \"../../core/index.js\";\n\nconst hasNextSymbol = Symbol(\"hasNext\");\n\nexport function mockIncrementalStream<Chunks>({\n  responseHeaders,\n}: {\n  responseHeaders: Headers;\n}) {\n  type Payload = Chunks & { [hasNextSymbol]: boolean };\n  const CLOSE = Symbol();\n  let streamController: ReadableStreamDefaultController<Payload> | null = null;\n  let sentInitialChunk = false;\n\n  const queue: Array<Payload | typeof CLOSE> = [];\n\n  function processQueue() {\n    if (!streamController) {\n      throw new Error(\"Cannot process queue without stream controller\");\n    }\n\n    let chunk;\n    while ((chunk = queue.shift())) {\n      if (chunk === CLOSE) {\n        streamController.close();\n      } else {\n        streamController.enqueue(chunk);\n      }\n    }\n  }\n\n  function createStream() {\n    return new NodeReadableStream<Chunks & { [hasNextSymbol]: boolean }>({\n      start(c) {\n        streamController = c;\n        processQueue();\n      },\n    })\n      .pipeThrough(\n        new TransformStream<Chunks & { [hasNextSymbol]: boolean }, string>({\n          transform: (chunk, controller) => {\n            controller.enqueue(\n              (!sentInitialChunk ? \"\\r\\n---\\r\\n\" : \"\") +\n                \"content-type: application/json; charset=utf-8\\r\\n\\r\\n\" +\n                JSON.stringify(chunk) +\n                (chunk[hasNextSymbol] ? \"\\r\\n---\\r\\n\" : \"\\r\\n-----\\r\\n\")\n            );\n            sentInitialChunk = true;\n          },\n        })\n      )\n      .pipeThrough(new TextEncoderStream());\n  }\n\n  const httpLink = new HttpLink({\n    fetch(input, init) {\n      return Promise.resolve(\n        new Response(\n          createStream() satisfies NodeReadableStream<Uint8Array> as ReadableStream<Uint8Array>,\n          {\n            status: 200,\n            headers: responseHeaders,\n          }\n        )\n      );\n    },\n  });\n\n  function queueNext(event: Payload | typeof CLOSE) {\n    queue.push(event);\n\n    if (streamController) {\n      processQueue();\n    }\n  }\n\n  function close() {\n    queueNext(CLOSE);\n\n    streamController = null;\n    sentInitialChunk = false;\n  }\n\n  function enqueue(chunk: Chunks, hasNext: boolean) {\n    queueNext({ ...chunk, [hasNextSymbol]: hasNext });\n\n    if (!hasNext) {\n      close();\n    }\n  }\n\n  return {\n    httpLink,\n    enqueue,\n    close,\n  };\n}\n\nexport function mockDeferStream<\n  TData = Record<string, unknown>,\n  TExtensions = Record<string, unknown>,\n>() {\n  const { httpLink, enqueue } = mockIncrementalStream<\n    | InitialIncrementalExecutionResult<TData, TExtensions>\n    | SubsequentIncrementalExecutionResult<TData, TExtensions>\n  >({\n    responseHeaders: new Headers({\n      \"Content-Type\": 'multipart/mixed; boundary=\"-\"; deferSpec=20220824',\n    }),\n  });\n  return {\n    httpLink,\n    enqueueInitialChunk(\n      chunk: InitialIncrementalExecutionResult<TData, TExtensions>\n    ) {\n      enqueue(chunk, chunk.hasNext);\n    },\n    enqueueSubsequentChunk(\n      chunk: SubsequentIncrementalExecutionResult<TData, TExtensions>\n    ) {\n      enqueue(chunk, chunk.hasNext);\n    },\n    enqueueErrorChunk(errors: GraphQLFormattedError[]) {\n      enqueue(\n        {\n          hasNext: true,\n          incremental: [\n            {\n              // eslint-disable-next-line @typescript-eslint/no-restricted-types\n              errors: errors as GraphQLError[],\n            },\n          ],\n        } satisfies SubsequentIncrementalExecutionResult<TData, TExtensions>,\n        true\n      );\n    },\n  };\n}\n\nexport function mockMultipartSubscriptionStream<\n  TData = Record<string, unknown>,\n  TExtensions = Record<string, unknown>,\n>() {\n  const { httpLink, enqueue } = mockIncrementalStream<\n    ApolloPayloadResult<TData, TExtensions>\n  >({\n    responseHeaders: new Headers({\n      \"Content-Type\": \"multipart/mixed\",\n    }),\n  });\n\n  enqueueHeartbeat();\n\n  function enqueueHeartbeat() {\n    enqueue({} as any, true);\n  }\n\n  return {\n    httpLink,\n    enqueueHeartbeat,\n    enqueuePayloadResult(\n      payload: ApolloPayloadResult<TData, TExtensions>[\"payload\"],\n      hasNext = true\n    ) {\n      enqueue({ payload }, hasNext);\n    },\n    enqueueProtocolErrors(errors: ApolloPayloadResult[\"errors\"]) {\n      enqueue({ payload: null, errors }, false);\n    },\n  };\n}\n"]}