{"version": 3, "file": "renderHookAsync.js", "sourceRoot": "", "sources": ["../../../../src/testing/internal/rtl/renderHookAsync.tsx"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,iFAAiF;AACjF,sEAAsE;AACtE,yEAAyE;AACzE,yEAAyE;;AAQzE,OAAO,KAAK,QAAQ,MAAM,WAAW,CAAC;AACtC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAO/C,MAAM,UAAgB,eAAe;wDAOnC,cAA+C,EAC/C,OAA6E;QAiB7E,SAAS,aAAa,CAAC,EAItB;gBAHC,mBAAmB,yBAAA;YAInB,IAAM,aAAa,GAAG,cAAc,CAAC,mBAAmB,CAAC,CAAC;YAE1D,KAAK,CAAC,SAAS,CAAC;gBACd,MAAM,CAAC,OAAO,GAAG,aAAa,CAAC;YACjC,CAAC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAOD,SAAS,QAAQ,CAAC,qBAA6B;YAC7C,OAAO,YAAY,CACjB,oBAAC,aAAa,IAAC,mBAAmB,EAAE,qBAAsB,GAAI,CAC/D,CAAC;QACJ,CAAC;;QAxCD,wBAAA,EAAA,YAA6E;;;;oBAErE,YAAY,GAAuB,OAAO,aAA9B,EAAK,aAAa,UAAK,OAAO,EAA5C,gBAAkC,CAAF,CAAa;oBAEnD,mBAAmB;oBACnB,IAAI,aAAa,CAAC,UAAU,IAAI,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;wBAChE,KAAK,GAAG,IAAI,KAAK,CACrB,gEAAgE;4BAC9D,mEAAmE;4BACnE,gIAAgI,CACnI,CAAC;wBACF,KAAK,CAAC,iBAAiB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;wBAChD,MAAM,KAAK,CAAC;oBACd,CAAC;oBAEK,MAAM,GAAG,KAAK,CAAC,SAAS,EAAiC,CAAC;oBAgBpB,qBAAM,WAAW,CAC3D,oBAAC,aAAa,IAAC,mBAAmB,EAAE,YAAa,GAAI,EACrD,aAAa,CACd,EAAA;;oBAHK,KAAsC,SAG3C,EAHiB,YAAY,cAAA,EAAE,OAAO,aAAA;oBAWvC,sBAAO,EAAE,MAAM,QAAA,EAAE,QAAQ,UAAA,EAAE,OAAO,SAAA,EAAE,EAAC;;;;CACtC", "sourcesContent": ["// This is a helper required for React 19 testing.\n// There are currently multiple directions this could play out in RTL and none of\n// them has been released yet, so we are inlining this helper for now.\n// See https://github.com/testing-library/react-testing-library/pull/1214\n// and https://github.com/testing-library/react-testing-library/pull/1365\n\nimport type { queries, Queries } from \"@testing-library/dom\";\nimport type {\n  RenderHookOptions,\n  RenderHookResult,\n} from \"@testing-library/react\";\nimport type * as ReactDOMClient from \"react-dom/client\";\nimport * as ReactDOM from \"react-dom\";\nimport * as React from \"react\";\nimport { renderAsync } from \"./renderAsync.js\";\n\ntype RendererableContainer = ReactDOMClient.Container;\ntype HydrateableContainer = Parameters<\n  (typeof ReactDOMClient)[\"hydrateRoot\"]\n>[0];\n\nexport async function renderHookAsync<\n  Result,\n  Props,\n  Q extends Queries = typeof queries,\n  Container extends RendererableContainer | HydrateableContainer = HTMLElement,\n  BaseElement extends RendererableContainer | HydrateableContainer = Container,\n>(\n  renderCallback: (initialProps: Props) => Result,\n  options: RenderHookOptions<Props, Q, Container, BaseElement> | undefined = {}\n): Promise<RenderHookResult<Result, Props>> {\n  const { initialProps, ...renderOptions } = options;\n\n  // @ts-expect-error\n  if (renderOptions.legacyRoot && typeof ReactDOM.render !== \"function\") {\n    const error = new Error(\n      \"`legacyRoot: true` is not supported in this version of React. \" +\n        \"If your app runs React 19 or later, you should remove this flag. \" +\n        \"If your app runs React 18 or earlier, visit https://react.dev/blog/2022/03/08/react-18-upgrade-guide for upgrade instructions.\"\n    );\n    Error.captureStackTrace(error, renderHookAsync);\n    throw error;\n  }\n\n  const result = React.createRef<Result>() as { current: Result };\n\n  function TestComponent({\n    renderCallbackProps,\n  }: {\n    renderCallbackProps: Props;\n  }) {\n    const pendingResult = renderCallback(renderCallbackProps);\n\n    React.useEffect(() => {\n      result.current = pendingResult;\n    });\n\n    return null;\n  }\n\n  const { rerender: baseRerender, unmount } = await renderAsync(\n    <TestComponent renderCallbackProps={initialProps!} />,\n    renderOptions\n  );\n\n  function rerender(rerenderCallbackProps?: Props) {\n    return baseRerender(\n      <TestComponent renderCallbackProps={rerenderCallbackProps!} />\n    );\n  }\n\n  return { result, rerender, unmount };\n}\n"]}