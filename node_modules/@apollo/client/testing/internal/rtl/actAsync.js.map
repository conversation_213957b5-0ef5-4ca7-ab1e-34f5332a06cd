{"version": 3, "file": "actAsync.js", "sourceRoot": "", "sources": ["../../../../src/testing/internal/rtl/actAsync.ts"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,iFAAiF;AACjF,sEAAsE;AACtE,yEAAyE;AACzE,yEAAyE;;AAEzE,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,KAAK,wBAAwB,MAAM,sBAAsB,CAAC;AAEjE,IAAM,QAAQ,GACZ,OAAO,KAAK,CAAC,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,wBAAwB,CAAC,GAAG,CAAC;AAE7E,MAAM,UAAU,QAAQ,CAAI,KAA2B;IAAvD,iBAIC;IAHC,OAAO,QAAQ,CAAC;;;wBACP,qBAAM,KAAK,EAAE,EAAA;wBAApB,sBAAO,SAAa,EAAC;;;SACtB,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// This is a helper required for React 19 testing.\n// There are currently multiple directions this could play out in RTL and none of\n// them has been released yet, so we are inlining this helper for now.\n// See https://github.com/testing-library/react-testing-library/pull/1214\n// and https://github.com/testing-library/react-testing-library/pull/1365\n\nimport * as React from \"react\";\nimport * as DeprecatedReactTestUtils from \"react-dom/test-utils\";\n\nconst reactAct =\n  typeof React.act === \"function\" ? React.act : DeprecatedReactTestUtils.act;\n\nexport function actAsync<T>(scope: () => T | Promise<T>): Promise<T> {\n  return reactAct(async () => {\n    return await scope();\n  });\n}\n"]}