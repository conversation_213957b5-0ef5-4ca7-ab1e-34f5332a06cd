{"version": 3, "file": "renderAsync.js", "sourceRoot": "", "sources": ["../../../../src/testing/internal/rtl/renderAsync.ts"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,iFAAiF;AACjF,sEAAsE;AACtE,yEAAyE;AACzE,yEAAyE;;AAIzE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAqBrD,MAAM,UAAU,WAAW;IAA3B,iBAIC;IAJ2B,cAAmB;SAAnB,UAAmB,EAAnB,qBAAmB,EAAnB,IAAmB;QAAnB,yBAAmB;;IAC7C,OAAO,GAAG,CAAC;;;wBACF,qBAAM,MAAM,eAAI,IAAI,GAAC;wBAA5B,sBAAO,SAAqB,EAAC;;;SAC9B,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["// This is a helper required for React 19 testing.\n// There are currently multiple directions this could play out in RTL and none of\n// them has been released yet, so we are inlining this helper for now.\n// See https://github.com/testing-library/react-testing-library/pull/1214\n// and https://github.com/testing-library/react-testing-library/pull/1365\n\nimport type { queries, Queries } from \"@testing-library/dom\";\nimport type { RenderOptions, RenderResult } from \"@testing-library/react\";\nimport { act, render } from \"@testing-library/react\";\nimport type * as ReactDOMClient from \"react-dom/client\";\n\ntype RendererableContainer = ReactDOMClient.Container;\ntype HydrateableContainer = Parameters<\n  (typeof ReactDOMClient)[\"hydrateRoot\"]\n>[0];\n\nexport function renderAsync<\n  Q extends Queries = typeof queries,\n  Container extends RendererableContainer | HydrateableContainer = HTMLElement,\n  BaseElement extends RendererableContainer | HydrateableContainer = Container,\n>(\n  ui: React.ReactNode,\n  options: RenderOptions<Q, Container, BaseElement>\n): Promise<RenderResult<Q, Container, BaseElement>>;\nexport function renderAsync(\n  ui: React.ReactNode,\n  options?: Omit<RenderOptions, \"queries\"> | undefined\n): Promise<RenderResult>;\n\nexport function renderAsync(...args: [any, any]): any {\n  return act(async () => {\n    return await render(...args);\n  });\n}\n"]}