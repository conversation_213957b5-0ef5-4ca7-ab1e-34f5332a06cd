{"version": 3, "file": "ObservableStream.js", "sourceRoot": "", "sources": ["../../../src/testing/internal/ObservableStream.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAC9D,OAAO,EAAE,MAAM,EAAE,MAAM,eAAe,CAAC;AACvC,OAAO,KAAK,YAAY,MAAM,oBAAoB,CAAC;AAKnD,OAAO,EAAE,cAAc,EAAE,MAAM,iBAAiB,CAAC;AAUjD;IAKE,0BAAY,UAAyB;QAArC,iBAUC;QAZO,gBAAW,GAAuC,EAAE,CAAC;QAG3D,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,CAAqB;YACnD,KAAK,EAAE,UAAC,UAAU;gBAChB,KAAI,CAAC,YAAY,GAAG,UAAU,CAAC,SAAS,CACtC,UAAC,KAAK,IAAK,OAAA,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,OAAA,EAAE,CAAC,EAA3C,CAA2C,EACtD,UAAC,KAAK,IAAK,OAAA,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,OAAA,EAAE,CAAC,EAA5C,CAA4C,EACvD,cAAM,OAAA,UAAU,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,EAAxC,CAAwC,CAC/C,CAAC;YACJ,CAAC;SACF,CAAC,CAAC,SAAS,EAAE,CAAC;IACjB,CAAC;IAED,+BAAI,GAAJ,UAAK,EAAmC;YAAnC,qBAAiC,EAAE,KAAA,EAAjC,eAAa,EAAb,OAAO,mBAAG,GAAG,KAAA;QAClB,uEAAuE;QACvE,qDAAqD;QACrD,IAAI,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAExC,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,uEAAuE;YACvE,wEAAwE;YACxE,wEAAwE;YACxE,oEAAoE;YACpE,6DAA6D;YAC7D,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YACrC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACvC,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,aAAa;YACb,IAAI,OAAO,CAAqB,UAAC,CAAC,EAAE,MAAM;gBACxC,UAAU,CACR,MAAM,EACN,OAAO,EACP,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAC5C,CAAC;YACJ,CAAC,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED,+BAAI,GAAJ,UAAK,EAAmC;YAAnC,qBAAiC,EAAE,KAAA,EAAjC,eAAa,EAAb,OAAO,mBAAG,GAAG,KAAA;QAClB,OAAO,OAAO,CAAC,IAAI,CAAC;YAClB,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC,aAAa,EAAE;YAChD,IAAI,OAAO,CAAqB,UAAC,CAAC,EAAE,MAAM;gBACxC,UAAU,CACR,MAAM,EACN,OAAO,EACP,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAC5C,CAAC;YACJ,CAAC,CAAC;SACH,CAAC,CAAC;IACL,CAAC;IAED,sCAAW,GAAX;QACE,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;IAClC,CAAC;IAEK,mCAAQ,GAAd,UAAe,OAAqB;;;;;4BACpB,qBAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAA;;wBAAhC,KAAK,GAAG,SAAwB;wBACtC,cAAc,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;wBAClE,sBAAQ,KAA+C,CAAC,KAAK,EAAC;;;;KAC/D;IAEK,oCAAS,GAAf,UAAgB,OAAqB;;;;;4BACrB,qBAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAA;;wBAAhC,KAAK,GAAG,SAAwB;wBACtC,cAAc,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;wBACnE,sBAAQ,KAAgD,CAAC,KAAK,EAAC;;;;KAChE;IAEK,uCAAY,GAAlB,UAAmB,OAAqB;;;;;4BACxB,qBAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAA;;wBAAhC,KAAK,GAAG,SAAwB;wBACtC,cAAc,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;;;;;KAC7C;IAEa,wCAAa,GAA3B;;;gBACE,sBAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,UAAC,MAAM,IAAK,OAAA,MAAM,CAAC,KAAM,EAAb,CAAa,CAAC,EAAC;;;KAC3D;IACH,uBAAC;AAAD,CAAC,AAjFD,IAiFC;;AAED,gFAAgF;AAChF,iFAAiF;AACjF,uDAAuD;AACvD,SAAS,cAAc,CACrB,WAAiC,EACjC,aAAmC;IAEnC,qDAAqD;IACrD,yHAAyH;IACzH,IAAM,OAAO,GAAG,MAAM,CAAC,WAAW,EAAE,aAAa,kCAC5C,iBAAiB,EAAE;QACtB,gBAAgB;cAChB,CAAC;IAEH,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO;IACT,CAAC;IAED,IAAM,IAAI,GAAG,YAAY,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;IAEvE,MAAM,IAAI,KAAK,CACb,IAAI;QACF,MAAM;QACN,YAAY,CAAC,oBAAoB,CAC/B,aAAa,EACb,WAAW,EACX,UAAU,EACV,UAAU,EACV,IAAI,CACL,CACJ,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB;IACxB,mIAAmI;IACnI,IAAM,oBAAoB,GAAG,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IAClE,OAAQ,UAAkB,CAAC,oBAAoB,CAAC,CAAC,qBAAqB,CAAC;AACzE,CAAC", "sourcesContent": ["import type { Tester } from \"@jest/expect-utils\";\nimport { equals, iterableEquality } from \"@jest/expect-utils\";\nimport { expect } from \"@jest/globals\";\nimport * as matcherUtils from \"jest-matcher-utils\";\nimport type {\n  Observable,\n  ObservableSubscription,\n} from \"../../utilities/index.js\";\nimport { ReadableStream } from \"node:stream/web\";\n\nexport interface TakeOptions {\n  timeout?: number;\n}\ntype ObservableEvent<T> =\n  | { type: \"next\"; value: T }\n  | { type: \"error\"; error: any }\n  | { type: \"complete\" };\n\nexport class ObservableStream<T> {\n  private reader: ReadableStreamDefaultReader<ObservableEvent<T>>;\n  private subscription!: ObservableSubscription;\n  private readerQueue: Array<Promise<ObservableEvent<T>>> = [];\n\n  constructor(observable: Observable<T>) {\n    this.reader = new ReadableStream<ObservableEvent<T>>({\n      start: (controller) => {\n        this.subscription = observable.subscribe(\n          (value) => controller.enqueue({ type: \"next\", value }),\n          (error) => controller.enqueue({ type: \"error\", error }),\n          () => controller.enqueue({ type: \"complete\" })\n        );\n      },\n    }).getReader();\n  }\n\n  peek({ timeout = 100 }: TakeOptions = {}) {\n    // Calling `peek` multiple times in a row should not advance the reader\n    // multiple times until this value has been consumed.\n    let readerPromise = this.readerQueue[0];\n\n    if (!readerPromise) {\n      // Since this.reader.read() advances the reader in the stream, we don't\n      // want to consume this promise entirely, otherwise we will miss it when\n      // calling `take`. Instead, we push it into a queue that can be consumed\n      // by `take` the next time its called so that we avoid advancing the\n      // reader until we are finished processing all peeked values.\n      readerPromise = this.readNextValue();\n      this.readerQueue.push(readerPromise);\n    }\n\n    return Promise.race([\n      readerPromise,\n      new Promise<ObservableEvent<T>>((_, reject) => {\n        setTimeout(\n          reject,\n          timeout,\n          new Error(\"Timeout waiting for next event\")\n        );\n      }),\n    ]);\n  }\n\n  take({ timeout = 100 }: TakeOptions = {}) {\n    return Promise.race([\n      this.readerQueue.shift() || this.readNextValue(),\n      new Promise<ObservableEvent<T>>((_, reject) => {\n        setTimeout(\n          reject,\n          timeout,\n          new Error(\"Timeout waiting for next event\")\n        );\n      }),\n    ]);\n  }\n\n  unsubscribe() {\n    this.subscription.unsubscribe();\n  }\n\n  async takeNext(options?: TakeOptions): Promise<T> {\n    const event = await this.take(options);\n    validateEquals(event, { type: \"next\", value: expect.anything() });\n    return (event as ObservableEvent<T> & { type: \"next\" }).value;\n  }\n\n  async takeError(options?: TakeOptions): Promise<any> {\n    const event = await this.take(options);\n    validateEquals(event, { type: \"error\", error: expect.anything() });\n    return (event as ObservableEvent<T> & { type: \"error\" }).error;\n  }\n\n  async takeComplete(options?: TakeOptions): Promise<void> {\n    const event = await this.take(options);\n    validateEquals(event, { type: \"complete\" });\n  }\n\n  private async readNextValue() {\n    return this.reader.read().then((result) => result.value!);\n  }\n}\n\n// Lightweight expect(...).toEqual(...) check that avoids using `expect` so that\n// `expect.assertions(num)` does not double count assertions when using the take*\n// functions inside of expect(stream).toEmit* matchers.\nfunction validateEquals(\n  actualEvent: ObservableEvent<any>,\n  expectedEvent: ObservableEvent<any>\n) {\n  // Uses the same matchers as expect(...).toEqual(...)\n  // https://github.com/jestjs/jest/blob/611d1a4ba0008d67b5dcda485177f0813b2b573e/packages/expect/src/matchers.ts#L626-L629\n  const isEqual = equals(actualEvent, expectedEvent, [\n    ...getCustomMatchers(),\n    iterableEquality,\n  ]);\n\n  if (isEqual) {\n    return;\n  }\n\n  const hint = matcherUtils.matcherHint(\"toEqual\", \"stream\", \"expected\");\n\n  throw new Error(\n    hint +\n      \"\\n\\n\" +\n      matcherUtils.printDiffOrStringify(\n        expectedEvent,\n        actualEvent,\n        \"Expected\",\n        \"Received\",\n        true\n      )\n  );\n}\n\nfunction getCustomMatchers(): Array<Tester> {\n  // https://github.com/jestjs/jest/blob/611d1a4ba0008d67b5dcda485177f0813b2b573e/packages/expect/src/jestMatchersObject.ts#L141-L143\n  const JEST_MATCHERS_OBJECT = Symbol.for(\"$$jest-matchers-object\");\n  return (globalThis as any)[JEST_MATCHERS_OBJECT].customEqualityTesters;\n}\n"]}