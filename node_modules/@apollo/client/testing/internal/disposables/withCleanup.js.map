{"version": 3, "file": "withCleanup.js", "sourceRoot": "", "sources": ["../../../../src/testing/internal/disposables/withCleanup.ts"], "names": [], "mappings": ";AAAA,gBAAgB;AAChB,MAAM,UAAU,WAAW,CACzB,IAAO,EACP,OAA0B;;IAE1B,6BACK,IAAI,aACP,GAAC,MAAM,CAAC,OAAO,IAAf;QACE,OAAO,CAAC,IAAI,CAAC,CAAC;QACd,+FAA+F;QAC/F,wDAAwD;QACxD,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YAC1B,IAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;QACzC,CAAC;IACH,CAAC,OACD;AACJ,CAAC", "sourcesContent": ["/** @internal */\nexport function withCleanup<T extends object>(\n  item: T,\n  cleanup: (item: T) => void\n): T & Disposable {\n  return {\n    ...item,\n    [Symbol.dispose]() {\n      cleanup(item);\n      // if `item` already has a cleanup function, we also need to call the original cleanup function\n      // (e.g. if something is wrapped in `withCleanup` twice)\n      if (Symbol.dispose in item) {\n        (item as Disposable)[Symbol.dispose]();\n      }\n    },\n  };\n}\n"]}