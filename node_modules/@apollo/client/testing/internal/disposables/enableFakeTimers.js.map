{"version": 3, "file": "enableFakeTimers.js", "sourceRoot": "", "sources": ["../../../../src/testing/internal/disposables/enableFakeTimers.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAS/C,MAAM,UAAU,gBAAgB,CAC9B,MAAkD;IAElD,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;QAChC,4DAA4D;QAC5D,wDAAwD;QACxD,OAAO,WAAW,CAAC,EAAE,EAAE,cAAO,CAAC,CAAC,CAAC;IACnC,CAAC;IAED,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC3B,OAAO,WAAW,CAAC,EAAE,EAAE;QACrB,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;YAChC,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5B,IAAI,CAAC,aAAa,EAAE,CAAC;QACvB,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { withCleanup } from \"./withCleanup.js\";\n\ndeclare global {\n  interface DateConstructor {\n    /* <PERSON><PERSON> uses @sinonjs/fake-timers, that add this flag */\n    isFake: boolean;\n  }\n}\n\nexport function enableFakeTimers(\n  config?: FakeTimersConfig | LegacyFakeTimersConfig\n) {\n  if (global.Date.isFake === true) {\n    // Nothing to do here, fake timers have already been set up.\n    // That also means we don't want to clean that up later.\n    return withCleanup({}, () => {});\n  }\n\n  jest.useFakeTimers(config);\n  return withCleanup({}, () => {\n    if (global.Date.isFake === true) {\n      jest.runOnlyPendingTimers();\n      jest.useRealTimers();\n    }\n  });\n}\n"]}