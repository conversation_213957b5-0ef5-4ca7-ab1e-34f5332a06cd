{"version": 3, "file": "spyOnConsole.js", "sourceRoot": "", "sources": ["../../../../src/testing/internal/disposables/spyOnConsole.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAE/C,IAAM,IAAI,GAAG,cAAO,CAAC,CAAC;AACtB,IAAM,OAAO,GAAG,UAAC,GAAqB,IAAK,OAAA,GAAG,CAAC,WAAW,EAAE,EAAjB,CAAiB,CAAC;AAS7D,gBAAgB;AAChB,MAAM,UAAU,YAAY;IAC1B,eAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,0BAAc;;IAEd,IAAM,KAAK,GAAG,EAAiB,CAAC;IAChC,KAAkB,UAAK,EAAL,eAAK,EAAL,mBAAK,EAAL,IAAK,EAAE,CAAC;QAArB,IAAM,GAAG,cAAA;QACZ,aAAa;QACb,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,WAAW,CAAC,KAAK,EAAE,UAAC,KAAK;QAC9B,KAAkB,UAA0C,EAA1C,KAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAuB,EAA1C,cAA0C,EAA1C,IAA0C,EAAE,CAAC;YAA1D,IAAM,GAAG,SAAA;YACZ,OAAO,CAAC,GAAG,CAAC,CAAC;QACf,CAAC;IACH,CAAC,CAAC,CAAC;AACL,CAAC;AAED,YAAY,CAAC,aAAa,GAAG;IAC3B,eAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,0BAAc;;IAEd,OAAA,WAAW,CAAC,YAAY,eAAI,KAAK,GAAG,UAAC,KAAK;QACxC,KAAkB,UAA0C,EAA1C,KAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAuB,EAA1C,cAA0C,EAA1C,IAA0C,EAAE,CAAC;YAA1D,IAAM,GAAG,SAAA;YACZ,MAAM,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC;QAChC,CAAC;IACH,CAAC,CAAC;AAJF,CAIE,CAAC", "sourcesContent": ["import { withCleanup } from \"./withCleanup.js\";\n\nconst noOp = () => {};\nconst restore = (spy: jest.SpyInstance) => spy.mockRestore();\n\ntype ConsoleMethod = \"log\" | \"info\" | \"warn\" | \"error\" | \"debug\";\n\ntype Spies<Keys extends ConsoleMethod[]> = Record<\n  Keys[number],\n  jest.SpyInstance<void, any[], any>\n>;\n\n/** @internal */\nexport function spyOnConsole<Keys extends ConsoleMethod[]>(\n  ...spyOn: Keys\n): Spies<Keys> & Disposable {\n  const spies = {} as Spies<Keys>;\n  for (const key of spyOn) {\n    // @ts-ignore\n    spies[key] = jest.spyOn(console, key).mockImplementation(noOp);\n  }\n  return withCleanup(spies, (spies) => {\n    for (const spy of Object.values(spies) as jest.SpyInstance[]) {\n      restore(spy);\n    }\n  });\n}\n\nspyOnConsole.takeSnapshots = <Keys extends ConsoleMethod[]>(\n  ...spyOn: Keys\n): Spies<Keys> & Disposable =>\n  withCleanup(spyOnConsole(...spyOn), (spies) => {\n    for (const spy of Object.values(spies) as jest.SpyInstance[]) {\n      expect(spy).toMatchSnapshot();\n    }\n  });\n"]}