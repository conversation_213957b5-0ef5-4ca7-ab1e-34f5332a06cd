{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/testing/internal/disposables/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AACjD,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAC/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC", "sourcesContent": ["export { spyOnConsole } from \"./spyOnConsole.js\";\nexport { withCleanup } from \"./withCleanup.js\";\nexport { enableFakeTimers } from \"./enableFakeTimers.js\";\nexport { withProdMode } from \"./withProdMode.js\";\n"]}