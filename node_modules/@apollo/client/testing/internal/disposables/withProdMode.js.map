{"version": 3, "file": "withProdMode.js", "sourceRoot": "", "sources": ["../../../../src/testing/internal/disposables/withProdMode.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAE/C,MAAM,UAAU,YAAY;IAC1B,IAAM,IAAI,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;IAClC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;IAE/D,OAAO,WAAW,CAAC,IAAI,EAAE,UAAC,EAAW;YAAT,OAAO,aAAA;QACjC,MAAM,CAAC,cAAc,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;IACnE,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import { withCleanup } from \"./withCleanup.js\";\n\nexport function withProdMode() {\n  const prev = { prevDEV: __DEV__ };\n  Object.defineProperty(globalThis, \"__DEV__\", { value: false });\n\n  return withCleanup(prev, ({ prevDEV }) => {\n    Object.defineProperty(globalThis, \"__DEV__\", { value: prevDEV });\n  });\n}\n"]}