{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../src/testing/internal/scenarios/index.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,MAAM,wBAAwB,CAAC;AASrE,MAAM,UAAU,eAAe;IAC7B,IAAM,KAAK,GAA6D,GAAG,2HAAA,wDAI1E,IAAA,CAAC;IAEF,IAAM,KAAK,GAAqC;QAC9C;YACE,OAAO,EAAE,EAAE,KAAK,OAAA,EAAE;YAClB,MAAM,EAAE,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE;YACvC,KAAK,EAAE,EAAE;SACV;KACF,CAAC;IAEF,OAAO,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,EAAE,CAAC;AAC1B,CAAC;AAcD,MAAM,UAAU,kBAAkB;IAChC,IAAM,KAAK,GACT,GAAG,mMAAA,gIAOF,IAAA,CAAC;IACJ,IAAM,UAAU,GAAG,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAErE,IAAM,KAAK,GAAwC,kBAAI,UAAU,QAAE,GAAG,CACpE,UAAC,IAAI,EAAE,KAAK,IAAK,OAAA,CAAC;QAChB,OAAO,EAAE,EAAE,KAAK,OAAA,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;QACxD,MAAM,EAAE;YACN,IAAI,EAAE;gBACJ,SAAS,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,MAAA,EAAE;aACpE;SACF;QACD,KAAK,EAAE,EAAE;KACV,CAAC,EARe,CAQf,CACH,CAAC;IAEF,OAAO,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,EAAE,CAAC;AAC1B,CAAC;AA0BD,MAAM,UAAU,wBAAwB;IACtC,IAAM,QAAQ,GAAG,GAAG,yQAAA,sMAWnB,IAAA,CAAC;IACF,IAAM,KAAK,GAGP,QAAQ,CAAC;IAEb,IAAM,aAAa,GAGf,QAAQ,CAAC;IAEb,IAAM,UAAU,GAAG,CAAC,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IAErE,IAAM,KAAK,GAA8C,kBAAI,UAAU,QAAE,GAAG,CAC1E,UAAC,IAAI,EAAE,KAAK,IAAK,OAAA,CAAC;QAChB,OAAO,EAAE,EAAE,KAAK,OAAA,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;QACxD,MAAM,EAAE;YACN,IAAI,EAAE;gBACJ,SAAS,EAAE,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,MAAA,EAAE;aACpE;SACF;QACD,KAAK,EAAE,EAAE;KACV,CAAC,EARe,CAQf,CACH,CAAC;IAEF,OAAO,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,EAAE,aAAa,eAAA,EAAE,CAAC;AACzC,CAAC;AAED,MAAM,UAAU,eAAe,CAC7B,KAAQ,EACR,KAAW,EACX,QAAgB;IADhB,sBAAA,EAAA,WAAW;IACX,yBAAA,EAAA,gBAAgB;IAEhB,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI;QACpB,OAAA,QAAQ,CAAC,CAAC,uBAAM,IAAI,KAAE,KAAK,OAAA,IAAG,CAAC,YAAG,KAAK,OAAA,IAAK,IAAI,CAAE;IAAlD,CAAkD,CACnD,CAAC;AACJ,CAAC;AAiBD,MAAM,UAAU,kBAAkB;IAChC,IAAM,KAAK,GACT,GAAG,+OAAA,4KAOF,IAAA,CAAC;IAEJ,IAAM,IAAI,GAAG,wBAAwB,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,UAAC,MAAM,EAAE,KAAK,IAAK,OAAA,CAAC;QACtE,UAAU,EAAE,QAAQ;QACpB,MAAM,QAAA;QACN,QAAQ,EAAE,KAAK,GAAG,CAAC;KACpB,CAAC,EAJqE,CAIrE,CAAC,CAAC;IAEJ,IAAM,IAAI,GAAG,IAAI,UAAU,CAAC,UAAC,SAAS;QAC9B,IAAA,KAA4B,SAAS,CAAC,SAAS,EAA7C,cAAU,EAAV,MAAM,mBAAG,CAAC,KAAA,EAAE,aAAS,EAAT,KAAK,mBAAG,CAAC,KAAwB,CAAC;QACtD,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC;QAEnD,OAAO,IAAI,UAAU,CAAC,UAAC,QAAQ;YAC7B,UAAU,CAAC;gBACT,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,OAAO,SAAA,EAAE,EAAE,CAAC,CAAC;gBACrC,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACtB,CAAC,EAAE,EAAE,CAAC,CAAC;QACT,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,EAAE,KAAK,OAAA,EAAE,IAAI,MAAA,EAAE,IAAI,MAAA,EAAE,CAAC;AAC/B,CAAC", "sourcesContent": ["import { ApolloLink, Observable, gql } from \"../../../core/index.js\";\nimport type { TypedDocumentNode } from \"../../../core/index.js\";\nimport type { MaskedDocumentNode } from \"../../../masking/index.js\";\nimport type { MockedResponse } from \"../../core/index.js\";\n\nexport interface SimpleCaseData {\n  greeting: string;\n}\n\nexport function setupSimpleCase() {\n  const query: TypedDocumentNode<SimpleCaseData, Record<string, never>> = gql`\n    query GreetingQuery {\n      greeting\n    }\n  `;\n\n  const mocks: MockedResponse<SimpleCaseData>[] = [\n    {\n      request: { query },\n      result: { data: { greeting: \"Hello\" } },\n      delay: 20,\n    },\n  ];\n\n  return { query, mocks };\n}\n\nexport interface VariablesCaseData {\n  character: {\n    __typename: \"Character\";\n    id: string;\n    name: string;\n  };\n}\n\nexport interface VariablesCaseVariables {\n  id: string;\n}\n\nexport function setupVariablesCase() {\n  const query: TypedDocumentNode<VariablesCaseData, VariablesCaseVariables> =\n    gql`\n      query CharacterQuery($id: ID!) {\n        character(id: $id) {\n          id\n          name\n        }\n      }\n    `;\n  const CHARACTERS = [\"Spider-Man\", \"Black Widow\", \"Iron Man\", \"Hulk\"];\n\n  const mocks: MockedResponse<VariablesCaseData>[] = [...CHARACTERS].map(\n    (name, index) => ({\n      request: { query, variables: { id: String(index + 1) } },\n      result: {\n        data: {\n          character: { __typename: \"Character\", id: String(index + 1), name },\n        },\n      },\n      delay: 20,\n    })\n  );\n\n  return { mocks, query };\n}\n\nexport type MaskedVariablesCaseFragment = {\n  __typename: \"Character\";\n  name: string;\n} & { \" $fragmentName\"?: \"MaskedVariablesCaseFragment\" };\n\nexport interface MaskedVariablesCaseData {\n  character: {\n    __typename: \"Character\";\n    id: string;\n  } & {\n    \" $fragmentRefs\"?: {\n      MaskedVariablesCaseFragment: MaskedVariablesCaseFragment;\n    };\n  };\n}\n\nexport interface UnmaskedVariablesCaseData {\n  character: {\n    __typename: \"Character\";\n    id: string;\n    name: string;\n  };\n}\n\nexport function setupMaskedVariablesCase() {\n  const document = gql`\n    query CharacterQuery($id: ID!) {\n      character(id: $id) {\n        id\n        ...CharacterFragment\n      }\n    }\n\n    fragment CharacterFragment on Character {\n      name\n    }\n  `;\n  const query: MaskedDocumentNode<\n    MaskedVariablesCaseData,\n    VariablesCaseVariables\n  > = document;\n\n  const unmaskedQuery: TypedDocumentNode<\n    MaskedVariablesCaseData,\n    VariablesCaseVariables\n  > = document;\n\n  const CHARACTERS = [\"Spider-Man\", \"Black Widow\", \"Iron Man\", \"Hulk\"];\n\n  const mocks: MockedResponse<MaskedVariablesCaseData>[] = [...CHARACTERS].map(\n    (name, index) => ({\n      request: { query, variables: { id: String(index + 1) } },\n      result: {\n        data: {\n          character: { __typename: \"Character\", id: String(index + 1), name },\n        },\n      },\n      delay: 20,\n    })\n  );\n\n  return { mocks, query, unmaskedQuery };\n}\n\nexport function addDelayToMocks<T extends MockedResponse<unknown>[]>(\n  mocks: T,\n  delay = 150,\n  override = false\n) {\n  return mocks.map((mock) =>\n    override ? { ...mock, delay } : { delay, ...mock }\n  );\n}\n\ninterface Letter {\n  __typename: \"Letter\";\n  letter: string;\n  position: number;\n}\n\nexport interface PaginatedCaseData {\n  letters: Letter[];\n}\n\nexport interface PaginatedCaseVariables {\n  limit?: number;\n  offset?: number;\n}\n\nexport function setupPaginatedCase() {\n  const query: TypedDocumentNode<PaginatedCaseData, PaginatedCaseVariables> =\n    gql`\n      query LettersQuery($limit: Int, $offset: Int) {\n        letters(limit: $limit, offset: $offset) {\n          letter\n          position\n        }\n      }\n    `;\n\n  const data = \"ABCDEFGHIJKLMNOPQRSTUV\".split(\"\").map((letter, index) => ({\n    __typename: \"Letter\",\n    letter,\n    position: index + 1,\n  }));\n\n  const link = new ApolloLink((operation) => {\n    const { offset = 0, limit = 2 } = operation.variables;\n    const letters = data.slice(offset, offset + limit);\n\n    return new Observable((observer) => {\n      setTimeout(() => {\n        observer.next({ data: { letters } });\n        observer.complete();\n      }, 10);\n    });\n  });\n\n  return { query, link, data };\n}\n"]}