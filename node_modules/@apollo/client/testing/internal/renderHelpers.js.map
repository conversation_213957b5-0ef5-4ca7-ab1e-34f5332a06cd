{"version": 3, "file": "renderHelpers.js", "sourceRoot": "", "sources": ["../../../src/testing/internal/renderHelpers.tsx"], "names": [], "mappings": ";AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,MAAM,EAAE,MAAM,wBAAwB,CAAC;AAGhD,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AAEtD,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAC;AAU5D,MAAM,UAAU,mBAAmB,CACjC,MAAyB,EACzB,OAEmB;IAFnB,wBAAA,EAAA,UAEK,KAAK,CAAC,QAAQ;IAInB,OAAO,UAAC,EAAY;YAAV,QAAQ,cAAA;QAChB,OAAO,CACL,oBAAC,cAAc,IAAC,MAAM,EAAE,MAAM;YAC5B,oBAAC,OAAO,QAAE,QAAQ,CAAW,CACd,CAClB,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,gBAAgB,CAK9B,EAAgB,EAChB,EAIqD;IAHnD,IAAA,MAAM,YAAA,EACN,OAAO,aAAA,EACJ,aAAa,cAHlB,qBAIC,CADiB;IAGlB,OAAO,MAAM,CAAC,EAAE,wBACX,aAAa,KAChB,OAAO,EAAE,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,IAC7C,CAAC;AACL,CAAC;AASD,MAAM,UAAU,iBAAiB,CAC/B,aAAuC,EACvC,OAEmB;IAFnB,wBAAA,EAAA,UAEK,KAAK,CAAC,QAAQ;IAInB,OAAO,UAAC,EAAY;YAAV,QAAQ,cAAA;QAChB,OAAO,CACL,oBAAC,cAAc,eAAK,aAAa;YAC/B,oBAAC,OAAO,QAAE,QAAQ,CAAW,CACd,CAClB,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,eAAe,CAK7B,EAAgB,EAChB,EAGoD;IAFlD,IAAA,OAAO,aAAA,EACJ,aAAa,cAFlB,WAGC,CADiB;IAGlB,OAAO,MAAM,CAAC,EAAE,wBACX,aAAa,KAChB,OAAO,EAAE,iBAAiB,CAAC,aAAa,EAAE,OAAO,CAAC,IAClD,CAAC;AACL,CAAC", "sourcesContent": ["import * as React from \"react\";\nimport type { ReactElement } from \"react\";\nimport { render } from \"@testing-library/react\";\nimport type { Queries, RenderOptions, queries } from \"@testing-library/react\";\nimport type { ApolloClient } from \"../../core/index.js\";\nimport { ApolloProvider } from \"../../react/index.js\";\nimport type { MockedProviderProps } from \"../react/MockedProvider.js\";\nimport { MockedProvider } from \"../react/MockedProvider.js\";\n\nexport interface RenderWithClientOptions<\n  Q extends Queries = typeof queries,\n  Container extends Element | DocumentFragment = HTMLElement,\n  BaseElement extends Element | DocumentFragment = Container,\n> extends RenderOptions<Q, Container, BaseElement> {\n  client: ApolloClient<any>;\n}\n\nexport function createClientWrapper(\n  client: ApolloClient<any>,\n  Wrapper: React.JSXElementConstructor<{\n    children: React.ReactNode;\n  }> = React.Fragment\n): React.JSXElementConstructor<{\n  children: React.ReactNode;\n}> {\n  return ({ children }) => {\n    return (\n      <ApolloProvider client={client}>\n        <Wrapper>{children}</Wrapper>\n      </ApolloProvider>\n    );\n  };\n}\n\nexport function renderWithClient<\n  Q extends Queries = typeof queries,\n  Container extends Element | DocumentFragment = HTMLElement,\n  BaseElement extends Element | DocumentFragment = Container,\n>(\n  ui: ReactElement,\n  {\n    client,\n    wrapper,\n    ...renderOptions\n  }: RenderWithClientOptions<Q, Container, BaseElement>\n) {\n  return render(ui, {\n    ...renderOptions,\n    wrapper: createClientWrapper(client, wrapper),\n  });\n}\n\nexport interface RenderWithMocksOptions<\n  Q extends Queries = typeof queries,\n  Container extends Element | DocumentFragment = HTMLElement,\n  BaseElement extends Element | DocumentFragment = Container,\n> extends RenderOptions<Q, Container, BaseElement>,\n    MockedProviderProps<any> {}\n\nexport function createMockWrapper(\n  renderOptions: MockedProviderProps<any>,\n  Wrapper: React.JSXElementConstructor<{\n    children: React.ReactNode;\n  }> = React.Fragment\n): React.JSXElementConstructor<{\n  children: React.ReactNode;\n}> {\n  return ({ children }) => {\n    return (\n      <MockedProvider {...renderOptions}>\n        <Wrapper>{children}</Wrapper>\n      </MockedProvider>\n    );\n  };\n}\n\nexport function renderWithMocks<\n  Q extends Queries = typeof queries,\n  Container extends Element | DocumentFragment = HTMLElement,\n  BaseElement extends Element | DocumentFragment = Container,\n>(\n  ui: ReactElement,\n  {\n    wrapper,\n    ...renderOptions\n  }: RenderWithMocksOptions<Q, Container, BaseElement>\n) {\n  return render(ui, {\n    ...renderOptions,\n    wrapper: createMockWrapper(renderOptions, wrapper),\n  });\n}\n"]}