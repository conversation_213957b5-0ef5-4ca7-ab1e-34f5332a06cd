{"version": 3, "file": "MockedProvider.js", "sourceRoot": "", "sources": ["../../../src/testing/react/MockedProvider.tsx"], "names": [], "mappings": ";AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAG/B,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAE,aAAa,IAAI,KAAK,EAAE,MAAM,sBAAsB,CAAC;AAC9D,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAE9D,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AA0B5C;IAAoC,kCAGnC;IAKC,wBAAY,KAA0B;QACpC,YAAA,MAAK,YAAC,KAAK,CAAC,SAAC;QAEP,IAAA,KASF,KAAI,CAAC,KAAK,EARZ,KAAK,WAAA,EACL,WAAW,iBAAA,EACX,cAAc,oBAAA,EACd,KAAK,WAAA,EACL,SAAS,eAAA,EACT,IAAI,UAAA,EACJ,YAAY,kBAAA,EACZ,yBAAyB,EAAzB,iBAAiB,mBAAG,KAAK,KACb,CAAC;QACf,IAAM,MAAM,GAAG,IAAI,YAAY,CAAC;YAC9B,KAAK,EAAE,KAAK,IAAI,IAAI,KAAK,CAAC,EAAE,WAAW,aAAA,EAAE,CAAC;YAC1C,cAAc,gBAAA;YACd,iBAAiB,mBAAA;YACjB,IAAI,EAAE,IAAI,IAAI,IAAI,QAAQ,CAAC,KAAK,IAAI,EAAE,EAAE,WAAW,EAAE,EAAE,YAAY,cAAA,EAAE,CAAC;YACtE,SAAS,WAAA;SACV,CAAC,CAAC;QAEH,KAAI,CAAC,KAAK,GAAG;YACX,MAAM,QAAA;SACP,CAAC;;IACJ,CAAC;IAEM,+BAAM,GAAb;QACQ,IAAA,KAA2B,IAAI,CAAC,KAAK,EAAnC,QAAQ,cAAA,EAAE,UAAU,gBAAe,CAAC;QACpC,IAAA,MAAM,GAAK,IAAI,CAAC,KAAK,OAAf,CAAgB;QAE9B,OAAO,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC;YACnC,oBAAC,cAAc,IAAC,MAAM,EAAE,MAAM,IAC3B,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAO,UAAU,EAAG,CACtD;YACnB,CAAC,CAAC,IAAI,CAAC;IACX,CAAC;IAEM,6CAAoB,GAA3B;QACE,oEAAoE;QACpE,mDAAmD;QACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IAC3B,CAAC;IA7Ca,2BAAY,GAAwB;QAChD,WAAW,EAAE,IAAI;KAClB,CAAC;IA4CJ,qBAAC;CAAA,AAlDD,CAAoC,KAAK,CAAC,SAAS,GAkDlD;SAlDY,cAAc", "sourcesContent": ["import * as React from \"react\";\n\nimport type { DefaultOptions } from \"../../core/index.js\";\nimport { ApolloClient } from \"../../core/index.js\";\nimport { InMemoryCache as Cache } from \"../../cache/index.js\";\nimport { Apollo<PERSON>rovider } from \"../../react/context/index.js\";\nimport type { MockedResponse } from \"../core/index.js\";\nimport { MockLink } from \"../core/index.js\";\nimport type { ApolloLink } from \"../../link/core/index.js\";\nimport type { Resolvers } from \"../../core/index.js\";\nimport type { ApolloCache } from \"../../cache/index.js\";\n\nexport interface MockedProviderProps<TSerializedCache = {}> {\n  mocks?: ReadonlyArray<MockedResponse<any, any>>;\n  addTypename?: boolean;\n  defaultOptions?: DefaultOptions;\n  cache?: ApolloCache<TSerializedCache>;\n  resolvers?: Resolvers;\n  childProps?: object;\n  children?: any;\n  link?: ApolloLink;\n  showWarnings?: boolean;\n  /**\n   * If set to true, the MockedProvider will try to connect to the Apollo DevTools.\n   * Defaults to false.\n   */\n  connectToDevTools?: boolean;\n}\n\nexport interface MockedProviderState {\n  client: ApolloClient<any>;\n}\n\nexport class MockedProvider extends React.Component<\n  MockedProviderProps,\n  MockedProviderState\n> {\n  public static defaultProps: MockedProviderProps = {\n    addTypename: true,\n  };\n\n  constructor(props: MockedProviderProps) {\n    super(props);\n\n    const {\n      mocks,\n      addTypename,\n      defaultOptions,\n      cache,\n      resolvers,\n      link,\n      showWarnings,\n      connectToDevTools = false,\n    } = this.props;\n    const client = new ApolloClient({\n      cache: cache || new Cache({ addTypename }),\n      defaultOptions,\n      connectToDevTools,\n      link: link || new MockLink(mocks || [], addTypename, { showWarnings }),\n      resolvers,\n    });\n\n    this.state = {\n      client,\n    };\n  }\n\n  public render() {\n    const { children, childProps } = this.props;\n    const { client } = this.state;\n\n    return React.isValidElement(children) ?\n        <ApolloProvider client={client}>\n          {React.cloneElement(React.Children.only(children), { ...childProps })}\n        </ApolloProvider>\n      : null;\n  }\n\n  public componentWillUnmount() {\n    // Since this.state.client was created in the constructor, it's this\n    // MockedProvider's responsibility to terminate it.\n    this.state.client.stop();\n  }\n}\n"]}