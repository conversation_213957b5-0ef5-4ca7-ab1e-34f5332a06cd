export type { MockedResponse, MockLinkOptions, ResultFunction, } from "./mocking/mockLink.js";
export { MockLink, mockSingleLink } from "./mocking/mockLink.js";
export { MockSubscriptionLink, mockObservableLink, } from "./mocking/mockSubscriptionLink.js";
export { createMockClient } from "./mocking/mockClient.js";
export { default as subscribeAndCount } from "./subscribeAndCount.js";
export { itAsync } from "./itAsync.js";
export { wait, tick } from "./wait.js";
export * from "./withConsoleSpy.js";
//# sourceMappingURL=index.d.ts.map