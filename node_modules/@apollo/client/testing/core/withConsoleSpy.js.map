{"version": 3, "file": "withConsoleSpy.js", "sourceRoot": "", "sources": ["../../../src/testing/core/withConsoleSpy.ts"], "names": [], "mappings": "AAAA,SAAS,gBAAgB,CACvB,EAA2B,EAC3B,iBAA2C;IAE3C,OAAO;QAAA,iBASN;QAT2B,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QACxC,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;QACnD,GAAG,CAAC,kBAAkB,CAAC,cAAO,CAAC,CAAC,CAAC;QACjC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;YACzB,OAAO,CAAC,EAAE,aAAF,EAAE,uBAAF,EAAE,CAAE,KAAK,CAAC,KAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,OAAO,CAAC;YACT,MAAM,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC;YAC9B,GAAG,CAAC,SAAS,EAAE,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAED,yFAAyF;AACzF,MAAM,UAAU,YAAY,CAC1B,EAA+B;IAC/B,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,6BAAc;;IAEd,IAAI,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC7C,OAAO,EAAE,eAAI,IAAI,EAAE;AACrB,CAAC;AAED,yFAAyF;AACzF,MAAM,UAAU,cAAc,CAC5B,EAA+B;IAC/B,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,6BAAc;;IAEd,IAAI,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;IAC5C,OAAO,EAAE,eAAI,IAAI,EAAE;AACrB,CAAC;AAED,yFAAyF;AACzF,MAAM,UAAU,UAAU,CACxB,EAA+B;IAC/B,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,6BAAc;;IAEd,IAAI,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3C,OAAO,EAAE,eAAI,IAAI,EAAE;AACrB,CAAC", "sourcesContent": ["function wrapTestFunction(\n  fn: (...args: any[]) => any,\n  consoleMethodName: \"log\" | \"warn\" | \"error\"\n) {\n  return function (this: any, ...args: any[]) {\n    const spy = jest.spyOn(console, consoleMethodName);\n    spy.mockImplementation(() => {});\n    return new Promise((resolve) => {\n      resolve(fn?.apply(this, args));\n    }).finally(() => {\n      expect(spy).toMatchSnapshot();\n      spy.mockReset();\n    });\n  };\n}\n\n/** @deprecated This method will be removed in the next major version of Apollo Client */\nexport function withErrorSpy<TArgs extends any[], TResult>(\n  it: (...args: TArgs) => TResult,\n  ...args: TArgs\n) {\n  args[1] = wrapTestFunction(args[1], \"error\");\n  return it(...args);\n}\n\n/** @deprecated This method will be removed in the next major version of Apollo Client */\nexport function withWarningSpy<TArgs extends any[], TResult>(\n  it: (...args: TArgs) => TResult,\n  ...args: TArgs\n) {\n  args[1] = wrapTestFunction(args[1], \"warn\");\n  return it(...args);\n}\n\n/** @deprecated This method will be removed in the next major version of Apollo Client */\nexport function withLogSpy<TArgs extends any[], TResult>(\n  it: (...args: TArgs) => TResult,\n  ...args: TArgs\n) {\n  args[1] = wrapTestFunction(args[1], \"log\");\n  return it(...args);\n}\n"]}