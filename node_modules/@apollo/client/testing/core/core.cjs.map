{"version": 3, "file": "core.cjs", "sources": ["mocking/mockLink.js", "mocking/mockSubscriptionLink.js", "mocking/mockClient.js", "subscribeAndCount.js", "itAsync.js", "wait.js", "withConsoleSpy.js"], "sourcesContent": ["import { __assign, __extends } from \"tslib\";\nimport { invariant } from \"../../../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { ApolloLink } from \"../../../link/core/index.js\";\nimport { Observable, addTypenameToDocument, removeClientSetsFromDocument, cloneDeep, print, getOperationDefinition, getDefaultValues, removeDirectivesFromDocument, checkDocument, makeUniqueId, } from \"../../../utilities/index.js\";\nfunction requestToKey(request, addTypename) {\n    var queryString = request.query &&\n        print(addTypename ? addTypenameToDocument(request.query) : request.query);\n    var requestKey = { query: queryString };\n    return JSON.stringify(requestKey);\n}\nvar MockLink = /** @class */ (function (_super) {\n    __extends(MockLink, _super);\n    function MockLink(mockedResponses, addTypename, options) {\n        if (addTypename === void 0) { addTypename = true; }\n        if (options === void 0) { options = Object.create(null); }\n        var _a;\n        var _this = _super.call(this) || this;\n        _this.addTypename = true;\n        _this.showWarnings = true;\n        _this.mockedResponsesByKey = {};\n        _this.addTypename = addTypename;\n        _this.showWarnings = (_a = options.showWarnings) !== null && _a !== void 0 ? _a : true;\n        if (mockedResponses) {\n            mockedResponses.forEach(function (mockedResponse) {\n                _this.addMockedResponse(mockedResponse);\n            });\n        }\n        return _this;\n    }\n    MockLink.prototype.addMockedResponse = function (mockedResponse) {\n        var normalizedMockedResponse = this.normalizeMockedResponse(mockedResponse);\n        var key = requestToKey(normalizedMockedResponse.request, this.addTypename);\n        var mockedResponses = this.mockedResponsesByKey[key];\n        if (!mockedResponses) {\n            mockedResponses = [];\n            this.mockedResponsesByKey[key] = mockedResponses;\n        }\n        mockedResponses.push(normalizedMockedResponse);\n    };\n    MockLink.prototype.request = function (operation) {\n        var _this = this;\n        var _a;\n        this.operation = operation;\n        var key = requestToKey(operation, this.addTypename);\n        var unmatchedVars = [];\n        var requestVariables = operation.variables || {};\n        var mockedResponses = this.mockedResponsesByKey[key];\n        var responseIndex = mockedResponses ?\n            mockedResponses.findIndex(function (res, index) {\n                var mockedResponseVars = res.request.variables || {};\n                if (equal(requestVariables, mockedResponseVars)) {\n                    return true;\n                }\n                if (res.variableMatcher && res.variableMatcher(operation.variables)) {\n                    return true;\n                }\n                unmatchedVars.push(mockedResponseVars);\n                return false;\n            })\n            : -1;\n        var response = responseIndex >= 0 ? mockedResponses[responseIndex] : void 0;\n        // There have been platform- and engine-dependent differences with\n        // setInterval(fn, Infinity), so we pass 0 instead (but detect\n        // Infinity where we call observer.error or observer.next to pend\n        // indefinitely in those cases.)\n        var delay = (response === null || response === void 0 ? void 0 : response.delay) === Infinity ? 0 : (_a = response === null || response === void 0 ? void 0 : response.delay) !== null && _a !== void 0 ? _a : 0;\n        var configError;\n        if (!response) {\n            configError = new Error(\"No more mocked responses for the query: \".concat(print(operation.query), \"\\nExpected variables: \").concat(stringifyForDebugging(operation.variables), \"\\n\").concat(unmatchedVars.length > 0 ?\n                \"\\nFailed to match \".concat(unmatchedVars.length, \" mock\").concat(unmatchedVars.length === 1 ? \"\" : \"s\", \" for this query. The mocked response had the following variables:\\n\").concat(unmatchedVars.map(function (d) { return \"  \".concat(stringifyForDebugging(d)); }).join(\"\\n\"), \"\\n\")\n                : \"\"));\n            if (this.showWarnings) {\n                console.warn(configError.message +\n                    \"\\nThis typically indicates a configuration error in your mocks \" +\n                    \"setup, usually due to a typo or mismatched variable.\");\n            }\n        }\n        else {\n            if (response.maxUsageCount && response.maxUsageCount > 1) {\n                response.maxUsageCount--;\n            }\n            else {\n                mockedResponses.splice(responseIndex, 1);\n            }\n            var newData = response.newData;\n            if (newData) {\n                response.result = newData(operation.variables);\n                mockedResponses.push(response);\n            }\n            if (!response.result && !response.error && response.delay !== Infinity) {\n                configError = new Error(\"Mocked response should contain either `result`, `error` or a `delay` of `Infinity`: \".concat(key));\n            }\n        }\n        return new Observable(function (observer) {\n            var timer = setTimeout(function () {\n                if (configError) {\n                    try {\n                        // The onError function can return false to indicate that\n                        // configError need not be passed to observer.error. For\n                        // example, the default implementation of onError calls\n                        // observer.error(configError) and then returns false to\n                        // prevent this extra (harmless) observer.error call.\n                        if (_this.onError(configError, observer) !== false) {\n                            throw configError;\n                        }\n                    }\n                    catch (error) {\n                        observer.error(error);\n                    }\n                }\n                else if (response && response.delay !== Infinity) {\n                    if (response.error) {\n                        observer.error(response.error);\n                    }\n                    else {\n                        if (response.result) {\n                            observer.next(typeof response.result === \"function\" ?\n                                response.result(operation.variables)\n                                : response.result);\n                        }\n                        observer.complete();\n                    }\n                }\n            }, delay);\n            return function () {\n                clearTimeout(timer);\n            };\n        });\n    };\n    MockLink.prototype.normalizeMockedResponse = function (mockedResponse) {\n        var _a;\n        var newMockedResponse = cloneDeep(mockedResponse);\n        var queryWithoutClientOnlyDirectives = removeDirectivesFromDocument([{ name: \"connection\" }, { name: \"nonreactive\" }, { name: \"unmask\" }], checkDocument(newMockedResponse.request.query));\n        invariant(queryWithoutClientOnlyDirectives, 75);\n        newMockedResponse.request.query = queryWithoutClientOnlyDirectives;\n        var query = removeClientSetsFromDocument(newMockedResponse.request.query);\n        if (query) {\n            newMockedResponse.request.query = query;\n        }\n        mockedResponse.maxUsageCount = (_a = mockedResponse.maxUsageCount) !== null && _a !== void 0 ? _a : 1;\n        invariant(mockedResponse.maxUsageCount > 0, 76, mockedResponse.maxUsageCount);\n        this.normalizeVariableMatching(newMockedResponse);\n        return newMockedResponse;\n    };\n    MockLink.prototype.normalizeVariableMatching = function (mockedResponse) {\n        var request = mockedResponse.request;\n        if (mockedResponse.variableMatcher && request.variables) {\n            throw new Error(\"Mocked response should contain either variableMatcher or request.variables\");\n        }\n        if (!mockedResponse.variableMatcher) {\n            request.variables = __assign(__assign({}, getDefaultValues(getOperationDefinition(request.query))), request.variables);\n            mockedResponse.variableMatcher = function (vars) {\n                var requestVariables = vars || {};\n                var mockedResponseVariables = request.variables || {};\n                return equal(requestVariables, mockedResponseVariables);\n            };\n        }\n    };\n    return MockLink;\n}(ApolloLink));\nexport { MockLink };\n// Pass in multiple mocked responses, so that you can test flows that end up\n// making multiple queries to the server.\n// NOTE: The last arg can optionally be an `addTypename` arg.\nexport function mockSingleLink() {\n    var mockedResponses = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        mockedResponses[_i] = arguments[_i];\n    }\n    // To pull off the potential typename. If this isn't a boolean, we'll just\n    // set it true later.\n    var maybeTypename = mockedResponses[mockedResponses.length - 1];\n    var mocks = mockedResponses.slice(0, mockedResponses.length - 1);\n    if (typeof maybeTypename !== \"boolean\") {\n        mocks = mockedResponses;\n        maybeTypename = true;\n    }\n    return new MockLink(mocks, maybeTypename);\n}\n// This is similiar to the stringifyForDisplay utility we ship, but includes\n// support for NaN in addition to undefined. More values may be handled in the\n// future. This is not added to the primary stringifyForDisplay helper since it\n// is used for the cache and other purposes. We need this for debuggging only.\nexport function stringifyForDebugging(value, space) {\n    if (space === void 0) { space = 0; }\n    var undefId = makeUniqueId(\"undefined\");\n    var nanId = makeUniqueId(\"NaN\");\n    return JSON.stringify(value, function (_, value) {\n        if (value === void 0) {\n            return undefId;\n        }\n        if (Number.isNaN(value)) {\n            return nanId;\n        }\n        return value;\n    }, space)\n        .replace(new RegExp(JSON.stringify(undefId), \"g\"), \"<undefined>\")\n        .replace(new RegExp(JSON.stringify(nanId), \"g\"), \"NaN\");\n}\n//# sourceMappingURL=mockLink.js.map", "import { __extends } from \"tslib\";\nimport { Observable } from \"../../../utilities/index.js\";\nimport { ApolloLink } from \"../../../link/core/index.js\";\nvar MockSubscriptionLink = /** @class */ (function (_super) {\n    __extends(MockSubscriptionLink, _super);\n    function MockSubscriptionLink() {\n        var _this = _super.call(this) || this;\n        _this.unsubscribers = [];\n        _this.setups = [];\n        _this.observers = [];\n        return _this;\n    }\n    MockSubscriptionLink.prototype.request = function (operation) {\n        var _this = this;\n        this.operation = operation;\n        return new Observable(function (observer) {\n            _this.setups.forEach(function (x) { return x(); });\n            _this.observers.push(observer);\n            return function () {\n                _this.unsubscribers.forEach(function (x) { return x(); });\n            };\n        });\n    };\n    MockSubscriptionLink.prototype.simulateResult = function (result, complete) {\n        var _this = this;\n        if (complete === void 0) { complete = false; }\n        setTimeout(function () {\n            var observers = _this.observers;\n            if (!observers.length)\n                throw new Error(\"subscription torn down\");\n            observers.forEach(function (observer) {\n                if (result.result && observer.next)\n                    observer.next(result.result);\n                if (result.error && observer.error)\n                    observer.error(result.error);\n                if (complete && observer.complete)\n                    observer.complete();\n            });\n        }, result.delay || 0);\n    };\n    MockSubscriptionLink.prototype.simulateComplete = function () {\n        var observers = this.observers;\n        if (!observers.length)\n            throw new Error(\"subscription torn down\");\n        observers.forEach(function (observer) {\n            if (observer.complete)\n                observer.complete();\n        });\n    };\n    MockSubscriptionLink.prototype.onSetup = function (listener) {\n        this.setups = this.setups.concat([listener]);\n    };\n    MockSubscriptionLink.prototype.onUnsubscribe = function (listener) {\n        this.unsubscribers = this.unsubscribers.concat([listener]);\n    };\n    return MockSubscriptionLink;\n}(ApolloLink));\nexport { MockSubscriptionLink };\nexport function mockObservableLink() {\n    return new MockSubscriptionLink();\n}\n//# sourceMappingURL=mockSubscriptionLink.js.map", "import { ApolloClient } from \"../../../core/index.js\";\nimport { InMemoryCache } from \"../../../cache/index.js\";\nimport { mockSingleLink } from \"./mockLink.js\";\nexport function createMockClient(data, query, variables) {\n    if (variables === void 0) { variables = {}; }\n    return new ApolloClient({\n        link: mockSingleLink({\n            request: { query: query, variables: variables },\n            result: { data: data },\n        }).setOnError(function (error) {\n            throw error;\n        }),\n        cache: new InMemoryCache({ addTypename: false }),\n    });\n}\n//# sourceMappingURL=mockClient.js.map", "import { asyncMap } from \"../../utilities/index.js\";\nexport default function subscribeAndCount(reject, observable, cb) {\n    // Use a Promise queue to prevent callbacks from being run out of order.\n    var queue = Promise.resolve();\n    var handleCount = 0;\n    var subscription = asyncMap(observable, function (result) {\n        // All previous asynchronous callbacks must complete before cb can\n        // be invoked with this result.\n        return (queue = queue\n            .then(function () {\n            return cb(++handleCount, result);\n        })\n            .catch(error));\n    }).subscribe({ error: error });\n    function error(e) {\n        subscription.unsubscribe();\n        reject(e);\n    }\n    return subscription;\n}\n//# sourceMappingURL=subscribeAndCount.js.map", "function wrap(key) {\n    return function (message, callback, timeout) {\n        return (key ? it[key] : it)(message, function () {\n            var _this = this;\n            return new Promise(function (resolve, reject) {\n                return callback.call(_this, resolve, reject);\n            });\n        }, timeout);\n    };\n}\nvar wrappedIt = wrap();\nexport var itAsync = Object.assign(function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    return wrappedIt.apply(this, args);\n}, {\n    only: wrap(\"only\"),\n    skip: wrap(\"skip\"),\n    todo: wrap(\"todo\"),\n});\n//# sourceMappingURL=itAsync.js.map", "import { __awaiter, __generator } from \"tslib\";\nexport function wait(ms) {\n    return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            return [2 /*return*/, new Promise(function (resolve) { return setTimeout(resolve, ms); })];\n        });\n    });\n}\nexport function tick() {\n    return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            return [2 /*return*/, wait(0)];\n        });\n    });\n}\n//# sourceMappingURL=wait.js.map", "function wrapTestFunction(fn, consoleMethodName) {\n    return function () {\n        var _this = this;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var spy = jest.spyOn(console, consoleMethodName);\n        spy.mockImplementation(function () { });\n        return new Promise(function (resolve) {\n            resolve(fn === null || fn === void 0 ? void 0 : fn.apply(_this, args));\n        }).finally(function () {\n            expect(spy).toMatchSnapshot();\n            spy.mockReset();\n        });\n    };\n}\n/** @deprecated This method will be removed in the next major version of Apollo Client */\nexport function withErrorSpy(it) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    args[1] = wrapTestFunction(args[1], \"error\");\n    return it.apply(void 0, args);\n}\n/** @deprecated This method will be removed in the next major version of Apollo Client */\nexport function withWarningSpy(it) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    args[1] = wrapTestFunction(args[1], \"warn\");\n    return it.apply(void 0, args);\n}\n/** @deprecated This method will be removed in the next major version of Apollo Client */\nexport function withLogSpy(it) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n        args[_i - 1] = arguments[_i];\n    }\n    args[1] = wrapTestFunction(args[1], \"log\");\n    return it.apply(void 0, args);\n}\n//# sourceMappingURL=withConsoleSpy.js.map"], "names": ["print", "addTypenameToDocument", "__extends", "equal", "Observable", "cloneDeep", "removeDirectivesFromDocument", "checkDocument", "invariant", "removeClientSetsFromDocument", "__assign", "getDefaultValues", "getOperationDefinition", "ApolloLink", "makeUniqueId", "ApolloClient", "InMemoryCache", "asyncMap", "__awaiter", "__generator"], "mappings": ";;;;;;;;;;;;AAKA,SAAS,YAAY,CAAC,OAAO,EAAE,WAAW,EAAE;AAC5C,IAAI,IAAI,WAAW,GAAG,OAAO,CAAC,KAAK;AACnC,QAAQA,eAAK,CAAC,WAAW,GAAGC,+BAAqB,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAClF,IAAI,IAAI,UAAU,GAAG,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AACtC,CAAC;AACE,IAAC,QAAQ,KAAkB,UAAU,MAAM,EAAE;AAChD,IAAIC,eAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAChC,IAAI,SAAS,QAAQ,CAAC,eAAe,EAAE,WAAW,EAAE,OAAO,EAAE;AAC7D,QAAQ,IAAI,WAAW,KAAK,KAAK,CAAC,EAAE,EAAE,WAAW,GAAG,IAAI,CAAC,EAAE;AAC3D,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAClE,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,QAAQ,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;AACjC,QAAQ,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;AAClC,QAAQ,KAAK,CAAC,oBAAoB,GAAG,EAAE,CAAC;AACxC,QAAQ,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;AACxC,QAAQ,KAAK,CAAC,YAAY,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC,YAAY,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;AAC/F,QAAQ,IAAI,eAAe,EAAE;AAC7B,YAAY,eAAe,CAAC,OAAO,CAAC,UAAU,cAAc,EAAE;AAC9D,gBAAgB,KAAK,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;AACxD,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,QAAQ,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,cAAc,EAAE;AACrE,QAAQ,IAAI,wBAAwB,GAAG,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;AACpF,QAAQ,IAAI,GAAG,GAAG,YAAY,CAAC,wBAAwB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACnF,QAAQ,IAAI,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC7D,QAAQ,IAAI,CAAC,eAAe,EAAE;AAC9B,YAAY,eAAe,GAAG,EAAE,CAAC;AACjC,YAAY,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;AAC7D,SAAS;AACT,QAAQ,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;AACvD,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE;AACtD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,IAAI,GAAG,GAAG,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC5D,QAAQ,IAAI,aAAa,GAAG,EAAE,CAAC;AAC/B,QAAQ,IAAI,gBAAgB,GAAG,SAAS,CAAC,SAAS,IAAI,EAAE,CAAC;AACzD,QAAQ,IAAI,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;AAC7D,QAAQ,IAAI,aAAa,GAAG,eAAe;AAC3C,YAAY,eAAe,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,KAAK,EAAE;AAC5D,gBAAgB,IAAI,kBAAkB,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;AACrE,gBAAgB,IAAIC,cAAK,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,EAAE;AACjE,oBAAoB,OAAO,IAAI,CAAC;AAChC,iBAAiB;AACjB,gBAAgB,IAAI,GAAG,CAAC,eAAe,IAAI,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;AACrF,oBAAoB,OAAO,IAAI,CAAC;AAChC,iBAAiB;AACjB,gBAAgB,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AACvD,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa,CAAC;AACd,cAAc,CAAC,CAAC,CAAC;AACjB,QAAQ,IAAI,QAAQ,GAAG,aAAa,IAAI,CAAC,GAAG,eAAe,CAAC,aAAa,CAAC,GAAG,KAAK,CAAC,CAAC;AAKpF,QAAQ,IAAI,KAAK,GAAG,CAAC,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,MAAM,QAAQ,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,QAAQ,CAAC,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACzN,QAAQ,IAAI,WAAW,CAAC;AACxB,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACvB,YAAY,WAAW,GAAG,IAAI,KAAK,CAAC,0CAA0C,CAAC,MAAM,CAACH,eAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,wBAAwB,CAAC,CAAC,MAAM,CAAC,qBAAqB,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC;AAChO,gBAAgB,oBAAoB,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,qEAAqE,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;AAC1S,kBAAkB,EAAE,CAAC,CAAC,CAAC;AACvB,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE;AACnC,gBAAgB,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO;AAChD,oBAAoB,iEAAiE;AACrF,oBAAoB,sDAAsD,CAAC,CAAC;AAC5E,aAAa;AACb,SAAS;AACT,aAAa;AACb,YAAY,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE;AACtE,gBAAgB,QAAQ,CAAC,aAAa,EAAE,CAAC;AACzC,aAAa;AACb,iBAAiB;AACjB,gBAAgB,eAAe,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AACzD,aAAa;AACb,YAAY,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AAC3C,YAAY,IAAI,OAAO,EAAE;AACzB,gBAAgB,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAC/D,gBAAgB,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC/C,aAAa;AACb,YAAY,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,KAAK,QAAQ,EAAE;AACpF,gBAAgB,WAAW,GAAG,IAAI,KAAK,CAAC,sFAAsF,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5I,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAII,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAClD,YAAY,IAAI,KAAK,GAAG,UAAU,CAAC,YAAY;AAC/C,gBAAgB,IAAI,WAAW,EAAE;AACjC,oBAAoB,IAAI;AAMxB,wBAAwB,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,KAAK,EAAE;AAC5E,4BAA4B,MAAM,WAAW,CAAC;AAC9C,yBAAyB;AACzB,qBAAqB;AACrB,oBAAoB,OAAO,KAAK,EAAE;AAClC,wBAAwB,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC9C,qBAAqB;AACrB,iBAAiB;AACjB,qBAAqB,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,KAAK,QAAQ,EAAE;AAClE,oBAAoB,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxC,wBAAwB,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AACvD,qBAAqB;AACrB,yBAAyB;AACzB,wBAAwB,IAAI,QAAQ,CAAC,MAAM,EAAE;AAC7C,4BAA4B,QAAQ,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU;AAC/E,gCAAgC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;AACpE,kCAAkC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACnD,yBAAyB;AACzB,wBAAwB,QAAQ,CAAC,QAAQ,EAAE,CAAC;AAC5C,qBAAqB;AACrB,iBAAiB;AACjB,aAAa,EAAE,KAAK,CAAC,CAAC;AACtB,YAAY,OAAO,YAAY;AAC/B,gBAAgB,YAAY,CAAC,KAAK,CAAC,CAAC;AACpC,aAAa,CAAC;AACd,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,uBAAuB,GAAG,UAAU,cAAc,EAAE;AAC3E,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,iBAAiB,GAAGC,mBAAS,CAAC,cAAc,CAAC,CAAC;AAC1D,QAAQ,IAAI,gCAAgC,GAAGC,sCAA4B,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAEC,uBAAa,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AACnM,QAAQC,iBAAS,CAAC,gCAAgC,EAAE,EAAE,CAAC,CAAC;AACxD,QAAQ,iBAAiB,CAAC,OAAO,CAAC,KAAK,GAAG,gCAAgC,CAAC;AAC3E,QAAQ,IAAI,KAAK,GAAGC,sCAA4B,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAClF,QAAQ,IAAI,KAAK,EAAE;AACnB,YAAY,iBAAiB,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AACpD,SAAS;AACT,QAAQ,cAAc,CAAC,aAAa,GAAG,CAAC,EAAE,GAAG,cAAc,CAAC,aAAa,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC9G,QAAQD,iBAAS,CAAC,cAAc,CAAC,aAAa,GAAG,CAAC,EAAE,EAAE,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;AACtF,QAAQ,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;AAC1D,QAAQ,OAAO,iBAAiB,CAAC;AACjC,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,yBAAyB,GAAG,UAAU,cAAc,EAAE;AAC7E,QAAQ,IAAI,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;AAC7C,QAAQ,IAAI,cAAc,CAAC,eAAe,IAAI,OAAO,CAAC,SAAS,EAAE;AACjE,YAAY,MAAM,IAAI,KAAK,CAAC,4EAA4E,CAAC,CAAC;AAC1G,SAAS;AACT,QAAQ,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE;AAC7C,YAAY,OAAO,CAAC,SAAS,GAAGE,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAEC,0BAAgB,CAACC,gCAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACnI,YAAY,cAAc,CAAC,eAAe,GAAG,UAAU,IAAI,EAAE;AAC7D,gBAAgB,IAAI,gBAAgB,GAAG,IAAI,IAAI,EAAE,CAAC;AAClD,gBAAgB,IAAI,uBAAuB,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;AACtE,gBAAgB,OAAOT,cAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,CAAC;AACxE,aAAa,CAAC;AACd,SAAS;AACT,KAAK,CAAC;AACN,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC,CAACU,eAAU,CAAC,EAAE;AAKR,SAAS,cAAc,GAAG;AACjC,IAAI,IAAI,eAAe,GAAG,EAAE,CAAC;AAC7B,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,eAAe,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AAC5C,KAAK;AAGL,IAAI,IAAI,aAAa,GAAG,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACpE,IAAI,IAAI,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACrE,IAAI,IAAI,OAAO,aAAa,KAAK,SAAS,EAAE;AAC5C,QAAQ,KAAK,GAAG,eAAe,CAAC;AAChC,QAAQ,aAAa,GAAG,IAAI,CAAC;AAC7B,KAAK;AACL,IAAI,OAAO,IAAI,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AAC9C,CAAC;AAKM,SAAS,qBAAqB,CAAC,KAAK,EAAE,KAAK,EAAE;AACpD,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;AACxC,IAAI,IAAI,OAAO,GAAGC,sBAAY,CAAC,WAAW,CAAC,CAAC;AAC5C,IAAI,IAAI,KAAK,GAAGA,sBAAY,CAAC,KAAK,CAAC,CAAC;AACpC,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,KAAK,EAAE;AACrD,QAAQ,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE;AAC9B,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AACjC,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,EAAE,KAAK,CAAC;AACb,SAAS,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,aAAa,CAAC;AACzE,SAAS,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AAChE;;ACpMG,IAAC,oBAAoB,KAAkB,UAAU,MAAM,EAAE;AAC5D,IAAIZ,eAAS,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;AAC5C,IAAI,SAAS,oBAAoB,GAAG;AACpC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,QAAQ,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC;AACjC,QAAQ,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC;AAC1B,QAAQ,KAAK,CAAC,SAAS,GAAG,EAAE,CAAC;AAC7B,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,oBAAoB,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,SAAS,EAAE;AAClE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,QAAQ,OAAO,IAAIE,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAClD,YAAY,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC/D,YAAY,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3C,YAAY,OAAO,YAAY;AAC/B,gBAAgB,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1E,aAAa,CAAC;AACd,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,oBAAoB,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,MAAM,EAAE,QAAQ,EAAE;AAChF,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE,EAAE,QAAQ,GAAG,KAAK,CAAC,EAAE;AACtD,QAAQ,UAAU,CAAC,YAAY;AAC/B,YAAY,IAAI,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;AAC5C,YAAY,IAAI,CAAC,SAAS,CAAC,MAAM;AACjC,gBAAgB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AAC1D,YAAY,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;AAClD,gBAAgB,IAAI,MAAM,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI;AAClD,oBAAoB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACjD,gBAAgB,IAAI,MAAM,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;AAClD,oBAAoB,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjD,gBAAgB,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ;AACjD,oBAAoB,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACxC,aAAa,CAAC,CAAC;AACf,SAAS,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;AAC9B,KAAK,CAAC;AACN,IAAI,oBAAoB,CAAC,SAAS,CAAC,gBAAgB,GAAG,YAAY;AAClE,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACvC,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM;AAC7B,YAAY,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;AACtD,QAAQ,SAAS,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;AAC9C,YAAY,IAAI,QAAQ,CAAC,QAAQ;AACjC,gBAAgB,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACpC,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,oBAAoB,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,QAAQ,EAAE;AACjE,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AACrD,KAAK,CAAC;AACN,IAAI,oBAAoB,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,QAAQ,EAAE;AACvE,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;AACnE,KAAK,CAAC;AACN,IAAI,OAAO,oBAAoB,CAAC;AAChC,CAAC,CAACS,eAAU,CAAC,EAAE;AAER,SAAS,kBAAkB,GAAG;AACrC,IAAI,OAAO,IAAI,oBAAoB,EAAE,CAAC;AACtC;;ACzDO,SAAS,gBAAgB,CAAC,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;AACzD,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,EAAE,CAAC,EAAE;AACjD,IAAI,OAAO,IAAIE,mBAAY,CAAC;AAC5B,QAAQ,IAAI,EAAE,cAAc,CAAC;AAC7B,YAAY,OAAO,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE;AAC3D,YAAY,MAAM,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;AAClC,SAAS,CAAC,CAAC,UAAU,CAAC,UAAU,KAAK,EAAE;AACvC,YAAY,MAAM,KAAK,CAAC;AACxB,SAAS,CAAC;AACV,QAAQ,KAAK,EAAE,IAAIC,mBAAa,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;AACxD,KAAK,CAAC,CAAC;AACP;;ACbe,SAAS,iBAAiB,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE,EAAE;AAElE,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;AAClC,IAAI,IAAI,WAAW,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,YAAY,GAAGC,kBAAQ,CAAC,UAAU,EAAE,UAAU,MAAM,EAAE;AAG9D,QAAQ,QAAQ,KAAK,GAAG,KAAK;AAC7B,aAAa,IAAI,CAAC,YAAY;AAC9B,YAAY,OAAO,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;AAC7C,SAAS,CAAC;AACV,aAAa,KAAK,CAAC,KAAK,CAAC,EAAE;AAC3B,KAAK,CAAC,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AACnC,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE;AACtB,QAAQ,YAAY,CAAC,WAAW,EAAE,CAAC;AACnC,QAAQ,MAAM,CAAC,CAAC,CAAC,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,YAAY,CAAC;AACxB;;ACnBA,SAAS,IAAI,CAAC,GAAG,EAAE;AACnB,IAAI,OAAO,UAAU,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE;AACjD,QAAQ,OAAO,CAAC,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,YAAY;AACzD,YAAY,IAAI,KAAK,GAAG,IAAI,CAAC;AAC7B,YAAY,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,MAAM,EAAE;AAC1D,gBAAgB,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;AAC7D,aAAa,CAAC,CAAC;AACf,SAAS,EAAE,OAAO,CAAC,CAAC;AACpB,KAAK,CAAC;AACN,CAAC;AACD,IAAI,SAAS,GAAG,IAAI,EAAE,CAAC;AACb,IAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY;AAC/C,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACvC,CAAC,EAAE;AACH,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;AACtB,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;AACtB,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;AACtB,CAAC;;ACpBM,SAAS,IAAI,CAAC,EAAE,EAAE;AACzB,IAAI,OAAOC,eAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AACvD,QAAQ,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AAC/C,YAAY,OAAO,CAAC,CAAC,GAAa,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvG,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP,CAAC;AACM,SAAS,IAAI,GAAG;AACvB,IAAI,OAAOD,eAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AACvD,QAAQ,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AAC/C,YAAY,OAAO,CAAC,CAAC,GAAa,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP;;ACdA,SAAS,gBAAgB,CAAC,EAAE,EAAE,iBAAiB,EAAE;AACjD,IAAI,OAAO,YAAY;AACvB,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AACtB,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACrC,SAAS;AACT,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;AACzD,QAAQ,GAAG,CAAC,kBAAkB,CAAC,YAAY,GAAG,CAAC,CAAC;AAChD,QAAQ,OAAO,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE;AAC9C,YAAY,OAAO,CAAC,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AACnF,SAAS,CAAC,CAAC,OAAO,CAAC,YAAY;AAC/B,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,CAAC;AAC1C,YAAY,GAAG,CAAC,SAAS,EAAE,CAAC;AAC5B,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,CAAC;AAEM,SAAS,YAAY,CAAC,EAAE,EAAE;AACjC,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC,CAAC;AAEM,SAAS,cAAc,CAAC,EAAE,EAAE;AACnC,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AAChD,IAAI,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC,CAAC;AAEM,SAAS,UAAU,CAAC,EAAE,EAAE;AAC/B,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC;AAClB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC/C,IAAI,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC;AAClC;;;;;;;;;;;;;;;"}