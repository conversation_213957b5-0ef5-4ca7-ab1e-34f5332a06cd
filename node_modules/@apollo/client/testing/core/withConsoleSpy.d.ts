/** @deprecated This method will be removed in the next major version of Apollo Client */
export declare function withErrorSpy<TArgs extends any[], TResult>(it: (...args: TArgs) => TResult, ...args: TArgs): TResult;
/** @deprecated This method will be removed in the next major version of Apollo Client */
export declare function withWarningSpy<TArgs extends any[], TResult>(it: (...args: TArgs) => TResult, ...args: TArgs): TResult;
/** @deprecated This method will be removed in the next major version of Apollo Client */
export declare function withLogSpy<TArgs extends any[], TResult>(it: (...args: TArgs) => TResult, ...args: TArgs): TResult;
//# sourceMappingURL=withConsoleSpy.d.ts.map