{"version": 3, "file": "wait.js", "sourceRoot": "", "sources": ["../../../src/testing/core/wait.ts"], "names": [], "mappings": ";AAAA,MAAM,UAAgB,IAAI,CAAC,EAAU;;;YACnC,sBAAO,IAAI,OAAO,CAAO,UAAC,OAAO,IAAK,OAAA,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,EAAvB,CAAuB,CAAC,EAAC;;;CAChE;AAED,MAAM,UAAgB,IAAI;;;YACxB,sBAAO,IAAI,CAAC,CAAC,CAAC,EAAC;;;CAChB", "sourcesContent": ["export async function wait(ms: number) {\n  return new Promise<void>((resolve) => setTimeout(resolve, ms));\n}\n\nexport async function tick() {\n  return wait(0);\n}\n"]}