{"version": 3, "file": "itAsync.js", "sourceRoot": "", "sources": ["../../../src/testing/core/itAsync.ts"], "names": [], "mappings": "AAAA,SAAS,IAAI,CAAC,GAA8B;IAC1C,OAAO,UACL,OAAe,EACf,QAGQ,EACR,OAAgB;QAEhB,OAAA,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAClB,OAAO,EACP;YAAA,iBAIC;YAHC,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;gBACjC,OAAA,QAAQ,CAAC,IAAI,CAAC,KAAI,EAAE,OAAO,EAAE,MAAM,CAAC;YAApC,CAAoC,CACrC,CAAC;QACJ,CAAC,EACD,OAAO,CACR;IARD,CAQC,CAAC;AACN,CAAC;AAED,IAAM,SAAS,GAAG,IAAI,EAAE,CAAC;AAEzB,MAAM,CAAC,IAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAClC;IAAyB,cAAqC;SAArC,UAAqC,EAArC,qBAAqC,EAArC,IAAqC;QAArC,yBAAqC;;IAC5D,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACrC,CAAC,EACD;IACE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;IAClB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;IAClB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;CACnB,CACF,CAAC", "sourcesContent": ["function wrap(key?: \"only\" | \"skip\" | \"todo\") {\n  return (\n    message: string,\n    callback: (\n      resolve: (result?: any) => void,\n      reject: (reason?: any) => void\n    ) => any,\n    timeout?: number\n  ) =>\n    (key ? it[key] : it)(\n      message,\n      function (this: unknown) {\n        return new Promise((resolve, reject) =>\n          callback.call(this, resolve, reject)\n        );\n      },\n      timeout\n    );\n}\n\nconst wrappedIt = wrap();\n\nexport const itAsync = Object.assign(\n  function (this: unknown, ...args: Parameters<typeof wrappedIt>) {\n    return wrappedIt.apply(this, args);\n  },\n  {\n    only: wrap(\"only\"),\n    skip: wrap(\"skip\"),\n    todo: wrap(\"todo\"),\n  }\n);\n"]}