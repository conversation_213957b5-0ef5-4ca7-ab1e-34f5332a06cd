{"version": 3, "file": "subscribeAndCount.js", "sourceRoot": "", "sources": ["../../../src/testing/core/subscribeAndCount.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAEpD,MAAM,CAAC,OAAO,UAAU,iBAAiB,CACvC,MAA4B,EAC5B,UAA+B,EAC/B,EAAiD;IAEjD,wEAAwE;IACxE,IAAI,KAAK,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;IAC9B,IAAI,WAAW,GAAG,CAAC,CAAC;IAEpB,IAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,EAAE,UAAC,MAAM;QAC/C,kEAAkE;QAClE,+BAA+B;QAC/B,OAAO,CAAC,KAAK,GAAG,KAAK;aAClB,IAAI,CAAC;YACJ,OAAO,EAAE,CAAC,EAAE,WAAW,EAAE,MAAM,CAAC,CAAC;QACnC,CAAC,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE,KAAK,OAAA,EAAE,CAAC,CAAC;IAExB,SAAS,KAAK,CAAC,CAAM;QACnB,YAAY,CAAC,WAAW,EAAE,CAAC;QAC3B,MAAM,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC", "sourcesContent": ["import type {\n  ObservableSubscription,\n  Observable,\n} from \"../../utilities/index.js\";\nimport { asyncMap } from \"../../utilities/index.js\";\n\nexport default function subscribeAndCount<TResult>(\n  reject: (reason: any) => any,\n  observable: Observable<TResult>,\n  cb: (handleCount: number, result: TResult) => any\n): ObservableSubscription {\n  // Use a Promise queue to prevent callbacks from being run out of order.\n  let queue = Promise.resolve();\n  let handleCount = 0;\n\n  const subscription = asyncMap(observable, (result) => {\n    // All previous asynchronous callbacks must complete before cb can\n    // be invoked with this result.\n    return (queue = queue\n      .then(() => {\n        return cb(++handleCount, result);\n      })\n      .catch(error));\n  }).subscribe({ error });\n\n  function error(e: any) {\n    subscription.unsubscribe();\n    reject(e);\n  }\n\n  return subscription;\n}\n"]}