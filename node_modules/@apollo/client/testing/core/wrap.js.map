{"version": 3, "file": "wrap.js", "sourceRoot": "", "sources": ["../../../src/testing/core/wrap.ts"], "names": [], "mappings": "AAAA,wEAAwE;AACxE,sBAAsB;AACtB,gBAAe,UACX,MAA4B,EAC5B,EAA+B;IAEjC,OAAA;QAAC,cAAc;aAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;YAAd,yBAAc;;QACb,IAAI,CAAC;YACH,OAAO,EAAE,eAAI,IAAI,EAAE;QACrB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC;IACH,CAAC;AAND,CAMC,EAAC;AAEJ,MAAM,UAAU,SAAS,CAAC,IAAc,EAAE,KAAa;IACrD,IAAI,OAAO,GAAW,IAAa,CAAC;IACpC,IAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC;IAE/B,OAAO,CAAC,KAAK,GAAG,UAAC,CAAS,IAAK,OAAA,CAAC,OAAO,GAAG,CAAC,CAAC,EAAb,CAAa,CAAC;IAE7C,IAAI,CAAC;QACH,IAAM,MAAM,GAAG,IAAI,EAAE,CAAC;QACtB,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC/B,OAAO,MAAM,CAAC;IAChB,CAAC;YAAS,CAAC;QACT,OAAO,CAAC,KAAK,GAAG,QAAQ,CAAC;IAC3B,CAAC;AACH,CAAC", "sourcesContent": ["// I'm not sure why mocha doesn't provide something like this, you can't\n// always use promises\nexport default <TArgs extends any[], TResult>(\n    reject: (reason: any) => any,\n    cb: (...args: TArgs) => TResult\n  ) =>\n  (...args: TArgs) => {\n    try {\n      return cb(...args);\n    } catch (e) {\n      reject(e);\n    }\n  };\n\nexport function withError(func: Function, regex: RegExp) {\n  let message: string = null as never;\n  const oldError = console.error;\n\n  console.error = (m: string) => (message = m);\n\n  try {\n    const result = func();\n    expect(message).toMatch(regex);\n    return result;\n  } finally {\n    console.error = oldError;\n  }\n}\n"]}