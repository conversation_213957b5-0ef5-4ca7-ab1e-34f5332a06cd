{"version": 3, "file": "mockSubscriptionLink.js", "sourceRoot": "", "sources": ["../../../../src/testing/core/mocking/mockSubscriptionLink.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,6BAA6B,CAAC;AAEzD,OAAO,EAAE,UAAU,EAAE,MAAM,6BAA6B,CAAC;AAYzD;IAA0C,wCAAU;IAOlD;QACE,YAAA,MAAK,WAAE,SAAC;QAPH,mBAAa,GAAU,EAAE,CAAC;QAC1B,YAAM,GAAU,EAAE,CAAC;QAGlB,eAAS,GAAU,EAAE,CAAC;;IAI9B,CAAC;IAEM,sCAAO,GAAd,UAAe,SAAoB;QAAnC,iBASC;QARC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,OAAO,IAAI,UAAU,CAAc,UAAC,QAAQ;YAC1C,KAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,EAAE,EAAH,CAAG,CAAC,CAAC;YAChC,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC9B,OAAO;gBACL,KAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,EAAE,EAAH,CAAG,CAAC,CAAC;YACzC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,6CAAc,GAArB,UAAsB,MAAgC,EAAE,QAAgB;QAAxE,iBAUC;QAVuD,yBAAA,EAAA,gBAAgB;QACtE,UAAU,CAAC;YACD,IAAA,SAAS,GAAK,KAAI,UAAT,CAAU;YAC3B,IAAI,CAAC,SAAS,CAAC,MAAM;gBAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YACjE,SAAS,CAAC,OAAO,CAAC,UAAC,QAAQ;gBACzB,IAAI,MAAM,CAAC,MAAM,IAAI,QAAQ,CAAC,IAAI;oBAAE,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;gBACjE,IAAI,MAAM,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;oBAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACjE,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ;oBAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC;YACzD,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC;IACxB,CAAC;IAEM,+CAAgB,GAAvB;QACU,IAAA,SAAS,GAAK,IAAI,UAAT,CAAU;QAC3B,IAAI,CAAC,SAAS,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QACjE,SAAS,CAAC,OAAO,CAAC,UAAC,QAAQ;YACzB,IAAI,QAAQ,CAAC,QAAQ;gBAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,sCAAO,GAAd,UAAe,QAAa;QAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC/C,CAAC;IAEM,4CAAa,GAApB,UAAqB,QAAa;QAChC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC7D,CAAC;IACH,2BAAC;AAAD,CAAC,AAjDD,CAA0C,UAAU,GAiDnD;;AAED,MAAM,UAAU,kBAAkB;IAChC,OAAO,IAAI,oBAAoB,EAAE,CAAC;AACpC,CAAC", "sourcesContent": ["import { Observable } from \"../../../utilities/index.js\";\nimport type { FetchResult, Operation } from \"../../../link/core/index.js\";\nimport { ApolloLink } from \"../../../link/core/index.js\";\n\nexport interface MockedSubscription {\n  request: Operation;\n}\n\nexport interface MockedSubscriptionResult {\n  result?: FetchResult;\n  error?: Error;\n  delay?: number;\n}\n\nexport class MockSubscriptionLink extends ApolloLink {\n  public unsubscribers: any[] = [];\n  public setups: any[] = [];\n  public operation?: Operation;\n\n  private observers: any[] = [];\n\n  constructor() {\n    super();\n  }\n\n  public request(operation: Operation) {\n    this.operation = operation;\n    return new Observable<FetchResult>((observer) => {\n      this.setups.forEach((x) => x());\n      this.observers.push(observer);\n      return () => {\n        this.unsubscribers.forEach((x) => x());\n      };\n    });\n  }\n\n  public simulateResult(result: MockedSubscriptionResult, complete = false) {\n    setTimeout(() => {\n      const { observers } = this;\n      if (!observers.length) throw new Error(\"subscription torn down\");\n      observers.forEach((observer) => {\n        if (result.result && observer.next) observer.next(result.result);\n        if (result.error && observer.error) observer.error(result.error);\n        if (complete && observer.complete) observer.complete();\n      });\n    }, result.delay || 0);\n  }\n\n  public simulateComplete() {\n    const { observers } = this;\n    if (!observers.length) throw new Error(\"subscription torn down\");\n    observers.forEach((observer) => {\n      if (observer.complete) observer.complete();\n    });\n  }\n\n  public onSetup(listener: any): void {\n    this.setups = this.setups.concat([listener]);\n  }\n\n  public onUnsubscribe(listener: any): void {\n    this.unsubscribers = this.unsubscribers.concat([listener]);\n  }\n}\n\nexport function mockObservableLink(): MockSubscriptionLink {\n  return new MockSubscriptionLink();\n}\n"]}