{"version": 3, "file": "mockLink.js", "sourceRoot": "", "sources": ["../../../../src/testing/core/mocking/mockLink.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,qCAAqC,CAAC;AAEhE,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAOtC,OAAO,EAAE,UAAU,EAAE,MAAM,6BAA6B,CAAC;AAEzD,OAAO,EACL,UAAU,EACV,qBAAqB,EACrB,4BAA4B,EAC5B,SAAS,EACT,KAAK,EACL,sBAAsB,EACtB,gBAAgB,EAChB,4BAA4B,EAC5B,aAAa,EACb,YAAY,GACb,MAAM,6BAA6B,CAAC;AAoCrC,SAAS,YAAY,CAAC,OAAuB,EAAE,WAAoB;IACjE,IAAM,WAAW,GACf,OAAO,CAAC,KAAK;QACb,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAC5E,IAAM,UAAU,GAAG,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;IAC1C,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AACpC,CAAC;AAED;IAA8B,4BAAU;IAMtC,kBACE,eAAwD,EACxD,WAA2B,EAC3B,OAA8C;QAD9C,4BAAA,EAAA,kBAA2B;QAC3B,wBAAA,EAAA,UAA2B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;;QAE9C,YAAA,MAAK,WAAE,SAAC;QATH,iBAAW,GAAY,IAAI,CAAC;QAC5B,kBAAY,GAAY,IAAI,CAAC;QAC5B,0BAAoB,GAAwC,EAAE,CAAC;QAQrE,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,KAAI,CAAC,YAAY,GAAG,MAAA,OAAO,CAAC,YAAY,mCAAI,IAAI,CAAC;QAEjD,IAAI,eAAe,EAAE,CAAC;YACpB,eAAe,CAAC,OAAO,CAAC,UAAC,cAAc;gBACrC,KAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC;;IACH,CAAC;IAEM,oCAAiB,GAAxB,UAAyB,cAA8B;QACrD,IAAM,wBAAwB,GAC5B,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;QAC/C,IAAM,GAAG,GAAG,YAAY,CACtB,wBAAwB,CAAC,OAAO,EAChC,IAAI,CAAC,WAAW,CACjB,CAAC;QACF,IAAI,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACrD,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,eAAe,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;QACnD,CAAC;QACD,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACjD,CAAC;IAEM,0BAAO,GAAd,UAAe,SAAoB;QAAnC,iBA6GC;;QA5GC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAM,GAAG,GAAG,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACtD,IAAM,aAAa,GAA+B,EAAE,CAAC;QACrD,IAAM,gBAAgB,GAAG,SAAS,CAAC,SAAS,IAAI,EAAE,CAAC;QACnD,IAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACvD,IAAM,aAAa,GACjB,eAAe,CAAC,CAAC;YACf,eAAe,CAAC,SAAS,CAAC,UAAC,GAAG,EAAE,KAAK;gBACnC,IAAM,kBAAkB,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;gBACvD,IAAI,KAAK,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,EAAE,CAAC;oBAChD,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,IAAI,GAAG,CAAC,eAAe,IAAI,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC;oBACpE,OAAO,IAAI,CAAC;gBACd,CAAC;gBACD,aAAa,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;gBACvC,OAAO,KAAK,CAAC;YACf,CAAC,CAAC;YACJ,CAAC,CAAC,CAAC,CAAC,CAAC;QAEP,IAAM,QAAQ,GACZ,aAAa,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAE/D,kEAAkE;QAClE,8DAA8D;QAC9D,iEAAiE;QACjE,gCAAgC;QAChC,IAAM,KAAK,GAAG,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,MAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,mCAAI,CAAC,CAAC;QAEtE,IAAI,WAAkB,CAAC;QAEvB,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,WAAW,GAAG,IAAI,KAAK,CACrB,kDAA2C,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,mCACnD,qBAAqB,CAAC,SAAS,CAAC,SAAS,CAAC,eAE9D,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBACxB,4BACc,aAAa,CAAC,MAAM,kBAChC,aAAa,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,gFAEzC,aAAa,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,YAAK,qBAAqB,CAAC,CAAC,CAAC,CAAE,EAA/B,CAA+B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OACrE;gBACC,CAAC,CAAC,EAAE,CACJ,CACK,CAAC;YAEF,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,OAAO,CAAC,IAAI,CACV,WAAW,CAAC,OAAO;oBACjB,iEAAiE;oBACjE,sDAAsD,CACzD,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,QAAQ,CAAC,aAAa,IAAI,QAAQ,CAAC,aAAa,GAAG,CAAC,EAAE,CAAC;gBACzD,QAAQ,CAAC,aAAa,EAAE,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,eAAe,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;YAC3C,CAAC;YACO,IAAA,OAAO,GAAK,QAAQ,QAAb,CAAc;YAC7B,IAAI,OAAO,EAAE,CAAC;gBACZ,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;gBAC/C,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjC,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvE,WAAW,GAAG,IAAI,KAAK,CACrB,8FAA+F,GAAG,CAAE,CACrG,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,IAAI,UAAU,CAAC,UAAC,QAAQ;YAC7B,IAAM,KAAK,GAAG,UAAU,CAAC;gBACvB,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAI,CAAC;wBACH,yDAAyD;wBACzD,wDAAwD;wBACxD,uDAAuD;wBACvD,wDAAwD;wBACxD,qDAAqD;wBACrD,IAAI,KAAI,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,KAAK,EAAE,CAAC;4BAClD,MAAM,WAAW,CAAC;wBACpB,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACxB,CAAC;gBACH,CAAC;qBAAM,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;oBACnD,IAAI,QAAQ,CAAC,KAAK,EAAE,CAAC;wBACnB,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACjC,CAAC;yBAAM,CAAC;wBACN,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;4BACpB,QAAQ,CAAC,IAAI,CACX,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC,CAAC;gCACrC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;gCACtC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAClB,CAAC;wBACJ,CAAC;wBACD,QAAQ,CAAC,QAAQ,EAAE,CAAC;oBACtB,CAAC;gBACH,CAAC;YACH,CAAC,EAAE,KAAK,CAAC,CAAC;YAEV,OAAO;gBACL,YAAY,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,0CAAuB,GAA/B,UACE,cAA8B;;QAE9B,IAAM,iBAAiB,GAAG,SAAS,CAAC,cAAc,CAAC,CAAC;QACpD,IAAM,gCAAgC,GAAG,4BAA4B,CACnE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,EACrE,aAAa,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAC/C,CAAC;QACF,SAAS,CAAC,gCAAgC,EAAE,mBAAmB,CAAC,CAAC;QACjE,iBAAiB,CAAC,OAAO,CAAC,KAAK,GAAG,gCAAiC,CAAC;QACpE,IAAM,KAAK,GAAG,4BAA4B,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC5E,IAAI,KAAK,EAAE,CAAC;YACV,iBAAiB,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QAC1C,CAAC;QAED,cAAc,CAAC,aAAa,GAAG,MAAA,cAAc,CAAC,aAAa,mCAAI,CAAC,CAAC;QACjE,SAAS,CACP,cAAc,CAAC,aAAa,GAAG,CAAC,EAChC,8DAA8D,EAC9D,cAAc,CAAC,aAAa,CAC7B,CAAC;QAEF,IAAI,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,CAAC;QAClD,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAEO,4CAAyB,GAAjC,UAAkC,cAA8B;QAC9D,IAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;QACvC,IAAI,cAAc,CAAC,eAAe,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACxD,MAAM,IAAI,KAAK,CACb,4EAA4E,CAC7E,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC;YACpC,OAAO,CAAC,SAAS,yBACZ,gBAAgB,CAAC,sBAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GACvD,OAAO,CAAC,SAAS,CACrB,CAAC;YACF,cAAc,CAAC,eAAe,GAAG,UAAC,IAAI;gBACpC,IAAM,gBAAgB,GAAG,IAAI,IAAI,EAAE,CAAC;gBACpC,IAAM,uBAAuB,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;gBACxD,OAAO,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,CAAC;YAC1D,CAAC,CAAC;QACJ,CAAC;IACH,CAAC;IACH,eAAC;AAAD,CAAC,AAlMD,CAA8B,UAAU,GAkMvC;;AAMD,4EAA4E;AAC5E,yCAAyC;AACzC,6DAA6D;AAC7D,MAAM,UAAU,cAAc;IAAC,yBAA8B;SAA9B,UAA8B,EAA9B,qBAA8B,EAA9B,IAA8B;QAA9B,oCAA8B;;IAC3D,0EAA0E;IAC1E,qBAAqB;IACrB,IAAI,aAAa,GAAG,eAAe,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAChE,IAAI,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAEjE,IAAI,OAAO,aAAa,KAAK,SAAS,EAAE,CAAC;QACvC,KAAK,GAAG,eAAe,CAAC;QACxB,aAAa,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,OAAO,IAAI,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AAC5C,CAAC;AAED,4EAA4E;AAC5E,8EAA8E;AAC9E,+EAA+E;AAC/E,8EAA8E;AAC9E,MAAM,UAAU,qBAAqB,CAAC,KAAU,EAAE,KAAS;IAAT,sBAAA,EAAA,SAAS;IACzD,IAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;IAC1C,IAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;IAElC,OAAO,IAAI,CAAC,SAAS,CACnB,KAAK,EACL,UAAC,CAAC,EAAE,KAAK;QACP,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC,EACD,KAAK,CACN;SACE,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC,EAAE,aAAa,CAAC;SAChE,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;AAC5D,CAAC", "sourcesContent": ["import { invariant } from \"../../../utilities/globals/index.js\";\n\nimport { equal } from \"@wry/equality\";\n\nimport type {\n  Operation,\n  GraphQLRequest,\n  FetchResult,\n} from \"../../../link/core/index.js\";\nimport { ApolloLink } from \"../../../link/core/index.js\";\n\nimport {\n  Observable,\n  addTypenameToDocument,\n  removeClientSetsFromDocument,\n  cloneDeep,\n  print,\n  getOperationDefinition,\n  getDefaultValues,\n  removeDirectivesFromDocument,\n  checkDocument,\n  makeUniqueId,\n} from \"../../../utilities/index.js\";\nimport type { Unmasked } from \"../../../masking/index.js\";\n\n/** @internal */\ntype CovariantUnaryFunction<out Arg, out Ret> = { fn(arg: Arg): Ret }[\"fn\"];\n\nexport type ResultFunction<T, V = Record<string, any>> = CovariantUnaryFunction<\n  V,\n  T\n>;\n\nexport type VariableMatcher<V = Record<string, any>> = CovariantUnaryFunction<\n  V,\n  boolean\n>;\n\nexport interface MockedResponse<\n  // @ts-ignore\n  out TData = Record<string, any>,\n  out TVariables = Record<string, any>,\n> {\n  request: GraphQLRequest<TVariables>;\n  maxUsageCount?: number;\n  result?:\n    | FetchResult<Unmasked<TData>>\n    | ResultFunction<FetchResult<Unmasked<TData>>, TVariables>;\n  error?: Error;\n  delay?: number;\n  variableMatcher?: VariableMatcher<TVariables>;\n  newData?: ResultFunction<FetchResult<Unmasked<TData>>, TVariables>;\n}\n\nexport interface MockLinkOptions {\n  showWarnings?: boolean;\n}\n\nfunction requestToKey(request: GraphQLRequest, addTypename: Boolean): string {\n  const queryString =\n    request.query &&\n    print(addTypename ? addTypenameToDocument(request.query) : request.query);\n  const requestKey = { query: queryString };\n  return JSON.stringify(requestKey);\n}\n\nexport class MockLink extends ApolloLink {\n  public operation!: Operation;\n  public addTypename: Boolean = true;\n  public showWarnings: boolean = true;\n  private mockedResponsesByKey: { [key: string]: MockedResponse[] } = {};\n\n  constructor(\n    mockedResponses: ReadonlyArray<MockedResponse<any, any>>,\n    addTypename: Boolean = true,\n    options: MockLinkOptions = Object.create(null)\n  ) {\n    super();\n    this.addTypename = addTypename;\n    this.showWarnings = options.showWarnings ?? true;\n\n    if (mockedResponses) {\n      mockedResponses.forEach((mockedResponse) => {\n        this.addMockedResponse(mockedResponse);\n      });\n    }\n  }\n\n  public addMockedResponse(mockedResponse: MockedResponse) {\n    const normalizedMockedResponse =\n      this.normalizeMockedResponse(mockedResponse);\n    const key = requestToKey(\n      normalizedMockedResponse.request,\n      this.addTypename\n    );\n    let mockedResponses = this.mockedResponsesByKey[key];\n    if (!mockedResponses) {\n      mockedResponses = [];\n      this.mockedResponsesByKey[key] = mockedResponses;\n    }\n    mockedResponses.push(normalizedMockedResponse);\n  }\n\n  public request(operation: Operation): Observable<FetchResult> | null {\n    this.operation = operation;\n    const key = requestToKey(operation, this.addTypename);\n    const unmatchedVars: Array<Record<string, any>> = [];\n    const requestVariables = operation.variables || {};\n    const mockedResponses = this.mockedResponsesByKey[key];\n    const responseIndex =\n      mockedResponses ?\n        mockedResponses.findIndex((res, index) => {\n          const mockedResponseVars = res.request.variables || {};\n          if (equal(requestVariables, mockedResponseVars)) {\n            return true;\n          }\n          if (res.variableMatcher && res.variableMatcher(operation.variables)) {\n            return true;\n          }\n          unmatchedVars.push(mockedResponseVars);\n          return false;\n        })\n      : -1;\n\n    const response =\n      responseIndex >= 0 ? mockedResponses[responseIndex] : void 0;\n\n    // There have been platform- and engine-dependent differences with\n    // setInterval(fn, Infinity), so we pass 0 instead (but detect\n    // Infinity where we call observer.error or observer.next to pend\n    // indefinitely in those cases.)\n    const delay = response?.delay === Infinity ? 0 : response?.delay ?? 0;\n\n    let configError: Error;\n\n    if (!response) {\n      configError = new Error(\n        `No more mocked responses for the query: ${print(operation.query)}\nExpected variables: ${stringifyForDebugging(operation.variables)}\n${\n  unmatchedVars.length > 0 ?\n    `\nFailed to match ${unmatchedVars.length} mock${\n      unmatchedVars.length === 1 ? \"\" : \"s\"\n    } for this query. The mocked response had the following variables:\n${unmatchedVars.map((d) => `  ${stringifyForDebugging(d)}`).join(\"\\n\")}\n`\n  : \"\"\n}`\n      );\n\n      if (this.showWarnings) {\n        console.warn(\n          configError.message +\n            \"\\nThis typically indicates a configuration error in your mocks \" +\n            \"setup, usually due to a typo or mismatched variable.\"\n        );\n      }\n    } else {\n      if (response.maxUsageCount && response.maxUsageCount > 1) {\n        response.maxUsageCount--;\n      } else {\n        mockedResponses.splice(responseIndex, 1);\n      }\n      const { newData } = response;\n      if (newData) {\n        response.result = newData(operation.variables);\n        mockedResponses.push(response);\n      }\n\n      if (!response.result && !response.error && response.delay !== Infinity) {\n        configError = new Error(\n          `Mocked response should contain either \\`result\\`, \\`error\\` or a \\`delay\\` of \\`Infinity\\`: ${key}`\n        );\n      }\n    }\n\n    return new Observable((observer) => {\n      const timer = setTimeout(() => {\n        if (configError) {\n          try {\n            // The onError function can return false to indicate that\n            // configError need not be passed to observer.error. For\n            // example, the default implementation of onError calls\n            // observer.error(configError) and then returns false to\n            // prevent this extra (harmless) observer.error call.\n            if (this.onError(configError, observer) !== false) {\n              throw configError;\n            }\n          } catch (error) {\n            observer.error(error);\n          }\n        } else if (response && response.delay !== Infinity) {\n          if (response.error) {\n            observer.error(response.error);\n          } else {\n            if (response.result) {\n              observer.next(\n                typeof response.result === \"function\" ?\n                  response.result(operation.variables)\n                : response.result\n              );\n            }\n            observer.complete();\n          }\n        }\n      }, delay);\n\n      return () => {\n        clearTimeout(timer);\n      };\n    });\n  }\n\n  private normalizeMockedResponse(\n    mockedResponse: MockedResponse\n  ): MockedResponse {\n    const newMockedResponse = cloneDeep(mockedResponse);\n    const queryWithoutClientOnlyDirectives = removeDirectivesFromDocument(\n      [{ name: \"connection\" }, { name: \"nonreactive\" }, { name: \"unmask\" }],\n      checkDocument(newMockedResponse.request.query)\n    );\n    invariant(queryWithoutClientOnlyDirectives, \"query is required\");\n    newMockedResponse.request.query = queryWithoutClientOnlyDirectives!;\n    const query = removeClientSetsFromDocument(newMockedResponse.request.query);\n    if (query) {\n      newMockedResponse.request.query = query;\n    }\n\n    mockedResponse.maxUsageCount = mockedResponse.maxUsageCount ?? 1;\n    invariant(\n      mockedResponse.maxUsageCount > 0,\n      `Mock response maxUsageCount must be greater than 0, %s given`,\n      mockedResponse.maxUsageCount\n    );\n\n    this.normalizeVariableMatching(newMockedResponse);\n    return newMockedResponse;\n  }\n\n  private normalizeVariableMatching(mockedResponse: MockedResponse) {\n    const request = mockedResponse.request;\n    if (mockedResponse.variableMatcher && request.variables) {\n      throw new Error(\n        \"Mocked response should contain either variableMatcher or request.variables\"\n      );\n    }\n\n    if (!mockedResponse.variableMatcher) {\n      request.variables = {\n        ...getDefaultValues(getOperationDefinition(request.query)),\n        ...request.variables,\n      };\n      mockedResponse.variableMatcher = (vars) => {\n        const requestVariables = vars || {};\n        const mockedResponseVariables = request.variables || {};\n        return equal(requestVariables, mockedResponseVariables);\n      };\n    }\n  }\n}\n\nexport interface MockApolloLink extends ApolloLink {\n  operation?: Operation;\n}\n\n// Pass in multiple mocked responses, so that you can test flows that end up\n// making multiple queries to the server.\n// NOTE: The last arg can optionally be an `addTypename` arg.\nexport function mockSingleLink(...mockedResponses: Array<any>): MockApolloLink {\n  // To pull off the potential typename. If this isn't a boolean, we'll just\n  // set it true later.\n  let maybeTypename = mockedResponses[mockedResponses.length - 1];\n  let mocks = mockedResponses.slice(0, mockedResponses.length - 1);\n\n  if (typeof maybeTypename !== \"boolean\") {\n    mocks = mockedResponses;\n    maybeTypename = true;\n  }\n\n  return new MockLink(mocks, maybeTypename);\n}\n\n// This is similiar to the stringifyForDisplay utility we ship, but includes\n// support for NaN in addition to undefined. More values may be handled in the\n// future. This is not added to the primary stringifyForDisplay helper since it\n// is used for the cache and other purposes. We need this for debuggging only.\nexport function stringifyForDebugging(value: any, space = 0): string {\n  const undefId = makeUniqueId(\"undefined\");\n  const nanId = makeUniqueId(\"NaN\");\n\n  return JSON.stringify(\n    value,\n    (_, value) => {\n      if (value === void 0) {\n        return undefId;\n      }\n\n      if (Number.isNaN(value)) {\n        return nanId;\n      }\n\n      return value;\n    },\n    space\n  )\n    .replace(new RegExp(JSON.stringify(undefId), \"g\"), \"<undefined>\")\n    .replace(new RegExp(JSON.stringify(nanId), \"g\"), \"NaN\");\n}\n"]}