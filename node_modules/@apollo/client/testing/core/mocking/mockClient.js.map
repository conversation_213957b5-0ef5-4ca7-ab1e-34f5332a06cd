{"version": 3, "file": "mockClient.js", "sourceRoot": "", "sources": ["../../../../src/testing/core/mocking/mockClient.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,wBAAwB,CAAC;AAEtD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,cAAc,EAAE,MAAM,eAAe,CAAC;AAE/C,MAAM,UAAU,gBAAgB,CAC9B,IAAW,EACX,KAAmB,EACnB,SAAc;IAAd,0BAAA,EAAA,cAAc;IAEd,OAAO,IAAI,YAAY,CAAC;QACtB,IAAI,EAAE,cAAc,CAAC;YACnB,OAAO,EAAE,EAAE,KAAK,OAAA,EAAE,SAAS,WAAA,EAAE;YAC7B,MAAM,EAAE,EAAE,IAAI,MAAA,EAAE;SACjB,CAAC,CAAC,UAAU,CAAC,UAAC,KAAK;YAClB,MAAM,KAAK,CAAC;QACd,CAAC,CAAC;QACF,KAAK,EAAE,IAAI,aAAa,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;KACjD,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["import type { DocumentNode } from \"graphql\";\n\nimport { ApolloClient } from \"../../../core/index.js\";\nimport type { NormalizedCacheObject } from \"../../../cache/index.js\";\nimport { InMemoryCache } from \"../../../cache/index.js\";\nimport { mockSingleLink } from \"./mockLink.js\";\n\nexport function createMockClient<TData>(\n  data: TData,\n  query: DocumentNode,\n  variables = {}\n): ApolloClient<NormalizedCacheObject> {\n  return new ApolloClient({\n    link: mockSingleLink({\n      request: { query, variables },\n      result: { data },\n    }).setOnError((error) => {\n      throw error;\n    }),\n    cache: new InMemoryCache({ addTypename: false }),\n  });\n}\n"]}