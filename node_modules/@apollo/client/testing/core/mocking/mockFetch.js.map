{"version": 3, "file": "mockFetch.js", "sourceRoot": "", "sources": ["../../../../src/testing/core/mocking/mockFetch.ts"], "names": [], "mappings": ";AAAA,OAAO,cAAc,CAAC;AAmBtB,MAAM,UAAU,qBAAqB,CACnC,MAAc,EACd,OAAa;IAEb,IAAM,MAAM,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;IAClD,IAAM,UAAU,GAAG,CAAC,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,SAAS,CAAC;IAEhE,OAAO;QACL,EAAE,EAAE,MAAM,KAAK,GAAG;QAClB,MAAM,QAAA;QACN,UAAU,YAAA;QACV,IAAI;YACF,OAAO,OAAO,CAAC,OAAO,CAAS,MAAM,CAAC,CAAC;QACzC,CAAC;KACF,CAAC;AACJ,CAAC;AAED;IAGE;QAAY,yBAAyC;aAAzC,UAAyC,EAAzC,qBAAyC,EAAzC,IAAyC;YAAzC,oCAAyC;;QAArD,iBAMC;QALC,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;QAE/B,eAAe,CAAC,OAAO,CAAC,UAAC,cAAc;YACrC,KAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,qCAAiB,GAAxB,UAAyB,cAAmC;QAC1D,IAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,GAAG,EAAE,cAAc,CAAC,IAAI,CAAC,CAAC;QAC3E,IAAI,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAErD,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,eAAe,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;QACnD,CAAC;QAED,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACvC,CAAC;IAEM,yBAAK,GAAZ,UAAa,GAAW,EAAE,IAAiB;QACzC,IAAM,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAC7C,IAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CACb,wDAAiD,GAAG,kBAAQ,IAAI,CAAE,CACnE,CAAC;QACJ,CAAC;QAEK,IAAA,KAAoB,SAAS,CAAC,KAAK,EAAG,EAApC,MAAM,YAAA,EAAE,KAAK,WAAuB,CAAC;QAE7C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO;YACzB,UAAU,CACR;gBACE,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,EACD,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAClB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEM,oCAAgB,GAAvB,UAAwB,GAAW,EAAE,IAAiB;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,GAAG,KAAA;YACH,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC;SACtB,CAAC,CAAC;IACL,CAAC;IAED,wEAAwE;IACxE,0EAA0E;IAC1E,+CAA+C;IACxC,4BAAQ,GAAf;QACE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IACH,gBAAC;AAAD,CAAC,AA7DD,IA6DC;;AAED,SAAS,SAAS,CAAC,GAAQ;IACzB,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;SACpB,IAAI,EAAE;SACN,MAAM,CACL,UAAC,GAAQ,EAAE,GAAW;;QACpB,OAAA,MAAM,CAAC,MAAM;YAET,GAAC,GAAG,IACF,CACE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACrD,QAAQ,CACT,CAAC,CAAC;gBACD,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACrB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;iBAEd,GAAG,CACJ;IAXD,CAWC,EACH,EAAE,CACH,CAAC;AACN,CAAC;AAED,MAAM,UAAU,eAAe;IAAC,yBAAyC;SAAzC,UAAyC,EAAzC,qBAAyC,EAAzC,IAAyC;QAAzC,oCAAyC;;IACvE,OAAO,KAAI,SAAS,YAAT,SAAS,0BAAI,eAAe,aAAE,QAAQ,EAAE,CAAC;AACtD,CAAC", "sourcesContent": ["import \"whatwg-fetch\";\n\n// This is an implementation of a mocked window.fetch implementation similar in\n// structure to the MockedNetworkInterface.\n\nexport interface MockedIResponse {\n  ok: boolean;\n  status: number;\n  statusText?: string;\n  json(): Promise<Object>;\n}\n\nexport interface MockedFetchResponse {\n  url: string;\n  opts: RequestInit;\n  result: MockedIResponse;\n  delay?: number;\n}\n\nexport function createMockedIResponse(\n  result: Object,\n  options?: any\n): MockedIResponse {\n  const status = (options && options.status) || 200;\n  const statusText = (options && options.statusText) || undefined;\n\n  return {\n    ok: status === 200,\n    status,\n    statusText,\n    json() {\n      return Promise.resolve<Object>(result);\n    },\n  };\n}\n\nexport class MockFetch {\n  private mockedResponsesByKey: { [key: string]: MockedFetchResponse[] };\n\n  constructor(...mockedResponses: MockedFetchResponse[]) {\n    this.mockedResponsesByKey = {};\n\n    mockedResponses.forEach((mockedResponse) => {\n      this.addMockedResponse(mockedResponse);\n    });\n  }\n\n  public addMockedResponse(mockedResponse: MockedFetchResponse) {\n    const key = this.fetchParamsToKey(mockedResponse.url, mockedResponse.opts);\n    let mockedResponses = this.mockedResponsesByKey[key];\n\n    if (!mockedResponses) {\n      mockedResponses = [];\n      this.mockedResponsesByKey[key] = mockedResponses;\n    }\n\n    mockedResponses.push(mockedResponse);\n  }\n\n  public fetch(url: string, opts: RequestInit) {\n    const key = this.fetchParamsToKey(url, opts);\n    const responses = this.mockedResponsesByKey[key];\n    if (!responses || responses.length === 0) {\n      throw new Error(\n        `No more mocked fetch responses for the params ${url} and ${opts}`\n      );\n    }\n\n    const { result, delay } = responses.shift()!;\n\n    if (!result) {\n      throw new Error(`Mocked fetch response should contain a result.`);\n    }\n\n    return new Promise((resolve) => {\n      setTimeout(\n        () => {\n          resolve(result);\n        },\n        delay ? delay : 0\n      );\n    });\n  }\n\n  public fetchParamsToKey(url: string, opts: RequestInit): string {\n    return JSON.stringify({\n      url,\n      opts: sortByKey(opts),\n    });\n  }\n\n  // Returns a \"fetch\" function equivalent that mocks the given responses.\n  // The function by returned by this should be tacked onto the global scope\n  // in order to test functions that use \"fetch\".\n  public getFetch() {\n    return this.fetch.bind(this);\n  }\n}\n\nfunction sortByKey(obj: any): Object {\n  return Object.keys(obj)\n    .sort()\n    .reduce(\n      (ret: any, key: string): Object =>\n        Object.assign(\n          {\n            [key]:\n              (\n                Object.prototype.toString.call(obj[key]).slice(8, -1) ===\n                \"Object\"\n              ) ?\n                sortByKey(obj[key])\n              : obj[key],\n          },\n          ret\n        ),\n      {}\n    );\n}\n\nexport function createMockFetch(...mockedResponses: MockedFetchResponse[]) {\n  return new MockFetch(...mockedResponses).getFetch();\n}\n"]}