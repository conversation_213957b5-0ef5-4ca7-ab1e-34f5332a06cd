{"version": 3, "file": "testing.cjs", "sources": ["react/MockedProvider.js"], "sourcesContent": ["import { __assign, __extends } from \"tslib\";\nimport * as React from \"react\";\nimport { ApolloClient } from \"../../core/index.js\";\nimport { InMemoryCache as Cache } from \"../../cache/index.js\";\nimport { ApolloProvider } from \"../../react/context/index.js\";\nimport { MockLink } from \"../core/index.js\";\nvar MockedProvider = /** @class */ (function (_super) {\n    __extends(MockedProvider, _super);\n    function MockedProvider(props) {\n        var _this = _super.call(this, props) || this;\n        var _a = _this.props, mocks = _a.mocks, addTypename = _a.addTypename, defaultOptions = _a.defaultOptions, cache = _a.cache, resolvers = _a.resolvers, link = _a.link, showWarnings = _a.showWarnings, _b = _a.connectToDevTools, connectToDevTools = _b === void 0 ? false : _b;\n        var client = new ApolloClient({\n            cache: cache || new Cache({ addTypename: addTypename }),\n            defaultOptions: defaultOptions,\n            connectToDevTools: connectToDevTools,\n            link: link || new MockLink(mocks || [], addTypename, { showWarnings: showWarnings }),\n            resolvers: resolvers,\n        });\n        _this.state = {\n            client: client,\n        };\n        return _this;\n    }\n    MockedProvider.prototype.render = function () {\n        var _a = this.props, children = _a.children, childProps = _a.childProps;\n        var client = this.state.client;\n        return React.isValidElement(children) ?\n            React.createElement(ApolloProvider, { client: client }, React.cloneElement(React.Children.only(children), __assign({}, childProps)))\n            : null;\n    };\n    MockedProvider.prototype.componentWillUnmount = function () {\n        // Since this.state.client was created in the constructor, it's this\n        // MockedProvider's responsibility to terminate it.\n        this.state.client.stop();\n    };\n    MockedProvider.defaultProps = {\n        addTypename: true,\n    };\n    return MockedProvider;\n}(React.Component));\nexport { MockedProvider };\n//# sourceMappingURL=MockedProvider.js.map"], "names": ["__extends", "cache", "ApolloClient", "<PERSON><PERSON>", "MockLink", "React", "ApolloProvider", "__assign"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAMG,IAAC,cAAc,KAAkB,UAAU,MAAM,EAAE;AACtD,IAAIA,eAAS,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;AACtC,IAAI,SAAS,cAAc,CAAC,KAAK,EAAE;AACnC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;AACrD,QAAQ,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,cAAc,GAAG,EAAE,CAAC,cAAc,EAAEC,OAAK,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,EAAE,GAAG,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACxR,QAAQ,IAAI,MAAM,GAAG,IAAIC,mBAAY,CAAC;AACtC,YAAY,KAAK,EAAED,OAAK,IAAI,IAAIE,mBAAK,CAAC,EAAE,WAAW,EAAE,WAAW,EAAE,CAAC;AACnE,YAAY,cAAc,EAAE,cAAc;AAC1C,YAAY,iBAAiB,EAAE,iBAAiB;AAChD,YAAY,IAAI,EAAE,IAAI,IAAI,IAAIC,aAAQ,CAAC,KAAK,IAAI,EAAE,EAAE,WAAW,EAAE,EAAE,YAAY,EAAE,YAAY,EAAE,CAAC;AAChG,YAAY,SAAS,EAAE,SAAS;AAChC,SAAS,CAAC,CAAC;AACX,QAAQ,KAAK,CAAC,KAAK,GAAG;AACtB,YAAY,MAAM,EAAE,MAAM;AAC1B,SAAS,CAAC;AACV,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,cAAc,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AAClD,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,UAAU,GAAG,EAAE,CAAC,UAAU,CAAC;AAChF,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AACvC,QAAQ,OAAOC,gBAAK,CAAC,cAAc,CAAC,QAAQ,CAAC;AAC7C,YAAYA,gBAAK,CAAC,aAAa,CAACC,sBAAc,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,EAAED,gBAAK,CAAC,YAAY,CAACA,gBAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAEE,cAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC;AAChJ,cAAc,IAAI,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,cAAc,CAAC,SAAS,CAAC,oBAAoB,GAAG,YAAY;AAGhE,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AACjC,KAAK,CAAC;AACN,IAAI,cAAc,CAAC,YAAY,GAAG;AAClC,QAAQ,WAAW,EAAE,IAAI;AACzB,KAAK,CAAC;AACN,IAAI,OAAO,cAAc,CAAC;AAC1B,CAAC,CAACF,gBAAK,CAAC,SAAS,CAAC;;;;;;;"}