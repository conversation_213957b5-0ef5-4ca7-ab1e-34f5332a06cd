{"version": 3, "file": "toEqualFetchResult.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toEqualFetchResult.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAItD,MAAM,CAAC,IAAM,kBAAkB,GAC7B,UAAU,MAAM,EAAE,QAAQ;IAA1B,iBAuCC;IAtCC,IAAM,MAAM,GAAG,MAA0B,CAAC;IAC1C,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CACjC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,oBAAoB,EAC7D,QAAQ,EACR,UAAU,EACV,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC7C,CAAC;IAEF,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CACtB,MAAM,EACN,QAAQ,kCAEJ,IAAI,CAAC,aAAa,UAAE,gBAAgB,WACxC,IAAI,CACL,CAAC;IAEF,OAAO;QACL,IAAI,MAAA;QACJ,OAAO,EAAE;YACP,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO,CACL,IAAI,GAAG,4BAAqB,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAE,CACjE,CAAC;YACJ,CAAC;YAED,OAAO,CACL,IAAI;gBACJ,MAAM;gBACN,KAAI,CAAC,KAAK,CAAC,oBAAoB,CAC7B,QAAQ,EACR,MAAM,EACN,UAAU,EACV,UAAU,EACV,IAAI,CACL,CACF,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { iterableEquality } from \"@jest/expect-utils\";\nimport type { MatcherFunction } from \"expect\";\nimport type { FetchResult } from \"../../core/index.js\";\n\nexport const toEqualFetchResult: MatcherFunction<[result: FetchResult]> =\n  function (actual, expected) {\n    const result = actual as FetchResult<any>;\n    const hint = this.utils.matcherHint(\n      this.isNot ? \".not.toEqualFetchResult\" : \"toEqualFetchResult\",\n      \"result\",\n      \"expected\",\n      { isNot: this.isNot, promise: this.promise }\n    );\n\n    const pass = this.equals(\n      result,\n      expected,\n      // https://github.com/jestjs/jest/blob/22029ba06b69716699254bb9397f2b3bc7b3cf3b/packages/expect/src/matchers.ts#L62-L67\n      [...this.customTesters, iterableEquality],\n      true\n    );\n\n    return {\n      pass,\n      message: () => {\n        if (pass) {\n          return (\n            hint + `\\n\\nExpected: not ${this.utils.printExpected(expected)}`\n          );\n        }\n\n        return (\n          hint +\n          \"\\n\\n\" +\n          this.utils.printDiffOrStringify(\n            expected,\n            result,\n            \"Expected\",\n            \"Received\",\n            true\n          )\n        );\n      },\n    };\n  };\n"]}