{"version": 3, "file": "toEmitMatchedValue.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toEmitMatchedValue.ts"], "names": [], "mappings": ";AAAA,OAAO,EACL,gBAAgB,EAChB,cAAc,EACd,eAAe,GAChB,MAAM,oBAAoB,CAAC;AAK5B,MAAM,CAAC,IAAM,kBAAkB,GAE3B,UAAgB,MAAM,EAAE,QAAQ,EAAE,OAAO;;;;;;;oBACrC,MAAM,GAAG,MAA+B,CAAC;oBACzC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CACjC,oBAAoB,EACpB,QAAQ,EACR,UAAU,EACV,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC7C,CAAC;;;;oBAGc,qBAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAA;;oBAAtC,UAAQ,SAA8B;oBACtC,SAAO,IAAI,CAAC,MAAM,CACtB,OAAK,EACL,QAAQ,kCAEJ,IAAI,CAAC,aAAa,UAAE,gBAAgB,EAAE,cAAc,UACzD,CAAC;oBAEF,sBAAO;4BACL,IAAI,QAAA;4BACJ,OAAO,EAAE;gCACP,IAAI,MAAI,EAAE,CAAC;oCACT,OAAO,CACL,IAAI;wCACJ,uEAAuE,CACxE,CAAC;gCACJ,CAAC;gCAED,OAAO,CACL,IAAI;oCACJ,MAAM;oCACN,KAAI,CAAC,KAAK,CAAC,oBAAoB,CAC7B,QAAQ,EACR,eAAe,CAAC,OAAK,EAAE,QAAQ,EAAE,KAAI,CAAC,aAAa,CAAC,EACpD,UAAU,EACV,UAAU,EACV,IAAI,CACL,CACF,CAAC;4BACJ,CAAC;yBACF,EAAC;;;oBAEF,IACE,OAAK,YAAY,KAAK;wBACtB,OAAK,CAAC,OAAO,KAAK,gCAAgC,EAClD,CAAC;wBACD,sBAAO;gCACL,IAAI,EAAE,KAAK;gCACX,OAAO,EAAE;oCACP,OAAA,IAAI,GAAG,qDAAqD;gCAA5D,CAA4D;6BAC/D,EAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,MAAM,OAAK,CAAC;oBACd,CAAC;;;;;;CAEJ,CAAC", "sourcesContent": ["import {\n  iterableEquality,\n  subsetEquality,\n  getObjectSubset,\n} from \"@jest/expect-utils\";\nimport type { MatcherFunction } from \"expect\";\nimport type { ObservableStream } from \"../internal/index.js\";\nimport type { TakeOptions } from \"../internal/ObservableStream.js\";\n\nexport const toEmitMatchedValue: MatcherFunction<\n  [value: any, options?: TakeOptions]\n> = async function (actual, expected, options) {\n  const stream = actual as ObservableStream<any>;\n  const hint = this.utils.matcherHint(\n    \"toEmitMatchedValue\",\n    \"stream\",\n    \"expected\",\n    { isNot: this.isNot, promise: this.promise }\n  );\n\n  try {\n    const value = await stream.takeNext(options);\n    const pass = this.equals(\n      value,\n      expected,\n      // https://github.com/jestjs/jest/blob/22029ba06b69716699254bb9397f2b3bc7b3cf3b/packages/expect/src/matchers.ts#L923-L927\n      [...this.customTesters, iterableEquality, subsetEquality]\n    );\n\n    return {\n      pass,\n      message: () => {\n        if (pass) {\n          return (\n            hint +\n            \"\\n\\nExpected stream not to emit a value equal to expected but it did.\"\n          );\n        }\n\n        return (\n          hint +\n          \"\\n\\n\" +\n          this.utils.printDiffOrStringify(\n            expected,\n            getObjectSubset(value, expected, this.customTesters),\n            \"Expected\",\n            \"Recieved\",\n            true\n          )\n        );\n      },\n    };\n  } catch (error) {\n    if (\n      error instanceof Error &&\n      error.message === \"Timeout waiting for next event\"\n    ) {\n      return {\n        pass: false,\n        message: () =>\n          hint + \"\\n\\nExpected stream to emit a value but it did not.\",\n      };\n    } else {\n      throw error;\n    }\n  }\n};\n"]}