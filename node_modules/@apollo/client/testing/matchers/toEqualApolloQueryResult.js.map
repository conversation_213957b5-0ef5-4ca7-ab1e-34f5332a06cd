{"version": 3, "file": "toEqualApolloQueryResult.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toEqualApolloQueryResult.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAItD,MAAM,CAAC,IAAM,wBAAwB,GAEjC,UAAU,MAAM,EAAE,QAAQ;IAA1B,iBAqCH;IApCC,IAAM,WAAW,GAAG,MAAgC,CAAC;IACrD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CACjC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,+BAA+B,CAAC,CAAC,CAAC,0BAA0B,EACzE,aAAa,EACb,UAAU,EACV,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC7C,CAAC;IAEF,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CACtB,WAAW,EACX,QAAQ,kCAEJ,IAAI,CAAC,aAAa,UAAE,gBAAgB,WACxC,IAAI,CACL,CAAC;IAEF,OAAO;QACL,IAAI,MAAA;QACJ,OAAO,EAAE;YACP,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO,IAAI,GAAG,4BAAqB,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAE,CAAC;YAC1E,CAAC;YAED,OAAO,CACL,IAAI;gBACJ,MAAM;gBACN,KAAI,CAAC,KAAK,CAAC,oBAAoB,CAC7B,QAAQ,EACR,WAAW,EACX,UAAU,EACV,UAAU,EACV,IAAI,CACL,CACF,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { iterableEquality } from \"@jest/expect-utils\";\nimport type { MatcherFunction } from \"expect\";\nimport type { ApolloQueryResult } from \"../../core/index.js\";\n\nexport const toEqualApolloQueryResult: MatcherFunction<\n  [queryResult: ApolloQueryResult<any>]\n> = function (actual, expected) {\n  const queryResult = actual as ApolloQueryResult<any>;\n  const hint = this.utils.matcherHint(\n    this.isNot ? \".not.toEqualApolloQueryResult\" : \"toEqualApolloQueryResult\",\n    \"queryResult\",\n    \"expected\",\n    { isNot: this.isNot, promise: this.promise }\n  );\n\n  const pass = this.equals(\n    queryResult,\n    expected,\n    // https://github.com/jestjs/jest/blob/22029ba06b69716699254bb9397f2b3bc7b3cf3b/packages/expect/src/matchers.ts#L62-L67\n    [...this.customTesters, iterableEquality],\n    true\n  );\n\n  return {\n    pass,\n    message: () => {\n      if (pass) {\n        return hint + `\\n\\nExpected: not ${this.utils.printExpected(expected)}`;\n      }\n\n      return (\n        hint +\n        \"\\n\\n\" +\n        this.utils.printDiffOrStringify(\n          expected,\n          queryResult,\n          \"Expected\",\n          \"Received\",\n          true\n        )\n      );\n    },\n  };\n};\n"]}