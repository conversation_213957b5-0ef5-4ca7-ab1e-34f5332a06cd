{"version": 3, "file": "toBeDisposed.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toBeDisposed.ts"], "names": [], "mappings": "AAEA,OAAO,EACL,qBAAqB,EACrB,cAAc,GACf,MAAM,+BAA+B,CAAC;AAEvC,MAAM,CAAC,IAAM,YAAY,GAAwB,UAAU,SAAS;IAAnB,iBAkBhD;IAjBC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc,EAAE,UAAU,EAAE,EAAE,EAAE;QAClE,KAAK,EAAE,IAAI,CAAC,KAAK;KAClB,CAAC,CAAC;IAEH,IAAM,QAAQ,GAAG,SAAqB,CAAC;IACvC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAEhC,IAAM,IAAI,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC;IAE/C,OAAO;QACL,IAAI,MAAA;QACJ,OAAO,EAAE;YACP,OAAO,UAAG,IAAI,mCACZ,KAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,uCACG,KAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,MAAG,CAAC;QAC3D,CAAC;KACF,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import type { MatcherFunction } from \"expect\";\nimport type { QueryRef } from \"../../react/internal/index.js\";\nimport {\n  assertWrappedQueryRef,\n  unwrapQueryRef,\n} from \"../../react/internal/index.js\";\n\nexport const toBeDisposed: MatcherFunction<[]> = function (_queryRef) {\n  const hint = this.utils.matcherHint(\"toBeDisposed\", \"queryRef\", \"\", {\n    isNot: this.isNot,\n  });\n\n  const queryRef = _queryRef as QueryRef;\n  assertWrappedQueryRef(queryRef);\n\n  const pass = unwrapQueryRef(queryRef).disposed;\n\n  return {\n    pass,\n    message: () => {\n      return `${hint}\\n\\nExpected queryRef ${\n        this.isNot ? \"not \" : \"\"\n      }to be disposed, but it was${this.isNot ? \"\" : \" not\"}.`;\n    },\n  };\n};\n"]}