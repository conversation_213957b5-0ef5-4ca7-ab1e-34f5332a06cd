{"version": 3, "file": "toHaveSuspenseCacheEntryUsing.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toHaveSuspenseCacheEntryUsing.ts"], "names": [], "mappings": ";AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AACnD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,gBAAgB,EAAE,MAAM,+BAA+B,CAAC;AAGjE,MAAM,CAAC,IAAM,6BAA6B,GAQtC,UACF,MAAM,EACN,KAAK,EACL,EAAkD;;QAAlD,qBAA+B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,KAAA,EAAhD,SAAS,eAAA,EAAE,gBAAa,EAAb,QAAQ,mBAAG,EAAE,KAAA;IAE1B,IAAI,CAAC,CAAC,MAAM,YAAY,YAAY,CAAC,EAAE,CAAC;QACtC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IAClE,CAAC;IAED,IAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAE/C,IAAM,QAAQ;QACZ,KAAK;QACL,kBAAkB,CAAC,SAAS,CAAC;OACzB,EAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,OAClC,CAAC;IACF,IAAM,QAAQ,GAAG,MAAA,aAAa,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,0CAAE,OAAO,CAAC;IAE3E,OAAO;QACL,IAAI,EAAE,CAAC,CAAC,QAAQ;QAChB,OAAO,EAAE;YACP,OAAO,kCACL,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,kCACO,CAAC;QAClC,CAAC;KACF,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import type { MatcherFunction } from \"expect\";\nimport type { DocumentNode } from \"graphql\";\nimport type { OperationVariables } from \"../../core/index.js\";\nimport { ApolloClient } from \"../../core/index.js\";\nimport { canonicalStringify } from \"../../cache/index.js\";\nimport { getSuspenseCache } from \"../../react/internal/index.js\";\nimport type { CacheKey } from \"../../react/internal/index.js\";\n\nexport const toHaveSuspenseCacheEntryUsing: MatcherFunction<\n  [\n    query: DocumentNode,\n    options: {\n      variables?: OperationVariables;\n      queryKey?: string | number | any[];\n    },\n  ]\n> = function (\n  client,\n  query,\n  { variables, queryKey = [] } = Object.create(null)\n) {\n  if (!(client instanceof ApolloClient)) {\n    throw new Error(\"Actual must be an instance of `ApolloClient`\");\n  }\n\n  const suspenseCache = getSuspenseCache(client);\n\n  const cacheKey: CacheKey = [\n    query,\n    canonicalStringify(variables),\n    ...([] as any[]).concat(queryKey),\n  ];\n  const queryRef = suspenseCache[\"queryRefs\"].lookupArray(cacheKey)?.current;\n\n  return {\n    pass: !!queryRef,\n    message: () => {\n      return `Expected suspense cache ${\n        queryRef ? \"not \" : \"\"\n      }to have cache entry using key`;\n    },\n  };\n};\n"]}