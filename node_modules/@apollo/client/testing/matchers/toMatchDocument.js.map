{"version": 3, "file": "toMatchDocument.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toMatchDocument.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,aAAa,EAAE,KAAK,EAAE,MAAM,0BAA0B,CAAC;AAIhE,MAAM,CAAC,IAAM,eAAe,GAC1B,UAAU,MAAM,EAAE,QAAQ;IAA1B,iBA+CC;IA9CC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;IACvD,IAAM,cAAc,GAAG,KAAK,CAC1B,gBAAgB,CACd,MAAM,EACN,IAAI;QACF,cAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAC9B,UAAU,CACX,yCAAsC,CAC1C,CACF,CAAC;IACF,IAAM,gBAAgB,GAAG,KAAK,CAC5B,gBAAgB,CACd,QAAQ,EACR,IAAI;QACF,cAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAC9B,UAAU,CACX,yCAAsC,CAC1C,CACF,CAAC;IAEF,IAAM,IAAI,GAAG,cAAc,KAAK,gBAAgB,CAAC;IAEjD,OAAO;QACL,IAAI,MAAA;QACJ,OAAO,EAAE;YACP,IAAM,IAAI,GAAG,KAAI,CAAC,KAAK,CAAC,WAAW,CACjC,iBAAiB,EACjB,SAAS,EACT,SAAS,EACT,EAAE,KAAK,EAAE,KAAI,CAAC,KAAK,EAAE,CACtB,CAAC;YAEF,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO,CACL,IAAI;oBACJ,MAAM;oBACN,eAAe;oBACf,KAAI,CAAC,KAAK,CAAC,cAAc,CAAC,cAAc,CAAC,CAC1C,CAAC;YACJ,CAAC;YAED,OAAO,CACL,IAAI,GAAG,MAAM,GAAG,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAClE,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAEJ,SAAS,gBAAgB,CAAC,QAAiB,EAAE,OAAe;IAC1D,IAAI,CAAC;QACH,aAAa,CAAC,QAAwB,CAAC,CAAC;IAC1C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAED,OAAO,QAAwB,CAAC;AAClC,CAAC", "sourcesContent": ["import { checkDocument, print } from \"../../utilities/index.js\";\nimport type { DocumentNode } from \"../../core/index.js\";\nimport type { MatcherFunction } from \"expect\";\n\nexport const toMatchDocument: MatcherFunction<[document: DocumentNode]> =\n  function (actual, document) {\n    const hint = this.utils.matcherHint(\"toMatchDocument\");\n    const actualDocument = print(\n      validateDocument(\n        actual,\n        hint +\n          `\\n\\n${this.utils.RECEIVED_COLOR(\n            \"received\"\n          )} document must be a parsed document.`\n      )\n    );\n    const expectedDocument = print(\n      validateDocument(\n        document,\n        hint +\n          `\\n\\n${this.utils.EXPECTED_COLOR(\n            \"expected\"\n          )} document must be a parsed document.`\n      )\n    );\n\n    const pass = actualDocument === expectedDocument;\n\n    return {\n      pass,\n      message: () => {\n        const hint = this.utils.matcherHint(\n          \"toMatchDocument\",\n          undefined,\n          undefined,\n          { isNot: this.isNot }\n        );\n\n        if (pass) {\n          return (\n            hint +\n            \"\\n\\n\" +\n            \"Received:\\n\\n\" +\n            this.utils.RECEIVED_COLOR(actualDocument)\n          );\n        }\n\n        return (\n          hint + \"\\n\\n\" + this.utils.diff(expectedDocument, actualDocument)\n        );\n      },\n    };\n  };\n\nfunction validateDocument(document: unknown, message: string) {\n  try {\n    checkDocument(document as DocumentNode);\n  } catch (e) {\n    throw new Error(message);\n  }\n\n  return document as DocumentNode;\n}\n"]}