{"version": 3, "file": "toEmitAnything.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toEmitAnything.ts"], "names": [], "mappings": ";AAIA,MAAM,CAAC,IAAM,cAAc,GACzB,UAAgB,MAAM,EAAE,OAAO;;;;;;;oBACvB,MAAM,GAAG,MAA+B,CAAC;oBACzC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,gBAAgB,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;;;;oBAGpD,qBAAM,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAA;;oBAAlC,UAAQ,SAA0B;oBAExC,sBAAO;4BACL,IAAI,EAAE,IAAI;4BACV,OAAO,EAAE;gCACP,OAAO,CACL,IAAI;oCACJ,sDAAsD;oCACtD,iBAAiB;oCACjB,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAK,CAAC,CAChC,CAAC;4BACJ,CAAC;yBACF,EAAC;;;oBAEF,IACE,OAAK,YAAY,KAAK;wBACtB,OAAK,CAAC,OAAO,KAAK,gCAAgC,EAClD,CAAC;wBACD,sBAAO;gCACL,IAAI,EAAE,KAAK;gCACX,OAAO,EAAE;oCACP,OAAA,IAAI,GAAG,sDAAsD;gCAA7D,CAA6D;6BAChE,EAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,MAAM,OAAK,CAAC;oBACd,CAAC;;;;;;CAEJ,CAAC", "sourcesContent": ["import type { MatcherFunction } from \"expect\";\nimport type { ObservableStream } from \"../internal/index.js\";\nimport type { TakeOptions } from \"../internal/ObservableStream.js\";\n\nexport const toEmitAnything: MatcherFunction<[options?: TakeOptions]> =\n  async function (actual, options) {\n    const stream = actual as ObservableStream<any>;\n    const hint = this.utils.matcherHint(\"toEmitAnything\", \"stream\", \"\");\n\n    try {\n      const value = await stream.peek(options);\n\n      return {\n        pass: true,\n        message: () => {\n          return (\n            hint +\n            \"\\n\\nExpected stream not to emit anything but it did.\" +\n            \"\\n\\nReceived:\\n\" +\n            this.utils.printReceived(value)\n          );\n        },\n      };\n    } catch (error) {\n      if (\n        error instanceof Error &&\n        error.message === \"Timeout waiting for next event\"\n      ) {\n        return {\n          pass: false,\n          message: () =>\n            hint + \"\\n\\nExpected stream to emit an event but it did not.\",\n        };\n      } else {\n        throw error;\n      }\n    }\n  };\n"]}