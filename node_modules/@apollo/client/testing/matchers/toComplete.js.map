{"version": 3, "file": "toComplete.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toComplete.ts"], "names": [], "mappings": ";AAIA,MAAM,CAAC,IAAM,UAAU,GACrB,UAAgB,MAAM,EAAE,OAAO;;;;;;oBACvB,MAAM,GAAG,MAA+B,CAAC;oBACzC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,YAAY,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC;;;;oBAG9D,qBAAM,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,EAAA;;oBAAlC,SAAkC,CAAC;oBAEnC,sBAAO;4BACL,IAAI,EAAE,IAAI;4BACV,OAAO,EAAE;gCACP,OAAO,IAAI,GAAG,iDAAiD,CAAC;4BAClE,CAAC;yBACF,EAAC;;;oBAEF,IACE,OAAK,YAAY,KAAK;wBACtB,OAAK,CAAC,OAAO,KAAK,gCAAgC,EAClD,CAAC;wBACD,sBAAO;gCACL,IAAI,EAAE,KAAK;gCACX,OAAO,EAAE;oCACP,OAAA,IAAI,GAAG,iDAAiD;gCAAxD,CAAwD;6BAC3D,EAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,MAAM,OAAK,CAAC;oBACd,CAAC;;;;;;CAEJ,CAAC", "sourcesContent": ["import type { MatcherFunction } from \"expect\";\nimport type { ObservableStream } from \"../internal/index.js\";\nimport type { TakeOptions } from \"../internal/ObservableStream.js\";\n\nexport const toComplete: MatcherFunction<[options?: TakeOptions]> =\n  async function (actual, options) {\n    const stream = actual as ObservableStream<any>;\n    const hint = this.utils.matcherHint(\"toComplete\", \"stream\", \"\");\n\n    try {\n      await stream.takeComplete(options);\n\n      return {\n        pass: true,\n        message: () => {\n          return hint + \"\\n\\nExpected stream not to complete but it did.\";\n        },\n      };\n    } catch (error) {\n      if (\n        error instanceof Error &&\n        error.message === \"Timeout waiting for next event\"\n      ) {\n        return {\n          pass: false,\n          message: () =>\n            hint + \"\\n\\nExpected stream to complete but it did not.\",\n        };\n      } else {\n        throw error;\n      }\n    }\n  };\n"]}