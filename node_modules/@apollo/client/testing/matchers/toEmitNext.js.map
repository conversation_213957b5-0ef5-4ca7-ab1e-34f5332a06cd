{"version": 3, "file": "toEmitNext.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toEmitNext.ts"], "names": [], "mappings": ";AAIA,MAAM,CAAC,IAAM,UAAU,GACrB,UAAgB,MAAM,EAAE,OAAO;;;;;;oBACvB,MAAM,GAAG,MAA+B,CAAC;oBACzC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CACjC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,aAAa,EAC/C,QAAQ,EACR,UAAU,CACX,CAAC;;;;oBAGA,qBAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAA;;oBAA9B,SAA8B,CAAC;oBAE/B,sBAAO;4BACL,IAAI,EAAE,IAAI;4BACV,OAAO,EAAE;gCACP,OAAO,IAAI,GAAG,qDAAqD,CAAC;4BACtE,CAAC;yBACF,EAAC;;;oBAEF,IACE,OAAK,YAAY,KAAK;wBACtB,OAAK,CAAC,OAAO,KAAK,gCAAgC,EAClD,CAAC;wBACD,sBAAO;gCACL,IAAI,EAAE,KAAK;gCACX,OAAO,EAAE;oCACP,OAAA,IAAI,GAAG,qDAAqD;gCAA5D,CAA4D;6BAC/D,EAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,MAAM,OAAK,CAAC;oBACd,CAAC;;;;;;CAEJ,CAAC", "sourcesContent": ["import type { MatcherFunction } from \"expect\";\nimport type { ObservableStream } from \"../internal/index.js\";\nimport type { TakeOptions } from \"../internal/ObservableStream.js\";\n\nexport const toEmitNext: MatcherFunction<[options?: TakeOptions]> =\n  async function (actual, options) {\n    const stream = actual as ObservableStream<any>;\n    const hint = this.utils.matcherHint(\n      this.isNot ? \".not.toEmitValue\" : \"toEmitValue\",\n      \"stream\",\n      \"expected\"\n    );\n\n    try {\n      await stream.takeNext(options);\n\n      return {\n        pass: true,\n        message: () => {\n          return hint + \"\\n\\nExpected stream not to emit a value but it did.\";\n        },\n      };\n    } catch (error) {\n      if (\n        error instanceof Error &&\n        error.message === \"Timeout waiting for next event\"\n      ) {\n        return {\n          pass: false,\n          message: () =>\n            hint + \"\\n\\nExpected stream to emit a value but it did not.\",\n        };\n      } else {\n        throw error;\n      }\n    }\n  };\n"]}