{"version": 3, "file": "toEmitError.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toEmitError.ts"], "names": [], "mappings": ";AAIA,SAAS,YAAY,CAAuB,QAAa,EAAE,MAAW;IACpE,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,MAAM,YAAY,KAAK,EAAE,CAAC;QAC5D,OAAO,MAAM,CAAC,OAAO,KAAK,QAAQ,CAAC;IACrC,CAAC;IAED,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;AAC3D,CAAC;AAED,MAAM,CAAC,IAAM,WAAW,GAEpB,UAAgB,MAAM,EAAE,QAAQ,EAAE,OAAO;;;;;;;oBACrC,MAAM,GAAG,MAA+B,CAAC;oBACzC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CACjC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,aAAa,EAC/C,QAAQ,EACR,OAAO,CACR,CAAC;;;;oBAGc,qBAAM,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,EAAA;;oBAAvC,UAAQ,SAA+B;oBACvC,SACJ,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAK,CAAC,CAAC;oBAE3E,sBAAO;4BACL,IAAI,QAAA;4BACJ,OAAO,EAAE;gCACP,IAAI,MAAI,EAAE,CAAC;oCACT,OAAO,CACL,IAAI;wCACJ,mDAAmD;wCACnD,eAAe;wCACf,IAAI;wCACJ,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,OAAK,CAAC,CAChC,CAAC;gCACJ,CAAC;gCAED,OAAO,CACL,IAAI;oCACJ,MAAM;oCACN,KAAI,CAAC,KAAK,CAAC,oBAAoB,CAC7B,QAAQ,EACR,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,OAAK,EACpD,UAAU,EACV,UAAU,EACV,IAAI,CACL,CACF,CAAC;4BACJ,CAAC;yBACF,EAAC;;;oBAEF,IACE,OAAK,YAAY,KAAK;wBACtB,OAAK,CAAC,OAAO,KAAK,gCAAgC,EAClD,CAAC;wBACD,sBAAO;gCACL,IAAI,EAAE,KAAK;gCACX,OAAO,EAAE;oCACP,OAAA,IAAI,GAAG,sDAAsD;gCAA7D,CAA6D;6BAChE,EAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,MAAM,OAAK,CAAC;oBACd,CAAC;;;;;;CAEJ,CAAC", "sourcesContent": ["import type { MatcherFunction, MatcherContext } from \"expect\";\nimport type { ObservableStream } from \"../internal/index.js\";\nimport type { TakeOptions } from \"../internal/ObservableStream.js\";\n\nfunction isErrorEqual(this: MatcherContext, expected: any, actual: any) {\n  if (typeof expected === \"string\" && actual instanceof Error) {\n    return actual.message === expected;\n  }\n\n  return this.equals(expected, actual, this.customTesters);\n}\n\nexport const toEmitError: MatcherFunction<\n  [value?: any, options?: TakeOptions]\n> = async function (actual, expected, options) {\n  const stream = actual as ObservableStream<any>;\n  const hint = this.utils.matcherHint(\n    this.isNot ? \".not.toEmitError\" : \"toEmitError\",\n    \"stream\",\n    \"error\"\n  );\n\n  try {\n    const error = await stream.takeError(options);\n    const pass =\n      expected === undefined ? true : isErrorEqual.call(this, expected, error);\n\n    return {\n      pass,\n      message: () => {\n        if (pass) {\n          return (\n            hint +\n            \"\\n\\nExpected stream not to emit error but it did.\" +\n            `\\n\\nReceived:` +\n            `\\n` +\n            this.utils.printReceived(error)\n          );\n        }\n\n        return (\n          hint +\n          \"\\n\\n\" +\n          this.utils.printDiffOrStringify(\n            expected,\n            typeof expected === \"string\" ? error.message : error,\n            \"Expected\",\n            \"Recieved\",\n            true\n          )\n        );\n      },\n    };\n  } catch (error) {\n    if (\n      error instanceof Error &&\n      error.message === \"Timeout waiting for next event\"\n    ) {\n      return {\n        pass: false,\n        message: () =>\n          hint + \"\\n\\nExpected stream to emit an error but it did not.\",\n      };\n    } else {\n      throw error;\n    }\n  }\n};\n"]}