{"version": 3, "file": "toEmitValue.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toEmitValue.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAKtD,MAAM,CAAC,IAAM,WAAW,GACtB,UAAgB,MAAM,EAAE,QAAQ,EAAE,OAAO;;;;;;;oBACjC,MAAM,GAAG,MAA+B,CAAC;oBACzC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CACjC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,aAAa,EAC/C,QAAQ,EACR,UAAU,CACX,CAAC;;;;oBAGc,qBAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAA;;oBAAtC,UAAQ,SAA8B;oBACtC,SAAO,IAAI,CAAC,MAAM,CAAC,OAAK,EAAE,QAAQ,kCACnC,IAAI,CAAC,aAAa;wBACrB,gBAAgB;8BAChB,CAAC;oBAEH,sBAAO;4BACL,IAAI,QAAA;4BACJ,OAAO,EAAE;gCACP,IAAI,MAAI,EAAE,CAAC;oCACT,OAAO,CACL,IAAI;wCACJ,uEAAuE,CACxE,CAAC;gCACJ,CAAC;gCAED,OAAO,CACL,IAAI;oCACJ,MAAM;oCACN,KAAI,CAAC,KAAK,CAAC,oBAAoB,CAC7B,QAAQ,EACR,OAAK,EACL,UAAU,EACV,UAAU,EACV,IAAI,CACL,CACF,CAAC;4BACJ,CAAC;yBACF,EAAC;;;oBAEF,IACE,OAAK,YAAY,KAAK;wBACtB,OAAK,CAAC,OAAO,KAAK,gCAAgC,EAClD,CAAC;wBACD,sBAAO;gCACL,IAAI,EAAE,KAAK;gCACX,OAAO,EAAE;oCACP,OAAA,IAAI,GAAG,qDAAqD;gCAA5D,CAA4D;6BAC/D,EAAC;oBACJ,CAAC;yBAAM,CAAC;wBACN,MAAM,OAAK,CAAC;oBACd,CAAC;;;;;;CAEJ,CAAC", "sourcesContent": ["import { iterableEquality } from \"@jest/expect-utils\";\nimport type { MatcherFunction } from \"expect\";\nimport type { ObservableStream } from \"../internal/index.js\";\nimport type { TakeOptions } from \"../internal/ObservableStream.js\";\n\nexport const toEmitValue: MatcherFunction<[value: any, options?: TakeOptions]> =\n  async function (actual, expected, options) {\n    const stream = actual as ObservableStream<any>;\n    const hint = this.utils.matcherHint(\n      this.isNot ? \".not.toEmitValue\" : \"toEmitValue\",\n      \"stream\",\n      \"expected\"\n    );\n\n    try {\n      const value = await stream.takeNext(options);\n      const pass = this.equals(value, expected, [\n        ...this.customTesters,\n        iterableEquality,\n      ]);\n\n      return {\n        pass,\n        message: () => {\n          if (pass) {\n            return (\n              hint +\n              \"\\n\\nExpected stream not to emit a value equal to expected but it did.\"\n            );\n          }\n\n          return (\n            hint +\n            \"\\n\\n\" +\n            this.utils.printDiffOrStringify(\n              expected,\n              value,\n              \"Expected\",\n              \"Recieved\",\n              true\n            )\n          );\n        },\n      };\n    } catch (error) {\n      if (\n        error instanceof Error &&\n        error.message === \"Timeout waiting for next event\"\n      ) {\n        return {\n          pass: false,\n          message: () =>\n            hint + \"\\n\\nExpected stream to emit a value but it did not.\",\n        };\n      } else {\n        throw error;\n      }\n    }\n  };\n"]}