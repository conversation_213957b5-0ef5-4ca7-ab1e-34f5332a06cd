{"version": 3, "file": "toBeGarbageCollected.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toBeGarbageCollected.ts"], "names": [], "mappings": ";AASA,MAAM,CAAC,IAAM,oBAAoB,GAC/B,UAAgB,MAAM;;;;;;oBACd,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC;oBAE5D,IAAI,CAAC,CAAC,MAAM,YAAY,OAAO,CAAC,EAAE,CAAC;wBACjC,MAAM,IAAI,KAAK,CACb,IAAI;4BACF,MAAM;4BACN,uDAAgD,OAAO,MAAM,MAAG,CACnE,CAAC;oBACJ,CAAC;oBAEG,IAAI,GAAG,KAAK,CAAC;oBAGjB,qBAAM,OAAO,CAAC,IAAI,CAAC;4BACjB,IAAI,OAAO,CAAO,UAAC,OAAO;gCACxB,OAAO,GAAG,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;4BACtC,CAAC,CAAC;4BACF,IAAI,OAAO,CAAO,UAAC,OAAO;gCACxB,QAAQ,GAAG,WAAW,CAAC;oCACrB,MAAM,CAAC,EAAG,EAAE,CAAC;oCACb,IAAI,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,SAAS,CAAC;oCACpC,IAAI,IAAI,EAAE,CAAC;wCACT,OAAO,EAAE,CAAC;oCACZ,CAAC;gCACH,CAAC,EAAE,CAAC,CAAC,CAAC;4BACR,CAAC,CAAC;yBACH,CAAC,EAAA;;oBAbF,SAaE,CAAC;oBAEH,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxB,YAAY,CAAC,OAAO,CAAC,CAAC;oBAEtB,sBAAO;4BACL,IAAI,MAAA;4BACJ,OAAO,EAAE;gCACP,IAAI,IAAI,EAAE,CAAC;oCACT,OAAO,CACL,IAAI;wCACJ,MAAM;wCACN,uDAAuD,CACxD,CAAC;gCACJ,CAAC;gCAED,OAAO,CACL,IAAI,GAAG,4DAA4D,CACpE,CAAC;4BACJ,CAAC;yBACF,EAAC;;;;CACH,CAAC", "sourcesContent": ["import type { MatcherFunction } from \"expect\";\n\n// this is necessary because this file is picked up by `tsc` (it's not a test),\n// but our main `tsconfig.json` doesn't include `\"ES2021.WeakRef\"` on purpose\ndeclare class WeakRef<T extends WeakKey> {\n  constructor(target: T);\n  deref(): T | undefined;\n}\n\nexport const toBeGarbageCollected: MatcherFunction<[weakRef: WeakRef<any>]> =\n  async function (actual) {\n    const hint = this.utils.matcherHint(\"toBeGarbageCollected\");\n\n    if (!(actual instanceof WeakRef)) {\n      throw new Error(\n        hint +\n          \"\\n\\n\" +\n          `Expected value to be a WeakRef, but it was a ${typeof actual}.`\n      );\n    }\n\n    let pass = false;\n    let interval: NodeJS.Timeout | undefined;\n    let timeout: NodeJS.Timeout | undefined;\n    await Promise.race([\n      new Promise<void>((resolve) => {\n        timeout = setTimeout(resolve, 1000);\n      }),\n      new Promise<void>((resolve) => {\n        interval = setInterval(() => {\n          global.gc!();\n          pass = actual.deref() === undefined;\n          if (pass) {\n            resolve();\n          }\n        }, 1);\n      }),\n    ]);\n\n    clearInterval(interval);\n    clearTimeout(timeout);\n\n    return {\n      pass,\n      message: () => {\n        if (pass) {\n          return (\n            hint +\n            \"\\n\\n\" +\n            \"Expected value to not be cache-collected, but it was.\"\n          );\n        }\n\n        return (\n          hint + \"\\n\\n Expected value to be cache-collected, but it was not.\"\n        );\n      },\n    };\n  };\n"]}