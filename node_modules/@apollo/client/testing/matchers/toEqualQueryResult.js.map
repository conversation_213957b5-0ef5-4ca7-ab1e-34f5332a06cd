{"version": 3, "file": "toEqualQueryResult.js", "sourceRoot": "", "sources": ["../../../src/testing/matchers/toEqualQueryResult.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAItD,IAAM,YAAY,GAAG;IACnB,SAAS;IACT,OAAO;IACP,QAAQ;IACR,MAAM;IACN,WAAW;IACX,eAAe;IACf,QAAQ;IACR,QAAQ;IACR,cAAc;CACN,CAAC;AAIX,IAAM,cAAc,GAAG,UAAC,GAAwB,EAAE,GAAW;IAC3D,OAAA,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;AAA9C,CAA8C,CAAC;AAEjD,MAAM,CAAC,IAAM,kBAAkB,GAE3B,UAAU,MAAM,EAAE,QAAQ;IAA1B,iBAgDH;IA/CC,IAAM,WAAW,GAAG,MAA+B,CAAC;IACpD,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CACjC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAAC,oBAAoB,EAC7D,aAAa,EACb,UAAU,EACV,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CAC7C,CAAC;IAEF,IAAM,kBAAkB,GAAG,YAAY,CAAC,MAAM,CAC5C,UAAC,IAAI,EAAE,GAAG;QACR,IAAI,cAAc,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QAC/B,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,EACD,EAAoC,CACrC,CAAC;IAEF,IAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CACtB,kBAAkB,EAClB,QAAQ,kCAEJ,IAAI,CAAC,aAAa,UAAE,gBAAgB,WACxC,IAAI,CACL,CAAC;IAEF,OAAO;QACL,IAAI,MAAA;QACJ,OAAO,EAAE;YACP,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO,IAAI,GAAG,4BAAqB,KAAI,CAAC,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAE,CAAC;YAC1E,CAAC;YAED,OAAO,CACL,IAAI;gBACJ,MAAM;gBACN,KAAI,CAAC,KAAK,CAAC,oBAAoB,CAC7B,QAAQ,EACR,kBAAkB,EAClB,UAAU,EACV,UAAU,EACV,IAAI,CACL,CACF,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { iterableEquality } from \"@jest/expect-utils\";\nimport type { MatcherFunction } from \"expect\";\nimport type { QueryResult } from \"../../react/index.js\";\n\nconst CHECKED_KEYS = [\n  \"loading\",\n  \"error\",\n  \"errors\",\n  \"data\",\n  \"variables\",\n  \"networkStatus\",\n  \"errors\",\n  \"called\",\n  \"previousData\",\n] as const;\n\nexport type CheckedKeys = (typeof CHECKED_KEYS)[number];\n\nconst hasOwnProperty = (obj: Record<string, any>, key: string) =>\n  Object.prototype.hasOwnProperty.call(obj, key);\n\nexport const toEqualQueryResult: MatcherFunction<\n  [queryResult: Pick<QueryResult<any, any>, CheckedKeys>]\n> = function (actual, expected) {\n  const queryResult = actual as QueryResult<any, any>;\n  const hint = this.utils.matcherHint(\n    this.isNot ? \".not.toEqualQueryResult\" : \"toEqualQueryResult\",\n    \"queryResult\",\n    \"expected\",\n    { isNot: this.isNot, promise: this.promise }\n  );\n\n  const checkedQueryResult = CHECKED_KEYS.reduce(\n    (memo, key) => {\n      if (hasOwnProperty(queryResult, key)) {\n        memo[key] = queryResult[key];\n      }\n\n      return memo;\n    },\n    {} as Partial<QueryResult<any, any>>\n  );\n\n  const pass = this.equals(\n    checkedQueryResult,\n    expected,\n    // https://github.com/jestjs/jest/blob/22029ba06b69716699254bb9397f2b3bc7b3cf3b/packages/expect/src/matchers.ts#L62-L67\n    [...this.customTesters, iterableEquality],\n    true\n  );\n\n  return {\n    pass,\n    message: () => {\n      if (pass) {\n        return hint + `\\n\\nExpected: not ${this.utils.printExpected(expected)}`;\n      }\n\n      return (\n        hint +\n        \"\\n\\n\" +\n        this.utils.printDiffOrStringify(\n          expected,\n          checkedQueryResult,\n          \"Expected\",\n          \"Received\",\n          true\n        )\n      );\n    },\n  };\n};\n"]}