{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/testing/index.ts"], "names": [], "mappings": "AAAA,OAAO,+BAA+B,CAAC;AAEvC,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAC;AAC3D,cAAc,iBAAiB,CAAC", "sourcesContent": ["import \"../utilities/globals/index.js\";\nexport type { MockedProviderProps } from \"./react/MockedProvider.js\";\nexport { MockedProvider } from \"./react/MockedProvider.js\";\nexport * from \"./core/index.js\";\n"]}