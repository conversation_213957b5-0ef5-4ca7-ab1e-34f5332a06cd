import type { GraphQLSchema } from "graphql";
/**
 * A function that accepts a static `schema` and a `mockFetchOpts` object and
 * returns a disposable object with `mock` and `restore` functions.
 *
 * The `mock` function is a mock fetch function that is set on the global
 * `window` object. This function intercepts any fetch requests and
 * returns a response by executing the operation against the provided schema.
 *
 * The `restore` function is a cleanup function that will restore the previous
 * `fetch`. It is automatically called if the function's return value is
 * declared with `using`. If your environment does not support the language
 * feature `using`, you should manually invoke the `restore` function.
 *
 * @param schema - A `GraphQLSchema`.
 * @param mockFetchOpts - Configuration options.
 * @returns An object with both `mock` and `restore` functions.
 *
 * @example
 * ```js
 * using _fetch = createSchemaFetch(schema); // automatically restores fetch after exiting the block
 *
 * const { restore } = createSchemaFetch(schema);
 * restore(); // manually restore fetch if `using` is not supported
 * ```
 * @since 3.10.0
 * @alpha
 * @deprecated `createSchemaFetch` is deprecated and will be removed in 3.12.0.
 * Please migrate to [`@apollo/graphql-testing-library`](https://github.com/apollographql/graphql-testing-library).
 */
declare const createSchemaFetch: (schema: GraphQLSchema, mockFetchOpts?: {
    validate?: boolean;
    delay?: {
        min: number;
        max: number;
    };
}) => ((uri?: any, options?: any) => Promise<Response>) & {
    mockGlobal: () => {
        restore: () => void;
    } & Disposable;
};
export { createSchemaFetch };
//# sourceMappingURL=createSchemaFetch.d.ts.map