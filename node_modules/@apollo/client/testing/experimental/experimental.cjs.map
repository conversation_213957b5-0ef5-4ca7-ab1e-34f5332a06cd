{"version": 3, "file": "experimental.cjs", "sources": ["graphql-tools/utils.js", "createTestSchema.js", "../internal/disposables/withCleanup.js", "../core/wait.js", "createSchemaFetch.js"], "sourcesContent": ["import { __assign, __spreadArray } from \"tslib\";\nimport { GraphQLInterfaceType, GraphQLString, GraphQLUnionType, defaultFieldResolver, getNullableType, isAbstractType, isEnumType, isInterfaceType, isListType, isObjectType, isScalarType, isUnionType, } from \"graphql\";\nimport { isNonNullObject } from \"../../../utilities/index.js\";\nimport { MapperKind, mapSchema, getRootTypeNames } from \"@graphql-tools/utils\";\n// Taken from @graphql-tools/mock:\n// https://github.com/ardatan/graphql-tools/blob/4b56b04d69b02919f6c5fa4f97d33da63f36e8c8/packages/mock/src/utils.ts#L20\nvar takeRandom = function (arr) { return arr[Math.floor(Math.random() * arr.length)]; };\n/**\n * A function that accepts a static `schema` and a `mocks` object for specifying\n * default scalar mocks and returns a `GraphQLSchema`.\n *\n * @param staticSchema - A static `GraphQLSchema`.\n * @param mocks - An object containing scalar mocks.\n * @returns A `GraphQLSchema` with scalar mocks.\n *\n * @example\n * ```js\n * const mockedSchema = createMockSchema(schema, {\n     ID: () => \"1\",\n     Int: () => 42,\n     String: () => \"String\",\n     Date: () => new Date(\"January 1, 2024 01:00:00\").toJSON().split(\"T\")[0],\n  });\n * ```\n * @since 3.10.0\n * @alpha\n */\nvar createMockSchema = function (staticSchema, mocks) {\n    var _a;\n    // Taken from @graphql-tools/mock:\n    // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/MockStore.ts#L613\n    var getType = function (typeName) {\n        var type = staticSchema.getType(typeName);\n        if (!type || !(isObjectType(type) || isInterfaceType(type))) {\n            throw new Error(\"\".concat(typeName, \" does not exist on schema or is not an object or interface\"));\n        }\n        return type;\n    };\n    // Taken from @graphql-tools/mock:\n    // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/MockStore.ts#L597\n    var getFieldType = function (typeName, fieldName) {\n        if (fieldName === \"__typename\") {\n            return GraphQLString;\n        }\n        var type = getType(typeName);\n        var field = type.getFields()[fieldName];\n        if (!field) {\n            throw new Error(\"\".concat(fieldName, \" does not exist on type \").concat(typeName));\n        }\n        return field.type;\n    };\n    // Taken from @graphql-tools/mock:\n    // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/MockStore.ts#L527\n    var generateValueFromType = function (fieldType) {\n        var nullableType = getNullableType(fieldType);\n        if (isScalarType(nullableType)) {\n            var mockFn = mocks[nullableType.name];\n            if (typeof mockFn !== \"function\") {\n                throw new Error(\"No mock defined for type \\\"\".concat(nullableType.name, \"\\\"\"));\n            }\n            return mockFn();\n        }\n        else if (isEnumType(nullableType)) {\n            var mockFn = mocks[nullableType.name];\n            if (typeof mockFn === \"function\")\n                return mockFn();\n            var values = nullableType.getValues().map(function (v) { return v.value; });\n            return takeRandom(values);\n        }\n        else if (isObjectType(nullableType)) {\n            return {};\n        }\n        else if (isListType(nullableType)) {\n            return __spreadArray([], new Array(2), true).map(function () {\n                return generateValueFromType(nullableType.ofType);\n            });\n        }\n        else if (isAbstractType(nullableType)) {\n            var mock = mocks[nullableType.name];\n            var typeName = void 0;\n            var values = {};\n            if (!mock) {\n                typeName = takeRandom(staticSchema.getPossibleTypes(nullableType).map(function (t) { return t.name; }));\n            }\n            else if (typeof mock === \"function\") {\n                var mockRes = mock();\n                if (mockRes === null)\n                    return null;\n                if (!isNonNullObject(mockRes)) {\n                    throw new Error(\"Value returned by the mock for \".concat(nullableType.name, \" is not an object or null\"));\n                }\n                values = mockRes;\n                if (typeof values[\"__typename\"] !== \"string\") {\n                    throw new Error(\"Please return a __typename in \\\"\".concat(nullableType.name, \"\\\"\"));\n                }\n                typeName = values[\"__typename\"];\n            }\n            else if (isNonNullObject(mock) &&\n                typeof mock[\"__typename\"] === \"function\") {\n                var mockRes = mock[\"__typename\"]();\n                if (typeof mockRes !== \"string\") {\n                    throw new Error(\"'__typename' returned by the mock for abstract type \".concat(nullableType.name, \" is not a string\"));\n                }\n                typeName = mockRes;\n            }\n            else {\n                throw new Error(\"Please return a __typename in \\\"\".concat(nullableType.name, \"\\\"\"));\n            }\n            return typeName;\n        }\n        else {\n            throw new Error(\"\".concat(nullableType, \" not implemented\"));\n        }\n    };\n    // Taken from @graphql-tools/mock:\n    // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/utils.ts#L53\n    var isRootType = function (type, schema) {\n        var rootTypeNames = getRootTypeNames(schema);\n        return rootTypeNames.has(type.name);\n    };\n    // Taken from @graphql-tools/mock:\n    // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/addMocksToSchema.ts#L123\n    var mockResolver = function (source, args, contex, info) {\n        var defaultResolvedValue = defaultFieldResolver(source, args, contex, info);\n        // priority to default resolved value\n        if (defaultResolvedValue !== undefined)\n            return defaultResolvedValue;\n        // we have to handle the root mutation, root query and root subscription types\n        // differently, because no resolver is called at the root\n        if (isRootType(info.parentType, info.schema)) {\n            return {\n                typeName: info.parentType.name,\n                key: \"ROOT\",\n                fieldName: info.fieldName,\n                fieldArgs: args,\n            };\n        }\n        if (defaultResolvedValue === undefined) {\n            var fieldType = getFieldType(info.parentType.name, info.fieldName);\n            return generateValueFromType(fieldType);\n        }\n        return undefined;\n    };\n    // Taken from @graphql-tools/mock:\n    // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/addMocksToSchema.ts#L176\n    return mapSchema(staticSchema, (_a = {},\n        _a[MapperKind.OBJECT_FIELD] = function (fieldConfig) {\n            var newFieldConfig = __assign({}, fieldConfig);\n            var oldResolver = fieldConfig.resolve;\n            if (!oldResolver) {\n                newFieldConfig.resolve = mockResolver;\n            }\n            return newFieldConfig;\n        },\n        _a[MapperKind.ABSTRACT_TYPE] = function (type) {\n            if (type.resolveType != null && type.resolveType.length) {\n                return;\n            }\n            var typeResolver = function (typename) {\n                return typename;\n            };\n            if (isUnionType(type)) {\n                return new GraphQLUnionType(__assign(__assign({}, type.toConfig()), { resolveType: typeResolver }));\n            }\n            else {\n                return new GraphQLInterfaceType(__assign(__assign({}, type.toConfig()), { resolveType: typeResolver }));\n            }\n        },\n        _a));\n};\nexport { createMockSchema };\n//# sourceMappingURL=utils.js.map", "import { __assign } from \"tslib\";\nimport { addResolversToSchema } from \"@graphql-tools/schema\";\nimport { mergeResolvers } from \"@graphql-tools/merge\";\nimport { createMockSchema } from \"./graphql-tools/utils.js\";\n/**\n * A function that creates a [Proxy object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy)\n * around a given `schema` with `resolvers`. This proxied schema can be used to\n * progressively layer resolvers on top of the original schema using the `add`\n * method. The `fork` method can be used to create a new proxied schema which\n * can be modified independently of the original schema. `reset` will restore\n * resolvers to the original proxied schema.\n *\n * @param schema - A `GraphQLSchema`.\n * @param options - An `options` object that accepts `scalars` and `resolvers` objects.\n * @returns A `ProxiedSchema` with `add`, `fork` and `reset` methods.\n *\n * @example\n * ```js\n *\n * const schema = createTestSchema(schemaWithTypeDefs, {\n *   resolvers: {\n       Query: {\n         writer: () => ({\n           name: \"<PERSON>\",\n         }),\n       }\n     },\n     scalars: {\n       ID: () => \"1\",\n       Int: () => 36,\n       String: () => \"String\",\n       Date: () => new Date(\"December 10, 1815 01:00:00\").toJSON().split(\"T\")[0],\n     }\n   });\n * ```\n * @since 3.9.0\n * @alpha\n * @deprecated `createTestSchema` is deprecated and will be removed in 3.12.0.\n * Please migrate to [`@apollo/graphql-testing-library`](https://github.com/apollographql/graphql-testing-library).\n */\nvar createTestSchema = function (schemaWithTypeDefs, options) {\n    var _a;\n    var targetResolvers = __assign({}, options.resolvers);\n    var targetSchema = addResolversToSchema({\n        schema: createMockSchema(schemaWithTypeDefs, (_a = options.scalars) !== null && _a !== void 0 ? _a : {}),\n        resolvers: targetResolvers,\n    });\n    var fns = {\n        add: function (_a) {\n            var newResolvers = _a.resolvers;\n            // @ts-ignore TODO(fixme): IResolvers type does not play well with our Resolvers\n            targetResolvers = mergeResolvers([targetResolvers, newResolvers]);\n            targetSchema = addResolversToSchema({\n                schema: targetSchema,\n                resolvers: targetResolvers,\n            });\n            return targetSchema;\n        },\n        fork: function (_a) {\n            var _b;\n            var _c = _a === void 0 ? {} : _a, newResolvers = _c.resolvers;\n            return createTestSchema(targetSchema, {\n                // @ts-ignore TODO(fixme): IResolvers type does not play well with our Resolvers\n                resolvers: (_b = mergeResolvers([targetResolvers, newResolvers])) !== null && _b !== void 0 ? _b : targetResolvers,\n                scalars: options.scalars,\n            });\n        },\n        reset: function () {\n            targetSchema = addResolversToSchema({\n                schema: schemaWithTypeDefs,\n                resolvers: options.resolvers,\n            });\n        },\n    };\n    var schema = new Proxy(targetSchema, {\n        get: function (_target, p) {\n            if (p in fns) {\n                return Reflect.get(fns, p);\n            }\n            // An optimization that eliminates round-trips through the proxy\n            // on class methods invoked via `this` on a base class instance wrapped by\n            // the proxy.\n            //\n            // For example, consider the following class:\n            //\n            // class Base {\n            //   foo(){\n            //     this.bar()\n            //   }\n            //   bar(){\n            //     ...\n            //   }\n            // }\n            //\n            // Calling `proxy.foo()` would call `foo` with `this` being the proxy.\n            // This would result in calling `proxy.bar()` which would again invoke\n            // the proxy to resolve `bar` and call that method.\n            //\n            // Instead, calls to `proxy.foo()` should result in a call to\n            // `innerObject.foo()` with a `this` of `innerObject`, and that call\n            // should directly call `innerObject.bar()`.\n            var property = Reflect.get(targetSchema, p);\n            if (typeof property === \"function\") {\n                return property.bind(targetSchema);\n            }\n            return property;\n        },\n    });\n    return schema;\n};\nexport { createTestSchema };\n//# sourceMappingURL=createTestSchema.js.map", "import { __assign } from \"tslib\";\n/** @internal */\nexport function withCleanup(item, cleanup) {\n    var _a;\n    return __assign(__assign({}, item), (_a = {}, _a[Symbol.dispose] = function () {\n        cleanup(item);\n        // if `item` already has a cleanup function, we also need to call the original cleanup function\n        // (e.g. if something is wrapped in `withCleanup` twice)\n        if (Symbol.dispose in item) {\n            item[Symbol.dispose]();\n        }\n    }, _a));\n}\n//# sourceMappingURL=withCleanup.js.map", "import { __awaiter, __generator } from \"tslib\";\nexport function wait(ms) {\n    return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            return [2 /*return*/, new Promise(function (resolve) { return setTimeout(resolve, ms); })];\n        });\n    });\n}\nexport function tick() {\n    return __awaiter(this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            return [2 /*return*/, wait(0)];\n        });\n    });\n}\n//# sourceMappingURL=wait.js.map", "import { __awaiter, __generator } from \"tslib\";\nimport { execute, GraphQLError, validate } from \"graphql\";\nimport { gql } from \"../../core/index.js\";\nimport { withCleanup } from \"../internal/index.js\";\nimport { wait } from \"../core/wait.js\";\n/**\n * A function that accepts a static `schema` and a `mockFetchOpts` object and\n * returns a disposable object with `mock` and `restore` functions.\n *\n * The `mock` function is a mock fetch function that is set on the global\n * `window` object. This function intercepts any fetch requests and\n * returns a response by executing the operation against the provided schema.\n *\n * The `restore` function is a cleanup function that will restore the previous\n * `fetch`. It is automatically called if the function's return value is\n * declared with `using`. If your environment does not support the language\n * feature `using`, you should manually invoke the `restore` function.\n *\n * @param schema - A `GraphQLSchema`.\n * @param mockFetchOpts - Configuration options.\n * @returns An object with both `mock` and `restore` functions.\n *\n * @example\n * ```js\n * using _fetch = createSchemaFetch(schema); // automatically restores fetch after exiting the block\n *\n * const { restore } = createSchemaFetch(schema);\n * restore(); // manually restore fetch if `using` is not supported\n * ```\n * @since 3.10.0\n * @alpha\n * @deprecated `createSchemaFetch` is deprecated and will be removed in 3.12.0.\n * Please migrate to [`@apollo/graphql-testing-library`](https://github.com/apollographql/graphql-testing-library).\n */\nvar createSchemaFetch = function (schema, mockFetchOpts) {\n    var _a, _b, _c, _d;\n    if (mockFetchOpts === void 0) { mockFetchOpts = { validate: true }; }\n    var prevFetch = window.fetch;\n    var delayMin = (_b = (_a = mockFetchOpts.delay) === null || _a === void 0 ? void 0 : _a.min) !== null && _b !== void 0 ? _b : 3;\n    var delayMax = (_d = (_c = mockFetchOpts.delay) === null || _c === void 0 ? void 0 : _c.max) !== null && _d !== void 0 ? _d : delayMin + 2;\n    if (delayMin > delayMax) {\n        throw new Error(\"Please configure a minimum delay that is less than the maximum delay. The default minimum delay is 3ms.\");\n    }\n    var mockFetch = function (_uri, options) { return __awaiter(void 0, void 0, void 0, function () {\n        var randomDelay, body, document, validationErrors, result, stringifiedResult;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    if (!(delayMin > 0)) return [3 /*break*/, 2];\n                    randomDelay = Math.random() * (delayMax - delayMin) + delayMin;\n                    return [4 /*yield*/, wait(randomDelay)];\n                case 1:\n                    _a.sent();\n                    _a.label = 2;\n                case 2:\n                    body = JSON.parse(options.body);\n                    document = gql(body.query);\n                    if (mockFetchOpts.validate) {\n                        validationErrors = [];\n                        try {\n                            validationErrors = validate(schema, document);\n                        }\n                        catch (e) {\n                            validationErrors = [\n                                e instanceof Error ?\n                                    GraphQLError.prototype.toJSON.apply(e)\n                                    : e,\n                            ];\n                        }\n                        if ((validationErrors === null || validationErrors === void 0 ? void 0 : validationErrors.length) > 0) {\n                            return [2 /*return*/, new Response(JSON.stringify({ errors: validationErrors }))];\n                        }\n                    }\n                    return [4 /*yield*/, execute({\n                            schema: schema,\n                            document: document,\n                            variableValues: body.variables,\n                            operationName: body.operationName,\n                        })];\n                case 3:\n                    result = _a.sent();\n                    stringifiedResult = JSON.stringify(result);\n                    return [2 /*return*/, new Response(stringifiedResult)];\n            }\n        });\n    }); };\n    function mockGlobal() {\n        window.fetch = mockFetch;\n        var restore = function () {\n            if (window.fetch === mockFetch) {\n                window.fetch = prevFetch;\n            }\n        };\n        return withCleanup({ restore: restore }, restore);\n    }\n    return Object.assign(mockFetch, {\n        mockGlobal: mockGlobal,\n        // if https://github.com/rbuckton/proposal-using-enforcement lands\n        // [Symbol.enter]: mockGlobal\n    });\n};\nexport { createSchemaFetch };\n//# sourceMappingURL=createSchemaFetch.js.map"], "names": ["isObjectType", "isInterfaceType", "GraphQLString", "getNullableType", "isScalarType", "isEnumType", "isListType", "__spread<PERSON><PERSON>y", "isAbstractType", "isNonNullObject", "getRootTypeNames", "defaultFieldResolver", "mapSchema", "MapperKind", "__assign", "isUnionType", "GraphQLUnionType", "GraphQLInterfaceType", "addResolversToSchema", "mergeResolvers", "schema", "__awaiter", "__generator", "gql", "validate", "GraphQLError", "execute"], "mappings": ";;;;;;;;;;;;AAMA,IAAI,UAAU,GAAG,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;AAqBxF,IAAI,gBAAgB,GAAG,UAAU,YAAY,EAAE,KAAK,EAAE;AACtD,IAAI,IAAI,EAAE,CAAC;AAGX,IAAI,IAAI,OAAO,GAAG,UAAU,QAAQ,EAAE;AACtC,QAAQ,IAAI,IAAI,GAAG,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAClD,QAAQ,IAAI,CAAC,IAAI,IAAI,EAAEA,oBAAY,CAAC,IAAI,CAAC,IAAIC,uBAAe,CAAC,IAAI,CAAC,CAAC,EAAE;AACrE,YAAY,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,4DAA4D,CAAC,CAAC,CAAC;AAC/G,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AAGN,IAAI,IAAI,YAAY,GAAG,UAAU,QAAQ,EAAE,SAAS,EAAE;AACtD,QAAQ,IAAI,SAAS,KAAK,YAAY,EAAE;AACxC,YAAY,OAAOC,qBAAa,CAAC;AACjC,SAAS;AACT,QAAQ,IAAI,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;AACrC,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC;AAChD,QAAQ,IAAI,CAAC,KAAK,EAAE;AACpB,YAAY,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,EAAE,0BAA0B,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/F,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC;AAC1B,KAAK,CAAC;AAGN,IAAI,IAAI,qBAAqB,GAAG,UAAU,SAAS,EAAE;AACrD,QAAQ,IAAI,YAAY,GAAGC,uBAAe,CAAC,SAAS,CAAC,CAAC;AACtD,QAAQ,IAAIC,oBAAY,CAAC,YAAY,CAAC,EAAE;AACxC,YAAY,IAAI,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAClD,YAAY,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;AAC9C,gBAAgB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AAC/F,aAAa;AACb,YAAY,OAAO,MAAM,EAAE,CAAC;AAC5B,SAAS;AACT,aAAa,IAAIC,kBAAU,CAAC,YAAY,CAAC,EAAE;AAC3C,YAAY,IAAI,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAClD,YAAY,IAAI,OAAO,MAAM,KAAK,UAAU;AAC5C,gBAAgB,OAAO,MAAM,EAAE,CAAC;AAChC,YAAY,IAAI,MAAM,GAAG,YAAY,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACxF,YAAY,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC;AACtC,SAAS;AACT,aAAa,IAAIL,oBAAY,CAAC,YAAY,CAAC,EAAE;AAC7C,YAAY,OAAO,EAAE,CAAC;AACtB,SAAS;AACT,aAAa,IAAIM,kBAAU,CAAC,YAAY,CAAC,EAAE;AAC3C,YAAY,OAAOC,mBAAa,CAAC,EAAE,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY;AACzE,gBAAgB,OAAO,qBAAqB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AAClE,aAAa,CAAC,CAAC;AACf,SAAS;AACT,aAAa,IAAIC,sBAAc,CAAC,YAAY,CAAC,EAAE;AAC/C,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAChD,YAAY,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC;AAClC,YAAY,IAAI,MAAM,GAAG,EAAE,CAAC;AAC5B,YAAY,IAAI,CAAC,IAAI,EAAE;AACvB,gBAAgB,QAAQ,GAAG,UAAU,CAAC,YAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACxH,aAAa;AACb,iBAAiB,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;AACjD,gBAAgB,IAAI,OAAO,GAAG,IAAI,EAAE,CAAC;AACrC,gBAAgB,IAAI,OAAO,KAAK,IAAI;AACpC,oBAAoB,OAAO,IAAI,CAAC;AAChC,gBAAgB,IAAI,CAACC,yBAAe,CAAC,OAAO,CAAC,EAAE;AAC/C,oBAAoB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,2BAA2B,CAAC,CAAC,CAAC;AAC9H,iBAAiB;AACjB,gBAAgB,MAAM,GAAG,OAAO,CAAC;AACjC,gBAAgB,IAAI,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE;AAC9D,oBAAoB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACxG,iBAAiB;AACjB,gBAAgB,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;AAChD,aAAa;AACb,iBAAiB,IAAIA,yBAAe,CAAC,IAAI,CAAC;AAC1C,gBAAgB,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,UAAU,EAAE;AAC1D,gBAAgB,IAAI,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;AACnD,gBAAgB,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACjD,oBAAoB,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC;AAC1I,iBAAiB;AACjB,gBAAgB,QAAQ,GAAG,OAAO,CAAC;AACnC,aAAa;AACb,iBAAiB;AACjB,gBAAgB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACpG,aAAa;AACb,YAAY,OAAO,QAAQ,CAAC;AAC5B,SAAS;AACT,aAAa;AACb,YAAY,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,EAAE,kBAAkB,CAAC,CAAC,CAAC;AACzE,SAAS;AACT,KAAK,CAAC;AAGN,IAAI,IAAI,UAAU,GAAG,UAAU,IAAI,EAAE,MAAM,EAAE;AAC7C,QAAQ,IAAI,aAAa,GAAGC,sBAAgB,CAAC,MAAM,CAAC,CAAC;AACrD,QAAQ,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5C,KAAK,CAAC;AAGN,IAAI,IAAI,YAAY,GAAG,UAAU,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;AAC7D,QAAQ,IAAI,oBAAoB,GAAGC,4BAAoB,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAEpF,QAAQ,IAAI,oBAAoB,KAAK,SAAS;AAC9C,YAAY,OAAO,oBAAoB,CAAC;AAGxC,QAAQ,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;AACtD,YAAY,OAAO;AACnB,gBAAgB,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;AAC9C,gBAAgB,GAAG,EAAE,MAAM;AAC3B,gBAAgB,SAAS,EAAE,IAAI,CAAC,SAAS;AACzC,gBAAgB,SAAS,EAAE,IAAI;AAC/B,aAAa,CAAC;AACd,SAAS;AACT,QAAQ,IAAI,oBAAoB,KAAK,SAAS,EAAE;AAChD,YAAY,IAAI,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/E,YAAY,OAAO,qBAAqB,CAAC,SAAS,CAAC,CAAC;AACpD,SAAS;AACT,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK,CAAC;AAGN,IAAI,OAAOC,eAAS,CAAC,YAAY,GAAG,EAAE,GAAG,EAAE;AAC3C,QAAQ,EAAE,CAACC,gBAAU,CAAC,YAAY,CAAC,GAAG,UAAU,WAAW,EAAE;AAC7D,YAAY,IAAI,cAAc,GAAGC,cAAQ,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;AAC3D,YAAY,IAAI,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;AAClD,YAAY,IAAI,CAAC,WAAW,EAAE;AAC9B,gBAAgB,cAAc,CAAC,OAAO,GAAG,YAAY,CAAC;AACtD,aAAa;AACb,YAAY,OAAO,cAAc,CAAC;AAClC,SAAS;AACT,QAAQ,EAAE,CAACD,gBAAU,CAAC,aAAa,CAAC,GAAG,UAAU,IAAI,EAAE;AACvD,YAAY,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;AACrE,gBAAgB,OAAO;AACvB,aAAa;AACb,YAAY,IAAI,YAAY,GAAG,UAAU,QAAQ,EAAE;AACnD,gBAAgB,OAAO,QAAQ,CAAC;AAChC,aAAa,CAAC;AACd,YAAY,IAAIE,mBAAW,CAAC,IAAI,CAAC,EAAE;AACnC,gBAAgB,OAAO,IAAIC,wBAAgB,CAACF,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;AACpH,aAAa;AACb,iBAAiB;AACjB,gBAAgB,OAAO,IAAIG,4BAAoB,CAACH,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;AACxH,aAAa;AACb,SAAS;AACT,QAAQ,EAAE,EAAE,CAAC;AACb,CAAC;;ACjIE,IAAC,gBAAgB,GAAG,UAAU,kBAAkB,EAAE,OAAO,EAAE;AAC9D,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,eAAe,GAAGA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AAC1D,IAAI,IAAI,YAAY,GAAGI,2BAAoB,CAAC;AAC5C,QAAQ,MAAM,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,CAAC,EAAE,GAAG,OAAO,CAAC,OAAO,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;AAChH,QAAQ,SAAS,EAAE,eAAe;AAClC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,GAAG,GAAG;AACd,QAAQ,GAAG,EAAE,UAAU,EAAE,EAAE;AAC3B,YAAY,IAAI,YAAY,GAAG,EAAE,CAAC,SAAS,CAAC;AAE5C,YAAY,eAAe,GAAGC,oBAAc,CAAC,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,CAAC;AAC9E,YAAY,YAAY,GAAGD,2BAAoB,CAAC;AAChD,gBAAgB,MAAM,EAAE,YAAY;AACpC,gBAAgB,SAAS,EAAE,eAAe;AAC1C,aAAa,CAAC,CAAC;AACf,YAAY,OAAO,YAAY,CAAC;AAChC,SAAS;AACT,QAAQ,IAAI,EAAE,UAAU,EAAE,EAAE;AAC5B,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,IAAI,EAAE,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,YAAY,GAAG,EAAE,CAAC,SAAS,CAAC;AAC1E,YAAY,OAAO,gBAAgB,CAAC,YAAY,EAAE;AAElD,gBAAgB,SAAS,EAAE,CAAC,EAAE,GAAGC,oBAAc,CAAC,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,eAAe;AAClI,gBAAgB,OAAO,EAAE,OAAO,CAAC,OAAO;AACxC,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,KAAK,EAAE,YAAY;AAC3B,YAAY,YAAY,GAAGD,2BAAoB,CAAC;AAChD,gBAAgB,MAAM,EAAE,kBAAkB;AAC1C,gBAAgB,SAAS,EAAE,OAAO,CAAC,SAAS;AAC5C,aAAa,CAAC,CAAC;AACf,SAAS;AACT,KAAK,CAAC;AACN,IAAI,IAAIE,QAAM,GAAG,IAAI,KAAK,CAAC,YAAY,EAAE;AACzC,QAAQ,GAAG,EAAE,UAAU,OAAO,EAAE,CAAC,EAAE;AACnC,YAAY,IAAI,CAAC,IAAI,GAAG,EAAE;AAC1B,gBAAgB,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC3C,aAAa;AAuBb,YAAY,IAAI,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;AACxD,YAAY,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AAChD,gBAAgB,OAAO,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACnD,aAAa;AACb,YAAY,OAAO,QAAQ,CAAC;AAC5B,SAAS;AACT,KAAK,CAAC,CAAC;AACP,IAAI,OAAOA,QAAM,CAAC;AAClB;;AC3GO,SAAS,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE;AAC3C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,OAAON,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,YAAY;AACnF,QAAQ,OAAO,CAAC,IAAI,CAAC,CAAC;AAGtB,QAAQ,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;AACpC,YAAY,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;AACnC,SAAS;AACT,KAAK,EAAE,EAAE,EAAE,CAAC;AACZ;;ACXO,SAAS,IAAI,CAAC,EAAE,EAAE;AACzB,IAAI,OAAOO,eAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AACvD,QAAQ,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AAC/C,YAAY,OAAO,CAAC,CAAC,GAAa,IAAI,OAAO,CAAC,UAAU,OAAO,EAAE,EAAE,OAAO,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvG,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC;AACP;;AC2BG,IAAC,iBAAiB,GAAG,UAAU,MAAM,EAAE,aAAa,EAAE;AACzD,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvB,IAAI,IAAI,aAAa,KAAK,KAAK,CAAC,EAAE,EAAE,aAAa,GAAG,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,EAAE;AACzE,IAAI,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;AACjC,IAAI,IAAI,QAAQ,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACpI,IAAI,IAAI,QAAQ,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,QAAQ,GAAG,CAAC,CAAC;AAC/I,IAAI,IAAI,QAAQ,GAAG,QAAQ,EAAE;AAC7B,QAAQ,MAAM,IAAI,KAAK,CAAC,yGAAyG,CAAC,CAAC;AACnI,KAAK;AACL,IAAI,IAAI,SAAS,GAAG,UAAU,IAAI,EAAE,OAAO,EAAE,EAAE,OAAOD,eAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;AACpG,QAAQ,IAAI,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,MAAM,EAAE,iBAAiB,CAAC;AACrF,QAAQ,OAAOC,iBAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AAC/C,YAAY,QAAQ,EAAE,CAAC,KAAK;AAC5B,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,IAAI,EAAE,QAAQ,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,GAAY,CAAC,CAAC,CAAC;AACjE,oBAAoB,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;AACnF,oBAAoB,OAAO,CAAC,CAAC,GAAY,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AAC5D,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,EAAE,CAAC,IAAI,EAAE,CAAC;AAC9B,oBAAoB,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;AACjC,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpD,oBAAoB,QAAQ,GAAGC,QAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC/C,oBAAoB,IAAI,aAAa,CAAC,QAAQ,EAAE;AAChD,wBAAwB,gBAAgB,GAAG,EAAE,CAAC;AAC9C,wBAAwB,IAAI;AAC5B,4BAA4B,gBAAgB,GAAGC,gBAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC1E,yBAAyB;AACzB,wBAAwB,OAAO,CAAC,EAAE;AAClC,4BAA4B,gBAAgB,GAAG;AAC/C,gCAAgC,CAAC,YAAY,KAAK;AAClD,oCAAoCC,oBAAY,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1E,sCAAsC,CAAC;AACvC,6BAA6B,CAAC;AAC9B,yBAAyB;AACzB,wBAAwB,IAAI,CAAC,gBAAgB,KAAK,IAAI,IAAI,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,gBAAgB,CAAC,MAAM,IAAI,CAAC,EAAE;AAC/H,4BAA4B,OAAO,CAAC,CAAC,GAAa,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9G,yBAAyB;AACzB,qBAAqB;AACrB,oBAAoB,OAAO,CAAC,CAAC,GAAYC,eAAO,CAAC;AACjD,4BAA4B,MAAM,EAAE,MAAM;AAC1C,4BAA4B,QAAQ,EAAE,QAAQ;AAC9C,4BAA4B,cAAc,EAAE,IAAI,CAAC,SAAS;AAC1D,4BAA4B,aAAa,EAAE,IAAI,CAAC,aAAa;AAC7D,yBAAyB,CAAC,CAAC,CAAC;AAC5B,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,MAAM,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;AACvC,oBAAoB,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/D,oBAAoB,OAAO,CAAC,CAAC,GAAa,IAAI,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC;AAC3E,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC,CAAC,EAAE,CAAC;AACV,IAAI,SAAS,UAAU,GAAG;AAC1B,QAAQ,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;AACjC,QAAQ,IAAI,OAAO,GAAG,YAAY;AAClC,YAAY,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;AAC5C,gBAAgB,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;AACzC,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,OAAO,WAAW,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,OAAO,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE;AACpC,QAAQ,UAAU,EAAE,UAAU;AAG9B,KAAK,CAAC,CAAC;AACP;;;;;"}