{"version": 3, "file": "createSchemaFetch.js", "sourceRoot": "", "sources": ["../../../src/testing/experimental/createSchemaFetch.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AAE1D,OAAO,EAAE,GAAG,EAAE,MAAM,qBAAqB,CAAC;AAC1C,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,EAAE,IAAI,EAAE,MAAM,iBAAiB,CAAC;AAEvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4BG;AACH,IAAM,iBAAiB,GAAG,UACxB,MAAqB,EACrB,aAGsB;;IAHtB,8BAAA,EAAA,kBAGM,QAAQ,EAAE,IAAI,EAAE;IAEtB,IAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;IAC/B,IAAM,QAAQ,GAAG,MAAA,MAAA,aAAa,CAAC,KAAK,0CAAE,GAAG,mCAAI,CAAC,CAAC;IAC/C,IAAM,QAAQ,GAAG,MAAA,MAAA,aAAa,CAAC,KAAK,0CAAE,GAAG,mCAAI,QAAQ,GAAG,CAAC,CAAC;IAE1D,IAAI,QAAQ,GAAG,QAAQ,EAAE,CAAC;QACxB,MAAM,IAAI,KAAK,CACb,yGAAyG,CAC1G,CAAC;IACJ,CAAC;IAED,IAAM,SAAS,GAAoD,UACjE,IAAI,EACJ,OAAO;;;;;yBAEH,CAAA,QAAQ,GAAG,CAAC,CAAA,EAAZ,wBAAY;oBACR,WAAW,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC;oBACrE,qBAAM,IAAI,CAAC,WAAW,CAAC,EAAA;;oBAAvB,SAAuB,CAAC;;;oBAGpB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBAChC,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBAEjC,IAAI,aAAa,CAAC,QAAQ,EAAE,CAAC;wBACvB,gBAAgB,GAAqC,EAAE,CAAC;wBAE5D,IAAI,CAAC;4BACH,gBAAgB,GAAG,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;wBAChD,CAAC;wBAAC,OAAO,CAAC,EAAE,CAAC;4BACX,gBAAgB,GAAG;gCACjB,CAAC,YAAY,KAAK,CAAC,CAAC;oCAClB,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;oCACxC,CAAC,CAAE,CAAS;6BACb,CAAC;wBACJ,CAAC;wBAED,IAAI,CAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,IAAG,CAAC,EAAE,CAAC;4BACjC,sBAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,gBAAgB,EAAE,CAAC,CAAC,EAAC;wBACpE,CAAC;oBACH,CAAC;oBAEc,qBAAM,OAAO,CAAC;4BAC3B,MAAM,QAAA;4BACN,QAAQ,UAAA;4BACR,cAAc,EAAE,IAAI,CAAC,SAAS;4BAC9B,aAAa,EAAE,IAAI,CAAC,aAAa;yBAClC,CAAC,EAAA;;oBALI,MAAM,GAAG,SAKb;oBAEI,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBAEjD,sBAAO,IAAI,QAAQ,CAAC,iBAAiB,CAAC,EAAC;;;SACxC,CAAC;IAEF,SAAS,UAAU;QACjB,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;QAEzB,IAAM,OAAO,GAAG;YACd,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC/B,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;QAEF,OAAO,WAAW,CAAC,EAAE,OAAO,SAAA,EAAE,EAAE,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE;QAC9B,UAAU,YAAA;QACV,kEAAkE;QAClE,6BAA6B;KAC9B,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,OAAO,EAAE,iBAAiB,EAAE,CAAC", "sourcesContent": ["import { execute, Graph<PERSON>Error, validate } from \"graphql\";\nimport type { GraphQLFormattedError, GraphQLSchema } from \"graphql\";\nimport { gql } from \"../../core/index.js\";\nimport { withCleanup } from \"../internal/index.js\";\nimport { wait } from \"../core/wait.js\";\n\n/**\n * A function that accepts a static `schema` and a `mockFetchOpts` object and\n * returns a disposable object with `mock` and `restore` functions.\n *\n * The `mock` function is a mock fetch function that is set on the global\n * `window` object. This function intercepts any fetch requests and\n * returns a response by executing the operation against the provided schema.\n *\n * The `restore` function is a cleanup function that will restore the previous\n * `fetch`. It is automatically called if the function's return value is\n * declared with `using`. If your environment does not support the language\n * feature `using`, you should manually invoke the `restore` function.\n *\n * @param schema - A `GraphQLSchema`.\n * @param mockFetchOpts - Configuration options.\n * @returns An object with both `mock` and `restore` functions.\n *\n * @example\n * ```js\n * using _fetch = createSchemaFetch(schema); // automatically restores fetch after exiting the block\n *\n * const { restore } = createSchemaFetch(schema);\n * restore(); // manually restore fetch if `using` is not supported\n * ```\n * @since 3.10.0\n * @alpha\n * @deprecated `createSchemaFetch` is deprecated and will be removed in 3.12.0.\n * Please migrate to [`@apollo/graphql-testing-library`](https://github.com/apollographql/graphql-testing-library).\n */\nconst createSchemaFetch = (\n  schema: GraphQLSchema,\n  mockFetchOpts: {\n    validate?: boolean;\n    delay?: { min: number; max: number };\n  } = { validate: true }\n) => {\n  const prevFetch = window.fetch;\n  const delayMin = mockFetchOpts.delay?.min ?? 3;\n  const delayMax = mockFetchOpts.delay?.max ?? delayMin + 2;\n\n  if (delayMin > delayMax) {\n    throw new Error(\n      \"Please configure a minimum delay that is less than the maximum delay. The default minimum delay is 3ms.\"\n    );\n  }\n\n  const mockFetch: (uri?: any, options?: any) => Promise<Response> = async (\n    _uri,\n    options\n  ) => {\n    if (delayMin > 0) {\n      const randomDelay = Math.random() * (delayMax - delayMin) + delayMin;\n      await wait(randomDelay);\n    }\n\n    const body = JSON.parse(options.body);\n    const document = gql(body.query);\n\n    if (mockFetchOpts.validate) {\n      let validationErrors: readonly GraphQLFormattedError[] = [];\n\n      try {\n        validationErrors = validate(schema, document);\n      } catch (e) {\n        validationErrors = [\n          e instanceof Error ?\n            GraphQLError.prototype.toJSON.apply(e)\n          : (e as any),\n        ];\n      }\n\n      if (validationErrors?.length > 0) {\n        return new Response(JSON.stringify({ errors: validationErrors }));\n      }\n    }\n\n    const result = await execute({\n      schema,\n      document,\n      variableValues: body.variables,\n      operationName: body.operationName,\n    });\n\n    const stringifiedResult = JSON.stringify(result);\n\n    return new Response(stringifiedResult);\n  };\n\n  function mockGlobal() {\n    window.fetch = mockFetch;\n\n    const restore = () => {\n      if (window.fetch === mockFetch) {\n        window.fetch = prevFetch;\n      }\n    };\n\n    return withCleanup({ restore }, restore);\n  }\n\n  return Object.assign(mockFetch, {\n    mockGlobal,\n    // if https://github.com/rbuckton/proposal-using-enforcement lands\n    // [Symbol.enter]: mockGlobal\n  });\n};\n\nexport { createSchemaFetch };\n"]}