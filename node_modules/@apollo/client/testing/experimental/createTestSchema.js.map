{"version": 3, "file": "createTestSchema.js", "sourceRoot": "", "sources": ["../../../src/testing/experimental/createTestSchema.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,oBAAoB,EAAE,MAAM,uBAAuB,CAAC;AAC7D,OAAO,EAAE,cAAc,EAAE,MAAM,sBAAsB,CAAC;AACtD,OAAO,EAAE,gBAAgB,EAAE,MAAM,0BAA0B,CAAC;AAgB5D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmCG;AACH,IAAM,gBAAgB,GAAG,UACvB,kBAAiC,EACjC,OAA0B;;IAE1B,IAAI,eAAe,gBAAQ,OAAO,CAAC,SAAS,CAAE,CAAC;IAC/C,IAAI,YAAY,GAAG,oBAAoB,CAAC;QACtC,MAAM,EAAE,gBAAgB,CAAC,kBAAkB,EAAE,MAAA,OAAO,CAAC,OAAO,mCAAI,EAAE,CAAC;QACnE,SAAS,EAAE,eAAe;KAC3B,CAAC,CAAC;IAEH,IAAM,GAAG,GAAkB;QACzB,GAAG,EAAE,UAAC,EAA2B;gBAAd,YAAY,eAAA;YAC7B,gFAAgF;YAChF,eAAe,GAAG,cAAc,CAAC,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,CAAC;YAElE,YAAY,GAAG,oBAAoB,CAAC;gBAClC,MAAM,EAAE,YAAY;gBACpB,SAAS,EAAE,eAAe;aAC3B,CAAC,CAAC;YAEH,OAAO,YAA6B,CAAC;QACvC,CAAC;QAED,IAAI,EAAE,UAAC,EAAgC;;gBAAhC,qBAA8B,EAAE,KAAA,EAAnB,YAAY,eAAA;YAC9B,OAAO,gBAAgB,CAAC,YAAY,EAAE;gBACpC,gFAAgF;gBAChF,SAAS,EACP,MAAA,cAAc,CAAC,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,mCAAI,eAAe;gBACpE,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;QACL,CAAC;QAED,KAAK,EAAE;YACL,YAAY,GAAG,oBAAoB,CAAC;gBAClC,MAAM,EAAE,kBAAkB;gBAC1B,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B,CAAC,CAAC;QACL,CAAC;KACF,CAAC;IAEF,IAAM,MAAM,GAAG,IAAI,KAAK,CAAC,YAAY,EAAE;QACrC,GAAG,YAAC,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;gBACb,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC;YAED,gEAAgE;YAChE,0EAA0E;YAC1E,aAAa;YACb,EAAE;YACF,6CAA6C;YAC7C,EAAE;YACF,eAAe;YACf,WAAW;YACX,iBAAiB;YACjB,MAAM;YACN,WAAW;YACX,UAAU;YACV,MAAM;YACN,IAAI;YACJ,EAAE;YACF,sEAAsE;YACtE,sEAAsE;YACtE,mDAAmD;YACnD,EAAE;YACF,6DAA6D;YAC7D,oEAAoE;YACpE,4CAA4C;YAE5C,IAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;YAC9C,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE,CAAC;gBACnC,OAAO,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACrC,CAAC;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC;KACF,CAAC,CAAC;IAEH,OAAO,MAAuB,CAAC;AACjC,CAAC,CAAC;AAEF,OAAO,EAAE,gBAAgB,EAAE,CAAC", "sourcesContent": ["import type { GraphQLSchema } from \"graphql\";\nimport { addResolversToSchema } from \"@graphql-tools/schema\";\nimport { mergeResolvers } from \"@graphql-tools/merge\";\nimport { createMockSchema } from \"./graphql-tools/utils.js\";\nimport type { Resolvers } from \"../../core/types.js\";\n\ntype ProxiedSchema = GraphQLSchema & TestSchemaFns;\n\ninterface TestSchemaFns {\n  add: (addOptions: { resolvers: Resolvers }) => ProxiedSchema;\n  fork: (forkOptions?: { resolvers?: Resolvers }) => ProxiedSchema;\n  reset: () => void;\n}\n\ninterface TestSchemaOptions {\n  resolvers: Resolvers;\n  scalars?: { [key: string]: any };\n}\n\n/**\n * A function that creates a [Proxy object](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Proxy)\n * around a given `schema` with `resolvers`. This proxied schema can be used to\n * progressively layer resolvers on top of the original schema using the `add`\n * method. The `fork` method can be used to create a new proxied schema which\n * can be modified independently of the original schema. `reset` will restore\n * resolvers to the original proxied schema.\n *\n * @param schema - A `GraphQLSchema`.\n * @param options - An `options` object that accepts `scalars` and `resolvers` objects.\n * @returns A `ProxiedSchema` with `add`, `fork` and `reset` methods.\n *\n * @example\n * ```js\n *\n * const schema = createTestSchema(schemaWithTypeDefs, {\n *   resolvers: {\n       Query: {\n         writer: () => ({\n           name: \"Ada Lovelace\",\n         }),\n       }\n     },\n     scalars: {\n       ID: () => \"1\",\n       Int: () => 36,\n       String: () => \"String\",\n       Date: () => new Date(\"December 10, 1815 01:00:00\").toJSON().split(\"T\")[0],\n     }\n   });\n * ```\n * @since 3.9.0\n * @alpha\n * @deprecated `createTestSchema` is deprecated and will be removed in 3.12.0.\n * Please migrate to [`@apollo/graphql-testing-library`](https://github.com/apollographql/graphql-testing-library).\n */\nconst createTestSchema = (\n  schemaWithTypeDefs: GraphQLSchema,\n  options: TestSchemaOptions\n): ProxiedSchema => {\n  let targetResolvers = { ...options.resolvers };\n  let targetSchema = addResolversToSchema({\n    schema: createMockSchema(schemaWithTypeDefs, options.scalars ?? {}),\n    resolvers: targetResolvers,\n  });\n\n  const fns: TestSchemaFns = {\n    add: ({ resolvers: newResolvers }) => {\n      // @ts-ignore TODO(fixme): IResolvers type does not play well with our Resolvers\n      targetResolvers = mergeResolvers([targetResolvers, newResolvers]);\n\n      targetSchema = addResolversToSchema({\n        schema: targetSchema,\n        resolvers: targetResolvers,\n      });\n\n      return targetSchema as ProxiedSchema;\n    },\n\n    fork: ({ resolvers: newResolvers } = {}) => {\n      return createTestSchema(targetSchema, {\n        // @ts-ignore TODO(fixme): IResolvers type does not play well with our Resolvers\n        resolvers:\n          mergeResolvers([targetResolvers, newResolvers]) ?? targetResolvers,\n        scalars: options.scalars,\n      });\n    },\n\n    reset: () => {\n      targetSchema = addResolversToSchema({\n        schema: schemaWithTypeDefs,\n        resolvers: options.resolvers,\n      });\n    },\n  };\n\n  const schema = new Proxy(targetSchema, {\n    get(_target, p) {\n      if (p in fns) {\n        return Reflect.get(fns, p);\n      }\n\n      // An optimization that eliminates round-trips through the proxy\n      // on class methods invoked via `this` on a base class instance wrapped by\n      // the proxy.\n      //\n      // For example, consider the following class:\n      //\n      // class Base {\n      //   foo(){\n      //     this.bar()\n      //   }\n      //   bar(){\n      //     ...\n      //   }\n      // }\n      //\n      // Calling `proxy.foo()` would call `foo` with `this` being the proxy.\n      // This would result in calling `proxy.bar()` which would again invoke\n      // the proxy to resolve `bar` and call that method.\n      //\n      // Instead, calls to `proxy.foo()` should result in a call to\n      // `innerObject.foo()` with a `this` of `innerObject`, and that call\n      // should directly call `innerObject.bar()`.\n\n      const property = Reflect.get(targetSchema, p);\n      if (typeof property === \"function\") {\n        return property.bind(targetSchema);\n      }\n      return property;\n    },\n  });\n\n  return schema as ProxiedSchema;\n};\n\nexport { createTestSchema };\n"]}