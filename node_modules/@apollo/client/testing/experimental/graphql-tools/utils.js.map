{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../src/testing/experimental/graphql-tools/utils.ts"], "names": [], "mappings": ";AAOA,OAAO,EACL,oBAAoB,EACpB,aAAa,EACb,gBAAgB,EAChB,oBAAoB,EACpB,eAAe,EACf,cAAc,EACd,UAAU,EACV,eAAe,EACf,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,WAAW,GACZ,MAAM,SAAS,CAAC;AAEjB,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAC;AAC9D,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAC;AAE/E,kCAAkC;AAClC,wHAAwH;AACxH,IAAM,UAAU,GAAG,UAAI,GAAQ,IAAK,OAAA,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAA3C,CAA2C,CAAC;AAEhF;;;;;;;;;;;;;;;;;;;GAmBG;AACH,IAAM,gBAAgB,GAAG,UACvB,YAA2B,EAC3B,KAA6B;;IAE7B,kCAAkC;IAClC,6HAA6H;IAC7H,IAAM,OAAO,GAAG,UAAC,QAAgB;QAC/B,IAAM,IAAI,GAAG,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE5C,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,KAAK,CACb,UAAG,QAAQ,+DAA4D,CACxE,CAAC;QACJ,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IAEF,kCAAkC;IAClC,6HAA6H;IAC7H,IAAM,YAAY,GAAG,UAAC,QAAgB,EAAE,SAAiB;QACvD,IAAI,SAAS,KAAK,YAAY,EAAE,CAAC;YAC/B,OAAO,aAAa,CAAC;QACvB,CAAC;QAED,IAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE/B,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC;QAE1C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,UAAG,SAAS,qCAA2B,QAAQ,CAAE,CAAC,CAAC;QACrE,CAAC;QAED,OAAO,KAAK,CAAC,IAAI,CAAC;IACpB,CAAC,CAAC;IAEF,kCAAkC;IAClC,6HAA6H;IAC7H,IAAM,qBAAqB,GAAG,UAAC,SAA4B;QACzD,IAAM,YAAY,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;QAEhD,IAAI,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC;YAC/B,IAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAExC,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,qCAA6B,YAAY,CAAC,IAAI,OAAG,CAAC,CAAC;YACrE,CAAC;YAED,OAAO,MAAM,EAAE,CAAC;QAClB,CAAC;aAAM,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACpC,IAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAExC,IAAI,OAAO,MAAM,KAAK,UAAU;gBAAE,OAAO,MAAM,EAAE,CAAC;YAElD,IAAM,MAAM,GAAG,YAAY,CAAC,SAAS,EAAE,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,KAAK,EAAP,CAAO,CAAC,CAAC;YAE5D,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,YAAY,CAAC,YAAY,CAAC,EAAE,CAAC;YACtC,OAAO,EAAE,CAAC;QACZ,CAAC;aAAM,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YACpC,OAAO,kBAAI,IAAI,KAAK,CAAC,CAAC,CAAC,QAAE,GAAG,CAAC;gBAC3B,OAAA,qBAAqB,CAAC,YAAY,CAAC,MAAM,CAAC;YAA1C,CAA0C,CAC3C,CAAC;QACJ,CAAC;aAAM,IAAI,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC;YACxC,IAAM,IAAI,GAAG,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAEtC,IAAI,QAAQ,SAAQ,CAAC;YAErB,IAAI,MAAM,GAA+B,EAAE,CAAC;YAE5C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,QAAQ,GAAG,UAAU,CACnB,YAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,IAAI,EAAN,CAAM,CAAC,CAC/D,CAAC;YACJ,CAAC;iBAAM,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE,CAAC;gBACtC,IAAM,OAAO,GAAG,IAAI,EAAE,CAAC;gBAEvB,IAAI,OAAO,KAAK,IAAI;oBAAE,OAAO,IAAI,CAAC;gBAElC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;oBAC9B,MAAM,IAAI,KAAK,CACb,yCAAkC,YAAY,CAAC,IAAI,8BAA2B,CAC/E,CAAC;gBACJ,CAAC;gBAED,MAAM,GAAG,OAAO,CAAC;gBAEjB,IAAI,OAAO,MAAM,CAAC,YAAY,CAAC,KAAK,QAAQ,EAAE,CAAC;oBAC7C,MAAM,IAAI,KAAK,CACb,0CAAkC,YAAY,CAAC,IAAI,OAAG,CACvD,CAAC;gBACJ,CAAC;gBAED,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;YAClC,CAAC;iBAAM,IACL,eAAe,CAAC,IAAI,CAAC;gBACrB,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,UAAU,EACxC,CAAC;gBACD,IAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBAErC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;oBAChC,MAAM,IAAI,KAAK,CACb,8DAAuD,YAAY,CAAC,IAAI,qBAAkB,CAC3F,CAAC;gBACJ,CAAC;gBAED,QAAQ,GAAG,OAAO,CAAC;YACrB,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,KAAK,CAAC,0CAAkC,YAAY,CAAC,IAAI,OAAG,CAAC,CAAC;YAC1E,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,UAAG,YAAY,qBAAkB,CAAC,CAAC;QACrD,CAAC;IACH,CAAC,CAAC;IAEF,kCAAkC;IAClC,wHAAwH;IACxH,IAAM,UAAU,GAAG,UAAC,IAAuB,EAAE,MAAqB;QAChE,IAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAE/C,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACtC,CAAC,CAAC;IAEF,kCAAkC;IAClC,oIAAoI;IACpI,IAAM,YAAY,GAAmC,UACnD,MAAM,EACN,IAAI,EACJ,MAAM,EACN,IAAI;QAEJ,IAAM,oBAAoB,GAAG,oBAAoB,CAC/C,MAAM,EACN,IAAI,EACJ,MAAM,EACN,IAAI,CACL,CAAC;QAEF,qCAAqC;QACrC,IAAI,oBAAoB,KAAK,SAAS;YAAE,OAAO,oBAAoB,CAAC;QAEpE,8EAA8E;QAC9E,yDAAyD;QACzD,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7C,OAAO;gBACL,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI;gBAC9B,GAAG,EAAE,MAAM;gBACX,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,SAAS,EAAE,IAAI;aAChB,CAAC;QACJ,CAAC;QAED,IAAI,oBAAoB,KAAK,SAAS,EAAE,CAAC;YACvC,IAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YAErE,OAAO,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC;IAEF,kCAAkC;IAClC,oIAAoI;IACpI,OAAO,SAAS,CAAC,YAAY;QAC3B,GAAC,UAAU,CAAC,YAAY,IAAG,UAAC,WAAW;YACrC,IAAM,cAAc,gBAAQ,WAAW,CAAE,CAAC;YAE1C,IAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;YAExC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,cAAc,CAAC,OAAO,GAAG,YAAY,CAAC;YACxC,CAAC;YACD,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,GAAC,UAAU,CAAC,aAAa,IAAG,UAAC,IAAI;YAC/B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;gBACxD,OAAO;YACT,CAAC;YAED,IAAM,YAAY,GAAG,UAAC,QAAgB;gBACpC,OAAO,QAAQ,CAAC;YAClB,CAAC,CAAC;YAEF,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBACtB,OAAO,IAAI,gBAAgB,uBACtB,IAAI,CAAC,QAAQ,EAAE,KAClB,WAAW,EAAE,YAAY,IACzB,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,oBAAoB,uBAC1B,IAAI,CAAC,QAAQ,EAAE,KAClB,WAAW,EAAE,YAAY,IACzB,CAAC;YACL,CAAC;QACH,CAAC;YACD,CAAC;AACL,CAAC,CAAC;AAEF,OAAO,EAAE,gBAAgB,EAAE,CAAC", "sourcesContent": ["import type {\n  GraphQLFieldResolver,\n  GraphQLObjectType,\n  GraphQLOutputType,\n  GraphQLSchema,\n} from \"graphql\";\n\nimport {\n  GraphQLInterfaceType,\n  GraphQLString,\n  GraphQLUnionType,\n  defaultFieldResolver,\n  getNullableType,\n  isAbstractType,\n  isEnumType,\n  isInterfaceType,\n  isListType,\n  isObjectType,\n  isScalarType,\n  isUnionType,\n} from \"graphql\";\n\nimport { isNonNullObject } from \"../../../utilities/index.js\";\nimport { MapperKind, mapSchema, getRootTypeNames } from \"@graphql-tools/utils\";\n\n// Taken from @graphql-tools/mock:\n// https://github.com/ardatan/graphql-tools/blob/4b56b04d69b02919f6c5fa4f97d33da63f36e8c8/packages/mock/src/utils.ts#L20\nconst takeRandom = <T>(arr: T[]) => arr[Math.floor(Math.random() * arr.length)];\n\n/**\n * A function that accepts a static `schema` and a `mocks` object for specifying\n * default scalar mocks and returns a `GraphQLSchema`.\n *\n * @param staticSchema - A static `GraphQLSchema`.\n * @param mocks - An object containing scalar mocks.\n * @returns A `GraphQLSchema` with scalar mocks.\n *\n * @example\n * ```js\n * const mockedSchema = createMockSchema(schema, {\n     ID: () => \"1\",\n     Int: () => 42,\n     String: () => \"String\",\n     Date: () => new Date(\"January 1, 2024 01:00:00\").toJSON().split(\"T\")[0],\n  });\n * ```\n * @since 3.10.0\n * @alpha\n */\nconst createMockSchema = (\n  staticSchema: GraphQLSchema,\n  mocks: { [key: string]: any }\n) => {\n  // Taken from @graphql-tools/mock:\n  // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/MockStore.ts#L613\n  const getType = (typeName: string) => {\n    const type = staticSchema.getType(typeName);\n\n    if (!type || !(isObjectType(type) || isInterfaceType(type))) {\n      throw new Error(\n        `${typeName} does not exist on schema or is not an object or interface`\n      );\n    }\n\n    return type;\n  };\n\n  // Taken from @graphql-tools/mock:\n  // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/MockStore.ts#L597\n  const getFieldType = (typeName: string, fieldName: string) => {\n    if (fieldName === \"__typename\") {\n      return GraphQLString;\n    }\n\n    const type = getType(typeName);\n\n    const field = type.getFields()[fieldName];\n\n    if (!field) {\n      throw new Error(`${fieldName} does not exist on type ${typeName}`);\n    }\n\n    return field.type;\n  };\n\n  // Taken from @graphql-tools/mock:\n  // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/MockStore.ts#L527\n  const generateValueFromType = (fieldType: GraphQLOutputType): unknown => {\n    const nullableType = getNullableType(fieldType);\n\n    if (isScalarType(nullableType)) {\n      const mockFn = mocks[nullableType.name];\n\n      if (typeof mockFn !== \"function\") {\n        throw new Error(`No mock defined for type \"${nullableType.name}\"`);\n      }\n\n      return mockFn();\n    } else if (isEnumType(nullableType)) {\n      const mockFn = mocks[nullableType.name];\n\n      if (typeof mockFn === \"function\") return mockFn();\n\n      const values = nullableType.getValues().map((v) => v.value);\n\n      return takeRandom(values);\n    } else if (isObjectType(nullableType)) {\n      return {};\n    } else if (isListType(nullableType)) {\n      return [...new Array(2)].map(() =>\n        generateValueFromType(nullableType.ofType)\n      );\n    } else if (isAbstractType(nullableType)) {\n      const mock = mocks[nullableType.name];\n\n      let typeName: string;\n\n      let values: { [key: string]: unknown } = {};\n\n      if (!mock) {\n        typeName = takeRandom(\n          staticSchema.getPossibleTypes(nullableType).map((t) => t.name)\n        );\n      } else if (typeof mock === \"function\") {\n        const mockRes = mock();\n\n        if (mockRes === null) return null;\n\n        if (!isNonNullObject(mockRes)) {\n          throw new Error(\n            `Value returned by the mock for ${nullableType.name} is not an object or null`\n          );\n        }\n\n        values = mockRes;\n\n        if (typeof values[\"__typename\"] !== \"string\") {\n          throw new Error(\n            `Please return a __typename in \"${nullableType.name}\"`\n          );\n        }\n\n        typeName = values[\"__typename\"];\n      } else if (\n        isNonNullObject(mock) &&\n        typeof mock[\"__typename\"] === \"function\"\n      ) {\n        const mockRes = mock[\"__typename\"]();\n\n        if (typeof mockRes !== \"string\") {\n          throw new Error(\n            `'__typename' returned by the mock for abstract type ${nullableType.name} is not a string`\n          );\n        }\n\n        typeName = mockRes;\n      } else {\n        throw new Error(`Please return a __typename in \"${nullableType.name}\"`);\n      }\n\n      return typeName;\n    } else {\n      throw new Error(`${nullableType} not implemented`);\n    }\n  };\n\n  // Taken from @graphql-tools/mock:\n  // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/utils.ts#L53\n  const isRootType = (type: GraphQLObjectType, schema: GraphQLSchema) => {\n    const rootTypeNames = getRootTypeNames(schema);\n\n    return rootTypeNames.has(type.name);\n  };\n\n  // Taken from @graphql-tools/mock:\n  // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/addMocksToSchema.ts#L123\n  const mockResolver: GraphQLFieldResolver<any, any> = (\n    source,\n    args,\n    contex,\n    info\n  ) => {\n    const defaultResolvedValue = defaultFieldResolver(\n      source,\n      args,\n      contex,\n      info\n    );\n\n    // priority to default resolved value\n    if (defaultResolvedValue !== undefined) return defaultResolvedValue;\n\n    // we have to handle the root mutation, root query and root subscription types\n    // differently, because no resolver is called at the root\n    if (isRootType(info.parentType, info.schema)) {\n      return {\n        typeName: info.parentType.name,\n        key: \"ROOT\",\n        fieldName: info.fieldName,\n        fieldArgs: args,\n      };\n    }\n\n    if (defaultResolvedValue === undefined) {\n      const fieldType = getFieldType(info.parentType.name, info.fieldName);\n\n      return generateValueFromType(fieldType);\n    }\n\n    return undefined;\n  };\n\n  // Taken from @graphql-tools/mock:\n  // https://github.com/ardatan/graphql-tools/blob/5ed60e44f94868f976cd28fe1b6a764fb146bbe9/packages/mock/src/addMocksToSchema.ts#L176\n  return mapSchema(staticSchema, {\n    [MapperKind.OBJECT_FIELD]: (fieldConfig) => {\n      const newFieldConfig = { ...fieldConfig };\n\n      const oldResolver = fieldConfig.resolve;\n\n      if (!oldResolver) {\n        newFieldConfig.resolve = mockResolver;\n      }\n      return newFieldConfig;\n    },\n\n    [MapperKind.ABSTRACT_TYPE]: (type) => {\n      if (type.resolveType != null && type.resolveType.length) {\n        return;\n      }\n\n      const typeResolver = (typename: string) => {\n        return typename;\n      };\n\n      if (isUnionType(type)) {\n        return new GraphQLUnionType({\n          ...type.toConfig(),\n          resolveType: typeResolver,\n        });\n      } else {\n        return new GraphQLInterfaceType({\n          ...type.toConfig(),\n          resolveType: typeResolver,\n        });\n      }\n    },\n  });\n};\n\nexport { createMockSchema };\n"]}