{"version": 3, "file": "utils.test.js", "sourceRoot": "", "sources": ["../../../../src/testing/experimental/graphql-tools/utils.test.ts"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,sIAAsI;;AAEtI,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,SAAS,CAAC;AAC/C,OAAO,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAE9C,IAAM,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAEnD,IAAM,KAAK,GAAG;IACZ,GAAG,EAAE,cAAM,OAAA,CAAC,EAAD,CAAC;IACZ,KAAK,EAAE,cAAM,OAAA,IAAI,EAAJ,CAAI;IACjB,MAAM,EAAE,cAAM,OAAA,QAAQ,EAAR,CAAQ;IACtB,EAAE,EAAE,cAAM,OAAA,IAAI,EAAJ,CAAI;IACd,IAAI,EAAE,cAAM,OAAA,QAAQ,EAAR,CAAQ;CACrB,CAAC;AAEF,IAAM,QAAQ,GAAG,aAAa,CAAC,m1BAwD9B,CAAC;AAEF,IAAM,MAAM,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;AAErC,QAAQ,CAAC,kBAAkB,EAAE;IAC3B,EAAE,CAAC,OAAO,EAAE;;;;;oBACJ,KAAK,GAAiB,0GAQ3B,CAAC;oBAEI,YAAY,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;oBAE5B,qBAAM,OAAO,CAAC;4BACrC,MAAM,EAAE,YAAY;4BACpB,MAAM,EAAE,KAAK;yBACd,CAAC,EAAA;;oBAHI,KAAmB,SAGvB,EAHM,IAAI,UAAA,EAAE,MAAM,YAAA;oBAKpB,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;oBACjC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;oBAErB,UAAU,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAG,QAAQ,CAAQ,CAAC;oBAC3C,MAAM,CAAC,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC/C,MAAM,CAAC,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACjD,MAAM,CAAC,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAExB,qBAAM,OAAO,CAAC;4BACpC,MAAM,EAAE,YAAY;4BACpB,MAAM,EAAE,KAAK;yBACd,CAAC,EAAA;;oBAHY,KAAK,GAAK,CAAA,SAGtB,CAAA,KAHiB;oBAKb,WAAW,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAG,QAAQ,CAAQ,CAAC;oBAE7C,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;;;;SACrD,CAAC,CAAC;IAEH,EAAE,CAAC,sBAAsB,EAAE;;;;;oBACnB,KAAK,GAAiB,4FAO3B,CAAC;oBACI,YAAY,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;oBAC5B,qBAAM,OAAO,CAAC;4BACrC,MAAM,EAAE,YAAY;4BACpB,MAAM,EAAE,KAAK;yBACd,CAAC,EAAA;;oBAHI,KAAmB,SAGvB,EAHM,IAAI,UAAA,EAAE,MAAM,YAAA;oBAKpB,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;oBACjC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;oBACrB,UAAU,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAG,QAAQ,CAAQ,CAAC;oBAC3C,MAAM,CAAC,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAChD,MAAM,CAAC,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAEzB,qBAAM,OAAO,CAAC;4BACpC,MAAM,EAAE,YAAY;4BACpB,MAAM,EAAE,KAAK;yBACd,CAAC,EAAA;;oBAHY,KAAK,GAAK,CAAA,SAGtB,CAAA,KAHiB;oBAKb,WAAW,GAAG,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAG,QAAQ,CAAQ,CAAC;oBAE7C,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;;;;SACvD,CAAC,CAAC;IAEH,EAAE,CAAC,0BAA0B,EAAE;;;;;oBACvB,KAAK,GAAiB,wQAc3B,CAAC;oBAEI,YAAY,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;oBAE5B,qBAAM,OAAO,CAAC;4BACrC,MAAM,EAAE,YAAY;4BACpB,MAAM,EAAE,KAAK;yBACd,CAAC,EAAA;;oBAHI,KAAmB,SAGvB,EAHM,IAAI,UAAA,EAAE,MAAM,YAAA;oBAKpB,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;oBACjC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;oBAC3B,MAAM,CAAE,IAAK,CAAC,QAAQ,CAAS,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;;;;SACvE,CAAC,CAAC;IAEH,EAAE,CAAC,8BAA8B,EAAE;;;;;oBAC3B,KAAK,GAAiB,iRAe3B,CAAC;oBAEI,YAAY,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;oBAE5B,qBAAM,OAAO,CAAC;4BACrC,MAAM,EAAE,YAAY;4BACpB,MAAM,EAAE,KAAK;yBACd,CAAC,EAAA;;oBAHI,KAAmB,SAGvB,EAHM,IAAI,UAAA,EAAE,MAAM,YAAA;oBAKpB,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;oBACjC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;oBAC3B,MAAM,CAAE,IAAK,CAAC,QAAQ,CAAS,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;;;;SACtE,CAAC,CAAC;IAEH,EAAE,CAAC,8BAA8B,EAAE;;;;;oBAC3B,KAAK,GAAiB,wIAS3B,CAAC;oBAEI,YAAY,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;oBAE5B,qBAAM,OAAO,CAAC;4BACrC,MAAM,EAAE,YAAY;4BACpB,MAAM,EAAE,KAAK;yBACd,CAAC,EAAA;;oBAHI,KAAmB,SAGvB,EAHM,IAAI,UAAA,EAAE,MAAM,YAAA;oBAKpB,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;oBACjC,MAAM,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,CAAC;oBAC3B,MAAM,CAAE,IAAK,CAAC,QAAQ,CAAS,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;;;;SACxE,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "sourcesContent": ["// Originally from @graphql-tools/mock\n// https://github.com/ardatan/graphql-tools/blob/4b56b04d69b02919f6c5fa4f97d33da63f36e8c8/packages/mock/tests/addMocksToSchema.spec.ts\n\nimport { buildSchema, graphql } from \"graphql\";\nimport { createMockSchema } from \"./utils.js\";\n\nconst mockDate = new Date().toJSON().split(\"T\")[0];\n\nconst mocks = {\n  Int: () => 6,\n  Float: () => 22.1,\n  String: () => \"string\",\n  ID: () => \"id\",\n  Date: () => mockDate,\n};\n\nconst typeDefs = /* GraphQL */ `\n  type User {\n    id: ID!\n    age: Int!\n    name: String!\n    image: UserImage!\n    book: Book!\n  }\n\n  type Author {\n    _id: ID!\n    name: String!\n    book: Book!\n  }\n\n  union UserImage = UserImageSolidColor | UserImageURL\n\n  type UserImageSolidColor {\n    color: String!\n  }\n\n  type UserImageURL {\n    url: String!\n  }\n\n  scalar Date\n\n  interface Book {\n    id: ID!\n    title: String\n    publishedAt: Date\n  }\n\n  type TextBook implements Book {\n    id: ID!\n    title: String\n    publishedAt: Date\n    text: String\n  }\n\n  type ColoringBook implements Book {\n    id: ID!\n    title: String\n    publishedAt: Date\n    colors: [String]\n  }\n\n  type Query {\n    viewer: User!\n    userById(id: ID!): User!\n    author: Author!\n  }\n\n  type Mutation {\n    changeViewerName(newName: String!): User!\n  }\n`;\n\nconst schema = buildSchema(typeDefs);\n\ndescribe(\"addMocksToSchema\", () => {\n  it(\"basic\", async () => {\n    const query = /* GraphQL */ `\n      query {\n        viewer {\n          id\n          name\n          age\n        }\n      }\n    `;\n\n    const mockedSchema = createMockSchema(schema, mocks);\n\n    const { data, errors } = await graphql({\n      schema: mockedSchema,\n      source: query,\n    });\n\n    expect(errors).not.toBeDefined();\n    expect(data).toBeDefined();\n\n    const viewerData = data?.[\"viewer\"] as any;\n    expect(typeof viewerData[\"id\"]).toBe(\"string\");\n    expect(typeof viewerData[\"name\"]).toBe(\"string\");\n    expect(typeof viewerData[\"age\"]).toBe(\"number\");\n\n    const { data: data2 } = await graphql({\n      schema: mockedSchema,\n      source: query,\n    });\n\n    const viewerData2 = data2?.[\"viewer\"] as any;\n\n    expect(viewerData2[\"id\"]).toEqual(viewerData[\"id\"]);\n  });\n\n  it(\"handle _id key field\", async () => {\n    const query = /* GraphQL */ `\n      query {\n        author {\n          _id\n          name\n        }\n      }\n    `;\n    const mockedSchema = createMockSchema(schema, mocks);\n    const { data, errors } = await graphql({\n      schema: mockedSchema,\n      source: query,\n    });\n\n    expect(errors).not.toBeDefined();\n    expect(data).toBeDefined();\n    const viewerData = data?.[\"author\"] as any;\n    expect(typeof viewerData[\"_id\"]).toBe(\"string\");\n    expect(typeof viewerData[\"name\"]).toBe(\"string\");\n\n    const { data: data2 } = await graphql({\n      schema: mockedSchema,\n      source: query,\n    });\n\n    const viewerData2 = data2?.[\"author\"] as any;\n\n    expect(viewerData2[\"_id\"]).toEqual(viewerData[\"_id\"]);\n  });\n\n  it(\"should handle union type\", async () => {\n    const query = /* GraphQL */ `\n      query {\n        viewer {\n          image {\n            __typename\n            ... on UserImageURL {\n              url\n            }\n            ... on UserImageSolidColor {\n              color\n            }\n          }\n        }\n      }\n    `;\n\n    const mockedSchema = createMockSchema(schema, mocks);\n\n    const { data, errors } = await graphql({\n      schema: mockedSchema,\n      source: query,\n    });\n\n    expect(errors).not.toBeDefined();\n    expect(data).toBeDefined();\n    expect((data![\"viewer\"] as any)[\"image\"][\"__typename\"]).toBeDefined();\n  });\n\n  it(\"should handle interface type\", async () => {\n    const query = /* GraphQL */ `\n      query {\n        viewer {\n          book {\n            title\n            __typename\n            ... on TextBook {\n              text\n            }\n            ... on ColoringBook {\n              colors\n            }\n          }\n        }\n      }\n    `;\n\n    const mockedSchema = createMockSchema(schema, mocks);\n\n    const { data, errors } = await graphql({\n      schema: mockedSchema,\n      source: query,\n    });\n\n    expect(errors).not.toBeDefined();\n    expect(data).toBeDefined();\n    expect((data![\"viewer\"] as any)[\"book\"][\"__typename\"]).toBeDefined();\n  });\n\n  it(\"should handle custom scalars\", async () => {\n    const query = /* GraphQL */ `\n      query {\n        viewer {\n          book {\n            title\n            publishedAt\n          }\n        }\n      }\n    `;\n\n    const mockedSchema = createMockSchema(schema, mocks);\n\n    const { data, errors } = await graphql({\n      schema: mockedSchema,\n      source: query,\n    });\n\n    expect(errors).not.toBeDefined();\n    expect(data).toBeDefined();\n    expect((data![\"viewer\"] as any)[\"book\"][\"publishedAt\"]).toBe(mockDate);\n  });\n});\n"]}