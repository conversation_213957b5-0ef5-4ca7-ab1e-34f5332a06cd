{"version": 3, "file": "writeToStore.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/writeToStore.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AAChF,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AACtC,OAAO,EAAE,IAAI,EAAE,MAAM,WAAW,CAAC;AAEjC,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAS/B,OAAO,EACL,wBAAwB,EACxB,gBAAgB,EAChB,sBAAsB,EACtB,qBAAqB,EACrB,aAAa,EACb,OAAO,EACP,sBAAsB,EACtB,WAAW,EACX,aAAa,EACb,SAAS,EACT,qBAAqB,EACrB,eAAe,EACf,wBAAwB,EACxB,kBAAkB,GACnB,MAAM,0BAA0B,CAAC;AAQlC,OAAO,EACL,OAAO,EACP,yBAAyB,EACzB,sBAAsB,EACtB,uBAAuB,EACvB,sBAAsB,GACvB,MAAM,cAAc,CAAC;AAKtB,OAAO,EAAE,yBAAyB,EAAE,MAAM,eAAe,CAAC;AAkC1D,4EAA4E;AAC5E,8EAA8E;AAC9E,gFAAgF;AAChF,+EAA+E;AAC/E,+DAA+D;AAC/D,SAAS,gBAAgB,CACvB,OAAiB,EACjB,UAAkC,EAClC,QAA8B;IAE9B,IAAM,GAAG,GAAG,UAAG,UAAU,SAAG,QAAQ,CAAE,CAAC;IACvC,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACxC,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,CAAC,OAAO,CAAC,GAAG,CACjB,GAAG,EACH,CAAC,QAAQ;YACP,OAAO,CAAC,UAAU,KAAK,UAAU,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC;gBAClE,OAAO;gBACT,CAAC,uBACM,OAAO,KACV,UAAU,YAAA,EACV,QAAQ,UAAA,GACT,CAAC,CACP,CAAC;IACJ,CAAC;IACD,OAAO,QAAoB,CAAC;AAC9B,CAAC;AAUD;IACE,qBACkB,KAAoB,EAC5B,MAAoB,EACpB,SAA4C;QAFpC,UAAK,GAAL,KAAK,CAAe;QAC5B,WAAM,GAAN,MAAM,CAAc;QACpB,cAAS,GAAT,SAAS,CAAmC;IACnD,CAAC;IAEG,kCAAY,GAAnB,UACE,KAAsB,EACtB,EAAmE;QAFrE,iBAiHC;YA/GG,KAAK,WAAA,EAAE,MAAM,YAAA,EAAE,MAAM,YAAA,EAAE,SAAS,eAAA,EAAE,SAAS,eAAA;QAE7C,IAAM,mBAAmB,GAAG,sBAAsB,CAAC,KAAK,CAAE,CAAC;QAC3D,IAAM,MAAM,GAAG,yBAAyB,EAAE,CAAC;QAE3C,SAAS,yBACJ,gBAAgB,CAAC,mBAAmB,CAAC,GACrC,SAAU,CACd,CAAC;QAEF,IAAM,OAAO,uBACX,KAAK,OAAA,EACL,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAC5B,KAAK,YAAI,QAAW,EAAE,QAAW;gBAC/B,OAAO,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAM,CAAC;YAC/C,CAAC,EACD,SAAS,WAAA,EACT,SAAS,EAAE,kBAAkB,CAAC,SAAS,CAAC,IACrC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,KAChD,SAAS,EAAE,CAAC,CAAC,SAAS,EACtB,YAAY,EAAE,IAAI,GAAG,EAAE,EACvB,UAAU,EAAE,KAAK,EACjB,QAAQ,EAAE,KAAK,EACf,OAAO,EAAE,IAAI,GAAG,EAAE,GACnB,CAAC;QAEF,IAAM,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC;YACnC,MAAM,EAAE,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;YACrC,MAAM,QAAA;YACN,YAAY,EAAE,mBAAmB,CAAC,YAAY;YAC9C,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE;YAC7B,OAAO,SAAA;SACR,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,iBAAiB,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAC;QAClE,CAAC;QAED,uEAAuE;QACvE,2EAA2E;QAC3E,OAAO,CAAC,YAAY,CAAC,OAAO,CAC1B,UAAC,EAAwC,EAAE,MAAM;gBAA9C,WAAW,iBAAA,EAAE,SAAS,eAAA,EAAE,YAAY,kBAAA;YACrC,IAAM,SAAS,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;YAExC,IAAI,SAAS,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACpC,IAAM,OAAO,GAAG,KAAI,CAAC,WAAW,CAC9B,SAAS,EACT,SAAS,EACT,WAAW,EACX,OAAO,CACR,CAAC;gBACF,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;oBACzB,qEAAqE;oBACrE,qEAAqE;oBACrE,kCAAkC;oBAClC,OAAO;gBACT,CAAC;gBACD,wEAAwE;gBACxE,0DAA0D;gBAC1D,WAAW,GAAG,OAAO,CAAC;YACxB,CAAC;YAED,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;gBAClC,IAAM,yBAAuB,GAC3B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBACtB,YAAY,CAAC,OAAO,CAAC,UAAC,KAAK;oBACzB,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;wBACvB,yBAAuB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;oBACnD,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,IAAM,iBAAe,GAAG,UAAC,cAAsB;oBAC7C,OAAA,yBAAuB,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;wBAC/D,IAAI;gBADJ,CACI,CAAC;gBAEP,IAAM,kBAAgB,GAAG,UAAC,cAAsB;oBAC9C,IAAM,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;oBACjE,OAAO,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtE,CAAC,CAAC;gBAEF,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAC,cAAc;oBAC9C,gEAAgE;oBAChE,gEAAgE;oBAChE,gEAAgE;oBAChE,6DAA6D;oBAC7D,IACE,iBAAe,CAAC,cAAc,CAAC;wBAC/B,CAAC,kBAAgB,CAAC,cAAc,CAAC,EACjC,CAAC;wBACD,iBAAiB,CACf,SAAS,EACT,WAAW,EACX,cAAc,EACd,OAAO,CAAC,KAAK,CACd,CAAC;oBACJ,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QACnC,CAAC,CACF,CAAC;QAEF,8DAA8D;QAC9D,oEAAoE;QACpE,mEAAmE;QACnE,sEAAsE;QACtE,oCAAoC;QACpC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QAExB,OAAO,GAAG,CAAC;IACb,CAAC;IAEO,yCAAmB,GAA3B,UAA4B,EAQC;QAR7B,iBA6NC;YA5NC,MAAM,YAAA,EACN,MAAM,YAAA,EACN,YAAY,kBAAA,EACZ,OAAO,aAAA;QACP,sEAAsE;QACtE,gEAAgE;QAChE,SAAS,eAAA;QAED,IAAA,QAAQ,GAAK,IAAI,CAAC,KAAK,SAAf,CAAgB;QAEhC,kEAAkE;QAClE,gEAAgE;QAChE,IAAI,QAAQ,GAAgB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEhD,iEAAiE;QACjE,iEAAiE;QACjE,cAAc;QACd,IAAM,QAAQ,GACZ,CAAC,MAAM,IAAI,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAC9C,qBAAqB,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC;YAChE,CAAC,MAAM,IAAK,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAY,CAAC,CAAC;QAElE,IAAI,QAAQ,KAAK,OAAO,QAAQ,EAAE,CAAC;YACjC,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC;QACjC,CAAC;QAED,qEAAqE;QACrE,2EAA2E;QAC3E,wEAAwE;QACxE,sEAAsE;QACtE,0EAA0E;QAC1E,yEAAyE;QACzE,wEAAwE;QACxE,2BAA2B;QAC3B,IAAM,SAAS,GAAsB;YACnC,IAAM,OAAO,GAAG,yBAAyB,CACvC,SAAS,EACT,QAAQ,EACR,OAAO,CAAC,SAAS,CAClB,CAAC;YAEF,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,IAAM,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1D,IAAI,IAAI,EAAE,CAAC;oBACT,IAAM,QAAM,GAAG,QAAQ,CAAC,SAAS,uBAE1B,OAAO,KACV,IAAI,EAAE,IAAI,CAAC,WAAW,KAExB,OAAO,CACR,CAAC;oBAEF,IAAI,QAAM,KAAK,KAAK,CAAC,EAAE,CAAC;wBACtB,OAAO,QAAM,CAAC;oBAChB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC,CAAC;QAEF,IAAM,YAAY,GAAG,IAAI,GAAG,EAAa,CAAC;QAE1C,IAAI,CAAC,aAAa,CAChB,YAAY,EACZ,MAAM;QACN,0EAA0E;QAC1E,0EAA0E;QAC1E,sEAAsE;QACtE,OAAO,EACP,QAAQ,CACT,CAAC,OAAO,CAAC,UAAC,OAAO,EAAE,KAAK;;YACvB,IAAM,cAAc,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACrD,IAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;YAErC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAExB,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE,CAAC;gBACrB,IAAM,cAAc,GAAG,QAAQ,CAAC,iBAAiB,CAAC;oBAChD,QAAQ,UAAA;oBACR,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;oBAC3B,KAAK,OAAA;oBACL,SAAS,EAAE,OAAO,CAAC,SAAS;iBAC7B,CAAC,CAAC;gBAEH,IAAM,SAAS,GAAG,iBAAiB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;gBAE/D,IAAI,aAAa,GAAG,KAAI,CAAC,iBAAiB,CACxC,KAAK,EACL,KAAK;gBACL,iEAAiE;gBACjE,kDAAkD;gBAClD,KAAK,CAAC,YAAY,CAAC,CAAC;oBAClB,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;oBACzC,CAAC,CAAC,OAAO,EACT,SAAS,CACV,CAAC;gBAEF,wEAAwE;gBACxE,uEAAuE;gBACvE,6BAA6B;gBAC7B,IAAI,aAAa,SAAoB,CAAC;gBAEtC,uEAAuE;gBACvE,gEAAgE;gBAChE,IACE,KAAK,CAAC,YAAY;oBAClB,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,uBAAuB,CAAC,aAAa,CAAC,CAAC,EACtE,CAAC;oBACD,aAAa,GAAG,SAAS,CAAS,YAAY,EAAE,aAAa,CAAC,CAAC;gBACjE,CAAC;gBAED,IAAM,KAAK,GAAG,QAAQ,CAAC,gBAAgB,CACrC,QAAQ,EACR,KAAK,CAAC,IAAI,CAAC,KAAK,EAChB,aAAa,CACd,CAAC;gBAEF,IAAI,KAAK,EAAE,CAAC;oBACV,SAAS,CAAC,IAAI,GAAG;wBACf,iEAAiE;wBACjE,KAAK,OAAA;wBACL,QAAQ,UAAA;wBACR,KAAK,OAAA;qBACN,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,0BAA0B,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;gBACxD,CAAC;gBAED,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ;oBAC/B,GAAC,cAAc,IAAG,aAAa;wBAC/B,CAAC;YACL,CAAC;iBAAM,IACL,OAAO;gBACP,CAAC,OAAO,CAAC,UAAU;gBACnB,CAAC,OAAO,CAAC,QAAQ;gBACjB,CAAC,qBAAqB,CAAC,KAAK,CAAC,KAAK,CAAC;gBACnC,mEAAmE;gBACnE,uEAAuE;gBACvE,0BAA0B;gBAC1B,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EACrD,CAAC;gBACD,SAAS,CAAC,KAAK,CACb,4CAA4C,EAC5C,sBAAsB,CAAC,KAAK,CAAC,EAC7B,MAAM,CACP,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,mEAAmE;QACnE,wCAAwC;QACxC,IAAI,CAAC;YACG,IAAA,KAAkB,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAChD,QAAQ,UAAA;gBACR,YAAY,cAAA;gBACZ,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,WAAW,EAAE,QAAQ;gBACrB,SAAS,WAAA;aACV,CAAC,EANK,EAAE,QAAA,EAAE,SAAS,QAMlB,CAAC;YAEH,oEAAoE;YACpE,qBAAqB;YACrB,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;YAEtB,qEAAqE;YACrE,iDAAiD;YACjD,IAAI,SAAS,EAAE,CAAC;gBACd,2CAA2C;gBAC3C,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;YAChD,CAAC;QACH,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,iEAAiE;YACjE,IAAI,CAAC,MAAM;gBAAE,MAAM,CAAC,CAAC;QACvB,CAAC;QAED,IAAI,QAAQ,KAAK,OAAO,MAAM,EAAE,CAAC;YAC/B,IAAM,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;YAEtC,mEAAmE;YACnE,kEAAkE;YAClE,kEAAkE;YAClE,oEAAoE;YACpE,8CAA8C;YAC9C,IAAM,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACvE,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,OAAO,CAAC;YACpD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAExB,iEAAiE;YACjE,kEAAkE;YAClE,iEAAiE;YACjE,gEAAgE;YAChE,yDAAyD;YACzD,IACE,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,EAC3D,CAAC;gBACD,OAAO,OAAO,CAAC;YACjB,CAAC;YAED,IAAM,UAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAClD,IAAI,UAAQ,EAAE,CAAC;gBACb,UAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,UAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;gBACrE,UAAQ,CAAC,SAAS,GAAG,eAAe,CAAC,UAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;gBACpE,YAAY,CAAC,OAAO,CAAC,UAAC,KAAK,IAAK,OAAA,UAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,EAAhC,CAAgC,CAAC,CAAC;YACpE,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE;oBAC/B,WAAW,EAAE,QAAQ;oBACrB,iEAAiE;oBACjE,qEAAqE;oBACrE,0DAA0D;oBAC1D,SAAS,EAAE,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;oBAC3D,YAAY,cAAA;iBACb,CAAC,CAAC;YACL,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,uCAAiB,GAAzB,UACE,KAAU,EACV,KAAgB,EAChB,OAAqB,EACrB,SAAoB;QAJtB,iBAgCC;QA1BC,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAC1C,qEAAqE;YACrE,yEAAyE;YACzE,iEAAiE;YACjE,OAAO,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAC5C,CAAC;QAED,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACnB,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,EAAE,CAAC;gBACvB,IAAM,KAAK,GAAG,KAAI,CAAC,iBAAiB,CAClC,IAAI,EACJ,KAAK,EACL,OAAO,EACP,iBAAiB,CAAC,SAAS,EAAE,CAAC,CAAC,CAChC,CAAC;gBACF,0BAA0B,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;gBACzC,OAAO,KAAK,CAAC;YACf,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO,IAAI,CAAC,mBAAmB,CAAC;YAC9B,MAAM,EAAE,KAAK;YACb,YAAY,EAAE,KAAK,CAAC,YAAY;YAChC,OAAO,SAAA;YACP,SAAS,WAAA;SACV,CAAC,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,6DAA6D;IACrD,mCAAa,GAArB,UAWE,YAA8B,EAC9B,MAA2B,EAC3B,OAAiB,EACjB,QAA2E;QAA3E,yBAAA,EAAA,WAAW,qBAAqB,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC;QAE3E,IAAM,QAAQ,GAAG,IAAI,GAAG,EAAuB,CAAC;QACxC,IAAA,QAAQ,GAAK,IAAI,CAAC,KAAK,SAAf,CAAgB;QAEhC,IAAM,YAAY,GAAG,IAAI,IAAI,CAU1B,KAAK,CAAC,CAAC,CAAC,2DAA2D;QAEtE,CAAC,SAAS,OAAO,CAEf,YAA8B,EAC9B,gBAA0B;YAE1B,IAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CACrC,YAAY;YACZ,iEAAiE;YACjE,sEAAsE;YACtE,uEAAuE;YACvE,wEAAwE;YACxE,gBAAgB,CAAC,UAAU,EAC3B,gBAAgB,CAAC,QAAQ,CAC1B,CAAC;YACF,IAAI,WAAW,CAAC,OAAO;gBAAE,OAAO;YAChC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;YAE3B,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,SAAS;gBACxC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC;oBAAE,OAAO;gBAEnD,IAAA,UAAU,GAAe,gBAAgB,WAA/B,EAAE,QAAQ,GAAK,gBAAgB,SAArB,CAAsB;gBAChD;gBACE,iEAAiE;gBACjE,+DAA+D;gBAC/D,iEAAiE;gBACjE,CAAC,CAAC,UAAU,IAAI,QAAQ,CAAC;oBACzB,eAAe,CAAC,SAAS,CAAC,UAAU,CAAC,EACrC,CAAC;oBACD,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,UAAC,GAAG;wBAC/B,IAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;wBAC5B,IAAI,IAAI,KAAK,QAAQ;4BAAE,UAAU,GAAG,IAAI,CAAC;wBACzC,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;4BACrB,IAAM,IAAI,GAAG,wBAAwB,CAAC,GAAG,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;4BAC9D,yDAAyD;4BACzD,wDAAwD;4BACxD,8DAA8D;4BAC9D,uDAAuD;4BACvD,IAAI,CAAC,IAAI,IAAK,IAAyB,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;gCACrD,QAAQ,GAAG,IAAI,CAAC;4BAClB,CAAC;4BACD,6DAA6D;4BAC7D,6CAA6C;wBAC/C,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;oBACvB,IAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBACzC,IAAI,QAAQ,EAAE,CAAC;wBACb,8DAA8D;wBAC9D,mEAAmE;wBACnE,iEAAiE;wBACjE,UAAU,GAAG,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC;wBAC/C,QAAQ,GAAG,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC;oBAC3C,CAAC;oBAED,QAAQ,CAAC,GAAG,CACV,SAAS,EACT,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAChD,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,IAAM,QAAQ,GAAG,wBAAwB,CACvC,SAAS,EACT,OAAO,CAAC,cAAc,CACvB,CAAC;oBAEF,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;wBACzD,MAAM,iBAAiB,CACrB,sBAAsB,EACtB,SAAS,CAAC,IAAI,CAAC,KAAK,CACrB,CAAC;oBACJ,CAAC;oBAED,IACE,QAAQ;wBACR,QAAQ,CAAC,eAAe,CACtB,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,CAAC,SAAS,CAClB,EACD,CAAC;wBACD,OAAO,CACL,QAAQ,CAAC,YAAY,EACrB,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAChD,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAE1B,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,iCAAW,GAAnB,UACE,SAAoB,EACpB,QAAoB,EACpB,QAAW,EACX,OAAqB,EACrB,cAAsD;;QALxD,iBAmGC;QA5FC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjD,IAAM,GAAC;YACL,yDAAyD;YACzD,kEAAkE;YAClE,4DAA4D;YAC5D,CACE,CAAC,OAAO,CAAC,QAAQ,CAAC;gBAClB,iEAAiE;gBACjE,iEAAiE;gBACjE,uBAAuB;gBACvB,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAC7D,CAAC,CAAC;gBACD,QAAQ;gBACV,CAAC,CAAC,KAAK,CAAC,CAAC;YAEX,0DAA0D;YAC1D,kEAAkE;YAClE,2BAA2B;YAC3B,IAAM,GAAC,GAAG,QAAsC,CAAC;YAEjD,mEAAmE;YACnE,4DAA4D;YAC5D,oEAAoE;YACpE,oDAAoD;YACpD,IAAI,GAAC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACzB,cAAc,GAAG,CAAC,WAAW,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAC,CAAC,CAAC;YAClD,CAAC;YAED,mEAAmE;YACnE,mEAAmE;YACnE,oEAAoE;YACpE,mEAAmE;YACnE,wCAAwC;YACxC,IAAI,eAA2D,CAAC;YAEhE,IAAM,UAAQ,GAAG,UACf,IAAyB,EACzB,IAAqB;gBAErB,OAAO,CACL,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;oBACb,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC;wBACxB,IAAI,CAAC,IAAI,CAAC;wBACZ,CAAC,CAAC,KAAK,CAAC;oBACV,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,CAClD,CAAC;YACJ,CAAC,CAAC;YAEF,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,UAAC,SAAS,EAAE,cAAc;gBAC9C,IAAM,IAAI,GAAG,UAAQ,CAAC,GAAC,EAAE,cAAc,CAAC,CAAC;gBACzC,IAAM,IAAI,GAAG,UAAQ,CAAC,GAAC,EAAE,cAAc,CAAC,CAAC;gBACzC,kEAAkE;gBAClE,IAAI,KAAK,CAAC,KAAK,IAAI;oBAAE,OAAO;gBAC5B,IAAI,cAAc,EAAE,CAAC;oBACnB,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBACtC,CAAC;gBACD,IAAM,IAAI,GAAG,KAAI,CAAC,WAAW,CAC3B,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,cAAc,CACf,CAAC;gBACF,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;oBAClB,eAAa,GAAG,eAAa,IAAI,IAAI,GAAG,EAAE,CAAC;oBAC3C,eAAa,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;gBAC1C,CAAC;gBACD,IAAI,cAAc,EAAE,CAAC;oBACnB,SAAS,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,cAAc,CAAC,CAAC;gBACrD,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,eAAa,EAAE,CAAC;gBAClB,sDAAsD;gBACtD,QAAQ,GAAG,CAAC,OAAO,CAAC,GAAC,CAAC,CAAC,CAAC,CAAC,GAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,cAAM,GAAC,CAAE,CAAM,CAAC;gBACrD,eAAa,CAAC,OAAO,CAAC,UAAC,KAAK,EAAE,IAAI;oBAC/B,QAAgB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBAClC,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,IAAI,SAAS,CAAC,IAAI,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,CACzC,QAAQ,EACR,QAAQ,EACR,SAAS,CAAC,IAAI,EACd,OAAO,EACP,cAAc,IAAI,CAAA,KAAA,OAAO,CAAC,KAAK,CAAA,CAAC,UAAU,WAAI,cAAc,CAAC,CAC9D,CAAC;QACJ,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IACH,kBAAC;AAAD,CAAC,AA9lBD,IA8lBC;;AAED,IAAM,kBAAkB,GAAgB,EAAE,CAAC;AAE3C,SAAS,iBAAiB,CACxB,EAAkB,EAClB,IAAqB;QADnB,GAAG,SAAA;IAGL,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACnB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,kBAAkB,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC;IAChE,CAAC;IACD,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;AACxB,CAAC;AAED,SAAS,eAAe,CACtB,IAA2B,EAC3B,KAA4B;IAE5B,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,gBAAgB,CAAC,KAAK,CAAC;QAAE,OAAO,IAAK,CAAC;IACtE,IAAI,CAAC,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC;QAAE,OAAO,KAAK,CAAC;IAElD,IAAM,IAAI,GACR,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,uBAElB,IAAI,CAAC,IAAI,GACT,KAAK,CAAC,IAAI,EAEjB,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC;IAE5B,IAAM,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;IACxD,IAAM,GAAG,GACP,eAAe,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;QAC3B,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG;YAC1B,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC;IAEd,IAAM,MAAM,GAAG,EAAE,IAAI,MAAA,EAAE,GAAG,KAAA,EAAE,CAAC;IAE7B,IAAI,eAAe,EAAE,CAAC;QACpB,IAAM,oBAAkB,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QAErD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,GAAG;YAC7B,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACnE,oBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,oBAAkB,CAAC,OAAO,CAAC,UAAC,GAAG;YAC7B,MAAM,CAAC,GAAG,CAAC,GAAG,CACZ,GAAG,EACH,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CACvD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,gBAAgB,CAAC,IAA2B;IACnD,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,0BAA0B,CAAC,EAAkB,EAAE,IAAqB;QAAvC,GAAG,SAAA;IACvC,IAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,IAAI,SAAS,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;QAC7C,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACnC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;AACH,CAAC;AAED,IAAM,QAAQ,GAAG,IAAI,GAAG,EAAU,CAAC;AAEnC,sEAAsE;AACtE,0CAA0C;AAC1C,SAAS,iBAAiB,CACxB,WAAsB,EACtB,WAAwB,EACxB,cAAsB,EACtB,KAAsB;IAEtB,IAAM,QAAQ,GAAG,UAAC,QAAiC;QACjD,IAAM,KAAK,GAAG,KAAK,CAAC,aAAa,CAAc,QAAQ,EAAE,cAAc,CAAC,CAAC;QACzE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;IACvC,IAAI,CAAC,QAAQ;QAAE,OAAO;IAEtB,IAAM,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;IACvC,IAAI,CAAC,QAAQ;QAAE,OAAO;IAEtB,mEAAmE;IACnE,2BAA2B;IAC3B,IAAI,WAAW,CAAC,QAAQ,CAAC;QAAE,OAAO;IAElC,qEAAqE;IACrE,qCAAqC;IACrC,IAAI,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC;QAAE,OAAO;IAEtC,gEAAgE;IAChE,8DAA8D;IAC9D,oDAAoD;IACpD,IACE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CACzB,UAAC,GAAG,IAAK,OAAA,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,KAAK,CAAC,EAA7C,CAA6C,CACvD,EACD,CAAC;QACD,OAAO;IACT,CAAC;IAED,IAAM,UAAU,GACd,KAAK,CAAC,aAAa,CAAS,WAAW,EAAE,YAAY,CAAC;QACtD,KAAK,CAAC,aAAa,CAAS,WAAW,EAAE,YAAY,CAAC,CAAC;IACzD,IAAM,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;IACzD,IAAM,WAAW,GAAG,UAAG,UAAU,cAAI,SAAS,CAAE,CAAC;IACjD,iEAAiE;IACjE,IAAI,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC;QAAE,OAAO;IACtC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAE1B,IAAM,cAAc,GAAa,EAAE,CAAC;IACpC,uEAAuE;IACvE,4DAA4D;IAC5D,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC7C,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAC,KAAK;YACjC,IAAM,QAAQ,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YAC1D,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvE,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAChC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS,CAAC,IAAI,CACZ,woBAaH,EACG,SAAS,EACT,UAAU,EACV,cAAc,CAAC,MAAM,CAAC,CAAC;QACrB,oCAAoC;YAClC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;YAC5B,6CAA6C;QACjD,CAAC,CAAC,EAAE,EACJ,WAAW,eACN,QAAQ,gBACR,QAAQ,EACd,CAAC;AACJ,CAAC", "sourcesContent": ["import { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { Trie } from \"@wry/trie\";\nimport type { SelectionSetNode, FieldNode } from \"graphql\";\nimport { Kind } from \"graphql\";\n\nimport type {\n  FragmentMap,\n  FragmentMapFunction,\n  StoreValue,\n  StoreObject,\n  Reference,\n} from \"../../utilities/index.js\";\nimport {\n  getFragmentFromSelection,\n  getDefaultValues,\n  getOperationDefinition,\n  getTypenameFromResult,\n  makeReference,\n  isField,\n  resultKeyNameFromField,\n  isReference,\n  shouldInclude,\n  cloneDeep,\n  addTypenameToDocument,\n  isNonEmptyArray,\n  argumentsObjectFromField,\n  canonicalStringify,\n} from \"../../utilities/index.js\";\n\nimport type {\n  NormalizedCache,\n  ReadMergeModifyContext,\n  MergeTree,\n  InMemoryCacheConfig,\n} from \"./types.js\";\nimport {\n  isArray,\n  makeProcessedFieldsMerger,\n  fieldNameFromStoreName,\n  storeValueIsStoreObject,\n  extractFragmentContext,\n} from \"./helpers.js\";\nimport type { StoreReader } from \"./readFromStore.js\";\nimport type { InMemoryCache } from \"./inMemoryCache.js\";\nimport type { EntityStore } from \"./entityStore.js\";\nimport type { Cache } from \"../../core/index.js\";\nimport { normalizeReadFieldOptions } from \"./policies.js\";\nimport type { ReadFieldFunction } from \"../core/types/common.js\";\n\nexport interface WriteContext extends ReadMergeModifyContext {\n  readonly written: {\n    [dataId: string]: SelectionSetNode[];\n  };\n  readonly fragmentMap: FragmentMap;\n  lookupFragment: FragmentMapFunction;\n  // General-purpose deep-merge function for use during writes.\n  merge<T>(existing: T, incoming: T): T;\n  // If true, merge functions will be called with undefined existing data.\n  overwrite: boolean;\n  incomingById: Map<\n    string,\n    {\n      storeObject: StoreObject;\n      mergeTree?: MergeTree;\n      fieldNodeSet: Set<FieldNode>;\n    }\n  >;\n  // Directive metadata for @client and @defer. We could use a bitfield for this\n  // information to save some space, and use that bitfield number as the keys in\n  // the context.flavors Map.\n  clientOnly: boolean;\n  deferred: boolean;\n  flavors: Map<string, FlavorableWriteContext>;\n}\n\ntype FlavorableWriteContext = Pick<\n  WriteContext,\n  \"clientOnly\" | \"deferred\" | \"flavors\"\n>;\n\n// Since there are only four possible combinations of context.clientOnly and\n// context.deferred values, we should need at most four \"flavors\" of any given\n// WriteContext. To avoid creating multiple copies of the same context, we cache\n// the contexts in the context.flavors Map (shared by all flavors) according to\n// their clientOnly and deferred values (always in that order).\nfunction getContextFlavor<TContext extends FlavorableWriteContext>(\n  context: TContext,\n  clientOnly: TContext[\"clientOnly\"],\n  deferred: TContext[\"deferred\"]\n): TContext {\n  const key = `${clientOnly}${deferred}`;\n  let flavored = context.flavors.get(key);\n  if (!flavored) {\n    context.flavors.set(\n      key,\n      (flavored =\n        context.clientOnly === clientOnly && context.deferred === deferred ?\n          context\n        : {\n            ...context,\n            clientOnly,\n            deferred,\n          })\n    );\n  }\n  return flavored as TContext;\n}\n\ninterface ProcessSelectionSetOptions {\n  dataId?: string;\n  result: Record<string, any>;\n  selectionSet: SelectionSetNode;\n  context: WriteContext;\n  mergeTree: MergeTree;\n}\n\nexport class StoreWriter {\n  constructor(\n    public readonly cache: InMemoryCache,\n    private reader?: StoreReader,\n    private fragments?: InMemoryCacheConfig[\"fragments\"]\n  ) {}\n\n  public writeToStore(\n    store: NormalizedCache,\n    { query, result, dataId, variables, overwrite }: Cache.WriteOptions\n  ): Reference | undefined {\n    const operationDefinition = getOperationDefinition(query)!;\n    const merger = makeProcessedFieldsMerger();\n\n    variables = {\n      ...getDefaultValues(operationDefinition),\n      ...variables!,\n    };\n\n    const context: WriteContext = {\n      store,\n      written: Object.create(null),\n      merge<T>(existing: T, incoming: T) {\n        return merger.merge(existing, incoming) as T;\n      },\n      variables,\n      varString: canonicalStringify(variables),\n      ...extractFragmentContext(query, this.fragments),\n      overwrite: !!overwrite,\n      incomingById: new Map(),\n      clientOnly: false,\n      deferred: false,\n      flavors: new Map(),\n    };\n\n    const ref = this.processSelectionSet({\n      result: result || Object.create(null),\n      dataId,\n      selectionSet: operationDefinition.selectionSet,\n      mergeTree: { map: new Map() },\n      context,\n    });\n\n    if (!isReference(ref)) {\n      throw newInvariantError(`Could not identify object %s`, result);\n    }\n\n    // So far, the store has not been modified, so now it's time to process\n    // context.incomingById and merge those incoming fields into context.store.\n    context.incomingById.forEach(\n      ({ storeObject, mergeTree, fieldNodeSet }, dataId) => {\n        const entityRef = makeReference(dataId);\n\n        if (mergeTree && mergeTree.map.size) {\n          const applied = this.applyMerges(\n            mergeTree,\n            entityRef,\n            storeObject,\n            context\n          );\n          if (isReference(applied)) {\n            // Assume References returned by applyMerges have already been merged\n            // into the store. See makeMergeObjectsFunction in policies.ts for an\n            // example of how this can happen.\n            return;\n          }\n          // Otherwise, applyMerges returned a StoreObject, whose fields we should\n          // merge into the store (see store.merge statement below).\n          storeObject = applied;\n        }\n\n        if (__DEV__ && !context.overwrite) {\n          const fieldsWithSelectionSets: Record<string, true> =\n            Object.create(null);\n          fieldNodeSet.forEach((field) => {\n            if (field.selectionSet) {\n              fieldsWithSelectionSets[field.name.value] = true;\n            }\n          });\n\n          const hasSelectionSet = (storeFieldName: string) =>\n            fieldsWithSelectionSets[fieldNameFromStoreName(storeFieldName)] ===\n            true;\n\n          const hasMergeFunction = (storeFieldName: string) => {\n            const childTree = mergeTree && mergeTree.map.get(storeFieldName);\n            return Boolean(childTree && childTree.info && childTree.info.merge);\n          };\n\n          Object.keys(storeObject).forEach((storeFieldName) => {\n            // If a merge function was defined for this field, trust that it\n            // did the right thing about (not) clobbering data. If the field\n            // has no selection set, it's a scalar field, so it doesn't need\n            // a merge function (even if it's an object, like JSON data).\n            if (\n              hasSelectionSet(storeFieldName) &&\n              !hasMergeFunction(storeFieldName)\n            ) {\n              warnAboutDataLoss(\n                entityRef,\n                storeObject,\n                storeFieldName,\n                context.store\n              );\n            }\n          });\n        }\n\n        store.merge(dataId, storeObject);\n      }\n    );\n\n    // Any IDs written explicitly to the cache will be retained as\n    // reachable root IDs for garbage collection purposes. Although this\n    // logic includes root IDs like ROOT_QUERY and ROOT_MUTATION, their\n    // retainment counts are effectively ignored because cache.gc() always\n    // includes them in its root ID set.\n    store.retain(ref.__ref);\n\n    return ref;\n  }\n\n  private processSelectionSet({\n    dataId,\n    result,\n    selectionSet,\n    context,\n    // This object allows processSelectionSet to report useful information\n    // to its callers without explicitly returning that information.\n    mergeTree,\n  }: ProcessSelectionSetOptions): StoreObject | Reference {\n    const { policies } = this.cache;\n\n    // This variable will be repeatedly updated using context.merge to\n    // accumulate all fields that need to be written into the store.\n    let incoming: StoreObject = Object.create(null);\n\n    // If typename was not passed in, infer it. Note that typename is\n    // always passed in for tricky-to-infer cases such as \"Query\" for\n    // ROOT_QUERY.\n    const typename: string | undefined =\n      (dataId && policies.rootTypenamesById[dataId]) ||\n      getTypenameFromResult(result, selectionSet, context.fragmentMap) ||\n      (dataId && (context.store.get(dataId, \"__typename\") as string));\n\n    if (\"string\" === typeof typename) {\n      incoming.__typename = typename;\n    }\n\n    // This readField function will be passed as context.readField in the\n    // KeyFieldsContext object created within policies.identify (called below).\n    // In addition to reading from the existing context.store (thanks to the\n    // policies.readField(options, context) line at the very bottom), this\n    // version of readField can read from Reference objects that are currently\n    // pending in context.incomingById, which is important whenever keyFields\n    // need to be extracted from a child object that processSelectionSet has\n    // turned into a Reference.\n    const readField: ReadFieldFunction = function (this: void) {\n      const options = normalizeReadFieldOptions(\n        arguments,\n        incoming,\n        context.variables\n      );\n\n      if (isReference(options.from)) {\n        const info = context.incomingById.get(options.from.__ref);\n        if (info) {\n          const result = policies.readField(\n            {\n              ...options,\n              from: info.storeObject,\n            },\n            context\n          );\n\n          if (result !== void 0) {\n            return result;\n          }\n        }\n      }\n\n      return policies.readField(options, context);\n    };\n\n    const fieldNodeSet = new Set<FieldNode>();\n\n    this.flattenFields(\n      selectionSet,\n      result,\n      // This WriteContext will be the default context value for fields returned\n      // by the flattenFields method, but some fields may be assigned a modified\n      // context, depending on the presence of @client and other directives.\n      context,\n      typename\n    ).forEach((context, field) => {\n      const resultFieldKey = resultKeyNameFromField(field);\n      const value = result[resultFieldKey];\n\n      fieldNodeSet.add(field);\n\n      if (value !== void 0) {\n        const storeFieldName = policies.getStoreFieldName({\n          typename,\n          fieldName: field.name.value,\n          field,\n          variables: context.variables,\n        });\n\n        const childTree = getChildMergeTree(mergeTree, storeFieldName);\n\n        let incomingValue = this.processFieldValue(\n          value,\n          field,\n          // Reset context.clientOnly and context.deferred to their default\n          // values before processing nested selection sets.\n          field.selectionSet ?\n            getContextFlavor(context, false, false)\n          : context,\n          childTree\n        );\n\n        // To determine if this field holds a child object with a merge function\n        // defined in its type policy (see PR #7070), we need to figure out the\n        // child object's __typename.\n        let childTypename: string | undefined;\n\n        // The field's value can be an object that has a __typename only if the\n        // field has a selection set. Otherwise incomingValue is scalar.\n        if (\n          field.selectionSet &&\n          (isReference(incomingValue) || storeValueIsStoreObject(incomingValue))\n        ) {\n          childTypename = readField<string>(\"__typename\", incomingValue);\n        }\n\n        const merge = policies.getMergeFunction(\n          typename,\n          field.name.value,\n          childTypename\n        );\n\n        if (merge) {\n          childTree.info = {\n            // TODO Check compatibility against any existing childTree.field?\n            field,\n            typename,\n            merge,\n          };\n        } else {\n          maybeRecycleChildMergeTree(mergeTree, storeFieldName);\n        }\n\n        incoming = context.merge(incoming, {\n          [storeFieldName]: incomingValue,\n        });\n      } else if (\n        __DEV__ &&\n        !context.clientOnly &&\n        !context.deferred &&\n        !addTypenameToDocument.added(field) &&\n        // If the field has a read function, it may be a synthetic field or\n        // provide a default value, so its absence from the written data should\n        // not be cause for alarm.\n        !policies.getReadFunction(typename, field.name.value)\n      ) {\n        invariant.error(\n          `Missing field '%s' while writing result %o`,\n          resultKeyNameFromField(field),\n          result\n        );\n      }\n    });\n\n    // Identify the result object, even if dataId was already provided,\n    // since we always need keyObject below.\n    try {\n      const [id, keyObject] = policies.identify(result, {\n        typename,\n        selectionSet,\n        fragmentMap: context.fragmentMap,\n        storeObject: incoming,\n        readField,\n      });\n\n      // If dataId was not provided, fall back to the id just generated by\n      // policies.identify.\n      dataId = dataId || id;\n\n      // Write any key fields that were used during identification, even if\n      // they were not mentioned in the original query.\n      if (keyObject) {\n        // TODO Reverse the order of the arguments?\n        incoming = context.merge(incoming, keyObject);\n      }\n    } catch (e) {\n      // If dataId was provided, tolerate failure of policies.identify.\n      if (!dataId) throw e;\n    }\n\n    if (\"string\" === typeof dataId) {\n      const dataRef = makeReference(dataId);\n\n      // Avoid processing the same entity object using the same selection\n      // set more than once. We use an array instead of a Set since most\n      // entity IDs will be written using only one selection set, so the\n      // size of this array is likely to be very small, meaning indexOf is\n      // likely to be faster than Set.prototype.has.\n      const sets = context.written[dataId] || (context.written[dataId] = []);\n      if (sets.indexOf(selectionSet) >= 0) return dataRef;\n      sets.push(selectionSet);\n\n      // If we're about to write a result object into the store, but we\n      // happen to know that the exact same (===) result object would be\n      // returned if we were to reread the result with the same inputs,\n      // then we can skip the rest of the processSelectionSet work for\n      // this object, and immediately return a Reference to it.\n      if (\n        this.reader &&\n        this.reader.isFresh(result, dataRef, selectionSet, context)\n      ) {\n        return dataRef;\n      }\n\n      const previous = context.incomingById.get(dataId);\n      if (previous) {\n        previous.storeObject = context.merge(previous.storeObject, incoming);\n        previous.mergeTree = mergeMergeTrees(previous.mergeTree, mergeTree);\n        fieldNodeSet.forEach((field) => previous.fieldNodeSet.add(field));\n      } else {\n        context.incomingById.set(dataId, {\n          storeObject: incoming,\n          // Save a reference to mergeTree only if it is not empty, because\n          // empty MergeTrees may be recycled by maybeRecycleChildMergeTree and\n          // reused for entirely different parts of the result tree.\n          mergeTree: mergeTreeIsEmpty(mergeTree) ? void 0 : mergeTree,\n          fieldNodeSet,\n        });\n      }\n\n      return dataRef;\n    }\n\n    return incoming;\n  }\n\n  private processFieldValue(\n    value: any,\n    field: FieldNode,\n    context: WriteContext,\n    mergeTree: MergeTree\n  ): StoreValue {\n    if (!field.selectionSet || value === null) {\n      // In development, we need to clone scalar values so that they can be\n      // safely frozen with maybeDeepFreeze in readFromStore.ts. In production,\n      // it's cheaper to store the scalar values directly in the cache.\n      return __DEV__ ? cloneDeep(value) : value;\n    }\n\n    if (isArray(value)) {\n      return value.map((item, i) => {\n        const value = this.processFieldValue(\n          item,\n          field,\n          context,\n          getChildMergeTree(mergeTree, i)\n        );\n        maybeRecycleChildMergeTree(mergeTree, i);\n        return value;\n      });\n    }\n\n    return this.processSelectionSet({\n      result: value,\n      selectionSet: field.selectionSet,\n      context,\n      mergeTree,\n    });\n  }\n\n  // Implements https://spec.graphql.org/draft/#sec-Field-Collection, but with\n  // some additions for tracking @client and @defer directives.\n  private flattenFields<\n    TContext extends Pick<\n      WriteContext,\n      | \"clientOnly\"\n      | \"deferred\"\n      | \"flavors\"\n      | \"fragmentMap\"\n      | \"lookupFragment\"\n      | \"variables\"\n    >,\n  >(\n    selectionSet: SelectionSetNode,\n    result: Record<string, any>,\n    context: TContext,\n    typename = getTypenameFromResult(result, selectionSet, context.fragmentMap)\n  ): Map<FieldNode, TContext> {\n    const fieldMap = new Map<FieldNode, TContext>();\n    const { policies } = this.cache;\n\n    const limitingTrie = new Trie<{\n      // Tracks whether (selectionSet, clientOnly, deferred) has been flattened\n      // before. The GraphQL specification only uses the fragment name for\n      // skipping previously visited fragments, but the top-level fragment\n      // selection set corresponds 1:1 with the fagment name (and is slightly\n      // easier too work with), and we need to consider clientOnly and deferred\n      // values as well, potentially revisiting selection sets that were\n      // previously visited with different inherited configurations of those\n      // directives.\n      visited?: boolean;\n    }>(false); // No need for WeakMap, since limitingTrie does not escape.\n\n    (function flatten(\n      this: void,\n      selectionSet: SelectionSetNode,\n      inheritedContext: TContext\n    ) {\n      const visitedNode = limitingTrie.lookup(\n        selectionSet,\n        // Because we take inheritedClientOnly and inheritedDeferred into\n        // consideration here (in addition to selectionSet), it's possible for\n        // the same selection set to be flattened more than once, if it appears\n        // in the query with different @client and/or @directive configurations.\n        inheritedContext.clientOnly,\n        inheritedContext.deferred\n      );\n      if (visitedNode.visited) return;\n      visitedNode.visited = true;\n\n      selectionSet.selections.forEach((selection) => {\n        if (!shouldInclude(selection, context.variables)) return;\n\n        let { clientOnly, deferred } = inheritedContext;\n        if (\n          // Since the presence of @client or @defer on this field can only\n          // cause clientOnly or deferred to become true, we can skip the\n          // forEach loop if both clientOnly and deferred are already true.\n          !(clientOnly && deferred) &&\n          isNonEmptyArray(selection.directives)\n        ) {\n          selection.directives.forEach((dir) => {\n            const name = dir.name.value;\n            if (name === \"client\") clientOnly = true;\n            if (name === \"defer\") {\n              const args = argumentsObjectFromField(dir, context.variables);\n              // The @defer directive takes an optional args.if boolean\n              // argument, similar to @include(if: boolean). Note that\n              // @defer(if: false) does not make context.deferred false, but\n              // instead behaves as if there was no @defer directive.\n              if (!args || (args as { if?: boolean }).if !== false) {\n                deferred = true;\n              }\n              // TODO In the future, we may want to record args.label using\n              // context.deferred, if a label is specified.\n            }\n          });\n        }\n\n        if (isField(selection)) {\n          const existing = fieldMap.get(selection);\n          if (existing) {\n            // If this field has been visited along another recursive path\n            // before, the final context should have clientOnly or deferred set\n            // to true only if *all* paths have the directive (hence the &&).\n            clientOnly = clientOnly && existing.clientOnly;\n            deferred = deferred && existing.deferred;\n          }\n\n          fieldMap.set(\n            selection,\n            getContextFlavor(context, clientOnly, deferred)\n          );\n        } else {\n          const fragment = getFragmentFromSelection(\n            selection,\n            context.lookupFragment\n          );\n\n          if (!fragment && selection.kind === Kind.FRAGMENT_SPREAD) {\n            throw newInvariantError(\n              `No fragment named %s`,\n              selection.name.value\n            );\n          }\n\n          if (\n            fragment &&\n            policies.fragmentMatches(\n              fragment,\n              typename,\n              result,\n              context.variables\n            )\n          ) {\n            flatten(\n              fragment.selectionSet,\n              getContextFlavor(context, clientOnly, deferred)\n            );\n          }\n        }\n      });\n    })(selectionSet, context);\n\n    return fieldMap;\n  }\n\n  private applyMerges<T extends StoreValue>(\n    mergeTree: MergeTree,\n    existing: StoreValue,\n    incoming: T,\n    context: WriteContext,\n    getStorageArgs?: Parameters<EntityStore[\"getStorage\"]>\n  ): T | Reference {\n    if (mergeTree.map.size && !isReference(incoming)) {\n      const e: StoreObject | Reference | undefined =\n        // Items in the same position in different arrays are not\n        // necessarily related to each other, so when incoming is an array\n        // we process its elements as if there was no existing data.\n        (\n          !isArray(incoming) &&\n          // Likewise, existing must be either a Reference or a StoreObject\n          // in order for its fields to be safe to merge with the fields of\n          // the incoming object.\n          (isReference(existing) || storeValueIsStoreObject(existing))\n        ) ?\n          existing\n        : void 0;\n\n      // This narrowing is implied by mergeTree.map.size > 0 and\n      // !isReference(incoming), though TypeScript understandably cannot\n      // hope to infer this type.\n      const i = incoming as StoreObject | StoreValue[];\n\n      // The options.storage objects provided to read and merge functions\n      // are derived from the identity of the parent object plus a\n      // sequence of storeFieldName strings/numbers identifying the nested\n      // field name path of each field value to be merged.\n      if (e && !getStorageArgs) {\n        getStorageArgs = [isReference(e) ? e.__ref : e];\n      }\n\n      // It's possible that applying merge functions to this subtree will\n      // not change the incoming data, so this variable tracks the fields\n      // that did change, so we can create a new incoming object when (and\n      // only when) at least one incoming field has changed. We use a Map\n      // to preserve the type of numeric keys.\n      let changedFields: Map<string | number, StoreValue> | undefined;\n\n      const getValue = (\n        from: typeof e | typeof i,\n        name: string | number\n      ): StoreValue => {\n        return (\n          isArray(from) ?\n            typeof name === \"number\" ?\n              from[name]\n            : void 0\n          : context.store.getFieldValue(from, String(name))\n        );\n      };\n\n      mergeTree.map.forEach((childTree, storeFieldName) => {\n        const eVal = getValue(e, storeFieldName);\n        const iVal = getValue(i, storeFieldName);\n        // If we have no incoming data, leave any existing data untouched.\n        if (void 0 === iVal) return;\n        if (getStorageArgs) {\n          getStorageArgs.push(storeFieldName);\n        }\n        const aVal = this.applyMerges(\n          childTree,\n          eVal,\n          iVal,\n          context,\n          getStorageArgs\n        );\n        if (aVal !== iVal) {\n          changedFields = changedFields || new Map();\n          changedFields.set(storeFieldName, aVal);\n        }\n        if (getStorageArgs) {\n          invariant(getStorageArgs.pop() === storeFieldName);\n        }\n      });\n\n      if (changedFields) {\n        // Shallow clone i so we can add changed fields to it.\n        incoming = (isArray(i) ? i.slice(0) : { ...i }) as T;\n        changedFields.forEach((value, name) => {\n          (incoming as any)[name] = value;\n        });\n      }\n    }\n\n    if (mergeTree.info) {\n      return this.cache.policies.runMergeFunction(\n        existing,\n        incoming,\n        mergeTree.info,\n        context,\n        getStorageArgs && context.store.getStorage(...getStorageArgs)\n      );\n    }\n\n    return incoming;\n  }\n}\n\nconst emptyMergeTreePool: MergeTree[] = [];\n\nfunction getChildMergeTree(\n  { map }: MergeTree,\n  name: string | number\n): MergeTree {\n  if (!map.has(name)) {\n    map.set(name, emptyMergeTreePool.pop() || { map: new Map() });\n  }\n  return map.get(name)!;\n}\n\nfunction mergeMergeTrees(\n  left: MergeTree | undefined,\n  right: MergeTree | undefined\n): MergeTree {\n  if (left === right || !right || mergeTreeIsEmpty(right)) return left!;\n  if (!left || mergeTreeIsEmpty(left)) return right;\n\n  const info =\n    left.info && right.info ?\n      {\n        ...left.info,\n        ...right.info,\n      }\n    : left.info || right.info;\n\n  const needToMergeMaps = left.map.size && right.map.size;\n  const map =\n    needToMergeMaps ? new Map()\n    : left.map.size ? left.map\n    : right.map;\n\n  const merged = { info, map };\n\n  if (needToMergeMaps) {\n    const remainingRightKeys = new Set(right.map.keys());\n\n    left.map.forEach((leftTree, key) => {\n      merged.map.set(key, mergeMergeTrees(leftTree, right.map.get(key)));\n      remainingRightKeys.delete(key);\n    });\n\n    remainingRightKeys.forEach((key) => {\n      merged.map.set(\n        key,\n        mergeMergeTrees(right.map.get(key), left.map.get(key))\n      );\n    });\n  }\n\n  return merged;\n}\n\nfunction mergeTreeIsEmpty(tree: MergeTree | undefined): boolean {\n  return !tree || !(tree.info || tree.map.size);\n}\n\nfunction maybeRecycleChildMergeTree({ map }: MergeTree, name: string | number) {\n  const childTree = map.get(name);\n  if (childTree && mergeTreeIsEmpty(childTree)) {\n    emptyMergeTreePool.push(childTree);\n    map.delete(name);\n  }\n}\n\nconst warnings = new Set<string>();\n\n// Note that this function is unused in production, and thus should be\n// pruned by any well-configured minifier.\nfunction warnAboutDataLoss(\n  existingRef: Reference,\n  incomingObj: StoreObject,\n  storeFieldName: string,\n  store: NormalizedCache\n) {\n  const getChild = (objOrRef: StoreObject | Reference): StoreObject | false => {\n    const child = store.getFieldValue<StoreObject>(objOrRef, storeFieldName);\n    return typeof child === \"object\" && child;\n  };\n\n  const existing = getChild(existingRef);\n  if (!existing) return;\n\n  const incoming = getChild(incomingObj);\n  if (!incoming) return;\n\n  // It's always safe to replace a reference, since it refers to data\n  // safely stored elsewhere.\n  if (isReference(existing)) return;\n\n  // If the values are structurally equivalent, we do not need to worry\n  // about incoming replacing existing.\n  if (equal(existing, incoming)) return;\n\n  // If we're replacing every key of the existing object, then the\n  // existing data would be overwritten even if the objects were\n  // normalized, so warning would not be helpful here.\n  if (\n    Object.keys(existing).every(\n      (key) => store.getFieldValue(incoming, key) !== void 0\n    )\n  ) {\n    return;\n  }\n\n  const parentType =\n    store.getFieldValue<string>(existingRef, \"__typename\") ||\n    store.getFieldValue<string>(incomingObj, \"__typename\");\n  const fieldName = fieldNameFromStoreName(storeFieldName);\n  const typeDotName = `${parentType}.${fieldName}`;\n  // Avoid warning more than once for the same type and field name.\n  if (warnings.has(typeDotName)) return;\n  warnings.add(typeDotName);\n\n  const childTypenames: string[] = [];\n  // Arrays do not have __typename fields, and always need a custom merge\n  // function, even if their elements are normalized entities.\n  if (!isArray(existing) && !isArray(incoming)) {\n    [existing, incoming].forEach((child) => {\n      const typename = store.getFieldValue(child, \"__typename\");\n      if (typeof typename === \"string\" && !childTypenames.includes(typename)) {\n        childTypenames.push(typename);\n      }\n    });\n  }\n\n  invariant.warn(\n    `Cache data may be lost when replacing the %s field of a %s object.\n\nThis could cause additional (usually avoidable) network requests to fetch data that were otherwise cached.\n\nTo address this problem (which is not a bug in Apollo Client), %sdefine a custom merge function for the %s field, so InMemoryCache can safely merge these objects:\n\n  existing: %o\n  incoming: %o\n\nFor more information about these options, please refer to the documentation:\n\n  * Ensuring entity objects have IDs: https://go.apollo.dev/c/generating-unique-identifiers\n  * Defining custom merge functions: https://go.apollo.dev/c/merging-non-normalized-objects\n`,\n    fieldName,\n    parentType,\n    childTypenames.length ?\n      \"either ensure all objects of type \" +\n        childTypenames.join(\" and \") +\n        \" have an ID or a custom merge function, or \"\n    : \"\",\n    typeDotName,\n    { ...existing },\n    { ...incoming }\n  );\n}\n"]}