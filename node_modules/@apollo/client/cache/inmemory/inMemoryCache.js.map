{"version": 3, "file": "inMemoryCache.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/inMemoryCache.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,kCAAkC,CAAC;AAE7D,0EAA0E;AAC1E,OAAO,mBAAmB,CAAC;AAQ3B,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAChC,OAAO,EAAE,KAAK,EAAE,MAAM,eAAe,CAAC;AAEtC,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAE/C,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAE5D,OAAO,EACL,qBAAqB,EACrB,WAAW,EACX,iBAAiB,EACjB,kBAAkB,EAClB,KAAK,EACL,UAAU,GAEX,MAAM,0BAA0B,CAAC;AAElC,OAAO,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACjD,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,EAAE,WAAW,EAAE,qBAAqB,EAAE,MAAM,kBAAkB,CAAC;AACtE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AACtE,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,qBAAqB,EAAE,MAAM,cAAc,CAAC;AAE9E,OAAO,EAAE,+BAA+B,EAAE,MAAM,+CAA+C,CAAC;AAOhG;IAAmC,iCAAkC;IA6BnE,uBAAY,MAAgC;QAAhC,uBAAA,EAAA,WAAgC;QAC1C,YAAA,MAAK,WAAE,SAAC;QAzBF,aAAO,GAAG,IAAI,GAAG,EAAsB,CAAC;QAKxC,0BAAoB,GAAG,IAAI,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;QAQ5E,4EAA4E;QAC5E,2EAA2E;QAC3D,4BAAsB,GAAG,IAAI,CAAC;QAO9B,aAAO,GAAG,OAAO,CAAC;QA4V1B,aAAO,GAAG,CAAC,CAAC;QAxVlB,KAAI,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QACtC,KAAI,CAAC,WAAW,GAAG,CAAC,CAAC,KAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QAE7C,KAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC;YAC3B,KAAK,EAAE,KAAI;YACX,gBAAgB,EAAE,KAAI,CAAC,MAAM,CAAC,gBAAgB;YAC9C,aAAa,EAAE,KAAI,CAAC,MAAM,CAAC,aAAa;YACxC,YAAY,EAAE,KAAI,CAAC,MAAM,CAAC,YAAY;SACvC,CAAC,CAAC;QAEH,KAAI,CAAC,IAAI,EAAE,CAAC;;IACd,CAAC;IAEO,4BAAI,GAAZ;QACE,4EAA4E;QAC5E,yEAAyE;QACzE,sDAAsD;QACtD,IAAM,SAAS,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC;YAClD,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;SACzC,CAAC,CAAC,CAAC;QAEJ,2EAA2E;QAC3E,2EAA2E;QAC3E,uEAAuE;QACvE,oEAAoE;QACpE,mCAAmC;QACnC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC;QAEtC,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC1B,CAAC;IAEO,wCAAgB,GAAxB,UAAyB,qBAA+B;QAAxD,iBA4DC;QA3DC,IAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;QAChC,IAAA,SAAS,GAAK,IAAI,CAAC,MAAM,UAAhB,CAAiB;QAElC,uEAAuE;QACvE,4EAA4E;QAC5E,sDAAsD;QACtD,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAChC,IAAI,EACJ,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC;YAClC,KAAK,EAAE,IAAI;YACX,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAClD,eAAe,EAAE,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC;YACnD,KAAK,EACH,qBAAqB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAC/B,cAAc,IAAI,cAAc,CAAC,KAAK,CACvC;YACH,SAAS,WAAA;SACV,CAAC,CAAC,EACH,SAAS,CACV,CAAC;QAEF,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAC7B,UAAC,CAAqB,EAAE,OAA0B;YAChD,OAAO,KAAI,CAAC,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;QACzC,CAAC,EACD;YACE,GAAG,EACD,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBAC9B,UAAU,CAAC,mCAAmC,CAAC;iFACO;YACxD,YAAY,EAAE,UAAC,CAAqB;gBAClC,kEAAkE;gBAClE,kEAAkE;gBAClE,IAAM,KAAK,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,KAAI,CAAC,cAAc,CAAC,CAAC,CAAC,KAAI,CAAC,IAAI,CAAC;gBAC7D,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzB,IAAA,UAAU,GAAoB,CAAC,WAArB,EAAE,EAAE,GAAgB,CAAC,GAAjB,EAAE,SAAS,GAAK,CAAC,UAAN,CAAO;oBACxC,OAAO,KAAK,CAAC,YAAY,CACvB,CAAC,CAAC,KAAK;oBACP,wDAAwD;oBACxD,4DAA4D;oBAC5D,6DAA6D;oBAC7D,0DAA0D;oBAC1D,2DAA2D;oBAC3D,8CAA8C;oBAC9C,CAAC,CAAC,QAAQ,EACV,kBAAkB,CAAC,EAAE,UAAU,YAAA,EAAE,EAAE,IAAA,EAAE,SAAS,WAAA,EAAE,CAAC,CAClD,CAAC;gBACJ,CAAC;YACH,CAAC;SACF,CACF,CAAC;QAEF,wEAAwE;QACxE,0EAA0E;QAC1E,yCAAyC;QACzC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,UAAC,KAAK;YAClE,OAAA,KAAK,CAAC,YAAY,EAAE;QAApB,CAAoB,CACrB,CAAC;IACJ,CAAC;IAEM,+BAAO,GAAd,UAAe,IAA2B;QACxC,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,4EAA4E;QAC5E,0EAA0E;QAC1E,mCAAmC;QACnC,IAAI,IAAI;YAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,+BAAO,GAAd,UAAe,UAA2B;QAA3B,2BAAA,EAAA,kBAA2B;QACxC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,CAAC;IAClE,CAAC;IAEM,4BAAI,GAAX,UAAe,OAA0B;QASrC;QAPA,mEAAmE;QACnE,gEAAgE;QAChE,kEAAkE;QAClE,mEAAmE;QACnE,mEAAmE;QACnE,4DAA4D;QAC5D,wBAAwB;QACxB,KACE,OAAO,kBADgB;QAPzB,mEAAmE;QACnE,gEAAgE;QAChE,kEAAkE;QAClE,mEAAmE;QACnE,mEAAmE;QACnE,4DAA4D;QAC5D,wBAAwB;QACxB,iBAAiB,mBAAG,KAAK,KAAA,CACf;QACZ,IAAI,CAAC;YACH,OAAO,CACL,IAAI,CAAC,WAAW,CAAC,qBAAqB,uBACjC,OAAO,KACV,KAAK,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAC3D,MAAM,EAAE,IAAI,CAAC,MAAM,EACnB,iBAAiB,mBAAA,IACjB,CAAC,MAAM,IAAI,IAAI,CAClB,CAAC;QACJ,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,IAAI,CAAC,YAAY,iBAAiB,EAAE,CAAC;gBACnC,uEAAuE;gBACvE,qEAAqE;gBACrE,oEAAoE;gBACpE,uEAAuE;gBACvE,kCAAkC;gBAClC,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,CAAC,CAAC;QACV,CAAC;IACH,CAAC;IAEM,6BAAK,GAAZ,UAAa,OAA2B;QACtC,IAAI,CAAC;YACH,EAAE,IAAI,CAAC,OAAO,CAAC;YACf,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3D,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;gBACnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEM,8BAAM,GAAb,UACE,OAAoC;QAEpC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;YAC9C,kEAAkE;YAClE,kEAAkE;YAClE,mEAAmE;YACnE,kEAAkE;YAClE,6DAA6D;YAC7D,oEAAoE;YACpE,kEAAkE;YAClE,mEAAmE;YACnE,6BAA6B;YAC7B,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAM,KAAK,GACT,CACE,OAAO,CAAC,UAAU,CAAC,qBAAqB;SACzC,CAAC,CAAC;YACD,IAAI,CAAC,cAAc;YACrB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QACd,IAAI,CAAC;YACH,EAAE,IAAI,CAAC,OAAO,CAAC;YACf,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;QAClE,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;gBACnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEM,4BAAI,GAAX,UACE,OAA6C;QAE7C,OAAO,IAAI,CAAC,WAAW,CAAC,qBAAqB,uBACxC,OAAO,KACV,KAAK,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAC3D,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,YAAY,EAClC,MAAM,EAAE,IAAI,CAAC,MAAM,IACnB,CAAC;IACL,CAAC;IAEM,6BAAK,GAAZ,UACE,KAA4C;QAD9C,iBAgCC;QA7BC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACvB,yDAAyD;YACzD,oEAAoE;YACpE,oEAAoE;YACpE,gEAAgE;YAChE,mEAAmE;YACnE,mEAAmE;YACnE,gEAAgE;YAChE,+DAA+D;YAC/D,kEAAkE;YAClE,2DAA2D;YAC3D,WAAW,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACxB,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;QACD,OAAO;YACL,0EAA0E;YAC1E,yEAAyE;YACzE,uDAAuD;YACvD,IAAI,KAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBACrD,WAAW,CAAC,KAAI,CAAC,CAAC;YACpB,CAAC;YACD,sDAAsD;YACtD,mEAAmE;YACnE,iDAAiD;YACjD,KAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC;IACJ,CAAC;IAEM,0BAAE,GAAT,UAAU,OAQT;;QACC,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAC3B,KAAK,CAAC,KAAK,EAAE,CAAC;QACd,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC;QACvC,MAAA,IAAI,CAAC,MAAM,CAAC,SAAS,0CAAE,WAAW,EAAE,CAAC;QACrC,IAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC;QACrC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAC7B,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;YACvD,CAAC;iBAAM,IAAI,OAAO,CAAC,qBAAqB,EAAE,CAAC;gBACzC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;YAChC,CAAC;QACH,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAED,0EAA0E;IAC1E,0EAA0E;IAC1E,6EAA6E;IAC7E,0EAA0E;IAC1E,2EAA2E;IAC3E,uEAAuE;IACvE,6CAA6C;IACtC,8BAAM,GAAb,UAAc,MAAc,EAAE,UAAoB;QAChD,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACvE,CAAC;IAED,4EAA4E;IAC5E,2EAA2E;IAC3E,4EAA4E;IAC5E,6EAA6E;IAC7E,gCAAgC;IACzB,+BAAO,GAAd,UAAe,MAAc,EAAE,UAAoB;QACjD,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;IACxE,CAAC;IAED,yEAAyE;IACzE,2EAA2E;IAC3E,2EAA2E;IAC3E,2EAA2E;IAC3E,0EAA0E;IAC1E,wEAAwE;IACjE,gCAAQ,GAAf,UAAgB,MAA+B;QAC7C,IAAI,WAAW,CAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC,KAAK,CAAC;QAC7C,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAEM,6BAAK,GAAZ,UAAa,OAA2B;QACtC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;YAChB,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;gBAC/B,8DAA8D;gBAC9D,4CAA4C;gBAC5C,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,yBAAQ,OAAO,KAAE,EAAE,EAAE,YAAY,GAAE,CAAC;QAC7C,CAAC;QACD,IAAI,CAAC;YACH,iEAAiE;YACjE,oEAAoE;YACpE,iEAAiE;YACjE,4BAA4B;YAC5B,EAAE,IAAI,CAAC,OAAO,CAAC;YACf,uEAAuE;YACvE,wEAAwE;YACxE,6DAA6D;YAC7D,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE,CAAC;gBACnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,CAAC;QACH,CAAC;IACH,CAAC;IAEM,6BAAK,GAAZ,UAAa,OAA4B;QAAzC,iBAsBC;QArBC,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,kBAAkB,CAAC,KAAK,EAAE,CAAC;QAE3B,IAAI,OAAO,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YACtC,kEAAkE;YAClE,+CAA+C;YAC/C,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,KAAK,IAAK,OAAA,KAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAtC,CAAsC,CAAC,CAAC;YACxE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACrB,WAAW,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;aAAM,CAAC;YACN,yEAAyE;YACzE,wEAAwE;YACxE,0EAA0E;YAC1E,qEAAqE;YACrE,yEAAyE;YACzE,4DAA4D;YAC5D,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAEM,wCAAgB,GAAvB,UAAwB,UAAkB;QACxC,IAAM,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QACtE,IAAI,iBAAiB,KAAK,IAAI,CAAC,cAAc,EAAE,CAAC;YAC9C,IAAI,CAAC,cAAc,GAAG,iBAAiB,CAAC;YACxC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,CAAC;IACH,CAAC;IAIM,6BAAK,GAAZ,UACE,OAAyD;QAD3D,iBAmGC;QA/FG,IAAA,MAAM,GAIJ,OAAO,OAJH,EACN,KAGE,OAAO,WAHQ,EAAjB,UAAU,mBAAG,IAAI,KAAA,EACjB,gBAAgB,GAEd,OAAO,iBAFO,EAChB,cAAc,GACZ,OAAO,eADK,CACJ;QAEZ,IAAI,YAA2B,CAAC;QAChC,IAAM,OAAO,GAAG,UAAC,KAAmB;YAC5B,IAAA,KAA2B,KAAI,EAA7B,IAAI,UAAA,EAAE,cAAc,oBAAS,CAAC;YACtC,EAAE,KAAI,CAAC,OAAO,CAAC;YACf,IAAI,KAAK,EAAE,CAAC;gBACV,KAAI,CAAC,IAAI,GAAG,KAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC1C,CAAC;YACD,IAAI,CAAC;gBACH,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,KAAI,CAAC,CAAC,CAAC;YACvC,CAAC;oBAAS,CAAC;gBACT,EAAE,KAAI,CAAC,OAAO,CAAC;gBACf,KAAI,CAAC,IAAI,GAAG,IAAI,CAAC;gBACjB,KAAI,CAAC,cAAc,GAAG,cAAc,CAAC;YACvC,CAAC;QACH,CAAC,CAAC;QAEF,IAAM,YAAY,GAAG,IAAI,GAAG,EAAsB,CAAC;QAEnD,IAAI,cAAc,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACpC,wEAAwE;YACxE,uEAAuE;YACvE,yEAAyE;YACzE,0EAA0E;YAC1E,qEAAqE;YACrE,uEAAuE;YACvE,gEAAgE;YAChE,0EAA0E;YAC1E,kCAAkC;YAClC,IAAI,CAAC,gBAAgB,uBAChB,OAAO,KACV,cAAc,YAAC,KAAK;oBAClB,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBACxB,OAAO,KAAK,CAAC;gBACf,CAAC,IACD,CAAC;QACL,CAAC;QAED,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;YACnC,sEAAsE;YACtE,uEAAuE;YACvE,+DAA+D;YAC/D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC1E,CAAC;aAAM,IAAI,UAAU,KAAK,KAAK,EAAE,CAAC;YAChC,kEAAkE;YAClE,mEAAmE;YACnE,yEAAyE;YACzE,oEAAoE;YACpE,YAAY;YACZ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,uEAAuE;YACvE,sCAAsC;YACtC,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE,CAAC;YACzC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;QAC1E,CAAC;QAED,wEAAwE;QACxE,wEAAwE;QACxE,sCAAsC;QACtC,IAAI,cAAc,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;YACxC,IAAI,CAAC,gBAAgB,uBAChB,OAAO,KACV,cAAc,YAAC,KAAK,EAAE,IAAI;oBACxB,IAAM,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;oBACtD,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;wBACrB,0DAA0D;wBAC1D,4DAA4D;wBAC5D,4DAA4D;wBAC5D,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;oBAC7B,CAAC;oBACD,OAAO,MAAM,CAAC;gBAChB,CAAC,IACD,CAAC;YACH,0EAA0E;YAC1E,kDAAkD;YAClD,IAAI,YAAY,CAAC,IAAI,EAAE,CAAC;gBACtB,YAAY,CAAC,OAAO,CAAC,UAAC,KAAK,IAAK,OAAA,KAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK,CAAC,EAArC,CAAqC,CAAC,CAAC;YACzE,CAAC;QACH,CAAC;aAAM,CAAC;YACN,8DAA8D;YAC9D,2DAA2D;YAC3D,0BAA0B;YAC1B,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,YAAa,CAAC;IACvB,CAAC;IAEM,0CAAkB,GAAzB,UACE,MAAqC,EACrC,YAA4B;QAE5B,OAAO,IAAI,CAAC,KAAK,CAAC;YAChB,MAAM,QAAA;YACN,UAAU,EAAE,YAAY,IAAI,YAAY,KAAK,IAAI;SAClD,CAAC,CAAC;IACL,CAAC;IAEM,yCAAiB,GAAxB,UAAyB,QAAsB;QAC7C,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;IAC3E,CAAC;IAEM,uCAAe,GAAtB,UACE,QAA4B,EAC5B,QAAgB;QAEhB,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC3D,CAAC;IAEM,sCAAc,GAArB,UAAsB,YAAoB;;QACxC,OAAO,CAAA,MAAA,IAAI,CAAC,MAAM,CAAC,SAAS,0CAAE,MAAM,CAAC,YAAY,CAAC,KAAI,IAAI,CAAC;IAC7D,CAAC;IAES,wCAAgB,GAA1B,UAA2B,OAA0B;QAArD,iBAIC;QAHC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAC,CAAC,IAAK,OAAA,KAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,OAAO,CAAC,EAApC,CAAoC,CAAC,CAAC;QACpE,CAAC;IACH,CAAC;IAEO,8CAAsB,GAA9B,UAA+B,QAAsB;QAC3C,IAAA,SAAS,GAAK,IAAI,CAAC,MAAM,UAAhB,CAAiB;QAClC,OAAO,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAC9D,CAAC;IAEO,6CAAqB,GAA7B,UAA8B,QAAsB;QAClD,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,oEAAoE;IACpE,uEAAuE;IACvE,mEAAmE;IACnE,qEAAqE;IACrE,qEAAqE;IACrE,sDAAsD;IAC9C,sCAAc,GAAtB,UAAuB,CAAqB,EAAE,OAA0B;QAC9D,IAAA,QAAQ,GAAK,CAAC,SAAN,CAAO;QAEvB,wEAAwE;QACxE,kEAAkE;QAClE,wEAAwE;QACxE,0EAA0E;QAC1E,wEAAwE;QACxE,2DAA2D;QAC3D,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAM,CAAC,CAAC,CAAC;QAE/B,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,CAAC,UAAU,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;gBAC3D,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;YACxC,CAAC;YAED,IACE,OAAO,CAAC,cAAc;gBACtB,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK,KAAK,EAC9D,CAAC;gBACD,gEAAgE;gBAChE,6CAA6C;gBAC7C,OAAO;YACT,CAAC;QACH,CAAC;QAED,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;YACtD,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAUH,oBAAC;AAAD,CAAC,AAtjBD,CAAmC,WAAW,GAsjB7C;;AAED,IAAI,OAAO,EAAE,CAAC;IACZ,aAAa,CAAC,SAAS,CAAC,kBAAkB,GAAG,+BAA+B,CAAC;AAC/E,CAAC", "sourcesContent": ["import { invariant } from \"../../utilities/globals/index.js\";\n\n// Make builtins like Map and Set safe to use with non-extensible objects.\nimport \"./fixPolyfills.js\";\n\nimport type {\n  DocumentNode,\n  FragmentDefinitionNode,\n  InlineFragmentNode,\n} from \"graphql\";\nimport type { OptimisticWrapperFunction } from \"optimism\";\nimport { wrap } from \"optimism\";\nimport { equal } from \"@wry/equality\";\n\nimport { ApolloCache } from \"../core/cache.js\";\nimport type { Cache } from \"../core/types/Cache.js\";\nimport { MissingFieldError } from \"../core/types/common.js\";\nimport type { StoreObject, Reference } from \"../../utilities/index.js\";\nimport {\n  addTypenameToDocument,\n  isReference,\n  DocumentTransform,\n  canonicalStringify,\n  print,\n  cacheSizes,\n  defaultCacheSizes,\n} from \"../../utilities/index.js\";\nimport type { InMemoryCacheConfig, NormalizedCacheObject } from \"./types.js\";\nimport { StoreReader } from \"./readFromStore.js\";\nimport { StoreWriter } from \"./writeToStore.js\";\nimport { EntityStore, supportsResultCaching } from \"./entityStore.js\";\nimport { makeVar, forgetCache, recallCache } from \"./reactiveVars.js\";\nimport { Policies } from \"./policies.js\";\nimport { hasOwn, normalizeConfig, shouldCanonizeResults } from \"./helpers.js\";\nimport type { OperationVariables } from \"../../core/index.js\";\nimport { getInMemoryCacheMemoryInternals } from \"../../utilities/caching/getMemoryInternals.js\";\n\ntype BroadcastOptions = Pick<\n  Cache.BatchOptions<InMemoryCache>,\n  \"optimistic\" | \"onWatchUpdated\"\n>;\n\nexport class InMemoryCache extends ApolloCache<NormalizedCacheObject> {\n  private data!: EntityStore;\n  private optimisticData!: EntityStore;\n\n  protected config: InMemoryCacheConfig;\n  private watches = new Set<Cache.WatchOptions>();\n  private addTypename: boolean;\n\n  private storeReader!: StoreReader;\n  private storeWriter!: StoreWriter;\n  private addTypenameTransform = new DocumentTransform(addTypenameToDocument);\n\n  private maybeBroadcastWatch!: OptimisticWrapperFunction<\n    [Cache.WatchOptions, BroadcastOptions?],\n    any,\n    [Cache.WatchOptions]\n  >;\n\n  // Override the default value, since InMemoryCache result objects are frozen\n  // in development and expected to remain logically immutable in production.\n  public readonly assumeImmutableResults = true;\n\n  // Dynamically imported code can augment existing typePolicies or\n  // possibleTypes by calling cache.policies.addTypePolicies or\n  // cache.policies.addPossibletypes.\n  public readonly policies: Policies;\n\n  public readonly makeVar = makeVar;\n\n  constructor(config: InMemoryCacheConfig = {}) {\n    super();\n    this.config = normalizeConfig(config);\n    this.addTypename = !!this.config.addTypename;\n\n    this.policies = new Policies({\n      cache: this,\n      dataIdFromObject: this.config.dataIdFromObject,\n      possibleTypes: this.config.possibleTypes,\n      typePolicies: this.config.typePolicies,\n    });\n\n    this.init();\n  }\n\n  private init() {\n    // Passing { resultCaching: false } in the InMemoryCache constructor options\n    // will completely disable dependency tracking, which will improve memory\n    // usage but worsen the performance of repeated reads.\n    const rootStore = (this.data = new EntityStore.Root({\n      policies: this.policies,\n      resultCaching: this.config.resultCaching,\n    }));\n\n    // When no optimistic writes are currently active, cache.optimisticData ===\n    // cache.data, so there are no additional layers on top of the actual data.\n    // When an optimistic update happens, this.optimisticData will become a\n    // linked list of EntityStore Layer objects that terminates with the\n    // original this.data cache object.\n    this.optimisticData = rootStore.stump;\n\n    this.resetResultCache();\n  }\n\n  private resetResultCache(resetResultIdentities?: boolean) {\n    const previousReader = this.storeReader;\n    const { fragments } = this.config;\n\n    // The StoreWriter is mostly stateless and so doesn't really need to be\n    // reset, but it does need to have its writer.storeReader reference updated,\n    // so it's simpler to update this.storeWriter as well.\n    this.storeWriter = new StoreWriter(\n      this,\n      (this.storeReader = new StoreReader({\n        cache: this,\n        addTypename: this.addTypename,\n        resultCacheMaxSize: this.config.resultCacheMaxSize,\n        canonizeResults: shouldCanonizeResults(this.config),\n        canon:\n          resetResultIdentities ? void 0 : (\n            previousReader && previousReader.canon\n          ),\n        fragments,\n      })),\n      fragments\n    );\n\n    this.maybeBroadcastWatch = wrap(\n      (c: Cache.WatchOptions, options?: BroadcastOptions) => {\n        return this.broadcastWatch(c, options);\n      },\n      {\n        max:\n          this.config.resultCacheMaxSize ||\n          cacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] ||\n          defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"],\n        makeCacheKey: (c: Cache.WatchOptions) => {\n          // Return a cache key (thus enabling result caching) only if we're\n          // currently using a data store that can track cache dependencies.\n          const store = c.optimistic ? this.optimisticData : this.data;\n          if (supportsResultCaching(store)) {\n            const { optimistic, id, variables } = c;\n            return store.makeCacheKey(\n              c.query,\n              // Different watches can have the same query, optimistic\n              // status, rootId, and variables, but if their callbacks are\n              // different, the (identical) result needs to be delivered to\n              // each distinct callback. The easiest way to achieve that\n              // separation is to include c.callback in the cache key for\n              // maybeBroadcastWatch calls. See issue #5733.\n              c.callback,\n              canonicalStringify({ optimistic, id, variables })\n            );\n          }\n        },\n      }\n    );\n\n    // Since we have thrown away all the cached functions that depend on the\n    // CacheGroup dependencies maintained by EntityStore, we should also reset\n    // all CacheGroup dependency information.\n    new Set([this.data.group, this.optimisticData.group]).forEach((group) =>\n      group.resetCaching()\n    );\n  }\n\n  public restore(data: NormalizedCacheObject): this {\n    this.init();\n    // Since calling this.init() discards/replaces the entire StoreReader, along\n    // with the result caches it maintains, this.data.replace(data) won't have\n    // to bother deleting the old data.\n    if (data) this.data.replace(data);\n    return this;\n  }\n\n  public extract(optimistic: boolean = false): NormalizedCacheObject {\n    return (optimistic ? this.optimisticData : this.data).extract();\n  }\n\n  public read<T>(options: Cache.ReadOptions): T | null {\n    const {\n      // Since read returns data or null, without any additional metadata\n      // about whether/where there might have been missing fields, the\n      // default behavior cannot be returnPartialData = true (like it is\n      // for the diff method), since defaulting to true would violate the\n      // integrity of the T in the return type. However, partial data may\n      // be useful in some cases, so returnPartialData:true may be\n      // specified explicitly.\n      returnPartialData = false,\n    } = options;\n    try {\n      return (\n        this.storeReader.diffQueryAgainstStore<T>({\n          ...options,\n          store: options.optimistic ? this.optimisticData : this.data,\n          config: this.config,\n          returnPartialData,\n        }).result || null\n      );\n    } catch (e) {\n      if (e instanceof MissingFieldError) {\n        // Swallow MissingFieldError and return null, so callers do not need to\n        // worry about catching \"normal\" exceptions resulting from incomplete\n        // cache data. Unexpected errors will be re-thrown. If you need more\n        // information about which fields were missing, use cache.diff instead,\n        // and examine diffResult.missing.\n        return null;\n      }\n      throw e;\n    }\n  }\n\n  public write(options: Cache.WriteOptions): Reference | undefined {\n    try {\n      ++this.txCount;\n      return this.storeWriter.writeToStore(this.data, options);\n    } finally {\n      if (!--this.txCount && options.broadcast !== false) {\n        this.broadcastWatches();\n      }\n    }\n  }\n\n  public modify<Entity extends Record<string, any> = Record<string, any>>(\n    options: Cache.ModifyOptions<Entity>\n  ): boolean {\n    if (hasOwn.call(options, \"id\") && !options.id) {\n      // To my knowledge, TypeScript does not currently provide a way to\n      // enforce that an optional property?:type must *not* be undefined\n      // when present. That ability would be useful here, because we want\n      // options.id to default to ROOT_QUERY only when no options.id was\n      // provided. If the caller attempts to pass options.id with a\n      // falsy/undefined value (perhaps because cache.identify failed), we\n      // should not assume the goal was to modify the ROOT_QUERY object.\n      // We could throw, but it seems natural to return false to indicate\n      // that nothing was modified.\n      return false;\n    }\n    const store =\n      (\n        options.optimistic // Defaults to false.\n      ) ?\n        this.optimisticData\n      : this.data;\n    try {\n      ++this.txCount;\n      return store.modify(options.id || \"ROOT_QUERY\", options.fields);\n    } finally {\n      if (!--this.txCount && options.broadcast !== false) {\n        this.broadcastWatches();\n      }\n    }\n  }\n\n  public diff<TData, TVariables extends OperationVariables = any>(\n    options: Cache.DiffOptions<TData, TVariables>\n  ): Cache.DiffResult<TData> {\n    return this.storeReader.diffQueryAgainstStore({\n      ...options,\n      store: options.optimistic ? this.optimisticData : this.data,\n      rootId: options.id || \"ROOT_QUERY\",\n      config: this.config,\n    });\n  }\n\n  public watch<TData = any, TVariables = any>(\n    watch: Cache.WatchOptions<TData, TVariables>\n  ): () => void {\n    if (!this.watches.size) {\n      // In case we previously called forgetCache(this) because\n      // this.watches became empty (see below), reattach this cache to any\n      // reactive variables on which it previously depended. It might seem\n      // paradoxical that we're able to recall something we supposedly\n      // forgot, but the point of calling forgetCache(this) is to silence\n      // useless broadcasts while this.watches is empty, and to allow the\n      // cache to be garbage collected. If, however, we manage to call\n      // recallCache(this) here, this cache object must not have been\n      // garbage collected yet, and should resume receiving updates from\n      // reactive variables, now that it has a watcher to notify.\n      recallCache(this);\n    }\n    this.watches.add(watch);\n    if (watch.immediate) {\n      this.maybeBroadcastWatch(watch);\n    }\n    return () => {\n      // Once we remove the last watch from this.watches, cache.broadcastWatches\n      // no longer does anything, so we preemptively tell the reactive variable\n      // system to exclude this cache from future broadcasts.\n      if (this.watches.delete(watch) && !this.watches.size) {\n        forgetCache(this);\n      }\n      // Remove this watch from the LRU cache managed by the\n      // maybeBroadcastWatch OptimisticWrapperFunction, to prevent memory\n      // leaks involving the closure of watch.callback.\n      this.maybeBroadcastWatch.forget(watch);\n    };\n  }\n\n  public gc(options?: {\n    // If true, also free non-essential result cache memory by bulk-releasing\n    // this.{store{Reader,Writer},maybeBroadcastWatch}. Defaults to false.\n    resetResultCache?: boolean;\n    // If resetResultCache is true, this.storeReader.canon will be preserved by\n    // default, but can also be discarded by passing resetResultIdentities:true.\n    // Defaults to false.\n    resetResultIdentities?: boolean;\n  }) {\n    canonicalStringify.reset();\n    print.reset();\n    this.addTypenameTransform.resetCache();\n    this.config.fragments?.resetCaches();\n    const ids = this.optimisticData.gc();\n    if (options && !this.txCount) {\n      if (options.resetResultCache) {\n        this.resetResultCache(options.resetResultIdentities);\n      } else if (options.resetResultIdentities) {\n        this.storeReader.resetCanon();\n      }\n    }\n    return ids;\n  }\n\n  // Call this method to ensure the given root ID remains in the cache after\n  // garbage collection, along with its transitive child entities. Note that\n  // the cache automatically retains all directly written entities. By default,\n  // the retainment persists after optimistic updates are removed. Pass true\n  // for the optimistic argument if you would prefer for the retainment to be\n  // discarded when the top-most optimistic layer is removed. Returns the\n  // resulting (non-negative) retainment count.\n  public retain(rootId: string, optimistic?: boolean): number {\n    return (optimistic ? this.optimisticData : this.data).retain(rootId);\n  }\n\n  // Call this method to undo the effect of the retain method, above. Once the\n  // retainment count falls to zero, the given ID will no longer be preserved\n  // during garbage collection, though it may still be preserved by other safe\n  // entities that refer to it. Returns the resulting (non-negative) retainment\n  // count, in case that's useful.\n  public release(rootId: string, optimistic?: boolean): number {\n    return (optimistic ? this.optimisticData : this.data).release(rootId);\n  }\n\n  // Returns the canonical ID for a given StoreObject, obeying typePolicies\n  // and keyFields (and dataIdFromObject, if you still use that). At minimum,\n  // the object must contain a __typename and any primary key fields required\n  // to identify entities of that type. If you pass a query result object, be\n  // sure that none of the primary key fields have been renamed by aliasing.\n  // If you pass a Reference object, its __ref ID string will be returned.\n  public identify(object: StoreObject | Reference): string | undefined {\n    if (isReference(object)) return object.__ref;\n    try {\n      return this.policies.identify(object)[0];\n    } catch (e) {\n      invariant.warn(e);\n    }\n  }\n\n  public evict(options: Cache.EvictOptions): boolean {\n    if (!options.id) {\n      if (hasOwn.call(options, \"id\")) {\n        // See comment in modify method about why we return false when\n        // options.id exists but is falsy/undefined.\n        return false;\n      }\n      options = { ...options, id: \"ROOT_QUERY\" };\n    }\n    try {\n      // It's unlikely that the eviction will end up invoking any other\n      // cache update operations while it's running, but {in,de}crementing\n      // this.txCount still seems like a good idea, for uniformity with\n      // the other update methods.\n      ++this.txCount;\n      // Pass this.data as a limit on the depth of the eviction, so evictions\n      // during optimistic updates (when this.data is temporarily set equal to\n      // this.optimisticData) do not escape their optimistic Layer.\n      return this.optimisticData.evict(options, this.data);\n    } finally {\n      if (!--this.txCount && options.broadcast !== false) {\n        this.broadcastWatches();\n      }\n    }\n  }\n\n  public reset(options?: Cache.ResetOptions): Promise<void> {\n    this.init();\n\n    canonicalStringify.reset();\n\n    if (options && options.discardWatches) {\n      // Similar to what happens in the unsubscribe function returned by\n      // cache.watch, applied to all current watches.\n      this.watches.forEach((watch) => this.maybeBroadcastWatch.forget(watch));\n      this.watches.clear();\n      forgetCache(this);\n    } else {\n      // Calling this.init() above unblocks all maybeBroadcastWatch caching, so\n      // this.broadcastWatches() triggers a broadcast to every current watcher\n      // (letting them know their data is now missing). This default behavior is\n      // convenient because it means the watches do not have to be manually\n      // reestablished after resetting the cache. To prevent this broadcast and\n      // cancel all watches, pass true for options.discardWatches.\n      this.broadcastWatches();\n    }\n\n    return Promise.resolve();\n  }\n\n  public removeOptimistic(idToRemove: string) {\n    const newOptimisticData = this.optimisticData.removeLayer(idToRemove);\n    if (newOptimisticData !== this.optimisticData) {\n      this.optimisticData = newOptimisticData;\n      this.broadcastWatches();\n    }\n  }\n\n  private txCount = 0;\n\n  public batch<TUpdateResult>(\n    options: Cache.BatchOptions<InMemoryCache, TUpdateResult>\n  ): TUpdateResult {\n    const {\n      update,\n      optimistic = true,\n      removeOptimistic,\n      onWatchUpdated,\n    } = options;\n\n    let updateResult: TUpdateResult;\n    const perform = (layer?: EntityStore): TUpdateResult => {\n      const { data, optimisticData } = this;\n      ++this.txCount;\n      if (layer) {\n        this.data = this.optimisticData = layer;\n      }\n      try {\n        return (updateResult = update(this));\n      } finally {\n        --this.txCount;\n        this.data = data;\n        this.optimisticData = optimisticData;\n      }\n    };\n\n    const alreadyDirty = new Set<Cache.WatchOptions>();\n\n    if (onWatchUpdated && !this.txCount) {\n      // If an options.onWatchUpdated callback is provided, we want to call it\n      // with only the Cache.WatchOptions objects affected by options.update,\n      // but there might be dirty watchers already waiting to be broadcast that\n      // have nothing to do with the update. To prevent including those watchers\n      // in the post-update broadcast, we perform this initial broadcast to\n      // collect the dirty watchers, so we can re-dirty them later, after the\n      // post-update broadcast, allowing them to receive their pending\n      // broadcasts the next time broadcastWatches is called, just as they would\n      // if we never called cache.batch.\n      this.broadcastWatches({\n        ...options,\n        onWatchUpdated(watch) {\n          alreadyDirty.add(watch);\n          return false;\n        },\n      });\n    }\n\n    if (typeof optimistic === \"string\") {\n      // Note that there can be multiple layers with the same optimistic ID.\n      // When removeOptimistic(id) is called for that id, all matching layers\n      // will be removed, and the remaining layers will be reapplied.\n      this.optimisticData = this.optimisticData.addLayer(optimistic, perform);\n    } else if (optimistic === false) {\n      // Ensure both this.data and this.optimisticData refer to the root\n      // (non-optimistic) layer of the cache during the update. Note that\n      // this.data could be a Layer if we are currently executing an optimistic\n      // update function, but otherwise will always be an EntityStore.Root\n      // instance.\n      perform(this.data);\n    } else {\n      // Otherwise, leave this.data and this.optimisticData unchanged and run\n      // the update with broadcast batching.\n      perform();\n    }\n\n    if (typeof removeOptimistic === \"string\") {\n      this.optimisticData = this.optimisticData.removeLayer(removeOptimistic);\n    }\n\n    // Note: if this.txCount > 0, then alreadyDirty.size === 0, so this code\n    // takes the else branch and calls this.broadcastWatches(options), which\n    // does nothing when this.txCount > 0.\n    if (onWatchUpdated && alreadyDirty.size) {\n      this.broadcastWatches({\n        ...options,\n        onWatchUpdated(watch, diff) {\n          const result = onWatchUpdated.call(this, watch, diff);\n          if (result !== false) {\n            // Since onWatchUpdated did not return false, this diff is\n            // about to be broadcast to watch.callback, so we don't need\n            // to re-dirty it with the other alreadyDirty watches below.\n            alreadyDirty.delete(watch);\n          }\n          return result;\n        },\n      });\n      // Silently re-dirty any watches that were already dirty before the update\n      // was performed, and were not broadcast just now.\n      if (alreadyDirty.size) {\n        alreadyDirty.forEach((watch) => this.maybeBroadcastWatch.dirty(watch));\n      }\n    } else {\n      // If alreadyDirty is empty or we don't have an onWatchUpdated\n      // function, we don't need to go to the trouble of wrapping\n      // options.onWatchUpdated.\n      this.broadcastWatches(options);\n    }\n\n    return updateResult!;\n  }\n\n  public performTransaction(\n    update: (cache: InMemoryCache) => any,\n    optimisticId?: string | null\n  ) {\n    return this.batch({\n      update,\n      optimistic: optimisticId || optimisticId !== null,\n    });\n  }\n\n  public transformDocument(document: DocumentNode): DocumentNode {\n    return this.addTypenameToDocument(this.addFragmentsToDocument(document));\n  }\n\n  public fragmentMatches(\n    fragment: InlineFragmentNode,\n    typename: string\n  ): boolean {\n    return this.policies.fragmentMatches(fragment, typename);\n  }\n\n  public lookupFragment(fragmentName: string): FragmentDefinitionNode | null {\n    return this.config.fragments?.lookup(fragmentName) || null;\n  }\n\n  protected broadcastWatches(options?: BroadcastOptions) {\n    if (!this.txCount) {\n      this.watches.forEach((c) => this.maybeBroadcastWatch(c, options));\n    }\n  }\n\n  private addFragmentsToDocument(document: DocumentNode) {\n    const { fragments } = this.config;\n    return fragments ? fragments.transform(document) : document;\n  }\n\n  private addTypenameToDocument(document: DocumentNode) {\n    if (this.addTypename) {\n      return this.addTypenameTransform.transformDocument(document);\n    }\n    return document;\n  }\n\n  // This method is wrapped by maybeBroadcastWatch, which is called by\n  // broadcastWatches, so that we compute and broadcast results only when\n  // the data that would be broadcast might have changed. It would be\n  // simpler to check for changes after recomputing a result but before\n  // broadcasting it, but this wrapping approach allows us to skip both\n  // the recomputation and the broadcast, in most cases.\n  private broadcastWatch(c: Cache.WatchOptions, options?: BroadcastOptions) {\n    const { lastDiff } = c;\n\n    // Both WatchOptions and DiffOptions extend ReadOptions, and DiffOptions\n    // currently requires no additional properties, so we can use c (a\n    // WatchOptions object) as DiffOptions, without having to allocate a new\n    // object, and without having to enumerate the relevant properties (query,\n    // variables, etc.) explicitly. There will be some additional properties\n    // (lastDiff, callback, etc.), but cache.diff ignores them.\n    const diff = this.diff<any>(c);\n\n    if (options) {\n      if (c.optimistic && typeof options.optimistic === \"string\") {\n        diff.fromOptimisticTransaction = true;\n      }\n\n      if (\n        options.onWatchUpdated &&\n        options.onWatchUpdated.call(this, c, diff, lastDiff) === false\n      ) {\n        // Returning false from the onWatchUpdated callback will prevent\n        // calling c.callback(diff) for this watcher.\n        return;\n      }\n    }\n\n    if (!lastDiff || !equal(lastDiff.result, diff.result)) {\n      c.callback((c.lastDiff = diff), lastDiff);\n    }\n  }\n\n  /**\n   * @experimental\n   * @internal\n   * This is not a stable API - it is used in development builds to expose\n   * information to the DevTools.\n   * Use at your own risk!\n   */\n  public getMemoryInternals?: typeof getInMemoryCacheMemoryInternals;\n}\n\nif (__DEV__) {\n  InMemoryCache.prototype.getMemoryInternals = getInMemoryCacheMemoryInternals;\n}\n"]}