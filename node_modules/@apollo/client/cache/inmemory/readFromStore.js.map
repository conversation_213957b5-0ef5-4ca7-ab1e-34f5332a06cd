{"version": 3, "file": "readFromStore.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/readFromStore.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AAGhF,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAQhC,OAAO,EACL,OAAO,EACP,sBAAsB,EACtB,WAAW,EACX,aAAa,EACb,aAAa,EACb,qBAAqB,EACrB,gBAAgB,EAChB,iBAAiB,EACjB,kBAAkB,EAClB,wBAAwB,EACxB,eAAe,EACf,cAAc,EACd,UAAU,EACV,eAAe,EACf,aAAa,EACb,OAAO,EACP,kBAAkB,EAClB,UAAU,GAEX,MAAM,0BAA0B,CAAC;AAQlC,OAAO,EACL,8BAA8B,EAC9B,qBAAqB,GACtB,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EACL,OAAO,EACP,sBAAsB,EACtB,0BAA0B,EAC1B,qBAAqB,GACtB,MAAM,cAAc,CAAC;AAItB,OAAO,EAAE,iBAAiB,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAgDhD,SAAS,uBAAuB,CAC9B,OAAgC;IAEhC,OAAO;QACL,OAAO,CAAC,YAAY;QACpB,OAAO,CAAC,iBAAiB;QACzB,OAAO,CAAC,OAAO;QACf,6DAA6D;QAC7D,0DAA0D;QAC1D,OAAO,CAAC,OAAO,CAAC,eAAe;KAChC,CAAC;AACJ,CAAC;AAED;IAiCE,qBAAY,MAAyB;QAArC,iBAwFC;QAlGO,iBAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,EAGvD,CAAC;QAQF,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE;YAC5B,WAAW,EAAE,MAAM,CAAC,WAAW,KAAK,KAAK;YACzC,eAAe,EAAE,qBAAqB,CAAC,MAAM,CAAC;SAC/C,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI,WAAW,EAAE,CAAC;QAE/C,+DAA+D;QAC/D,2CAA2C;QAC3C,oCAAoC;QACpC,oEAAoE;QACpE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAC7B,UAAC,OAAO;;YACE,IAAA,eAAe,GAAK,OAAO,CAAC,OAAO,gBAApB,CAAqB;YAE5C,IAAM,QAAQ,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;YAElD,sEAAsE;YACtE,6CAA6C;YAC7C,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC;YAE/B,IAAM,KAAK,GAAG,CAAA,KAAA,KAAI,CAAC,mBAAmB,CAAA,CAAC,IAAI,WAAI,QAAQ,CAAC,CAAC;YAEzD,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,eAAe,EAAE,CAAC;oBACpB,6BACK,KAAK;wBACR,kEAAkE;wBAClE,iDAAiD;wBACjD,MAAM,EAAE,KAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,IACtC;gBACJ,CAAC;gBACD,sEAAsE;gBACtE,sCAAsC;gBACtC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,8BAA8B,CAC5B,OAAO,CAAC,OAAO,CAAC,KAAK,EACrB,OAAO,CAAC,YAAY,CAAC,KAAK,CAC3B,CAAC;YAEF,uEAAuE;YACvE,sDAAsD;YACtD,OAAO,KAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC,EACD;YACE,GAAG,EACD,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBAC9B,UAAU,CAAC,mCAAmC,CAAC;kFACO;YACxD,OAAO,EAAE,uBAAuB;YAChC,iEAAiE;YACjE,6BAA6B;YAC7B,YAAY,YAAC,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe;gBACzD,IAAI,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzC,OAAO,OAAO,CAAC,KAAK,CAAC,YAAY,CAC/B,YAAY,EACZ,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAC3C,OAAO,CAAC,SAAS,EACjB,eAAe,CAChB,CAAC;gBACJ,CAAC;YACH,CAAC;SACF,CACF,CAAC;QAEF,IAAI,CAAC,uBAAuB,GAAG,IAAI,CACjC,UAAC,OAAoC;YACnC,8BAA8B,CAC5B,OAAO,CAAC,OAAO,CAAC,KAAK,EACrB,OAAO,CAAC,YAAY,CAAC,KAAK,CAC3B,CAAC;YACF,OAAO,KAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC,EACD;YACE,GAAG,EACD,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBAC9B,UAAU,CAAC,uCAAuC,CAAC;sFACO;YAC5D,YAAY,YAAC,EAAyB;oBAAvB,KAAK,WAAA,EAAE,KAAK,WAAA,EAAE,OAAO,aAAA;gBAClC,IAAI,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzC,OAAO,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;SACF,CACF,CAAC;IACJ,CAAC;IA5FM,gCAAU,GAAjB;QACE,IAAI,CAAC,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;IACjC,CAAC;IA4FD;;;OAGG;IACI,2CAAqB,GAA5B,UAAgC,EAOD;YAN7B,KAAK,WAAA,EACL,KAAK,WAAA,EACL,cAAqB,EAArB,MAAM,mBAAG,YAAY,KAAA,EACrB,SAAS,eAAA,EACT,yBAAwB,EAAxB,iBAAiB,mBAAG,IAAI,KAAA,EACxB,uBAA6C,EAA7C,eAAe,mBAAG,IAAI,CAAC,MAAM,CAAC,eAAe,KAAA;QAE7C,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;QAE5C,SAAS,yBACJ,gBAAgB,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,GAC3C,SAAU,CACd,CAAC;QAEF,IAAM,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QACtC,IAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC;YAC1C,YAAY,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC,YAAY;YACnD,iBAAiB,EAAE,OAAO;YAC1B,YAAY,EAAE,OAAO;YACrB,OAAO,aACL,KAAK,OAAA,EACL,KAAK,OAAA,EACL,QAAQ,UAAA,EACR,SAAS,WAAA,EACT,SAAS,EAAE,kBAAkB,CAAC,SAAS,CAAC,EACxC,eAAe,iBAAA,IACZ,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CACxD;SACF,CAAC,CAAC;QAEH,IAAI,OAAwC,CAAC;QAC7C,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,0DAA0D;YAC1D,yEAAyE;YACzE,qEAAqE;YACrE,2CAA2C;YAC3C,OAAO,GAAG;gBACR,IAAI,iBAAiB,CACnB,YAAY,CAAC,UAAU,CAAC,OAAO,CAAE,EACjC,UAAU,CAAC,OAAO,EAClB,KAAK,EACL,SAAS,CACV;aACF,CAAC;YACF,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACvB,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC;YACnB,CAAC;QACH,CAAC;QAED,OAAO;YACL,MAAM,EAAE,UAAU,CAAC,MAAM;YACzB,QAAQ,EAAE,CAAC,OAAO;YAClB,OAAO,SAAA;SACR,CAAC;IACJ,CAAC;IAEM,6BAAO,GAAd,UACE,MAA2B,EAC3B,MAA+B,EAC/B,YAA8B,EAC9B,OAA+B;QAE/B,IACE,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC;YACpC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,YAAY,EAC9C,CAAC;YACD,IAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAC1C,YAAY,EACZ,MAAM,EACN,OAAO;YACP,kEAAkE;YAClE,qEAAqE;YACrE,0CAA0C;YAC1C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAC3B,CAAC;YACF,IAAI,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,2CAA2C;IACnC,0CAAoB,GAA5B,UAA6B,EAKH;QAL1B,iBAmJC;YAlJC,YAAY,kBAAA,EACZ,iBAAiB,uBAAA,EACjB,YAAY,kBAAA,EACZ,OAAO,aAAA;QAEP,IACE,WAAW,CAAC,iBAAiB,CAAC;YAC9B,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,KAAK,CAAC;YAC5D,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAC3C,CAAC;YACD,OAAO;gBACL,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;gBACxB,OAAO,EAAE,wCAAiC,iBAAiB,CAAC,KAAK,YAAS;aAC3E,CAAC;QACJ,CAAC;QAEO,IAAA,SAAS,GAAsB,OAAO,UAA7B,EAAE,QAAQ,GAAY,OAAO,SAAnB,EAAE,KAAK,GAAK,OAAO,MAAZ,CAAa;QAC/C,IAAM,QAAQ,GAAG,KAAK,CAAC,aAAa,CAClC,iBAAiB,EACjB,YAAY,CACb,CAAC;QAEF,IAAM,cAAc,GAA0B,EAAE,CAAC;QACjD,IAAI,OAAgC,CAAC;QACrC,IAAM,aAAa,GAAG,IAAI,UAAU,EAAE,CAAC;QAEvC,IACE,IAAI,CAAC,MAAM,CAAC,WAAW;YACvB,OAAO,QAAQ,KAAK,QAAQ;YAC5B,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EACrC,CAAC;YACD,8DAA8D;YAC9D,mEAAmE;YACnE,6DAA6D;YAC7D,cAAc,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;QAChD,CAAC;QAED,SAAS,aAAa,CAAI,MAAqB,EAAE,UAAkB;;YACjE,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,OAAO;oBACnC,GAAC,UAAU,IAAG,MAAM,CAAC,OAAO;wBAC5B,CAAC;YACL,CAAC;YACD,OAAO,MAAM,CAAC,MAAM,CAAC;QACvB,CAAC;QAED,IAAM,OAAO,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAEjD,OAAO,CAAC,OAAO,CAAC,UAAC,SAAS;;YACxB,2DAA2D;YAC3D,+BAA+B;YAC/B,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC;gBAAE,OAAO;YAEjD,IAAI,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACvB,IAAI,UAAU,GAAG,QAAQ,CAAC,SAAS,CACjC;oBACE,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK;oBAC/B,KAAK,EAAE,SAAS;oBAChB,SAAS,EAAE,OAAO,CAAC,SAAS;oBAC5B,IAAI,EAAE,iBAAiB;iBACxB,EACD,OAAO,CACR,CAAC;gBAEF,IAAM,UAAU,GAAG,sBAAsB,CAAC,SAAS,CAAC,CAAC;gBAErD,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,CAAC;oBAC1B,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;wBAC5C,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,OAAO;4BACnC,GAAC,UAAU,IAAG,4BAAqB,SAAS,CAAC,IAAI,CAAC,KAAK,kBACrD,WAAW,CAAC,iBAAiB,CAAC,CAAC,CAAC;gCAC9B,iBAAiB,CAAC,KAAK,GAAG,SAAS;gCACrC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,CACxD;gCACF,CAAC;oBACL,CAAC;gBACH,CAAC;qBAAM,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC/B,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC1B,UAAU,GAAG,aAAa,CACxB,KAAI,CAAC,uBAAuB,CAAC;4BAC3B,KAAK,EAAE,SAAS;4BAChB,KAAK,EAAE,UAAU;4BACjB,YAAY,cAAA;4BACZ,OAAO,SAAA;yBACR,CAAC,EACF,UAAU,CACX,CAAC;oBACJ,CAAC;gBACH,CAAC;qBAAM,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC;oBACnC,gEAAgE;oBAChE,4DAA4D;oBAC5D,6DAA6D;oBAC7D,6DAA6D;oBAC7D,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;wBAC5B,UAAU,GAAG,KAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBAC3C,CAAC;gBACH,CAAC;qBAAM,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;oBAC9B,+DAA+D;oBAC/D,+DAA+D;oBAC/D,8BAA8B;oBAC9B,UAAU,GAAG,aAAa,CACxB,KAAI,CAAC,mBAAmB,CAAC;wBACvB,YAAY,EAAE,SAAS,CAAC,YAAY;wBACpC,iBAAiB,EAAE,UAAqC;wBACxD,YAAY,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY;wBACjE,OAAO,SAAA;qBACR,CAAC,EACF,UAAU,CACX,CAAC;gBACJ,CAAC;gBAED,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,CAAC;oBAC1B,cAAc,CAAC,IAAI,WAAG,GAAC,UAAU,IAAG,UAAU,MAAG,CAAC;gBACpD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAM,QAAQ,GAAG,wBAAwB,CACvC,SAAS,EACT,OAAO,CAAC,cAAc,CACvB,CAAC;gBAEF,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe,EAAE,CAAC;oBACzD,MAAM,iBAAiB,CAAC,sBAAsB,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACxE,CAAC;gBAED,IAAI,QAAQ,IAAI,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;oBAC7D,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAM,MAAM,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;QAC9C,IAAM,WAAW,GAAe,EAAE,MAAM,QAAA,EAAE,OAAO,SAAA,EAAE,CAAC;QACpD,IAAM,MAAM,GACV,OAAO,CAAC,eAAe,CAAC,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;YAC7B,yEAAyE;YACzE,kEAAkE;YACpE,CAAC,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAEjC,kEAAkE;QAClE,wDAAwD;QACxD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,+CAA+C;IACvC,8CAAwB,GAAhC,UAAiC,EAKH;QAL9B,iBA+DC;YA9DC,KAAK,WAAA,EACL,KAAK,WAAA,EACL,YAAY,kBAAA,EACZ,OAAO,aAAA;QAEP,IAAI,OAAgC,CAAC;QACrC,IAAI,aAAa,GAAG,IAAI,UAAU,EAAiB,CAAC;QAEpD,SAAS,aAAa,CAAI,WAA0B,EAAE,CAAS;;YAC7D,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;gBACxB,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,OAAO,YAAI,GAAC,CAAC,IAAG,WAAW,CAAC,OAAO,MAAG,CAAC;YACvE,CAAC;YACD,OAAO,WAAW,CAAC,MAAM,CAAC;QAC5B,CAAC;QAED,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;YACvB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9C,CAAC;QAED,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,UAAC,IAAI,EAAE,CAAC;YACxB,sBAAsB;YACtB,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC;YACd,CAAC;YAED,kCAAkC;YAClC,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClB,OAAO,aAAa,CAClB,KAAI,CAAC,uBAAuB,CAAC;oBAC3B,KAAK,OAAA;oBACL,KAAK,EAAE,IAAI;oBACX,YAAY,cAAA;oBACZ,OAAO,SAAA;iBACR,CAAC,EACF,CAAC,CACF,CAAC;YACJ,CAAC;YAED,iDAAiD;YACjD,IAAI,KAAK,CAAC,YAAY,EAAE,CAAC;gBACvB,OAAO,aAAa,CAClB,KAAI,CAAC,mBAAmB,CAAC;oBACvB,YAAY,EAAE,KAAK,CAAC,YAAY;oBAChC,iBAAiB,EAAE,IAAI;oBACvB,YAAY,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;oBACrD,OAAO,SAAA;iBACR,CAAC,EACF,CAAC,CACF,CAAC;YACJ,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACZ,4BAA4B,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,MAAM,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK;YACjE,OAAO,SAAA;SACR,CAAC;IACJ,CAAC;IACH,kBAAC;AAAD,CAAC,AAzaD,IAyaC;;AAED,SAAS,YAAY,CAAC,IAAiB;IACrC,IAAI,CAAC;QACH,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,UAAC,CAAC,EAAE,KAAK;YAC5B,IAAI,OAAO,KAAK,KAAK,QAAQ;gBAAE,MAAM,KAAK,CAAC;YAC3C,OAAO,KAAK,CAAC;QACf,CAAC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,MAAM,EAAE,CAAC;QAChB,OAAO,MAAgB,CAAC;IAC1B,CAAC;AACH,CAAC;AAED,SAAS,4BAA4B,CACnC,KAAsB,EACtB,KAAgB,EAChB,UAAe;IAEf,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;QACxB,IAAM,SAAO,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;QACtC,SAAO,CAAC,OAAO,CAAC,UAAC,KAAK;YACpB,IAAI,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3B,SAAS,CACP,CAAC,WAAW,CAAC,KAAK,CAAC,EACnB,yEAAyE,EACzE,0BAA0B,CAAC,KAAK,EAAE,KAAK,CAAC,EACxC,KAAK,CAAC,IAAI,CAAC,KAAK,CACjB,CAAC;gBACF,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,SAAO,CAAC,GAAG,EAAE,SAAO,CAAC,CAAC;YACrD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC", "sourcesContent": ["import { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\n\nimport type { DocumentNode, FieldNode, SelectionSetNode } from \"graphql\";\nimport { Kind } from \"graphql\";\nimport type { OptimisticWrapperFunction } from \"optimism\";\nimport { wrap } from \"optimism\";\n\nimport type {\n  Reference,\n  StoreObject,\n  FragmentMap,\n  FragmentMapFunction,\n} from \"../../utilities/index.js\";\nimport {\n  isField,\n  resultKeyNameFromField,\n  isReference,\n  makeReference,\n  shouldInclude,\n  addTypenameToDocument,\n  getDefaultValues,\n  getMainDefinition,\n  getQueryDefinition,\n  getFragmentFromSelection,\n  maybeDeepFreeze,\n  mergeDeepArray,\n  DeepMerger,\n  isNonNullObject,\n  canUseWeakMap,\n  compact,\n  canonicalStringify,\n  cacheSizes,\n  defaultCacheSizes,\n} from \"../../utilities/index.js\";\nimport type { Cache } from \"../core/types/Cache.js\";\nimport type {\n  DiffQueryAgainstStoreOptions,\n  InMemoryCacheConfig,\n  NormalizedCache,\n  ReadMergeModifyContext,\n} from \"./types.js\";\nimport {\n  maybeDependOnExistenceOfEntity,\n  supportsResultCaching,\n} from \"./entityStore.js\";\nimport {\n  isArray,\n  extractFragmentContext,\n  getTypenameFromStoreObject,\n  shouldCanonizeResults,\n} from \"./helpers.js\";\nimport type { Policies } from \"./policies.js\";\nimport type { InMemoryCache } from \"./inMemoryCache.js\";\nimport type { MissingTree } from \"../core/types/common.js\";\nimport { MissingFieldError } from \"../core/types/common.js\";\nimport { ObjectCanon } from \"./object-canon.js\";\n\nexport type VariableMap = { [name: string]: any };\n\ninterface ReadContext extends ReadMergeModifyContext {\n  query: DocumentNode;\n  policies: Policies;\n  canonizeResults: boolean;\n  fragmentMap: FragmentMap;\n  lookupFragment: FragmentMapFunction;\n}\n\nexport type ExecResult<R = any> = {\n  result: R;\n  missing?: MissingTree;\n};\n\ntype ExecSelectionSetOptions = {\n  selectionSet: SelectionSetNode;\n  objectOrReference: StoreObject | Reference;\n  enclosingRef: Reference;\n  context: ReadContext;\n};\n\ntype ExecSubSelectedArrayOptions = {\n  field: FieldNode;\n  array: readonly any[];\n  enclosingRef: Reference;\n  context: ReadContext;\n};\n\nexport interface StoreReaderConfig {\n  cache: InMemoryCache;\n  addTypename?: boolean;\n  resultCacheMaxSize?: number;\n  canonizeResults?: boolean;\n  canon?: ObjectCanon;\n  fragments?: InMemoryCacheConfig[\"fragments\"];\n}\n\n// Arguments type after keyArgs translation.\ntype ExecSelectionSetKeyArgs = [\n  SelectionSetNode,\n  StoreObject | Reference,\n  ReadMergeModifyContext,\n  boolean,\n];\n\nfunction execSelectionSetKeyArgs(\n  options: ExecSelectionSetOptions\n): ExecSelectionSetKeyArgs {\n  return [\n    options.selectionSet,\n    options.objectOrReference,\n    options.context,\n    // We split out this property so we can pass different values\n    // independently without modifying options.context itself.\n    options.context.canonizeResults,\n  ];\n}\n\nexport class StoreReader {\n  // cached version of executeSelectionSet\n  private executeSelectionSet: OptimisticWrapperFunction<\n    [ExecSelectionSetOptions], // Actual arguments tuple type.\n    ExecResult, // Actual return type.\n    ExecSelectionSetKeyArgs\n  >;\n\n  // cached version of executeSubSelectedArray\n  private executeSubSelectedArray: OptimisticWrapperFunction<\n    [ExecSubSelectedArrayOptions],\n    ExecResult<any>,\n    [ExecSubSelectedArrayOptions]\n  >;\n\n  private config: {\n    cache: InMemoryCache;\n    addTypename: boolean;\n    resultCacheMaxSize?: number;\n    canonizeResults: boolean;\n    fragments?: InMemoryCacheConfig[\"fragments\"];\n  };\n\n  private knownResults = new (canUseWeakMap ? WeakMap : Map)<\n    Record<string, any>,\n    SelectionSetNode\n  >();\n\n  public canon: ObjectCanon;\n  public resetCanon() {\n    this.canon = new ObjectCanon();\n  }\n\n  constructor(config: StoreReaderConfig) {\n    this.config = compact(config, {\n      addTypename: config.addTypename !== false,\n      canonizeResults: shouldCanonizeResults(config),\n    });\n\n    this.canon = config.canon || new ObjectCanon();\n\n    // memoized functions in this class will be \"garbage-collected\"\n    // by recreating the whole `StoreReader` in\n    // `InMemoryCache.resetResultsCache`\n    // (triggered from `InMemoryCache.gc` with `resetResultCache: true`)\n    this.executeSelectionSet = wrap(\n      (options) => {\n        const { canonizeResults } = options.context;\n\n        const peekArgs = execSelectionSetKeyArgs(options);\n\n        // Negate this boolean option so we can find out if we've already read\n        // this result using the other boolean value.\n        peekArgs[3] = !canonizeResults;\n\n        const other = this.executeSelectionSet.peek(...peekArgs);\n\n        if (other) {\n          if (canonizeResults) {\n            return {\n              ...other,\n              // If we previously read this result without canonizing it, we can\n              // reuse that result simply by canonizing it now.\n              result: this.canon.admit(other.result),\n            };\n          }\n          // If we previously read this result with canonization enabled, we can\n          // return that canonized result as-is.\n          return other;\n        }\n\n        maybeDependOnExistenceOfEntity(\n          options.context.store,\n          options.enclosingRef.__ref\n        );\n\n        // Finally, if we didn't find any useful previous results, run the real\n        // execSelectionSetImpl method with the given options.\n        return this.execSelectionSetImpl(options);\n      },\n      {\n        max:\n          this.config.resultCacheMaxSize ||\n          cacheSizes[\"inMemoryCache.executeSelectionSet\"] ||\n          defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"],\n        keyArgs: execSelectionSetKeyArgs,\n        // Note that the parameters of makeCacheKey are determined by the\n        // array returned by keyArgs.\n        makeCacheKey(selectionSet, parent, context, canonizeResults) {\n          if (supportsResultCaching(context.store)) {\n            return context.store.makeCacheKey(\n              selectionSet,\n              isReference(parent) ? parent.__ref : parent,\n              context.varString,\n              canonizeResults\n            );\n          }\n        },\n      }\n    );\n\n    this.executeSubSelectedArray = wrap(\n      (options: ExecSubSelectedArrayOptions) => {\n        maybeDependOnExistenceOfEntity(\n          options.context.store,\n          options.enclosingRef.__ref\n        );\n        return this.execSubSelectedArrayImpl(options);\n      },\n      {\n        max:\n          this.config.resultCacheMaxSize ||\n          cacheSizes[\"inMemoryCache.executeSubSelectedArray\"] ||\n          defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"],\n        makeCacheKey({ field, array, context }) {\n          if (supportsResultCaching(context.store)) {\n            return context.store.makeCacheKey(field, array, context.varString);\n          }\n        },\n      }\n    );\n  }\n\n  /**\n   * Given a store and a query, return as much of the result as possible and\n   * identify if any data was missing from the store.\n   */\n  public diffQueryAgainstStore<T>({\n    store,\n    query,\n    rootId = \"ROOT_QUERY\",\n    variables,\n    returnPartialData = true,\n    canonizeResults = this.config.canonizeResults,\n  }: DiffQueryAgainstStoreOptions): Cache.DiffResult<T> {\n    const policies = this.config.cache.policies;\n\n    variables = {\n      ...getDefaultValues(getQueryDefinition(query)),\n      ...variables!,\n    };\n\n    const rootRef = makeReference(rootId);\n    const execResult = this.executeSelectionSet({\n      selectionSet: getMainDefinition(query).selectionSet,\n      objectOrReference: rootRef,\n      enclosingRef: rootRef,\n      context: {\n        store,\n        query,\n        policies,\n        variables,\n        varString: canonicalStringify(variables),\n        canonizeResults,\n        ...extractFragmentContext(query, this.config.fragments),\n      },\n    });\n\n    let missing: MissingFieldError[] | undefined;\n    if (execResult.missing) {\n      // For backwards compatibility we still report an array of\n      // MissingFieldError objects, even though there will only ever be at most\n      // one of them, now that all missing field error messages are grouped\n      // together in the execResult.missing tree.\n      missing = [\n        new MissingFieldError(\n          firstMissing(execResult.missing)!,\n          execResult.missing,\n          query,\n          variables\n        ),\n      ];\n      if (!returnPartialData) {\n        throw missing[0];\n      }\n    }\n\n    return {\n      result: execResult.result,\n      complete: !missing,\n      missing,\n    };\n  }\n\n  public isFresh(\n    result: Record<string, any>,\n    parent: StoreObject | Reference,\n    selectionSet: SelectionSetNode,\n    context: ReadMergeModifyContext\n  ): boolean {\n    if (\n      supportsResultCaching(context.store) &&\n      this.knownResults.get(result) === selectionSet\n    ) {\n      const latest = this.executeSelectionSet.peek(\n        selectionSet,\n        parent,\n        context,\n        // If result is canonical, then it could only have been previously\n        // cached by the canonizing version of executeSelectionSet, so we can\n        // avoid checking both possibilities here.\n        this.canon.isKnown(result)\n      );\n      if (latest && result === latest.result) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  // Uncached version of executeSelectionSet.\n  private execSelectionSetImpl({\n    selectionSet,\n    objectOrReference,\n    enclosingRef,\n    context,\n  }: ExecSelectionSetOptions): ExecResult {\n    if (\n      isReference(objectOrReference) &&\n      !context.policies.rootTypenamesById[objectOrReference.__ref] &&\n      !context.store.has(objectOrReference.__ref)\n    ) {\n      return {\n        result: this.canon.empty,\n        missing: `Dangling reference to missing ${objectOrReference.__ref} object`,\n      };\n    }\n\n    const { variables, policies, store } = context;\n    const typename = store.getFieldValue<string>(\n      objectOrReference,\n      \"__typename\"\n    );\n\n    const objectsToMerge: Record<string, any>[] = [];\n    let missing: MissingTree | undefined;\n    const missingMerger = new DeepMerger();\n\n    if (\n      this.config.addTypename &&\n      typeof typename === \"string\" &&\n      !policies.rootIdsByTypename[typename]\n    ) {\n      // Ensure we always include a default value for the __typename\n      // field, if we have one, and this.config.addTypename is true. Note\n      // that this field can be overridden by other merged objects.\n      objectsToMerge.push({ __typename: typename });\n    }\n\n    function handleMissing<T>(result: ExecResult<T>, resultName: string): T {\n      if (result.missing) {\n        missing = missingMerger.merge(missing, {\n          [resultName]: result.missing,\n        });\n      }\n      return result.result;\n    }\n\n    const workSet = new Set(selectionSet.selections);\n\n    workSet.forEach((selection) => {\n      // Omit fields with directives @skip(if: <truthy value>) or\n      // @include(if: <falsy value>).\n      if (!shouldInclude(selection, variables)) return;\n\n      if (isField(selection)) {\n        let fieldValue = policies.readField(\n          {\n            fieldName: selection.name.value,\n            field: selection,\n            variables: context.variables,\n            from: objectOrReference,\n          },\n          context\n        );\n\n        const resultName = resultKeyNameFromField(selection);\n\n        if (fieldValue === void 0) {\n          if (!addTypenameToDocument.added(selection)) {\n            missing = missingMerger.merge(missing, {\n              [resultName]: `Can't find field '${selection.name.value}' on ${\n                isReference(objectOrReference) ?\n                  objectOrReference.__ref + \" object\"\n                : \"object \" + JSON.stringify(objectOrReference, null, 2)\n              }`,\n            });\n          }\n        } else if (isArray(fieldValue)) {\n          if (fieldValue.length > 0) {\n            fieldValue = handleMissing(\n              this.executeSubSelectedArray({\n                field: selection,\n                array: fieldValue,\n                enclosingRef,\n                context,\n              }),\n              resultName\n            );\n          }\n        } else if (!selection.selectionSet) {\n          // If the field does not have a selection set, then we handle it\n          // as a scalar value. To keep this.canon from canonicalizing\n          // this value, we use this.canon.pass to wrap fieldValue in a\n          // Pass object that this.canon.admit will later unwrap as-is.\n          if (context.canonizeResults) {\n            fieldValue = this.canon.pass(fieldValue);\n          }\n        } else if (fieldValue != null) {\n          // In this case, because we know the field has a selection set,\n          // it must be trying to query a GraphQLObjectType, which is why\n          // fieldValue must be != null.\n          fieldValue = handleMissing(\n            this.executeSelectionSet({\n              selectionSet: selection.selectionSet,\n              objectOrReference: fieldValue as StoreObject | Reference,\n              enclosingRef: isReference(fieldValue) ? fieldValue : enclosingRef,\n              context,\n            }),\n            resultName\n          );\n        }\n\n        if (fieldValue !== void 0) {\n          objectsToMerge.push({ [resultName]: fieldValue });\n        }\n      } else {\n        const fragment = getFragmentFromSelection(\n          selection,\n          context.lookupFragment\n        );\n\n        if (!fragment && selection.kind === Kind.FRAGMENT_SPREAD) {\n          throw newInvariantError(`No fragment named %s`, selection.name.value);\n        }\n\n        if (fragment && policies.fragmentMatches(fragment, typename)) {\n          fragment.selectionSet.selections.forEach(workSet.add, workSet);\n        }\n      }\n    });\n\n    const result = mergeDeepArray(objectsToMerge);\n    const finalResult: ExecResult = { result, missing };\n    const frozen =\n      context.canonizeResults ?\n        this.canon.admit(finalResult)\n        // Since this.canon is normally responsible for freezing results (only in\n        // development), freeze them manually if canonization is disabled.\n      : maybeDeepFreeze(finalResult);\n\n    // Store this result with its selection set so that we can quickly\n    // recognize it again in the StoreReader#isFresh method.\n    if (frozen.result) {\n      this.knownResults.set(frozen.result, selectionSet);\n    }\n\n    return frozen;\n  }\n\n  // Uncached version of executeSubSelectedArray.\n  private execSubSelectedArrayImpl({\n    field,\n    array,\n    enclosingRef,\n    context,\n  }: ExecSubSelectedArrayOptions): ExecResult {\n    let missing: MissingTree | undefined;\n    let missingMerger = new DeepMerger<MissingTree[]>();\n\n    function handleMissing<T>(childResult: ExecResult<T>, i: number): T {\n      if (childResult.missing) {\n        missing = missingMerger.merge(missing, { [i]: childResult.missing });\n      }\n      return childResult.result;\n    }\n\n    if (field.selectionSet) {\n      array = array.filter(context.store.canRead);\n    }\n\n    array = array.map((item, i) => {\n      // null value in array\n      if (item === null) {\n        return null;\n      }\n\n      // This is a nested array, recurse\n      if (isArray(item)) {\n        return handleMissing(\n          this.executeSubSelectedArray({\n            field,\n            array: item,\n            enclosingRef,\n            context,\n          }),\n          i\n        );\n      }\n\n      // This is an object, run the selection set on it\n      if (field.selectionSet) {\n        return handleMissing(\n          this.executeSelectionSet({\n            selectionSet: field.selectionSet,\n            objectOrReference: item,\n            enclosingRef: isReference(item) ? item : enclosingRef,\n            context,\n          }),\n          i\n        );\n      }\n\n      if (__DEV__) {\n        assertSelectionSetForIdValue(context.store, field, item);\n      }\n\n      return item;\n    });\n\n    return {\n      result: context.canonizeResults ? this.canon.admit(array) : array,\n      missing,\n    };\n  }\n}\n\nfunction firstMissing(tree: MissingTree): string | undefined {\n  try {\n    JSON.stringify(tree, (_, value) => {\n      if (typeof value === \"string\") throw value;\n      return value;\n    });\n  } catch (result) {\n    return result as string;\n  }\n}\n\nfunction assertSelectionSetForIdValue(\n  store: NormalizedCache,\n  field: FieldNode,\n  fieldValue: any\n) {\n  if (!field.selectionSet) {\n    const workSet = new Set([fieldValue]);\n    workSet.forEach((value) => {\n      if (isNonNullObject(value)) {\n        invariant(\n          !isReference(value),\n          `Missing selection set for object of type %s returned for query field %s`,\n          getTypenameFromStoreObject(store, value),\n          field.name.value\n        );\n        Object.values(value).forEach(workSet.add, workSet);\n      }\n    });\n  }\n}\n"]}