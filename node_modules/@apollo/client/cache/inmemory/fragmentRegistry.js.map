{"version": 3, "file": "fragmentRegistry.js", "sourceRoot": "", "sources": ["../../../src/cache/inmemory/fragmentRegistry.ts"], "names": [], "mappings": ";AAMA,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAEhC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAGhC,OAAO,EACL,UAAU,EAEV,sBAAsB,GACvB,MAAM,0BAA0B,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AASxC,iEAAiE;AACjE,+EAA+E;AAC/E,wEAAwE;AACxE,6EAA6E;AAC7E,gFAAgF;AAChF,8BAA8B;AAC9B,MAAM,UAAU,sBAAsB;IACpC,mBAA4B;SAA5B,UAA4B,EAA5B,qBAA4B,EAA5B,IAA4B;QAA5B,8BAA4B;;IAE5B,YAAW,gBAAgB,YAAhB,gBAAgB,0BAAI,SAAS,aAAE;AAC5C,CAAC;AAED;IAGE,wDAAwD;IACxD,2EAA2E;IAC3E,gDAAgD;IAChD;QAAY,mBAA4B;aAA5B,UAA4B,EAA5B,qBAA4B,EAA5B,IAA4B;YAA5B,8BAA4B;;QALhC,aAAQ,GAAgB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAMlD,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;YACrB,IAAI,CAAC,QAAQ,OAAb,IAAI,EAAa,SAAS,EAAE;QAC9B,CAAC;IACH,CAAC;IAEM,mCAAQ,GAAf;QAAA,iBAgBC;QAhBe,mBAA4B;aAA5B,UAA4B,EAA5B,qBAA4B,EAA5B,IAA4B;YAA5B,8BAA4B;;QAC1C,IAAM,WAAW,GAAG,IAAI,GAAG,EAAkC,CAAC;QAC9D,SAAS,CAAC,OAAO,CAAC,UAAC,GAAiB;YAClC,sBAAsB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAC,IAAI;gBACvC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,WAAW,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,IAAI;YAC7B,IAAI,IAAI,KAAK,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjC,KAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;gBAC3B,KAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACxB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,8CAA8C;IACtC,qCAAU,GAAlB,UAAmB,IAAY,IAAG,CAAC;IAE5B,sCAAW,GAAlB;QACE,IAAM,KAAK,GAAG,gBAAgB,CAAC,SAAS,CAAC;QACzC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAC7D,YAAY,EAAE,UAAC,GAAG,IAAK,OAAA,GAAG,EAAH,CAAG;YAC1B,GAAG,EACD,UAAU,CAAC,yBAAyB,CAAC;uEACO;SAC/C,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,6DAA6D;QACxE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAChD,KAAK,EAAE,SAAS;YAChB,GAAG,EACD,UAAU,CAAC,4BAA4B,CAAC;0EACO;SAClD,CAAC,CAAC;QACH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACpE,KAAK,EAAE,SAAS;YAChB,GAAG,EACD,UAAU,CAAC,sCAAsC,CAAC;oFACO;SAC5D,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACI,iCAAM,GAAb,UAAc,YAAoB;QAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;IAC7C,CAAC;IAEM,oCAAS,GAAhB,UAAyC,QAAW;QAApD,iBAsDC;QArDC,IAAM,OAAO,GAAG,IAAI,GAAG,EAAkC,CAAC;QAC1D,sBAAsB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAC,GAAG;YAC3C,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;QAEH,IAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;QAClC,IAAM,OAAO,GAAG,UAAC,UAAkB;YACjC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC7B,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAC;QAEF,IAAM,mBAAmB,GAAG,UAAC,IAAa;YACxC,OAAA,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;QAA5D,CAA4D,CAAC;QAE/D,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAE9B,IAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAM,GAAG,GAAgB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE7C,mEAAmE;QACnE,yCAAyC;QACzC,OAAO,CAAC,OAAO,CAAC,UAAC,YAAY;YAC3B,IAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACnD,IAAI,gBAAgB,EAAE,CAAC;gBACrB,mBAAmB,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC3B,IAAM,GAAG,GAAG,KAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;gBACtC,IAAI,GAAG,EAAE,CAAC;oBACR,mBAAmB,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;gBACjD,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,IAAM,cAAY,GAA6B,EAAE,CAAC;YAClD,OAAO,CAAC,OAAO,CAAC,UAAC,IAAI;gBACnB,IAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;gBACtB,IAAI,GAAG,EAAE,CAAC;oBACR,cAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,IAAI,cAAY,CAAC,MAAM,EAAE,CAAC;gBACxB,QAAQ,yBACH,QAAQ,KACX,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,cAAY,CAAC,GACvD,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,8CAAmB,GAA1B,UAA2B,IAAa;QACtC,IAAM,OAAO,GAAsB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEvD,KAAK,CAAC,IAAI,EAAE;YACV,cAAc,YAAC,IAAI;gBACjB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;YAClC,CAAC;SACF,CAAC,CAAC;QAEH,OAAO,OAAO,CAAC;IACjB,CAAC;IACH,uBAAC;AAAD,CAAC,AApID,IAoIC", "sourcesContent": ["import type {\n  DocumentNode,\n  ASTNode,\n  FragmentDefinitionNode,\n  FragmentSpreadNode,\n} from \"graphql\";\nimport { visit } from \"graphql\";\n\nimport { wrap } from \"optimism\";\n\nimport type { FragmentMap } from \"../../utilities/index.js\";\nimport {\n  cacheSizes,\n  defaultCacheSizes,\n  getFragmentDefinitions,\n} from \"../../utilities/index.js\";\nimport { WeakCache } from \"@wry/caches\";\n\nexport interface FragmentRegistryAPI {\n  register(...fragments: DocumentNode[]): this;\n  lookup(fragmentName: string): FragmentDefinitionNode | null;\n  transform<D extends DocumentNode>(document: D): D;\n  resetCaches(): void;\n}\n\n// As long as createFragmentRegistry is not imported or used, the\n// FragmentRegistry example implementation provided below should not be bundled\n// (by tree-shaking bundlers like Rollup), because the implementation of\n// InMemoryCache refers only to the TypeScript interface FragmentRegistryAPI,\n// never the concrete implementation FragmentRegistry (which is deliberately not\n// exported from this module).\nexport function createFragmentRegistry(\n  ...fragments: DocumentNode[]\n): FragmentRegistryAPI {\n  return new FragmentRegistry(...fragments);\n}\n\nclass FragmentRegistry implements FragmentRegistryAPI {\n  private registry: FragmentMap = Object.create(null);\n\n  // Call `createFragmentRegistry` instead of invoking the\n  // FragmentRegistry constructor directly. This reserves the constructor for\n  // future configuration of the FragmentRegistry.\n  constructor(...fragments: DocumentNode[]) {\n    this.resetCaches();\n    if (fragments.length) {\n      this.register(...fragments);\n    }\n  }\n\n  public register(...fragments: DocumentNode[]): this {\n    const definitions = new Map<string, FragmentDefinitionNode>();\n    fragments.forEach((doc: DocumentNode) => {\n      getFragmentDefinitions(doc).forEach((node) => {\n        definitions.set(node.name.value, node);\n      });\n    });\n\n    definitions.forEach((node, name) => {\n      if (node !== this.registry[name]) {\n        this.registry[name] = node;\n        this.invalidate(name);\n      }\n    });\n\n    return this;\n  }\n\n  // Overridden in the resetCaches method below.\n  private invalidate(name: string) {}\n\n  public resetCaches() {\n    const proto = FragmentRegistry.prototype;\n    this.invalidate = (this.lookup = wrap(proto.lookup.bind(this), {\n      makeCacheKey: (arg) => arg,\n      max:\n        cacheSizes[\"fragmentRegistry.lookup\"] ||\n        defaultCacheSizes[\"fragmentRegistry.lookup\"],\n    })).dirty; // This dirty function is bound to the wrapped lookup method.\n    this.transform = wrap(proto.transform.bind(this), {\n      cache: WeakCache,\n      max:\n        cacheSizes[\"fragmentRegistry.transform\"] ||\n        defaultCacheSizes[\"fragmentRegistry.transform\"],\n    });\n    this.findFragmentSpreads = wrap(proto.findFragmentSpreads.bind(this), {\n      cache: WeakCache,\n      max:\n        cacheSizes[\"fragmentRegistry.findFragmentSpreads\"] ||\n        defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"],\n    });\n  }\n\n  /*\n   * Note:\n   * This method is only memoized so it can serve as a dependency to `tranform`,\n   * so calling `invalidate` will invalidate cache entries for `transform`.\n   */\n  public lookup(fragmentName: string): FragmentDefinitionNode | null {\n    return this.registry[fragmentName] || null;\n  }\n\n  public transform<D extends DocumentNode>(document: D): D {\n    const defined = new Map<string, FragmentDefinitionNode>();\n    getFragmentDefinitions(document).forEach((def) => {\n      defined.set(def.name.value, def);\n    });\n\n    const unbound = new Set<string>();\n    const enqueue = (spreadName: string) => {\n      if (!defined.has(spreadName)) {\n        unbound.add(spreadName);\n      }\n    };\n\n    const enqueueChildSpreads = (node: ASTNode) =>\n      Object.keys(this.findFragmentSpreads(node)).forEach(enqueue);\n\n    enqueueChildSpreads(document);\n\n    const missing: string[] = [];\n    const map: FragmentMap = Object.create(null);\n\n    // This Set forEach loop can be extended during iteration by adding\n    // additional strings to the unbound set.\n    unbound.forEach((fragmentName) => {\n      const knownFragmentDef = defined.get(fragmentName);\n      if (knownFragmentDef) {\n        enqueueChildSpreads((map[fragmentName] = knownFragmentDef));\n      } else {\n        missing.push(fragmentName);\n        const def = this.lookup(fragmentName);\n        if (def) {\n          enqueueChildSpreads((map[fragmentName] = def));\n        }\n      }\n    });\n\n    if (missing.length) {\n      const defsToAppend: FragmentDefinitionNode[] = [];\n      missing.forEach((name) => {\n        const def = map[name];\n        if (def) {\n          defsToAppend.push(def);\n        }\n      });\n\n      if (defsToAppend.length) {\n        document = {\n          ...document,\n          definitions: document.definitions.concat(defsToAppend),\n        };\n      }\n    }\n\n    return document;\n  }\n\n  public findFragmentSpreads(root: ASTNode): FragmentSpreadMap {\n    const spreads: FragmentSpreadMap = Object.create(null);\n\n    visit(root, {\n      FragmentSpread(node) {\n        spreads[node.name.value] = node;\n      },\n    });\n\n    return spreads;\n  }\n}\n\ninterface FragmentSpreadMap {\n  [fragmentSpreadName: string]: FragmentSpreadNode;\n}\n"]}