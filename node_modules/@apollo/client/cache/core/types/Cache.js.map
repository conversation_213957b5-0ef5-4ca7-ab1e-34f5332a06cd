{"version": 3, "file": "Cache.js", "sourceRoot": "", "sources": ["../../../../src/cache/core/types/Cache.ts"], "names": [], "mappings": "AAKA,MAAM,KAAW,KAAK,CA8GrB;AA9GD,WAAiB,KAAK;AA8GtB,CAAC,EA9GgB,KAAK,KAAL,KAAK,QA8GrB", "sourcesContent": ["import { DataProxy } from \"./DataProxy.js\";\nimport type { AllFieldsModifier, Modifiers } from \"./common.js\";\nimport type { ApolloCache } from \"../cache.js\";\nimport type { Unmasked } from \"../../../masking/index.js\";\n\nexport namespace Cache {\n  export type WatchCallback<TData = any> = (\n    diff: Cache.DiffResult<TData>,\n    lastDiff?: Cache.DiffResult<TData>\n  ) => void;\n\n  export interface ReadOptions<TVariables = any, TData = any>\n    extends DataProxy.Query<TVariables, TData> {\n    rootId?: string;\n    previousResult?: any;\n    optimistic: boolean;\n    returnPartialData?: boolean;\n    /**\n     * @deprecated\n     * Using `canonizeResults` can result in memory leaks so we generally do not\n     * recommend using this option anymore.\n     * A future version of Apollo Client will contain a similar feature without\n     * the risk of memory leaks.\n     */\n    canonizeResults?: boolean;\n  }\n\n  export interface WriteOptions<TResult = any, TVariables = any>\n    extends Omit<DataProxy.Query<TVariables, TResult>, \"id\">,\n      Omit<DataProxy.WriteOptions<TResult>, \"data\"> {\n    dataId?: string;\n    result: Unmasked<TResult>;\n  }\n\n  export interface DiffOptions<TData = any, TVariables = any>\n    extends Omit<ReadOptions<TVariables, TData>, \"rootId\"> {\n    // The DiffOptions interface is currently just an alias for\n    // ReadOptions, though DiffOptions used to be responsible for\n    // declaring the returnPartialData option.\n  }\n\n  export interface WatchOptions<TData = any, TVariables = any>\n    extends DiffOptions<TData, TVariables> {\n    watcher?: object;\n    immediate?: boolean;\n    callback: WatchCallback<TData>;\n    lastDiff?: DiffResult<TData>;\n  }\n\n  export interface EvictOptions {\n    id?: string;\n    fieldName?: string;\n    args?: Record<string, any>;\n    broadcast?: boolean;\n  }\n\n  // Although you can call cache.reset() without options, its behavior can be\n  // configured by passing a Cache.ResetOptions object.\n  export interface ResetOptions {\n    discardWatches?: boolean;\n  }\n\n  export interface ModifyOptions<\n    Entity extends Record<string, any> = Record<string, any>,\n  > {\n    id?: string;\n    fields: Modifiers<Entity> | AllFieldsModifier<Entity>;\n    optimistic?: boolean;\n    broadcast?: boolean;\n  }\n\n  export interface BatchOptions<\n    TCache extends ApolloCache<any>,\n    TUpdateResult = void,\n  > {\n    // Same as the first parameter of performTransaction, except the cache\n    // argument will have the subclass type rather than ApolloCache.\n    update(cache: TCache): TUpdateResult;\n\n    // Passing a string for this option creates a new optimistic layer, with the\n    // given string as its layer.id, just like passing a string for the\n    // optimisticId parameter of performTransaction. Passing true is the same as\n    // passing undefined to performTransaction (running the batch operation\n    // against the current top layer of the cache), and passing false is the\n    // same as passing null (running the operation against root/non-optimistic\n    // cache data).\n    optimistic?: string | boolean;\n\n    // If you specify the ID of an optimistic layer using this option, that\n    // layer will be removed as part of the batch transaction, triggering at\n    // most one broadcast for both the transaction and the removal of the layer.\n    // Note: this option is needed because calling cache.removeOptimistic during\n    // the transaction function may not be not safe, since any modifications to\n    // cache layers may be discarded after the transaction finishes.\n    removeOptimistic?: string;\n\n    // If you want to find out which watched queries were invalidated during\n    // this batch operation, pass this optional callback function. Returning\n    // false from the callback will prevent broadcasting this result.\n    onWatchUpdated?: (\n      this: TCache,\n      watch: Cache.WatchOptions,\n      diff: Cache.DiffResult<any>,\n      lastDiff?: Cache.DiffResult<any> | undefined\n    ) => any;\n  }\n\n  export import DiffResult = DataProxy.DiffResult;\n  export import ReadQueryOptions = DataProxy.ReadQueryOptions;\n  export import ReadFragmentOptions = DataProxy.ReadFragmentOptions;\n  export import WriteQueryOptions = DataProxy.WriteQueryOptions;\n  export import WriteFragmentOptions = DataProxy.WriteFragmentOptions;\n  export import UpdateQueryOptions = DataProxy.UpdateQueryOptions;\n  export import UpdateFragmentOptions = DataProxy.UpdateFragmentOptions;\n  export import Fragment = DataProxy.Fragment;\n}\n"]}