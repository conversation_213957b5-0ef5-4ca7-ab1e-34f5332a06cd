{"version": 3, "file": "cache.cjs", "sources": ["../utilities/caching/getMemoryInternals.js", "../core/equalByQuery.js", "core/cache.js", "core/types/Cache.js", "core/types/common.js", "inmemory/helpers.js", "inmemory/entityStore.js", "inmemory/object-canon.js", "inmemory/readFromStore.js", "inmemory/reactiveVars.js", "inmemory/key-extractor.js", "inmemory/policies.js", "inmemory/writeToStore.js", "inmemory/inMemoryCache.js", "inmemory/fragmentRegistry.js"], "sourcesContent": ["import { __assign, __spreadArray } from \"tslib\";\nimport { cacheSizes } from \"./sizes.js\";\nvar globalCaches = {};\nexport function registerGlobalCache(name, getSize) {\n    globalCaches[name] = getSize;\n}\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getApolloClientMemoryInternals = globalThis.__DEV__ !== false ?\n    _getApolloClientMemoryInternals\n    : undefined;\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getInMemoryCacheMemoryInternals = globalThis.__DEV__ !== false ?\n    _getInMemoryCacheMemoryInternals\n    : undefined;\n/**\n * For internal purposes only - please call `ApolloClient.getMemoryInternals` instead\n * @internal\n */\nexport var getApolloCacheMemoryInternals = globalThis.__DEV__ !== false ?\n    _getApolloCacheMemoryInternals\n    : undefined;\nfunction getCurrentCacheSizes() {\n    // `defaultCacheSizes` is a `const enum` that will be inlined during build, so we have to reconstruct it's shape here\n    var defaults = {\n        parser: 1000 /* defaultCacheSizes[\"parser\"] */,\n        canonicalStringify: 1000 /* defaultCacheSizes[\"canonicalStringify\"] */,\n        print: 2000 /* defaultCacheSizes[\"print\"] */,\n        \"documentTransform.cache\": 2000 /* defaultCacheSizes[\"documentTransform.cache\"] */,\n        \"queryManager.getDocumentInfo\": 2000 /* defaultCacheSizes[\"queryManager.getDocumentInfo\"] */,\n        \"PersistedQueryLink.persistedQueryHashes\": 2000 /* defaultCacheSizes[\"PersistedQueryLink.persistedQueryHashes\"] */,\n        \"fragmentRegistry.transform\": 2000 /* defaultCacheSizes[\"fragmentRegistry.transform\"] */,\n        \"fragmentRegistry.lookup\": 1000 /* defaultCacheSizes[\"fragmentRegistry.lookup\"] */,\n        \"fragmentRegistry.findFragmentSpreads\": 4000 /* defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"] */,\n        \"cache.fragmentQueryDocuments\": 1000 /* defaultCacheSizes[\"cache.fragmentQueryDocuments\"] */,\n        \"removeTypenameFromVariables.getVariableDefinitions\": 2000 /* defaultCacheSizes[\"removeTypenameFromVariables.getVariableDefinitions\"] */,\n        \"inMemoryCache.maybeBroadcastWatch\": 5000 /* defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] */,\n        \"inMemoryCache.executeSelectionSet\": 50000 /* defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"] */,\n        \"inMemoryCache.executeSubSelectedArray\": 10000 /* defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"] */,\n    };\n    return Object.fromEntries(Object.entries(defaults).map(function (_a) {\n        var k = _a[0], v = _a[1];\n        return [\n            k,\n            cacheSizes[k] || v,\n        ];\n    }));\n}\nfunction _getApolloClientMemoryInternals() {\n    var _a, _b, _c, _d, _e;\n    if (!(globalThis.__DEV__ !== false))\n        throw new Error(\"only supported in development mode\");\n    return {\n        limits: getCurrentCacheSizes(),\n        sizes: __assign({ print: (_a = globalCaches.print) === null || _a === void 0 ? void 0 : _a.call(globalCaches), parser: (_b = globalCaches.parser) === null || _b === void 0 ? void 0 : _b.call(globalCaches), canonicalStringify: (_c = globalCaches.canonicalStringify) === null || _c === void 0 ? void 0 : _c.call(globalCaches), links: linkInfo(this.link), queryManager: {\n                getDocumentInfo: this[\"queryManager\"][\"transformCache\"].size,\n                documentTransforms: transformInfo(this[\"queryManager\"].documentTransform),\n            } }, (_e = (_d = this.cache).getMemoryInternals) === null || _e === void 0 ? void 0 : _e.call(_d)),\n    };\n}\nfunction _getApolloCacheMemoryInternals() {\n    return {\n        cache: {\n            fragmentQueryDocuments: getWrapperInformation(this[\"getFragmentDoc\"]),\n        },\n    };\n}\nfunction _getInMemoryCacheMemoryInternals() {\n    var fragments = this.config.fragments;\n    return __assign(__assign({}, _getApolloCacheMemoryInternals.apply(this)), { addTypenameDocumentTransform: transformInfo(this[\"addTypenameTransform\"]), inMemoryCache: {\n            executeSelectionSet: getWrapperInformation(this[\"storeReader\"][\"executeSelectionSet\"]),\n            executeSubSelectedArray: getWrapperInformation(this[\"storeReader\"][\"executeSubSelectedArray\"]),\n            maybeBroadcastWatch: getWrapperInformation(this[\"maybeBroadcastWatch\"]),\n        }, fragmentRegistry: {\n            findFragmentSpreads: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.findFragmentSpreads),\n            lookup: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.lookup),\n            transform: getWrapperInformation(fragments === null || fragments === void 0 ? void 0 : fragments.transform),\n        } });\n}\nfunction isWrapper(f) {\n    return !!f && \"dirtyKey\" in f;\n}\nfunction getWrapperInformation(f) {\n    return isWrapper(f) ? f.size : undefined;\n}\nfunction isDefined(value) {\n    return value != null;\n}\nfunction transformInfo(transform) {\n    return recurseTransformInfo(transform).map(function (cache) { return ({ cache: cache }); });\n}\nfunction recurseTransformInfo(transform) {\n    return transform ?\n        __spreadArray(__spreadArray([\n            getWrapperInformation(transform === null || transform === void 0 ? void 0 : transform[\"performWork\"])\n        ], recurseTransformInfo(transform === null || transform === void 0 ? void 0 : transform[\"left\"]), true), recurseTransformInfo(transform === null || transform === void 0 ? void 0 : transform[\"right\"]), true).filter(isDefined)\n        : [];\n}\nfunction linkInfo(link) {\n    var _a;\n    return link ?\n        __spreadArray(__spreadArray([\n            (_a = link === null || link === void 0 ? void 0 : link.getMemoryInternals) === null || _a === void 0 ? void 0 : _a.call(link)\n        ], linkInfo(link === null || link === void 0 ? void 0 : link.left), true), linkInfo(link === null || link === void 0 ? void 0 : link.right), true).filter(isDefined)\n        : [];\n}\n//# sourceMappingURL=getMemoryInternals.js.map", "import { __rest } from \"tslib\";\nimport equal from \"@wry/equality\";\nimport { createFragmentMap, getFragmentDefinitions, getFragmentFromSelection, getMainDefinition, isField, resultKeyNameFromField, shouldInclude, } from \"../utilities/index.js\";\n// Returns true if aResult and bResult are deeply equal according to the fields\n// selected by the given query, ignoring any fields marked as @nonreactive.\nexport function equalByQuery(query, _a, _b, variables) {\n    var aData = _a.data, aRest = __rest(_a, [\"data\"]);\n    var bData = _b.data, bRest = __rest(_b, [\"data\"]);\n    return (equal(aRest, bRest) &&\n        equalBySelectionSet(getMainDefinition(query).selectionSet, aData, bData, {\n            fragmentMap: createFragmentMap(getFragmentDefinitions(query)),\n            variables: variables,\n        }));\n}\nfunction equalBySelectionSet(selectionSet, aResult, bResult, context) {\n    if (aResult === bResult) {\n        return true;\n    }\n    var seenSelections = new Set();\n    // Returning true from this Array.prototype.every callback function skips the\n    // current field/subtree. Returning false aborts the entire traversal\n    // immediately, causing equalBySelectionSet to return false.\n    return selectionSet.selections.every(function (selection) {\n        // Avoid re-processing the same selection at the same level of recursion, in\n        // case the same field gets included via multiple indirect fragment spreads.\n        if (seenSelections.has(selection))\n            return true;\n        seenSelections.add(selection);\n        // Ignore @skip(if: true) and @include(if: false) fields.\n        if (!shouldInclude(selection, context.variables))\n            return true;\n        // If the field or (named) fragment spread has a @nonreactive directive on\n        // it, we don't care if it's different, so we pretend it's the same.\n        if (selectionHasNonreactiveDirective(selection))\n            return true;\n        if (isField(selection)) {\n            var resultKey = resultKeyNameFromField(selection);\n            var aResultChild = aResult && aResult[resultKey];\n            var bResultChild = bResult && bResult[resultKey];\n            var childSelectionSet = selection.selectionSet;\n            if (!childSelectionSet) {\n                // These are scalar values, so we can compare them with deep equal\n                // without redoing the main recursive work.\n                return equal(aResultChild, bResultChild);\n            }\n            var aChildIsArray = Array.isArray(aResultChild);\n            var bChildIsArray = Array.isArray(bResultChild);\n            if (aChildIsArray !== bChildIsArray)\n                return false;\n            if (aChildIsArray && bChildIsArray) {\n                var length_1 = aResultChild.length;\n                if (bResultChild.length !== length_1) {\n                    return false;\n                }\n                for (var i = 0; i < length_1; ++i) {\n                    if (!equalBySelectionSet(childSelectionSet, aResultChild[i], bResultChild[i], context)) {\n                        return false;\n                    }\n                }\n                return true;\n            }\n            return equalBySelectionSet(childSelectionSet, aResultChild, bResultChild, context);\n        }\n        else {\n            var fragment = getFragmentFromSelection(selection, context.fragmentMap);\n            if (fragment) {\n                // The fragment might === selection if it's an inline fragment, but\n                // could be !== if it's a named fragment ...spread.\n                if (selectionHasNonreactiveDirective(fragment))\n                    return true;\n                return equalBySelectionSet(fragment.selectionSet, \n                // Notice that we reuse the same aResult and bResult values here,\n                // since the fragment ...spread does not specify a field name, but\n                // consists of multiple fields (within the fragment's selection set)\n                // that should be applied to the current result value(s).\n                aResult, bResult, context);\n            }\n        }\n    });\n}\nfunction selectionHasNonreactiveDirective(selection) {\n    return (!!selection.directives && selection.directives.some(directiveIsNonreactive));\n}\nfunction directiveIsNonreactive(dir) {\n    return dir.name.value === \"nonreactive\";\n}\n//# sourceMappingURL=equalByQuery.js.map", "import { __assign, __rest } from \"tslib\";\nimport { wrap } from \"optimism\";\nimport { Observable, cacheSizes, getFragmentDefinition, getFragmentQueryDocument, mergeDeepArray, } from \"../../utilities/index.js\";\nimport { WeakCache } from \"@wry/caches\";\nimport { getApolloCacheMemoryInternals } from \"../../utilities/caching/getMemoryInternals.js\";\nimport { equalByQuery } from \"../../core/equalByQuery.js\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { maskFragment } from \"../../masking/index.js\";\nvar ApolloCache = /** @class */ (function () {\n    function ApolloCache() {\n        this.assumeImmutableResults = false;\n        // Make sure we compute the same (===) fragment query document every\n        // time we receive the same fragment in readFragment.\n        this.getFragmentDoc = wrap(getFragmentQueryDocument, {\n            max: cacheSizes[\"cache.fragmentQueryDocuments\"] ||\n                1000 /* defaultCacheSizes[\"cache.fragmentQueryDocuments\"] */,\n            cache: WeakCache,\n        });\n    }\n    // Function used to lookup a fragment when a fragment definition is not part\n    // of the GraphQL document. This is useful for caches, such as InMemoryCache,\n    // that register fragments ahead of time so they can be referenced by name.\n    ApolloCache.prototype.lookupFragment = function (fragmentName) {\n        return null;\n    };\n    // Transactional API\n    // The batch method is intended to replace/subsume both performTransaction\n    // and recordOptimisticTransaction, but performTransaction came first, so we\n    // provide a default batch implementation that's just another way of calling\n    // performTransaction. Subclasses of ApolloCache (such as InMemoryCache) can\n    // override the batch method to do more interesting things with its options.\n    ApolloCache.prototype.batch = function (options) {\n        var _this = this;\n        var optimisticId = typeof options.optimistic === \"string\" ? options.optimistic\n            : options.optimistic === false ? null\n                : void 0;\n        var updateResult;\n        this.performTransaction(function () { return (updateResult = options.update(_this)); }, optimisticId);\n        return updateResult;\n    };\n    ApolloCache.prototype.recordOptimisticTransaction = function (transaction, optimisticId) {\n        this.performTransaction(transaction, optimisticId);\n    };\n    // Optional API\n    // Called once per input document, allowing the cache to make static changes\n    // to the query, such as adding __typename fields.\n    ApolloCache.prototype.transformDocument = function (document) {\n        return document;\n    };\n    // Called before each ApolloLink request, allowing the cache to make dynamic\n    // changes to the query, such as filling in missing fragment definitions.\n    ApolloCache.prototype.transformForLink = function (document) {\n        return document;\n    };\n    ApolloCache.prototype.identify = function (object) {\n        return;\n    };\n    ApolloCache.prototype.gc = function () {\n        return [];\n    };\n    ApolloCache.prototype.modify = function (options) {\n        return false;\n    };\n    // DataProxy API\n    ApolloCache.prototype.readQuery = function (options, optimistic) {\n        if (optimistic === void 0) { optimistic = !!options.optimistic; }\n        return this.read(__assign(__assign({}, options), { rootId: options.id || \"ROOT_QUERY\", optimistic: optimistic }));\n    };\n    /** {@inheritDoc @apollo/client!ApolloClient#watchFragment:member(1)} */\n    ApolloCache.prototype.watchFragment = function (options) {\n        var _this = this;\n        var fragment = options.fragment, fragmentName = options.fragmentName, from = options.from, _a = options.optimistic, optimistic = _a === void 0 ? true : _a, otherOptions = __rest(options, [\"fragment\", \"fragmentName\", \"from\", \"optimistic\"]);\n        var query = this.getFragmentDoc(fragment, fragmentName);\n        // While our TypeScript types do not allow for `undefined` as a valid\n        // `from`, its possible `useFragment` gives us an `undefined` since it\n        // calls` cache.identify` and provides that value to `from`. We are\n        // adding this fix here however to ensure those using plain JavaScript\n        // and using `cache.identify` themselves will avoid seeing the obscure\n        // warning.\n        var id = typeof from === \"undefined\" || typeof from === \"string\" ?\n            from\n            : this.identify(from);\n        var dataMasking = !!options[Symbol.for(\"apollo.dataMasking\")];\n        if (globalThis.__DEV__ !== false) {\n            var actualFragmentName = fragmentName || getFragmentDefinition(fragment).name.value;\n            if (!id) {\n                globalThis.__DEV__ !== false && invariant.warn(1, actualFragmentName);\n            }\n        }\n        var diffOptions = __assign(__assign({}, otherOptions), { returnPartialData: true, id: id, query: query, optimistic: optimistic });\n        var latestDiff;\n        return new Observable(function (observer) {\n            return _this.watch(__assign(__assign({}, diffOptions), { immediate: true, callback: function (diff) {\n                    var data = dataMasking ?\n                        maskFragment(diff.result, fragment, _this, fragmentName)\n                        : diff.result;\n                    if (\n                    // Always ensure we deliver the first result\n                    latestDiff &&\n                        equalByQuery(query, { data: latestDiff.result }, { data: data }, \n                        // TODO: Fix the type on WatchFragmentOptions so that TVars\n                        // extends OperationVariables\n                        options.variables)) {\n                        return;\n                    }\n                    var result = {\n                        data: data,\n                        complete: !!diff.complete,\n                    };\n                    if (diff.missing) {\n                        result.missing = mergeDeepArray(diff.missing.map(function (error) { return error.missing; }));\n                    }\n                    latestDiff = __assign(__assign({}, diff), { result: data });\n                    observer.next(result);\n                } }));\n        });\n    };\n    ApolloCache.prototype.readFragment = function (options, optimistic) {\n        if (optimistic === void 0) { optimistic = !!options.optimistic; }\n        return this.read(__assign(__assign({}, options), { query: this.getFragmentDoc(options.fragment, options.fragmentName), rootId: options.id, optimistic: optimistic }));\n    };\n    ApolloCache.prototype.writeQuery = function (_a) {\n        var id = _a.id, data = _a.data, options = __rest(_a, [\"id\", \"data\"]);\n        return this.write(Object.assign(options, {\n            dataId: id || \"ROOT_QUERY\",\n            result: data,\n        }));\n    };\n    ApolloCache.prototype.writeFragment = function (_a) {\n        var id = _a.id, data = _a.data, fragment = _a.fragment, fragmentName = _a.fragmentName, options = __rest(_a, [\"id\", \"data\", \"fragment\", \"fragmentName\"]);\n        return this.write(Object.assign(options, {\n            query: this.getFragmentDoc(fragment, fragmentName),\n            dataId: id,\n            result: data,\n        }));\n    };\n    ApolloCache.prototype.updateQuery = function (options, update) {\n        return this.batch({\n            update: function (cache) {\n                var value = cache.readQuery(options);\n                var data = update(value);\n                if (data === void 0 || data === null)\n                    return value;\n                cache.writeQuery(__assign(__assign({}, options), { data: data }));\n                return data;\n            },\n        });\n    };\n    ApolloCache.prototype.updateFragment = function (options, update) {\n        return this.batch({\n            update: function (cache) {\n                var value = cache.readFragment(options);\n                var data = update(value);\n                if (data === void 0 || data === null)\n                    return value;\n                cache.writeFragment(__assign(__assign({}, options), { data: data }));\n                return data;\n            },\n        });\n    };\n    return ApolloCache;\n}());\nexport { ApolloCache };\nif (globalThis.__DEV__ !== false) {\n    ApolloCache.prototype.getMemoryInternals = getApolloCacheMemoryInternals;\n}\n//# sourceMappingURL=cache.js.map", "export var Cache;\n(function (Cache) {\n})(Cache || (Cache = {}));\n//# sourceMappingURL=Cache.js.map", "import { __extends } from \"tslib\";\nvar MissingFieldError = /** @class */ (function (_super) {\n    __extends(MissingFieldError, _super);\n    function MissingFieldError(message, path, query, variables) {\n        var _a;\n        // 'Error' breaks prototype chain here\n        var _this = _super.call(this, message) || this;\n        _this.message = message;\n        _this.path = path;\n        _this.query = query;\n        _this.variables = variables;\n        if (Array.isArray(_this.path)) {\n            _this.missing = _this.message;\n            for (var i = _this.path.length - 1; i >= 0; --i) {\n                _this.missing = (_a = {}, _a[_this.path[i]] = _this.missing, _a);\n            }\n        }\n        else {\n            _this.missing = _this.path;\n        }\n        // We're not using `Object.setPrototypeOf` here as it isn't fully supported\n        // on Android (see issue #3236).\n        _this.__proto__ = MissingFieldError.prototype;\n        return _this;\n    }\n    return MissingFieldError;\n}(<PERSON>rror));\nexport { MissingFieldError };\n//# sourceMappingURL=common.js.map", "import { is<PERSON><PERSON><PERSON>nce, is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, resultKeyNameFromField, shouldInclude, isNonNullObject, compact, createFragmentMap, getFragmentDefinitions, isArray, } from \"../../utilities/index.js\";\nexport var hasOwn = Object.prototype.hasOwnProperty;\nexport function isNullish(value) {\n    return value === null || value === void 0;\n}\nexport { isArray };\nexport function defaultDataIdFromObject(_a, context) {\n    var __typename = _a.__typename, id = _a.id, _id = _a._id;\n    if (typeof __typename === \"string\") {\n        if (context) {\n            context.keyObject =\n                !isNullish(id) ? { id: id }\n                    : !isNullish(_id) ? { _id: _id }\n                        : void 0;\n        }\n        // If there is no object.id, fall back to object._id.\n        if (isNullish(id) && !isNullish(_id)) {\n            id = _id;\n        }\n        if (!isNullish(id)) {\n            return \"\".concat(__typename, \":\").concat(typeof id === \"number\" || typeof id === \"string\" ?\n                id\n                : JSON.stringify(id));\n        }\n    }\n}\nvar defaultConfig = {\n    dataIdFromObject: defaultDataIdFromObject,\n    addTypename: true,\n    resultCaching: true,\n    // Thanks to the shouldCanonizeResults helper, this should be the only line\n    // you have to change to reenable canonization by default in the future.\n    canonizeResults: false,\n};\nexport function normalizeConfig(config) {\n    return compact(defaultConfig, config);\n}\nexport function shouldCanonizeResults(config) {\n    var value = config.canonizeResults;\n    return value === void 0 ? defaultConfig.canonizeResults : value;\n}\nexport function getTypenameFromStoreObject(store, objectOrReference) {\n    return isReference(objectOrReference) ?\n        store.get(objectOrReference.__ref, \"__typename\")\n        : objectOrReference && objectOrReference.__typename;\n}\nexport var TypeOrFieldNameRegExp = /^[_a-z][_0-9a-z]*/i;\nexport function fieldNameFromStoreName(storeFieldName) {\n    var match = storeFieldName.match(TypeOrFieldNameRegExp);\n    return match ? match[0] : storeFieldName;\n}\nexport function selectionSetMatchesResult(selectionSet, result, variables) {\n    if (isNonNullObject(result)) {\n        return isArray(result) ?\n            result.every(function (item) {\n                return selectionSetMatchesResult(selectionSet, item, variables);\n            })\n            : selectionSet.selections.every(function (field) {\n                if (isField(field) && shouldInclude(field, variables)) {\n                    var key = resultKeyNameFromField(field);\n                    return (hasOwn.call(result, key) &&\n                        (!field.selectionSet ||\n                            selectionSetMatchesResult(field.selectionSet, result[key], variables)));\n                }\n                // If the selection has been skipped with @skip(true) or\n                // @include(false), it should not count against the matching. If\n                // the selection is not a field, it must be a fragment (inline or\n                // named). We will determine if selectionSetMatchesResult for that\n                // fragment when we get to it, so for now we return true.\n                return true;\n            });\n    }\n    return false;\n}\nexport function storeValueIsStoreObject(value) {\n    return isNonNullObject(value) && !isReference(value) && !isArray(value);\n}\nexport function makeProcessedFieldsMerger() {\n    return new DeepMerger();\n}\nexport function extractFragmentContext(document, fragments) {\n    // FragmentMap consisting only of fragments defined directly in document, not\n    // including other fragments registered in the FragmentRegistry.\n    var fragmentMap = createFragmentMap(getFragmentDefinitions(document));\n    return {\n        fragmentMap: fragmentMap,\n        lookupFragment: function (name) {\n            var def = fragmentMap[name];\n            if (!def && fragments) {\n                def = fragments.lookup(name);\n            }\n            return def || null;\n        },\n    };\n}\n//# sourceMappingURL=helpers.js.map", "import { __assign, __extends, __rest } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\nimport { dep } from \"optimism\";\nimport { equal } from \"@wry/equality\";\nimport { Trie } from \"@wry/trie\";\nimport { isReference, makeReference, DeepMerger, maybeDeepFreeze, canUseWeakMap, isNonNullObject, } from \"../../utilities/index.js\";\nimport { hasOwn, fieldNameFromStoreName } from \"./helpers.js\";\nvar DELETE = Object.create(null);\nvar delModifier = function () { return DELETE; };\nvar INVALIDATE = Object.create(null);\nvar EntityStore = /** @class */ (function () {\n    function EntityStore(policies, group) {\n        var _this = this;\n        this.policies = policies;\n        this.group = group;\n        this.data = Object.create(null);\n        // Maps root entity IDs to the number of times they have been retained, minus\n        // the number of times they have been released. Retained entities keep other\n        // entities they reference (even indirectly) from being garbage collected.\n        this.rootIds = Object.create(null);\n        // Lazily tracks { __ref: <dataId> } strings contained by this.data[dataId].\n        this.refs = Object.create(null);\n        // Bound function that can be passed around to provide easy access to fields\n        // of Reference objects as well as ordinary objects.\n        this.getFieldValue = function (objectOrReference, storeFieldName) {\n            return maybeDeepFreeze(isReference(objectOrReference) ?\n                _this.get(objectOrReference.__ref, storeFieldName)\n                : objectOrReference && objectOrReference[storeFieldName]);\n        };\n        // Returns true for non-normalized StoreObjects and non-dangling\n        // References, indicating that readField(name, objOrRef) has a chance of\n        // working. Useful for filtering out dangling references from lists.\n        this.canRead = function (objOrRef) {\n            return isReference(objOrRef) ?\n                _this.has(objOrRef.__ref)\n                : typeof objOrRef === \"object\";\n        };\n        // Bound function that converts an id or an object with a __typename and\n        // primary key fields to a Reference object. If called with a Reference object,\n        // that same Reference object is returned. Pass true for mergeIntoStore to persist\n        // an object into the store.\n        this.toReference = function (objOrIdOrRef, mergeIntoStore) {\n            if (typeof objOrIdOrRef === \"string\") {\n                return makeReference(objOrIdOrRef);\n            }\n            if (isReference(objOrIdOrRef)) {\n                return objOrIdOrRef;\n            }\n            var id = _this.policies.identify(objOrIdOrRef)[0];\n            if (id) {\n                var ref = makeReference(id);\n                if (mergeIntoStore) {\n                    _this.merge(id, objOrIdOrRef);\n                }\n                return ref;\n            }\n        };\n    }\n    // Although the EntityStore class is abstract, it contains concrete\n    // implementations of the various NormalizedCache interface methods that\n    // are inherited by the Root and Layer subclasses.\n    EntityStore.prototype.toObject = function () {\n        return __assign({}, this.data);\n    };\n    EntityStore.prototype.has = function (dataId) {\n        return this.lookup(dataId, true) !== void 0;\n    };\n    EntityStore.prototype.get = function (dataId, fieldName) {\n        this.group.depend(dataId, fieldName);\n        if (hasOwn.call(this.data, dataId)) {\n            var storeObject = this.data[dataId];\n            if (storeObject && hasOwn.call(storeObject, fieldName)) {\n                return storeObject[fieldName];\n            }\n        }\n        if (fieldName === \"__typename\" &&\n            hasOwn.call(this.policies.rootTypenamesById, dataId)) {\n            return this.policies.rootTypenamesById[dataId];\n        }\n        if (this instanceof Layer) {\n            return this.parent.get(dataId, fieldName);\n        }\n    };\n    EntityStore.prototype.lookup = function (dataId, dependOnExistence) {\n        // The has method (above) calls lookup with dependOnExistence = true, so\n        // that it can later be invalidated when we add or remove a StoreObject for\n        // this dataId. Any consumer who cares about the contents of the StoreObject\n        // should not rely on this dependency, since the contents could change\n        // without the object being added or removed.\n        if (dependOnExistence)\n            this.group.depend(dataId, \"__exists\");\n        if (hasOwn.call(this.data, dataId)) {\n            return this.data[dataId];\n        }\n        if (this instanceof Layer) {\n            return this.parent.lookup(dataId, dependOnExistence);\n        }\n        if (this.policies.rootTypenamesById[dataId]) {\n            return Object.create(null);\n        }\n    };\n    EntityStore.prototype.merge = function (older, newer) {\n        var _this = this;\n        var dataId;\n        // Convert unexpected references to ID strings.\n        if (isReference(older))\n            older = older.__ref;\n        if (isReference(newer))\n            newer = newer.__ref;\n        var existing = typeof older === \"string\" ? this.lookup((dataId = older)) : older;\n        var incoming = typeof newer === \"string\" ? this.lookup((dataId = newer)) : newer;\n        // If newer was a string ID, but that ID was not defined in this store,\n        // then there are no fields to be merged, so we're done.\n        if (!incoming)\n            return;\n        invariant(typeof dataId === \"string\", 2);\n        var merged = new DeepMerger(storeObjectReconciler).merge(existing, incoming);\n        // Even if merged === existing, existing may have come from a lower\n        // layer, so we always need to set this.data[dataId] on this level.\n        this.data[dataId] = merged;\n        if (merged !== existing) {\n            delete this.refs[dataId];\n            if (this.group.caching) {\n                var fieldsToDirty_1 = Object.create(null);\n                // If we added a new StoreObject where there was previously none, dirty\n                // anything that depended on the existence of this dataId, such as the\n                // EntityStore#has method.\n                if (!existing)\n                    fieldsToDirty_1.__exists = 1;\n                // Now invalidate dependents who called getFieldValue for any fields\n                // that are changing as a result of this merge.\n                Object.keys(incoming).forEach(function (storeFieldName) {\n                    if (!existing ||\n                        existing[storeFieldName] !== merged[storeFieldName]) {\n                        // Always dirty the full storeFieldName, which may include\n                        // serialized arguments following the fieldName prefix.\n                        fieldsToDirty_1[storeFieldName] = 1;\n                        // Also dirty fieldNameFromStoreName(storeFieldName) if it's\n                        // different from storeFieldName and this field does not have\n                        // keyArgs configured, because that means the cache can't make\n                        // any assumptions about how field values with the same field\n                        // name but different arguments might be interrelated, so it\n                        // must err on the side of invalidating all field values that\n                        // share the same short fieldName, regardless of arguments.\n                        var fieldName = fieldNameFromStoreName(storeFieldName);\n                        if (fieldName !== storeFieldName &&\n                            !_this.policies.hasKeyArgs(merged.__typename, fieldName)) {\n                            fieldsToDirty_1[fieldName] = 1;\n                        }\n                        // If merged[storeFieldName] has become undefined, and this is the\n                        // Root layer, actually delete the property from the merged object,\n                        // which is guaranteed to have been created fresh in this method.\n                        if (merged[storeFieldName] === void 0 && !(_this instanceof Layer)) {\n                            delete merged[storeFieldName];\n                        }\n                    }\n                });\n                if (fieldsToDirty_1.__typename &&\n                    !(existing && existing.__typename) &&\n                    // Since we return default root __typename strings\n                    // automatically from store.get, we don't need to dirty the\n                    // ROOT_QUERY.__typename field if merged.__typename is equal\n                    // to the default string (usually \"Query\").\n                    this.policies.rootTypenamesById[dataId] === merged.__typename) {\n                    delete fieldsToDirty_1.__typename;\n                }\n                Object.keys(fieldsToDirty_1).forEach(function (fieldName) {\n                    return _this.group.dirty(dataId, fieldName);\n                });\n            }\n        }\n    };\n    EntityStore.prototype.modify = function (dataId, fields) {\n        var _this = this;\n        var storeObject = this.lookup(dataId);\n        if (storeObject) {\n            var changedFields_1 = Object.create(null);\n            var needToMerge_1 = false;\n            var allDeleted_1 = true;\n            var sharedDetails_1 = {\n                DELETE: DELETE,\n                INVALIDATE: INVALIDATE,\n                isReference: isReference,\n                toReference: this.toReference,\n                canRead: this.canRead,\n                readField: function (fieldNameOrOptions, from) {\n                    return _this.policies.readField(typeof fieldNameOrOptions === \"string\" ?\n                        {\n                            fieldName: fieldNameOrOptions,\n                            from: from || makeReference(dataId),\n                        }\n                        : fieldNameOrOptions, { store: _this });\n                },\n            };\n            Object.keys(storeObject).forEach(function (storeFieldName) {\n                var fieldName = fieldNameFromStoreName(storeFieldName);\n                var fieldValue = storeObject[storeFieldName];\n                if (fieldValue === void 0)\n                    return;\n                var modify = typeof fields === \"function\" ? fields : (fields[storeFieldName] || fields[fieldName]);\n                if (modify) {\n                    var newValue = modify === delModifier ? DELETE : (modify(maybeDeepFreeze(fieldValue), __assign(__assign({}, sharedDetails_1), { fieldName: fieldName, storeFieldName: storeFieldName, storage: _this.getStorage(dataId, storeFieldName) })));\n                    if (newValue === INVALIDATE) {\n                        _this.group.dirty(dataId, storeFieldName);\n                    }\n                    else {\n                        if (newValue === DELETE)\n                            newValue = void 0;\n                        if (newValue !== fieldValue) {\n                            changedFields_1[storeFieldName] = newValue;\n                            needToMerge_1 = true;\n                            fieldValue = newValue;\n                            if (globalThis.__DEV__ !== false) {\n                                var checkReference = function (ref) {\n                                    if (_this.lookup(ref.__ref) === undefined) {\n                                        globalThis.__DEV__ !== false && invariant.warn(3, ref);\n                                        return true;\n                                    }\n                                };\n                                if (isReference(newValue)) {\n                                    checkReference(newValue);\n                                }\n                                else if (Array.isArray(newValue)) {\n                                    // Warn about writing \"mixed\" arrays of Reference and non-Reference objects\n                                    var seenReference = false;\n                                    var someNonReference = void 0;\n                                    for (var _i = 0, newValue_1 = newValue; _i < newValue_1.length; _i++) {\n                                        var value = newValue_1[_i];\n                                        if (isReference(value)) {\n                                            seenReference = true;\n                                            if (checkReference(value))\n                                                break;\n                                        }\n                                        else {\n                                            // Do not warn on primitive values, since those could never be represented\n                                            // by a reference. This is a valid (albeit uncommon) use case.\n                                            if (typeof value === \"object\" && !!value) {\n                                                var id = _this.policies.identify(value)[0];\n                                                // check if object could even be referenced, otherwise we are not interested in it for this warning\n                                                if (id) {\n                                                    someNonReference = value;\n                                                }\n                                            }\n                                        }\n                                        if (seenReference && someNonReference !== undefined) {\n                                            globalThis.__DEV__ !== false && invariant.warn(4, someNonReference);\n                                            break;\n                                        }\n                                    }\n                                }\n                            }\n                        }\n                    }\n                }\n                if (fieldValue !== void 0) {\n                    allDeleted_1 = false;\n                }\n            });\n            if (needToMerge_1) {\n                this.merge(dataId, changedFields_1);\n                if (allDeleted_1) {\n                    if (this instanceof Layer) {\n                        this.data[dataId] = void 0;\n                    }\n                    else {\n                        delete this.data[dataId];\n                    }\n                    this.group.dirty(dataId, \"__exists\");\n                }\n                return true;\n            }\n        }\n        return false;\n    };\n    // If called with only one argument, removes the entire entity\n    // identified by dataId. If called with a fieldName as well, removes all\n    // fields of that entity whose names match fieldName according to the\n    // fieldNameFromStoreName helper function. If called with a fieldName\n    // and variables, removes all fields of that entity whose names match fieldName\n    // and whose arguments when cached exactly match the variables passed.\n    EntityStore.prototype.delete = function (dataId, fieldName, args) {\n        var _a;\n        var storeObject = this.lookup(dataId);\n        if (storeObject) {\n            var typename = this.getFieldValue(storeObject, \"__typename\");\n            var storeFieldName = fieldName && args ?\n                this.policies.getStoreFieldName({ typename: typename, fieldName: fieldName, args: args })\n                : fieldName;\n            return this.modify(dataId, storeFieldName ? (_a = {},\n                _a[storeFieldName] = delModifier,\n                _a) : delModifier);\n        }\n        return false;\n    };\n    EntityStore.prototype.evict = function (options, limit) {\n        var evicted = false;\n        if (options.id) {\n            if (hasOwn.call(this.data, options.id)) {\n                evicted = this.delete(options.id, options.fieldName, options.args);\n            }\n            if (this instanceof Layer && this !== limit) {\n                evicted = this.parent.evict(options, limit) || evicted;\n            }\n            // Always invalidate the field to trigger rereading of watched\n            // queries, even if no cache data was modified by the eviction,\n            // because queries may depend on computed fields with custom read\n            // functions, whose values are not stored in the EntityStore.\n            if (options.fieldName || evicted) {\n                this.group.dirty(options.id, options.fieldName || \"__exists\");\n            }\n        }\n        return evicted;\n    };\n    EntityStore.prototype.clear = function () {\n        this.replace(null);\n    };\n    EntityStore.prototype.extract = function () {\n        var _this = this;\n        var obj = this.toObject();\n        var extraRootIds = [];\n        this.getRootIdSet().forEach(function (id) {\n            if (!hasOwn.call(_this.policies.rootTypenamesById, id)) {\n                extraRootIds.push(id);\n            }\n        });\n        if (extraRootIds.length) {\n            obj.__META = { extraRootIds: extraRootIds.sort() };\n        }\n        return obj;\n    };\n    EntityStore.prototype.replace = function (newData) {\n        var _this = this;\n        Object.keys(this.data).forEach(function (dataId) {\n            if (!(newData && hasOwn.call(newData, dataId))) {\n                _this.delete(dataId);\n            }\n        });\n        if (newData) {\n            var __META = newData.__META, rest_1 = __rest(newData, [\"__META\"]);\n            Object.keys(rest_1).forEach(function (dataId) {\n                _this.merge(dataId, rest_1[dataId]);\n            });\n            if (__META) {\n                __META.extraRootIds.forEach(this.retain, this);\n            }\n        }\n    };\n    EntityStore.prototype.retain = function (rootId) {\n        return (this.rootIds[rootId] = (this.rootIds[rootId] || 0) + 1);\n    };\n    EntityStore.prototype.release = function (rootId) {\n        if (this.rootIds[rootId] > 0) {\n            var count = --this.rootIds[rootId];\n            if (!count)\n                delete this.rootIds[rootId];\n            return count;\n        }\n        return 0;\n    };\n    // Return a Set<string> of all the ID strings that have been retained by\n    // this layer/root *and* any layers/roots beneath it.\n    EntityStore.prototype.getRootIdSet = function (ids) {\n        if (ids === void 0) { ids = new Set(); }\n        Object.keys(this.rootIds).forEach(ids.add, ids);\n        if (this instanceof Layer) {\n            this.parent.getRootIdSet(ids);\n        }\n        else {\n            // Official singleton IDs like ROOT_QUERY and ROOT_MUTATION are\n            // always considered roots for garbage collection, regardless of\n            // their retainment counts in this.rootIds.\n            Object.keys(this.policies.rootTypenamesById).forEach(ids.add, ids);\n        }\n        return ids;\n    };\n    // The goal of garbage collection is to remove IDs from the Root layer of the\n    // store that are no longer reachable starting from any IDs that have been\n    // explicitly retained (see retain and release, above). Returns an array of\n    // dataId strings that were removed from the store.\n    EntityStore.prototype.gc = function () {\n        var _this = this;\n        var ids = this.getRootIdSet();\n        var snapshot = this.toObject();\n        ids.forEach(function (id) {\n            if (hasOwn.call(snapshot, id)) {\n                // Because we are iterating over an ECMAScript Set, the IDs we add here\n                // will be visited in later iterations of the forEach loop only if they\n                // were not previously contained by the Set.\n                Object.keys(_this.findChildRefIds(id)).forEach(ids.add, ids);\n                // By removing IDs from the snapshot object here, we protect them from\n                // getting removed from the root store layer below.\n                delete snapshot[id];\n            }\n        });\n        var idsToRemove = Object.keys(snapshot);\n        if (idsToRemove.length) {\n            var root_1 = this;\n            while (root_1 instanceof Layer)\n                root_1 = root_1.parent;\n            idsToRemove.forEach(function (id) { return root_1.delete(id); });\n        }\n        return idsToRemove;\n    };\n    EntityStore.prototype.findChildRefIds = function (dataId) {\n        if (!hasOwn.call(this.refs, dataId)) {\n            var found_1 = (this.refs[dataId] = Object.create(null));\n            var root = this.data[dataId];\n            if (!root)\n                return found_1;\n            var workSet_1 = new Set([root]);\n            // Within the store, only arrays and objects can contain child entity\n            // references, so we can prune the traversal using this predicate:\n            workSet_1.forEach(function (obj) {\n                if (isReference(obj)) {\n                    found_1[obj.__ref] = true;\n                    // In rare cases, a { __ref } Reference object may have other fields.\n                    // This often indicates a mismerging of References with StoreObjects,\n                    // but garbage collection should not be fooled by a stray __ref\n                    // property in a StoreObject (ignoring all the other fields just\n                    // because the StoreObject looks like a Reference). To avoid this\n                    // premature termination of findChildRefIds recursion, we fall through\n                    // to the code below, which will handle any other properties of obj.\n                }\n                if (isNonNullObject(obj)) {\n                    Object.keys(obj).forEach(function (key) {\n                        var child = obj[key];\n                        // No need to add primitive values to the workSet, since they cannot\n                        // contain reference objects.\n                        if (isNonNullObject(child)) {\n                            workSet_1.add(child);\n                        }\n                    });\n                }\n            });\n        }\n        return this.refs[dataId];\n    };\n    EntityStore.prototype.makeCacheKey = function () {\n        return this.group.keyMaker.lookupArray(arguments);\n    };\n    return EntityStore;\n}());\nexport { EntityStore };\n// A single CacheGroup represents a set of one or more EntityStore objects,\n// typically the Root store in a CacheGroup by itself, and all active Layer\n// stores in a group together. A single EntityStore object belongs to only\n// one CacheGroup, store.group. The CacheGroup is responsible for tracking\n// dependencies, so store.group is helpful for generating unique keys for\n// cached results that need to be invalidated when/if those dependencies\n// change. If we used the EntityStore objects themselves as cache keys (that\n// is, store rather than store.group), the cache would become unnecessarily\n// fragmented by all the different Layer objects. Instead, the CacheGroup\n// approach allows all optimistic Layer objects in the same linked list to\n// belong to one CacheGroup, with the non-optimistic Root object belonging\n// to another CacheGroup, allowing resultCaching dependencies to be tracked\n// separately for optimistic and non-optimistic entity data.\nvar CacheGroup = /** @class */ (function () {\n    function CacheGroup(caching, parent) {\n        if (parent === void 0) { parent = null; }\n        this.caching = caching;\n        this.parent = parent;\n        this.d = null;\n        this.resetCaching();\n    }\n    CacheGroup.prototype.resetCaching = function () {\n        this.d = this.caching ? dep() : null;\n        this.keyMaker = new Trie(canUseWeakMap);\n    };\n    CacheGroup.prototype.depend = function (dataId, storeFieldName) {\n        if (this.d) {\n            this.d(makeDepKey(dataId, storeFieldName));\n            var fieldName = fieldNameFromStoreName(storeFieldName);\n            if (fieldName !== storeFieldName) {\n                // Fields with arguments that contribute extra identifying\n                // information to the fieldName (thus forming the storeFieldName)\n                // depend not only on the full storeFieldName but also on the\n                // short fieldName, so the field can be invalidated using either\n                // level of specificity.\n                this.d(makeDepKey(dataId, fieldName));\n            }\n            if (this.parent) {\n                this.parent.depend(dataId, storeFieldName);\n            }\n        }\n    };\n    CacheGroup.prototype.dirty = function (dataId, storeFieldName) {\n        if (this.d) {\n            this.d.dirty(makeDepKey(dataId, storeFieldName), \n            // When storeFieldName === \"__exists\", that means the entity identified\n            // by dataId has either disappeared from the cache or was newly added,\n            // so the result caching system would do well to \"forget everything it\n            // knows\" about that object. To achieve that kind of invalidation, we\n            // not only dirty the associated result cache entry, but also remove it\n            // completely from the dependency graph. For the optimism implementation\n            // details, see https://github.com/benjamn/optimism/pull/195.\n            storeFieldName === \"__exists\" ? \"forget\" : \"setDirty\");\n        }\n    };\n    return CacheGroup;\n}());\nfunction makeDepKey(dataId, storeFieldName) {\n    // Since field names cannot have '#' characters in them, this method\n    // of joining the field name and the ID should be unambiguous, and much\n    // cheaper than JSON.stringify([dataId, fieldName]).\n    return storeFieldName + \"#\" + dataId;\n}\nexport function maybeDependOnExistenceOfEntity(store, entityId) {\n    if (supportsResultCaching(store)) {\n        // We use this pseudo-field __exists elsewhere in the EntityStore code to\n        // represent changes in the existence of the entity object identified by\n        // entityId. This dependency gets reliably dirtied whenever an object with\n        // this ID is deleted (or newly created) within this group, so any result\n        // cache entries (for example, StoreReader#executeSelectionSet results) that\n        // depend on __exists for this entityId will get dirtied as well, leading to\n        // the eventual recomputation (instead of reuse) of those result objects the\n        // next time someone reads them from the cache.\n        store.group.depend(entityId, \"__exists\");\n    }\n}\n(function (EntityStore) {\n    // Refer to this class as EntityStore.Root outside this namespace.\n    var Root = /** @class */ (function (_super) {\n        __extends(Root, _super);\n        function Root(_a) {\n            var policies = _a.policies, _b = _a.resultCaching, resultCaching = _b === void 0 ? true : _b, seed = _a.seed;\n            var _this = _super.call(this, policies, new CacheGroup(resultCaching)) || this;\n            _this.stump = new Stump(_this);\n            _this.storageTrie = new Trie(canUseWeakMap);\n            if (seed)\n                _this.replace(seed);\n            return _this;\n        }\n        Root.prototype.addLayer = function (layerId, replay) {\n            // Adding an optimistic Layer on top of the Root actually adds the Layer\n            // on top of the Stump, so the Stump always comes between the Root and\n            // any Layer objects that we've added.\n            return this.stump.addLayer(layerId, replay);\n        };\n        Root.prototype.removeLayer = function () {\n            // Never remove the root layer.\n            return this;\n        };\n        Root.prototype.getStorage = function () {\n            return this.storageTrie.lookupArray(arguments);\n        };\n        return Root;\n    }(EntityStore));\n    EntityStore.Root = Root;\n})(EntityStore || (EntityStore = {}));\n// Not exported, since all Layer instances are created by the addLayer method\n// of the EntityStore.Root class.\nvar Layer = /** @class */ (function (_super) {\n    __extends(Layer, _super);\n    function Layer(id, parent, replay, group) {\n        var _this = _super.call(this, parent.policies, group) || this;\n        _this.id = id;\n        _this.parent = parent;\n        _this.replay = replay;\n        _this.group = group;\n        replay(_this);\n        return _this;\n    }\n    Layer.prototype.addLayer = function (layerId, replay) {\n        return new Layer(layerId, this, replay, this.group);\n    };\n    Layer.prototype.removeLayer = function (layerId) {\n        var _this = this;\n        // Remove all instances of the given id, not just the first one.\n        var parent = this.parent.removeLayer(layerId);\n        if (layerId === this.id) {\n            if (this.group.caching) {\n                // Dirty every ID we're removing. Technically we might be able to avoid\n                // dirtying fields that have values in higher layers, but we don't have\n                // easy access to higher layers here, and we're about to recreate those\n                // layers anyway (see parent.addLayer below).\n                Object.keys(this.data).forEach(function (dataId) {\n                    var ownStoreObject = _this.data[dataId];\n                    var parentStoreObject = parent[\"lookup\"](dataId);\n                    if (!parentStoreObject) {\n                        // The StoreObject identified by dataId was defined in this layer\n                        // but will be undefined in the parent layer, so we can delete the\n                        // whole entity using this.delete(dataId). Since we're about to\n                        // throw this layer away, the only goal of this deletion is to dirty\n                        // the removed fields.\n                        _this.delete(dataId);\n                    }\n                    else if (!ownStoreObject) {\n                        // This layer had an entry for dataId but it was undefined, which\n                        // means the entity was deleted in this layer, and it's about to\n                        // become undeleted when we remove this layer, so we need to dirty\n                        // all fields that are about to be reexposed.\n                        _this.group.dirty(dataId, \"__exists\");\n                        Object.keys(parentStoreObject).forEach(function (storeFieldName) {\n                            _this.group.dirty(dataId, storeFieldName);\n                        });\n                    }\n                    else if (ownStoreObject !== parentStoreObject) {\n                        // If ownStoreObject is not exactly the same as parentStoreObject,\n                        // dirty any fields whose values will change as a result of this\n                        // removal.\n                        Object.keys(ownStoreObject).forEach(function (storeFieldName) {\n                            if (!equal(ownStoreObject[storeFieldName], parentStoreObject[storeFieldName])) {\n                                _this.group.dirty(dataId, storeFieldName);\n                            }\n                        });\n                    }\n                });\n            }\n            return parent;\n        }\n        // No changes are necessary if the parent chain remains identical.\n        if (parent === this.parent)\n            return this;\n        // Recreate this layer on top of the new parent.\n        return parent.addLayer(this.id, this.replay);\n    };\n    Layer.prototype.toObject = function () {\n        return __assign(__assign({}, this.parent.toObject()), this.data);\n    };\n    Layer.prototype.findChildRefIds = function (dataId) {\n        var fromParent = this.parent.findChildRefIds(dataId);\n        return hasOwn.call(this.data, dataId) ? __assign(__assign({}, fromParent), _super.prototype.findChildRefIds.call(this, dataId)) : fromParent;\n    };\n    Layer.prototype.getStorage = function () {\n        var p = this.parent;\n        while (p.parent)\n            p = p.parent;\n        return p.getStorage.apply(p, \n        // @ts-expect-error\n        arguments);\n    };\n    return Layer;\n}(EntityStore));\n// Represents a Layer permanently installed just above the Root, which allows\n// reading optimistically (and registering optimistic dependencies) even when\n// no optimistic layers are currently active. The stump.group CacheGroup object\n// is shared by any/all Layer objects added on top of the Stump.\nvar Stump = /** @class */ (function (_super) {\n    __extends(Stump, _super);\n    function Stump(root) {\n        return _super.call(this, \"EntityStore.Stump\", root, function () { }, new CacheGroup(root.group.caching, root.group)) || this;\n    }\n    Stump.prototype.removeLayer = function () {\n        // Never remove the Stump layer.\n        return this;\n    };\n    Stump.prototype.merge = function (older, newer) {\n        // We never want to write any data into the Stump, so we forward any merge\n        // calls to the Root instead. Another option here would be to throw an\n        // exception, but the toReference(object, true) function can sometimes\n        // trigger Stump writes (which used to be Root writes, before the Stump\n        // concept was introduced).\n        return this.parent.merge(older, newer);\n    };\n    return Stump;\n}(Layer));\nfunction storeObjectReconciler(existingObject, incomingObject, property) {\n    var existingValue = existingObject[property];\n    var incomingValue = incomingObject[property];\n    // Wherever there is a key collision, prefer the incoming value, unless\n    // it is deeply equal to the existing value. It's worth checking deep\n    // equality here (even though blindly returning incoming would be\n    // logically correct) because preserving the referential identity of\n    // existing data can prevent needless rereading and rerendering.\n    return equal(existingValue, incomingValue) ? existingValue : incomingValue;\n}\nexport function supportsResultCaching(store) {\n    // When result caching is disabled, store.depend will be null.\n    return !!(store instanceof EntityStore && store.group.caching);\n}\n//# sourceMappingURL=entityStore.js.map", "import { __assign } from \"tslib\";\nimport { <PERSON><PERSON> } from \"@wry/trie\";\nimport { canUseWeakMap, canUseWeakSet, isNonNullObject as isObjectOrArray, } from \"../../utilities/index.js\";\nimport { isArray } from \"./helpers.js\";\nfunction shallowCopy(value) {\n    if (isObjectOrArray(value)) {\n        return isArray(value) ?\n            value.slice(0)\n            : __assign({ __proto__: Object.getPrototypeOf(value) }, value);\n    }\n    return value;\n}\n// When programmers talk about the \"canonical form\" of an object, they\n// usually have the following meaning in mind, which I've copied from\n// https://en.wiktionary.org/wiki/canonical_form:\n//\n// 1. A standard or normal presentation of a mathematical entity [or\n//    object]. A canonical form is an element of a set of representatives\n//    of equivalence classes of forms such that there is a function or\n//    procedure which projects every element of each equivalence class\n//    onto that one element, the canonical form of that equivalence\n//    class. The canonical form is expected to be simpler than the rest of\n//    the forms in some way.\n//\n// That's a long-winded way of saying any two objects that have the same\n// canonical form may be considered equivalent, even if they are !==,\n// which usually means the objects are structurally equivalent (deeply\n// equal), but don't necessarily use the same memory.\n//\n// Like a literary or musical canon, this ObjectCanon class represents a\n// collection of unique canonical items (JavaScript objects), with the\n// important property that canon.admit(a) === canon.admit(b) if a and b\n// are deeply equal to each other. In terms of the definition above, the\n// canon.admit method is the \"function or procedure which projects every\"\n// object \"onto that one element, the canonical form.\"\n//\n// In the worst case, the canonicalization process may involve looking at\n// every property in the provided object tree, so it takes the same order\n// of time as deep equality checking. Fortunately, already-canonicalized\n// objects are returned immediately from canon.admit, so the presence of\n// canonical subtrees tends to speed up canonicalization.\n//\n// Since consumers of canonical objects can check for deep equality in\n// constant time, canonicalizing cache results can massively improve the\n// performance of application code that skips re-rendering unchanged\n// results, such as \"pure\" UI components in a framework like React.\n//\n// Of course, since canonical objects may be shared widely between\n// unrelated consumers, it's important to think of them as immutable, even\n// though they are not actually frozen with Object.freeze in production,\n// due to the extra performance overhead that comes with frozen objects.\n//\n// Custom scalar objects whose internal class name is neither Array nor\n// Object can be included safely in the admitted tree, but they will not\n// be replaced with a canonical version (to put it another way, they are\n// assumed to be canonical already).\n//\n// If we ignore custom objects, no detection of cycles or repeated object\n// references is currently required by the StoreReader class, since\n// GraphQL result objects are JSON-serializable trees (and thus contain\n// neither cycles nor repeated subtrees), so we can avoid the complexity\n// of keeping track of objects we've already seen during the recursion of\n// the admit method.\n//\n// In the future, we may consider adding additional cases to the switch\n// statement to handle other common object types, such as \"[object Date]\"\n// objects, as needed.\nvar ObjectCanon = /** @class */ (function () {\n    function ObjectCanon() {\n        // Set of all canonical objects this ObjectCanon has admitted, allowing\n        // canon.admit to return previously-canonicalized objects immediately.\n        this.known = new (canUseWeakSet ? WeakSet : Set)();\n        // Efficient storage/lookup structure for canonical objects.\n        this.pool = new Trie(canUseWeakMap);\n        // Make the ObjectCanon assume this value has already been\n        // canonicalized.\n        this.passes = new WeakMap();\n        // Arrays that contain the same elements in a different order can share\n        // the same SortedKeysInfo object, to save memory.\n        this.keysByJSON = new Map();\n        // This has to come last because it depends on keysByJSON.\n        this.empty = this.admit({});\n    }\n    ObjectCanon.prototype.isKnown = function (value) {\n        return isObjectOrArray(value) && this.known.has(value);\n    };\n    ObjectCanon.prototype.pass = function (value) {\n        if (isObjectOrArray(value)) {\n            var copy = shallowCopy(value);\n            this.passes.set(copy, value);\n            return copy;\n        }\n        return value;\n    };\n    ObjectCanon.prototype.admit = function (value) {\n        var _this = this;\n        if (isObjectOrArray(value)) {\n            var original = this.passes.get(value);\n            if (original)\n                return original;\n            var proto = Object.getPrototypeOf(value);\n            switch (proto) {\n                case Array.prototype: {\n                    if (this.known.has(value))\n                        return value;\n                    var array = value.map(this.admit, this);\n                    // Arrays are looked up in the Trie using their recursively\n                    // canonicalized elements, and the known version of the array is\n                    // preserved as node.array.\n                    var node = this.pool.lookupArray(array);\n                    if (!node.array) {\n                        this.known.add((node.array = array));\n                        // Since canonical arrays may be shared widely between\n                        // unrelated consumers, it's important to regard them as\n                        // immutable, even if they are not frozen in production.\n                        if (globalThis.__DEV__ !== false) {\n                            Object.freeze(array);\n                        }\n                    }\n                    return node.array;\n                }\n                case null:\n                case Object.prototype: {\n                    if (this.known.has(value))\n                        return value;\n                    var proto_1 = Object.getPrototypeOf(value);\n                    var array_1 = [proto_1];\n                    var keys = this.sortedKeys(value);\n                    array_1.push(keys.json);\n                    var firstValueIndex_1 = array_1.length;\n                    keys.sorted.forEach(function (key) {\n                        array_1.push(_this.admit(value[key]));\n                    });\n                    // Objects are looked up in the Trie by their prototype (which\n                    // is *not* recursively canonicalized), followed by a JSON\n                    // representation of their (sorted) keys, followed by the\n                    // sequence of recursively canonicalized values corresponding to\n                    // those keys. To keep the final results unambiguous with other\n                    // sequences (such as arrays that just happen to contain [proto,\n                    // keys.json, value1, value2, ...]), the known version of the\n                    // object is stored as node.object.\n                    var node = this.pool.lookupArray(array_1);\n                    if (!node.object) {\n                        var obj_1 = (node.object = Object.create(proto_1));\n                        this.known.add(obj_1);\n                        keys.sorted.forEach(function (key, i) {\n                            obj_1[key] = array_1[firstValueIndex_1 + i];\n                        });\n                        // Since canonical objects may be shared widely between\n                        // unrelated consumers, it's important to regard them as\n                        // immutable, even if they are not frozen in production.\n                        if (globalThis.__DEV__ !== false) {\n                            Object.freeze(obj_1);\n                        }\n                    }\n                    return node.object;\n                }\n            }\n        }\n        return value;\n    };\n    // It's worthwhile to cache the sorting of arrays of strings, since the\n    // same initial unsorted arrays tend to be encountered many times.\n    // Fortunately, we can reuse the Trie machinery to look up the sorted\n    // arrays in linear time (which is faster than sorting large arrays).\n    ObjectCanon.prototype.sortedKeys = function (obj) {\n        var keys = Object.keys(obj);\n        var node = this.pool.lookupArray(keys);\n        if (!node.keys) {\n            keys.sort();\n            var json = JSON.stringify(keys);\n            if (!(node.keys = this.keysByJSON.get(json))) {\n                this.keysByJSON.set(json, (node.keys = { sorted: keys, json: json }));\n            }\n        }\n        return node.keys;\n    };\n    return ObjectCanon;\n}());\nexport { ObjectCanon };\n//# sourceMappingURL=object-canon.js.map", "import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { Kind } from \"graphql\";\nimport { wrap } from \"optimism\";\nimport { isField, resultKeyNameFromField, isReference, makeReference, shouldInclude, addTypenameToDocument, getDefaultValues, getMainDefinition, getQueryDefinition, getFragmentFromSelection, maybeDeepFreeze, mergeDeepArray, DeepMerger, isNonNullObject, canUseWeakMap, compact, canonicalStringify, cacheSizes, } from \"../../utilities/index.js\";\nimport { maybeDependOnExistenceOfEntity, supportsResultCaching, } from \"./entityStore.js\";\nimport { isArray, extractFragmentContext, getTypenameFromStoreObject, shouldCanonizeResults, } from \"./helpers.js\";\nimport { MissingFieldError } from \"../core/types/common.js\";\nimport { ObjectCanon } from \"./object-canon.js\";\nfunction execSelectionSetKeyArgs(options) {\n    return [\n        options.selectionSet,\n        options.objectOrReference,\n        options.context,\n        // We split out this property so we can pass different values\n        // independently without modifying options.context itself.\n        options.context.canonizeResults,\n    ];\n}\nvar StoreReader = /** @class */ (function () {\n    function StoreReader(config) {\n        var _this = this;\n        this.knownResults = new (canUseWeakMap ? WeakMap : Map)();\n        this.config = compact(config, {\n            addTypename: config.addTypename !== false,\n            canonizeResults: shouldCanonizeResults(config),\n        });\n        this.canon = config.canon || new ObjectCanon();\n        // memoized functions in this class will be \"garbage-collected\"\n        // by recreating the whole `StoreReader` in\n        // `InMemoryCache.resetResultsCache`\n        // (triggered from `InMemoryCache.gc` with `resetResultCache: true`)\n        this.executeSelectionSet = wrap(function (options) {\n            var _a;\n            var canonizeResults = options.context.canonizeResults;\n            var peekArgs = execSelectionSetKeyArgs(options);\n            // Negate this boolean option so we can find out if we've already read\n            // this result using the other boolean value.\n            peekArgs[3] = !canonizeResults;\n            var other = (_a = _this.executeSelectionSet).peek.apply(_a, peekArgs);\n            if (other) {\n                if (canonizeResults) {\n                    return __assign(__assign({}, other), { \n                        // If we previously read this result without canonizing it, we can\n                        // reuse that result simply by canonizing it now.\n                        result: _this.canon.admit(other.result) });\n                }\n                // If we previously read this result with canonization enabled, we can\n                // return that canonized result as-is.\n                return other;\n            }\n            maybeDependOnExistenceOfEntity(options.context.store, options.enclosingRef.__ref);\n            // Finally, if we didn't find any useful previous results, run the real\n            // execSelectionSetImpl method with the given options.\n            return _this.execSelectionSetImpl(options);\n        }, {\n            max: this.config.resultCacheMaxSize ||\n                cacheSizes[\"inMemoryCache.executeSelectionSet\"] ||\n                50000 /* defaultCacheSizes[\"inMemoryCache.executeSelectionSet\"] */,\n            keyArgs: execSelectionSetKeyArgs,\n            // Note that the parameters of makeCacheKey are determined by the\n            // array returned by keyArgs.\n            makeCacheKey: function (selectionSet, parent, context, canonizeResults) {\n                if (supportsResultCaching(context.store)) {\n                    return context.store.makeCacheKey(selectionSet, isReference(parent) ? parent.__ref : parent, context.varString, canonizeResults);\n                }\n            },\n        });\n        this.executeSubSelectedArray = wrap(function (options) {\n            maybeDependOnExistenceOfEntity(options.context.store, options.enclosingRef.__ref);\n            return _this.execSubSelectedArrayImpl(options);\n        }, {\n            max: this.config.resultCacheMaxSize ||\n                cacheSizes[\"inMemoryCache.executeSubSelectedArray\"] ||\n                10000 /* defaultCacheSizes[\"inMemoryCache.executeSubSelectedArray\"] */,\n            makeCacheKey: function (_a) {\n                var field = _a.field, array = _a.array, context = _a.context;\n                if (supportsResultCaching(context.store)) {\n                    return context.store.makeCacheKey(field, array, context.varString);\n                }\n            },\n        });\n    }\n    StoreReader.prototype.resetCanon = function () {\n        this.canon = new ObjectCanon();\n    };\n    /**\n     * Given a store and a query, return as much of the result as possible and\n     * identify if any data was missing from the store.\n     */\n    StoreReader.prototype.diffQueryAgainstStore = function (_a) {\n        var store = _a.store, query = _a.query, _b = _a.rootId, rootId = _b === void 0 ? \"ROOT_QUERY\" : _b, variables = _a.variables, _c = _a.returnPartialData, returnPartialData = _c === void 0 ? true : _c, _d = _a.canonizeResults, canonizeResults = _d === void 0 ? this.config.canonizeResults : _d;\n        var policies = this.config.cache.policies;\n        variables = __assign(__assign({}, getDefaultValues(getQueryDefinition(query))), variables);\n        var rootRef = makeReference(rootId);\n        var execResult = this.executeSelectionSet({\n            selectionSet: getMainDefinition(query).selectionSet,\n            objectOrReference: rootRef,\n            enclosingRef: rootRef,\n            context: __assign({ store: store, query: query, policies: policies, variables: variables, varString: canonicalStringify(variables), canonizeResults: canonizeResults }, extractFragmentContext(query, this.config.fragments)),\n        });\n        var missing;\n        if (execResult.missing) {\n            // For backwards compatibility we still report an array of\n            // MissingFieldError objects, even though there will only ever be at most\n            // one of them, now that all missing field error messages are grouped\n            // together in the execResult.missing tree.\n            missing = [\n                new MissingFieldError(firstMissing(execResult.missing), execResult.missing, query, variables),\n            ];\n            if (!returnPartialData) {\n                throw missing[0];\n            }\n        }\n        return {\n            result: execResult.result,\n            complete: !missing,\n            missing: missing,\n        };\n    };\n    StoreReader.prototype.isFresh = function (result, parent, selectionSet, context) {\n        if (supportsResultCaching(context.store) &&\n            this.knownResults.get(result) === selectionSet) {\n            var latest = this.executeSelectionSet.peek(selectionSet, parent, context, \n            // If result is canonical, then it could only have been previously\n            // cached by the canonizing version of executeSelectionSet, so we can\n            // avoid checking both possibilities here.\n            this.canon.isKnown(result));\n            if (latest && result === latest.result) {\n                return true;\n            }\n        }\n        return false;\n    };\n    // Uncached version of executeSelectionSet.\n    StoreReader.prototype.execSelectionSetImpl = function (_a) {\n        var _this = this;\n        var selectionSet = _a.selectionSet, objectOrReference = _a.objectOrReference, enclosingRef = _a.enclosingRef, context = _a.context;\n        if (isReference(objectOrReference) &&\n            !context.policies.rootTypenamesById[objectOrReference.__ref] &&\n            !context.store.has(objectOrReference.__ref)) {\n            return {\n                result: this.canon.empty,\n                missing: \"Dangling reference to missing \".concat(objectOrReference.__ref, \" object\"),\n            };\n        }\n        var variables = context.variables, policies = context.policies, store = context.store;\n        var typename = store.getFieldValue(objectOrReference, \"__typename\");\n        var objectsToMerge = [];\n        var missing;\n        var missingMerger = new DeepMerger();\n        if (this.config.addTypename &&\n            typeof typename === \"string\" &&\n            !policies.rootIdsByTypename[typename]) {\n            // Ensure we always include a default value for the __typename\n            // field, if we have one, and this.config.addTypename is true. Note\n            // that this field can be overridden by other merged objects.\n            objectsToMerge.push({ __typename: typename });\n        }\n        function handleMissing(result, resultName) {\n            var _a;\n            if (result.missing) {\n                missing = missingMerger.merge(missing, (_a = {},\n                    _a[resultName] = result.missing,\n                    _a));\n            }\n            return result.result;\n        }\n        var workSet = new Set(selectionSet.selections);\n        workSet.forEach(function (selection) {\n            var _a, _b;\n            // Omit fields with directives @skip(if: <truthy value>) or\n            // @include(if: <falsy value>).\n            if (!shouldInclude(selection, variables))\n                return;\n            if (isField(selection)) {\n                var fieldValue = policies.readField({\n                    fieldName: selection.name.value,\n                    field: selection,\n                    variables: context.variables,\n                    from: objectOrReference,\n                }, context);\n                var resultName = resultKeyNameFromField(selection);\n                if (fieldValue === void 0) {\n                    if (!addTypenameToDocument.added(selection)) {\n                        missing = missingMerger.merge(missing, (_a = {},\n                            _a[resultName] = \"Can't find field '\".concat(selection.name.value, \"' on \").concat(isReference(objectOrReference) ?\n                                objectOrReference.__ref + \" object\"\n                                : \"object \" + JSON.stringify(objectOrReference, null, 2)),\n                            _a));\n                    }\n                }\n                else if (isArray(fieldValue)) {\n                    if (fieldValue.length > 0) {\n                        fieldValue = handleMissing(_this.executeSubSelectedArray({\n                            field: selection,\n                            array: fieldValue,\n                            enclosingRef: enclosingRef,\n                            context: context,\n                        }), resultName);\n                    }\n                }\n                else if (!selection.selectionSet) {\n                    // If the field does not have a selection set, then we handle it\n                    // as a scalar value. To keep this.canon from canonicalizing\n                    // this value, we use this.canon.pass to wrap fieldValue in a\n                    // Pass object that this.canon.admit will later unwrap as-is.\n                    if (context.canonizeResults) {\n                        fieldValue = _this.canon.pass(fieldValue);\n                    }\n                }\n                else if (fieldValue != null) {\n                    // In this case, because we know the field has a selection set,\n                    // it must be trying to query a GraphQLObjectType, which is why\n                    // fieldValue must be != null.\n                    fieldValue = handleMissing(_this.executeSelectionSet({\n                        selectionSet: selection.selectionSet,\n                        objectOrReference: fieldValue,\n                        enclosingRef: isReference(fieldValue) ? fieldValue : enclosingRef,\n                        context: context,\n                    }), resultName);\n                }\n                if (fieldValue !== void 0) {\n                    objectsToMerge.push((_b = {}, _b[resultName] = fieldValue, _b));\n                }\n            }\n            else {\n                var fragment = getFragmentFromSelection(selection, context.lookupFragment);\n                if (!fragment && selection.kind === Kind.FRAGMENT_SPREAD) {\n                    throw newInvariantError(10, selection.name.value);\n                }\n                if (fragment && policies.fragmentMatches(fragment, typename)) {\n                    fragment.selectionSet.selections.forEach(workSet.add, workSet);\n                }\n            }\n        });\n        var result = mergeDeepArray(objectsToMerge);\n        var finalResult = { result: result, missing: missing };\n        var frozen = context.canonizeResults ?\n            this.canon.admit(finalResult)\n            // Since this.canon is normally responsible for freezing results (only in\n            // development), freeze them manually if canonization is disabled.\n            : maybeDeepFreeze(finalResult);\n        // Store this result with its selection set so that we can quickly\n        // recognize it again in the StoreReader#isFresh method.\n        if (frozen.result) {\n            this.knownResults.set(frozen.result, selectionSet);\n        }\n        return frozen;\n    };\n    // Uncached version of executeSubSelectedArray.\n    StoreReader.prototype.execSubSelectedArrayImpl = function (_a) {\n        var _this = this;\n        var field = _a.field, array = _a.array, enclosingRef = _a.enclosingRef, context = _a.context;\n        var missing;\n        var missingMerger = new DeepMerger();\n        function handleMissing(childResult, i) {\n            var _a;\n            if (childResult.missing) {\n                missing = missingMerger.merge(missing, (_a = {}, _a[i] = childResult.missing, _a));\n            }\n            return childResult.result;\n        }\n        if (field.selectionSet) {\n            array = array.filter(context.store.canRead);\n        }\n        array = array.map(function (item, i) {\n            // null value in array\n            if (item === null) {\n                return null;\n            }\n            // This is a nested array, recurse\n            if (isArray(item)) {\n                return handleMissing(_this.executeSubSelectedArray({\n                    field: field,\n                    array: item,\n                    enclosingRef: enclosingRef,\n                    context: context,\n                }), i);\n            }\n            // This is an object, run the selection set on it\n            if (field.selectionSet) {\n                return handleMissing(_this.executeSelectionSet({\n                    selectionSet: field.selectionSet,\n                    objectOrReference: item,\n                    enclosingRef: isReference(item) ? item : enclosingRef,\n                    context: context,\n                }), i);\n            }\n            if (globalThis.__DEV__ !== false) {\n                assertSelectionSetForIdValue(context.store, field, item);\n            }\n            return item;\n        });\n        return {\n            result: context.canonizeResults ? this.canon.admit(array) : array,\n            missing: missing,\n        };\n    };\n    return StoreReader;\n}());\nexport { StoreReader };\nfunction firstMissing(tree) {\n    try {\n        JSON.stringify(tree, function (_, value) {\n            if (typeof value === \"string\")\n                throw value;\n            return value;\n        });\n    }\n    catch (result) {\n        return result;\n    }\n}\nfunction assertSelectionSetForIdValue(store, field, fieldValue) {\n    if (!field.selectionSet) {\n        var workSet_1 = new Set([fieldValue]);\n        workSet_1.forEach(function (value) {\n            if (isNonNullObject(value)) {\n                invariant(\n                    !isReference(value),\n                    11,\n                    getTypenameFromStoreObject(store, value),\n                    field.name.value\n                );\n                Object.values(value).forEach(workSet_1.add, workSet_1);\n            }\n        });\n    }\n}\n//# sourceMappingURL=readFromStore.js.map", "import { dep, Slot } from \"optimism\";\n// Contextual Slot that acquires its value when custom read functions are\n// called in Policies#readField.\nexport var cacheSlot = new Slot();\nvar cacheInfoMap = new WeakMap();\nfunction getCacheInfo(cache) {\n    var info = cacheInfoMap.get(cache);\n    if (!info) {\n        cacheInfoMap.set(cache, (info = {\n            vars: new Set(),\n            dep: dep(),\n        }));\n    }\n    return info;\n}\nexport function forgetCache(cache) {\n    getCacheInfo(cache).vars.forEach(function (rv) { return rv.forgetCache(cache); });\n}\n// Calling forgetCache(cache) serves to silence broadcasts and allows the\n// cache to be garbage collected. However, the varsByCache WeakMap\n// preserves the set of reactive variables that were previously associated\n// with this cache, which makes it possible to \"recall\" the cache at a\n// later time, by reattaching it to those variables. If the cache has been\n// garbage collected in the meantime, because it is no longer reachable,\n// you won't be able to call recallCache(cache), and the cache will\n// automatically disappear from the varsByCache WeakMap.\nexport function recallCache(cache) {\n    getCacheInfo(cache).vars.forEach(function (rv) { return rv.attachCache(cache); });\n}\nexport function makeVar(value) {\n    var caches = new Set();\n    var listeners = new Set();\n    var rv = function (newValue) {\n        if (arguments.length > 0) {\n            if (value !== newValue) {\n                value = newValue;\n                caches.forEach(function (cache) {\n                    // Invalidate any fields with custom read functions that\n                    // consumed this variable, so query results involving those\n                    // fields will be recomputed the next time we read them.\n                    getCacheInfo(cache).dep.dirty(rv);\n                    // Broadcast changes to any caches that have previously read\n                    // from this variable.\n                    broadcast(cache);\n                });\n                // Finally, notify any listeners added via rv.onNextChange.\n                var oldListeners = Array.from(listeners);\n                listeners.clear();\n                oldListeners.forEach(function (listener) { return listener(value); });\n            }\n        }\n        else {\n            // When reading from the variable, obtain the current cache from\n            // context via cacheSlot. This isn't entirely foolproof, but it's\n            // the same system that powers varDep.\n            var cache = cacheSlot.getValue();\n            if (cache) {\n                attach(cache);\n                getCacheInfo(cache).dep(rv);\n            }\n        }\n        return value;\n    };\n    rv.onNextChange = function (listener) {\n        listeners.add(listener);\n        return function () {\n            listeners.delete(listener);\n        };\n    };\n    var attach = (rv.attachCache = function (cache) {\n        caches.add(cache);\n        getCacheInfo(cache).vars.add(rv);\n        return rv;\n    });\n    rv.forgetCache = function (cache) { return caches.delete(cache); };\n    return rv;\n}\nfunction broadcast(cache) {\n    if (cache.broadcastWatches) {\n        cache.broadcastWatches();\n    }\n}\n//# sourceMappingURL=reactiveVars.js.map", "import { invariant } from \"../../utilities/globals/index.js\";\nimport { argumentsO<PERSON><PERSON><PERSON><PERSON><PERSON>, DeepMerger, isNonEmptyArray, isNonNullObject, } from \"../../utilities/index.js\";\nimport { hasOwn, isArray } from \"./helpers.js\";\n// Mapping from JSON-encoded KeySpecifier strings to associated information.\nvar specifierInfoCache = Object.create(null);\nfunction lookupSpecifierInfo(spec) {\n    // It's safe to encode KeySpecifier arrays with JSON.stringify, since they're\n    // just arrays of strings or nested KeySpecifier arrays, and the order of the\n    // array elements is important (and suitably preserved by JSON.stringify).\n    var cacheKey = JSON.stringify(spec);\n    return (specifierInfoCache[cacheKey] ||\n        (specifierInfoCache[cacheKey] = Object.create(null)));\n}\nexport function keyFieldsFnFromSpecifier(specifier) {\n    var info = lookupSpecifierInfo(specifier);\n    return (info.keyFieldsFn || (info.keyFieldsFn = function (object, context) {\n            var extract = function (from, key) {\n                return context.readField(key, from);\n            };\n            var keyObject = (context.keyObject = collectSpecifierPaths(specifier, function (schemaKeyPath) {\n                var extracted = extractKeyPath(context.storeObject, schemaKeyPath, \n                // Using context.readField to extract paths from context.storeObject\n                // allows the extraction to see through Reference objects and respect\n                // custom read functions.\n                extract);\n                if (extracted === void 0 &&\n                    object !== context.storeObject &&\n                    hasOwn.call(object, schemaKeyPath[0])) {\n                    // If context.storeObject fails to provide a value for the requested\n                    // path, fall back to the raw result object, if it has a top-level key\n                    // matching the first key in the path (schemaKeyPath[0]). This allows\n                    // key fields included in the written data to be saved in the cache\n                    // even if they are not selected explicitly in context.selectionSet.\n                    // Not being mentioned by context.selectionSet is convenient here,\n                    // since it means these extra fields cannot be affected by field\n                    // aliasing, which is why we can use extractKey instead of\n                    // context.readField for this extraction.\n                    extracted = extractKeyPath(object, schemaKeyPath, extractKey);\n                }\n                invariant(extracted !== void 0, 5, schemaKeyPath.join(\".\"), object);\n                return extracted;\n            }));\n            return \"\".concat(context.typename, \":\").concat(JSON.stringify(keyObject));\n        }));\n}\n// The keyArgs extraction process is roughly analogous to keyFields extraction,\n// but there are no aliases involved, missing fields are tolerated (by merely\n// omitting them from the key), and drawing from field.directives or variables\n// is allowed (in addition to drawing from the field's arguments object).\n// Concretely, these differences mean passing a different key path extractor\n// function to collectSpecifierPaths, reusing the shared extractKeyPath helper\n// wherever possible.\nexport function keyArgsFnFromSpecifier(specifier) {\n    var info = lookupSpecifierInfo(specifier);\n    return (info.keyArgsFn ||\n        (info.keyArgsFn = function (args, _a) {\n            var field = _a.field, variables = _a.variables, fieldName = _a.fieldName;\n            var collected = collectSpecifierPaths(specifier, function (keyPath) {\n                var firstKey = keyPath[0];\n                var firstChar = firstKey.charAt(0);\n                if (firstChar === \"@\") {\n                    if (field && isNonEmptyArray(field.directives)) {\n                        var directiveName_1 = firstKey.slice(1);\n                        // If the directive appears multiple times, only the first\n                        // occurrence's arguments will be used. TODO Allow repetition?\n                        // TODO Cache this work somehow, a la aliasMap?\n                        var d = field.directives.find(function (d) { return d.name.value === directiveName_1; });\n                        // Fortunately argumentsObjectFromField works for DirectiveNode!\n                        var directiveArgs = d && argumentsObjectFromField(d, variables);\n                        // For directives without arguments (d defined, but directiveArgs ===\n                        // null), the presence or absence of the directive still counts as\n                        // part of the field key, so we return null in those cases. If no\n                        // directive with this name was found for this field (d undefined and\n                        // thus directiveArgs undefined), we return undefined, which causes\n                        // this value to be omitted from the key object returned by\n                        // collectSpecifierPaths.\n                        return (directiveArgs &&\n                            extractKeyPath(directiveArgs, \n                            // If keyPath.length === 1, this code calls extractKeyPath with an\n                            // empty path, which works because it uses directiveArgs as the\n                            // extracted value.\n                            keyPath.slice(1)));\n                    }\n                    // If the key started with @ but there was no corresponding directive,\n                    // we want to omit this value from the key object, not fall through to\n                    // treating @whatever as a normal argument name.\n                    return;\n                }\n                if (firstChar === \"$\") {\n                    var variableName = firstKey.slice(1);\n                    if (variables && hasOwn.call(variables, variableName)) {\n                        var varKeyPath = keyPath.slice(0);\n                        varKeyPath[0] = variableName;\n                        return extractKeyPath(variables, varKeyPath);\n                    }\n                    // If the key started with $ but there was no corresponding variable, we\n                    // want to omit this value from the key object, not fall through to\n                    // treating $whatever as a normal argument name.\n                    return;\n                }\n                if (args) {\n                    return extractKeyPath(args, keyPath);\n                }\n            });\n            var suffix = JSON.stringify(collected);\n            // If no arguments were passed to this field, and it didn't have any other\n            // field key contributions from directives or variables, hide the empty\n            // :{} suffix from the field key. However, a field passed no arguments can\n            // still end up with a non-empty :{...} suffix if its key configuration\n            // refers to directives or variables.\n            if (args || suffix !== \"{}\") {\n                fieldName += \":\" + suffix;\n            }\n            return fieldName;\n        }));\n}\nexport function collectSpecifierPaths(specifier, extractor) {\n    // For each path specified by specifier, invoke the extractor, and repeatedly\n    // merge the results together, with appropriate ancestor context.\n    var merger = new DeepMerger();\n    return getSpecifierPaths(specifier).reduce(function (collected, path) {\n        var _a;\n        var toMerge = extractor(path);\n        if (toMerge !== void 0) {\n            // This path is not expected to contain array indexes, so the toMerge\n            // reconstruction will not contain arrays. TODO Fix this?\n            for (var i = path.length - 1; i >= 0; --i) {\n                toMerge = (_a = {}, _a[path[i]] = toMerge, _a);\n            }\n            collected = merger.merge(collected, toMerge);\n        }\n        return collected;\n    }, Object.create(null));\n}\nexport function getSpecifierPaths(spec) {\n    var info = lookupSpecifierInfo(spec);\n    if (!info.paths) {\n        var paths_1 = (info.paths = []);\n        var currentPath_1 = [];\n        spec.forEach(function (s, i) {\n            if (isArray(s)) {\n                getSpecifierPaths(s).forEach(function (p) { return paths_1.push(currentPath_1.concat(p)); });\n                currentPath_1.length = 0;\n            }\n            else {\n                currentPath_1.push(s);\n                if (!isArray(spec[i + 1])) {\n                    paths_1.push(currentPath_1.slice(0));\n                    currentPath_1.length = 0;\n                }\n            }\n        });\n    }\n    return info.paths;\n}\nfunction extractKey(object, key) {\n    return object[key];\n}\nexport function extractKeyPath(object, path, extract) {\n    // For each key in path, extract the corresponding child property from obj,\n    // flattening arrays if encountered (uncommon for keyFields and keyArgs, but\n    // possible). The final result of path.reduce is normalized so unexpected leaf\n    // objects have their keys safely sorted. That final result is difficult to\n    // type as anything other than any. You're welcome to try to improve the\n    // return type, but keep in mind extractKeyPath is not a public function\n    // (exported only for testing), so the effort may not be worthwhile unless the\n    // limited set of actual callers (see above) pass arguments that TypeScript\n    // can statically type. If we know only that path is some array of strings\n    // (and not, say, a specific tuple of statically known strings), any (or\n    // possibly unknown) is the honest answer.\n    extract = extract || extractKey;\n    return normalize(path.reduce(function reducer(obj, key) {\n        return isArray(obj) ?\n            obj.map(function (child) { return reducer(child, key); })\n            : obj && extract(obj, key);\n    }, object));\n}\nfunction normalize(value) {\n    // Usually the extracted value will be a scalar value, since most primary\n    // key fields are scalar, but just in case we get an object or an array, we\n    // need to do some normalization of the order of (nested) keys.\n    if (isNonNullObject(value)) {\n        if (isArray(value)) {\n            return value.map(normalize);\n        }\n        return collectSpecifierPaths(Object.keys(value).sort(), function (path) {\n            return extractKeyPath(value, path);\n        });\n    }\n    return value;\n}\n//# sourceMappingURL=key-extractor.js.map", "import { __assign, __rest } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { storeKeyNameFromField, argumentsObjectFromField, isReference, getStoreKeyName, isNonNullObject, stringifyForDisplay, } from \"../../utilities/index.js\";\nimport { hasOwn, fieldNameFromStoreName, storeValueIsStoreObject, selectionSetMatchesResult, TypeOrFieldNameRegExp, defaultDataIdFromObject, isArray, } from \"./helpers.js\";\nimport { cacheSlot } from \"./reactiveVars.js\";\nimport { keyArgsFnFromSpecifier, keyFieldsFnFromSpecifier, } from \"./key-extractor.js\";\nimport { disableWarningsSlot } from \"../../masking/index.js\";\nfunction argsFromFieldSpecifier(spec) {\n    return (spec.args !== void 0 ? spec.args\n        : spec.field ? argumentsObjectFromField(spec.field, spec.variables)\n            : null);\n}\nvar nullKeyFieldsFn = function () { return void 0; };\nvar simpleKeyArgsFn = function (_args, context) { return context.fieldName; };\n// These merge functions can be selected by specifying merge:true or\n// merge:false in a field policy.\nvar mergeTrueFn = function (existing, incoming, _a) {\n    var mergeObjects = _a.mergeObjects;\n    return mergeObjects(existing, incoming);\n};\nvar mergeFalseFn = function (_, incoming) { return incoming; };\nvar Policies = /** @class */ (function () {\n    function Policies(config) {\n        this.config = config;\n        this.typePolicies = Object.create(null);\n        this.toBeAdded = Object.create(null);\n        // Map from subtype names to sets of supertype names. Note that this\n        // representation inverts the structure of possibleTypes (whose keys are\n        // supertypes and whose values are arrays of subtypes) because it tends\n        // to be much more efficient to search upwards than downwards.\n        this.supertypeMap = new Map();\n        // Any fuzzy subtypes specified by possibleTypes will be converted to\n        // RegExp objects and recorded here. Every key of this map can also be\n        // found in supertypeMap. In many cases this Map will be empty, which\n        // means no fuzzy subtype checking will happen in fragmentMatches.\n        this.fuzzySubtypes = new Map();\n        this.rootIdsByTypename = Object.create(null);\n        this.rootTypenamesById = Object.create(null);\n        this.usingPossibleTypes = false;\n        this.config = __assign({ dataIdFromObject: defaultDataIdFromObject }, config);\n        this.cache = this.config.cache;\n        this.setRootTypename(\"Query\");\n        this.setRootTypename(\"Mutation\");\n        this.setRootTypename(\"Subscription\");\n        if (config.possibleTypes) {\n            this.addPossibleTypes(config.possibleTypes);\n        }\n        if (config.typePolicies) {\n            this.addTypePolicies(config.typePolicies);\n        }\n    }\n    Policies.prototype.identify = function (object, partialContext) {\n        var _a;\n        var policies = this;\n        var typename = (partialContext &&\n            (partialContext.typename || ((_a = partialContext.storeObject) === null || _a === void 0 ? void 0 : _a.__typename))) ||\n            object.__typename;\n        // It should be possible to write root Query fields with writeFragment,\n        // using { __typename: \"Query\", ... } as the data, but it does not make\n        // sense to allow the same identification behavior for the Mutation and\n        // Subscription types, since application code should never be writing\n        // directly to (or reading directly from) those root objects.\n        if (typename === this.rootTypenamesById.ROOT_QUERY) {\n            return [\"ROOT_QUERY\"];\n        }\n        // Default context.storeObject to object if not otherwise provided.\n        var storeObject = (partialContext && partialContext.storeObject) || object;\n        var context = __assign(__assign({}, partialContext), { typename: typename, storeObject: storeObject, readField: (partialContext && partialContext.readField) ||\n                function () {\n                    var options = normalizeReadFieldOptions(arguments, storeObject);\n                    return policies.readField(options, {\n                        store: policies.cache[\"data\"],\n                        variables: options.variables,\n                    });\n                } });\n        var id;\n        var policy = typename && this.getTypePolicy(typename);\n        var keyFn = (policy && policy.keyFn) || this.config.dataIdFromObject;\n        disableWarningsSlot.withValue(true, function () {\n            while (keyFn) {\n                var specifierOrId = keyFn(__assign(__assign({}, object), storeObject), context);\n                if (isArray(specifierOrId)) {\n                    keyFn = keyFieldsFnFromSpecifier(specifierOrId);\n                }\n                else {\n                    id = specifierOrId;\n                    break;\n                }\n            }\n        });\n        id = id ? String(id) : void 0;\n        return context.keyObject ? [id, context.keyObject] : [id];\n    };\n    Policies.prototype.addTypePolicies = function (typePolicies) {\n        var _this = this;\n        Object.keys(typePolicies).forEach(function (typename) {\n            var _a = typePolicies[typename], queryType = _a.queryType, mutationType = _a.mutationType, subscriptionType = _a.subscriptionType, incoming = __rest(_a, [\"queryType\", \"mutationType\", \"subscriptionType\"]);\n            // Though {query,mutation,subscription}Type configurations are rare,\n            // it's important to call setRootTypename as early as possible,\n            // since these configurations should apply consistently for the\n            // entire lifetime of the cache. Also, since only one __typename can\n            // qualify as one of these root types, these three properties cannot\n            // be inherited, unlike the rest of the incoming properties. That\n            // restriction is convenient, because the purpose of this.toBeAdded\n            // is to delay the processing of type/field policies until the first\n            // time they're used, allowing policies to be added in any order as\n            // long as all relevant policies (including policies for supertypes)\n            // have been added by the time a given policy is used for the first\n            // time. In other words, since inheritance doesn't matter for these\n            // properties, there's also no need to delay their processing using\n            // the this.toBeAdded queue.\n            if (queryType)\n                _this.setRootTypename(\"Query\", typename);\n            if (mutationType)\n                _this.setRootTypename(\"Mutation\", typename);\n            if (subscriptionType)\n                _this.setRootTypename(\"Subscription\", typename);\n            if (hasOwn.call(_this.toBeAdded, typename)) {\n                _this.toBeAdded[typename].push(incoming);\n            }\n            else {\n                _this.toBeAdded[typename] = [incoming];\n            }\n        });\n    };\n    Policies.prototype.updateTypePolicy = function (typename, incoming) {\n        var _this = this;\n        var existing = this.getTypePolicy(typename);\n        var keyFields = incoming.keyFields, fields = incoming.fields;\n        function setMerge(existing, merge) {\n            existing.merge =\n                typeof merge === \"function\" ? merge\n                    // Pass merge:true as a shorthand for a merge implementation\n                    // that returns options.mergeObjects(existing, incoming).\n                    : merge === true ? mergeTrueFn\n                        // Pass merge:false to make incoming always replace existing\n                        // without any warnings about data clobbering.\n                        : merge === false ? mergeFalseFn\n                            : existing.merge;\n        }\n        // Type policies can define merge functions, as an alternative to\n        // using field policies to merge child objects.\n        setMerge(existing, incoming.merge);\n        existing.keyFn =\n            // Pass false to disable normalization for this typename.\n            keyFields === false ? nullKeyFieldsFn\n                // Pass an array of strings to use those fields to compute a\n                // composite ID for objects of this typename.\n                : isArray(keyFields) ? keyFieldsFnFromSpecifier(keyFields)\n                    // Pass a function to take full control over identification.\n                    : typeof keyFields === \"function\" ? keyFields\n                        // Leave existing.keyFn unchanged if above cases fail.\n                        : existing.keyFn;\n        if (fields) {\n            Object.keys(fields).forEach(function (fieldName) {\n                var existing = _this.getFieldPolicy(typename, fieldName, true);\n                var incoming = fields[fieldName];\n                if (typeof incoming === \"function\") {\n                    existing.read = incoming;\n                }\n                else {\n                    var keyArgs = incoming.keyArgs, read = incoming.read, merge = incoming.merge;\n                    existing.keyFn =\n                        // Pass false to disable argument-based differentiation of\n                        // field identities.\n                        keyArgs === false ? simpleKeyArgsFn\n                            // Pass an array of strings to use named arguments to\n                            // compute a composite identity for the field.\n                            : isArray(keyArgs) ? keyArgsFnFromSpecifier(keyArgs)\n                                // Pass a function to take full control over field identity.\n                                : typeof keyArgs === \"function\" ? keyArgs\n                                    // Leave existing.keyFn unchanged if above cases fail.\n                                    : existing.keyFn;\n                    if (typeof read === \"function\") {\n                        existing.read = read;\n                    }\n                    setMerge(existing, merge);\n                }\n                if (existing.read && existing.merge) {\n                    // If we have both a read and a merge function, assume\n                    // keyArgs:false, because read and merge together can take\n                    // responsibility for interpreting arguments in and out. This\n                    // default assumption can always be overridden by specifying\n                    // keyArgs explicitly in the FieldPolicy.\n                    existing.keyFn = existing.keyFn || simpleKeyArgsFn;\n                }\n            });\n        }\n    };\n    Policies.prototype.setRootTypename = function (which, typename) {\n        if (typename === void 0) { typename = which; }\n        var rootId = \"ROOT_\" + which.toUpperCase();\n        var old = this.rootTypenamesById[rootId];\n        if (typename !== old) {\n            invariant(!old || old === which, 6, which);\n            // First, delete any old __typename associated with this rootId from\n            // rootIdsByTypename.\n            if (old)\n                delete this.rootIdsByTypename[old];\n            // Now make this the only __typename that maps to this rootId.\n            this.rootIdsByTypename[typename] = rootId;\n            // Finally, update the __typename associated with this rootId.\n            this.rootTypenamesById[rootId] = typename;\n        }\n    };\n    Policies.prototype.addPossibleTypes = function (possibleTypes) {\n        var _this = this;\n        this.usingPossibleTypes = true;\n        Object.keys(possibleTypes).forEach(function (supertype) {\n            // Make sure all types have an entry in this.supertypeMap, even if\n            // their supertype set is empty, so we can return false immediately\n            // from policies.fragmentMatches for unknown supertypes.\n            _this.getSupertypeSet(supertype, true);\n            possibleTypes[supertype].forEach(function (subtype) {\n                _this.getSupertypeSet(subtype, true).add(supertype);\n                var match = subtype.match(TypeOrFieldNameRegExp);\n                if (!match || match[0] !== subtype) {\n                    // TODO Don't interpret just any invalid typename as a RegExp.\n                    _this.fuzzySubtypes.set(subtype, new RegExp(subtype));\n                }\n            });\n        });\n    };\n    Policies.prototype.getTypePolicy = function (typename) {\n        var _this = this;\n        if (!hasOwn.call(this.typePolicies, typename)) {\n            var policy_1 = (this.typePolicies[typename] = Object.create(null));\n            policy_1.fields = Object.create(null);\n            // When the TypePolicy for typename is first accessed, instead of\n            // starting with an empty policy object, inherit any properties or\n            // fields from the type policies of the supertypes of typename.\n            //\n            // Any properties or fields defined explicitly within the TypePolicy\n            // for typename will take precedence, and if there are multiple\n            // supertypes, the properties of policies whose types were added\n            // later via addPossibleTypes will take precedence over those of\n            // earlier supertypes. TODO Perhaps we should warn about these\n            // conflicts in development, and recommend defining the property\n            // explicitly in the subtype policy?\n            //\n            // Field policy inheritance is atomic/shallow: you can't inherit a\n            // field policy and then override just its read function, since read\n            // and merge functions often need to cooperate, so changing only one\n            // of them would be a recipe for inconsistency.\n            //\n            // Once the TypePolicy for typename has been accessed, its properties can\n            // still be updated directly using addTypePolicies, but future changes to\n            // inherited supertype policies will not be reflected in this subtype\n            // policy, because this code runs at most once per typename.\n            var supertypes_1 = this.supertypeMap.get(typename);\n            if (!supertypes_1 && this.fuzzySubtypes.size) {\n                // To make the inheritance logic work for unknown typename strings that\n                // may have fuzzy supertypes, we give this typename an empty supertype\n                // set and then populate it with any fuzzy supertypes that match.\n                supertypes_1 = this.getSupertypeSet(typename, true);\n                // This only works for typenames that are directly matched by a fuzzy\n                // supertype. What if there is an intermediate chain of supertypes?\n                // While possible, that situation can only be solved effectively by\n                // specifying the intermediate relationships via possibleTypes, manually\n                // and in a non-fuzzy way.\n                this.fuzzySubtypes.forEach(function (regExp, fuzzy) {\n                    if (regExp.test(typename)) {\n                        // The fuzzy parameter is just the original string version of regExp\n                        // (not a valid __typename string), but we can look up the\n                        // associated supertype(s) in this.supertypeMap.\n                        var fuzzySupertypes = _this.supertypeMap.get(fuzzy);\n                        if (fuzzySupertypes) {\n                            fuzzySupertypes.forEach(function (supertype) {\n                                return supertypes_1.add(supertype);\n                            });\n                        }\n                    }\n                });\n            }\n            if (supertypes_1 && supertypes_1.size) {\n                supertypes_1.forEach(function (supertype) {\n                    var _a = _this.getTypePolicy(supertype), fields = _a.fields, rest = __rest(_a, [\"fields\"]);\n                    Object.assign(policy_1, rest);\n                    Object.assign(policy_1.fields, fields);\n                });\n            }\n        }\n        var inbox = this.toBeAdded[typename];\n        if (inbox && inbox.length) {\n            // Merge the pending policies into this.typePolicies, in the order they\n            // were originally passed to addTypePolicy.\n            inbox.splice(0).forEach(function (policy) {\n                _this.updateTypePolicy(typename, policy);\n            });\n        }\n        return this.typePolicies[typename];\n    };\n    Policies.prototype.getFieldPolicy = function (typename, fieldName, createIfMissing) {\n        if (typename) {\n            var fieldPolicies = this.getTypePolicy(typename).fields;\n            return (fieldPolicies[fieldName] ||\n                (createIfMissing && (fieldPolicies[fieldName] = Object.create(null))));\n        }\n    };\n    Policies.prototype.getSupertypeSet = function (subtype, createIfMissing) {\n        var supertypeSet = this.supertypeMap.get(subtype);\n        if (!supertypeSet && createIfMissing) {\n            this.supertypeMap.set(subtype, (supertypeSet = new Set()));\n        }\n        return supertypeSet;\n    };\n    Policies.prototype.fragmentMatches = function (fragment, typename, result, variables) {\n        var _this = this;\n        if (!fragment.typeCondition)\n            return true;\n        // If the fragment has a type condition but the object we're matching\n        // against does not have a __typename, the fragment cannot match.\n        if (!typename)\n            return false;\n        var supertype = fragment.typeCondition.name.value;\n        // Common case: fragment type condition and __typename are the same.\n        if (typename === supertype)\n            return true;\n        if (this.usingPossibleTypes && this.supertypeMap.has(supertype)) {\n            var typenameSupertypeSet = this.getSupertypeSet(typename, true);\n            var workQueue_1 = [typenameSupertypeSet];\n            var maybeEnqueue_1 = function (subtype) {\n                var supertypeSet = _this.getSupertypeSet(subtype, false);\n                if (supertypeSet &&\n                    supertypeSet.size &&\n                    workQueue_1.indexOf(supertypeSet) < 0) {\n                    workQueue_1.push(supertypeSet);\n                }\n            };\n            // We need to check fuzzy subtypes only if we encountered fuzzy\n            // subtype strings in addPossibleTypes, and only while writing to\n            // the cache, since that's when selectionSetMatchesResult gives a\n            // strong signal of fragment matching. The StoreReader class calls\n            // policies.fragmentMatches without passing a result object, so\n            // needToCheckFuzzySubtypes is always false while reading.\n            var needToCheckFuzzySubtypes = !!(result && this.fuzzySubtypes.size);\n            var checkingFuzzySubtypes = false;\n            // It's important to keep evaluating workQueue.length each time through\n            // the loop, because the queue can grow while we're iterating over it.\n            for (var i = 0; i < workQueue_1.length; ++i) {\n                var supertypeSet = workQueue_1[i];\n                if (supertypeSet.has(supertype)) {\n                    if (!typenameSupertypeSet.has(supertype)) {\n                        if (checkingFuzzySubtypes) {\n                            globalThis.__DEV__ !== false && invariant.warn(7, typename, supertype);\n                        }\n                        // Record positive results for faster future lookup.\n                        // Unfortunately, we cannot safely cache negative results,\n                        // because new possibleTypes data could always be added to the\n                        // Policies class.\n                        typenameSupertypeSet.add(supertype);\n                    }\n                    return true;\n                }\n                supertypeSet.forEach(maybeEnqueue_1);\n                if (needToCheckFuzzySubtypes &&\n                    // Start checking fuzzy subtypes only after exhausting all\n                    // non-fuzzy subtypes (after the final iteration of the loop).\n                    i === workQueue_1.length - 1 &&\n                    // We could wait to compare fragment.selectionSet to result\n                    // after we verify the supertype, but this check is often less\n                    // expensive than that search, and we will have to do the\n                    // comparison anyway whenever we find a potential match.\n                    selectionSetMatchesResult(fragment.selectionSet, result, variables)) {\n                    // We don't always need to check fuzzy subtypes (if no result\n                    // was provided, or !this.fuzzySubtypes.size), but, when we do,\n                    // we only want to check them once.\n                    needToCheckFuzzySubtypes = false;\n                    checkingFuzzySubtypes = true;\n                    // If we find any fuzzy subtypes that match typename, extend the\n                    // workQueue to search through the supertypes of those fuzzy\n                    // subtypes. Otherwise the for-loop will terminate and we'll\n                    // return false below.\n                    this.fuzzySubtypes.forEach(function (regExp, fuzzyString) {\n                        var match = typename.match(regExp);\n                        if (match && match[0] === typename) {\n                            maybeEnqueue_1(fuzzyString);\n                        }\n                    });\n                }\n            }\n        }\n        return false;\n    };\n    Policies.prototype.hasKeyArgs = function (typename, fieldName) {\n        var policy = this.getFieldPolicy(typename, fieldName, false);\n        return !!(policy && policy.keyFn);\n    };\n    Policies.prototype.getStoreFieldName = function (fieldSpec) {\n        var typename = fieldSpec.typename, fieldName = fieldSpec.fieldName;\n        var policy = this.getFieldPolicy(typename, fieldName, false);\n        var storeFieldName;\n        var keyFn = policy && policy.keyFn;\n        if (keyFn && typename) {\n            var context = {\n                typename: typename,\n                fieldName: fieldName,\n                field: fieldSpec.field || null,\n                variables: fieldSpec.variables,\n            };\n            var args = argsFromFieldSpecifier(fieldSpec);\n            while (keyFn) {\n                var specifierOrString = keyFn(args, context);\n                if (isArray(specifierOrString)) {\n                    keyFn = keyArgsFnFromSpecifier(specifierOrString);\n                }\n                else {\n                    // If the custom keyFn returns a falsy value, fall back to\n                    // fieldName instead.\n                    storeFieldName = specifierOrString || fieldName;\n                    break;\n                }\n            }\n        }\n        if (storeFieldName === void 0) {\n            storeFieldName =\n                fieldSpec.field ?\n                    storeKeyNameFromField(fieldSpec.field, fieldSpec.variables)\n                    : getStoreKeyName(fieldName, argsFromFieldSpecifier(fieldSpec));\n        }\n        // Returning false from a keyArgs function is like configuring\n        // keyArgs: false, but more dynamic.\n        if (storeFieldName === false) {\n            return fieldName;\n        }\n        // Make sure custom field names start with the actual field.name.value\n        // of the field, so we can always figure out which properties of a\n        // StoreObject correspond to which original field names.\n        return fieldName === fieldNameFromStoreName(storeFieldName) ? storeFieldName\n            : fieldName + \":\" + storeFieldName;\n    };\n    Policies.prototype.readField = function (options, context) {\n        var objectOrReference = options.from;\n        if (!objectOrReference)\n            return;\n        var nameOrField = options.field || options.fieldName;\n        if (!nameOrField)\n            return;\n        if (options.typename === void 0) {\n            var typename = context.store.getFieldValue(objectOrReference, \"__typename\");\n            if (typename)\n                options.typename = typename;\n        }\n        var storeFieldName = this.getStoreFieldName(options);\n        var fieldName = fieldNameFromStoreName(storeFieldName);\n        var existing = context.store.getFieldValue(objectOrReference, storeFieldName);\n        var policy = this.getFieldPolicy(options.typename, fieldName, false);\n        var read = policy && policy.read;\n        if (read) {\n            var readOptions = makeFieldFunctionOptions(this, objectOrReference, options, context, context.store.getStorage(isReference(objectOrReference) ?\n                objectOrReference.__ref\n                : objectOrReference, storeFieldName));\n            // Call read(existing, readOptions) with cacheSlot holding this.cache.\n            return cacheSlot.withValue(this.cache, read, [\n                existing,\n                readOptions,\n            ]);\n        }\n        return existing;\n    };\n    Policies.prototype.getReadFunction = function (typename, fieldName) {\n        var policy = this.getFieldPolicy(typename, fieldName, false);\n        return policy && policy.read;\n    };\n    Policies.prototype.getMergeFunction = function (parentTypename, fieldName, childTypename) {\n        var policy = this.getFieldPolicy(parentTypename, fieldName, false);\n        var merge = policy && policy.merge;\n        if (!merge && childTypename) {\n            policy = this.getTypePolicy(childTypename);\n            merge = policy && policy.merge;\n        }\n        return merge;\n    };\n    Policies.prototype.runMergeFunction = function (existing, incoming, _a, context, storage) {\n        var field = _a.field, typename = _a.typename, merge = _a.merge;\n        if (merge === mergeTrueFn) {\n            // Instead of going to the trouble of creating a full\n            // FieldFunctionOptions object and calling mergeTrueFn, we can\n            // simply call mergeObjects, as mergeTrueFn would.\n            return makeMergeObjectsFunction(context.store)(existing, incoming);\n        }\n        if (merge === mergeFalseFn) {\n            // Likewise for mergeFalseFn, whose implementation is even simpler.\n            return incoming;\n        }\n        // If cache.writeQuery or cache.writeFragment was called with\n        // options.overwrite set to true, we still call merge functions, but\n        // the existing data is always undefined, so the merge function will\n        // not attempt to combine the incoming data with the existing data.\n        if (context.overwrite) {\n            existing = void 0;\n        }\n        return merge(existing, incoming, makeFieldFunctionOptions(this, \n        // Unlike options.readField for read functions, we do not fall\n        // back to the current object if no foreignObjOrRef is provided,\n        // because it's not clear what the current object should be for\n        // merge functions: the (possibly undefined) existing object, or\n        // the incoming object? If you think your merge function needs\n        // to read sibling fields in order to produce a new value for\n        // the current field, you might want to rethink your strategy,\n        // because that's a recipe for making merge behavior sensitive\n        // to the order in which fields are written into the cache.\n        // However, readField(name, ref) is useful for merge functions\n        // that need to deduplicate child objects and references.\n        void 0, {\n            typename: typename,\n            fieldName: field.name.value,\n            field: field,\n            variables: context.variables,\n        }, context, storage || Object.create(null)));\n    };\n    return Policies;\n}());\nexport { Policies };\nfunction makeFieldFunctionOptions(policies, objectOrReference, fieldSpec, context, storage) {\n    var storeFieldName = policies.getStoreFieldName(fieldSpec);\n    var fieldName = fieldNameFromStoreName(storeFieldName);\n    var variables = fieldSpec.variables || context.variables;\n    var _a = context.store, toReference = _a.toReference, canRead = _a.canRead;\n    return {\n        args: argsFromFieldSpecifier(fieldSpec),\n        field: fieldSpec.field || null,\n        fieldName: fieldName,\n        storeFieldName: storeFieldName,\n        variables: variables,\n        isReference: isReference,\n        toReference: toReference,\n        storage: storage,\n        cache: policies.cache,\n        canRead: canRead,\n        readField: function () {\n            return policies.readField(normalizeReadFieldOptions(arguments, objectOrReference, variables), context);\n        },\n        mergeObjects: makeMergeObjectsFunction(context.store),\n    };\n}\nexport function normalizeReadFieldOptions(readFieldArgs, objectOrReference, variables) {\n    var fieldNameOrOptions = readFieldArgs[0], from = readFieldArgs[1], argc = readFieldArgs.length;\n    var options;\n    if (typeof fieldNameOrOptions === \"string\") {\n        options = {\n            fieldName: fieldNameOrOptions,\n            // Default to objectOrReference only when no second argument was\n            // passed for the from parameter, not when undefined is explicitly\n            // passed as the second argument.\n            from: argc > 1 ? from : objectOrReference,\n        };\n    }\n    else {\n        options = __assign({}, fieldNameOrOptions);\n        // Default to objectOrReference only when fieldNameOrOptions.from is\n        // actually omitted, rather than just undefined.\n        if (!hasOwn.call(options, \"from\")) {\n            options.from = objectOrReference;\n        }\n    }\n    if (globalThis.__DEV__ !== false && options.from === void 0) {\n        globalThis.__DEV__ !== false && invariant.warn(8, stringifyForDisplay(Array.from(readFieldArgs)));\n    }\n    if (void 0 === options.variables) {\n        options.variables = variables;\n    }\n    return options;\n}\nfunction makeMergeObjectsFunction(store) {\n    return function mergeObjects(existing, incoming) {\n        if (isArray(existing) || isArray(incoming)) {\n            throw newInvariantError(9);\n        }\n        // These dynamic checks are necessary because the parameters of a\n        // custom merge function can easily have the any type, so the type\n        // system cannot always enforce the StoreObject | Reference parameter\n        // types of options.mergeObjects.\n        if (isNonNullObject(existing) && isNonNullObject(incoming)) {\n            var eType = store.getFieldValue(existing, \"__typename\");\n            var iType = store.getFieldValue(incoming, \"__typename\");\n            var typesDiffer = eType && iType && eType !== iType;\n            if (typesDiffer) {\n                return incoming;\n            }\n            if (isReference(existing) && storeValueIsStoreObject(incoming)) {\n                // Update the normalized EntityStore for the entity identified by\n                // existing.__ref, preferring/overwriting any fields contributed by the\n                // newer incoming StoreObject.\n                store.merge(existing.__ref, incoming);\n                return existing;\n            }\n            if (storeValueIsStoreObject(existing) && isReference(incoming)) {\n                // Update the normalized EntityStore for the entity identified by\n                // incoming.__ref, taking fields from the older existing object only if\n                // those fields are not already present in the newer StoreObject\n                // identified by incoming.__ref.\n                store.merge(existing, incoming.__ref);\n                return incoming;\n            }\n            if (storeValueIsStoreObject(existing) &&\n                storeValueIsStoreObject(incoming)) {\n                return __assign(__assign({}, existing), incoming);\n            }\n        }\n        return incoming;\n    };\n}\n//# sourceMappingURL=policies.js.map", "import { __assign } from \"tslib\";\nimport { invariant, newInvariantError } from \"../../utilities/globals/index.js\";\nimport { equal } from \"@wry/equality\";\nimport { Trie } from \"@wry/trie\";\nimport { Kind } from \"graphql\";\nimport { getFragmentFromSelection, getDefaultValues, getOperationDefinition, getTypenameFromResult, makeReference, isField, resultKeyNameFromField, isReference, shouldInclude, cloneDeep, addTypenameToDocument, isNonEmptyArray, argumentsObjectFromField, canonicalStringify, } from \"../../utilities/index.js\";\nimport { isArray, makeProcessedFieldsMerger, fieldNameFromStoreName, storeValueIsStoreObject, extractFragmentContext, } from \"./helpers.js\";\nimport { normalizeReadFieldOptions } from \"./policies.js\";\n// Since there are only four possible combinations of context.clientOnly and\n// context.deferred values, we should need at most four \"flavors\" of any given\n// WriteContext. To avoid creating multiple copies of the same context, we cache\n// the contexts in the context.flavors Map (shared by all flavors) according to\n// their clientOnly and deferred values (always in that order).\nfunction getContextFlavor(context, clientOnly, deferred) {\n    var key = \"\".concat(clientOnly).concat(deferred);\n    var flavored = context.flavors.get(key);\n    if (!flavored) {\n        context.flavors.set(key, (flavored =\n            context.clientOnly === clientOnly && context.deferred === deferred ?\n                context\n                : __assign(__assign({}, context), { clientOnly: clientOnly, deferred: deferred })));\n    }\n    return flavored;\n}\nvar StoreWriter = /** @class */ (function () {\n    function StoreWriter(cache, reader, fragments) {\n        this.cache = cache;\n        this.reader = reader;\n        this.fragments = fragments;\n    }\n    StoreWriter.prototype.writeToStore = function (store, _a) {\n        var _this = this;\n        var query = _a.query, result = _a.result, dataId = _a.dataId, variables = _a.variables, overwrite = _a.overwrite;\n        var operationDefinition = getOperationDefinition(query);\n        var merger = makeProcessedFieldsMerger();\n        variables = __assign(__assign({}, getDefaultValues(operationDefinition)), variables);\n        var context = __assign(__assign({ store: store, written: Object.create(null), merge: function (existing, incoming) {\n                return merger.merge(existing, incoming);\n            }, variables: variables, varString: canonicalStringify(variables) }, extractFragmentContext(query, this.fragments)), { overwrite: !!overwrite, incomingById: new Map(), clientOnly: false, deferred: false, flavors: new Map() });\n        var ref = this.processSelectionSet({\n            result: result || Object.create(null),\n            dataId: dataId,\n            selectionSet: operationDefinition.selectionSet,\n            mergeTree: { map: new Map() },\n            context: context,\n        });\n        if (!isReference(ref)) {\n            throw newInvariantError(12, result);\n        }\n        // So far, the store has not been modified, so now it's time to process\n        // context.incomingById and merge those incoming fields into context.store.\n        context.incomingById.forEach(function (_a, dataId) {\n            var storeObject = _a.storeObject, mergeTree = _a.mergeTree, fieldNodeSet = _a.fieldNodeSet;\n            var entityRef = makeReference(dataId);\n            if (mergeTree && mergeTree.map.size) {\n                var applied = _this.applyMerges(mergeTree, entityRef, storeObject, context);\n                if (isReference(applied)) {\n                    // Assume References returned by applyMerges have already been merged\n                    // into the store. See makeMergeObjectsFunction in policies.ts for an\n                    // example of how this can happen.\n                    return;\n                }\n                // Otherwise, applyMerges returned a StoreObject, whose fields we should\n                // merge into the store (see store.merge statement below).\n                storeObject = applied;\n            }\n            if (globalThis.__DEV__ !== false && !context.overwrite) {\n                var fieldsWithSelectionSets_1 = Object.create(null);\n                fieldNodeSet.forEach(function (field) {\n                    if (field.selectionSet) {\n                        fieldsWithSelectionSets_1[field.name.value] = true;\n                    }\n                });\n                var hasSelectionSet_1 = function (storeFieldName) {\n                    return fieldsWithSelectionSets_1[fieldNameFromStoreName(storeFieldName)] ===\n                        true;\n                };\n                var hasMergeFunction_1 = function (storeFieldName) {\n                    var childTree = mergeTree && mergeTree.map.get(storeFieldName);\n                    return Boolean(childTree && childTree.info && childTree.info.merge);\n                };\n                Object.keys(storeObject).forEach(function (storeFieldName) {\n                    // If a merge function was defined for this field, trust that it\n                    // did the right thing about (not) clobbering data. If the field\n                    // has no selection set, it's a scalar field, so it doesn't need\n                    // a merge function (even if it's an object, like JSON data).\n                    if (hasSelectionSet_1(storeFieldName) &&\n                        !hasMergeFunction_1(storeFieldName)) {\n                        warnAboutDataLoss(entityRef, storeObject, storeFieldName, context.store);\n                    }\n                });\n            }\n            store.merge(dataId, storeObject);\n        });\n        // Any IDs written explicitly to the cache will be retained as\n        // reachable root IDs for garbage collection purposes. Although this\n        // logic includes root IDs like ROOT_QUERY and ROOT_MUTATION, their\n        // retainment counts are effectively ignored because cache.gc() always\n        // includes them in its root ID set.\n        store.retain(ref.__ref);\n        return ref;\n    };\n    StoreWriter.prototype.processSelectionSet = function (_a) {\n        var _this = this;\n        var dataId = _a.dataId, result = _a.result, selectionSet = _a.selectionSet, context = _a.context, \n        // This object allows processSelectionSet to report useful information\n        // to its callers without explicitly returning that information.\n        mergeTree = _a.mergeTree;\n        var policies = this.cache.policies;\n        // This variable will be repeatedly updated using context.merge to\n        // accumulate all fields that need to be written into the store.\n        var incoming = Object.create(null);\n        // If typename was not passed in, infer it. Note that typename is\n        // always passed in for tricky-to-infer cases such as \"Query\" for\n        // ROOT_QUERY.\n        var typename = (dataId && policies.rootTypenamesById[dataId]) ||\n            getTypenameFromResult(result, selectionSet, context.fragmentMap) ||\n            (dataId && context.store.get(dataId, \"__typename\"));\n        if (\"string\" === typeof typename) {\n            incoming.__typename = typename;\n        }\n        // This readField function will be passed as context.readField in the\n        // KeyFieldsContext object created within policies.identify (called below).\n        // In addition to reading from the existing context.store (thanks to the\n        // policies.readField(options, context) line at the very bottom), this\n        // version of readField can read from Reference objects that are currently\n        // pending in context.incomingById, which is important whenever keyFields\n        // need to be extracted from a child object that processSelectionSet has\n        // turned into a Reference.\n        var readField = function () {\n            var options = normalizeReadFieldOptions(arguments, incoming, context.variables);\n            if (isReference(options.from)) {\n                var info = context.incomingById.get(options.from.__ref);\n                if (info) {\n                    var result_1 = policies.readField(__assign(__assign({}, options), { from: info.storeObject }), context);\n                    if (result_1 !== void 0) {\n                        return result_1;\n                    }\n                }\n            }\n            return policies.readField(options, context);\n        };\n        var fieldNodeSet = new Set();\n        this.flattenFields(selectionSet, result, \n        // This WriteContext will be the default context value for fields returned\n        // by the flattenFields method, but some fields may be assigned a modified\n        // context, depending on the presence of @client and other directives.\n        context, typename).forEach(function (context, field) {\n            var _a;\n            var resultFieldKey = resultKeyNameFromField(field);\n            var value = result[resultFieldKey];\n            fieldNodeSet.add(field);\n            if (value !== void 0) {\n                var storeFieldName = policies.getStoreFieldName({\n                    typename: typename,\n                    fieldName: field.name.value,\n                    field: field,\n                    variables: context.variables,\n                });\n                var childTree = getChildMergeTree(mergeTree, storeFieldName);\n                var incomingValue = _this.processFieldValue(value, field, \n                // Reset context.clientOnly and context.deferred to their default\n                // values before processing nested selection sets.\n                field.selectionSet ?\n                    getContextFlavor(context, false, false)\n                    : context, childTree);\n                // To determine if this field holds a child object with a merge function\n                // defined in its type policy (see PR #7070), we need to figure out the\n                // child object's __typename.\n                var childTypename = void 0;\n                // The field's value can be an object that has a __typename only if the\n                // field has a selection set. Otherwise incomingValue is scalar.\n                if (field.selectionSet &&\n                    (isReference(incomingValue) || storeValueIsStoreObject(incomingValue))) {\n                    childTypename = readField(\"__typename\", incomingValue);\n                }\n                var merge = policies.getMergeFunction(typename, field.name.value, childTypename);\n                if (merge) {\n                    childTree.info = {\n                        // TODO Check compatibility against any existing childTree.field?\n                        field: field,\n                        typename: typename,\n                        merge: merge,\n                    };\n                }\n                else {\n                    maybeRecycleChildMergeTree(mergeTree, storeFieldName);\n                }\n                incoming = context.merge(incoming, (_a = {},\n                    _a[storeFieldName] = incomingValue,\n                    _a));\n            }\n            else if (globalThis.__DEV__ !== false &&\n                !context.clientOnly &&\n                !context.deferred &&\n                !addTypenameToDocument.added(field) &&\n                // If the field has a read function, it may be a synthetic field or\n                // provide a default value, so its absence from the written data should\n                // not be cause for alarm.\n                !policies.getReadFunction(typename, field.name.value)) {\n                globalThis.__DEV__ !== false && invariant.error(13, resultKeyNameFromField(field), result);\n            }\n        });\n        // Identify the result object, even if dataId was already provided,\n        // since we always need keyObject below.\n        try {\n            var _b = policies.identify(result, {\n                typename: typename,\n                selectionSet: selectionSet,\n                fragmentMap: context.fragmentMap,\n                storeObject: incoming,\n                readField: readField,\n            }), id = _b[0], keyObject = _b[1];\n            // If dataId was not provided, fall back to the id just generated by\n            // policies.identify.\n            dataId = dataId || id;\n            // Write any key fields that were used during identification, even if\n            // they were not mentioned in the original query.\n            if (keyObject) {\n                // TODO Reverse the order of the arguments?\n                incoming = context.merge(incoming, keyObject);\n            }\n        }\n        catch (e) {\n            // If dataId was provided, tolerate failure of policies.identify.\n            if (!dataId)\n                throw e;\n        }\n        if (\"string\" === typeof dataId) {\n            var dataRef = makeReference(dataId);\n            // Avoid processing the same entity object using the same selection\n            // set more than once. We use an array instead of a Set since most\n            // entity IDs will be written using only one selection set, so the\n            // size of this array is likely to be very small, meaning indexOf is\n            // likely to be faster than Set.prototype.has.\n            var sets = context.written[dataId] || (context.written[dataId] = []);\n            if (sets.indexOf(selectionSet) >= 0)\n                return dataRef;\n            sets.push(selectionSet);\n            // If we're about to write a result object into the store, but we\n            // happen to know that the exact same (===) result object would be\n            // returned if we were to reread the result with the same inputs,\n            // then we can skip the rest of the processSelectionSet work for\n            // this object, and immediately return a Reference to it.\n            if (this.reader &&\n                this.reader.isFresh(result, dataRef, selectionSet, context)) {\n                return dataRef;\n            }\n            var previous_1 = context.incomingById.get(dataId);\n            if (previous_1) {\n                previous_1.storeObject = context.merge(previous_1.storeObject, incoming);\n                previous_1.mergeTree = mergeMergeTrees(previous_1.mergeTree, mergeTree);\n                fieldNodeSet.forEach(function (field) { return previous_1.fieldNodeSet.add(field); });\n            }\n            else {\n                context.incomingById.set(dataId, {\n                    storeObject: incoming,\n                    // Save a reference to mergeTree only if it is not empty, because\n                    // empty MergeTrees may be recycled by maybeRecycleChildMergeTree and\n                    // reused for entirely different parts of the result tree.\n                    mergeTree: mergeTreeIsEmpty(mergeTree) ? void 0 : mergeTree,\n                    fieldNodeSet: fieldNodeSet,\n                });\n            }\n            return dataRef;\n        }\n        return incoming;\n    };\n    StoreWriter.prototype.processFieldValue = function (value, field, context, mergeTree) {\n        var _this = this;\n        if (!field.selectionSet || value === null) {\n            // In development, we need to clone scalar values so that they can be\n            // safely frozen with maybeDeepFreeze in readFromStore.ts. In production,\n            // it's cheaper to store the scalar values directly in the cache.\n            return globalThis.__DEV__ !== false ? cloneDeep(value) : value;\n        }\n        if (isArray(value)) {\n            return value.map(function (item, i) {\n                var value = _this.processFieldValue(item, field, context, getChildMergeTree(mergeTree, i));\n                maybeRecycleChildMergeTree(mergeTree, i);\n                return value;\n            });\n        }\n        return this.processSelectionSet({\n            result: value,\n            selectionSet: field.selectionSet,\n            context: context,\n            mergeTree: mergeTree,\n        });\n    };\n    // Implements https://spec.graphql.org/draft/#sec-Field-Collection, but with\n    // some additions for tracking @client and @defer directives.\n    StoreWriter.prototype.flattenFields = function (selectionSet, result, context, typename) {\n        if (typename === void 0) { typename = getTypenameFromResult(result, selectionSet, context.fragmentMap); }\n        var fieldMap = new Map();\n        var policies = this.cache.policies;\n        var limitingTrie = new Trie(false); // No need for WeakMap, since limitingTrie does not escape.\n        (function flatten(selectionSet, inheritedContext) {\n            var visitedNode = limitingTrie.lookup(selectionSet, \n            // Because we take inheritedClientOnly and inheritedDeferred into\n            // consideration here (in addition to selectionSet), it's possible for\n            // the same selection set to be flattened more than once, if it appears\n            // in the query with different @client and/or @directive configurations.\n            inheritedContext.clientOnly, inheritedContext.deferred);\n            if (visitedNode.visited)\n                return;\n            visitedNode.visited = true;\n            selectionSet.selections.forEach(function (selection) {\n                if (!shouldInclude(selection, context.variables))\n                    return;\n                var clientOnly = inheritedContext.clientOnly, deferred = inheritedContext.deferred;\n                if (\n                // Since the presence of @client or @defer on this field can only\n                // cause clientOnly or deferred to become true, we can skip the\n                // forEach loop if both clientOnly and deferred are already true.\n                !(clientOnly && deferred) &&\n                    isNonEmptyArray(selection.directives)) {\n                    selection.directives.forEach(function (dir) {\n                        var name = dir.name.value;\n                        if (name === \"client\")\n                            clientOnly = true;\n                        if (name === \"defer\") {\n                            var args = argumentsObjectFromField(dir, context.variables);\n                            // The @defer directive takes an optional args.if boolean\n                            // argument, similar to @include(if: boolean). Note that\n                            // @defer(if: false) does not make context.deferred false, but\n                            // instead behaves as if there was no @defer directive.\n                            if (!args || args.if !== false) {\n                                deferred = true;\n                            }\n                            // TODO In the future, we may want to record args.label using\n                            // context.deferred, if a label is specified.\n                        }\n                    });\n                }\n                if (isField(selection)) {\n                    var existing = fieldMap.get(selection);\n                    if (existing) {\n                        // If this field has been visited along another recursive path\n                        // before, the final context should have clientOnly or deferred set\n                        // to true only if *all* paths have the directive (hence the &&).\n                        clientOnly = clientOnly && existing.clientOnly;\n                        deferred = deferred && existing.deferred;\n                    }\n                    fieldMap.set(selection, getContextFlavor(context, clientOnly, deferred));\n                }\n                else {\n                    var fragment = getFragmentFromSelection(selection, context.lookupFragment);\n                    if (!fragment && selection.kind === Kind.FRAGMENT_SPREAD) {\n                        throw newInvariantError(14, selection.name.value);\n                    }\n                    if (fragment &&\n                        policies.fragmentMatches(fragment, typename, result, context.variables)) {\n                        flatten(fragment.selectionSet, getContextFlavor(context, clientOnly, deferred));\n                    }\n                }\n            });\n        })(selectionSet, context);\n        return fieldMap;\n    };\n    StoreWriter.prototype.applyMerges = function (mergeTree, existing, incoming, context, getStorageArgs) {\n        var _a;\n        var _this = this;\n        if (mergeTree.map.size && !isReference(incoming)) {\n            var e_1 = \n            // Items in the same position in different arrays are not\n            // necessarily related to each other, so when incoming is an array\n            // we process its elements as if there was no existing data.\n            (!isArray(incoming) &&\n                // Likewise, existing must be either a Reference or a StoreObject\n                // in order for its fields to be safe to merge with the fields of\n                // the incoming object.\n                (isReference(existing) || storeValueIsStoreObject(existing))) ?\n                existing\n                : void 0;\n            // This narrowing is implied by mergeTree.map.size > 0 and\n            // !isReference(incoming), though TypeScript understandably cannot\n            // hope to infer this type.\n            var i_1 = incoming;\n            // The options.storage objects provided to read and merge functions\n            // are derived from the identity of the parent object plus a\n            // sequence of storeFieldName strings/numbers identifying the nested\n            // field name path of each field value to be merged.\n            if (e_1 && !getStorageArgs) {\n                getStorageArgs = [isReference(e_1) ? e_1.__ref : e_1];\n            }\n            // It's possible that applying merge functions to this subtree will\n            // not change the incoming data, so this variable tracks the fields\n            // that did change, so we can create a new incoming object when (and\n            // only when) at least one incoming field has changed. We use a Map\n            // to preserve the type of numeric keys.\n            var changedFields_1;\n            var getValue_1 = function (from, name) {\n                return (isArray(from) ?\n                    typeof name === \"number\" ?\n                        from[name]\n                        : void 0\n                    : context.store.getFieldValue(from, String(name)));\n            };\n            mergeTree.map.forEach(function (childTree, storeFieldName) {\n                var eVal = getValue_1(e_1, storeFieldName);\n                var iVal = getValue_1(i_1, storeFieldName);\n                // If we have no incoming data, leave any existing data untouched.\n                if (void 0 === iVal)\n                    return;\n                if (getStorageArgs) {\n                    getStorageArgs.push(storeFieldName);\n                }\n                var aVal = _this.applyMerges(childTree, eVal, iVal, context, getStorageArgs);\n                if (aVal !== iVal) {\n                    changedFields_1 = changedFields_1 || new Map();\n                    changedFields_1.set(storeFieldName, aVal);\n                }\n                if (getStorageArgs) {\n                    invariant(getStorageArgs.pop() === storeFieldName);\n                }\n            });\n            if (changedFields_1) {\n                // Shallow clone i so we can add changed fields to it.\n                incoming = (isArray(i_1) ? i_1.slice(0) : __assign({}, i_1));\n                changedFields_1.forEach(function (value, name) {\n                    incoming[name] = value;\n                });\n            }\n        }\n        if (mergeTree.info) {\n            return this.cache.policies.runMergeFunction(existing, incoming, mergeTree.info, context, getStorageArgs && (_a = context.store).getStorage.apply(_a, getStorageArgs));\n        }\n        return incoming;\n    };\n    return StoreWriter;\n}());\nexport { StoreWriter };\nvar emptyMergeTreePool = [];\nfunction getChildMergeTree(_a, name) {\n    var map = _a.map;\n    if (!map.has(name)) {\n        map.set(name, emptyMergeTreePool.pop() || { map: new Map() });\n    }\n    return map.get(name);\n}\nfunction mergeMergeTrees(left, right) {\n    if (left === right || !right || mergeTreeIsEmpty(right))\n        return left;\n    if (!left || mergeTreeIsEmpty(left))\n        return right;\n    var info = left.info && right.info ? __assign(__assign({}, left.info), right.info) : left.info || right.info;\n    var needToMergeMaps = left.map.size && right.map.size;\n    var map = needToMergeMaps ? new Map()\n        : left.map.size ? left.map\n            : right.map;\n    var merged = { info: info, map: map };\n    if (needToMergeMaps) {\n        var remainingRightKeys_1 = new Set(right.map.keys());\n        left.map.forEach(function (leftTree, key) {\n            merged.map.set(key, mergeMergeTrees(leftTree, right.map.get(key)));\n            remainingRightKeys_1.delete(key);\n        });\n        remainingRightKeys_1.forEach(function (key) {\n            merged.map.set(key, mergeMergeTrees(right.map.get(key), left.map.get(key)));\n        });\n    }\n    return merged;\n}\nfunction mergeTreeIsEmpty(tree) {\n    return !tree || !(tree.info || tree.map.size);\n}\nfunction maybeRecycleChildMergeTree(_a, name) {\n    var map = _a.map;\n    var childTree = map.get(name);\n    if (childTree && mergeTreeIsEmpty(childTree)) {\n        emptyMergeTreePool.push(childTree);\n        map.delete(name);\n    }\n}\nvar warnings = new Set();\n// Note that this function is unused in production, and thus should be\n// pruned by any well-configured minifier.\nfunction warnAboutDataLoss(existingRef, incomingObj, storeFieldName, store) {\n    var getChild = function (objOrRef) {\n        var child = store.getFieldValue(objOrRef, storeFieldName);\n        return typeof child === \"object\" && child;\n    };\n    var existing = getChild(existingRef);\n    if (!existing)\n        return;\n    var incoming = getChild(incomingObj);\n    if (!incoming)\n        return;\n    // It's always safe to replace a reference, since it refers to data\n    // safely stored elsewhere.\n    if (isReference(existing))\n        return;\n    // If the values are structurally equivalent, we do not need to worry\n    // about incoming replacing existing.\n    if (equal(existing, incoming))\n        return;\n    // If we're replacing every key of the existing object, then the\n    // existing data would be overwritten even if the objects were\n    // normalized, so warning would not be helpful here.\n    if (Object.keys(existing).every(function (key) { return store.getFieldValue(incoming, key) !== void 0; })) {\n        return;\n    }\n    var parentType = store.getFieldValue(existingRef, \"__typename\") ||\n        store.getFieldValue(incomingObj, \"__typename\");\n    var fieldName = fieldNameFromStoreName(storeFieldName);\n    var typeDotName = \"\".concat(parentType, \".\").concat(fieldName);\n    // Avoid warning more than once for the same type and field name.\n    if (warnings.has(typeDotName))\n        return;\n    warnings.add(typeDotName);\n    var childTypenames = [];\n    // Arrays do not have __typename fields, and always need a custom merge\n    // function, even if their elements are normalized entities.\n    if (!isArray(existing) && !isArray(incoming)) {\n        [existing, incoming].forEach(function (child) {\n            var typename = store.getFieldValue(child, \"__typename\");\n            if (typeof typename === \"string\" && !childTypenames.includes(typename)) {\n                childTypenames.push(typename);\n            }\n        });\n    }\n    globalThis.__DEV__ !== false && invariant.warn(15, fieldName, parentType, childTypenames.length ?\n        \"either ensure all objects of type \" +\n            childTypenames.join(\" and \") +\n            \" have an ID or a custom merge function, or \"\n        : \"\", typeDotName, __assign({}, existing), __assign({}, incoming));\n}\n//# sourceMappingURL=writeToStore.js.map", "import { __assign, __extends } from \"tslib\";\nimport { invariant } from \"../../utilities/globals/index.js\";\n// Make builtins like Map and Set safe to use with non-extensible objects.\nimport \"./fixPolyfills.js\";\nimport { wrap } from \"optimism\";\nimport { equal } from \"@wry/equality\";\nimport { ApolloCache } from \"../core/cache.js\";\nimport { MissingFieldError } from \"../core/types/common.js\";\nimport { addTypenameToDocument, isReference, DocumentTransform, canonicalStringify, print, cacheSizes, } from \"../../utilities/index.js\";\nimport { StoreReader } from \"./readFromStore.js\";\nimport { StoreWriter } from \"./writeToStore.js\";\nimport { EntityStore, supportsResultCaching } from \"./entityStore.js\";\nimport { makeVar, forgetCache, recallCache } from \"./reactiveVars.js\";\nimport { Policies } from \"./policies.js\";\nimport { hasOwn, normalizeConfig, shouldCanonizeResults } from \"./helpers.js\";\nimport { getInMemoryCacheMemoryInternals } from \"../../utilities/caching/getMemoryInternals.js\";\nvar InMemoryCache = /** @class */ (function (_super) {\n    __extends(InMemoryCache, _super);\n    function InMemoryCache(config) {\n        if (config === void 0) { config = {}; }\n        var _this = _super.call(this) || this;\n        _this.watches = new Set();\n        _this.addTypenameTransform = new DocumentTransform(addTypenameToDocument);\n        // Override the default value, since InMemoryCache result objects are frozen\n        // in development and expected to remain logically immutable in production.\n        _this.assumeImmutableResults = true;\n        _this.makeVar = makeVar;\n        _this.txCount = 0;\n        _this.config = normalizeConfig(config);\n        _this.addTypename = !!_this.config.addTypename;\n        _this.policies = new Policies({\n            cache: _this,\n            dataIdFromObject: _this.config.dataIdFromObject,\n            possibleTypes: _this.config.possibleTypes,\n            typePolicies: _this.config.typePolicies,\n        });\n        _this.init();\n        return _this;\n    }\n    InMemoryCache.prototype.init = function () {\n        // Passing { resultCaching: false } in the InMemoryCache constructor options\n        // will completely disable dependency tracking, which will improve memory\n        // usage but worsen the performance of repeated reads.\n        var rootStore = (this.data = new EntityStore.Root({\n            policies: this.policies,\n            resultCaching: this.config.resultCaching,\n        }));\n        // When no optimistic writes are currently active, cache.optimisticData ===\n        // cache.data, so there are no additional layers on top of the actual data.\n        // When an optimistic update happens, this.optimisticData will become a\n        // linked list of EntityStore Layer objects that terminates with the\n        // original this.data cache object.\n        this.optimisticData = rootStore.stump;\n        this.resetResultCache();\n    };\n    InMemoryCache.prototype.resetResultCache = function (resetResultIdentities) {\n        var _this = this;\n        var previousReader = this.storeReader;\n        var fragments = this.config.fragments;\n        // The StoreWriter is mostly stateless and so doesn't really need to be\n        // reset, but it does need to have its writer.storeReader reference updated,\n        // so it's simpler to update this.storeWriter as well.\n        this.storeWriter = new StoreWriter(this, (this.storeReader = new StoreReader({\n            cache: this,\n            addTypename: this.addTypename,\n            resultCacheMaxSize: this.config.resultCacheMaxSize,\n            canonizeResults: shouldCanonizeResults(this.config),\n            canon: resetResultIdentities ? void 0 : (previousReader && previousReader.canon),\n            fragments: fragments,\n        })), fragments);\n        this.maybeBroadcastWatch = wrap(function (c, options) {\n            return _this.broadcastWatch(c, options);\n        }, {\n            max: this.config.resultCacheMaxSize ||\n                cacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] ||\n                5000 /* defaultCacheSizes[\"inMemoryCache.maybeBroadcastWatch\"] */,\n            makeCacheKey: function (c) {\n                // Return a cache key (thus enabling result caching) only if we're\n                // currently using a data store that can track cache dependencies.\n                var store = c.optimistic ? _this.optimisticData : _this.data;\n                if (supportsResultCaching(store)) {\n                    var optimistic = c.optimistic, id = c.id, variables = c.variables;\n                    return store.makeCacheKey(c.query, \n                    // Different watches can have the same query, optimistic\n                    // status, rootId, and variables, but if their callbacks are\n                    // different, the (identical) result needs to be delivered to\n                    // each distinct callback. The easiest way to achieve that\n                    // separation is to include c.callback in the cache key for\n                    // maybeBroadcastWatch calls. See issue #5733.\n                    c.callback, canonicalStringify({ optimistic: optimistic, id: id, variables: variables }));\n                }\n            },\n        });\n        // Since we have thrown away all the cached functions that depend on the\n        // CacheGroup dependencies maintained by EntityStore, we should also reset\n        // all CacheGroup dependency information.\n        new Set([this.data.group, this.optimisticData.group]).forEach(function (group) {\n            return group.resetCaching();\n        });\n    };\n    InMemoryCache.prototype.restore = function (data) {\n        this.init();\n        // Since calling this.init() discards/replaces the entire StoreReader, along\n        // with the result caches it maintains, this.data.replace(data) won't have\n        // to bother deleting the old data.\n        if (data)\n            this.data.replace(data);\n        return this;\n    };\n    InMemoryCache.prototype.extract = function (optimistic) {\n        if (optimistic === void 0) { optimistic = false; }\n        return (optimistic ? this.optimisticData : this.data).extract();\n    };\n    InMemoryCache.prototype.read = function (options) {\n        var \n        // Since read returns data or null, without any additional metadata\n        // about whether/where there might have been missing fields, the\n        // default behavior cannot be returnPartialData = true (like it is\n        // for the diff method), since defaulting to true would violate the\n        // integrity of the T in the return type. However, partial data may\n        // be useful in some cases, so returnPartialData:true may be\n        // specified explicitly.\n        _a = options.returnPartialData, \n        // Since read returns data or null, without any additional metadata\n        // about whether/where there might have been missing fields, the\n        // default behavior cannot be returnPartialData = true (like it is\n        // for the diff method), since defaulting to true would violate the\n        // integrity of the T in the return type. However, partial data may\n        // be useful in some cases, so returnPartialData:true may be\n        // specified explicitly.\n        returnPartialData = _a === void 0 ? false : _a;\n        try {\n            return (this.storeReader.diffQueryAgainstStore(__assign(__assign({}, options), { store: options.optimistic ? this.optimisticData : this.data, config: this.config, returnPartialData: returnPartialData })).result || null);\n        }\n        catch (e) {\n            if (e instanceof MissingFieldError) {\n                // Swallow MissingFieldError and return null, so callers do not need to\n                // worry about catching \"normal\" exceptions resulting from incomplete\n                // cache data. Unexpected errors will be re-thrown. If you need more\n                // information about which fields were missing, use cache.diff instead,\n                // and examine diffResult.missing.\n                return null;\n            }\n            throw e;\n        }\n    };\n    InMemoryCache.prototype.write = function (options) {\n        try {\n            ++this.txCount;\n            return this.storeWriter.writeToStore(this.data, options);\n        }\n        finally {\n            if (!--this.txCount && options.broadcast !== false) {\n                this.broadcastWatches();\n            }\n        }\n    };\n    InMemoryCache.prototype.modify = function (options) {\n        if (hasOwn.call(options, \"id\") && !options.id) {\n            // To my knowledge, TypeScript does not currently provide a way to\n            // enforce that an optional property?:type must *not* be undefined\n            // when present. That ability would be useful here, because we want\n            // options.id to default to ROOT_QUERY only when no options.id was\n            // provided. If the caller attempts to pass options.id with a\n            // falsy/undefined value (perhaps because cache.identify failed), we\n            // should not assume the goal was to modify the ROOT_QUERY object.\n            // We could throw, but it seems natural to return false to indicate\n            // that nothing was modified.\n            return false;\n        }\n        var store = ((options.optimistic) // Defaults to false.\n        ) ?\n            this.optimisticData\n            : this.data;\n        try {\n            ++this.txCount;\n            return store.modify(options.id || \"ROOT_QUERY\", options.fields);\n        }\n        finally {\n            if (!--this.txCount && options.broadcast !== false) {\n                this.broadcastWatches();\n            }\n        }\n    };\n    InMemoryCache.prototype.diff = function (options) {\n        return this.storeReader.diffQueryAgainstStore(__assign(__assign({}, options), { store: options.optimistic ? this.optimisticData : this.data, rootId: options.id || \"ROOT_QUERY\", config: this.config }));\n    };\n    InMemoryCache.prototype.watch = function (watch) {\n        var _this = this;\n        if (!this.watches.size) {\n            // In case we previously called forgetCache(this) because\n            // this.watches became empty (see below), reattach this cache to any\n            // reactive variables on which it previously depended. It might seem\n            // paradoxical that we're able to recall something we supposedly\n            // forgot, but the point of calling forgetCache(this) is to silence\n            // useless broadcasts while this.watches is empty, and to allow the\n            // cache to be garbage collected. If, however, we manage to call\n            // recallCache(this) here, this cache object must not have been\n            // garbage collected yet, and should resume receiving updates from\n            // reactive variables, now that it has a watcher to notify.\n            recallCache(this);\n        }\n        this.watches.add(watch);\n        if (watch.immediate) {\n            this.maybeBroadcastWatch(watch);\n        }\n        return function () {\n            // Once we remove the last watch from this.watches, cache.broadcastWatches\n            // no longer does anything, so we preemptively tell the reactive variable\n            // system to exclude this cache from future broadcasts.\n            if (_this.watches.delete(watch) && !_this.watches.size) {\n                forgetCache(_this);\n            }\n            // Remove this watch from the LRU cache managed by the\n            // maybeBroadcastWatch OptimisticWrapperFunction, to prevent memory\n            // leaks involving the closure of watch.callback.\n            _this.maybeBroadcastWatch.forget(watch);\n        };\n    };\n    InMemoryCache.prototype.gc = function (options) {\n        var _a;\n        canonicalStringify.reset();\n        print.reset();\n        this.addTypenameTransform.resetCache();\n        (_a = this.config.fragments) === null || _a === void 0 ? void 0 : _a.resetCaches();\n        var ids = this.optimisticData.gc();\n        if (options && !this.txCount) {\n            if (options.resetResultCache) {\n                this.resetResultCache(options.resetResultIdentities);\n            }\n            else if (options.resetResultIdentities) {\n                this.storeReader.resetCanon();\n            }\n        }\n        return ids;\n    };\n    // Call this method to ensure the given root ID remains in the cache after\n    // garbage collection, along with its transitive child entities. Note that\n    // the cache automatically retains all directly written entities. By default,\n    // the retainment persists after optimistic updates are removed. Pass true\n    // for the optimistic argument if you would prefer for the retainment to be\n    // discarded when the top-most optimistic layer is removed. Returns the\n    // resulting (non-negative) retainment count.\n    InMemoryCache.prototype.retain = function (rootId, optimistic) {\n        return (optimistic ? this.optimisticData : this.data).retain(rootId);\n    };\n    // Call this method to undo the effect of the retain method, above. Once the\n    // retainment count falls to zero, the given ID will no longer be preserved\n    // during garbage collection, though it may still be preserved by other safe\n    // entities that refer to it. Returns the resulting (non-negative) retainment\n    // count, in case that's useful.\n    InMemoryCache.prototype.release = function (rootId, optimistic) {\n        return (optimistic ? this.optimisticData : this.data).release(rootId);\n    };\n    // Returns the canonical ID for a given StoreObject, obeying typePolicies\n    // and keyFields (and dataIdFromObject, if you still use that). At minimum,\n    // the object must contain a __typename and any primary key fields required\n    // to identify entities of that type. If you pass a query result object, be\n    // sure that none of the primary key fields have been renamed by aliasing.\n    // If you pass a Reference object, its __ref ID string will be returned.\n    InMemoryCache.prototype.identify = function (object) {\n        if (isReference(object))\n            return object.__ref;\n        try {\n            return this.policies.identify(object)[0];\n        }\n        catch (e) {\n            globalThis.__DEV__ !== false && invariant.warn(e);\n        }\n    };\n    InMemoryCache.prototype.evict = function (options) {\n        if (!options.id) {\n            if (hasOwn.call(options, \"id\")) {\n                // See comment in modify method about why we return false when\n                // options.id exists but is falsy/undefined.\n                return false;\n            }\n            options = __assign(__assign({}, options), { id: \"ROOT_QUERY\" });\n        }\n        try {\n            // It's unlikely that the eviction will end up invoking any other\n            // cache update operations while it's running, but {in,de}crementing\n            // this.txCount still seems like a good idea, for uniformity with\n            // the other update methods.\n            ++this.txCount;\n            // Pass this.data as a limit on the depth of the eviction, so evictions\n            // during optimistic updates (when this.data is temporarily set equal to\n            // this.optimisticData) do not escape their optimistic Layer.\n            return this.optimisticData.evict(options, this.data);\n        }\n        finally {\n            if (!--this.txCount && options.broadcast !== false) {\n                this.broadcastWatches();\n            }\n        }\n    };\n    InMemoryCache.prototype.reset = function (options) {\n        var _this = this;\n        this.init();\n        canonicalStringify.reset();\n        if (options && options.discardWatches) {\n            // Similar to what happens in the unsubscribe function returned by\n            // cache.watch, applied to all current watches.\n            this.watches.forEach(function (watch) { return _this.maybeBroadcastWatch.forget(watch); });\n            this.watches.clear();\n            forgetCache(this);\n        }\n        else {\n            // Calling this.init() above unblocks all maybeBroadcastWatch caching, so\n            // this.broadcastWatches() triggers a broadcast to every current watcher\n            // (letting them know their data is now missing). This default behavior is\n            // convenient because it means the watches do not have to be manually\n            // reestablished after resetting the cache. To prevent this broadcast and\n            // cancel all watches, pass true for options.discardWatches.\n            this.broadcastWatches();\n        }\n        return Promise.resolve();\n    };\n    InMemoryCache.prototype.removeOptimistic = function (idToRemove) {\n        var newOptimisticData = this.optimisticData.removeLayer(idToRemove);\n        if (newOptimisticData !== this.optimisticData) {\n            this.optimisticData = newOptimisticData;\n            this.broadcastWatches();\n        }\n    };\n    InMemoryCache.prototype.batch = function (options) {\n        var _this = this;\n        var update = options.update, _a = options.optimistic, optimistic = _a === void 0 ? true : _a, removeOptimistic = options.removeOptimistic, onWatchUpdated = options.onWatchUpdated;\n        var updateResult;\n        var perform = function (layer) {\n            var _a = _this, data = _a.data, optimisticData = _a.optimisticData;\n            ++_this.txCount;\n            if (layer) {\n                _this.data = _this.optimisticData = layer;\n            }\n            try {\n                return (updateResult = update(_this));\n            }\n            finally {\n                --_this.txCount;\n                _this.data = data;\n                _this.optimisticData = optimisticData;\n            }\n        };\n        var alreadyDirty = new Set();\n        if (onWatchUpdated && !this.txCount) {\n            // If an options.onWatchUpdated callback is provided, we want to call it\n            // with only the Cache.WatchOptions objects affected by options.update,\n            // but there might be dirty watchers already waiting to be broadcast that\n            // have nothing to do with the update. To prevent including those watchers\n            // in the post-update broadcast, we perform this initial broadcast to\n            // collect the dirty watchers, so we can re-dirty them later, after the\n            // post-update broadcast, allowing them to receive their pending\n            // broadcasts the next time broadcastWatches is called, just as they would\n            // if we never called cache.batch.\n            this.broadcastWatches(__assign(__assign({}, options), { onWatchUpdated: function (watch) {\n                    alreadyDirty.add(watch);\n                    return false;\n                } }));\n        }\n        if (typeof optimistic === \"string\") {\n            // Note that there can be multiple layers with the same optimistic ID.\n            // When removeOptimistic(id) is called for that id, all matching layers\n            // will be removed, and the remaining layers will be reapplied.\n            this.optimisticData = this.optimisticData.addLayer(optimistic, perform);\n        }\n        else if (optimistic === false) {\n            // Ensure both this.data and this.optimisticData refer to the root\n            // (non-optimistic) layer of the cache during the update. Note that\n            // this.data could be a Layer if we are currently executing an optimistic\n            // update function, but otherwise will always be an EntityStore.Root\n            // instance.\n            perform(this.data);\n        }\n        else {\n            // Otherwise, leave this.data and this.optimisticData unchanged and run\n            // the update with broadcast batching.\n            perform();\n        }\n        if (typeof removeOptimistic === \"string\") {\n            this.optimisticData = this.optimisticData.removeLayer(removeOptimistic);\n        }\n        // Note: if this.txCount > 0, then alreadyDirty.size === 0, so this code\n        // takes the else branch and calls this.broadcastWatches(options), which\n        // does nothing when this.txCount > 0.\n        if (onWatchUpdated && alreadyDirty.size) {\n            this.broadcastWatches(__assign(__assign({}, options), { onWatchUpdated: function (watch, diff) {\n                    var result = onWatchUpdated.call(this, watch, diff);\n                    if (result !== false) {\n                        // Since onWatchUpdated did not return false, this diff is\n                        // about to be broadcast to watch.callback, so we don't need\n                        // to re-dirty it with the other alreadyDirty watches below.\n                        alreadyDirty.delete(watch);\n                    }\n                    return result;\n                } }));\n            // Silently re-dirty any watches that were already dirty before the update\n            // was performed, and were not broadcast just now.\n            if (alreadyDirty.size) {\n                alreadyDirty.forEach(function (watch) { return _this.maybeBroadcastWatch.dirty(watch); });\n            }\n        }\n        else {\n            // If alreadyDirty is empty or we don't have an onWatchUpdated\n            // function, we don't need to go to the trouble of wrapping\n            // options.onWatchUpdated.\n            this.broadcastWatches(options);\n        }\n        return updateResult;\n    };\n    InMemoryCache.prototype.performTransaction = function (update, optimisticId) {\n        return this.batch({\n            update: update,\n            optimistic: optimisticId || optimisticId !== null,\n        });\n    };\n    InMemoryCache.prototype.transformDocument = function (document) {\n        return this.addTypenameToDocument(this.addFragmentsToDocument(document));\n    };\n    InMemoryCache.prototype.fragmentMatches = function (fragment, typename) {\n        return this.policies.fragmentMatches(fragment, typename);\n    };\n    InMemoryCache.prototype.lookupFragment = function (fragmentName) {\n        var _a;\n        return ((_a = this.config.fragments) === null || _a === void 0 ? void 0 : _a.lookup(fragmentName)) || null;\n    };\n    InMemoryCache.prototype.broadcastWatches = function (options) {\n        var _this = this;\n        if (!this.txCount) {\n            this.watches.forEach(function (c) { return _this.maybeBroadcastWatch(c, options); });\n        }\n    };\n    InMemoryCache.prototype.addFragmentsToDocument = function (document) {\n        var fragments = this.config.fragments;\n        return fragments ? fragments.transform(document) : document;\n    };\n    InMemoryCache.prototype.addTypenameToDocument = function (document) {\n        if (this.addTypename) {\n            return this.addTypenameTransform.transformDocument(document);\n        }\n        return document;\n    };\n    // This method is wrapped by maybeBroadcastWatch, which is called by\n    // broadcastWatches, so that we compute and broadcast results only when\n    // the data that would be broadcast might have changed. It would be\n    // simpler to check for changes after recomputing a result but before\n    // broadcasting it, but this wrapping approach allows us to skip both\n    // the recomputation and the broadcast, in most cases.\n    InMemoryCache.prototype.broadcastWatch = function (c, options) {\n        var lastDiff = c.lastDiff;\n        // Both WatchOptions and DiffOptions extend ReadOptions, and DiffOptions\n        // currently requires no additional properties, so we can use c (a\n        // WatchOptions object) as DiffOptions, without having to allocate a new\n        // object, and without having to enumerate the relevant properties (query,\n        // variables, etc.) explicitly. There will be some additional properties\n        // (lastDiff, callback, etc.), but cache.diff ignores them.\n        var diff = this.diff(c);\n        if (options) {\n            if (c.optimistic && typeof options.optimistic === \"string\") {\n                diff.fromOptimisticTransaction = true;\n            }\n            if (options.onWatchUpdated &&\n                options.onWatchUpdated.call(this, c, diff, lastDiff) === false) {\n                // Returning false from the onWatchUpdated callback will prevent\n                // calling c.callback(diff) for this watcher.\n                return;\n            }\n        }\n        if (!lastDiff || !equal(lastDiff.result, diff.result)) {\n            c.callback((c.lastDiff = diff), lastDiff);\n        }\n    };\n    return InMemoryCache;\n}(ApolloCache));\nexport { InMemoryCache };\nif (globalThis.__DEV__ !== false) {\n    InMemoryCache.prototype.getMemoryInternals = getInMemoryCacheMemoryInternals;\n}\n//# sourceMappingURL=inMemoryCache.js.map", "import { __assign, __spreadArray } from \"tslib\";\nimport { visit } from \"graphql\";\nimport { wrap } from \"optimism\";\nimport { cacheSizes, getFragmentDefinitions, } from \"../../utilities/index.js\";\nimport { WeakCache } from \"@wry/caches\";\n// As long as createFragmentRegistry is not imported or used, the\n// FragmentRegistry example implementation provided below should not be bundled\n// (by tree-shaking bundlers like Rollup), because the implementation of\n// InMemoryCache refers only to the TypeScript interface FragmentRegistryAPI,\n// never the concrete implementation FragmentRegistry (which is deliberately not\n// exported from this module).\nexport function createFragmentRegistry() {\n    var fragments = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        fragments[_i] = arguments[_i];\n    }\n    return new (FragmentRegistry.bind.apply(FragmentRegistry, __spreadArray([void 0], fragments, false)))();\n}\nvar FragmentRegistry = /** @class */ (function () {\n    // Call `createFragmentRegistry` instead of invoking the\n    // FragmentRegistry constructor directly. This reserves the constructor for\n    // future configuration of the FragmentRegistry.\n    function FragmentRegistry() {\n        var fragments = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            fragments[_i] = arguments[_i];\n        }\n        this.registry = Object.create(null);\n        this.resetCaches();\n        if (fragments.length) {\n            this.register.apply(this, fragments);\n        }\n    }\n    FragmentRegistry.prototype.register = function () {\n        var _this = this;\n        var fragments = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            fragments[_i] = arguments[_i];\n        }\n        var definitions = new Map();\n        fragments.forEach(function (doc) {\n            getFragmentDefinitions(doc).forEach(function (node) {\n                definitions.set(node.name.value, node);\n            });\n        });\n        definitions.forEach(function (node, name) {\n            if (node !== _this.registry[name]) {\n                _this.registry[name] = node;\n                _this.invalidate(name);\n            }\n        });\n        return this;\n    };\n    // Overridden in the resetCaches method below.\n    FragmentRegistry.prototype.invalidate = function (name) { };\n    FragmentRegistry.prototype.resetCaches = function () {\n        var proto = FragmentRegistry.prototype;\n        this.invalidate = (this.lookup = wrap(proto.lookup.bind(this), {\n            makeCacheKey: function (arg) { return arg; },\n            max: cacheSizes[\"fragmentRegistry.lookup\"] ||\n                1000 /* defaultCacheSizes[\"fragmentRegistry.lookup\"] */,\n        })).dirty; // This dirty function is bound to the wrapped lookup method.\n        this.transform = wrap(proto.transform.bind(this), {\n            cache: WeakCache,\n            max: cacheSizes[\"fragmentRegistry.transform\"] ||\n                2000 /* defaultCacheSizes[\"fragmentRegistry.transform\"] */,\n        });\n        this.findFragmentSpreads = wrap(proto.findFragmentSpreads.bind(this), {\n            cache: WeakCache,\n            max: cacheSizes[\"fragmentRegistry.findFragmentSpreads\"] ||\n                4000 /* defaultCacheSizes[\"fragmentRegistry.findFragmentSpreads\"] */,\n        });\n    };\n    /*\n     * Note:\n     * This method is only memoized so it can serve as a dependency to `tranform`,\n     * so calling `invalidate` will invalidate cache entries for `transform`.\n     */\n    FragmentRegistry.prototype.lookup = function (fragmentName) {\n        return this.registry[fragmentName] || null;\n    };\n    FragmentRegistry.prototype.transform = function (document) {\n        var _this = this;\n        var defined = new Map();\n        getFragmentDefinitions(document).forEach(function (def) {\n            defined.set(def.name.value, def);\n        });\n        var unbound = new Set();\n        var enqueue = function (spreadName) {\n            if (!defined.has(spreadName)) {\n                unbound.add(spreadName);\n            }\n        };\n        var enqueueChildSpreads = function (node) {\n            return Object.keys(_this.findFragmentSpreads(node)).forEach(enqueue);\n        };\n        enqueueChildSpreads(document);\n        var missing = [];\n        var map = Object.create(null);\n        // This Set forEach loop can be extended during iteration by adding\n        // additional strings to the unbound set.\n        unbound.forEach(function (fragmentName) {\n            var knownFragmentDef = defined.get(fragmentName);\n            if (knownFragmentDef) {\n                enqueueChildSpreads((map[fragmentName] = knownFragmentDef));\n            }\n            else {\n                missing.push(fragmentName);\n                var def = _this.lookup(fragmentName);\n                if (def) {\n                    enqueueChildSpreads((map[fragmentName] = def));\n                }\n            }\n        });\n        if (missing.length) {\n            var defsToAppend_1 = [];\n            missing.forEach(function (name) {\n                var def = map[name];\n                if (def) {\n                    defsToAppend_1.push(def);\n                }\n            });\n            if (defsToAppend_1.length) {\n                document = __assign(__assign({}, document), { definitions: document.definitions.concat(defsToAppend_1) });\n            }\n        }\n        return document;\n    };\n    FragmentRegistry.prototype.findFragmentSpreads = function (root) {\n        var spreads = Object.create(null);\n        visit(root, {\n            FragmentSpread: function (node) {\n                spreads[node.name.value] = node;\n            },\n        });\n        return spreads;\n    };\n    return FragmentRegistry;\n}());\n//# sourceMappingURL=fragmentRegistry.js.map"], "names": ["__assign", "__spread<PERSON><PERSON>y", "__rest", "equal", "getMainDefinition", "createFragmentMap", "getFragmentDefinitions", "shouldInclude", "isField", "resultKeyNameFromField", "getFragmentFromSelection", "wrap", "getFragmentQueryDocument", "cacheSizes", "<PERSON>ak<PERSON><PERSON>", "getFragmentDefinition", "invariant", "Observable", "maskFragment", "mergeDeepArray", "<PERSON><PERSON>", "__extends", "compact", "isReference", "isNonNullObject", "isArray", "DeepMerger", "EntityStore", "maybeDeepFreeze", "makeReference", "dep", "<PERSON><PERSON>", "canUseWeakMap", "isObjectOrArray", "canUseWeakSet", "getDefaultValues", "getQueryDefinition", "canonicalStringify", "addTypenameToDocument", "Kind", "newInvariantError", "Slot", "isNonEmptyArray", "argumentsObjectFromField", "disableWarningsSlot", "storeKeyNameFromField", "getStoreKeyName", "stringifyForDisplay", "getOperationDefinition", "getTypenameFromResult", "cloneDeep", "DocumentTransform", "print", "visit"], "mappings": ";;;;;;;;;;;;;;;;;;AAiBO,IAAI,+BAA+B,GAAG,UAAU,CAAC,OAAO,KAAK,KAAK;AACzE,IAAI,gCAAgC;AACpC,MAAM,SAAS,CAAC;AAKT,IAAI,6BAA6B,GAAG,UAAU,CAAC,OAAO,KAAK,KAAK;AACvE,IAAI,8BAA8B;AAClC,MAAM,SAAS,CAAC;AAuChB,SAAS,8BAA8B,GAAG;AAC1C,IAAI,OAAO;AACX,QAAQ,KAAK,EAAE;AACf,YAAY,sBAAsB,EAAE,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACjF,SAAS;AACT,KAAK,CAAC;AACN,CAAC;AACD,SAAS,gCAAgC,GAAG;AAC5C,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;AAC1C,IAAI,OAAOA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,8BAA8B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,4BAA4B,EAAE,aAAa,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC,EAAE,aAAa,EAAE;AAC1K,YAAY,mBAAmB,EAAE,qBAAqB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,qBAAqB,CAAC,CAAC;AAClG,YAAY,uBAAuB,EAAE,qBAAqB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,yBAAyB,CAAC,CAAC;AAC1G,YAAY,mBAAmB,EAAE,qBAAqB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;AACnF,SAAS,EAAE,gBAAgB,EAAE;AAC7B,YAAY,mBAAmB,EAAE,qBAAqB,CAAC,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,mBAAmB,CAAC;AAC3I,YAAY,MAAM,EAAE,qBAAqB,CAAC,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;AACjH,YAAY,SAAS,EAAE,qBAAqB,CAAC,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC;AACvH,SAAS,EAAE,CAAC,CAAC;AACb,CAAC;AACD,SAAS,SAAS,CAAC,CAAC,EAAE;AACtB,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,UAAU,IAAI,CAAC,CAAC;AAClC,CAAC;AACD,SAAS,qBAAqB,CAAC,CAAC,EAAE;AAClC,IAAI,OAAO,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,SAAS,CAAC;AAC7C,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,IAAI,OAAO,KAAK,IAAI,IAAI,CAAC;AACzB,CAAC;AACD,SAAS,aAAa,CAAC,SAAS,EAAE;AAClC,IAAI,OAAO,oBAAoB,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;AAChG,CAAC;AACD,SAAS,oBAAoB,CAAC,SAAS,EAAE;AACzC,IAAI,OAAO,SAAS;AACpB,QAAQC,mBAAa,CAACA,mBAAa,CAAC;AACpC,YAAY,qBAAqB,CAAC,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,CAAC;AACjH,SAAS,EAAE,oBAAoB,CAAC,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,oBAAoB,CAAC,SAAS,KAAK,IAAI,IAAI,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;AACxO,UAAU,EAAE,CAAC;AACb;;ACjGO,SAAS,YAAY,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;AACvD,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,GAAGC,YAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACtD,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,GAAGA,YAAM,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACtD,IAAI,QAAQC,cAAK,CAAC,KAAK,EAAE,KAAK,CAAC;AAC/B,QAAQ,mBAAmB,CAACC,2BAAiB,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE;AACjF,YAAY,WAAW,EAAEC,2BAAiB,CAACC,gCAAsB,CAAC,KAAK,CAAC,CAAC;AACzE,YAAY,SAAS,EAAE,SAAS;AAChC,SAAS,CAAC,EAAE;AACZ,CAAC;AACD,SAAS,mBAAmB,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE;AACtE,IAAI,IAAI,OAAO,KAAK,OAAO,EAAE;AAC7B,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;AAInC,IAAI,OAAO,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,SAAS,EAAE;AAG9D,QAAQ,IAAI,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC;AACzC,YAAY,OAAO,IAAI,CAAC;AACxB,QAAQ,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAEtC,QAAQ,IAAI,CAACC,uBAAa,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC;AACxD,YAAY,OAAO,IAAI,CAAC;AAGxB,QAAQ,IAAI,gCAAgC,CAAC,SAAS,CAAC;AACvD,YAAY,OAAO,IAAI,CAAC;AACxB,QAAQ,IAAIC,iBAAO,CAAC,SAAS,CAAC,EAAE;AAChC,YAAY,IAAI,SAAS,GAAGC,gCAAsB,CAAC,SAAS,CAAC,CAAC;AAC9D,YAAY,IAAI,YAAY,GAAG,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC;AAC7D,YAAY,IAAI,YAAY,GAAG,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC;AAC7D,YAAY,IAAI,iBAAiB,GAAG,SAAS,CAAC,YAAY,CAAC;AAC3D,YAAY,IAAI,CAAC,iBAAiB,EAAE;AAGpC,gBAAgB,OAAON,cAAK,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;AACzD,aAAa;AACb,YAAY,IAAI,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC5D,YAAY,IAAI,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC5D,YAAY,IAAI,aAAa,KAAK,aAAa;AAC/C,gBAAgB,OAAO,KAAK,CAAC;AAC7B,YAAY,IAAI,aAAa,IAAI,aAAa,EAAE;AAChD,gBAAgB,IAAI,QAAQ,GAAG,YAAY,CAAC,MAAM,CAAC;AACnD,gBAAgB,IAAI,YAAY,CAAC,MAAM,KAAK,QAAQ,EAAE;AACtD,oBAAoB,OAAO,KAAK,CAAC;AACjC,iBAAiB;AACjB,gBAAgB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,EAAE,CAAC,EAAE;AACnD,oBAAoB,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE;AAC5G,wBAAwB,OAAO,KAAK,CAAC;AACrC,qBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,YAAY,OAAO,mBAAmB,CAAC,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AAC/F,SAAS;AACT,aAAa;AACb,YAAY,IAAI,QAAQ,GAAGO,kCAAwB,CAAC,SAAS,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC;AACpF,YAAY,IAAI,QAAQ,EAAE;AAG1B,gBAAgB,IAAI,gCAAgC,CAAC,QAAQ,CAAC;AAC9D,oBAAoB,OAAO,IAAI,CAAC;AAChC,gBAAgB,OAAO,mBAAmB,CAAC,QAAQ,CAAC,YAAY;AAKhE,gBAAgB,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC3C,aAAa;AACb,SAAS;AACT,KAAK,CAAC,CAAC;AACP,CAAC;AACD,SAAS,gCAAgC,CAAC,SAAS,EAAE;AACrD,IAAI,QAAQ,CAAC,CAAC,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,EAAE;AACzF,CAAC;AACD,SAAS,sBAAsB,CAAC,GAAG,EAAE;AACrC,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,aAAa,CAAC;AAC5C;;AC7EG,IAAC,WAAW,KAAkB,YAAY;AAC7C,IAAI,SAAS,WAAW,GAAG;AAC3B,QAAQ,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;AAG5C,QAAQ,IAAI,CAAC,cAAc,GAAGC,aAAI,CAACC,kCAAwB,EAAE;AAC7D,YAAY,GAAG,EAAEC,oBAAU,CAAC,8BAA8B,CAAC;AAC3D,gBAAgB,IAAI;AACpB,YAAY,KAAK,EAAEC,gBAAS;AAC5B,SAAS,CAAC,CAAC;AACX,KAAK;AAIL,IAAI,WAAW,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,YAAY,EAAE;AACnE,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AAON,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,EAAE;AACrD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,YAAY,GAAG,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,GAAG,OAAO,CAAC,UAAU;AACtF,cAAc,OAAO,CAAC,UAAU,KAAK,KAAK,GAAG,IAAI;AACjD,kBAAkB,KAAK,CAAC,CAAC;AACzB,QAAQ,IAAI,YAAY,CAAC;AACzB,QAAQ,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,QAAQ,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC;AAC9G,QAAQ,OAAO,YAAY,CAAC;AAC5B,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,2BAA2B,GAAG,UAAU,WAAW,EAAE,YAAY,EAAE;AAC7F,QAAQ,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AAC3D,KAAK,CAAC;AAIN,IAAI,WAAW,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,QAAQ,EAAE;AAClE,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK,CAAC;AAGN,IAAI,WAAW,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,QAAQ,EAAE;AACjE,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,MAAM,EAAE;AACvD,QAAQ,OAAO;AACf,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE,GAAG,YAAY;AAC3C,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,OAAO,EAAE;AACtD,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AAEN,IAAI,WAAW,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,OAAO,EAAE,UAAU,EAAE;AACrE,QAAQ,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AACzE,QAAQ,OAAO,IAAI,CAAC,IAAI,CAACd,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;AAC1H,KAAK,CAAC;AAEN,IAAI,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,OAAO,EAAE;AAC7D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,YAAY,GAAG,OAAO,CAAC,YAAY,EAAE,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,EAAE,GAAG,OAAO,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,EAAE,YAAY,GAAGE,YAAM,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC;AACvP,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AAOhE,QAAQ,IAAI,EAAE,GAAG,OAAO,IAAI,KAAK,WAAW,IAAI,OAAO,IAAI,KAAK,QAAQ;AACxE,YAAY,IAAI;AAChB,cAAc,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAClC,QAAQ,IAAI,WAAW,GAAG,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC;AACtE,QAAQ,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAC1C,YAAY,IAAI,kBAAkB,GAAG,YAAY,IAAIa,+BAAqB,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;AAChG,YAAY,IAAI,CAAC,EAAE,EAAE;AACrB,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIC,iBAAS,CAAC,IAAI,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;AACtF,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,WAAW,GAAGhB,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE,iBAAiB,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC;AAC1I,QAAQ,IAAI,UAAU,CAAC;AACvB,QAAQ,OAAO,IAAIiB,oBAAU,CAAC,UAAU,QAAQ,EAAE;AAClD,YAAY,OAAO,KAAK,CAAC,KAAK,CAACjB,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,IAAI,EAAE;AAChH,oBAAoB,IAAI,IAAI,GAAG,WAAW;AAC1C,wBAAwBkB,oBAAY,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,CAAC;AAChF,0BAA0B,IAAI,CAAC,MAAM,CAAC;AACtC,oBAAoB;AAEpB,oBAAoB,UAAU;AAC9B,wBAAwB,YAAY,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;AAGvF,wBAAwB,OAAO,CAAC,SAAS,CAAC,EAAE;AAC5C,wBAAwB,OAAO;AAC/B,qBAAqB;AACrB,oBAAoB,IAAI,MAAM,GAAG;AACjC,wBAAwB,IAAI,EAAE,IAAI;AAClC,wBAAwB,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,QAAQ;AACjD,qBAAqB,CAAC;AACtB,oBAAoB,IAAI,IAAI,CAAC,OAAO,EAAE;AACtC,wBAAwB,MAAM,CAAC,OAAO,GAAGC,wBAAc,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACtH,qBAAqB;AACrB,oBAAoB,UAAU,GAAGnB,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AAChF,oBAAoB,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1C,iBAAiB,EAAE,CAAC,CAAC,CAAC;AACtB,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,OAAO,EAAE,UAAU,EAAE;AACxE,QAAQ,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AACzE,QAAQ,OAAO,IAAI,CAAC,IAAI,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;AAC9K,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,EAAE,EAAE;AACrD,QAAQ,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,OAAO,GAAGE,YAAM,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7E,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;AACjD,YAAY,MAAM,EAAE,EAAE,IAAI,YAAY;AACtC,YAAY,MAAM,EAAE,IAAI;AACxB,SAAS,CAAC,CAAC,CAAC;AACZ,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,EAAE,EAAE;AACxD,QAAQ,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,OAAO,GAAGA,YAAM,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC;AACjK,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;AACjD,YAAY,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,YAAY,CAAC;AAC9D,YAAY,MAAM,EAAE,EAAE;AACtB,YAAY,MAAM,EAAE,IAAI;AACxB,SAAS,CAAC,CAAC,CAAC;AACZ,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,OAAO,EAAE,MAAM,EAAE;AACnE,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC;AAC1B,YAAY,MAAM,EAAE,UAAU,KAAK,EAAE;AACrC,gBAAgB,IAAI,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACrD,gBAAgB,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACzC,gBAAgB,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI;AACpD,oBAAoB,OAAO,KAAK,CAAC;AACjC,gBAAgB,KAAK,CAAC,UAAU,CAACF,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAClF,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,OAAO,EAAE,MAAM,EAAE;AACtE,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC;AAC1B,YAAY,MAAM,EAAE,UAAU,KAAK,EAAE;AACrC,gBAAgB,IAAI,KAAK,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;AACxD,gBAAgB,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACzC,gBAAgB,IAAI,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,IAAI;AACpD,oBAAoB,OAAO,KAAK,CAAC;AACjC,gBAAgB,KAAK,CAAC,aAAa,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACrF,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC,EAAE,EAAE;AAEL,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAClC,IAAI,WAAW,CAAC,SAAS,CAAC,kBAAkB,GAAG,6BAA6B,CAAC;AAC7E;;ACrKWoB,uBAAM;AACjB,CAAC,UAAU,KAAK,EAAE;AAClB,CAAC,EAAEA,aAAK,KAAKA,aAAK,GAAG,EAAE,CAAC,CAAC;;ACDtB,IAAC,iBAAiB,KAAkB,UAAU,MAAM,EAAE;AACzD,IAAIC,eAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;AACzC,IAAI,SAAS,iBAAiB,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;AAChE,QAAQ,IAAI,EAAE,CAAC;AAEf,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC;AACvD,QAAQ,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC,QAAQ,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AAC1B,QAAQ,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AAC5B,QAAQ,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AACpC,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AACvC,YAAY,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAC1C,YAAY,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AAC7D,gBAAgB,KAAK,CAAC,OAAO,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AACjF,aAAa;AACb,SAAS;AACT,aAAa;AACb,YAAY,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;AACvC,SAAS;AAGT,QAAQ,KAAK,CAAC,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC;AACtD,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,iBAAiB,CAAC;AAC7B,CAAC,CAAC,KAAK,CAAC;;ACzBD,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AAC7C,SAAS,SAAS,CAAC,KAAK,EAAE;AACjC,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,CAAC,CAAC;AAC9C,CAAC;AAEM,SAAS,uBAAuB,CAAC,EAAE,EAAE,OAAO,EAAE;AACrD,IAAI,IAAI,UAAU,GAAG,EAAE,CAAC,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC;AAC7D,IAAI,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AACxC,QAAQ,IAAI,OAAO,EAAE;AACrB,YAAY,OAAO,CAAC,SAAS;AAC7B,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE;AAC3C,sBAAsB,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACpD,0BAA0B,KAAK,CAAC,CAAC;AACjC,SAAS;AAET,QAAQ,IAAI,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;AAC9C,YAAY,EAAE,GAAG,GAAG,CAAC;AACrB,SAAS;AACT,QAAQ,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE;AAC5B,YAAY,OAAO,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,QAAQ,IAAI,OAAO,EAAE,KAAK,QAAQ;AACrG,gBAAgB,EAAE;AAClB,kBAAkB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;AACtC,SAAS;AACT,KAAK;AACL,CAAC;AACD,IAAI,aAAa,GAAG;AACpB,IAAI,gBAAgB,EAAE,uBAAuB;AAC7C,IAAI,WAAW,EAAE,IAAI;AACrB,IAAI,aAAa,EAAE,IAAI;AAGvB,IAAI,eAAe,EAAE,KAAK;AAC1B,CAAC,CAAC;AACK,SAAS,eAAe,CAAC,MAAM,EAAE;AACxC,IAAI,OAAOC,iBAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC1C,CAAC;AACM,SAAS,qBAAqB,CAAC,MAAM,EAAE;AAC9C,IAAI,IAAI,KAAK,GAAG,MAAM,CAAC,eAAe,CAAC;AACvC,IAAI,OAAO,KAAK,KAAK,KAAK,CAAC,GAAG,aAAa,CAAC,eAAe,GAAG,KAAK,CAAC;AACpE,CAAC;AACM,SAAS,0BAA0B,CAAC,KAAK,EAAE,iBAAiB,EAAE;AACrE,IAAI,OAAOC,qBAAW,CAAC,iBAAiB,CAAC;AACzC,QAAQ,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,CAAC;AACxD,UAAU,iBAAiB,IAAI,iBAAiB,CAAC,UAAU,CAAC;AAC5D,CAAC;AACM,IAAI,qBAAqB,GAAG,oBAAoB,CAAC;AACjD,SAAS,sBAAsB,CAAC,cAAc,EAAE;AACvD,IAAI,IAAI,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAC5D,IAAI,OAAO,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC;AAC7C,CAAC;AACM,SAAS,yBAAyB,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE;AAC3E,IAAI,IAAIC,yBAAe,CAAC,MAAM,CAAC,EAAE;AACjC,QAAQ,OAAOC,iBAAO,CAAC,MAAM,CAAC;AAC9B,YAAY,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE;AACzC,gBAAgB,OAAO,yBAAyB,CAAC,YAAY,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AAChF,aAAa,CAAC;AACd,cAAc,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,KAAK,EAAE;AAC7D,gBAAgB,IAAIjB,iBAAO,CAAC,KAAK,CAAC,IAAID,uBAAa,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;AACvE,oBAAoB,IAAI,GAAG,GAAGE,gCAAsB,CAAC,KAAK,CAAC,CAAC;AAC5D,oBAAoB,QAAQ,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC;AACpD,yBAAyB,CAAC,KAAK,CAAC,YAAY;AAC5C,4BAA4B,yBAAyB,CAAC,KAAK,CAAC,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE;AACpG,iBAAiB;AAMjB,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa,CAAC,CAAC;AACf,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACM,SAAS,uBAAuB,CAAC,KAAK,EAAE;AAC/C,IAAI,OAAOe,yBAAe,CAAC,KAAK,CAAC,IAAI,CAACD,qBAAW,CAAC,KAAK,CAAC,IAAI,CAACE,iBAAO,CAAC,KAAK,CAAC,CAAC;AAC5E,CAAC;AACM,SAAS,yBAAyB,GAAG;AAC5C,IAAI,OAAO,IAAIC,oBAAU,EAAE,CAAC;AAC5B,CAAC;AACM,SAAS,sBAAsB,CAAC,QAAQ,EAAE,SAAS,EAAE;AAG5D,IAAI,IAAI,WAAW,GAAGrB,2BAAiB,CAACC,gCAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC1E,IAAI,OAAO;AACX,QAAQ,WAAW,EAAE,WAAW;AAChC,QAAQ,cAAc,EAAE,UAAU,IAAI,EAAE;AACxC,YAAY,IAAI,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;AACxC,YAAY,IAAI,CAAC,GAAG,IAAI,SAAS,EAAE;AACnC,gBAAgB,GAAG,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7C,aAAa;AACb,YAAY,OAAO,GAAG,IAAI,IAAI,CAAC;AAC/B,SAAS;AACT,KAAK,CAAC;AACN;;ACvFA,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACjC,IAAI,WAAW,GAAG,YAAY,EAAE,OAAO,MAAM,CAAC,EAAE,CAAC;AACjD,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACjCqB,mBAAW,KAAkB,YAAY;AAC7C,IAAI,SAAS,WAAW,CAAC,QAAQ,EAAE,KAAK,EAAE;AAC1C,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAIxC,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAE3C,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAGxC,QAAQ,IAAI,CAAC,aAAa,GAAG,UAAU,iBAAiB,EAAE,cAAc,EAAE;AAC1E,YAAY,OAAOC,yBAAe,CAACL,qBAAW,CAAC,iBAAiB,CAAC;AACjE,gBAAgB,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,EAAE,cAAc,CAAC;AAClE,kBAAkB,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,CAAC,CAAC,CAAC;AAC1E,SAAS,CAAC;AAIV,QAAQ,IAAI,CAAC,OAAO,GAAG,UAAU,QAAQ,EAAE;AAC3C,YAAY,OAAOA,qBAAW,CAAC,QAAQ,CAAC;AACxC,gBAAgB,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC;AACzC,kBAAkB,OAAO,QAAQ,KAAK,QAAQ,CAAC;AAC/C,SAAS,CAAC;AAKV,QAAQ,IAAI,CAAC,WAAW,GAAG,UAAU,YAAY,EAAE,cAAc,EAAE;AACnE,YAAY,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;AAClD,gBAAgB,OAAOM,uBAAa,CAAC,YAAY,CAAC,CAAC;AACnD,aAAa;AACb,YAAY,IAAIN,qBAAW,CAAC,YAAY,CAAC,EAAE;AAC3C,gBAAgB,OAAO,YAAY,CAAC;AACpC,aAAa;AACb,YAAY,IAAI,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,YAAY,IAAI,EAAE,EAAE;AACpB,gBAAgB,IAAI,GAAG,GAAGM,uBAAa,CAAC,EAAE,CAAC,CAAC;AAC5C,gBAAgB,IAAI,cAAc,EAAE;AACpC,oBAAoB,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC;AAClD,iBAAiB;AACjB,gBAAgB,OAAO,GAAG,CAAC;AAC3B,aAAa;AACb,SAAS,CAAC;AACV,KAAK;AAIL,IAAI,WAAW,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY;AACjD,QAAQ,OAAO7B,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACvC,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,MAAM,EAAE;AAClD,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC;AACpD,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,MAAM,EAAE,SAAS,EAAE;AAC7D,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAC7C,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;AAC5C,YAAY,IAAI,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChD,YAAY,IAAI,WAAW,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,EAAE;AACpE,gBAAgB,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;AAC9C,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,SAAS,KAAK,YAAY;AACtC,YAAY,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,MAAM,CAAC,EAAE;AAClE,YAAY,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAC3D,SAAS;AACT,QAAQ,IAAI,IAAI,YAAY,KAAK,EAAE;AACnC,YAAY,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACtD,SAAS;AACT,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,MAAM,EAAE,iBAAiB,EAAE;AAMxE,QAAQ,IAAI,iBAAiB;AAC7B,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAClD,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;AAC5C,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,SAAS;AACT,QAAQ,IAAI,IAAI,YAAY,KAAK,EAAE;AACnC,YAAY,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;AACjE,SAAS;AACT,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE;AACrD,YAAY,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACvC,SAAS;AACT,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,KAAK,EAAE,KAAK,EAAE;AAC1D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,MAAM,CAAC;AAEnB,QAAQ,IAAIuB,qBAAW,CAAC,KAAK,CAAC;AAC9B,YAAY,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAChC,QAAQ,IAAIA,qBAAW,CAAC,KAAK,CAAC;AAC9B,YAAY,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;AAChC,QAAQ,IAAI,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC;AACzF,QAAQ,IAAI,QAAQ,GAAG,OAAO,KAAK,KAAK,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG,KAAK,CAAC;AAGzF,QAAQ,IAAI,CAAC,QAAQ;AACrB,YAAY,OAAO;AACnB,QAAQP,iBAAS,CAAC,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC,CAAC,CAAC;AACjD,QAAQ,IAAI,MAAM,GAAG,IAAIU,oBAAU,CAAC,qBAAqB,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAGrF,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;AACnC,QAAQ,IAAI,MAAM,KAAK,QAAQ,EAAE;AACjC,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACrC,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AACpC,gBAAgB,IAAI,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAI1D,gBAAgB,IAAI,CAAC,QAAQ;AAC7B,oBAAoB,eAAe,CAAC,QAAQ,GAAG,CAAC,CAAC;AAGjD,gBAAgB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,cAAc,EAAE;AACxE,oBAAoB,IAAI,CAAC,QAAQ;AACjC,wBAAwB,QAAQ,CAAC,cAAc,CAAC,KAAK,MAAM,CAAC,cAAc,CAAC,EAAE;AAG7E,wBAAwB,eAAe,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AAQ5D,wBAAwB,IAAI,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;AAC/E,wBAAwB,IAAI,SAAS,KAAK,cAAc;AACxD,4BAA4B,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE;AACtF,4BAA4B,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC3D,yBAAyB;AAIzB,wBAAwB,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE,KAAK,YAAY,KAAK,CAAC,EAAE;AAC5F,4BAA4B,OAAO,MAAM,CAAC,cAAc,CAAC,CAAC;AAC1D,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,IAAI,eAAe,CAAC,UAAU;AAC9C,oBAAoB,EAAE,QAAQ,IAAI,QAAQ,CAAC,UAAU,CAAC;AAKtD,oBAAoB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC,UAAU,EAAE;AACnF,oBAAoB,OAAO,eAAe,CAAC,UAAU,CAAC;AACtD,iBAAiB;AACjB,gBAAgB,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAC1E,oBAAoB,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AAChE,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE;AAC7D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9C,QAAQ,IAAI,WAAW,EAAE;AACzB,YAAY,IAAI,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtD,YAAY,IAAI,aAAa,GAAG,KAAK,CAAC;AACtC,YAAY,IAAI,YAAY,GAAG,IAAI,CAAC;AACpC,YAAY,IAAI,eAAe,GAAG;AAClC,gBAAgB,MAAM,EAAE,MAAM;AAC9B,gBAAgB,UAAU,EAAE,UAAU;AACtC,gBAAgB,WAAW,EAAEH,qBAAW;AACxC,gBAAgB,WAAW,EAAE,IAAI,CAAC,WAAW;AAC7C,gBAAgB,OAAO,EAAE,IAAI,CAAC,OAAO;AACrC,gBAAgB,SAAS,EAAE,UAAU,kBAAkB,EAAE,IAAI,EAAE;AAC/D,oBAAoB,OAAO,KAAK,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,kBAAkB,KAAK,QAAQ;AAC1F,wBAAwB;AACxB,4BAA4B,SAAS,EAAE,kBAAkB;AACzD,4BAA4B,IAAI,EAAE,IAAI,IAAIM,uBAAa,CAAC,MAAM,CAAC;AAC/D,yBAAyB;AACzB,0BAA0B,kBAAkB,EAAE,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;AAChE,iBAAiB;AACjB,aAAa,CAAC;AACd,YAAY,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,cAAc,EAAE;AACvE,gBAAgB,IAAI,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;AACvE,gBAAgB,IAAI,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC;AAC7D,gBAAgB,IAAI,UAAU,KAAK,KAAK,CAAC;AACzC,oBAAoB,OAAO;AAC3B,gBAAgB,IAAI,MAAM,GAAG,OAAO,MAAM,KAAK,UAAU,GAAG,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;AACnH,gBAAgB,IAAI,MAAM,EAAE;AAC5B,oBAAoB,IAAI,QAAQ,GAAG,MAAM,KAAK,WAAW,GAAG,MAAM,IAAI,MAAM,CAACD,yBAAe,CAAC,UAAU,CAAC,EAAE5B,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACjQ,oBAAoB,IAAI,QAAQ,KAAK,UAAU,EAAE;AACjD,wBAAwB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAClE,qBAAqB;AACrB,yBAAyB;AACzB,wBAAwB,IAAI,QAAQ,KAAK,MAAM;AAC/C,4BAA4B,QAAQ,GAAG,KAAK,CAAC,CAAC;AAC9C,wBAAwB,IAAI,QAAQ,KAAK,UAAU,EAAE;AACrD,4BAA4B,eAAe,CAAC,cAAc,CAAC,GAAG,QAAQ,CAAC;AACvE,4BAA4B,aAAa,GAAG,IAAI,CAAC;AACjD,4BAA4B,UAAU,GAAG,QAAQ,CAAC;AAClD,4BAA4B,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAC9D,gCAAgC,IAAI,cAAc,GAAG,UAAU,GAAG,EAAE;AACpE,oCAAoC,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;AAC/E,wCAAwC,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIgB,iBAAS,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;AAC/F,wCAAwC,OAAO,IAAI,CAAC;AACpD,qCAAqC;AACrC,iCAAiC,CAAC;AAClC,gCAAgC,IAAIO,qBAAW,CAAC,QAAQ,CAAC,EAAE;AAC3D,oCAAoC,cAAc,CAAC,QAAQ,CAAC,CAAC;AAC7D,iCAAiC;AACjC,qCAAqC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAElE,oCAAoC,IAAI,aAAa,GAAG,KAAK,CAAC;AAC9D,oCAAoC,IAAI,gBAAgB,GAAG,KAAK,CAAC,CAAC;AAClE,oCAAoC,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,UAAU,GAAG,QAAQ,EAAE,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAC1G,wCAAwC,IAAI,KAAK,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;AACnE,wCAAwC,IAAIA,qBAAW,CAAC,KAAK,CAAC,EAAE;AAChE,4CAA4C,aAAa,GAAG,IAAI,CAAC;AACjE,4CAA4C,IAAI,cAAc,CAAC,KAAK,CAAC;AACrE,gDAAgD,MAAM;AACtD,yCAAyC;AACzC,6CAA6C;AAG7C,4CAA4C,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE;AACtF,gDAAgD,IAAI,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAE3F,gDAAgD,IAAI,EAAE,EAAE;AACxD,oDAAoD,gBAAgB,GAAG,KAAK,CAAC;AAC7E,iDAAiD;AACjD,6CAA6C;AAC7C,yCAAyC;AACzC,wCAAwC,IAAI,aAAa,IAAI,gBAAgB,KAAK,SAAS,EAAE;AAC7F,4CAA4C,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIP,iBAAS,CAAC,IAAI,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC;AAChH,4CAA4C,MAAM;AAClD,yCAAyC;AACzC,qCAAqC;AACrC,iCAAiC;AACjC,6BAA6B;AAC7B,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB;AACjB,gBAAgB,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE;AAC3C,oBAAoB,YAAY,GAAG,KAAK,CAAC;AACzC,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,YAAY,IAAI,aAAa,EAAE;AAC/B,gBAAgB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;AACpD,gBAAgB,IAAI,YAAY,EAAE;AAClC,oBAAoB,IAAI,IAAI,YAAY,KAAK,EAAE;AAC/C,wBAAwB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC;AACnD,qBAAqB;AACrB,yBAAyB;AACzB,wBAAwB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjD,qBAAqB;AACrB,oBAAoB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AACzD,iBAAiB;AACjB,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AAON,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE;AACtE,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9C,QAAQ,IAAI,WAAW,EAAE;AACzB,YAAY,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AACzE,YAAY,IAAI,cAAc,GAAG,SAAS,IAAI,IAAI;AAClD,gBAAgB,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACzG,kBAAkB,SAAS,CAAC;AAC5B,YAAY,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,IAAI,EAAE,GAAG,EAAE;AAChE,gBAAgB,EAAE,CAAC,cAAc,CAAC,GAAG,WAAW;AAChD,gBAAgB,EAAE,IAAI,WAAW,CAAC,CAAC;AACnC,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,EAAE,KAAK,EAAE;AAC5D,QAAQ,IAAI,OAAO,GAAG,KAAK,CAAC;AAC5B,QAAQ,IAAI,OAAO,CAAC,EAAE,EAAE;AACxB,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC,EAAE;AACpD,gBAAgB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;AACnF,aAAa;AACb,YAAY,IAAI,IAAI,YAAY,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE;AACzD,gBAAgB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC;AACvE,aAAa;AAKb,YAAY,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,EAAE;AAC9C,gBAAgB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,IAAI,UAAU,CAAC,CAAC;AAC9E,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG,YAAY;AAC9C,QAAQ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY;AAChD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AAClC,QAAQ,IAAI,YAAY,GAAG,EAAE,CAAC;AAC9B,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;AAClD,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,iBAAiB,EAAE,EAAE,CAAC,EAAE;AACpE,gBAAgB,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,YAAY,CAAC,MAAM,EAAE;AACjC,YAAY,GAAG,CAAC,MAAM,GAAG,EAAE,YAAY,EAAE,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC;AAC/D,SAAS;AACT,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,OAAO,EAAE;AACvD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;AACzD,YAAY,IAAI,EAAE,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,EAAE;AAC5D,gBAAgB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACrC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,OAAO,EAAE;AACrB,YAAY,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,MAAM,GAAGd,YAAM,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC9E,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;AAC1D,gBAAgB,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;AACpD,aAAa,CAAC,CAAC;AACf,YAAY,IAAI,MAAM,EAAE;AACxB,gBAAgB,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAC/D,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,MAAM,EAAE;AACrD,QAAQ,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACxE,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE;AACtD,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;AACtC,YAAY,IAAI,KAAK,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC/C,YAAY,IAAI,CAAC,KAAK;AACtB,gBAAgB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC5C,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,OAAO,CAAC,CAAC;AACjB,KAAK,CAAC;AAGN,IAAI,WAAW,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,GAAG,EAAE;AACxD,QAAQ,IAAI,GAAG,KAAK,KAAK,CAAC,EAAE,EAAE,GAAG,GAAG,IAAI,GAAG,EAAE,CAAC,EAAE;AAChD,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACxD,QAAQ,IAAI,IAAI,YAAY,KAAK,EAAE;AACnC,YAAY,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC1C,SAAS;AACT,aAAa;AAIb,YAAY,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/E,SAAS;AACT,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,CAAC;AAKN,IAAI,WAAW,CAAC,SAAS,CAAC,EAAE,GAAG,YAAY;AAC3C,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AACtC,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;AACvC,QAAQ,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;AAClC,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,EAAE;AAI3C,gBAAgB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAG7E,gBAAgB,OAAO,QAAQ,CAAC,EAAE,CAAC,CAAC;AACpC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChD,QAAQ,IAAI,WAAW,CAAC,MAAM,EAAE;AAChC,YAAY,IAAI,MAAM,GAAG,IAAI,CAAC;AAC9B,YAAY,OAAO,MAAM,YAAY,KAAK;AAC1C,gBAAgB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AACvC,YAAY,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7E,SAAS;AACT,QAAQ,OAAO,WAAW,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,MAAM,EAAE;AAC9D,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;AAC7C,YAAY,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACpE,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACzC,YAAY,IAAI,CAAC,IAAI;AACrB,gBAAgB,OAAO,OAAO,CAAC;AAC/B,YAAY,IAAI,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAG5C,YAAY,SAAS,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AAC7C,gBAAgB,IAAIqB,qBAAW,CAAC,GAAG,CAAC,EAAE;AACtC,oBAAoB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAQ9C,iBAAiB;AACjB,gBAAgB,IAAIC,yBAAe,CAAC,GAAG,CAAC,EAAE;AAC1C,oBAAoB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AAC5D,wBAAwB,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;AAG7C,wBAAwB,IAAIA,yBAAe,CAAC,KAAK,CAAC,EAAE;AACpD,4BAA4B,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjD,yBAAyB;AACzB,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjC,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY;AACrD,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAC1D,KAAK,CAAC;AACN,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC,EAAE,EAAE;AAeL,IAAI,UAAU,KAAkB,YAAY;AAC5C,IAAI,SAAS,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE;AACzC,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC,EAAE;AACjD,QAAQ,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAC7B,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;AACtB,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5B,KAAK;AACL,IAAI,UAAU,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY;AACpD,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,OAAO,GAAGM,YAAG,EAAE,GAAG,IAAI,CAAC;AAC7C,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAIC,SAAI,CAACC,uBAAa,CAAC,CAAC;AAChD,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,MAAM,EAAE,cAAc,EAAE;AACpE,QAAQ,IAAI,IAAI,CAAC,CAAC,EAAE;AACpB,YAAY,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC;AACvD,YAAY,IAAI,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;AACnE,YAAY,IAAI,SAAS,KAAK,cAAc,EAAE;AAM9C,gBAAgB,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;AACtD,aAAa;AACb,YAAY,IAAI,IAAI,CAAC,MAAM,EAAE;AAC7B,gBAAgB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAC3D,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,MAAM,EAAE,cAAc,EAAE;AACnE,QAAQ,IAAI,IAAI,CAAC,CAAC,EAAE;AACpB,YAAY,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,cAAc,CAAC;AAQ3D,YAAY,cAAc,KAAK,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC,CAAC;AACnE,SAAS;AACT,KAAK,CAAC;AACN,IAAI,OAAO,UAAU,CAAC;AACtB,CAAC,EAAE,CAAC,CAAC;AACL,SAAS,UAAU,CAAC,MAAM,EAAE,cAAc,EAAE;AAI5C,IAAI,OAAO,cAAc,GAAG,GAAG,GAAG,MAAM,CAAC;AACzC,CAAC;AACM,SAAS,8BAA8B,CAAC,KAAK,EAAE,QAAQ,EAAE;AAChE,IAAI,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE;AAStC,QAAQ,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AACjD,KAAK;AACL,CAAC;AACD,CAAC,UAAU,WAAW,EAAE;AAExB,IAAI,IAAI,IAAI,KAAkB,UAAU,MAAM,EAAE;AAChD,QAAQX,eAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAChC,QAAQ,SAAS,IAAI,CAAC,EAAE,EAAE;AAC1B,YAAY,IAAI,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,CAAC,aAAa,EAAE,aAAa,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACzH,YAAY,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,UAAU,CAAC,aAAa,CAAC,CAAC,IAAI,IAAI,CAAC;AAC3F,YAAY,KAAK,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;AAC3C,YAAY,KAAK,CAAC,WAAW,GAAG,IAAIU,SAAI,CAACC,uBAAa,CAAC,CAAC;AACxD,YAAY,IAAI,IAAI;AACpB,gBAAgB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpC,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,OAAO,EAAE,MAAM,EAAE;AAI7D,YAAY,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AACxD,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,YAAY;AAEjD,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;AAChD,YAAY,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAC3D,SAAS,CAAC;AACV,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;AACpB,IAAI,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;AAC5B,CAAC,EAAEL,mBAAW,KAAKA,mBAAW,GAAG,EAAE,CAAC,CAAC,CAAC;AAGtC,IAAI,KAAK,KAAkB,UAAU,MAAM,EAAE;AAC7C,IAAIN,eAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC7B,IAAI,SAAS,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE;AAC9C,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC;AACtE,QAAQ,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC;AACtB,QAAQ,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAC9B,QAAQ,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAC9B,QAAQ,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AAC5B,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC;AACtB,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,OAAO,EAAE,MAAM,EAAE;AAC1D,QAAQ,OAAO,IAAI,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;AAC5D,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,OAAO,EAAE;AACrD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AAEzB,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACtD,QAAQ,IAAI,OAAO,KAAK,IAAI,CAAC,EAAE,EAAE;AACjC,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AAKpC,gBAAgB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;AACjE,oBAAoB,IAAI,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC5D,oBAAoB,IAAI,iBAAiB,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;AACrE,oBAAoB,IAAI,CAAC,iBAAiB,EAAE;AAM5C,wBAAwB,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7C,qBAAqB;AACrB,yBAAyB,IAAI,CAAC,cAAc,EAAE;AAK9C,wBAAwB,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAC9D,wBAAwB,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,UAAU,cAAc,EAAE;AACzF,4BAA4B,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AACtE,yBAAyB,CAAC,CAAC;AAC3B,qBAAqB;AACrB,yBAAyB,IAAI,cAAc,KAAK,iBAAiB,EAAE;AAInE,wBAAwB,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,UAAU,cAAc,EAAE;AACtF,4BAA4B,IAAI,CAAClB,WAAK,CAAC,cAAc,CAAC,cAAc,CAAC,EAAE,iBAAiB,CAAC,cAAc,CAAC,CAAC,EAAE;AAC3G,gCAAgC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAC1E,6BAA6B;AAC7B,yBAAyB,CAAC,CAAC;AAC3B,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,YAAY,OAAO,MAAM,CAAC;AAC1B,SAAS;AAET,QAAQ,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM;AAClC,YAAY,OAAO,IAAI,CAAC;AAExB,QAAQ,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACrD,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY;AAC3C,QAAQ,OAAOH,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACzE,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,MAAM,EAAE;AACxD,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAC7D,QAAQ,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC;AACrJ,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;AAC7C,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAC5B,QAAQ,OAAO,CAAC,CAAC,MAAM;AACvB,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC;AACzB,QAAQ,OAAO,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AAEnC,QAAQ,SAAS,CAAC,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC2B,mBAAW,CAAC,CAAC,CAAC;AAKhB,IAAI,KAAK,KAAkB,UAAU,MAAM,EAAE;AAC7C,IAAIN,eAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC7B,IAAI,SAAS,KAAK,CAAC,IAAI,EAAE;AACzB,QAAQ,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC;AACrI,KAAK;AACL,IAAI,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG,YAAY;AAE9C,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,KAAK,EAAE,KAAK,EAAE;AAMpD,QAAQ,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC/C,KAAK,CAAC;AACN,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AACV,SAAS,qBAAqB,CAAC,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE;AACzE,IAAI,IAAI,aAAa,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;AACjD,IAAI,IAAI,aAAa,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC;AAMjD,IAAI,OAAOlB,WAAK,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG,aAAa,GAAG,aAAa,CAAC;AAC/E,CAAC;AACM,SAAS,qBAAqB,CAAC,KAAK,EAAE;AAE7C,IAAI,OAAO,CAAC,EAAE,KAAK,YAAYwB,mBAAW,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACnE;;ACzpBA,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,IAAI,IAAIM,yBAAe,CAAC,KAAK,CAAC,EAAE;AAChC,QAAQ,OAAOR,iBAAO,CAAC,KAAK,CAAC;AAC7B,YAAY,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1B,cAAczB,cAAQ,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;AAC3E,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AAwDD,IAAI,WAAW,KAAkB,YAAY;AAC7C,IAAI,SAAS,WAAW,GAAG;AAG3B,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAKkC,uBAAa,GAAG,OAAO,GAAG,GAAG,GAAG,CAAC;AAE3D,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAIH,SAAI,CAACC,uBAAa,CAAC,CAAC;AAG5C,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;AAGpC,QAAQ,IAAI,CAAC,UAAU,GAAG,IAAI,GAAG,EAAE,CAAC;AAEpC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACpC,KAAK;AACL,IAAI,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,KAAK,EAAE;AACrD,QAAQ,OAAOC,yBAAe,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC/D,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,KAAK,EAAE;AAClD,QAAQ,IAAIA,yBAAe,CAAC,KAAK,CAAC,EAAE;AACpC,YAAY,IAAI,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AAC1C,YAAY,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACzC,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,KAAK,EAAE;AACnD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAIA,yBAAe,CAAC,KAAK,CAAC,EAAE;AACpC,YAAY,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAClD,YAAY,IAAI,QAAQ;AACxB,gBAAgB,OAAO,QAAQ,CAAC;AAChC,YAAY,IAAI,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACrD,YAAY,QAAQ,KAAK;AACzB,gBAAgB,KAAK,KAAK,CAAC,SAAS,EAAE;AACtC,oBAAoB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;AAC7C,wBAAwB,OAAO,KAAK,CAAC;AACrC,oBAAoB,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAI5D,oBAAoB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAC5D,oBAAoB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrC,wBAAwB,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,EAAE,CAAC;AAI7D,wBAAwB,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAC1D,4BAA4B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjD,yBAAyB;AACzB,qBAAqB;AACrB,oBAAoB,OAAO,IAAI,CAAC,KAAK,CAAC;AACtC,iBAAiB;AACjB,gBAAgB,KAAK,IAAI,CAAC;AAC1B,gBAAgB,KAAK,MAAM,CAAC,SAAS,EAAE;AACvC,oBAAoB,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;AAC7C,wBAAwB,OAAO,KAAK,CAAC;AACrC,oBAAoB,IAAI,OAAO,GAAG,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC/D,oBAAoB,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;AAC5C,oBAAoB,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;AACtD,oBAAoB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5C,oBAAoB,IAAI,iBAAiB,GAAG,OAAO,CAAC,MAAM,CAAC;AAC3D,oBAAoB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AACvD,wBAAwB,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9D,qBAAqB,CAAC,CAAC;AASvB,oBAAoB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC9D,oBAAoB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACtC,wBAAwB,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;AAC3E,wBAAwB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9C,wBAAwB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE,CAAC,EAAE;AAC9D,4BAA4B,KAAK,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;AACxE,yBAAyB,CAAC,CAAC;AAI3B,wBAAwB,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAC1D,4BAA4B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjD,yBAAyB;AACzB,qBAAqB;AACrB,oBAAoB,OAAO,IAAI,CAAC,MAAM,CAAC;AACvC,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AAKN,IAAI,WAAW,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,GAAG,EAAE;AACtD,QAAQ,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpC,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC/C,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACxB,YAAY,IAAI,CAAC,IAAI,EAAE,CAAC;AACxB,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAC5C,YAAY,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE;AAC1D,gBAAgB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC;AACtF,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC;AACzB,KAAK,CAAC;AACN,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC,EAAE,CAAC;;ACzKJ,SAAS,uBAAuB,CAAC,OAAO,EAAE;AAC1C,IAAI,OAAO;AACX,QAAQ,OAAO,CAAC,YAAY;AAC5B,QAAQ,OAAO,CAAC,iBAAiB;AACjC,QAAQ,OAAO,CAAC,OAAO;AAGvB,QAAQ,OAAO,CAAC,OAAO,CAAC,eAAe;AACvC,KAAK,CAAC;AACN,CAAC;AACD,IAAI,WAAW,KAAkB,YAAY;AAC7C,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE;AACjC,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,YAAY,GAAG,KAAKD,uBAAa,GAAG,OAAO,GAAG,GAAG,GAAG,CAAC;AAClE,QAAQ,IAAI,CAAC,MAAM,GAAGV,iBAAO,CAAC,MAAM,EAAE;AACtC,YAAY,WAAW,EAAE,MAAM,CAAC,WAAW,KAAK,KAAK;AACrD,YAAY,eAAe,EAAE,qBAAqB,CAAC,MAAM,CAAC;AAC1D,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI,WAAW,EAAE,CAAC;AAKvD,QAAQ,IAAI,CAAC,mBAAmB,GAAGX,aAAI,CAAC,UAAU,OAAO,EAAE;AAC3D,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,IAAI,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC;AAClE,YAAY,IAAI,QAAQ,GAAG,uBAAuB,CAAC,OAAO,CAAC,CAAC;AAG5D,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC;AAC3C,YAAY,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AAClF,YAAY,IAAI,KAAK,EAAE;AACvB,gBAAgB,IAAI,eAAe,EAAE;AACrC,oBAAoB,OAAOX,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE;AAGzD,wBAAwB,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AACnE,iBAAiB;AAGjB,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa;AACb,YAAY,8BAA8B,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAG9F,YAAY,OAAO,KAAK,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;AACvD,SAAS,EAAE;AACX,YAAY,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB;AAC/C,gBAAgBa,oBAAU,CAAC,mCAAmC,CAAC;AAC/D,gBAAgB,KAAK;AACrB,YAAY,OAAO,EAAE,uBAAuB;AAG5C,YAAY,YAAY,EAAE,UAAU,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE;AACpF,gBAAgB,IAAI,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC1D,oBAAoB,OAAO,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,YAAY,EAAEU,qBAAW,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,EAAE,OAAO,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;AACrJ,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,uBAAuB,GAAGZ,aAAI,CAAC,UAAU,OAAO,EAAE;AAC/D,YAAY,8BAA8B,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC9F,YAAY,OAAO,KAAK,CAAC,wBAAwB,CAAC,OAAO,CAAC,CAAC;AAC3D,SAAS,EAAE;AACX,YAAY,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB;AAC/C,gBAAgBE,oBAAU,CAAC,uCAAuC,CAAC;AACnE,gBAAgB,KAAK;AACrB,YAAY,YAAY,EAAE,UAAU,EAAE,EAAE;AACxC,gBAAgB,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AAC7E,gBAAgB,IAAI,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AAC1D,oBAAoB,OAAO,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AACvF,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,WAAW,CAAC,SAAS,CAAC,UAAU,GAAG,YAAY;AACnD,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;AACvC,KAAK,CAAC;AAKN,IAAI,WAAW,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,EAAE,EAAE;AAChE,QAAQ,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,YAAY,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,eAAe,EAAE,eAAe,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,GAAG,EAAE,CAAC;AAC5S,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;AAClD,QAAQ,SAAS,GAAGb,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAEmC,0BAAgB,CAACC,4BAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AACnG,QAAQ,IAAI,OAAO,GAAGP,uBAAa,CAAC,MAAM,CAAC,CAAC;AAC5C,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAClD,YAAY,YAAY,EAAEzB,2BAAiB,CAAC,KAAK,CAAC,CAAC,YAAY;AAC/D,YAAY,iBAAiB,EAAE,OAAO;AACtC,YAAY,YAAY,EAAE,OAAO;AACjC,YAAY,OAAO,EAAEJ,cAAQ,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAEqC,4BAAkB,CAAC,SAAS,CAAC,EAAE,eAAe,EAAE,eAAe,EAAE,EAAE,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACzO,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,OAAO,CAAC;AACpB,QAAQ,IAAI,UAAU,CAAC,OAAO,EAAE;AAKhC,YAAY,OAAO,GAAG;AACtB,gBAAgB,IAAI,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC;AAC7G,aAAa,CAAC;AACd,YAAY,IAAI,CAAC,iBAAiB,EAAE;AACpC,gBAAgB,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC;AACjC,aAAa;AACb,SAAS;AACT,QAAQ,OAAO;AACf,YAAY,MAAM,EAAE,UAAU,CAAC,MAAM;AACrC,YAAY,QAAQ,EAAE,CAAC,OAAO;AAC9B,YAAY,OAAO,EAAE,OAAO;AAC5B,SAAS,CAAC;AACV,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE;AACrF,QAAQ,IAAI,qBAAqB,CAAC,OAAO,CAAC,KAAK,CAAC;AAChD,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,YAAY,EAAE;AAC5D,YAAY,IAAI,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,OAAO;AAIpF,YAAY,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACxC,YAAY,IAAI,MAAM,IAAI,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;AACpD,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AAEN,IAAI,WAAW,CAAC,SAAS,CAAC,oBAAoB,GAAG,UAAU,EAAE,EAAE;AAC/D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,iBAAiB,GAAG,EAAE,CAAC,iBAAiB,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AAC3I,QAAQ,IAAId,qBAAW,CAAC,iBAAiB,CAAC;AAC1C,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,KAAK,CAAC;AACxE,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;AACzD,YAAY,OAAO;AACnB,gBAAgB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK;AACxC,gBAAgB,OAAO,EAAE,gCAAgC,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC;AACpG,aAAa,CAAC;AACd,SAAS;AACT,QAAQ,IAAI,SAAS,GAAG,OAAO,CAAC,SAAS,EAAE,QAAQ,GAAG,OAAO,CAAC,QAAQ,EAAE,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;AAC9F,QAAQ,IAAI,QAAQ,GAAG,KAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;AAC5E,QAAQ,IAAI,cAAc,GAAG,EAAE,CAAC;AAChC,QAAQ,IAAI,OAAO,CAAC;AACpB,QAAQ,IAAI,aAAa,GAAG,IAAIG,oBAAU,EAAE,CAAC;AAC7C,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW;AACnC,YAAY,OAAO,QAAQ,KAAK,QAAQ;AACxC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE;AAInD,YAAY,cAAc,CAAC,IAAI,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC1D,SAAS;AACT,QAAQ,SAAS,aAAa,CAAC,MAAM,EAAE,UAAU,EAAE;AACnD,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,IAAI,MAAM,CAAC,OAAO,EAAE;AAChC,gBAAgB,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG,EAAE;AAC/D,oBAAoB,EAAE,CAAC,UAAU,CAAC,GAAG,MAAM,CAAC,OAAO;AACnD,oBAAoB,EAAE,EAAE,CAAC;AACzB,aAAa;AACb,YAAY,OAAO,MAAM,CAAC,MAAM,CAAC;AACjC,SAAS;AACT,QAAQ,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;AACvD,QAAQ,OAAO,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAC7C,YAAY,IAAI,EAAE,EAAE,EAAE,CAAC;AAGvB,YAAY,IAAI,CAACnB,uBAAa,CAAC,SAAS,EAAE,SAAS,CAAC;AACpD,gBAAgB,OAAO;AACvB,YAAY,IAAIC,iBAAO,CAAC,SAAS,CAAC,EAAE;AACpC,gBAAgB,IAAI,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC;AACpD,oBAAoB,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK;AACnD,oBAAoB,KAAK,EAAE,SAAS;AACpC,oBAAoB,SAAS,EAAE,OAAO,CAAC,SAAS;AAChD,oBAAoB,IAAI,EAAE,iBAAiB;AAC3C,iBAAiB,EAAE,OAAO,CAAC,CAAC;AAC5B,gBAAgB,IAAI,UAAU,GAAGC,gCAAsB,CAAC,SAAS,CAAC,CAAC;AACnE,gBAAgB,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE;AAC3C,oBAAoB,IAAI,CAAC6B,+BAAqB,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;AACjE,wBAAwB,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG,EAAE;AACvE,4BAA4B,EAAE,CAAC,UAAU,CAAC,GAAG,oBAAoB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,MAAM,CAACf,qBAAW,CAAC,iBAAiB,CAAC;AAC7I,gCAAgC,iBAAiB,CAAC,KAAK,GAAG,SAAS;AACnE,kCAAkC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACzF,4BAA4B,EAAE,EAAE,CAAC;AACjC,qBAAqB;AACrB,iBAAiB;AACjB,qBAAqB,IAAIE,iBAAO,CAAC,UAAU,CAAC,EAAE;AAC9C,oBAAoB,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/C,wBAAwB,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,uBAAuB,CAAC;AACjF,4BAA4B,KAAK,EAAE,SAAS;AAC5C,4BAA4B,KAAK,EAAE,UAAU;AAC7C,4BAA4B,YAAY,EAAE,YAAY;AACtD,4BAA4B,OAAO,EAAE,OAAO;AAC5C,yBAAyB,CAAC,EAAE,UAAU,CAAC,CAAC;AACxC,qBAAqB;AACrB,iBAAiB;AACjB,qBAAqB,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;AAKlD,oBAAoB,IAAI,OAAO,CAAC,eAAe,EAAE;AACjD,wBAAwB,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAClE,qBAAqB;AACrB,iBAAiB;AACjB,qBAAqB,IAAI,UAAU,IAAI,IAAI,EAAE;AAI7C,oBAAoB,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,mBAAmB,CAAC;AACzE,wBAAwB,YAAY,EAAE,SAAS,CAAC,YAAY;AAC5D,wBAAwB,iBAAiB,EAAE,UAAU;AACrD,wBAAwB,YAAY,EAAEF,qBAAW,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,YAAY;AACzF,wBAAwB,OAAO,EAAE,OAAO;AACxC,qBAAqB,CAAC,EAAE,UAAU,CAAC,CAAC;AACpC,iBAAiB;AACjB,gBAAgB,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE;AAC3C,oBAAoB,cAAc,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,UAAU,EAAE,EAAE,EAAE,CAAC;AACpF,iBAAiB;AACjB,aAAa;AACb,iBAAiB;AACjB,gBAAgB,IAAI,QAAQ,GAAGb,kCAAwB,CAAC,SAAS,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;AAC3F,gBAAgB,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAK6B,YAAI,CAAC,eAAe,EAAE;AAC1E,oBAAoB,MAAMC,yBAAiB,CAAC,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACtE,iBAAiB;AACjB,gBAAgB,IAAI,QAAQ,IAAI,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;AAC9E,oBAAoB,QAAQ,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACnF,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,MAAM,GAAGrB,wBAAc,CAAC,cAAc,CAAC,CAAC;AACpD,QAAQ,IAAI,WAAW,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;AAC/D,QAAQ,IAAI,MAAM,GAAG,OAAO,CAAC,eAAe;AAC5C,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;AAGzC,cAAcS,yBAAe,CAAC,WAAW,CAAC,CAAC;AAG3C,QAAQ,IAAI,MAAM,CAAC,MAAM,EAAE;AAC3B,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AAC/D,SAAS;AACT,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,CAAC;AAEN,IAAI,WAAW,CAAC,SAAS,CAAC,wBAAwB,GAAG,UAAU,EAAE,EAAE;AACnE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AACrG,QAAQ,IAAI,OAAO,CAAC;AACpB,QAAQ,IAAI,aAAa,GAAG,IAAIF,oBAAU,EAAE,CAAC;AAC7C,QAAQ,SAAS,aAAa,CAAC,WAAW,EAAE,CAAC,EAAE;AAC/C,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,IAAI,WAAW,CAAC,OAAO,EAAE;AACrC,gBAAgB,OAAO,GAAG,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,OAAO,EAAE,EAAE,EAAE,CAAC;AACnG,aAAa;AACb,YAAY,OAAO,WAAW,CAAC,MAAM,CAAC;AACtC,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,YAAY,EAAE;AAChC,YAAY,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACxD,SAAS;AACT,QAAQ,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE,CAAC,EAAE;AAE7C,YAAY,IAAI,IAAI,KAAK,IAAI,EAAE;AAC/B,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AAEb,YAAY,IAAID,iBAAO,CAAC,IAAI,CAAC,EAAE;AAC/B,gBAAgB,OAAO,aAAa,CAAC,KAAK,CAAC,uBAAuB,CAAC;AACnE,oBAAoB,KAAK,EAAE,KAAK;AAChC,oBAAoB,KAAK,EAAE,IAAI;AAC/B,oBAAoB,YAAY,EAAE,YAAY;AAC9C,oBAAoB,OAAO,EAAE,OAAO;AACpC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;AACvB,aAAa;AAEb,YAAY,IAAI,KAAK,CAAC,YAAY,EAAE;AACpC,gBAAgB,OAAO,aAAa,CAAC,KAAK,CAAC,mBAAmB,CAAC;AAC/D,oBAAoB,YAAY,EAAE,KAAK,CAAC,YAAY;AACpD,oBAAoB,iBAAiB,EAAE,IAAI;AAC3C,oBAAoB,YAAY,EAAEF,qBAAW,CAAC,IAAI,CAAC,GAAG,IAAI,GAAG,YAAY;AACzE,oBAAoB,OAAO,EAAE,OAAO;AACpC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAC;AACvB,aAAa;AACb,YAAY,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAC9C,gBAAgB,4BAA4B,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACzE,aAAa;AACb,YAAY,OAAO,IAAI,CAAC;AACxB,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO;AACf,YAAY,MAAM,EAAE,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK;AAC7E,YAAY,OAAO,EAAE,OAAO;AAC5B,SAAS,CAAC;AACV,KAAK,CAAC;AACN,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC,EAAE,CAAC,CAAC;AAEL,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,IAAI,IAAI;AACR,QAAQ,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,CAAC,EAAE,KAAK,EAAE;AACjD,YAAY,IAAI,OAAO,KAAK,KAAK,QAAQ;AACzC,gBAAgB,MAAM,KAAK,CAAC;AAC5B,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,MAAM,EAAE;AACnB,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK;AACL,CAAC;AACD,SAAS,4BAA4B,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE;AAChE,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;AAC7B,QAAQ,IAAI,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC;AAC9C,QAAQ,SAAS,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;AAC3C,YAAY,IAAIC,yBAAe,CAAC,KAAK,CAAC,EAAE;AACxC,gBAAgBR,iBAAS;AACzB,oBAAoB,CAACO,qBAAW,CAAC,KAAK,CAAC;AACvC,oBAAoB,EAAE;AACtB,oBAAoB,0BAA0B,CAAC,KAAK,EAAE,KAAK,CAAC;AAC5D,oBAAoB,KAAK,CAAC,IAAI,CAAC,KAAK;AACpC,iBAAiB,CAAC;AAClB,gBAAgB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;AACvE,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL;;ACtUU,IAAC,SAAS,GAAG,IAAIkB,aAAI,GAAG;AAClC,IAAI,YAAY,GAAG,IAAI,OAAO,EAAE,CAAC;AACjC,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,IAAI,IAAI,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,QAAQ,YAAY,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,GAAG;AACxC,YAAY,IAAI,EAAE,IAAI,GAAG,EAAE;AAC3B,YAAY,GAAG,EAAEX,YAAG,EAAE;AACtB,SAAS,EAAE,CAAC;AACZ,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACM,SAAS,WAAW,CAAC,KAAK,EAAE;AACnC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACtF,CAAC;AASM,SAAS,WAAW,CAAC,KAAK,EAAE;AACnC,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACtF,CAAC;AACM,SAAS,OAAO,CAAC,KAAK,EAAE;AAC/B,IAAI,IAAI,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;AAC3B,IAAI,IAAI,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;AAC9B,IAAI,IAAI,EAAE,GAAG,UAAU,QAAQ,EAAE;AACjC,QAAQ,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,YAAY,IAAI,KAAK,KAAK,QAAQ,EAAE;AACpC,gBAAgB,KAAK,GAAG,QAAQ,CAAC;AACjC,gBAAgB,MAAM,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;AAIhD,oBAAoB,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AAGtD,oBAAoB,SAAS,CAAC,KAAK,CAAC,CAAC;AACrC,iBAAiB,CAAC,CAAC;AAEnB,gBAAgB,IAAI,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACzD,gBAAgB,SAAS,CAAC,KAAK,EAAE,CAAC;AAClC,gBAAgB,YAAY,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACtF,aAAa;AACb,SAAS;AACT,aAAa;AAIb,YAAY,IAAI,KAAK,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;AAC7C,YAAY,IAAI,KAAK,EAAE;AACvB,gBAAgB,MAAM,CAAC,KAAK,CAAC,CAAC;AAC9B,gBAAgB,YAAY,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAC5C,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,EAAE,CAAC,YAAY,GAAG,UAAU,QAAQ,EAAE;AAC1C,QAAQ,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAChC,QAAQ,OAAO,YAAY;AAC3B,YAAY,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACvC,SAAS,CAAC;AACV,KAAK,CAAC;AACN,IAAI,IAAI,MAAM,IAAI,EAAE,CAAC,WAAW,GAAG,UAAU,KAAK,EAAE;AACpD,QAAQ,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,QAAQ,YAAY,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AACzC,QAAQ,OAAO,EAAE,CAAC;AAClB,KAAK,CAAC,CAAC;AACP,IAAI,EAAE,CAAC,WAAW,GAAG,UAAU,KAAK,EAAE,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;AACvE,IAAI,OAAO,EAAE,CAAC;AACd,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,IAAI,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAChC,QAAQ,KAAK,CAAC,gBAAgB,EAAE,CAAC;AACjC,KAAK;AACL;;AC7EA,IAAI,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7C,SAAS,mBAAmB,CAAC,IAAI,EAAE;AAInC,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACxC,IAAI,QAAQ,kBAAkB,CAAC,QAAQ,CAAC;AACxC,SAAS,kBAAkB,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AAC9D,CAAC;AACM,SAAS,wBAAwB,CAAC,SAAS,EAAE;AACpD,IAAI,IAAI,IAAI,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;AAC9C,IAAI,QAAQ,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,GAAG,UAAU,MAAM,EAAE,OAAO,EAAE;AAC/E,YAAY,IAAI,OAAO,GAAG,UAAU,IAAI,EAAE,GAAG,EAAE;AAC/C,gBAAgB,OAAO,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACpD,aAAa,CAAC;AACd,YAAY,IAAI,SAAS,IAAI,OAAO,CAAC,SAAS,GAAG,qBAAqB,CAAC,SAAS,EAAE,UAAU,aAAa,EAAE;AAC3G,gBAAgB,IAAI,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,WAAW,EAAE,aAAa;AAIjF,gBAAgB,OAAO,CAAC,CAAC;AACzB,gBAAgB,IAAI,SAAS,KAAK,KAAK,CAAC;AACxC,oBAAoB,MAAM,KAAK,OAAO,CAAC,WAAW;AAClD,oBAAoB,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;AAU3D,oBAAoB,SAAS,GAAG,cAAc,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;AAClF,iBAAiB;AACjB,gBAAgBd,iBAAS,CAAC,SAAS,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;AACpF,gBAAgB,OAAO,SAAS,CAAC;AACjC,aAAa,CAAC,CAAC,CAAC;AAChB,YAAY,OAAO,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;AACtF,SAAS,CAAC,EAAE;AACZ,CAAC;AAQM,SAAS,sBAAsB,CAAC,SAAS,EAAE;AAClD,IAAI,IAAI,IAAI,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC;AAC9C,IAAI,QAAQ,IAAI,CAAC,SAAS;AAC1B,SAAS,IAAI,CAAC,SAAS,GAAG,UAAU,IAAI,EAAE,EAAE,EAAE;AAC9C,YAAY,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AACrF,YAAY,IAAI,SAAS,GAAG,qBAAqB,CAAC,SAAS,EAAE,UAAU,OAAO,EAAE;AAChF,gBAAgB,IAAI,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC1C,gBAAgB,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACnD,gBAAgB,IAAI,SAAS,KAAK,GAAG,EAAE;AACvC,oBAAoB,IAAI,KAAK,IAAI0B,yBAAe,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACpE,wBAAwB,IAAI,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAIhE,wBAAwB,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,eAAe,CAAC,EAAE,CAAC,CAAC;AAEjH,wBAAwB,IAAI,aAAa,GAAG,CAAC,IAAIC,kCAAwB,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAQxF,wBAAwB,QAAQ,aAAa;AAC7C,4BAA4B,cAAc,CAAC,aAAa;AAIxD,4BAA4B,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AAC/C,qBAAqB;AAIrB,oBAAoB,OAAO;AAC3B,iBAAiB;AACjB,gBAAgB,IAAI,SAAS,KAAK,GAAG,EAAE;AACvC,oBAAoB,IAAI,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzD,oBAAoB,IAAI,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE;AAC3E,wBAAwB,IAAI,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1D,wBAAwB,UAAU,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC;AACrD,wBAAwB,OAAO,cAAc,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AACrE,qBAAqB;AAIrB,oBAAoB,OAAO;AAC3B,iBAAiB;AACjB,gBAAgB,IAAI,IAAI,EAAE;AAC1B,oBAAoB,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACzD,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,YAAY,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AAMnD,YAAY,IAAI,IAAI,IAAI,MAAM,KAAK,IAAI,EAAE;AACzC,gBAAgB,SAAS,IAAI,GAAG,GAAG,MAAM,CAAC;AAC1C,aAAa;AACb,YAAY,OAAO,SAAS,CAAC;AAC7B,SAAS,CAAC,EAAE;AACZ,CAAC;AACM,SAAS,qBAAqB,CAAC,SAAS,EAAE,SAAS,EAAE;AAG5D,IAAI,IAAI,MAAM,GAAG,IAAIjB,oBAAU,EAAE,CAAC;AAClC,IAAI,OAAO,iBAAiB,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,UAAU,SAAS,EAAE,IAAI,EAAE;AAC1E,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AACtC,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE;AAGhC,YAAY,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,EAAE;AACvD,gBAAgB,OAAO,IAAI,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,OAAO,EAAE,EAAE,CAAC,CAAC;AAC/D,aAAa;AACb,YAAY,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AACzD,SAAS;AACT,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5B,CAAC;AACM,SAAS,iBAAiB,CAAC,IAAI,EAAE;AACxC,IAAI,IAAI,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrB,QAAQ,IAAI,OAAO,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC;AACxC,QAAQ,IAAI,aAAa,GAAG,EAAE,CAAC;AAC/B,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;AACrC,YAAY,IAAID,iBAAO,CAAC,CAAC,CAAC,EAAE;AAC5B,gBAAgB,iBAAiB,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7G,gBAAgB,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;AACzC,aAAa;AACb,iBAAiB;AACjB,gBAAgB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACtC,gBAAgB,IAAI,CAACA,iBAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;AAC3C,oBAAoB,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,oBAAoB,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;AAC7C,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC;AACtB,CAAC;AACD,SAAS,UAAU,CAAC,MAAM,EAAE,GAAG,EAAE;AACjC,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;AACvB,CAAC;AACM,SAAS,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE;AAYtD,IAAI,OAAO,GAAG,OAAO,IAAI,UAAU,CAAC;AACpC,IAAI,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,OAAO,CAAC,GAAG,EAAE,GAAG,EAAE;AAC5D,QAAQ,OAAOA,iBAAO,CAAC,GAAG,CAAC;AAC3B,YAAY,GAAG,CAAC,GAAG,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;AACrE,cAAc,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACvC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAChB,CAAC;AACD,SAAS,SAAS,CAAC,KAAK,EAAE;AAI1B,IAAI,IAAID,yBAAe,CAAC,KAAK,CAAC,EAAE;AAChC,QAAQ,IAAIC,iBAAO,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAY,OAAO,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACxC,SAAS;AACT,QAAQ,OAAO,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,EAAE,UAAU,IAAI,EAAE;AAChF,YAAY,OAAO,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC/C,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB;;ACvLA,SAAS,sBAAsB,CAAC,IAAI,EAAE;AACtC,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI;AAC5C,UAAU,IAAI,CAAC,KAAK,GAAGkB,kCAAwB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC;AAC3E,cAAc,IAAI,EAAE;AACpB,CAAC;AACD,IAAI,eAAe,GAAG,YAAY,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC;AACrD,IAAI,eAAe,GAAG,UAAU,KAAK,EAAE,OAAO,EAAE,EAAE,OAAO,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;AAG9E,IAAI,WAAW,GAAG,UAAU,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE;AACpD,IAAI,IAAI,YAAY,GAAG,EAAE,CAAC,YAAY,CAAC;AACvC,IAAI,OAAO,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC5C,CAAC,CAAC;AACF,IAAI,YAAY,GAAG,UAAU,CAAC,EAAE,QAAQ,EAAE,EAAE,OAAO,QAAQ,CAAC,EAAE,CAAC;AAC5D,IAAC,QAAQ,KAAkB,YAAY;AAC1C,IAAI,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC9B,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAC7B,QAAQ,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChD,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAK7C,QAAQ,IAAI,CAAC,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;AAKtC,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,EAAE,CAAC;AACvC,QAAQ,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,QAAQ,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrD,QAAQ,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACxC,QAAQ,IAAI,CAAC,MAAM,GAAG3C,cAAQ,CAAC,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,EAAE,MAAM,CAAC,CAAC;AACtF,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;AACvC,QAAQ,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACtC,QAAQ,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AACzC,QAAQ,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;AAC7C,QAAQ,IAAI,MAAM,CAAC,aAAa,EAAE;AAClC,YAAY,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACxD,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,YAAY,EAAE;AACjC,YAAY,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACtD,SAAS;AACT,KAAK;AACL,IAAI,QAAQ,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,MAAM,EAAE,cAAc,EAAE;AACpE,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC;AAC5B,QAAQ,IAAI,QAAQ,GAAG,CAAC,cAAc;AACtC,aAAa,cAAc,CAAC,QAAQ,KAAK,CAAC,EAAE,GAAG,cAAc,CAAC,WAAW,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,CAAC;AAC/H,YAAY,MAAM,CAAC,UAAU,CAAC;AAM9B,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;AAC5D,YAAY,OAAO,CAAC,YAAY,CAAC,CAAC;AAClC,SAAS;AAET,QAAQ,IAAI,WAAW,GAAG,CAAC,cAAc,IAAI,cAAc,CAAC,WAAW,KAAK,MAAM,CAAC;AACnF,QAAQ,IAAI,OAAO,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,CAAC,cAAc,IAAI,cAAc,CAAC,SAAS;AACnK,gBAAgB,YAAY;AAC5B,oBAAoB,IAAI,OAAO,GAAG,yBAAyB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AACpF,oBAAoB,OAAO,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE;AACvD,wBAAwB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;AACrD,wBAAwB,SAAS,EAAE,OAAO,CAAC,SAAS;AACpD,qBAAqB,CAAC,CAAC;AACvB,iBAAiB,EAAE,CAAC,CAAC;AACrB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,MAAM,GAAG,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC9D,QAAQ,IAAI,KAAK,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;AAC7E,QAAQ4C,2BAAmB,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY;AACxD,YAAY,OAAO,KAAK,EAAE;AAC1B,gBAAgB,IAAI,aAAa,GAAG,KAAK,CAAC5C,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,WAAW,CAAC,EAAE,OAAO,CAAC,CAAC;AAChG,gBAAgB,IAAIyB,iBAAO,CAAC,aAAa,CAAC,EAAE;AAC5C,oBAAoB,KAAK,GAAG,wBAAwB,CAAC,aAAa,CAAC,CAAC;AACpE,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,EAAE,GAAG,aAAa,CAAC;AACvC,oBAAoB,MAAM;AAC1B,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,EAAE,GAAG,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;AACtC,QAAQ,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;AAClE,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,YAAY,EAAE;AACjE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;AAC9D,YAAY,IAAI,EAAE,GAAG,YAAY,CAAC,QAAQ,CAAC,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,gBAAgB,GAAG,EAAE,CAAC,gBAAgB,EAAE,QAAQ,GAAGvB,YAAM,CAAC,EAAE,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC,CAAC;AAexN,YAAY,IAAI,SAAS;AACzB,gBAAgB,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;AACzD,YAAY,IAAI,YAAY;AAC5B,gBAAgB,KAAK,CAAC,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;AAC5D,YAAY,IAAI,gBAAgB;AAChC,gBAAgB,KAAK,CAAC,eAAe,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;AAChE,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE;AACxD,gBAAgB,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzD,aAAa;AACb,iBAAiB;AACjB,gBAAgB,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACvD,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,QAAQ,EAAE,QAAQ,EAAE;AACxE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACpD,QAAQ,IAAI,SAAS,GAAG,QAAQ,CAAC,SAAS,EAAE,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AACrE,QAAQ,SAAS,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE;AAC3C,YAAY,QAAQ,CAAC,KAAK;AAC1B,gBAAgB,OAAO,KAAK,KAAK,UAAU,GAAG,KAAK;AAGnD,sBAAsB,KAAK,KAAK,IAAI,GAAG,WAAW;AAGlD,0BAA0B,KAAK,KAAK,KAAK,GAAG,YAAY;AACxD,8BAA8B,QAAQ,CAAC,KAAK,CAAC;AAC7C,SAAS;AAGT,QAAQ,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC3C,QAAQ,QAAQ,CAAC,KAAK;AAEtB,YAAY,SAAS,KAAK,KAAK,GAAG,eAAe;AAGjD,kBAAkBuB,iBAAO,CAAC,SAAS,CAAC,GAAG,wBAAwB,CAAC,SAAS,CAAC;AAE1E,sBAAsB,OAAO,SAAS,KAAK,UAAU,GAAG,SAAS;AAEjE,0BAA0B,QAAQ,CAAC,KAAK,CAAC;AACzC,QAAQ,IAAI,MAAM,EAAE;AACpB,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAC7D,gBAAgB,IAAI,QAAQ,GAAG,KAAK,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;AAC/E,gBAAgB,IAAI,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AACjD,gBAAgB,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AACpD,oBAAoB,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC;AAC7C,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACjG,oBAAoB,QAAQ,CAAC,KAAK;AAGlC,wBAAwB,OAAO,KAAK,KAAK,GAAG,eAAe;AAG3D,8BAA8BA,iBAAO,CAAC,OAAO,CAAC,GAAG,sBAAsB,CAAC,OAAO,CAAC;AAEhF,kCAAkC,OAAO,OAAO,KAAK,UAAU,GAAG,OAAO;AAEzE,sCAAsC,QAAQ,CAAC,KAAK,CAAC;AACrD,oBAAoB,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;AACpD,wBAAwB,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7C,qBAAqB;AACrB,oBAAoB,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC9C,iBAAiB;AACjB,gBAAgB,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;AAMrD,oBAAoB,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,IAAI,eAAe,CAAC;AACvE,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS;AACT,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,KAAK,EAAE,QAAQ,EAAE;AACpE,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE,EAAE,QAAQ,GAAG,KAAK,CAAC,EAAE;AACtD,QAAQ,IAAI,MAAM,GAAG,OAAO,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;AACnD,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AACjD,QAAQ,IAAI,QAAQ,KAAK,GAAG,EAAE;AAC9B,YAAYT,iBAAS,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AAGvD,YAAY,IAAI,GAAG;AACnB,gBAAgB,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;AAEnD,YAAY,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;AAEtD,YAAY,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,QAAQ,CAAC;AACtD,SAAS;AACT,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,aAAa,EAAE;AACnE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACvC,QAAQ,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAIhE,YAAY,KAAK,CAAC,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACnD,YAAY,aAAa,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE;AAChE,gBAAgB,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACpE,gBAAgB,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;AACjE,gBAAgB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;AAEpD,oBAAoB,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;AAC1E,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,QAAQ,EAAE;AAC3D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,QAAQ,CAAC,EAAE;AACvD,YAAY,IAAI,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/E,YAAY,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAsBlD,YAAY,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC/D,YAAY,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;AAI1D,gBAAgB,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAMpE,gBAAgB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,KAAK,EAAE;AACpE,oBAAoB,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAI/C,wBAAwB,IAAI,eAAe,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5E,wBAAwB,IAAI,eAAe,EAAE;AAC7C,4BAA4B,eAAe,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AACzE,gCAAgC,OAAO,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACnE,6BAA6B,CAAC,CAAC;AAC/B,yBAAyB;AACzB,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,YAAY,IAAI,YAAY,IAAI,YAAY,CAAC,IAAI,EAAE;AACnD,gBAAgB,YAAY,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AAC1D,oBAAoB,IAAI,EAAE,GAAG,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,GAAGd,YAAM,CAAC,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/G,oBAAoB,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAClD,oBAAoB,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC3D,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AAC7C,QAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AAGnC,YAAY,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;AACtD,gBAAgB,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AACzD,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC3C,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,QAAQ,EAAE,SAAS,EAAE,eAAe,EAAE;AACxF,QAAQ,IAAI,QAAQ,EAAE;AACtB,YAAY,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;AACpE,YAAY,QAAQ,aAAa,CAAC,SAAS,CAAC;AAC5C,iBAAiB,eAAe,KAAK,aAAa,CAAC,SAAS,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;AACvF,SAAS;AACT,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,OAAO,EAAE,eAAe,EAAE;AAC7E,QAAQ,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;AAC1D,QAAQ,IAAI,CAAC,YAAY,IAAI,eAAe,EAAE;AAC9C,YAAY,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,GAAG,YAAY,GAAG,IAAI,GAAG,EAAE,EAAE,CAAC;AACvE,SAAS;AACT,QAAQ,OAAO,YAAY,CAAC;AAC5B,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE;AAC1F,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,QAAQ,CAAC,aAAa;AACnC,YAAY,OAAO,IAAI,CAAC;AAGxB,QAAQ,IAAI,CAAC,QAAQ;AACrB,YAAY,OAAO,KAAK,CAAC;AACzB,QAAQ,IAAI,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC;AAE1D,QAAQ,IAAI,QAAQ,KAAK,SAAS;AAClC,YAAY,OAAO,IAAI,CAAC;AACxB,QAAQ,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;AACzE,YAAY,IAAI,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC5E,YAAY,IAAI,WAAW,GAAG,CAAC,oBAAoB,CAAC,CAAC;AACrD,YAAY,IAAI,cAAc,GAAG,UAAU,OAAO,EAAE;AACpD,gBAAgB,IAAI,YAAY,GAAG,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACzE,gBAAgB,IAAI,YAAY;AAChC,oBAAoB,YAAY,CAAC,IAAI;AACrC,oBAAoB,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;AAC3D,oBAAoB,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACnD,iBAAiB;AACjB,aAAa,CAAC;AAOd,YAAY,IAAI,wBAAwB,GAAG,CAAC,EAAE,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACjF,YAAY,IAAI,qBAAqB,GAAG,KAAK,CAAC;AAG9C,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACzD,gBAAgB,IAAI,YAAY,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AAClD,gBAAgB,IAAI,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;AACjD,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;AAC9D,wBAAwB,IAAI,qBAAqB,EAAE;AACnD,4BAA4B,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIc,iBAAS,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;AACnG,yBAAyB;AAKzB,wBAAwB,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC5D,qBAAqB;AACrB,oBAAoB,OAAO,IAAI,CAAC;AAChC,iBAAiB;AACjB,gBAAgB,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACrD,gBAAgB,IAAI,wBAAwB;AAG5C,oBAAoB,CAAC,KAAK,WAAW,CAAC,MAAM,GAAG,CAAC;AAKhD,oBAAoB,yBAAyB,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE;AAIzF,oBAAoB,wBAAwB,GAAG,KAAK,CAAC;AACrD,oBAAoB,qBAAqB,GAAG,IAAI,CAAC;AAKjD,oBAAoB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE,WAAW,EAAE;AAC9E,wBAAwB,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC3D,wBAAwB,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAC5D,4BAA4B,cAAc,CAAC,WAAW,CAAC,CAAC;AACxD,yBAAyB;AACzB,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,QAAQ,EAAE,SAAS,EAAE;AACnE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AACrE,QAAQ,OAAO,CAAC,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1C,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,SAAS,EAAE;AAChE,QAAQ,IAAI,QAAQ,GAAG,SAAS,CAAC,QAAQ,EAAE,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC;AAC3E,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AACrE,QAAQ,IAAI,cAAc,CAAC;AAC3B,QAAQ,IAAI,KAAK,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;AAC3C,QAAQ,IAAI,KAAK,IAAI,QAAQ,EAAE;AAC/B,YAAY,IAAI,OAAO,GAAG;AAC1B,gBAAgB,QAAQ,EAAE,QAAQ;AAClC,gBAAgB,SAAS,EAAE,SAAS;AACpC,gBAAgB,KAAK,EAAE,SAAS,CAAC,KAAK,IAAI,IAAI;AAC9C,gBAAgB,SAAS,EAAE,SAAS,CAAC,SAAS;AAC9C,aAAa,CAAC;AACd,YAAY,IAAI,IAAI,GAAG,sBAAsB,CAAC,SAAS,CAAC,CAAC;AACzD,YAAY,OAAO,KAAK,EAAE;AAC1B,gBAAgB,IAAI,iBAAiB,GAAG,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC7D,gBAAgB,IAAIS,iBAAO,CAAC,iBAAiB,CAAC,EAAE;AAChD,oBAAoB,KAAK,GAAG,sBAAsB,CAAC,iBAAiB,CAAC,CAAC;AACtE,iBAAiB;AACjB,qBAAqB;AAGrB,oBAAoB,cAAc,GAAG,iBAAiB,IAAI,SAAS,CAAC;AACpE,oBAAoB,MAAM;AAC1B,iBAAiB;AACjB,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,cAAc,KAAK,KAAK,CAAC,EAAE;AACvC,YAAY,cAAc;AAC1B,gBAAgB,SAAS,CAAC,KAAK;AAC/B,oBAAoBoB,+BAAqB,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,SAAS,CAAC;AAC/E,sBAAsBC,yBAAe,CAAC,SAAS,EAAE,sBAAsB,CAAC,SAAS,CAAC,CAAC,CAAC;AACpF,SAAS;AAGT,QAAQ,IAAI,cAAc,KAAK,KAAK,EAAE;AACtC,YAAY,OAAO,SAAS,CAAC;AAC7B,SAAS;AAIT,QAAQ,OAAO,SAAS,KAAK,sBAAsB,CAAC,cAAc,CAAC,GAAG,cAAc;AACpF,cAAc,SAAS,GAAG,GAAG,GAAG,cAAc,CAAC;AAC/C,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,OAAO,EAAE,OAAO,EAAE;AAC/D,QAAQ,IAAI,iBAAiB,GAAG,OAAO,CAAC,IAAI,CAAC;AAC7C,QAAQ,IAAI,CAAC,iBAAiB;AAC9B,YAAY,OAAO;AACnB,QAAQ,IAAI,WAAW,GAAG,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,SAAS,CAAC;AAC7D,QAAQ,IAAI,CAAC,WAAW;AACxB,YAAY,OAAO;AACnB,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,EAAE;AACzC,YAAY,IAAI,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;AACxF,YAAY,IAAI,QAAQ;AACxB,gBAAgB,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC5C,SAAS;AACT,QAAQ,IAAI,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC7D,QAAQ,IAAI,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;AAC/D,QAAQ,IAAI,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;AACtF,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AAC7E,QAAQ,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC;AACzC,QAAQ,IAAI,IAAI,EAAE;AAClB,YAAY,IAAI,WAAW,GAAG,wBAAwB,CAAC,IAAI,EAAE,iBAAiB,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,UAAU,CAACvB,qBAAW,CAAC,iBAAiB,CAAC;AACzJ,gBAAgB,iBAAiB,CAAC,KAAK;AACvC,kBAAkB,iBAAiB,EAAE,cAAc,CAAC,CAAC,CAAC;AAEtD,YAAY,OAAO,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE;AACzD,gBAAgB,QAAQ;AACxB,gBAAgB,WAAW;AAC3B,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,QAAQ,EAAE,SAAS,EAAE;AACxE,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AACrE,QAAQ,OAAO,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC;AACrC,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,cAAc,EAAE,SAAS,EAAE,aAAa,EAAE;AAC9F,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AAC3E,QAAQ,IAAI,KAAK,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;AAC3C,QAAQ,IAAI,CAAC,KAAK,IAAI,aAAa,EAAE;AACrC,YAAY,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AACvD,YAAY,KAAK,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC;AAC3C,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,QAAQ,EAAE,QAAQ,EAAE,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE;AAC9F,QAAQ,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;AACvE,QAAQ,IAAI,KAAK,KAAK,WAAW,EAAE;AAInC,YAAY,OAAO,wBAAwB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC/E,SAAS;AACT,QAAQ,IAAI,KAAK,KAAK,YAAY,EAAE;AAEpC,YAAY,OAAO,QAAQ,CAAC;AAC5B,SAAS;AAKT,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE;AAC/B,YAAY,QAAQ,GAAG,KAAK,CAAC,CAAC;AAC9B,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,wBAAwB,CAAC,IAAI;AAYtE,QAAQ,KAAK,CAAC,EAAE;AAChB,YAAY,QAAQ,EAAE,QAAQ;AAC9B,YAAY,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;AACvC,YAAY,KAAK,EAAE,KAAK;AACxB,YAAY,SAAS,EAAE,OAAO,CAAC,SAAS;AACxC,SAAS,EAAE,OAAO,EAAE,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACrD,KAAK,CAAC;AACN,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC,EAAE,EAAE;AAEL,SAAS,wBAAwB,CAAC,QAAQ,EAAE,iBAAiB,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE;AAC5F,IAAI,IAAI,cAAc,GAAG,QAAQ,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;AAC/D,IAAI,IAAI,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;AAC3D,IAAI,IAAI,SAAS,GAAG,SAAS,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;AAC7D,IAAI,IAAI,EAAE,GAAG,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC;AAC/E,IAAI,OAAO;AACX,QAAQ,IAAI,EAAE,sBAAsB,CAAC,SAAS,CAAC;AAC/C,QAAQ,KAAK,EAAE,SAAS,CAAC,KAAK,IAAI,IAAI;AACtC,QAAQ,SAAS,EAAE,SAAS;AAC5B,QAAQ,cAAc,EAAE,cAAc;AACtC,QAAQ,SAAS,EAAE,SAAS;AAC5B,QAAQ,WAAW,EAAEA,qBAAW;AAChC,QAAQ,WAAW,EAAE,WAAW;AAChC,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,KAAK,EAAE,QAAQ,CAAC,KAAK;AAC7B,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,SAAS,EAAE,YAAY;AAC/B,YAAY,OAAO,QAAQ,CAAC,SAAS,CAAC,yBAAyB,CAAC,SAAS,EAAE,iBAAiB,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC,CAAC;AACnH,SAAS;AACT,QAAQ,YAAY,EAAE,wBAAwB,CAAC,OAAO,CAAC,KAAK,CAAC;AAC7D,KAAK,CAAC;AACN,CAAC;AACM,SAAS,yBAAyB,CAAC,aAAa,EAAE,iBAAiB,EAAE,SAAS,EAAE;AACvF,IAAI,IAAI,kBAAkB,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC;AACpG,IAAI,IAAI,OAAO,CAAC;AAChB,IAAI,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE;AAChD,QAAQ,OAAO,GAAG;AAClB,YAAY,SAAS,EAAE,kBAAkB;AAIzC,YAAY,IAAI,EAAE,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,iBAAiB;AACrD,SAAS,CAAC;AACV,KAAK;AACL,SAAS;AACT,QAAQ,OAAO,GAAGvB,cAAQ,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAC;AAGnD,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;AAC3C,YAAY,OAAO,CAAC,IAAI,GAAG,iBAAiB,CAAC;AAC7C,SAAS;AACT,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;AACjE,QAAQ,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIgB,iBAAS,CAAC,IAAI,CAAC,CAAC,EAAE+B,6BAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AAC1G,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,KAAK,OAAO,CAAC,SAAS,EAAE;AACtC,QAAQ,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;AACtC,KAAK;AACL,IAAI,OAAO,OAAO,CAAC;AACnB,CAAC;AACD,SAAS,wBAAwB,CAAC,KAAK,EAAE;AACzC,IAAI,OAAO,SAAS,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE;AACrD,QAAQ,IAAItB,iBAAO,CAAC,QAAQ,CAAC,IAAIA,iBAAO,CAAC,QAAQ,CAAC,EAAE;AACpD,YAAY,MAAMe,yBAAiB,CAAC,CAAC,CAAC,CAAC;AACvC,SAAS;AAKT,QAAQ,IAAIhB,yBAAe,CAAC,QAAQ,CAAC,IAAIA,yBAAe,CAAC,QAAQ,CAAC,EAAE;AACpE,YAAY,IAAI,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACpE,YAAY,IAAI,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACpE,YAAY,IAAI,WAAW,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AAChE,YAAY,IAAI,WAAW,EAAE;AAC7B,gBAAgB,OAAO,QAAQ,CAAC;AAChC,aAAa;AACb,YAAY,IAAID,qBAAW,CAAC,QAAQ,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC,EAAE;AAI5E,gBAAgB,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AACtD,gBAAgB,OAAO,QAAQ,CAAC;AAChC,aAAa;AACb,YAAY,IAAI,uBAAuB,CAAC,QAAQ,CAAC,IAAIA,qBAAW,CAAC,QAAQ,CAAC,EAAE;AAK5E,gBAAgB,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AACtD,gBAAgB,OAAO,QAAQ,CAAC;AAChC,aAAa;AACb,YAAY,IAAI,uBAAuB,CAAC,QAAQ,CAAC;AACjD,gBAAgB,uBAAuB,CAAC,QAAQ,CAAC,EAAE;AACnD,gBAAgB,OAAOvB,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,CAAC;AAClE,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK,CAAC;AACN;;AC7kBA,SAAS,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE;AACzD,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrD,IAAI,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,QAAQ,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,GAAG,QAAQ;AAC1C,YAAY,OAAO,CAAC,UAAU,KAAK,UAAU,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ;AAC9E,gBAAgB,OAAO;AACvB,kBAAkBA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AACpG,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB,CAAC;AACD,IAAI,WAAW,KAAkB,YAAY;AAC7C,IAAI,SAAS,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;AACnD,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AAC3B,QAAQ,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAC7B,QAAQ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AACnC,KAAK;AACL,IAAI,WAAW,CAAC,SAAS,CAAC,YAAY,GAAG,UAAU,KAAK,EAAE,EAAE,EAAE;AAC9D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AACzH,QAAQ,IAAI,mBAAmB,GAAGgD,gCAAsB,CAAC,KAAK,CAAC,CAAC;AAChE,QAAQ,IAAI,MAAM,GAAG,yBAAyB,EAAE,CAAC;AACjD,QAAQ,SAAS,GAAGhD,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAEmC,0BAAgB,CAAC,mBAAmB,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AAC7F,QAAQ,IAAI,OAAO,GAAGnC,cAAQ,CAACA,cAAQ,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,UAAU,QAAQ,EAAE,QAAQ,EAAE;AAC3H,gBAAgB,OAAO,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACxD,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAEqC,4BAAkB,CAAC,SAAS,CAAC,EAAE,EAAE,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,SAAS,EAAE,YAAY,EAAE,IAAI,GAAG,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC;AAC9O,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC3C,YAAY,MAAM,EAAE,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;AACjD,YAAY,MAAM,EAAE,MAAM;AAC1B,YAAY,YAAY,EAAE,mBAAmB,CAAC,YAAY;AAC1D,YAAY,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE;AACzC,YAAY,OAAO,EAAE,OAAO;AAC5B,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAACd,qBAAW,CAAC,GAAG,CAAC,EAAE;AAC/B,YAAY,MAAMiB,yBAAiB,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAChD,SAAS;AAGT,QAAQ,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE;AAC3D,YAAY,IAAI,WAAW,GAAG,EAAE,CAAC,WAAW,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,CAAC;AACvG,YAAY,IAAI,SAAS,GAAGX,uBAAa,CAAC,MAAM,CAAC,CAAC;AAClD,YAAY,IAAI,SAAS,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE;AACjD,gBAAgB,IAAI,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;AAC5F,gBAAgB,IAAIN,qBAAW,CAAC,OAAO,CAAC,EAAE;AAI1C,oBAAoB,OAAO;AAC3B,iBAAiB;AAGjB,gBAAgB,WAAW,GAAG,OAAO,CAAC;AACtC,aAAa;AACb,YAAY,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AACpE,gBAAgB,IAAI,yBAAyB,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpE,gBAAgB,YAAY,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;AACtD,oBAAoB,IAAI,KAAK,CAAC,YAAY,EAAE;AAC5C,wBAAwB,yBAAyB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAC3E,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,IAAI,iBAAiB,GAAG,UAAU,cAAc,EAAE;AAClE,oBAAoB,OAAO,yBAAyB,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;AAC5F,wBAAwB,IAAI,CAAC;AAC7B,iBAAiB,CAAC;AAClB,gBAAgB,IAAI,kBAAkB,GAAG,UAAU,cAAc,EAAE;AACnE,oBAAoB,IAAI,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AACnF,oBAAoB,OAAO,OAAO,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxF,iBAAiB,CAAC;AAClB,gBAAgB,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,cAAc,EAAE;AAK3E,oBAAoB,IAAI,iBAAiB,CAAC,cAAc,CAAC;AACzD,wBAAwB,CAAC,kBAAkB,CAAC,cAAc,CAAC,EAAE;AAC7D,wBAAwB,iBAAiB,CAAC,SAAS,EAAE,WAAW,EAAE,cAAc,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;AACjG,qBAAqB;AACrB,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,YAAY,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC7C,SAAS,CAAC,CAAC;AAMX,QAAQ,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,EAAE,EAAE;AAC9D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,YAAY,GAAG,EAAE,CAAC,YAAY,EAAE,OAAO,GAAG,EAAE,CAAC,OAAO;AAGxG,QAAQ,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC;AACjC,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;AAG3C,QAAQ,IAAI,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAI3C,QAAQ,IAAI,QAAQ,GAAG,CAAC,MAAM,IAAI,QAAQ,CAAC,iBAAiB,CAAC,MAAM,CAAC;AACpE,YAAY0B,+BAAqB,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC;AAC5E,aAAa,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC;AAChE,QAAQ,IAAI,QAAQ,KAAK,OAAO,QAAQ,EAAE;AAC1C,YAAY,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC;AAC3C,SAAS;AAST,QAAQ,IAAI,SAAS,GAAG,YAAY;AACpC,YAAY,IAAI,OAAO,GAAG,yBAAyB,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AAC5F,YAAY,IAAI1B,qBAAW,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC3C,gBAAgB,IAAI,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxE,gBAAgB,IAAI,IAAI,EAAE;AAC1B,oBAAoB,IAAI,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAACvB,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AAC5H,oBAAoB,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE;AAC7C,wBAAwB,OAAO,QAAQ,CAAC;AACxC,qBAAqB;AACrB,iBAAiB;AACjB,aAAa;AACb,YAAY,OAAO,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACxD,SAAS,CAAC;AACV,QAAQ,IAAI,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;AACrC,QAAQ,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,MAAM;AAI/C,QAAQ,OAAO,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,OAAO,EAAE,KAAK,EAAE;AAC7D,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,IAAI,cAAc,GAAGS,gCAAsB,CAAC,KAAK,CAAC,CAAC;AAC/D,YAAY,IAAI,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;AAC/C,YAAY,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACpC,YAAY,IAAI,KAAK,KAAK,KAAK,CAAC,EAAE;AAClC,gBAAgB,IAAI,cAAc,GAAG,QAAQ,CAAC,iBAAiB,CAAC;AAChE,oBAAoB,QAAQ,EAAE,QAAQ;AACtC,oBAAoB,SAAS,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK;AAC/C,oBAAoB,KAAK,EAAE,KAAK;AAChC,oBAAoB,SAAS,EAAE,OAAO,CAAC,SAAS;AAChD,iBAAiB,CAAC,CAAC;AACnB,gBAAgB,IAAI,SAAS,GAAG,iBAAiB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AAC7E,gBAAgB,IAAI,aAAa,GAAG,KAAK,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK;AAGxE,gBAAgB,KAAK,CAAC,YAAY;AAClC,oBAAoB,gBAAgB,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAC;AAC3D,sBAAsB,OAAO,EAAE,SAAS,CAAC,CAAC;AAI1C,gBAAgB,IAAI,aAAa,GAAG,KAAK,CAAC,CAAC;AAG3C,gBAAgB,IAAI,KAAK,CAAC,YAAY;AACtC,qBAAqBc,qBAAW,CAAC,aAAa,CAAC,IAAI,uBAAuB,CAAC,aAAa,CAAC,CAAC,EAAE;AAC5F,oBAAoB,aAAa,GAAG,SAAS,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;AAC3E,iBAAiB;AACjB,gBAAgB,IAAI,KAAK,GAAG,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;AACjG,gBAAgB,IAAI,KAAK,EAAE;AAC3B,oBAAoB,SAAS,CAAC,IAAI,GAAG;AAErC,wBAAwB,KAAK,EAAE,KAAK;AACpC,wBAAwB,QAAQ,EAAE,QAAQ;AAC1C,wBAAwB,KAAK,EAAE,KAAK;AACpC,qBAAqB,CAAC;AACtB,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,0BAA0B,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AAC1E,iBAAiB;AACjB,gBAAgB,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,GAAG,EAAE;AAC3D,oBAAoB,EAAE,CAAC,cAAc,CAAC,GAAG,aAAa;AACtD,oBAAoB,EAAE,EAAE,CAAC;AACzB,aAAa;AACb,iBAAiB,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK;AACjD,gBAAgB,CAAC,OAAO,CAAC,UAAU;AACnC,gBAAgB,CAAC,OAAO,CAAC,QAAQ;AACjC,gBAAgB,CAACe,+BAAqB,CAAC,KAAK,CAAC,KAAK,CAAC;AAInD,gBAAgB,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACvE,gBAAgB,UAAU,CAAC,OAAO,KAAK,KAAK,IAAItB,iBAAS,CAAC,KAAK,CAAC,EAAE,EAAEP,gCAAsB,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC;AAC3G,aAAa;AACb,SAAS,CAAC,CAAC;AAGX,QAAQ,IAAI;AACZ,YAAY,IAAI,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE;AAC/C,gBAAgB,QAAQ,EAAE,QAAQ;AAClC,gBAAgB,YAAY,EAAE,YAAY;AAC1C,gBAAgB,WAAW,EAAE,OAAO,CAAC,WAAW;AAChD,gBAAgB,WAAW,EAAE,QAAQ;AACrC,gBAAgB,SAAS,EAAE,SAAS;AACpC,aAAa,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;AAG9C,YAAY,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;AAGlC,YAAY,IAAI,SAAS,EAAE;AAE3B,gBAAgB,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;AAC9D,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE;AAElB,YAAY,IAAI,CAAC,MAAM;AACvB,gBAAgB,MAAM,CAAC,CAAC;AACxB,SAAS;AACT,QAAQ,IAAI,QAAQ,KAAK,OAAO,MAAM,EAAE;AACxC,YAAY,IAAI,OAAO,GAAGoB,uBAAa,CAAC,MAAM,CAAC,CAAC;AAMhD,YAAY,IAAI,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;AACjF,YAAY,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;AAC/C,gBAAgB,OAAO,OAAO,CAAC;AAC/B,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAMpC,YAAY,IAAI,IAAI,CAAC,MAAM;AAC3B,gBAAgB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE;AAC7E,gBAAgB,OAAO,OAAO,CAAC;AAC/B,aAAa;AACb,YAAY,IAAI,UAAU,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC9D,YAAY,IAAI,UAAU,EAAE;AAC5B,gBAAgB,UAAU,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACzF,gBAAgB,UAAU,CAAC,SAAS,GAAG,eAAe,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACxF,gBAAgB,YAAY,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACtG,aAAa;AACb,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE;AACjD,oBAAoB,WAAW,EAAE,QAAQ;AAIzC,oBAAoB,SAAS,EAAE,gBAAgB,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,GAAG,SAAS;AAC/E,oBAAoB,YAAY,EAAE,YAAY;AAC9C,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,YAAY,OAAO,OAAO,CAAC;AAC3B,SAAS;AACT,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE;AAC1F,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,KAAK,KAAK,IAAI,EAAE;AAInD,YAAY,OAAO,UAAU,CAAC,OAAO,KAAK,KAAK,GAAGqB,mBAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AAC3E,SAAS;AACT,QAAQ,IAAIzB,iBAAO,CAAC,KAAK,CAAC,EAAE;AAC5B,YAAY,OAAO,KAAK,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE,CAAC,EAAE;AAChD,gBAAgB,IAAI,KAAK,GAAG,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,iBAAiB,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3G,gBAAgB,0BAA0B,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;AACzD,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa,CAAC,CAAC;AACf,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,mBAAmB,CAAC;AACxC,YAAY,MAAM,EAAE,KAAK;AACzB,YAAY,YAAY,EAAE,KAAK,CAAC,YAAY;AAC5C,YAAY,OAAO,EAAE,OAAO;AAC5B,YAAY,SAAS,EAAE,SAAS;AAChC,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AAGN,IAAI,WAAW,CAAC,SAAS,CAAC,aAAa,GAAG,UAAU,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE;AAC7F,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC,EAAE,EAAE,QAAQ,GAAGwB,+BAAqB,CAAC,MAAM,EAAE,YAAY,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,EAAE;AACjH,QAAQ,IAAI,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;AACjC,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC3C,QAAQ,IAAI,YAAY,GAAG,IAAIlB,SAAI,CAAC,KAAK,CAAC,CAAC;AAC3C,QAAQ,CAAC,SAAS,OAAO,CAAC,YAAY,EAAE,gBAAgB,EAAE;AAC1D,YAAY,IAAI,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,YAAY;AAK9D,YAAY,gBAAgB,CAAC,UAAU,EAAE,gBAAgB,CAAC,QAAQ,CAAC,CAAC;AACpE,YAAY,IAAI,WAAW,CAAC,OAAO;AACnC,gBAAgB,OAAO;AACvB,YAAY,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;AACvC,YAAY,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;AACjE,gBAAgB,IAAI,CAACxB,uBAAa,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC;AAChE,oBAAoB,OAAO;AAC3B,gBAAgB,IAAI,UAAU,GAAG,gBAAgB,CAAC,UAAU,EAAE,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC;AACnG,gBAAgB;AAIhB,gBAAgB,EAAE,UAAU,IAAI,QAAQ,CAAC;AACzC,oBAAoBmC,yBAAe,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;AAC3D,oBAAoB,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AAChE,wBAAwB,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;AAClD,wBAAwB,IAAI,IAAI,KAAK,QAAQ;AAC7C,4BAA4B,UAAU,GAAG,IAAI,CAAC;AAC9C,wBAAwB,IAAI,IAAI,KAAK,OAAO,EAAE;AAC9C,4BAA4B,IAAI,IAAI,GAAGC,kCAAwB,CAAC,GAAG,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AAKxF,4BAA4B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,EAAE;AAC5D,gCAAgC,QAAQ,GAAG,IAAI,CAAC;AAChD,6BAA6B;AAG7B,yBAAyB;AACzB,qBAAqB,CAAC,CAAC;AACvB,iBAAiB;AACjB,gBAAgB,IAAInC,iBAAO,CAAC,SAAS,CAAC,EAAE;AACxC,oBAAoB,IAAI,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAC3D,oBAAoB,IAAI,QAAQ,EAAE;AAIlC,wBAAwB,UAAU,GAAG,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC;AACvE,wBAAwB,QAAQ,GAAG,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC;AACjE,qBAAqB;AACrB,oBAAoB,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC7F,iBAAiB;AACjB,qBAAqB;AACrB,oBAAoB,IAAI,QAAQ,GAAGE,kCAAwB,CAAC,SAAS,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;AAC/F,oBAAoB,IAAI,CAAC,QAAQ,IAAI,SAAS,CAAC,IAAI,KAAK6B,YAAI,CAAC,eAAe,EAAE;AAC9E,wBAAwB,MAAMC,yBAAiB,CAAC,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC1E,qBAAqB;AACrB,oBAAoB,IAAI,QAAQ;AAChC,wBAAwB,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,EAAE;AACjG,wBAAwB,OAAO,CAAC,QAAQ,CAAC,YAAY,EAAE,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC;AACxG,qBAAqB;AACrB,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,CAAC;AAClC,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE;AAC1G,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,CAACjB,qBAAW,CAAC,QAAQ,CAAC,EAAE;AAC1D,YAAY,IAAI,GAAG;AAInB,YAAY,CAAC,CAACE,iBAAO,CAAC,QAAQ,CAAC;AAI/B,iBAAiBF,qBAAW,CAAC,QAAQ,CAAC,IAAI,uBAAuB,CAAC,QAAQ,CAAC,CAAC;AAC5E,gBAAgB,QAAQ;AACxB,kBAAkB,KAAK,CAAC,CAAC;AAIzB,YAAY,IAAI,GAAG,GAAG,QAAQ,CAAC;AAK/B,YAAY,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE;AACxC,gBAAgB,cAAc,GAAG,CAACA,qBAAW,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AACtE,aAAa;AAMb,YAAY,IAAI,eAAe,CAAC;AAChC,YAAY,IAAI,UAAU,GAAG,UAAU,IAAI,EAAE,IAAI,EAAE;AACnD,gBAAgB,QAAQE,iBAAO,CAAC,IAAI,CAAC;AACrC,oBAAoB,OAAO,IAAI,KAAK,QAAQ;AAC5C,wBAAwB,IAAI,CAAC,IAAI,CAAC;AAClC,0BAA0B,KAAK,CAAC;AAChC,sBAAsB,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;AACvE,aAAa,CAAC;AACd,YAAY,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE,cAAc,EAAE;AACvE,gBAAgB,IAAI,IAAI,GAAG,UAAU,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;AAC3D,gBAAgB,IAAI,IAAI,GAAG,UAAU,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;AAE3D,gBAAgB,IAAI,KAAK,CAAC,KAAK,IAAI;AACnC,oBAAoB,OAAO;AAC3B,gBAAgB,IAAI,cAAc,EAAE;AACpC,oBAAoB,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AACxD,iBAAiB;AACjB,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,CAAC,CAAC;AAC7F,gBAAgB,IAAI,IAAI,KAAK,IAAI,EAAE;AACnC,oBAAoB,eAAe,GAAG,eAAe,IAAI,IAAI,GAAG,EAAE,CAAC;AACnE,oBAAoB,eAAe,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AAC9D,iBAAiB;AACjB,gBAAgB,IAAI,cAAc,EAAE;AACpC,oBAAoBT,iBAAS,CAAC,cAAc,CAAC,GAAG,EAAE,KAAK,cAAc,CAAC,CAAC;AACvE,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,YAAY,IAAI,eAAe,EAAE;AAEjC,gBAAgB,QAAQ,IAAIS,iBAAO,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGzB,cAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;AAC7E,gBAAgB,eAAe,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,IAAI,EAAE;AAC/D,oBAAoB,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AAC3C,iBAAiB,CAAC,CAAC;AACnB,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,SAAS,CAAC,IAAI,EAAE;AAC5B,YAAY,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAE,OAAO,EAAE,cAAc,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC,CAAC;AAClL,SAAS;AACT,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK,CAAC;AACN,IAAI,OAAO,WAAW,CAAC;AACvB,CAAC,EAAE,CAAC,CAAC;AAEL,IAAI,kBAAkB,GAAG,EAAE,CAAC;AAC5B,SAAS,iBAAiB,CAAC,EAAE,EAAE,IAAI,EAAE;AACrC,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC;AACrB,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACxB,QAAQ,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,kBAAkB,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC,CAAC;AACtE,KAAK;AACL,IAAI,OAAO,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACzB,CAAC;AACD,SAAS,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE;AACtC,IAAI,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,gBAAgB,CAAC,KAAK,CAAC;AAC3D,QAAQ,OAAO,IAAI,CAAC;AACpB,IAAI,IAAI,CAAC,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC;AACvC,QAAQ,OAAO,KAAK,CAAC;AACrB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,GAAGA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC;AACjH,IAAI,IAAI,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;AAC1D,IAAI,IAAI,GAAG,GAAG,eAAe,GAAG,IAAI,GAAG,EAAE;AACzC,UAAU,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG;AAClC,cAAc,KAAK,CAAC,GAAG,CAAC;AACxB,IAAI,IAAI,MAAM,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC1C,IAAI,IAAI,eAAe,EAAE;AACzB,QAAQ,IAAI,oBAAoB,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;AAC7D,QAAQ,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE,GAAG,EAAE;AAClD,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/E,YAAY,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC7C,SAAS,CAAC,CAAC;AACX,QAAQ,oBAAoB,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AACpD,YAAY,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,eAAe,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AACxF,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AACD,SAAS,gBAAgB,CAAC,IAAI,EAAE;AAChC,IAAI,OAAO,CAAC,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClD,CAAC;AACD,SAAS,0BAA0B,CAAC,EAAE,EAAE,IAAI,EAAE;AAC9C,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC;AACrB,IAAI,IAAI,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClC,IAAI,IAAI,SAAS,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;AAClD,QAAQ,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC3C,QAAQ,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzB,KAAK;AACL,CAAC;AACD,IAAI,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;AAGzB,SAAS,iBAAiB,CAAC,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,KAAK,EAAE;AAC5E,IAAI,IAAI,QAAQ,GAAG,UAAU,QAAQ,EAAE;AACvC,QAAQ,IAAI,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AAClE,QAAQ,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC;AAClD,KAAK,CAAC;AACN,IAAI,IAAI,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,QAAQ;AACjB,QAAQ,OAAO;AACf,IAAI,IAAI,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,QAAQ;AACjB,QAAQ,OAAO;AAGf,IAAI,IAAIuB,qBAAW,CAAC,QAAQ,CAAC;AAC7B,QAAQ,OAAO;AAGf,IAAI,IAAIpB,WAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACjC,QAAQ,OAAO;AAIf,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,UAAU,GAAG,EAAE,EAAE,OAAO,KAAK,CAAC,aAAa,CAAC,QAAQ,EAAE,GAAG,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;AAC/G,QAAQ,OAAO;AACf,KAAK;AACL,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC;AACnE,QAAQ,KAAK,CAAC,aAAa,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AACvD,IAAI,IAAI,SAAS,GAAG,sBAAsB,CAAC,cAAc,CAAC,CAAC;AAC3D,IAAI,IAAI,WAAW,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AAEnE,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC;AACjC,QAAQ,OAAO;AACf,IAAI,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC9B,IAAI,IAAI,cAAc,GAAG,EAAE,CAAC;AAG5B,IAAI,IAAI,CAACsB,iBAAO,CAAC,QAAQ,CAAC,IAAI,CAACA,iBAAO,CAAC,QAAQ,CAAC,EAAE;AAClD,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;AACtD,YAAY,IAAI,QAAQ,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACpE,YAAY,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACpF,gBAAgB,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9C,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK;AACL,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIT,iBAAS,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,CAAC,MAAM;AACnG,QAAQ,oCAAoC;AAC5C,YAAY,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC;AACxC,YAAY,6CAA6C;AACzD,UAAU,EAAE,EAAE,WAAW,EAAEhB,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAEA,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC3E;;AC/fG,IAAC,aAAa,KAAkB,UAAU,MAAM,EAAE;AACrD,IAAIqB,eAAS,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AACrC,IAAI,SAAS,aAAa,CAAC,MAAM,EAAE;AACnC,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAC/C,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;AAC9C,QAAQ,KAAK,CAAC,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAClC,QAAQ,KAAK,CAAC,oBAAoB,GAAG,IAAI8B,2BAAiB,CAACb,+BAAqB,CAAC,CAAC;AAGlF,QAAQ,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC;AAC5C,QAAQ,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;AAChC,QAAQ,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;AAC1B,QAAQ,KAAK,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;AAC/C,QAAQ,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AACvD,QAAQ,KAAK,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC;AACtC,YAAY,KAAK,EAAE,KAAK;AACxB,YAAY,gBAAgB,EAAE,KAAK,CAAC,MAAM,CAAC,gBAAgB;AAC3D,YAAY,aAAa,EAAE,KAAK,CAAC,MAAM,CAAC,aAAa;AACrD,YAAY,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,YAAY;AACnD,SAAS,CAAC,CAAC;AACX,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;AACrB,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,aAAa,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;AAI/C,QAAQ,IAAI,SAAS,IAAI,IAAI,CAAC,IAAI,GAAG,IAAIX,mBAAW,CAAC,IAAI,CAAC;AAC1D,YAAY,QAAQ,EAAE,IAAI,CAAC,QAAQ;AACnC,YAAY,aAAa,EAAE,IAAI,CAAC,MAAM,CAAC,aAAa;AACpD,SAAS,CAAC,CAAC,CAAC;AAMZ,QAAQ,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC;AAC9C,QAAQ,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAChC,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,qBAAqB,EAAE;AAChF,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;AAC9C,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;AAI9C,QAAQ,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC;AACrF,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,WAAW,EAAE,IAAI,CAAC,WAAW;AACzC,YAAY,kBAAkB,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB;AAC9D,YAAY,eAAe,EAAE,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/D,YAAY,KAAK,EAAE,qBAAqB,GAAG,KAAK,CAAC,IAAI,cAAc,IAAI,cAAc,CAAC,KAAK,CAAC;AAC5F,YAAY,SAAS,EAAE,SAAS;AAChC,SAAS,CAAC,GAAG,SAAS,CAAC,CAAC;AACxB,QAAQ,IAAI,CAAC,mBAAmB,GAAGhB,aAAI,CAAC,UAAU,CAAC,EAAE,OAAO,EAAE;AAC9D,YAAY,OAAO,KAAK,CAAC,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACpD,SAAS,EAAE;AACX,YAAY,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB;AAC/C,gBAAgBE,oBAAU,CAAC,mCAAmC,CAAC;AAC/D,gBAAgB,IAAI;AACpB,YAAY,YAAY,EAAE,UAAU,CAAC,EAAE;AAGvC,gBAAgB,IAAI,KAAK,GAAG,CAAC,CAAC,UAAU,GAAG,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC;AAC7E,gBAAgB,IAAI,qBAAqB,CAAC,KAAK,CAAC,EAAE;AAClD,oBAAoB,IAAI,UAAU,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;AACtF,oBAAoB,OAAO,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK;AAOrD,oBAAoB,CAAC,CAAC,QAAQ,EAAEwB,4BAAkB,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;AAC9G,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AAIX,QAAQ,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE;AACvF,YAAY,OAAO,KAAK,CAAC,YAAY,EAAE,CAAC;AACxC,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,IAAI,EAAE;AACtD,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;AAIpB,QAAQ,IAAI,IAAI;AAChB,YAAY,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpC,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,UAAU,EAAE;AAC5D,QAAQ,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE;AAC1D,QAAQ,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC;AACxE,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,OAAO,EAAE;AACtD,QAAQ;AAQR,QAAQ,EAAE,GAAG,OAAO,CAAC,iBAAiB;AAQtC,QAAQ,iBAAiB,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE,CAAC;AACvD,QAAQ,IAAI;AACZ,YAAY,QAAQ,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAACrC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,IAAI,EAAE;AACxO,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE;AAClB,YAAY,IAAI,CAAC,YAAY,iBAAiB,EAAE;AAMhD,gBAAgB,OAAO,IAAI,CAAC;AAC5B,aAAa;AACb,YAAY,MAAM,CAAC,CAAC;AACpB,SAAS;AACT,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,EAAE;AACvD,QAAQ,IAAI;AACZ,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC;AAC3B,YAAY,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACrE,SAAS;AACT,gBAAgB;AAChB,YAAY,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;AAChE,gBAAgB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxC,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,OAAO,EAAE;AACxD,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AAUvD,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS;AACT,QAAQ,IAAI,KAAK,GAAG,EAAE,OAAO,CAAC,UAAU;AACxC;AACA,YAAY,IAAI,CAAC,cAAc;AAC/B,cAAc,IAAI,CAAC,IAAI,CAAC;AACxB,QAAQ,IAAI;AACZ,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC;AAC3B,YAAY,OAAO,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,YAAY,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAC5E,SAAS;AACT,gBAAgB;AAChB,YAAY,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;AAChE,gBAAgB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxC,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,OAAO,EAAE;AACtD,QAAQ,OAAO,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,IAAI,YAAY,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACjN,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,KAAK,EAAE;AACrD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;AAWhC,YAAY,WAAW,CAAC,IAAI,CAAC,CAAC;AAC9B,SAAS;AACT,QAAQ,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAChC,QAAQ,IAAI,KAAK,CAAC,SAAS,EAAE;AAC7B,YAAY,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAC5C,SAAS;AACT,QAAQ,OAAO,YAAY;AAI3B,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE;AACpE,gBAAgB,WAAW,CAAC,KAAK,CAAC,CAAC;AACnC,aAAa;AAIb,YAAY,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACpD,SAAS,CAAC;AACV,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE,GAAG,UAAU,OAAO,EAAE;AACpD,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQqC,4BAAkB,CAAC,KAAK,EAAE,CAAC;AACnC,QAAQe,eAAK,CAAC,KAAK,EAAE,CAAC;AACtB,QAAQ,IAAI,CAAC,oBAAoB,CAAC,UAAU,EAAE,CAAC;AAC/C,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;AAC3F,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC;AAC3C,QAAQ,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACtC,YAAY,IAAI,OAAO,CAAC,gBAAgB,EAAE;AAC1C,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;AACrE,aAAa;AACb,iBAAiB,IAAI,OAAO,CAAC,qBAAqB,EAAE;AACpD,gBAAgB,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;AAC9C,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,GAAG,CAAC;AACnB,KAAK,CAAC;AAQN,IAAI,aAAa,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;AACnE,QAAQ,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;AAC7E,KAAK,CAAC;AAMN,IAAI,aAAa,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,UAAU,EAAE;AACpE,QAAQ,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9E,KAAK,CAAC;AAON,IAAI,aAAa,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,MAAM,EAAE;AACzD,QAAQ,IAAI7B,qBAAW,CAAC,MAAM,CAAC;AAC/B,YAAY,OAAO,MAAM,CAAC,KAAK,CAAC;AAChC,QAAQ,IAAI;AACZ,YAAY,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,SAAS;AACT,QAAQ,OAAO,CAAC,EAAE;AAClB,YAAY,UAAU,CAAC,OAAO,KAAK,KAAK,IAAIP,iBAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9D,SAAS;AACT,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,EAAE;AACvD,QAAQ,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;AACzB,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;AAG5C,gBAAgB,OAAO,KAAK,CAAC;AAC7B,aAAa;AACb,YAAY,OAAO,GAAGhB,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;AAC5E,SAAS;AACT,QAAQ,IAAI;AAKZ,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC;AAI3B,YAAY,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACjE,SAAS;AACT,gBAAgB;AAChB,YAAY,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,EAAE;AAChE,gBAAgB,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACxC,aAAa;AACb,SAAS;AACT,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,EAAE;AACvD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;AACpB,QAAQqC,4BAAkB,CAAC,KAAK,EAAE,CAAC;AACnC,QAAQ,IAAI,OAAO,IAAI,OAAO,CAAC,cAAc,EAAE;AAG/C,YAAY,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACvG,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AACjC,YAAY,WAAW,CAAC,IAAI,CAAC,CAAC;AAC9B,SAAS;AACT,aAAa;AAOb,YAAY,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACpC,SAAS;AACT,QAAQ,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;AACjC,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,UAAU,EAAE;AACrE,QAAQ,IAAI,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC5E,QAAQ,IAAI,iBAAiB,KAAK,IAAI,CAAC,cAAc,EAAE;AACvD,YAAY,IAAI,CAAC,cAAc,GAAG,iBAAiB,CAAC;AACpD,YAAY,IAAI,CAAC,gBAAgB,EAAE,CAAC;AACpC,SAAS;AACT,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,OAAO,EAAE;AACvD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,GAAG,OAAO,CAAC,UAAU,EAAE,UAAU,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE,EAAE,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,EAAE,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;AAC3L,QAAQ,IAAI,YAAY,CAAC;AACzB,QAAQ,IAAI,OAAO,GAAG,UAAU,KAAK,EAAE;AACvC,YAAY,IAAI,EAAE,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,cAAc,GAAG,EAAE,CAAC,cAAc,CAAC;AAC/E,YAAY,EAAE,KAAK,CAAC,OAAO,CAAC;AAC5B,YAAY,IAAI,KAAK,EAAE;AACvB,gBAAgB,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC;AAC1D,aAAa;AACb,YAAY,IAAI;AAChB,gBAAgB,QAAQ,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,EAAE;AACtD,aAAa;AACb,oBAAoB;AACpB,gBAAgB,EAAE,KAAK,CAAC,OAAO,CAAC;AAChC,gBAAgB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AAClC,gBAAgB,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;AACtD,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,IAAI,YAAY,GAAG,IAAI,GAAG,EAAE,CAAC;AACrC,QAAQ,IAAI,cAAc,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAU7C,YAAY,IAAI,CAAC,gBAAgB,CAACrC,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,cAAc,EAAE,UAAU,KAAK,EAAE;AACrG,oBAAoB,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5C,oBAAoB,OAAO,KAAK,CAAC;AACjC,iBAAiB,EAAE,CAAC,CAAC,CAAC;AACtB,SAAS;AACT,QAAQ,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;AAI5C,YAAY,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACpF,SAAS;AACT,aAAa,IAAI,UAAU,KAAK,KAAK,EAAE;AAMvC,YAAY,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/B,SAAS;AACT,aAAa;AAGb,YAAY,OAAO,EAAE,CAAC;AACtB,SAAS;AACT,QAAQ,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;AAClD,YAAY,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;AACpF,SAAS;AAIT,QAAQ,IAAI,cAAc,IAAI,YAAY,CAAC,IAAI,EAAE;AACjD,YAAY,IAAI,CAAC,gBAAgB,CAACA,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE,cAAc,EAAE,UAAU,KAAK,EAAE,IAAI,EAAE;AAC3G,oBAAoB,IAAI,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACxE,oBAAoB,IAAI,MAAM,KAAK,KAAK,EAAE;AAI1C,wBAAwB,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACnD,qBAAqB;AACrB,oBAAoB,OAAO,MAAM,CAAC;AAClC,iBAAiB,EAAE,CAAC,CAAC,CAAC;AAGtB,YAAY,IAAI,YAAY,CAAC,IAAI,EAAE;AACnC,gBAAgB,YAAY,CAAC,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,OAAO,KAAK,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AAC1G,aAAa;AACb,SAAS;AACT,aAAa;AAIb,YAAY,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC3C,SAAS;AACT,QAAQ,OAAO,YAAY,CAAC;AAC5B,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,kBAAkB,GAAG,UAAU,MAAM,EAAE,YAAY,EAAE;AACjF,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC;AAC1B,YAAY,MAAM,EAAE,MAAM;AAC1B,YAAY,UAAU,EAAE,YAAY,IAAI,YAAY,KAAK,IAAI;AAC7D,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,QAAQ,EAAE;AACpE,QAAQ,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjF,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,eAAe,GAAG,UAAU,QAAQ,EAAE,QAAQ,EAAE;AAC5E,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACjE,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,YAAY,EAAE;AACrE,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,OAAO,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,MAAM,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC;AACnH,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,gBAAgB,GAAG,UAAU,OAAO,EAAE;AAClE,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC3B,YAAY,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,OAAO,KAAK,CAAC,mBAAmB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;AACjG,SAAS;AACT,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,sBAAsB,GAAG,UAAU,QAAQ,EAAE;AACzE,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;AAC9C,QAAQ,OAAO,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,QAAQ,CAAC;AACpE,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,SAAS,CAAC,qBAAqB,GAAG,UAAU,QAAQ,EAAE;AACxE,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE;AAC9B,YAAY,OAAO,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AACzE,SAAS;AACT,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK,CAAC;AAON,IAAI,aAAa,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,CAAC,EAAE,OAAO,EAAE;AACnE,QAAQ,IAAI,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;AAOlC,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAChC,QAAQ,IAAI,OAAO,EAAE;AACrB,YAAY,IAAI,CAAC,CAAC,UAAU,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,EAAE;AACxE,gBAAgB,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;AACtD,aAAa;AACb,YAAY,IAAI,OAAO,CAAC,cAAc;AACtC,gBAAgB,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ,CAAC,KAAK,KAAK,EAAE;AAGhF,gBAAgB,OAAO;AACvB,aAAa;AACb,SAAS;AACT,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAACG,WAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;AAC/D,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,GAAG,IAAI,GAAG,QAAQ,CAAC,CAAC;AACtD,SAAS;AACT,KAAK,CAAC;AACN,IAAI,OAAO,aAAa,CAAC;AACzB,CAAC,CAAC,WAAW,CAAC,EAAE;AAEhB,IAAI,UAAU,CAAC,OAAO,KAAK,KAAK,EAAE;AAClC,IAAI,aAAa,CAAC,SAAS,CAAC,kBAAkB,GAAG,+BAA+B,CAAC;AACjF;;ACldO,SAAS,sBAAsB,GAAG;AACzC,IAAI,IAAI,SAAS,GAAG,EAAE,CAAC;AACvB,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAClD,QAAQ,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,OAAO,KAAK,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAEF,mBAAa,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC;AAC5G,CAAC;AACD,IAAI,gBAAgB,KAAkB,YAAY;AAIlD,IAAI,SAAS,gBAAgB,GAAG;AAChC,QAAQ,IAAI,SAAS,GAAG,EAAE,CAAC;AAC3B,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AAC1C,SAAS;AACT,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC5C,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3B,QAAQ,IAAI,SAAS,CAAC,MAAM,EAAE;AAC9B,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACjD,SAAS;AACT,KAAK;AACL,IAAI,gBAAgB,CAAC,SAAS,CAAC,QAAQ,GAAG,YAAY;AACtD,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,SAAS,GAAG,EAAE,CAAC;AAC3B,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,SAAS,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACtD,YAAY,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;AAC1C,SAAS;AACT,QAAQ,IAAI,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;AACpC,QAAQ,SAAS,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AACzC,YAAYK,gCAAsB,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AAChE,gBAAgB,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACvD,aAAa,CAAC,CAAC;AACf,SAAS,CAAC,CAAC;AACX,QAAQ,WAAW,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,IAAI,EAAE;AAClD,YAAY,IAAI,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC/C,gBAAgB,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAC5C,gBAAgB,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACvC,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AAEN,IAAI,gBAAgB,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,IAAI,EAAE,GAAG,CAAC;AAChE,IAAI,gBAAgB,CAAC,SAAS,CAAC,WAAW,GAAG,YAAY;AACzD,QAAQ,IAAI,KAAK,GAAG,gBAAgB,CAAC,SAAS,CAAC;AAC/C,QAAQ,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,MAAM,GAAGK,aAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACvE,YAAY,YAAY,EAAE,UAAU,GAAG,EAAE,EAAE,OAAO,GAAG,CAAC,EAAE;AACxD,YAAY,GAAG,EAAEE,oBAAU,CAAC,yBAAyB,CAAC;AACtD,gBAAgB,IAAI;AACpB,SAAS,CAAC,EAAE,KAAK,CAAC;AAClB,QAAQ,IAAI,CAAC,SAAS,GAAGF,aAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC1D,YAAY,KAAK,EAAEG,gBAAS;AAC5B,YAAY,GAAG,EAAED,oBAAU,CAAC,4BAA4B,CAAC;AACzD,gBAAgB,IAAI;AACpB,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,CAAC,mBAAmB,GAAGF,aAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC9E,YAAY,KAAK,EAAEG,gBAAS;AAC5B,YAAY,GAAG,EAAED,oBAAU,CAAC,sCAAsC,CAAC;AACnE,gBAAgB,IAAI;AACpB,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AAMN,IAAI,gBAAgB,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,YAAY,EAAE;AAChE,QAAQ,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC;AACnD,KAAK,CAAC;AACN,IAAI,gBAAgB,CAAC,SAAS,CAAC,SAAS,GAAG,UAAU,QAAQ,EAAE;AAC/D,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAChC,QAAQP,gCAAsB,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,GAAG,EAAE;AAChE,YAAY,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC7C,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC;AAChC,QAAQ,IAAI,OAAO,GAAG,UAAU,UAAU,EAAE;AAC5C,YAAY,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AAC1C,gBAAgB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACxC,aAAa;AACb,SAAS,CAAC;AACV,QAAQ,IAAI,mBAAmB,GAAG,UAAU,IAAI,EAAE;AAClD,YAAY,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACjF,SAAS,CAAC;AACV,QAAQ,mBAAmB,CAAC,QAAQ,CAAC,CAAC;AACtC,QAAQ,IAAI,OAAO,GAAG,EAAE,CAAC;AACzB,QAAQ,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAGtC,QAAQ,OAAO,CAAC,OAAO,CAAC,UAAU,YAAY,EAAE;AAChD,YAAY,IAAI,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC7D,YAAY,IAAI,gBAAgB,EAAE;AAClC,gBAAgB,mBAAmB,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,gBAAgB,EAAE,CAAC;AAC5E,aAAa;AACb,iBAAiB;AACjB,gBAAgB,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC3C,gBAAgB,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACrD,gBAAgB,IAAI,GAAG,EAAE;AACzB,oBAAoB,mBAAmB,EAAE,GAAG,CAAC,YAAY,CAAC,GAAG,GAAG,EAAE,CAAC;AACnE,iBAAiB;AACjB,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;AAC5B,YAAY,IAAI,cAAc,GAAG,EAAE,CAAC;AACpC,YAAY,OAAO,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;AAC5C,gBAAgB,IAAI,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACpC,gBAAgB,IAAI,GAAG,EAAE;AACzB,oBAAoB,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7C,iBAAiB;AACjB,aAAa,CAAC,CAAC;AACf,YAAY,IAAI,cAAc,CAAC,MAAM,EAAE;AACvC,gBAAgB,QAAQ,GAAGN,cAAQ,CAACA,cAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;AAC1H,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,QAAQ,CAAC;AACxB,KAAK,CAAC;AACN,IAAI,gBAAgB,CAAC,SAAS,CAAC,mBAAmB,GAAG,UAAU,IAAI,EAAE;AACrE,QAAQ,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC1C,QAAQqD,aAAK,CAAC,IAAI,EAAE;AACpB,YAAY,cAAc,EAAE,UAAU,IAAI,EAAE;AAC5C,gBAAgB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAChD,aAAa;AACb,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,OAAO,CAAC;AACvB,KAAK,CAAC;AACN,IAAI,OAAO,gBAAgB,CAAC;AAC5B,CAAC,EAAE,CAAC;;;;;;;;;;;;;;;"}