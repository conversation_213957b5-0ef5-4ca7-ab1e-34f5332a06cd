{"name": "@apollo/client", "version": "3.13.8", "description": "A fully-featured caching GraphQL client.", "private": false, "keywords": ["apollo", "graphql", "react", "hooks", "client", "cache"], "author": "<EMAIL>", "license": "MIT", "main": "./main.cjs", "module": "./index.js", "types": "./index.d.ts", "sideEffects": false, "react-native": {"./dist/cache/inmemory/fixPolyfills.js": "./cache/inmemory/fixPolyfills.native.js", "react-dom/server": false}, "repository": {"type": "git", "url": "git+https://github.com/apollographql/apollo-client.git"}, "bugs": {"url": "https://github.com/apollographql/apollo-client/issues"}, "homepage": "https://www.apollographql.com/docs/react/", "peerDependencies": {"graphql": "^15.0.0 || ^16.0.0", "graphql-ws": "^5.5.5 || ^6.0.3", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0 || >=19.0.0-rc", "subscriptions-transport-ws": "^0.9.0 || ^0.11.0"}, "peerDependenciesMeta": {"graphql-ws": {"optional": true}, "react": {"optional": true}, "react-dom": {"optional": true}, "subscriptions-transport-ws": {"optional": true}}, "dependencies": {"@graphql-typed-document-node/core": "^3.1.1", "@wry/caches": "^1.0.0", "@wry/equality": "^0.5.6", "@wry/trie": "^0.5.0", "graphql-tag": "^2.12.6", "hoist-non-react-statics": "^3.3.2", "optimism": "^0.18.0", "prop-types": "^15.7.2", "rehackt": "^0.1.0", "symbol-observable": "^4.0.0", "ts-invariant": "^0.10.3", "tslib": "^2.3.0", "zen-observable-ts": "^1.2.5"}, "devDependencies": {"@actions/github-script": "github:actions/github-script#v7.0.1", "@arethetypeswrong/cli": "0.15.3", "@ark/attest": "0.28.0", "@babel/parser": "7.25.0", "@changesets/changelog-github": "0.5.0", "@changesets/cli": "2.27.7", "@eslint/compat": "1.2.5", "@eslint/eslintrc": "3.2.0", "@eslint/js": "9.18.0", "@graphql-tools/merge": "9.0.4", "@graphql-tools/schema": "10.0.4", "@graphql-tools/utils": "10.5.0", "@microsoft/api-extractor": "7.49.1", "@rollup/plugin-node-resolve": "11.2.1", "@size-limit/esbuild-why": "11.1.4", "@size-limit/preset-small-lib": "11.1.4", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.1.0", "@testing-library/react-render-stream": "2.0.0", "@testing-library/user-event": "14.5.2", "@tsconfig/node20": "20.1.4", "@types/bytes": "3.1.4", "@types/fetch-mock": "7.3.8", "@types/glob": "8.1.0", "@types/hoist-non-react-statics": "3.3.5", "@types/jest": "29.5.12", "@types/lodash": "4.17.7", "@types/node": "22.10.7", "@types/node-fetch": "2.6.11", "@types/prop-types": "15.7.14", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@types/relay-runtime": "14.1.24", "@types/use-sync-external-store": "0.0.6", "@typescript-eslint/eslint-plugin": "8.21.0", "@typescript-eslint/parser": "8.21.0", "@typescript-eslint/rule-tester": "8.21.0", "@typescript-eslint/types": "8.21.0", "@typescript-eslint/utils": "8.21.0", "acorn": "8.12.1", "ajv": "8.17.1", "blob-polyfill": "7.0.20220408", "bytes": "3.1.2", "cross-fetch": "4.0.0", "eslint": "9.18.0", "eslint-import-resolver-typescript": "3.7.0", "eslint-plugin-import": "npm:@phryneas/eslint-plugin-import@2.27.5-pr.2813.2817.199971c", "eslint-plugin-local-rules": "3.0.2", "eslint-plugin-react-compiler": "19.0.0-beta-decd7b8-20250118", "eslint-plugin-react-hooks": "5.1.0", "eslint-plugin-testing-library": "7.1.1", "expect-type": "1.1.0", "fetch-mock": "9.11.0", "glob": "8.1.0", "globals": "15.14.0", "graphql": "16.9.0", "graphql-17-alpha2": "npm:graphql@17.0.0-alpha.2", "graphql-ws": "6.0.3", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "jest-junit": "16.0.0", "lodash": "4.17.21", "patch-package": "8.0.0", "pkg-pr-new": "0.0.24", "prettier": "3.1.1", "react": "19.0.0", "react-17": "npm:react@^17", "react-18": "npm:react@^18", "react-dom": "19.0.0", "react-dom-17": "npm:react-dom@^17", "react-dom-18": "npm:react-dom@^18", "react-error-boundary": "4.0.13", "recast": "0.23.9", "resolve": "1.22.8", "rimraf": "5.0.9", "rollup": "2.79.2", "rollup-plugin-cleanup": "3.2.1", "rollup-plugin-terser": "7.0.2", "rxjs": "7.8.1", "size-limit": "11.1.4", "subscriptions-transport-ws": "0.11.0", "terser": "5.31.3", "ts-api-utils": "2.0.0", "ts-jest": "29.2.3", "ts-jest-resolver": "2.0.1", "ts-morph": "25.0.0", "ts-node": "10.9.2", "tsx": "4.19.2", "typedoc": "0.25.0", "typescript": "5.7.3", "web-streams-polyfill": "4.0.0", "whatwg-fetch": "3.6.20"}, "publishConfig": {"access": "public"}, "files": ["LICENSE", "**/*.md", "**/*.cjs", "**/*.cjs.map", "**/*.d.cts", "**/*.js", "**/*.js.map", "**/*.d.ts", "**/*.json"], "overrides": {"pretty-format": "^29.7.0", "@testing-library/dom": "$@testing-library/dom"}, "type": "module"}