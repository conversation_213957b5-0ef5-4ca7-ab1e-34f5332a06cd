// Original file: proto/channelz.proto

import type { Long } from '@grpc/proto-loader';

export interface GetSocketRequest {
  /**
   * socket_id is the identifier of the specific socket to get.
   */
  'socket_id'?: (number | string | Long);
  /**
   * If true, the response will contain only high level information
   * that is inexpensive to obtain. Fields thay may be omitted are
   * documented.
   */
  'summary'?: (boolean);
}

export interface GetSocketRequest__Output {
  /**
   * socket_id is the identifier of the specific socket to get.
   */
  'socket_id': (string);
  /**
   * If true, the response will contain only high level information
   * that is inexpensive to obtain. Fields thay may be omitted are
   * documented.
   */
  'summary': (boolean);
}
