{"version": 3, "file": "call-credentials.js", "sourceRoot": "", "sources": ["../../src/call-credentials.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,yCAAsC;AA+BtC,SAAS,qBAAqB,CAC5B,MAAoB;IAEpB,OAAO,CACL,mBAAmB,IAAI,MAAM;QAC7B,OAAO,MAAM,CAAC,iBAAiB,KAAK,UAAU,CAC/C,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,MAAsB,eAAe;IAsBnC;;;;;;OAMG;IACH,MAAM,CAAC,2BAA2B,CAChC,iBAAwC;QAExC,OAAO,IAAI,qBAAqB,CAAC,iBAAiB,CAAC,CAAC;IACtD,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,0BAA0B,CAC/B,iBAA+B;QAE/B,OAAO,eAAe,CAAC,2BAA2B,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YACvE,IAAI,UAAgD,CAAC;YACrD,IAAI,qBAAqB,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAC7C,UAAU,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;YACxE,CAAC;iBAAM,CAAC;gBACN,UAAU,GAAG,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC3C,iBAAiB,CAAC,kBAAkB,CAClC,OAAO,CAAC,WAAW,EACnB,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;wBACf,IAAI,GAAG,EAAE,CAAC;4BACR,MAAM,CAAC,GAAG,CAAC,CAAC;4BACZ,OAAO;wBACT,CAAC;wBACD,IAAI,CAAC,OAAO,EAAE,CAAC;4BACb,MAAM,CAAC,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC,CAAC;4BACxD,OAAO;wBACT,CAAC;wBACD,OAAO,CAAC,OAAO,CAAC,CAAC;oBACnB,CAAC,CACF,CAAC;gBACJ,CAAC,CAAC,CAAC;YACL,CAAC;YACD,UAAU,CAAC,IAAI,CACb,OAAO,CAAC,EAAE;gBACR,MAAM,QAAQ,GAAG,IAAI,mBAAQ,EAAE,CAAC;gBAChC,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;oBACvC,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;gBAClC,CAAC;gBACD,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC3B,CAAC,EACD,GAAG,CAAC,EAAE;gBACJ,QAAQ,CAAC,GAAG,CAAC,CAAC;YAChB,CAAC,CACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,WAAW;QAChB,OAAO,IAAI,oBAAoB,EAAE,CAAC;IACpC,CAAC;CACF;AAnFD,0CAmFC;AAED,MAAM,uBAAwB,SAAQ,eAAe;IACnD,YAAoB,KAAwB;QAC1C,KAAK,EAAE,CAAC;QADU,UAAK,GAAL,KAAK,CAAmB;IAE5C,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAA4B;QACjD,MAAM,IAAI,GAAa,IAAI,mBAAQ,EAAE,CAAC;QACtC,MAAM,SAAS,GAAe,MAAM,OAAO,CAAC,GAAG,CAC7C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CACvD,CAAC;QACF,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CAAC,KAAsB;QAC5B,OAAO,IAAI,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAED,OAAO,CAAC,KAAsB;QAC5B,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,KAAK,YAAY,uBAAuB,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CACvC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAClC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAED,MAAM,qBAAsB,SAAQ,eAAe;IACjD,YAAoB,iBAAwC;QAC1D,KAAK,EAAE,CAAC;QADU,sBAAiB,GAAjB,iBAAiB,CAAuB;IAE5D,CAAC;IAED,gBAAgB,CAAC,OAA4B;QAC3C,OAAO,IAAI,OAAO,CAAW,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC/C,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;gBAChD,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBAC3B,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACpB,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,CAAC,KAAsB;QAC5B,OAAO,IAAI,uBAAuB,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,OAAO,CAAC,KAAsB;QAC5B,IAAI,IAAI,KAAK,KAAK,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,KAAK,YAAY,qBAAqB,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC,iBAAiB,KAAK,KAAK,CAAC,iBAAiB,CAAC;QAC5D,CAAC;aAAM,CAAC;YACN,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAED,MAAM,oBAAqB,SAAQ,eAAe;IAChD,gBAAgB,CAAC,OAA4B;QAC3C,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,mBAAQ,EAAE,CAAC,CAAC;IACzC,CAAC;IAED,OAAO,CAAC,KAAsB;QAC5B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CAAC,KAAsB;QAC5B,OAAO,KAAK,YAAY,oBAAoB,CAAC;IAC/C,CAAC;CACF"}