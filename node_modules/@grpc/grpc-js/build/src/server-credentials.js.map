{"version": 3, "file": "server-credentials.js", "sourceRoot": "", "sources": ["../../src/server-credentials.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAGH,+CAAmE;AAOnE,MAAsB,iBAAiB;IAIrC,MAAM,CAAC,cAAc;QACnB,OAAO,IAAI,yBAAyB,EAAE,CAAC;IACzC,CAAC;IAED,MAAM,CAAC,SAAS,CACd,SAAwB,EACxB,YAA2B,EAC3B,sBAAsB,GAAG,KAAK;QAE9B,IAAI,SAAS,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,SAAS,CAAC,+BAA+B,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,OAAO,sBAAsB,KAAK,SAAS,EAAE,CAAC;YAChD,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,MAAM,GAAG,GAAG,EAAE,CAAC;QAEf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,MAAM,IAAI,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;YAE7B,IAAI,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC9C,MAAM,IAAI,SAAS,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC;YAC7D,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,SAAS,CAAC,eAAe,CAAC,gCAAgC,CAAC,CAAC;YACxE,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,SAAS,CAAC,eAAe,CAAC,+BAA+B,CAAC,CAAC;YACvE,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,IAAI,uBAAuB,CAAC;YACjC,EAAE,EAAE,SAAS,IAAI,IAAA,iCAAmB,GAAE,IAAI,SAAS;YACnD,IAAI;YACJ,GAAG;YACH,WAAW,EAAE,sBAAsB;YACnC,OAAO,EAAE,2BAAa;SACvB,CAAC,CAAC;IACL,CAAC;CACF;AAvDD,8CAuDC;AAED,MAAM,yBAA0B,SAAQ,iBAAiB;IACvD,SAAS;QACP,OAAO,KAAK,CAAC;IACf,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAED,MAAM,uBAAwB,SAAQ,iBAAiB;IAGrD,YAAY,OAA4B;QACtC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC;IACd,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;CACF"}