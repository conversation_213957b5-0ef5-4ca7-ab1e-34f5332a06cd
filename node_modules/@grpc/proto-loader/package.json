{"name": "@grpc/proto-loader", "version": "0.7.15", "author": "Google Inc.", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "gRPC utility library for loading .proto files", "homepage": "https://grpc.io/", "main": "build/src/index.js", "typings": "build/src/index.d.ts", "scripts": {"build": "npm run compile", "clean": "rimraf ./build", "compile": "tsc -p .", "format": "clang-format -i -style=\"{Language: JavaScript, BasedOnStyle: Google, ColumnLimit: 80}\" src/*.ts test/*.ts", "lint": "tslint -c node_modules/google-ts-style/tslint.json -p . -t codeFrame --type-check", "prepare": "npm run compile", "test": "gulp test", "check": "gts check", "fix": "gts fix", "pretest": "npm run compile", "posttest": "npm run check", "generate-golden": "node ./build/bin/proto-loader-gen-types.js --keepCase --longs=String --enums=String --defaults --oneofs --json --includeComments --inputTemplate=I%s --outputTemplate=O%s -I deps/gapic-showcase/schema/ deps/googleapis/ -O ./golden-generated --grpcLib @grpc/grpc-js google/showcase/v1beta1/echo.proto", "validate-golden": "rm -rf ./golden-generated-old && mv ./golden-generated/ ./golden-generated-old && npm run generate-golden && diff -rb ./golden-generated ./golden-generated-old"}, "repository": {"type": "git", "url": "https://github.com/grpc/grpc-node.git"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/grpc/grpc-node/issues"}, "files": ["LICENSE", "build/src/*.d.ts", "build/src/*.{js,js.map}", "build/bin/*.{js,js.map}"], "bin": {"proto-loader-gen-types": "./build/bin/proto-loader-gen-types.js"}, "dependencies": {"lodash.camelcase": "^4.3.0", "long": "^5.0.0", "protobufjs": "^7.2.5", "yargs": "^17.7.2"}, "devDependencies": {"@types/lodash.camelcase": "^4.3.4", "@types/mkdirp": "^1.0.1", "@types/mocha": "^5.2.7", "@types/node": "^10.17.26", "@types/yargs": "^17.0.24", "clang-format": "^1.2.2", "google-proto-files": "^3.0.2", "gts": "^3.1.0", "rimraf": "^3.0.2", "ts-node": "^10.9.2", "typescript": "~4.7.4"}, "engines": {"node": ">=6"}}