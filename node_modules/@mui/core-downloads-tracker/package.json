{"name": "@mui/core-downloads-tracker", "version": "7.1.1", "author": "MUI Team", "description": "Internal package to track number of downloads of our design system libraries.", "files": [], "repository": {"type": "git", "url": "git+https://github.com/mui/material-ui.git", "directory": "packages/core-downloads-tracker"}, "license": "MIT", "bugs": {"url": "https://github.com/mui/material-ui/issues"}, "homepage": "https://mui.com/", "funding": {"type": "opencollective", "url": "https://opencollective.com/mui-org"}, "publishConfig": {"access": "public", "directory": "build"}, "private": false, "exports": {"./package.json": "./package.json", ".": {"require": {"types": "./index.d.ts", "default": "./index.js"}, "import": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}}, "./*": {"require": {"types": "./*/index.d.ts", "default": "./*/index.js"}, "import": {"types": "./esm/*/index.d.ts", "default": "./esm/*/index.js"}}, "./esm": null, "./modern": null}}