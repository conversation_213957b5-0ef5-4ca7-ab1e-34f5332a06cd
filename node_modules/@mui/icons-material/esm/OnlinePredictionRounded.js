"use client";

import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon(/*#__PURE__*/_jsx("path", {
  d: "M15.5 11.5c0 2-2.5 3.5-2.5 5h-2c0-1.5-2.5-3-2.5-5C8.5 9.57 10.07 8 12 8s3.5 1.57 3.5 3.5m-2.5 6h-2v.5c0 .55.45 1 1 1s1-.45 1-1zm9-5.5c0-2.46-.89-4.71-2.36-6.45-.29-.34-.8-.38-1.12-.06-.27.27-.3.71-.06 1C19.73 7.97 20.5 9.9 20.5 12s-.77 4.03-2.04 5.52c-.25.29-.21.73.06 1 .32.32.83.28 1.12-.06 1.47-1.75 2.36-4 2.36-6.46M3.5 12c0-2.1.77-4.03 2.04-5.52.25-.29.21-.73-.06-1-.31-.31-.83-.28-1.12.06C2.89 7.29 2 9.54 2 12s.89 4.71 2.36 6.46c.29.34.8.38 1.12.06.27-.27.3-.71.06-1C4.27 16.03 3.5 14.1 3.5 12m14 0c0 1.28-.44 2.47-1.18 3.41-.23.29-.2.71.07.98.32.32.85.29 1.13-.07C18.44 15.13 19 13.63 19 12s-.56-3.13-1.49-4.31c-.28-.36-.81-.39-1.13-.07-.26.26-.3.68-.07.98.75.93 1.19 2.12 1.19 3.4m-9.88 4.38c.26-.26.3-.68.07-.98-.75-.93-1.19-2.12-1.19-3.4s.44-2.47 1.18-3.41c.23-.29.2-.71-.07-.98-.31-.31-.84-.28-1.12.07C5.56 8.87 5 10.37 5 12s.56 3.13 1.49 4.32c.28.35.81.38 1.13.06"
}), 'OnlinePredictionRounded');