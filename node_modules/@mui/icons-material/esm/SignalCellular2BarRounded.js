import * as React from 'react';
import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export default createSvgIcon(/*#__PURE__*/_jsxs(React.Fragment, {
  children: [/*#__PURE__*/_jsx("path", {
    fillOpacity: ".3",
    d: "M4.41 22H20c1.1 0 2-.9 2-2V4.41c0-.89-1.08-1.34-1.71-.71L3.71 20.29c-.63.63-.19 1.71.7 1.71z"
  }), /*#__PURE__*/_jsx("path", {
    d: "M14 10L3.71 20.29c-.63.63-.19 1.71.7 1.71H14V10z"
  })]
}), 'SignalCellular2BarRounded');