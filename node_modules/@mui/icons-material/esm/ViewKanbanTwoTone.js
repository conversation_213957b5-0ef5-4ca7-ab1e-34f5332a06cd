"use client";

import createSvgIcon from "./utils/createSvgIcon.js";
import { jsx as _jsx } from "react/jsx-runtime";
export default createSvgIcon([/*#__PURE__*/_jsx("path", {
  d: "M5 19h14V5H5zM15 7h2v8h-2zm-4 0h2v5h-2zM7 7h2v10H7z",
  opacity: ".3"
}, "0"), /*#__PURE__*/_jsx("path", {
  d: "M7 7h2v10H7zm4 0h2v5h-2zm4 0h2v8h-2z"
}, "1"), /*#__PURE__*/_jsx("path", {
  d: "M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2m0 16H5V5h14z"
}, "2")], 'ViewKanbanTwoTone');