{"version": 3, "file": "index.esm2017.js", "sources": ["../../src/constants.ts", "../../src/utils/errors.ts", "../../src/utils/console_logger.ts", "../../src/services/api_service.ts", "../../src/services/iid_service.ts", "../../src/utils/string_merger.ts", "../../src/services/settings_service.ts", "../../src/utils/attributes_utils.ts", "../../src/utils/app_utils.ts", "../../src/services/remote_config_service.ts", "../../src/services/initialization_service.ts", "../../src/services/transport_service.ts", "../../src/services/perf_logger.ts", "../../src/resources/network_request.ts", "../../src/utils/metric_utils.ts", "../../src/resources/trace.ts", "../../src/services/oob_resources_service.ts", "../../src/controllers/perf.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../package.json';\n\nexport const SDK_VERSION = version;\n/** The prefix for start User Timing marks used for creating Traces. */\nexport const TRACE_START_MARK_PREFIX = 'FB-PERF-TRACE-START';\n/** The prefix for stop User Timing marks used for creating Traces. */\nexport const TRACE_STOP_MARK_PREFIX = 'FB-PERF-TRACE-STOP';\n/** The prefix for User Timing measure used for creating Traces. */\nexport const TRACE_MEASURE_PREFIX = 'FB-PERF-TRACE-MEASURE';\n/** The prefix for out of the box page load Trace name. */\nexport const OOB_TRACE_PAGE_LOAD_PREFIX = '_wt_';\n\nexport const FIRST_PAINT_COUNTER_NAME = '_fp';\n\nexport const FIRST_CONTENTFUL_PAINT_COUNTER_NAME = '_fcp';\n\nexport const FIRST_INPUT_DELAY_COUNTER_NAME = '_fid';\n\nexport const LARGEST_CONTENTFUL_PAINT_METRIC_NAME = '_lcp';\nexport const LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME = 'lcp_element';\n\nexport const INTERACTION_TO_NEXT_PAINT_METRIC_NAME = '_inp';\nexport const INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME = 'inp_interactionTarget';\n\nexport const CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME = '_cls';\nexport const CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME = 'cls_largestShiftTarget';\n\nexport const CONFIG_LOCAL_STORAGE_KEY = '@firebase/performance/config';\n\nexport const CONFIG_EXPIRY_LOCAL_STORAGE_KEY =\n  '@firebase/performance/configexpire';\n\nexport const SERVICE = 'performance';\nexport const SERVICE_NAME = 'Performance';\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from '../constants';\n\nexport const enum ErrorCode {\n  TRACE_STARTED_BEFORE = 'trace started',\n  TRACE_STOPPED_BEFORE = 'trace stopped',\n  NONPOSITIVE_TRACE_START_TIME = 'nonpositive trace startTime',\n  NONPOSITIVE_TRACE_DURATION = 'nonpositive trace duration',\n  NO_WINDOW = 'no window',\n  NO_APP_ID = 'no app id',\n  NO_PROJECT_ID = 'no project id',\n  NO_API_KEY = 'no api key',\n  INVALID_CC_LOG = 'invalid cc log',\n  FB_NOT_DEFAULT = 'FB not default',\n  RC_NOT_OK = 'RC response not ok',\n  INVALID_ATTRIBUTE_NAME = 'invalid attribute name',\n  INVALID_ATTRIBUTE_VALUE = 'invalid attribute value',\n  INVALID_CUSTOM_METRIC_NAME = 'invalid custom metric name',\n  INVALID_STRING_MERGER_PARAMETER = 'invalid String merger input',\n  ALREADY_INITIALIZED = 'already initialized'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.TRACE_STARTED_BEFORE]: 'Trace {$traceName} was started before.',\n  [ErrorCode.TRACE_STOPPED_BEFORE]: 'Trace {$traceName} is not running.',\n  [ErrorCode.NONPOSITIVE_TRACE_START_TIME]:\n    'Trace {$traceName} startTime should be positive.',\n  [ErrorCode.NONPOSITIVE_TRACE_DURATION]:\n    'Trace {$traceName} duration should be positive.',\n  [ErrorCode.NO_WINDOW]: 'Window is not available.',\n  [ErrorCode.NO_APP_ID]: 'App id is not available.',\n  [ErrorCode.NO_PROJECT_ID]: 'Project id is not available.',\n  [ErrorCode.NO_API_KEY]: 'Api key is not available.',\n  [ErrorCode.INVALID_CC_LOG]: 'Attempted to queue invalid cc event',\n  [ErrorCode.FB_NOT_DEFAULT]:\n    'Performance can only start when Firebase app instance is the default one.',\n  [ErrorCode.RC_NOT_OK]: 'RC response is not ok',\n  [ErrorCode.INVALID_ATTRIBUTE_NAME]:\n    'Attribute name {$attributeName} is invalid.',\n  [ErrorCode.INVALID_ATTRIBUTE_VALUE]:\n    'Attribute value {$attributeValue} is invalid.',\n  [ErrorCode.INVALID_CUSTOM_METRIC_NAME]:\n    'Custom metric name {$customMetricName} is invalid',\n  [ErrorCode.INVALID_STRING_MERGER_PARAMETER]:\n    'Input for String merger is invalid, contact support team to resolve.',\n  [ErrorCode.ALREADY_INITIALIZED]:\n    'initializePerformance() has already been called with ' +\n    'different options. To avoid this error, call initializePerformance() with the ' +\n    'same options as when it was originally called, or call getPerformance() to return the' +\n    ' already initialized instance.'\n};\n\ninterface ErrorParams {\n  [ErrorCode.TRACE_STARTED_BEFORE]: { traceName: string };\n  [ErrorCode.TRACE_STOPPED_BEFORE]: { traceName: string };\n  [ErrorCode.NONPOSITIVE_TRACE_START_TIME]: { traceName: string };\n  [ErrorCode.NONPOSITIVE_TRACE_DURATION]: { traceName: string };\n  [ErrorCode.INVALID_ATTRIBUTE_NAME]: { attributeName: string };\n  [ErrorCode.INVALID_ATTRIBUTE_VALUE]: { attributeValue: string };\n  [ErrorCode.INVALID_CUSTOM_METRIC_NAME]: { customMetricName: string };\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger, LogLevel } from '@firebase/logger';\nimport { SERVICE_NAME } from '../constants';\n\nexport const consoleLogger = new Logger(SERVICE_NAME);\nconsoleLogger.logLevel = LogLevel.INFO;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport { isIndexedDBAvailable, areCookiesEnabled } from '@firebase/util';\nimport { consoleLogger } from '../utils/console_logger';\nimport {\n  CLSMetricWithAttribution,\n  INPMetricWithAttribution,\n  LCPMetricWithAttribution,\n  onCLS as vitalsOnCLS,\n  onINP as vitalsOnINP,\n  onLCP as vitalsOnLCP\n} from 'web-vitals/attribution';\n\ndeclare global {\n  interface Window {\n    PerformanceObserver: typeof PerformanceObserver;\n    perfMetrics?: { onFirstInputDelay(fn: (fid: number) => void): void };\n  }\n}\n\nlet apiInstance: Api | undefined;\nlet windowInstance: Window | undefined;\n\nexport type EntryType =\n  | 'mark'\n  | 'measure'\n  | 'paint'\n  | 'resource'\n  | 'frame'\n  | 'navigation';\n\n/**\n * This class holds a reference to various browser related objects injected by\n * set methods.\n */\nexport class Api {\n  private readonly performance: Performance;\n  /** PerformanceObserver constructor function. */\n  private readonly PerformanceObserver: typeof PerformanceObserver;\n  private readonly windowLocation: Location;\n  readonly onFirstInputDelay?: (fn: (fid: number) => void) => void;\n  readonly onLCP: (fn: (metric: LCPMetricWithAttribution) => void) => void;\n  readonly onINP: (fn: (metric: INPMetricWithAttribution) => void) => void;\n  readonly onCLS: (fn: (metric: CLSMetricWithAttribution) => void) => void;\n  readonly localStorage?: Storage;\n  readonly document: Document;\n  readonly navigator: Navigator;\n\n  constructor(readonly window?: Window) {\n    if (!window) {\n      throw ERROR_FACTORY.create(ErrorCode.NO_WINDOW);\n    }\n    this.performance = window.performance;\n    this.PerformanceObserver = window.PerformanceObserver;\n    this.windowLocation = window.location;\n    this.navigator = window.navigator;\n    this.document = window.document;\n    if (this.navigator && this.navigator.cookieEnabled) {\n      // If user blocks cookies on the browser, accessing localStorage will\n      // throw an exception.\n      this.localStorage = window.localStorage;\n    }\n    if (window.perfMetrics && window.perfMetrics.onFirstInputDelay) {\n      this.onFirstInputDelay = window.perfMetrics.onFirstInputDelay;\n    }\n    this.onLCP = vitalsOnLCP;\n    this.onINP = vitalsOnINP;\n    this.onCLS = vitalsOnCLS;\n  }\n\n  getUrl(): string {\n    // Do not capture the string query part of url.\n    return this.windowLocation.href.split('?')[0];\n  }\n\n  mark(name: string): void {\n    if (!this.performance || !this.performance.mark) {\n      return;\n    }\n    this.performance.mark(name);\n  }\n\n  measure(measureName: string, mark1: string, mark2: string): void {\n    if (!this.performance || !this.performance.measure) {\n      return;\n    }\n    this.performance.measure(measureName, mark1, mark2);\n  }\n\n  getEntriesByType(type: EntryType): PerformanceEntry[] {\n    if (!this.performance || !this.performance.getEntriesByType) {\n      return [];\n    }\n    return this.performance.getEntriesByType(type);\n  }\n\n  getEntriesByName(name: string): PerformanceEntry[] {\n    if (!this.performance || !this.performance.getEntriesByName) {\n      return [];\n    }\n    return this.performance.getEntriesByName(name);\n  }\n\n  getTimeOrigin(): number {\n    // Polyfill the time origin with performance.timing.navigationStart.\n    return (\n      this.performance &&\n      (this.performance.timeOrigin || this.performance.timing.navigationStart)\n    );\n  }\n\n  requiredApisAvailable(): boolean {\n    if (!fetch || !Promise || !areCookiesEnabled()) {\n      consoleLogger.info(\n        'Firebase Performance cannot start if browser does not support fetch and Promise or cookie is disabled.'\n      );\n      return false;\n    }\n\n    if (!isIndexedDBAvailable()) {\n      consoleLogger.info('IndexedDB is not supported by current browser');\n      return false;\n    }\n    return true;\n  }\n\n  setupObserver(\n    entryType: EntryType,\n    callback: (entry: PerformanceEntry) => void\n  ): void {\n    if (!this.PerformanceObserver) {\n      return;\n    }\n    const observer = new this.PerformanceObserver(list => {\n      for (const entry of list.getEntries()) {\n        // `entry` is a PerformanceEntry instance.\n        callback(entry);\n      }\n    });\n\n    // Start observing the entry types you care about.\n    observer.observe({ entryTypes: [entryType] });\n  }\n\n  static getInstance(): Api {\n    if (apiInstance === undefined) {\n      apiInstance = new Api(windowInstance);\n    }\n    return apiInstance;\n  }\n}\n\nexport function setupApi(window: Window): void {\n  windowInstance = window;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\n\nlet iid: string | undefined;\nlet authToken: string | undefined;\n\nexport function getIidPromise(\n  installationsService: _FirebaseInstallationsInternal\n): Promise<string> {\n  const iidPromise = installationsService.getId();\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  iidPromise.then((iidVal: string) => {\n    iid = iidVal;\n  });\n  return iidPromise;\n}\n\n// This method should be used after the iid is retrieved by getIidPromise method.\nexport function getIid(): string | undefined {\n  return iid;\n}\n\nexport function getAuthTokenPromise(\n  installationsService: _FirebaseInstallationsInternal\n): Promise<string> {\n  const authTokenPromise = installationsService.getToken();\n  // eslint-disable-next-line @typescript-eslint/no-floating-promises\n  authTokenPromise.then((authTokenVal: string) => {\n    authToken = authTokenVal;\n  });\n  return authTokenPromise;\n}\n\nexport function getAuthenticationToken(): string | undefined {\n  return authToken;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './errors';\n\nexport function mergeStrings(part1: string, part2: string): string {\n  const sizeDiff = part1.length - part2.length;\n  if (sizeDiff < 0 || sizeDiff > 1) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_STRING_MERGER_PARAMETER);\n  }\n\n  const resultArray = [];\n  for (let i = 0; i < part1.length; i++) {\n    resultArray.push(part1.charAt(i));\n    if (part2.length > i) {\n      resultArray.push(part2.charAt(i));\n    }\n  }\n\n  return resultArray.join('');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { mergeStrings } from '../utils/string_merger';\n\nlet settingsServiceInstance: SettingsService | undefined;\n\nexport class SettingsService {\n  // The variable which controls logging of automatic traces and HTTP/S network monitoring.\n  instrumentationEnabled = true;\n\n  // The variable which controls logging of custom traces.\n  dataCollectionEnabled = true;\n\n  // Configuration flags set through remote config.\n  loggingEnabled = false;\n  // Sampling rate between 0 and 1.\n  tracesSamplingRate = 1;\n  networkRequestsSamplingRate = 1;\n\n  // Address of logging service.\n  logEndPointUrl =\n    'https://firebaselogging.googleapis.com/v0cc/log?format=json_proto';\n  // Performance event transport endpoint URL which should be compatible with proto3.\n  // New Address for transport service, not configurable via Remote Config.\n  flTransportEndpointUrl = mergeStrings(\n    'hts/frbslgigp.ogepscmv/ieo/eaylg',\n    'tp:/ieaeogn-agolai.o/1frlglgc/o'\n  );\n\n  transportKey = mergeStrings('AzSC8r6ReiGqFMyfvgow', 'Iayx0u-XT3vksVM-pIV');\n\n  // Source type for performance event logs.\n  logSource = 462;\n\n  // Flags which control per session logging of traces and network requests.\n  logTraceAfterSampling = false;\n  logNetworkAfterSampling = false;\n\n  // TTL of config retrieved from remote config in hours.\n  configTimeToLive = 12;\n\n  getFlTransportFullUrl(): string {\n    return this.flTransportEndpointUrl.concat('?key=', this.transportKey);\n  }\n\n  static getInstance(): SettingsService {\n    if (settingsServiceInstance === undefined) {\n      settingsServiceInstance = new SettingsService();\n    }\n    return settingsServiceInstance;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from '../services/api_service';\n\n// The values and orders of the following enums should not be changed.\nconst enum ServiceWorkerStatus {\n  UNKNOWN = 0,\n  UNSUPPORTED = 1,\n  CONTROLLED = 2,\n  UNCONTROLLED = 3\n}\n\nexport enum VisibilityState {\n  UNKNOWN = 0,\n  VISIBLE = 1,\n  HIDDEN = 2\n}\n\nconst enum EffectiveConnectionType {\n  UNKNOWN = 0,\n  CONNECTION_SLOW_2G = 1,\n  CONNECTION_2G = 2,\n  CONNECTION_3G = 3,\n  CONNECTION_4G = 4\n}\n\ntype ConnectionType =\n  | 'bluetooth'\n  | 'cellular'\n  | 'ethernet'\n  | 'mixed'\n  | 'none'\n  | 'other'\n  | 'unknown'\n  | 'wifi';\n\n/**\n * NetworkInformation\n * This API is not well supported in all major browsers, so TypeScript does not provide types for it.\n *\n * ref: https://developer.mozilla.org/en-US/docs/Web/API/NetworkInformation\n */\ninterface NetworkInformation extends EventTarget {\n  readonly type: ConnectionType;\n}\n\ninterface NetworkInformationWithEffectiveType extends NetworkInformation {\n  readonly effectiveType?: 'slow-2g' | '2g' | '3g' | '4g';\n}\n\ninterface NavigatorWithConnection extends Navigator {\n  readonly connection: NetworkInformationWithEffectiveType;\n}\n\nconst RESERVED_ATTRIBUTE_PREFIXES = ['firebase_', 'google_', 'ga_'];\nconst ATTRIBUTE_FORMAT_REGEX = new RegExp('^[a-zA-Z]\\\\w*$');\nconst MAX_ATTRIBUTE_NAME_LENGTH = 40;\nconst MAX_ATTRIBUTE_VALUE_LENGTH = 100;\n\nexport function getServiceWorkerStatus(): ServiceWorkerStatus {\n  const navigator = Api.getInstance().navigator;\n  if (navigator?.serviceWorker) {\n    if (navigator.serviceWorker.controller) {\n      return ServiceWorkerStatus.CONTROLLED;\n    } else {\n      return ServiceWorkerStatus.UNCONTROLLED;\n    }\n  } else {\n    return ServiceWorkerStatus.UNSUPPORTED;\n  }\n}\n\nexport function getVisibilityState(): VisibilityState {\n  const document = Api.getInstance().document;\n  const visibilityState = document.visibilityState;\n  switch (visibilityState) {\n    case 'visible':\n      return VisibilityState.VISIBLE;\n    case 'hidden':\n      return VisibilityState.HIDDEN;\n    default:\n      return VisibilityState.UNKNOWN;\n  }\n}\n\nexport function getEffectiveConnectionType(): EffectiveConnectionType {\n  const navigator = Api.getInstance().navigator;\n  const navigatorConnection = (navigator as NavigatorWithConnection).connection;\n  const effectiveType =\n    navigatorConnection && navigatorConnection.effectiveType;\n  switch (effectiveType) {\n    case 'slow-2g':\n      return EffectiveConnectionType.CONNECTION_SLOW_2G;\n    case '2g':\n      return EffectiveConnectionType.CONNECTION_2G;\n    case '3g':\n      return EffectiveConnectionType.CONNECTION_3G;\n    case '4g':\n      return EffectiveConnectionType.CONNECTION_4G;\n    default:\n      return EffectiveConnectionType.UNKNOWN;\n  }\n}\n\nexport function isValidCustomAttributeName(name: string): boolean {\n  if (name.length === 0 || name.length > MAX_ATTRIBUTE_NAME_LENGTH) {\n    return false;\n  }\n  const matchesReservedPrefix = RESERVED_ATTRIBUTE_PREFIXES.some(prefix =>\n    name.startsWith(prefix)\n  );\n  return !matchesReservedPrefix && !!name.match(ATTRIBUTE_FORMAT_REGEX);\n}\n\nexport function isValidCustomAttributeValue(value: string): boolean {\n  return value.length !== 0 && value.length <= MAX_ATTRIBUTE_VALUE_LENGTH;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ERROR_FACTORY, ErrorCode } from './errors';\nimport { FirebaseApp } from '@firebase/app';\n\nexport function getAppId(firebaseApp: FirebaseApp): string {\n  const appId = firebaseApp.options?.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_APP_ID);\n  }\n  return appId;\n}\n\nexport function getProjectId(firebaseApp: FirebaseApp): string {\n  const projectId = firebaseApp.options?.projectId;\n  if (!projectId) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_PROJECT_ID);\n  }\n  return projectId;\n}\n\nexport function getApiKey(firebaseApp: FirebaseApp): string {\n  const apiKey = firebaseApp.options?.apiKey;\n  if (!apiKey) {\n    throw ERROR_FACTORY.create(ErrorCode.NO_API_KEY);\n  }\n  return apiKey;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CONFIG_EXPIRY_LOCAL_STORAGE_KEY,\n  CONFIG_LOCAL_STORAGE_KEY,\n  SDK_VERSION\n} from '../constants';\nimport { consoleLogger } from '../utils/console_logger';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\n\nimport { Api } from './api_service';\nimport { getAuthTokenPromise } from './iid_service';\nimport { SettingsService } from './settings_service';\nimport { Performance<PERSON>ontroller } from '../controllers/perf';\nimport { getProjectId, getApiKey, getAppId } from '../utils/app_utils';\n\nconst REMOTE_CONFIG_SDK_VERSION = '0.0.1';\n\ninterface SecondaryConfig {\n  loggingEnabled?: boolean;\n  logSource?: number;\n  logEndPointUrl?: string;\n  transportKey?: string;\n  tracesSamplingRate?: number;\n  networkRequestsSamplingRate?: number;\n}\n\n// These values will be used if the remote config object is successfully\n// retrieved, but the template does not have these fields.\nconst DEFAULT_CONFIGS: SecondaryConfig = {\n  loggingEnabled: true\n};\n\n/* eslint-disable camelcase */\ninterface RemoteConfigTemplate {\n  fpr_enabled?: string;\n  fpr_log_source?: string;\n  fpr_log_endpoint_url?: string;\n  fpr_log_transport_key?: string;\n  fpr_log_transport_web_percent?: string;\n  fpr_vc_network_request_sampling_rate?: string;\n  fpr_vc_trace_sampling_rate?: string;\n  fpr_vc_session_sampling_rate?: string;\n}\n/* eslint-enable camelcase */\n\ninterface RemoteConfigResponse {\n  entries?: RemoteConfigTemplate;\n  state?: string;\n}\n\nconst FIS_AUTH_PREFIX = 'FIREBASE_INSTALLATIONS_AUTH';\n\nexport function getConfig(\n  performanceController: PerformanceController,\n  iid: string\n): Promise<void> {\n  const config = getStoredConfig();\n  if (config) {\n    processConfig(config);\n    return Promise.resolve();\n  }\n\n  return getRemoteConfig(performanceController, iid)\n    .then(processConfig)\n    .then(\n      config => storeConfig(config),\n      /** Do nothing for error, use defaults set in settings service. */\n      () => {}\n    );\n}\n\nfunction getStoredConfig(): RemoteConfigResponse | undefined {\n  const localStorage = Api.getInstance().localStorage;\n  if (!localStorage) {\n    return;\n  }\n  const expiryString = localStorage.getItem(CONFIG_EXPIRY_LOCAL_STORAGE_KEY);\n  if (!expiryString || !configValid(expiryString)) {\n    return;\n  }\n\n  const configStringified = localStorage.getItem(CONFIG_LOCAL_STORAGE_KEY);\n  if (!configStringified) {\n    return;\n  }\n  try {\n    const configResponse: RemoteConfigResponse = JSON.parse(configStringified);\n    return configResponse;\n  } catch {\n    return;\n  }\n}\n\nfunction storeConfig(config: RemoteConfigResponse | undefined): void {\n  const localStorage = Api.getInstance().localStorage;\n  if (!config || !localStorage) {\n    return;\n  }\n\n  localStorage.setItem(CONFIG_LOCAL_STORAGE_KEY, JSON.stringify(config));\n  localStorage.setItem(\n    CONFIG_EXPIRY_LOCAL_STORAGE_KEY,\n    String(\n      Date.now() +\n        SettingsService.getInstance().configTimeToLive * 60 * 60 * 1000\n    )\n  );\n}\n\nconst COULD_NOT_GET_CONFIG_MSG =\n  'Could not fetch config, will use default configs';\n\nfunction getRemoteConfig(\n  performanceController: PerformanceController,\n  iid: string\n): Promise<RemoteConfigResponse | undefined> {\n  // Perf needs auth token only to retrieve remote config.\n  return getAuthTokenPromise(performanceController.installations)\n    .then(authToken => {\n      const projectId = getProjectId(performanceController.app);\n      const apiKey = getApiKey(performanceController.app);\n      const configEndPoint = `https://firebaseremoteconfig.googleapis.com/v1/projects/${projectId}/namespaces/fireperf:fetch?key=${apiKey}`;\n      const request = new Request(configEndPoint, {\n        method: 'POST',\n        headers: { Authorization: `${FIS_AUTH_PREFIX} ${authToken}` },\n        /* eslint-disable camelcase */\n        body: JSON.stringify({\n          app_instance_id: iid,\n          app_instance_id_token: authToken,\n          app_id: getAppId(performanceController.app),\n          app_version: SDK_VERSION,\n          sdk_version: REMOTE_CONFIG_SDK_VERSION\n        })\n        /* eslint-enable camelcase */\n      });\n      return fetch(request).then(response => {\n        if (response.ok) {\n          return response.json() as RemoteConfigResponse;\n        }\n        // In case response is not ok. This will be caught by catch.\n        throw ERROR_FACTORY.create(ErrorCode.RC_NOT_OK);\n      });\n    })\n    .catch(() => {\n      consoleLogger.info(COULD_NOT_GET_CONFIG_MSG);\n      return undefined;\n    });\n}\n\n/**\n * Processes config coming either from calling RC or from local storage.\n * This method only runs if call is successful or config in storage\n * is valid.\n */\nfunction processConfig(\n  config?: RemoteConfigResponse\n): RemoteConfigResponse | undefined {\n  if (!config) {\n    return config;\n  }\n  const settingsServiceInstance = SettingsService.getInstance();\n  const entries = config.entries || {};\n  if (entries.fpr_enabled !== undefined) {\n    // TODO: Change the assignment of loggingEnabled once the received type is\n    // known.\n    settingsServiceInstance.loggingEnabled =\n      String(entries.fpr_enabled) === 'true';\n  } else if (DEFAULT_CONFIGS.loggingEnabled !== undefined) {\n    // Config retrieved successfully, but there is no fpr_enabled in template.\n    // Use secondary configs value.\n    settingsServiceInstance.loggingEnabled = DEFAULT_CONFIGS.loggingEnabled;\n  }\n  if (entries.fpr_log_source) {\n    settingsServiceInstance.logSource = Number(entries.fpr_log_source);\n  } else if (DEFAULT_CONFIGS.logSource) {\n    settingsServiceInstance.logSource = DEFAULT_CONFIGS.logSource;\n  }\n\n  if (entries.fpr_log_endpoint_url) {\n    settingsServiceInstance.logEndPointUrl = entries.fpr_log_endpoint_url;\n  } else if (DEFAULT_CONFIGS.logEndPointUrl) {\n    settingsServiceInstance.logEndPointUrl = DEFAULT_CONFIGS.logEndPointUrl;\n  }\n\n  // Key from Remote Config has to be non-empty string, otherwise use local value.\n  if (entries.fpr_log_transport_key) {\n    settingsServiceInstance.transportKey = entries.fpr_log_transport_key;\n  } else if (DEFAULT_CONFIGS.transportKey) {\n    settingsServiceInstance.transportKey = DEFAULT_CONFIGS.transportKey;\n  }\n\n  if (entries.fpr_vc_network_request_sampling_rate !== undefined) {\n    settingsServiceInstance.networkRequestsSamplingRate = Number(\n      entries.fpr_vc_network_request_sampling_rate\n    );\n  } else if (DEFAULT_CONFIGS.networkRequestsSamplingRate !== undefined) {\n    settingsServiceInstance.networkRequestsSamplingRate =\n      DEFAULT_CONFIGS.networkRequestsSamplingRate;\n  }\n  if (entries.fpr_vc_trace_sampling_rate !== undefined) {\n    settingsServiceInstance.tracesSamplingRate = Number(\n      entries.fpr_vc_trace_sampling_rate\n    );\n  } else if (DEFAULT_CONFIGS.tracesSamplingRate !== undefined) {\n    settingsServiceInstance.tracesSamplingRate =\n      DEFAULT_CONFIGS.tracesSamplingRate;\n  }\n  // Set the per session trace and network logging flags.\n  settingsServiceInstance.logTraceAfterSampling = shouldLogAfterSampling(\n    settingsServiceInstance.tracesSamplingRate\n  );\n  settingsServiceInstance.logNetworkAfterSampling = shouldLogAfterSampling(\n    settingsServiceInstance.networkRequestsSamplingRate\n  );\n  return config;\n}\n\nfunction configValid(expiry: string): boolean {\n  return Number(expiry) > Date.now();\n}\n\nfunction shouldLogAfterSampling(samplingRate: number): boolean {\n  return Math.random() <= samplingRate;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getIidPromise } from './iid_service';\nimport { getConfig } from './remote_config_service';\nimport { Api } from './api_service';\nimport { PerformanceController } from '../controllers/perf';\n\nconst enum InitializationStatus {\n  notInitialized = 1,\n  initializationPending,\n  initialized\n}\n\nlet initializationStatus = InitializationStatus.notInitialized;\n\nlet initializationPromise: Promise<void> | undefined;\n\nexport function getInitializationPromise(\n  performanceController: PerformanceController\n): Promise<void> {\n  initializationStatus = InitializationStatus.initializationPending;\n\n  initializationPromise =\n    initializationPromise || initializePerf(performanceController);\n\n  return initializationPromise;\n}\n\nexport function isPerfInitialized(): boolean {\n  return initializationStatus === InitializationStatus.initialized;\n}\n\nfunction initializePerf(\n  performanceController: PerformanceController\n): Promise<void> {\n  return getDocumentReadyComplete()\n    .then(() => getIidPromise(performanceController.installations))\n    .then(iid => getConfig(performanceController, iid))\n    .then(\n      () => changeInitializationStatus(),\n      () => changeInitializationStatus()\n    );\n}\n\n/**\n * Returns a promise which resolves whenever the document readystate is complete or\n * immediately if it is called after page load complete.\n */\nfunction getDocumentReadyComplete(): Promise<void> {\n  const document = Api.getInstance().document;\n  return new Promise(resolve => {\n    if (document && document.readyState !== 'complete') {\n      const handler = (): void => {\n        if (document.readyState === 'complete') {\n          document.removeEventListener('readystatechange', handler);\n          resolve();\n        }\n      };\n      document.addEventListener('readystatechange', handler);\n    } else {\n      resolve();\n    }\n  });\n}\n\nfunction changeInitializationStatus(): void {\n  initializationStatus = InitializationStatus.initialized;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SettingsService } from './settings_service';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport { consoleLogger } from '../utils/console_logger';\n\nconst DEFAULT_SEND_INTERVAL_MS = 10 * 1000;\nconst INITIAL_SEND_TIME_DELAY_MS = 5.5 * 1000;\nconst MAX_EVENT_COUNT_PER_REQUEST = 1000;\nconst DEFAULT_REMAINING_TRIES = 3;\n\nlet remainingTries = DEFAULT_REMAINING_TRIES;\n\ninterface BatchEvent {\n  message: string;\n  eventTime: number;\n}\n\n/* eslint-disable camelcase */\n// CC/Fl accepted log format.\ninterface TransportBatchLogFormat {\n  request_time_ms: string;\n  client_info: ClientInfo;\n  log_source: number;\n  log_event: Log[];\n}\n\ninterface ClientInfo {\n  client_type: number;\n  js_client_info: {};\n}\n\ninterface Log {\n  source_extension_json_proto3: string;\n  event_time_ms: string;\n}\n/* eslint-enable camelcase */\n\nlet queue: BatchEvent[] = [];\n\nlet isTransportSetup: boolean = false;\n\nexport function setupTransportService(): void {\n  if (!isTransportSetup) {\n    processQueue(INITIAL_SEND_TIME_DELAY_MS);\n    isTransportSetup = true;\n  }\n}\n\n/**\n * Utilized by testing to clean up message queue and un-initialize transport service.\n */\nexport function resetTransportService(): void {\n  isTransportSetup = false;\n  queue = [];\n}\n\nfunction processQueue(timeOffset: number): void {\n  setTimeout(() => {\n    // If there is no remainingTries left, stop retrying.\n    if (remainingTries <= 0) {\n      return;\n    }\n\n    if (queue.length > 0) {\n      dispatchQueueEvents();\n    }\n    processQueue(DEFAULT_SEND_INTERVAL_MS);\n  }, timeOffset);\n}\n\nfunction dispatchQueueEvents(): void {\n  // Extract events up to the maximum cap of single logRequest from top of \"official queue\".\n  // The staged events will be used for current logRequest attempt, remaining events will be kept\n  // for next attempt.\n  const staged = queue.splice(0, MAX_EVENT_COUNT_PER_REQUEST);\n\n  /* eslint-disable camelcase */\n  // We will pass the JSON serialized event to the backend.\n  const log_event: Log[] = staged.map(evt => ({\n    source_extension_json_proto3: evt.message,\n    event_time_ms: String(evt.eventTime)\n  }));\n\n  const data: TransportBatchLogFormat = {\n    request_time_ms: String(Date.now()),\n    client_info: {\n      client_type: 1, // 1 is JS\n      js_client_info: {}\n    },\n    log_source: SettingsService.getInstance().logSource,\n    log_event\n  };\n  /* eslint-enable camelcase */\n\n  postToFlEndpoint(data)\n    .then(() => {\n      remainingTries = DEFAULT_REMAINING_TRIES;\n    })\n    .catch(() => {\n      // If the request fails for some reason, add the events that were attempted\n      // back to the primary queue to retry later.\n      queue = [...staged, ...queue];\n      remainingTries--;\n      consoleLogger.info(`Tries left: ${remainingTries}.`);\n      processQueue(DEFAULT_SEND_INTERVAL_MS);\n    });\n}\n\nfunction postToFlEndpoint(data: TransportBatchLogFormat): Promise<void> {\n  const flTransportFullUrl =\n    SettingsService.getInstance().getFlTransportFullUrl();\n  const body = JSON.stringify(data);\n\n  return navigator.sendBeacon && navigator.sendBeacon(flTransportFullUrl, body)\n    ? Promise.resolve()\n    : fetch(flTransportFullUrl, {\n        method: 'POST',\n        body,\n        keepalive: true\n      }).then();\n}\n\nfunction addToQueue(evt: BatchEvent): void {\n  if (!evt.eventTime || !evt.message) {\n    throw ERROR_FACTORY.create(ErrorCode.INVALID_CC_LOG);\n  }\n  // Add the new event to the queue.\n  queue = [...queue, evt];\n}\n\n/** Log handler for cc service to send the performance logs to the server. */\nexport function transportHandler(\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  serializer: (...args: any[]) => string\n): (...args: unknown[]) => void {\n  return (...args) => {\n    const message = serializer(...args);\n    addToQueue({\n      message,\n      eventTime: Date.now()\n    });\n  };\n}\n\n/**\n * Force flush the queued events. Useful at page unload time to ensure all\n * events are uploaded.\n */\nexport function flushQueuedEvents(): void {\n  while (queue.length > 0) {\n    dispatchQueueEvents();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getIid } from './iid_service';\nimport { NetworkRequest } from '../resources/network_request';\nimport { Trace } from '../resources/trace';\nimport { Api } from './api_service';\nimport { SettingsService } from './settings_service';\nimport {\n  getServiceWorkerStatus,\n  getVisibilityState,\n  getEffectiveConnectionType\n} from '../utils/attributes_utils';\nimport {\n  isPerfInitialized,\n  getInitializationPromise\n} from './initialization_service';\nimport { transport<PERSON><PERSON><PERSON>, flushQueuedEvents } from './transport_service';\nimport { SDK_VERSION } from '../constants';\nimport { FirebaseApp } from '@firebase/app';\nimport { getAppId } from '../utils/app_utils';\n\nconst enum ResourceType {\n  NetworkRequest,\n  Trace\n}\n\n/* eslint-disable camelcase */\ninterface ApplicationInfo {\n  google_app_id: string;\n  app_instance_id?: string;\n  web_app_info: WebAppInfo;\n  application_process_state: number;\n}\n\ninterface WebAppInfo {\n  sdk_version: string;\n  page_url: string;\n  service_worker_status: number;\n  visibility_state: number;\n  effective_connection_type: number;\n}\n\ninterface PerfNetworkLog {\n  application_info: ApplicationInfo;\n  network_request_metric: NetworkRequestMetric;\n}\n\ninterface PerfTraceLog {\n  application_info: ApplicationInfo;\n  trace_metric: TraceMetric;\n}\n\ninterface NetworkRequestMetric {\n  url: string;\n  http_method: number;\n  http_response_code: number;\n  response_payload_bytes?: number;\n  client_start_time_us?: number;\n  time_to_response_initiated_us?: number;\n  time_to_response_completed_us?: number;\n}\n\ninterface TraceMetric {\n  name: string;\n  is_auto: boolean;\n  client_start_time_us: number;\n  duration_us: number;\n  counters?: { [key: string]: number };\n  custom_attributes?: { [key: string]: string };\n}\n\ninterface Logger {\n  send: (\n    resource: NetworkRequest | Trace,\n    resourceType: ResourceType\n  ) => void | undefined;\n  flush: () => void;\n}\n\nlet logger: Logger;\n//\n// This method is not called before initialization.\nfunction sendLog(\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n): void {\n  if (!logger) {\n    logger = {\n      send: transportHandler(serializer),\n      flush: flushQueuedEvents\n    };\n  }\n  logger.send(resource, resourceType);\n}\n\nexport function logTrace(trace: Trace): void {\n  const settingsService = SettingsService.getInstance();\n  // Do not log if trace is auto generated and instrumentation is disabled.\n  if (!settingsService.instrumentationEnabled && trace.isAuto) {\n    return;\n  }\n  // Do not log if trace is custom and data collection is disabled.\n  if (!settingsService.dataCollectionEnabled && !trace.isAuto) {\n    return;\n  }\n  // Do not log if required apis are not available.\n  if (!Api.getInstance().requiredApisAvailable()) {\n    return;\n  }\n\n  if (isPerfInitialized()) {\n    sendTraceLog(trace);\n  } else {\n    // Custom traces can be used before the initialization but logging\n    // should wait until after.\n    getInitializationPromise(trace.performanceController).then(\n      () => sendTraceLog(trace),\n      () => sendTraceLog(trace)\n    );\n  }\n}\n\nexport function flushLogs(): void {\n  if (logger) {\n    logger.flush();\n  }\n}\n\nfunction sendTraceLog(trace: Trace): void {\n  if (!getIid()) {\n    return;\n  }\n\n  const settingsService = SettingsService.getInstance();\n  if (\n    !settingsService.loggingEnabled ||\n    !settingsService.logTraceAfterSampling\n  ) {\n    return;\n  }\n\n  sendLog(trace, ResourceType.Trace);\n}\n\nexport function logNetworkRequest(networkRequest: NetworkRequest): void {\n  const settingsService = SettingsService.getInstance();\n  // Do not log network requests if instrumentation is disabled.\n  if (!settingsService.instrumentationEnabled) {\n    return;\n  }\n\n  // Do not log the js sdk's call to transport service domain to avoid unnecessary cycle.\n  // Need to blacklist both old and new endpoints to avoid migration gap.\n  const networkRequestUrl = networkRequest.url;\n\n  // Blacklist old log endpoint and new transport endpoint.\n  // Because Performance SDK doesn't instrument requests sent from SDK itself.\n  const logEndpointUrl = settingsService.logEndPointUrl.split('?')[0];\n  const flEndpointUrl = settingsService.flTransportEndpointUrl.split('?')[0];\n  if (\n    networkRequestUrl === logEndpointUrl ||\n    networkRequestUrl === flEndpointUrl\n  ) {\n    return;\n  }\n\n  if (\n    !settingsService.loggingEnabled ||\n    !settingsService.logNetworkAfterSampling\n  ) {\n    return;\n  }\n\n  sendLog(networkRequest, ResourceType.NetworkRequest);\n}\n\nfunction serializer(\n  resource: NetworkRequest | Trace,\n  resourceType: ResourceType\n): string {\n  if (resourceType === ResourceType.NetworkRequest) {\n    return serializeNetworkRequest(resource as NetworkRequest);\n  }\n  return serializeTrace(resource as Trace);\n}\n\nfunction serializeNetworkRequest(networkRequest: NetworkRequest): string {\n  const networkRequestMetric: NetworkRequestMetric = {\n    url: networkRequest.url,\n    http_method: networkRequest.httpMethod || 0,\n    http_response_code: 200,\n    response_payload_bytes: networkRequest.responsePayloadBytes,\n    client_start_time_us: networkRequest.startTimeUs,\n    time_to_response_initiated_us: networkRequest.timeToResponseInitiatedUs,\n    time_to_response_completed_us: networkRequest.timeToResponseCompletedUs\n  };\n  const perfMetric: PerfNetworkLog = {\n    application_info: getApplicationInfo(\n      networkRequest.performanceController.app\n    ),\n    network_request_metric: networkRequestMetric\n  };\n  return JSON.stringify(perfMetric);\n}\n\nfunction serializeTrace(trace: Trace): string {\n  const traceMetric: TraceMetric = {\n    name: trace.name,\n    is_auto: trace.isAuto,\n    client_start_time_us: trace.startTimeUs,\n    duration_us: trace.durationUs\n  };\n\n  if (Object.keys(trace.counters).length !== 0) {\n    traceMetric.counters = trace.counters;\n  }\n  const customAttributes = trace.getAttributes();\n  if (Object.keys(customAttributes).length !== 0) {\n    traceMetric.custom_attributes = customAttributes;\n  }\n\n  const perfMetric: PerfTraceLog = {\n    application_info: getApplicationInfo(trace.performanceController.app),\n    trace_metric: traceMetric\n  };\n  return JSON.stringify(perfMetric);\n}\n\nfunction getApplicationInfo(firebaseApp: FirebaseApp): ApplicationInfo {\n  return {\n    google_app_id: getAppId(firebaseApp),\n    app_instance_id: getIid(),\n    web_app_info: {\n      sdk_version: SDK_VERSION,\n      page_url: Api.getInstance().getUrl(),\n      service_worker_status: getServiceWorkerStatus(),\n      visibility_state: getVisibilityState(),\n      effective_connection_type: getEffectiveConnectionType()\n    },\n    application_process_state: 0\n  };\n}\n\n/* eslint-enable camelcase */\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Api } from '../services/api_service';\nimport { logNetworkRequest } from '../services/perf_logger';\nimport { PerformanceController } from '../controllers/perf';\n\n// The order of values of this enum should not be changed.\nexport const enum HttpMethod {\n  HTTP_METHOD_UNKNOWN = 0,\n  GET = 1,\n  PUT = 2,\n  POST = 3,\n  DELETE = 4,\n  HEAD = 5,\n  PATCH = 6,\n  OPTIONS = 7,\n  TRACE = 8,\n  CONNECT = 9\n}\n\n// Durations are in microseconds.\nexport interface NetworkRequest {\n  performanceController: PerformanceController;\n  url: string;\n  httpMethod?: HttpMethod;\n  requestPayloadBytes?: number;\n  responsePayloadBytes?: number;\n  httpResponseCode?: number;\n  responseContentType?: string;\n  startTimeUs?: number;\n  timeToRequestCompletedUs?: number;\n  timeToResponseInitiatedUs?: number;\n  timeToResponseCompletedUs?: number;\n}\n\nexport function createNetworkRequestEntry(\n  performanceController: PerformanceController,\n  entry: PerformanceEntry\n): void {\n  const performanceEntry = entry as PerformanceResourceTiming;\n  if (!performanceEntry || performanceEntry.responseStart === undefined) {\n    return;\n  }\n  const timeOrigin = Api.getInstance().getTimeOrigin();\n  const startTimeUs = Math.floor(\n    (performanceEntry.startTime + timeOrigin) * 1000\n  );\n  const timeToResponseInitiatedUs = performanceEntry.responseStart\n    ? Math.floor(\n        (performanceEntry.responseStart - performanceEntry.startTime) * 1000\n      )\n    : undefined;\n  const timeToResponseCompletedUs = Math.floor(\n    (performanceEntry.responseEnd - performanceEntry.startTime) * 1000\n  );\n  // Remove the query params from logged network request url.\n  const url = performanceEntry.name && performanceEntry.name.split('?')[0];\n  const networkRequest: NetworkRequest = {\n    performanceController,\n    url,\n    responsePayloadBytes: performanceEntry.transferSize,\n    startTimeUs,\n    timeToResponseInitiatedUs,\n    timeToResponseCompletedUs\n  };\n\n  logNetworkRequest(networkRequest);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  OOB_TRACE_PAGE_LOAD_PREFIX,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME\n} from '../constants';\nimport { consoleLogger } from '../utils/console_logger';\n\nconst MAX_METRIC_NAME_LENGTH = 100;\nconst RESERVED_AUTO_PREFIX = '_';\nconst oobMetrics = [\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME\n];\n\n/**\n * Returns true if the metric is custom and does not start with reserved prefix, or if\n * the metric is one of out of the box page load trace metrics.\n */\nexport function isValidMetricName(name: string, traceName?: string): boolean {\n  if (name.length === 0 || name.length > MAX_METRIC_NAME_LENGTH) {\n    return false;\n  }\n  return (\n    (traceName &&\n      traceName.startsWith(OOB_TRACE_PAGE_LOAD_PREFIX) &&\n      oobMetrics.indexOf(name) > -1) ||\n    !name.startsWith(RESERVED_AUTO_PREFIX)\n  );\n}\n\n/**\n * Converts the provided value to an integer value to be used in case of a metric.\n * @param providedValue Provided number value of the metric that needs to be converted to an integer.\n *\n * @returns Converted integer number to be set for the metric.\n */\nexport function convertMetricValueToInteger(providedValue: number): number {\n  const valueAsInteger: number = Math.floor(providedValue);\n  if (valueAsInteger < providedValue) {\n    consoleLogger.info(\n      `Metric value should be an Integer, setting the value as : ${valueAsInteger}.`\n    );\n  }\n  return valueAsInteger;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  TRACE_START_MARK_PREFIX,\n  TRACE_STOP_MARK_PREFIX,\n  TRACE_MEASURE_PREFIX,\n  OOB_TRACE_PAGE_LOAD_PREFIX,\n  FIRST_PAINT_COUNTER_NAME,\n  FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n  FIRST_INPUT_DELAY_COUNTER_NAME,\n  LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n  LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME,\n  INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n  INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n  CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME\n} from '../constants';\nimport { Api } from '../services/api_service';\nimport { logTrace, flushLogs } from '../services/perf_logger';\nimport { ERROR_FACTORY, ErrorCode } from '../utils/errors';\nimport {\n  isValidCustomAttributeName,\n  isValidCustomAttributeValue\n} from '../utils/attributes_utils';\nimport {\n  isValidMetricName,\n  convertMetricValueToInteger\n} from '../utils/metric_utils';\nimport { PerformanceTrace } from '../public_types';\nimport { PerformanceController } from '../controllers/perf';\nimport { CoreVitalMetric, WebVitalMetrics } from './web_vitals';\n\nconst enum TraceState {\n  UNINITIALIZED = 1,\n  RUNNING,\n  TERMINATED\n}\n\nexport class Trace implements PerformanceTrace {\n  private state: TraceState = TraceState.UNINITIALIZED;\n  startTimeUs!: number;\n  durationUs!: number;\n  private customAttributes: { [key: string]: string } = {};\n  counters: { [counterName: string]: number } = {};\n  private api = Api.getInstance();\n  private randomId = Math.floor(Math.random() * 1000000);\n  private traceStartMark!: string;\n  private traceStopMark!: string;\n  private traceMeasure!: string;\n\n  /**\n   * @param performanceController The performance controller running.\n   * @param name The name of the trace.\n   * @param isAuto If the trace is auto-instrumented.\n   * @param traceMeasureName The name of the measure marker in user timing specification. This field\n   * is only set when the trace is built for logging when the user directly uses the user timing\n   * api (performance.mark and performance.measure).\n   */\n  constructor(\n    readonly performanceController: PerformanceController,\n    readonly name: string,\n    readonly isAuto = false,\n    traceMeasureName?: string\n  ) {\n    if (!this.isAuto) {\n      this.traceStartMark = `${TRACE_START_MARK_PREFIX}-${this.randomId}-${this.name}`;\n      this.traceStopMark = `${TRACE_STOP_MARK_PREFIX}-${this.randomId}-${this.name}`;\n      this.traceMeasure =\n        traceMeasureName ||\n        `${TRACE_MEASURE_PREFIX}-${this.randomId}-${this.name}`;\n\n      if (traceMeasureName) {\n        // For the case of direct user timing traces, no start stop will happen. The measure object\n        // is already available.\n        this.calculateTraceMetrics();\n      }\n    }\n  }\n\n  /**\n   * Starts a trace. The measurement of the duration starts at this point.\n   */\n  start(): void {\n    if (this.state !== TraceState.UNINITIALIZED) {\n      throw ERROR_FACTORY.create(ErrorCode.TRACE_STARTED_BEFORE, {\n        traceName: this.name\n      });\n    }\n    this.api.mark(this.traceStartMark);\n    this.state = TraceState.RUNNING;\n  }\n\n  /**\n   * Stops the trace. The measurement of the duration of the trace stops at this point and trace\n   * is logged.\n   */\n  stop(): void {\n    if (this.state !== TraceState.RUNNING) {\n      throw ERROR_FACTORY.create(ErrorCode.TRACE_STOPPED_BEFORE, {\n        traceName: this.name\n      });\n    }\n    this.state = TraceState.TERMINATED;\n    this.api.mark(this.traceStopMark);\n    this.api.measure(\n      this.traceMeasure,\n      this.traceStartMark,\n      this.traceStopMark\n    );\n    this.calculateTraceMetrics();\n    logTrace(this);\n  }\n\n  /**\n   * Records a trace with predetermined values. If this method is used a trace is created and logged\n   * directly. No need to use start and stop methods.\n   * @param startTime Trace start time since epoch in millisec\n   * @param duration The duration of the trace in millisec\n   * @param options An object which can optionally hold maps of custom metrics and custom attributes\n   */\n  record(\n    startTime: number,\n    duration: number,\n    options?: {\n      metrics?: { [key: string]: number };\n      attributes?: { [key: string]: string };\n    }\n  ): void {\n    if (startTime <= 0) {\n      throw ERROR_FACTORY.create(ErrorCode.NONPOSITIVE_TRACE_START_TIME, {\n        traceName: this.name\n      });\n    }\n    if (duration <= 0) {\n      throw ERROR_FACTORY.create(ErrorCode.NONPOSITIVE_TRACE_DURATION, {\n        traceName: this.name\n      });\n    }\n\n    this.durationUs = Math.floor(duration * 1000);\n    this.startTimeUs = Math.floor(startTime * 1000);\n    if (options && options.attributes) {\n      this.customAttributes = { ...options.attributes };\n    }\n    if (options && options.metrics) {\n      for (const metricName of Object.keys(options.metrics)) {\n        if (!isNaN(Number(options.metrics[metricName]))) {\n          this.counters[metricName] = Math.floor(\n            Number(options.metrics[metricName])\n          );\n        }\n      }\n    }\n    logTrace(this);\n  }\n\n  /**\n   * Increments a custom metric by a certain number or 1 if number not specified. Will create a new\n   * custom metric if one with the given name does not exist. The value will be floored down to an\n   * integer.\n   * @param counter Name of the custom metric\n   * @param numAsInteger Increment by value\n   */\n  incrementMetric(counter: string, numAsInteger = 1): void {\n    if (this.counters[counter] === undefined) {\n      this.putMetric(counter, numAsInteger);\n    } else {\n      this.putMetric(counter, this.counters[counter] + numAsInteger);\n    }\n  }\n\n  /**\n   * Sets a custom metric to a specified value. Will create a new custom metric if one with the\n   * given name does not exist. The value will be floored down to an integer.\n   * @param counter Name of the custom metric\n   * @param numAsInteger Set custom metric to this value\n   */\n  putMetric(counter: string, numAsInteger: number): void {\n    if (isValidMetricName(counter, this.name)) {\n      this.counters[counter] = convertMetricValueToInteger(numAsInteger ?? 0);\n    } else {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_CUSTOM_METRIC_NAME, {\n        customMetricName: counter\n      });\n    }\n  }\n\n  /**\n   * Returns the value of the custom metric by that name. If a custom metric with that name does\n   * not exist will return zero.\n   * @param counter\n   */\n  getMetric(counter: string): number {\n    return this.counters[counter] || 0;\n  }\n\n  /**\n   * Sets a custom attribute of a trace to a certain value.\n   * @param attr\n   * @param value\n   */\n  putAttribute(attr: string, value: string): void {\n    const isValidName = isValidCustomAttributeName(attr);\n    const isValidValue = isValidCustomAttributeValue(value);\n    if (isValidName && isValidValue) {\n      this.customAttributes[attr] = value;\n      return;\n    }\n    // Throw appropriate error when the attribute name or value is invalid.\n    if (!isValidName) {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_ATTRIBUTE_NAME, {\n        attributeName: attr\n      });\n    }\n    if (!isValidValue) {\n      throw ERROR_FACTORY.create(ErrorCode.INVALID_ATTRIBUTE_VALUE, {\n        attributeValue: value\n      });\n    }\n  }\n\n  /**\n   * Retrieves the value a custom attribute of a trace is set to.\n   * @param attr\n   */\n  getAttribute(attr: string): string | undefined {\n    return this.customAttributes[attr];\n  }\n\n  removeAttribute(attr: string): void {\n    if (this.customAttributes[attr] === undefined) {\n      return;\n    }\n    delete this.customAttributes[attr];\n  }\n\n  getAttributes(): { [key: string]: string } {\n    return { ...this.customAttributes };\n  }\n\n  private setStartTime(startTime: number): void {\n    this.startTimeUs = startTime;\n  }\n\n  private setDuration(duration: number): void {\n    this.durationUs = duration;\n  }\n\n  /**\n   * Calculates and assigns the duration and start time of the trace using the measure performance\n   * entry.\n   */\n  private calculateTraceMetrics(): void {\n    const perfMeasureEntries = this.api.getEntriesByName(this.traceMeasure);\n    const perfMeasureEntry = perfMeasureEntries && perfMeasureEntries[0];\n    if (perfMeasureEntry) {\n      this.durationUs = Math.floor(perfMeasureEntry.duration * 1000);\n      this.startTimeUs = Math.floor(\n        (perfMeasureEntry.startTime + this.api.getTimeOrigin()) * 1000\n      );\n    }\n  }\n\n  /**\n   * @param navigationTimings A single element array which contains the navigationTIming object of\n   * the page load\n   * @param paintTimings A array which contains paintTiming object of the page load\n   * @param firstInputDelay First input delay in millisec\n   */\n  static createOobTrace(\n    performanceController: PerformanceController,\n    navigationTimings: PerformanceNavigationTiming[],\n    paintTimings: PerformanceEntry[],\n    webVitalMetrics: WebVitalMetrics,\n    firstInputDelay?: number\n  ): void {\n    const route = Api.getInstance().getUrl();\n    if (!route) {\n      return;\n    }\n    const trace = new Trace(\n      performanceController,\n      OOB_TRACE_PAGE_LOAD_PREFIX + route,\n      true\n    );\n    const timeOriginUs = Math.floor(Api.getInstance().getTimeOrigin() * 1000);\n    trace.setStartTime(timeOriginUs);\n\n    // navigationTimings includes only one element.\n    if (navigationTimings && navigationTimings[0]) {\n      trace.setDuration(Math.floor(navigationTimings[0].duration * 1000));\n      trace.putMetric(\n        'domInteractive',\n        Math.floor(navigationTimings[0].domInteractive * 1000)\n      );\n      trace.putMetric(\n        'domContentLoadedEventEnd',\n        Math.floor(navigationTimings[0].domContentLoadedEventEnd * 1000)\n      );\n      trace.putMetric(\n        'loadEventEnd',\n        Math.floor(navigationTimings[0].loadEventEnd * 1000)\n      );\n    }\n\n    const FIRST_PAINT = 'first-paint';\n    const FIRST_CONTENTFUL_PAINT = 'first-contentful-paint';\n    if (paintTimings) {\n      const firstPaint = paintTimings.find(\n        paintObject => paintObject.name === FIRST_PAINT\n      );\n      if (firstPaint && firstPaint.startTime) {\n        trace.putMetric(\n          FIRST_PAINT_COUNTER_NAME,\n          Math.floor(firstPaint.startTime * 1000)\n        );\n      }\n      const firstContentfulPaint = paintTimings.find(\n        paintObject => paintObject.name === FIRST_CONTENTFUL_PAINT\n      );\n      if (firstContentfulPaint && firstContentfulPaint.startTime) {\n        trace.putMetric(\n          FIRST_CONTENTFUL_PAINT_COUNTER_NAME,\n          Math.floor(firstContentfulPaint.startTime * 1000)\n        );\n      }\n\n      if (firstInputDelay) {\n        trace.putMetric(\n          FIRST_INPUT_DELAY_COUNTER_NAME,\n          Math.floor(firstInputDelay * 1000)\n        );\n      }\n    }\n\n    this.addWebVitalMetric(\n      trace,\n      LARGEST_CONTENTFUL_PAINT_METRIC_NAME,\n      LARGEST_CONTENTFUL_PAINT_ATTRIBUTE_NAME,\n      webVitalMetrics.lcp\n    );\n    this.addWebVitalMetric(\n      trace,\n      CUMULATIVE_LAYOUT_SHIFT_METRIC_NAME,\n      CUMULATIVE_LAYOUT_SHIFT_ATTRIBUTE_NAME,\n      webVitalMetrics.cls\n    );\n    this.addWebVitalMetric(\n      trace,\n      INTERACTION_TO_NEXT_PAINT_METRIC_NAME,\n      INTERACTION_TO_NEXT_PAINT_ATTRIBUTE_NAME,\n      webVitalMetrics.inp\n    );\n\n    // Page load logs are sent at unload time and so should be logged and\n    // flushed immediately.\n    logTrace(trace);\n    flushLogs();\n  }\n\n  static addWebVitalMetric(\n    trace: Trace,\n    metricKey: string,\n    attributeKey: string,\n    metric?: CoreVitalMetric\n  ): void {\n    if (metric) {\n      trace.putMetric(metricKey, Math.floor(metric.value * 1000));\n      if (metric.elementAttribution) {\n        trace.putAttribute(attributeKey, metric.elementAttribution);\n      }\n    }\n  }\n\n  static createUserTimingTrace(\n    performanceController: PerformanceController,\n    measureName: string\n  ): void {\n    const trace = new Trace(\n      performanceController,\n      measureName,\n      false,\n      measureName\n    );\n    logTrace(trace);\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CLSMetricWithAttribution,\n  INPMetricWithAttribution,\n  LCPMetricWithAttribution\n} from 'web-vitals/attribution';\n\nimport { TRACE_MEASURE_PREFIX } from '../constants';\nimport { PerformanceController } from '../controllers/perf';\nimport { createNetworkRequestEntry } from '../resources/network_request';\nimport { Trace } from '../resources/trace';\nimport { WebVitalMetrics } from '../resources/web_vitals';\n\nimport { Api } from './api_service';\nimport { getIid } from './iid_service';\n\nlet webVitalMetrics: WebVitalMetrics = {};\nlet sentPageLoadTrace: boolean = false;\nlet firstInputDelay: number | undefined;\n\nexport function setupOobResources(\n  performanceController: PerformanceController\n): void {\n  // Do not initialize unless iid is available.\n  if (!getIid()) {\n    return;\n  }\n  // The load event might not have fired yet, and that means performance\n  // navigation timing object has a duration of 0. The setup should run after\n  // all current tasks in js queue.\n  setTimeout(() => setupOobTraces(performanceController), 0);\n  setTimeout(() => setupNetworkRequests(performanceController), 0);\n  setTimeout(() => setupUserTimingTraces(performanceController), 0);\n}\n\nfunction setupNetworkRequests(\n  performanceController: PerformanceController\n): void {\n  const api = Api.getInstance();\n  const resources = api.getEntriesByType('resource');\n  for (const resource of resources) {\n    createNetworkRequestEntry(performanceController, resource);\n  }\n  api.setupObserver('resource', entry =>\n    createNetworkRequestEntry(performanceController, entry)\n  );\n}\n\nfunction setupOobTraces(performanceController: PerformanceController): void {\n  const api = Api.getInstance();\n  // Better support for Safari\n  if ('onpagehide' in window) {\n    api.document.addEventListener('pagehide', () =>\n      sendOobTrace(performanceController)\n    );\n  } else {\n    api.document.addEventListener('unload', () =>\n      sendOobTrace(performanceController)\n    );\n  }\n  api.document.addEventListener('visibilitychange', () => {\n    if (api.document.visibilityState === 'hidden') {\n      sendOobTrace(performanceController);\n    }\n  });\n\n  if (api.onFirstInputDelay) {\n    api.onFirstInputDelay((fid: number) => {\n      firstInputDelay = fid;\n    });\n  }\n\n  api.onLCP((metric: LCPMetricWithAttribution) => {\n    webVitalMetrics.lcp = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.element\n    };\n  });\n  api.onCLS((metric: CLSMetricWithAttribution) => {\n    webVitalMetrics.cls = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.largestShiftTarget\n    };\n  });\n  api.onINP((metric: INPMetricWithAttribution) => {\n    webVitalMetrics.inp = {\n      value: metric.value,\n      elementAttribution: metric.attribution?.interactionTarget\n    };\n  });\n}\n\nfunction setupUserTimingTraces(\n  performanceController: PerformanceController\n): void {\n  const api = Api.getInstance();\n  // Run through the measure performance entries collected up to this point.\n  const measures = api.getEntriesByType('measure');\n  for (const measure of measures) {\n    createUserTimingTrace(performanceController, measure);\n  }\n  // Setup an observer to capture the measures from this point on.\n  api.setupObserver('measure', entry =>\n    createUserTimingTrace(performanceController, entry)\n  );\n}\n\nfunction createUserTimingTrace(\n  performanceController: PerformanceController,\n  measure: PerformanceEntry\n): void {\n  const measureName = measure.name;\n  // Do not create a trace, if the user timing marks and measures are created by\n  // the sdk itself.\n  if (\n    measureName.substring(0, TRACE_MEASURE_PREFIX.length) ===\n    TRACE_MEASURE_PREFIX\n  ) {\n    return;\n  }\n  Trace.createUserTimingTrace(performanceController, measureName);\n}\n\nfunction sendOobTrace(performanceController: PerformanceController): void {\n  if (!sentPageLoadTrace) {\n    sentPageLoadTrace = true;\n    const api = Api.getInstance();\n    const navigationTimings = api.getEntriesByType(\n      'navigation'\n    ) as PerformanceNavigationTiming[];\n    const paintTimings = api.getEntriesByType('paint');\n\n    // On page unload web vitals may be updated so queue the oob trace creation\n    // so that these updates have time to be included.\n    setTimeout(() => {\n      Trace.createOobTrace(\n        performanceController,\n        navigationTimings,\n        paintTimings,\n        webVitalMetrics,\n        firstInputDelay\n      );\n    }, 0);\n  }\n}\n\n/**\n * This service will only export the page load trace once. This function allows\n * resetting it for unit tests\n */\nexport function resetForUnitTests(): void {\n  sentPageLoadTrace = false;\n  firstInputDelay = undefined;\n  webVitalMetrics = {};\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { setupOobResources } from '../services/oob_resources_service';\nimport { SettingsService } from '../services/settings_service';\nimport { getInitializationPromise } from '../services/initialization_service';\nimport { Api } from '../services/api_service';\nimport { FirebaseApp } from '@firebase/app';\nimport { _FirebaseInstallationsInternal } from '@firebase/installations';\nimport { PerformanceSettings, FirebasePerformance } from '../public_types';\nimport { validateIndexedDBOpenable } from '@firebase/util';\nimport { setupTransportService } from '../services/transport_service';\nimport { consoleLogger } from '../utils/console_logger';\n\nexport class PerformanceController implements FirebasePerformance {\n  private initialized: boolean = false;\n\n  constructor(\n    readonly app: FirebaseApp,\n    readonly installations: _FirebaseInstallationsInternal\n  ) {}\n\n  /**\n   * This method *must* be called internally as part of creating a\n   * PerformanceController instance.\n   *\n   * Currently it's not possible to pass the settings object through the\n   * constructor using Components, so this method exists to be called with the\n   * desired settings, to ensure nothing is collected without the user's\n   * consent.\n   */\n  _init(settings?: PerformanceSettings): void {\n    if (this.initialized) {\n      return;\n    }\n\n    if (settings?.dataCollectionEnabled !== undefined) {\n      this.dataCollectionEnabled = settings.dataCollectionEnabled;\n    }\n    if (settings?.instrumentationEnabled !== undefined) {\n      this.instrumentationEnabled = settings.instrumentationEnabled;\n    }\n\n    if (Api.getInstance().requiredApisAvailable()) {\n      validateIndexedDBOpenable()\n        .then(isAvailable => {\n          if (isAvailable) {\n            setupTransportService();\n            getInitializationPromise(this).then(\n              () => setupOobResources(this),\n              () => setupOobResources(this)\n            );\n            this.initialized = true;\n          }\n        })\n        .catch(error => {\n          consoleLogger.info(`Environment doesn't support IndexedDB: ${error}`);\n        });\n    } else {\n      consoleLogger.info(\n        'Firebase Performance cannot start if the browser does not support ' +\n          '\"Fetch\" and \"Promise\", or cookies are disabled.'\n      );\n    }\n  }\n\n  set instrumentationEnabled(val: boolean) {\n    SettingsService.getInstance().instrumentationEnabled = val;\n  }\n  get instrumentationEnabled(): boolean {\n    return SettingsService.getInstance().instrumentationEnabled;\n  }\n\n  set dataCollectionEnabled(val: boolean) {\n    SettingsService.getInstance().dataCollectionEnabled = val;\n  }\n  get dataCollectionEnabled(): boolean {\n    return SettingsService.getInstance().dataCollectionEnabled;\n  }\n}\n", "/**\n * The Firebase Performance Monitoring Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  FirebasePerformance,\n  PerformanceSettings,\n  PerformanceTrace\n} from './public_types';\nimport { ERROR_FACTORY, ErrorCode } from './utils/errors';\nimport { setupApi } from './services/api_service';\nimport { PerformanceController } from './controllers/perf';\nimport {\n  _registerComponent,\n  _getProvider,\n  registerVersion,\n  FirebaseApp,\n  getApp\n} from '@firebase/app';\nimport {\n  InstanceFactory,\n  ComponentContainer,\n  Component,\n  ComponentType\n} from '@firebase/component';\nimport { name, version } from '../package.json';\nimport { Trace } from './resources/trace';\nimport '@firebase/installations';\nimport { deepEqual, getModularInstance } from '@firebase/util';\n\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\n * Returns a {@link FirebasePerformance} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @public\n */\nexport function getPerformance(\n  app: FirebaseApp = getApp()\n): FirebasePerformance {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'performance');\n  const perfInstance = provider.getImmediate() as PerformanceController;\n  return perfInstance;\n}\n\n/**\n * Returns a {@link FirebasePerformance} instance for the given app. Can only be called once.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param settings - Optional settings for the {@link FirebasePerformance} instance.\n * @public\n */\nexport function initializePerformance(\n  app: FirebaseApp,\n  settings?: PerformanceSettings\n): FirebasePerformance {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'performance');\n\n  // throw if an instance was already created.\n  // It could happen if initializePerformance() is called more than once, or getPerformance() is called first.\n  if (provider.isInitialized()) {\n    const existingInstance = provider.getImmediate();\n    const initialSettings = provider.getOptions() as PerformanceSettings;\n    if (deepEqual(initialSettings, settings ?? {})) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(ErrorCode.ALREADY_INITIALIZED);\n    }\n  }\n\n  const perfInstance = provider.initialize({\n    options: settings\n  }) as PerformanceController;\n  return perfInstance;\n}\n\n/**\n * Returns a new `PerformanceTrace` instance.\n * @param performance - The {@link FirebasePerformance} instance to use.\n * @param name - The name of the trace.\n * @public\n */\nexport function trace(\n  performance: FirebasePerformance,\n  name: string\n): PerformanceTrace {\n  performance = getModularInstance(performance);\n  return new Trace(performance as PerformanceController, name);\n}\n\nconst factory: InstanceFactory<'performance'> = (\n  container: ComponentContainer,\n  { options: settings }: { options?: PerformanceSettings }\n) => {\n  // Dependencies\n  const app = container.getProvider('app').getImmediate();\n  const installations = container\n    .getProvider('installations-internal')\n    .getImmediate();\n\n  if (app.name !== DEFAULT_ENTRY_NAME) {\n    throw ERROR_FACTORY.create(ErrorCode.FB_NOT_DEFAULT);\n  }\n  if (typeof window === 'undefined') {\n    throw ERROR_FACTORY.create(ErrorCode.NO_WINDOW);\n  }\n  setupApi(window);\n  const perfInstance = new PerformanceController(app, installations);\n  perfInstance._init(settings);\n\n  return perfInstance;\n};\n\nfunction registerPerformance(): void {\n  _registerComponent(\n    new Component('performance', factory, ComponentType.PUBLIC)\n  );\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterPerformance();\n\nexport { FirebasePerformance, PerformanceSettings, PerformanceTrace };\n"], "names": ["vitalsOnLCP", "vitalsOnINP", "vitalsOnCLS"], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAII,MAAM,WAAW,GAAG,OAAO,CAAC;AACnC;AACO,MAAM,uBAAuB,GAAG,qBAAqB,CAAC;AAC7D;AACO,MAAM,sBAAsB,GAAG,oBAAoB,CAAC;AAC3D;AACO,MAAM,oBAAoB,GAAG,uBAAuB,CAAC;AAC5D;AACO,MAAM,0BAA0B,GAAG,MAAM,CAAC;AAE1C,MAAM,wBAAwB,GAAG,KAAK,CAAC;AAEvC,MAAM,mCAAmC,GAAG,MAAM,CAAC;AAEnD,MAAM,8BAA8B,GAAG,MAAM,CAAC;AAE9C,MAAM,oCAAoC,GAAG,MAAM,CAAC;AACpD,MAAM,uCAAuC,GAAG,aAAa,CAAC;AAE9D,MAAM,qCAAqC,GAAG,MAAM,CAAC;AACrD,MAAM,wCAAwC,GAAG,uBAAuB,CAAC;AAEzE,MAAM,mCAAmC,GAAG,MAAM,CAAC;AACnD,MAAM,sCAAsC,GAAG,wBAAwB,CAAC;AAExE,MAAM,wBAAwB,GAAG,8BAA8B,CAAC;AAEhE,MAAM,+BAA+B,GAC1C,oCAAoC,CAAC;AAEhC,MAAM,OAAO,GAAG,aAAa,CAAC;AAC9B,MAAM,YAAY,GAAG,aAAa;;AClDzC;;;;;;;;;;;;;;;AAeG;AAwBH,MAAM,qBAAqB,GAA4C;AACrE,IAAA,CAAA,eAAA,wCAAkC,wCAAwC;AAC1E,IAAA,CAAA,eAAA,wCAAkC,oCAAoC;AACtE,IAAA,CAAA,6BAAA,gDACE,kDAAkD;AACpD,IAAA,CAAA,4BAAA,8CACE,iDAAiD;AACnD,IAAA,CAAA,WAAA,6BAAuB,0BAA0B;AACjD,IAAA,CAAA,WAAA,6BAAuB,0BAA0B;AACjD,IAAA,CAAA,eAAA,iCAA2B,8BAA8B;AACzD,IAAA,CAAA,YAAA,8BAAwB,2BAA2B;AACnD,IAAA,CAAA,gBAAA,kCAA4B,qCAAqC;AACjE,IAAA,CAAA,gBAAA,kCACE,2EAA2E;AAC7E,IAAA,CAAA,oBAAA,6BAAuB,uBAAuB;AAC9C,IAAA,CAAA,wBAAA,0CACE,6CAA6C;AAC/C,IAAA,CAAA,yBAAA,2CACE,+CAA+C;AACjD,IAAA,CAAA,4BAAA,8CACE,mDAAmD;AACrD,IAAA,CAAA,6BAAA,mDACE,sEAAsE;AACxE,IAAA,CAAA,qBAAA,uCACE,uDAAuD;QACvD,gFAAgF;QAChF,uFAAuF;QACvF,gCAAgC;CACnC,CAAC;AAYK,MAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,OAAO,EACP,YAAY,EACZ,qBAAqB,CACtB;;ACnFD;;;;;;;;;;;;;;;AAeG;AAKI,MAAM,aAAa,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,CAAC;AACtD,aAAa,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI;;ACrBtC;;;;;;;;;;;;;;;AAeG;AAqBH,IAAI,WAA4B,CAAC;AACjC,IAAI,cAAkC,CAAC;AAUvC;;;AAGG;MACU,GAAG,CAAA;AAad,IAAA,WAAA,CAAqB,MAAe,EAAA;QAAf,IAAM,CAAA,MAAA,GAAN,MAAM,CAAS;QAClC,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,WAAA,2BAAqB,CAAC;SACjD;AACD,QAAA,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;AACtC,QAAA,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,mBAAmB,CAAC;AACtD,QAAA,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC;AACtC,QAAA,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAChC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE;;;AAGlD,YAAA,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;SACzC;QACD,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE;YAC9D,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,WAAW,CAAC,iBAAiB,CAAC;SAC/D;AACD,QAAA,IAAI,CAAC,KAAK,GAAGA,KAAW,CAAC;AACzB,QAAA,IAAI,CAAC,KAAK,GAAGC,KAAW,CAAC;AACzB,QAAA,IAAI,CAAC,KAAK,GAAGC,KAAW,CAAC;KAC1B;IAED,MAAM,GAAA;;AAEJ,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/C;AAED,IAAA,IAAI,CAAC,IAAY,EAAA;AACf,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;YAC/C,OAAO;SACR;AACD,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC7B;AAED,IAAA,OAAO,CAAC,WAAmB,EAAE,KAAa,EAAE,KAAa,EAAA;AACvD,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;YAClD,OAAO;SACR;QACD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;KACrD;AAED,IAAA,gBAAgB,CAAC,IAAe,EAAA;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE;AAC3D,YAAA,OAAO,EAAE,CAAC;SACX;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;KAChD;AAED,IAAA,gBAAgB,CAAC,IAAY,EAAA;AAC3B,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE;AAC3D,YAAA,OAAO,EAAE,CAAC;SACX;QACD,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;KAChD;IAED,aAAa,GAAA;;QAEX,QACE,IAAI,CAAC,WAAW;AAChB,aAAC,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,EACxE;KACH;IAED,qBAAqB,GAAA;QACnB,IAAI,CAAC,KAAK,IAAI,CAAC,OAAO,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC9C,YAAA,aAAa,CAAC,IAAI,CAChB,wGAAwG,CACzG,CAAC;AACF,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,IAAI,CAAC,oBAAoB,EAAE,EAAE;AAC3B,YAAA,aAAa,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;AACpE,YAAA,OAAO,KAAK,CAAC;SACd;AACD,QAAA,OAAO,IAAI,CAAC;KACb;IAED,aAAa,CACX,SAAoB,EACpB,QAA2C,EAAA;AAE3C,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B,OAAO;SACR;QACD,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,IAAG;YACnD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;gBAErC,QAAQ,CAAC,KAAK,CAAC,CAAC;aACjB;AACH,SAAC,CAAC,CAAC;;QAGH,QAAQ,CAAC,OAAO,CAAC,EAAE,UAAU,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;KAC/C;AAED,IAAA,OAAO,WAAW,GAAA;AAChB,QAAA,IAAI,WAAW,KAAK,SAAS,EAAE;AAC7B,YAAA,WAAW,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,CAAC;SACvC;AACD,QAAA,OAAO,WAAW,CAAC;KACpB;AACF,CAAA;AAEK,SAAU,QAAQ,CAAC,MAAc,EAAA;IACrC,cAAc,GAAG,MAAM,CAAC;AAC1B;;AC1KA;;;;;;;;;;;;;;;AAeG;AAIH,IAAI,GAAuB,CAAC;AAGtB,SAAU,aAAa,CAC3B,oBAAoD,EAAA;AAEpD,IAAA,MAAM,UAAU,GAAG,oBAAoB,CAAC,KAAK,EAAE,CAAC;;AAEhD,IAAA,UAAU,CAAC,IAAI,CAAC,CAAC,MAAc,KAAI;QACjC,GAAG,GAAG,MAAM,CAAC;AACf,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,UAAU,CAAC;AACpB,CAAC;AAED;SACgB,MAAM,GAAA;AACpB,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAEK,SAAU,mBAAmB,CACjC,oBAAoD,EAAA;AAEpD,IAAA,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,QAAQ,EAAE,CAAC;;AAEzD,IAAA,gBAAgB,CAAC,IAAI,CAAC,CAAC,YAAoB,KAAI;AAE/C,KAAC,CAAC,CAAC;AACH,IAAA,OAAO,gBAAgB,CAAC;AAC1B;;AC/CA;;;;;;;;;;;;;;;AAeG;AAIa,SAAA,YAAY,CAAC,KAAa,EAAE,KAAa,EAAA;IACvD,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC7C,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE;AAChC,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,6BAAA,iDAA2C,CAAC;KACvE;IAED,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACrC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,QAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACpB,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SACnC;KACF;AAED,IAAA,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC9B;;AClCA;;;;;;;;;;;;;;;AAeG;AAIH,IAAI,uBAAoD,CAAC;MAE5C,eAAe,CAAA;AAA5B,IAAA,WAAA,GAAA;;QAEE,IAAsB,CAAA,sBAAA,GAAG,IAAI,CAAC;;QAG9B,IAAqB,CAAA,qBAAA,GAAG,IAAI,CAAC;;QAG7B,IAAc,CAAA,cAAA,GAAG,KAAK,CAAC;;QAEvB,IAAkB,CAAA,kBAAA,GAAG,CAAC,CAAC;QACvB,IAA2B,CAAA,2BAAA,GAAG,CAAC,CAAC;;QAGhC,IAAc,CAAA,cAAA,GACZ,mEAAmE,CAAC;;;AAGtE,QAAA,IAAA,CAAA,sBAAsB,GAAG,YAAY,CACnC,kCAAkC,EAClC,iCAAiC,CAClC,CAAC;AAEF,QAAA,IAAA,CAAA,YAAY,GAAG,YAAY,CAAC,sBAAsB,EAAE,qBAAqB,CAAC,CAAC;;QAG3E,IAAS,CAAA,SAAA,GAAG,GAAG,CAAC;;QAGhB,IAAqB,CAAA,qBAAA,GAAG,KAAK,CAAC;QAC9B,IAAuB,CAAA,uBAAA,GAAG,KAAK,CAAC;;QAGhC,IAAgB,CAAA,gBAAA,GAAG,EAAE,CAAC;KAYvB;IAVC,qBAAqB,GAAA;AACnB,QAAA,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;KACvE;AAED,IAAA,OAAO,WAAW,GAAA;AAChB,QAAA,IAAI,uBAAuB,KAAK,SAAS,EAAE;AACzC,YAAA,uBAAuB,GAAG,IAAI,eAAe,EAAE,CAAC;SACjD;AACD,QAAA,OAAO,uBAAuB,CAAC;KAChC;AACF;;AClED;;;;;;;;;;;;;;;AAeG;AAYH,IAAY,eAIX,CAAA;AAJD,CAAA,UAAY,eAAe,EAAA;AACzB,IAAA,eAAA,CAAA,eAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW,CAAA;AACX,IAAA,eAAA,CAAA,eAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAW,CAAA;AACX,IAAA,eAAA,CAAA,eAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU,CAAA;AACZ,CAAC,EAJW,eAAe,KAAf,eAAe,GAI1B,EAAA,CAAA,CAAA,CAAA;AAsCD,MAAM,2BAA2B,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AACpE,MAAM,sBAAsB,GAAG,IAAI,MAAM,CAAC,gBAAgB,CAAC,CAAC;AAC5D,MAAM,yBAAyB,GAAG,EAAE,CAAC;AACrC,MAAM,0BAA0B,GAAG,GAAG,CAAC;SAEvB,sBAAsB,GAAA;IACpC,MAAM,SAAS,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC;IAC9C,IAAI,SAAS,aAAT,SAAS,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAT,SAAS,CAAE,aAAa,EAAE;AAC5B,QAAA,IAAI,SAAS,CAAC,aAAa,CAAC,UAAU,EAAE;YACtC,OAAsC,CAAA,sCAAA;SACvC;aAAM;YACL,OAAwC,CAAA,wCAAA;SACzC;KACF;SAAM;QACL,OAAuC,CAAA,uCAAA;KACxC;AACH,CAAC;SAEe,kBAAkB,GAAA;IAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;AAC5C,IAAA,MAAM,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAC;IACjD,QAAQ,eAAe;AACrB,QAAA,KAAK,SAAS;YACZ,OAAO,eAAe,CAAC,OAAO,CAAC;AACjC,QAAA,KAAK,QAAQ;YACX,OAAO,eAAe,CAAC,MAAM,CAAC;AAChC,QAAA;YACE,OAAO,eAAe,CAAC,OAAO,CAAC;KAClC;AACH,CAAC;SAEe,0BAA0B,GAAA;IACxC,MAAM,SAAS,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC;AAC9C,IAAA,MAAM,mBAAmB,GAAI,SAAqC,CAAC,UAAU,CAAC;AAC9E,IAAA,MAAM,aAAa,GACjB,mBAAmB,IAAI,mBAAmB,CAAC,aAAa,CAAC;IAC3D,QAAQ,aAAa;AACnB,QAAA,KAAK,SAAS;YACZ,OAAkD,CAAA,kDAAA;AACpD,QAAA,KAAK,IAAI;YACP,OAA6C,CAAA,6CAAA;AAC/C,QAAA,KAAK,IAAI;YACP,OAA6C,CAAA,6CAAA;AAC/C,QAAA,KAAK,IAAI;YACP,OAA6C,CAAA,6CAAA;AAC/C,QAAA;YACE,OAAuC,CAAA,uCAAA;KAC1C;AACH,CAAC;AAEK,SAAU,0BAA0B,CAAC,IAAY,EAAA;AACrD,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,yBAAyB,EAAE;AAChE,QAAA,OAAO,KAAK,CAAC;KACd;AACD,IAAA,MAAM,qBAAqB,GAAG,2BAA2B,CAAC,IAAI,CAAC,MAAM,IACnE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CACxB,CAAC;IACF,OAAO,CAAC,qBAAqB,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;AACxE,CAAC;AAEK,SAAU,2BAA2B,CAAC,KAAa,EAAA;IACvD,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,IAAI,0BAA0B,CAAC;AAC1E;;ACnIA;;;;;;;;;;;;;;;AAeG;AAKG,SAAU,QAAQ,CAAC,WAAwB,EAAA;;IAC/C,MAAM,KAAK,GAAG,CAAA,EAAA,GAAA,WAAW,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAC;IACzC,IAAI,CAAC,KAAK,EAAE;AACV,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,WAAA,2BAAqB,CAAC;KACjD;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAEK,SAAU,YAAY,CAAC,WAAwB,EAAA;;IACnD,MAAM,SAAS,GAAG,CAAA,EAAA,GAAA,WAAW,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,SAAS,CAAC;IACjD,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,eAAA,+BAAyB,CAAC;KACrD;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAEK,SAAU,SAAS,CAAC,WAAwB,EAAA;;IAChD,MAAM,MAAM,GAAG,CAAA,EAAA,GAAA,WAAW,CAAC,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAC;IAC3C,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,YAAA,4BAAsB,CAAC;KAClD;AACD,IAAA,OAAO,MAAM,CAAC;AAChB;;AC1CA;;;;;;;;;;;;;;;AAeG;AAgBH,MAAM,yBAAyB,GAAG,OAAO,CAAC;AAW1C;AACA;AACA,MAAM,eAAe,GAAoB;AACvC,IAAA,cAAc,EAAE,IAAI;CACrB,CAAC;AAoBF,MAAM,eAAe,GAAG,6BAA6B,CAAC;AAEtC,SAAA,SAAS,CACvB,qBAA4C,EAC5C,GAAW,EAAA;AAEX,IAAA,MAAM,MAAM,GAAG,eAAe,EAAE,CAAC;IACjC,IAAI,MAAM,EAAE;QACV,aAAa,CAAC,MAAM,CAAC,CAAC;AACtB,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AAED,IAAA,OAAO,eAAe,CAAC,qBAAqB,EAAE,GAAG,CAAC;SAC/C,IAAI,CAAC,aAAa,CAAC;SACnB,IAAI,CACH,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC;;AAE7B,IAAA,MAAK,GAAG,CACT,CAAC;AACN,CAAC;AAED,SAAS,eAAe,GAAA;IACtB,MAAM,YAAY,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,YAAY,CAAC;IACpD,IAAI,CAAC,YAAY,EAAE;QACjB,OAAO;KACR;IACD,MAAM,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC;IAC3E,IAAI,CAAC,YAAY,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,EAAE;QAC/C,OAAO;KACR;IAED,MAAM,iBAAiB,GAAG,YAAY,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;IACzE,IAAI,CAAC,iBAAiB,EAAE;QACtB,OAAO;KACR;AACD,IAAA,IAAI;QACF,MAAM,cAAc,GAAyB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAC3E,QAAA,OAAO,cAAc,CAAC;KACvB;AAAC,IAAA,OAAA,EAAA,EAAM;QACN,OAAO;KACR;AACH,CAAC;AAED,SAAS,WAAW,CAAC,MAAwC,EAAA;IAC3D,MAAM,YAAY,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,YAAY,CAAC;AACpD,IAAA,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,EAAE;QAC5B,OAAO;KACR;AAED,IAAA,YAAY,CAAC,OAAO,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IACvE,YAAY,CAAC,OAAO,CAClB,+BAA+B,EAC/B,MAAM,CACJ,IAAI,CAAC,GAAG,EAAE;AACR,QAAA,eAAe,CAAC,WAAW,EAAE,CAAC,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAClE,CACF,CAAC;AACJ,CAAC;AAED,MAAM,wBAAwB,GAC5B,kDAAkD,CAAC;AAErD,SAAS,eAAe,CACtB,qBAA4C,EAC5C,GAAW,EAAA;;AAGX,IAAA,OAAO,mBAAmB,CAAC,qBAAqB,CAAC,aAAa,CAAC;SAC5D,IAAI,CAAC,SAAS,IAAG;QAChB,MAAM,SAAS,GAAG,YAAY,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;QAC1D,MAAM,MAAM,GAAG,SAAS,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC;AACpD,QAAA,MAAM,cAAc,GAAG,CAAA,wDAAA,EAA2D,SAAS,CAAkC,+BAAA,EAAA,MAAM,EAAE,CAAC;AACtI,QAAA,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1C,YAAA,MAAM,EAAE,MAAM;YACd,OAAO,EAAE,EAAE,aAAa,EAAE,GAAG,eAAe,CAAA,CAAA,EAAI,SAAS,CAAA,CAAE,EAAE;;AAE7D,YAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;AACnB,gBAAA,eAAe,EAAE,GAAG;AACpB,gBAAA,qBAAqB,EAAE,SAAS;AAChC,gBAAA,MAAM,EAAE,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC;AAC3C,gBAAA,WAAW,EAAE,WAAW;AACxB,gBAAA,WAAW,EAAE,yBAAyB;aACvC,CAAC;;AAEH,SAAA,CAAC,CAAC;QACH,OAAO,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAG;AACpC,YAAA,IAAI,QAAQ,CAAC,EAAE,EAAE;AACf,gBAAA,OAAO,QAAQ,CAAC,IAAI,EAA0B,CAAC;aAChD;;AAED,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,oBAAA,2BAAqB,CAAC;AAClD,SAAC,CAAC,CAAC;AACL,KAAC,CAAC;SACD,KAAK,CAAC,MAAK;AACV,QAAA,aAAa,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;AAC7C,QAAA,OAAO,SAAS,CAAC;AACnB,KAAC,CAAC,CAAC;AACP,CAAC;AAED;;;;AAIG;AACH,SAAS,aAAa,CACpB,MAA6B,EAAA;IAE7B,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,OAAO,MAAM,CAAC;KACf;AACD,IAAA,MAAM,uBAAuB,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;AAC9D,IAAA,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC;AACrC,IAAA,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE;;;AAGrC,QAAA,uBAAuB,CAAC,cAAc;AACpC,YAAA,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,MAAM,CAAC;KAC1C;AAAM,SAAkD;;;AAGvD,QAAA,uBAAuB,CAAC,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC;KACzE;AACD,IAAA,IAAI,OAAO,CAAC,cAAc,EAAE;QAC1B,uBAAuB,CAAC,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;KACpE;AAAM,SAAA,IAAI,eAAe,CAAC,SAAS,EAAE;AACpC,QAAA,uBAAuB,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;KAC/D;AAED,IAAA,IAAI,OAAO,CAAC,oBAAoB,EAAE;AAChC,QAAA,uBAAuB,CAAC,cAAc,GAAG,OAAO,CAAC,oBAAoB,CAAC;KACvE;AAAM,SAAA,IAAI,eAAe,CAAC,cAAc,EAAE;AACzC,QAAA,uBAAuB,CAAC,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC;KACzE;;AAGD,IAAA,IAAI,OAAO,CAAC,qBAAqB,EAAE;AACjC,QAAA,uBAAuB,CAAC,YAAY,GAAG,OAAO,CAAC,qBAAqB,CAAC;KACtE;AAAM,SAAA,IAAI,eAAe,CAAC,YAAY,EAAE;AACvC,QAAA,uBAAuB,CAAC,YAAY,GAAG,eAAe,CAAC,YAAY,CAAC;KACrE;AAED,IAAA,IAAI,OAAO,CAAC,oCAAoC,KAAK,SAAS,EAAE;QAC9D,uBAAuB,CAAC,2BAA2B,GAAG,MAAM,CAC1D,OAAO,CAAC,oCAAoC,CAC7C,CAAC;KACH;AAAM,SAAA,IAAI,eAAe,CAAC,2BAA2B,KAAK,SAAS,EAAE;AACpE,QAAA,uBAAuB,CAAC,2BAA2B;YACjD,eAAe,CAAC,2BAA2B,CAAC;KAC/C;AACD,IAAA,IAAI,OAAO,CAAC,0BAA0B,KAAK,SAAS,EAAE;QACpD,uBAAuB,CAAC,kBAAkB,GAAG,MAAM,CACjD,OAAO,CAAC,0BAA0B,CACnC,CAAC;KACH;AAAM,SAAA,IAAI,eAAe,CAAC,kBAAkB,KAAK,SAAS,EAAE;AAC3D,QAAA,uBAAuB,CAAC,kBAAkB;YACxC,eAAe,CAAC,kBAAkB,CAAC;KACtC;;IAED,uBAAuB,CAAC,qBAAqB,GAAG,sBAAsB,CACpE,uBAAuB,CAAC,kBAAkB,CAC3C,CAAC;IACF,uBAAuB,CAAC,uBAAuB,GAAG,sBAAsB,CACtE,uBAAuB,CAAC,2BAA2B,CACpD,CAAC;AACF,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,WAAW,CAAC,MAAc,EAAA;IACjC,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACrC,CAAC;AAED,SAAS,sBAAsB,CAAC,YAAoB,EAAA;AAClD,IAAA,OAAO,IAAI,CAAC,MAAM,EAAE,IAAI,YAAY,CAAC;AACvC;;AC/OA;;;;;;;;;;;;;;;AAeG;AAaH,IAAI,oBAAoB,+CAAuC;AAE/D,IAAI,qBAAgD,CAAC;AAE/C,SAAU,wBAAwB,CACtC,qBAA4C,EAAA;AAE5C,IAAA,oBAAoB,sDAA8C;IAElE,qBAAqB;AACnB,QAAA,qBAAqB,IAAI,cAAc,CAAC,qBAAqB,CAAC,CAAC;AAEjE,IAAA,OAAO,qBAAqB,CAAC;AAC/B,CAAC;SAEe,iBAAiB,GAAA;IAC/B,OAAO,oBAAoB,8CAAsC;AACnE,CAAC;AAED,SAAS,cAAc,CACrB,qBAA4C,EAAA;AAE5C,IAAA,OAAO,wBAAwB,EAAE;SAC9B,IAAI,CAAC,MAAM,aAAa,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;SAC9D,IAAI,CAAC,GAAG,IAAI,SAAS,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;AAClD,SAAA,IAAI,CACH,MAAM,0BAA0B,EAAE,EAClC,MAAM,0BAA0B,EAAE,CACnC,CAAC;AACN,CAAC;AAED;;;AAGG;AACH,SAAS,wBAAwB,GAAA;IAC/B,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC;AAC5C,IAAA,OAAO,IAAI,OAAO,CAAC,OAAO,IAAG;QAC3B,IAAI,QAAQ,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE;YAClD,MAAM,OAAO,GAAG,MAAW;AACzB,gBAAA,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE;AACtC,oBAAA,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;AAC1D,oBAAA,OAAO,EAAE,CAAC;iBACX;AACH,aAAC,CAAC;AACF,YAAA,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;SACxD;aAAM;AACL,YAAA,OAAO,EAAE,CAAC;SACX;AACH,KAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,0BAA0B,GAAA;AACjC,IAAA,oBAAoB,4CAAoC;AAC1D;;AClFA;;;;;;;;;;;;;;;AAeG;AAMH,MAAM,wBAAwB,GAAG,EAAE,GAAG,IAAI,CAAC;AAC3C,MAAM,0BAA0B,GAAG,GAAG,GAAG,IAAI,CAAC;AAC9C,MAAM,2BAA2B,GAAG,IAAI,CAAC;AACzC,MAAM,uBAAuB,GAAG,CAAC,CAAC;AAElC,IAAI,cAAc,GAAG,uBAAuB,CAAC;AAyB7C;AAEA,IAAI,KAAK,GAAiB,EAAE,CAAC;AAE7B,IAAI,gBAAgB,GAAY,KAAK,CAAC;SAEtB,qBAAqB,GAAA;IACnC,IAAI,CAAC,gBAAgB,EAAE;QACrB,YAAY,CAAC,0BAA0B,CAAC,CAAC;QACzC,gBAAgB,GAAG,IAAI,CAAC;KACzB;AACH,CAAC;AAUD,SAAS,YAAY,CAAC,UAAkB,EAAA;IACtC,UAAU,CAAC,MAAK;;AAEd,QAAA,IAAI,cAAc,IAAI,CAAC,EAAE;YACvB,OAAO;SACR;AAED,QAAA,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACpB,YAAA,mBAAmB,EAAE,CAAC;SACvB;QACD,YAAY,CAAC,wBAAwB,CAAC,CAAC;KACxC,EAAE,UAAU,CAAC,CAAC;AACjB,CAAC;AAED,SAAS,mBAAmB,GAAA;;;;IAI1B,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,2BAA2B,CAAC,CAAC;;;IAI5D,MAAM,SAAS,GAAU,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK;QAC1C,4BAA4B,EAAE,GAAG,CAAC,OAAO;AACzC,QAAA,aAAa,EAAE,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC;AACrC,KAAA,CAAC,CAAC,CAAC;AAEJ,IAAA,MAAM,IAAI,GAA4B;AACpC,QAAA,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;AACnC,QAAA,WAAW,EAAE;YACX,WAAW,EAAE,CAAC;AACd,YAAA,cAAc,EAAE,EAAE;AACnB,SAAA;AACD,QAAA,UAAU,EAAE,eAAe,CAAC,WAAW,EAAE,CAAC,SAAS;QACnD,SAAS;KACV,CAAC;;IAGF,gBAAgB,CAAC,IAAI,CAAC;SACnB,IAAI,CAAC,MAAK;QACT,cAAc,GAAG,uBAAuB,CAAC;AAC3C,KAAC,CAAC;SACD,KAAK,CAAC,MAAK;;;QAGV,KAAK,GAAG,CAAC,GAAG,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC;AAC9B,QAAA,cAAc,EAAE,CAAC;AACjB,QAAA,aAAa,CAAC,IAAI,CAAC,eAAe,cAAc,CAAA,CAAA,CAAG,CAAC,CAAC;QACrD,YAAY,CAAC,wBAAwB,CAAC,CAAC;AACzC,KAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,gBAAgB,CAAC,IAA6B,EAAA;IACrD,MAAM,kBAAkB,GACtB,eAAe,CAAC,WAAW,EAAE,CAAC,qBAAqB,EAAE,CAAC;IACxD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;IAElC,OAAO,SAAS,CAAC,UAAU,IAAI,SAAS,CAAC,UAAU,CAAC,kBAAkB,EAAE,IAAI,CAAC;AAC3E,UAAE,OAAO,CAAC,OAAO,EAAE;AACnB,UAAE,KAAK,CAAC,kBAAkB,EAAE;AACxB,YAAA,MAAM,EAAE,MAAM;YACd,IAAI;AACJ,YAAA,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC,IAAI,EAAE,CAAC;AAChB,CAAC;AAED,SAAS,UAAU,CAAC,GAAe,EAAA;IACjC,IAAI,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;AAClC,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,gBAAA,gCAA0B,CAAC;KACtD;;AAED,IAAA,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC,CAAC;AAC1B,CAAC;AAED;SACgB,gBAAgB;AAC9B;AACA,UAAsC,EAAA;AAEtC,IAAA,OAAO,CAAC,GAAG,IAAI,KAAI;AACjB,QAAA,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;AACpC,QAAA,UAAU,CAAC;YACT,OAAO;AACP,YAAA,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;AACtB,SAAA,CAAC,CAAC;AACL,KAAC,CAAC;AACJ,CAAC;AAED;;;AAGG;SACa,iBAAiB,GAAA;AAC/B,IAAA,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,QAAA,mBAAmB,EAAE,CAAC;KACvB;AACH;;ACxKA;;;;;;;;;;;;;;;AAeG;AA+EH,IAAI,MAAc,CAAC;AACnB;AACA;AACA,SAAS,OAAO,CACd,QAAgC,EAChC,YAA0B,EAAA;IAE1B,IAAI,CAAC,MAAM,EAAE;AACX,QAAA,MAAM,GAAG;AACP,YAAA,IAAI,EAAE,gBAAgB,CAAC,UAAU,CAAC;AAClC,YAAA,KAAK,EAAE,iBAAiB;SACzB,CAAC;KACH;AACD,IAAA,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;AACtC,CAAC;AAEK,SAAU,QAAQ,CAAC,KAAY,EAAA;AACnC,IAAA,MAAM,eAAe,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;;IAEtD,IAAI,CAAC,eAAe,CAAC,sBAAsB,IAAI,KAAK,CAAC,MAAM,EAAE;QAC3D,OAAO;KACR;;IAED,IAAI,CAAC,eAAe,CAAC,qBAAqB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QAC3D,OAAO;KACR;;IAED,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,qBAAqB,EAAE,EAAE;QAC9C,OAAO;KACR;IAED,IAAI,iBAAiB,EAAE,EAAE;QACvB,YAAY,CAAC,KAAK,CAAC,CAAC;KACrB;SAAM;;;QAGL,wBAAwB,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,IAAI,CACxD,MAAM,YAAY,CAAC,KAAK,CAAC,EACzB,MAAM,YAAY,CAAC,KAAK,CAAC,CAC1B,CAAC;KACH;AACH,CAAC;SAEe,SAAS,GAAA;IACvB,IAAI,MAAM,EAAE;QACV,MAAM,CAAC,KAAK,EAAE,CAAC;KAChB;AACH,CAAC;AAED,SAAS,YAAY,CAAC,KAAY,EAAA;AAChC,IAAA,IAAI,CAAC,MAAM,EAAE,EAAE;QACb,OAAO;KACR;AAED,IAAA,MAAM,eAAe,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;IACtD,IACE,CAAC,eAAe,CAAC,cAAc;AAC/B,QAAA,CAAC,eAAe,CAAC,qBAAqB,EACtC;QACA,OAAO;KACR;IAED,OAAO,CAAC,KAAK,EAAA,CAAA,0BAAqB,CAAC;AACrC,CAAC;AAEK,SAAU,iBAAiB,CAAC,cAA8B,EAAA;AAC9D,IAAA,MAAM,eAAe,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;;AAEtD,IAAA,IAAI,CAAC,eAAe,CAAC,sBAAsB,EAAE;QAC3C,OAAO;KACR;;;AAID,IAAA,MAAM,iBAAiB,GAAG,cAAc,CAAC,GAAG,CAAC;;;AAI7C,IAAA,MAAM,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACpE,IAAA,MAAM,aAAa,GAAG,eAAe,CAAC,sBAAsB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3E,IACE,iBAAiB,KAAK,cAAc;QACpC,iBAAiB,KAAK,aAAa,EACnC;QACA,OAAO;KACR;IAED,IACE,CAAC,eAAe,CAAC,cAAc;AAC/B,QAAA,CAAC,eAAe,CAAC,uBAAuB,EACxC;QACA,OAAO;KACR;IAED,OAAO,CAAC,cAAc,EAAA,CAAA,mCAA8B,CAAC;AACvD,CAAC;AAED,SAAS,UAAU,CACjB,QAAgC,EAChC,YAA0B,EAAA;IAE1B,IAAI,YAAY,KAAgC,CAAA,oCAAE;AAChD,QAAA,OAAO,uBAAuB,CAAC,QAA0B,CAAC,CAAC;KAC5D;AACD,IAAA,OAAO,cAAc,CAAC,QAAiB,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,uBAAuB,CAAC,cAA8B,EAAA;AAC7D,IAAA,MAAM,oBAAoB,GAAyB;QACjD,GAAG,EAAE,cAAc,CAAC,GAAG;AACvB,QAAA,WAAW,EAAE,cAAc,CAAC,UAAU,IAAI,CAAC;AAC3C,QAAA,kBAAkB,EAAE,GAAG;QACvB,sBAAsB,EAAE,cAAc,CAAC,oBAAoB;QAC3D,oBAAoB,EAAE,cAAc,CAAC,WAAW;QAChD,6BAA6B,EAAE,cAAc,CAAC,yBAAyB;QACvE,6BAA6B,EAAE,cAAc,CAAC,yBAAyB;KACxE,CAAC;AACF,IAAA,MAAM,UAAU,GAAmB;QACjC,gBAAgB,EAAE,kBAAkB,CAClC,cAAc,CAAC,qBAAqB,CAAC,GAAG,CACzC;AACD,QAAA,sBAAsB,EAAE,oBAAoB;KAC7C,CAAC;AACF,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,cAAc,CAAC,KAAY,EAAA;AAClC,IAAA,MAAM,WAAW,GAAgB;QAC/B,IAAI,EAAE,KAAK,CAAC,IAAI;QAChB,OAAO,EAAE,KAAK,CAAC,MAAM;QACrB,oBAAoB,EAAE,KAAK,CAAC,WAAW;QACvC,WAAW,EAAE,KAAK,CAAC,UAAU;KAC9B,CAAC;AAEF,IAAA,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AAC5C,QAAA,WAAW,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;KACvC;AACD,IAAA,MAAM,gBAAgB,GAAG,KAAK,CAAC,aAAa,EAAE,CAAC;IAC/C,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9C,QAAA,WAAW,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;KAClD;AAED,IAAA,MAAM,UAAU,GAAiB;QAC/B,gBAAgB,EAAE,kBAAkB,CAAC,KAAK,CAAC,qBAAqB,CAAC,GAAG,CAAC;AACrE,QAAA,YAAY,EAAE,WAAW;KAC1B,CAAC;AACF,IAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,kBAAkB,CAAC,WAAwB,EAAA;IAClD,OAAO;AACL,QAAA,aAAa,EAAE,QAAQ,CAAC,WAAW,CAAC;QACpC,eAAe,EAAE,MAAM,EAAE;AACzB,QAAA,YAAY,EAAE;AACZ,YAAA,WAAW,EAAE,WAAW;AACxB,YAAA,QAAQ,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE;YACpC,qBAAqB,EAAE,sBAAsB,EAAE;YAC/C,gBAAgB,EAAE,kBAAkB,EAAE;YACtC,yBAAyB,EAAE,0BAA0B,EAAE;AACxD,SAAA;AACD,QAAA,yBAAyB,EAAE,CAAC;KAC7B,CAAC;AACJ,CAAC;AAED;;AClQA;;;;;;;;;;;;;;;AAeG;AAmCa,SAAA,yBAAyB,CACvC,qBAA4C,EAC5C,KAAuB,EAAA;IAEvB,MAAM,gBAAgB,GAAG,KAAkC,CAAC;IAC5D,IAAI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,aAAa,KAAK,SAAS,EAAE;QACrE,OAAO;KACR;IACD,MAAM,UAAU,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,aAAa,EAAE,CAAC;AACrD,IAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAC5B,CAAC,gBAAgB,CAAC,SAAS,GAAG,UAAU,IAAI,IAAI,CACjD,CAAC;AACF,IAAA,MAAM,yBAAyB,GAAG,gBAAgB,CAAC,aAAa;AAC9D,UAAE,IAAI,CAAC,KAAK,CACR,CAAC,gBAAgB,CAAC,aAAa,GAAG,gBAAgB,CAAC,SAAS,IAAI,IAAI,CACrE;UACD,SAAS,CAAC;AACd,IAAA,MAAM,yBAAyB,GAAG,IAAI,CAAC,KAAK,CAC1C,CAAC,gBAAgB,CAAC,WAAW,GAAG,gBAAgB,CAAC,SAAS,IAAI,IAAI,CACnE,CAAC;;AAEF,IAAA,MAAM,GAAG,GAAG,gBAAgB,CAAC,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,IAAA,MAAM,cAAc,GAAmB;QACrC,qBAAqB;QACrB,GAAG;QACH,oBAAoB,EAAE,gBAAgB,CAAC,YAAY;QACnD,WAAW;QACX,yBAAyB;QACzB,yBAAyB;KAC1B,CAAC;IAEF,iBAAiB,CAAC,cAAc,CAAC,CAAC;AACpC;;AClFA;;;;;;;;;;;;;;;AAeG;AAaH,MAAM,sBAAsB,GAAG,GAAG,CAAC;AACnC,MAAM,oBAAoB,GAAG,GAAG,CAAC;AACjC,MAAM,UAAU,GAAG;IACjB,wBAAwB;IACxB,mCAAmC;IACnC,8BAA8B;IAC9B,oCAAoC;IACpC,mCAAmC;IACnC,qCAAqC;CACtC,CAAC;AAEF;;;AAGG;AACa,SAAA,iBAAiB,CAAC,IAAY,EAAE,SAAkB,EAAA;AAChE,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,sBAAsB,EAAE;AAC7D,QAAA,OAAO,KAAK,CAAC;KACd;IACD,QACE,CAAC,SAAS;AACR,QAAA,SAAS,CAAC,UAAU,CAAC,0BAA0B,CAAC;QAChD,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/B,QAAA,CAAC,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,EACtC;AACJ,CAAC;AAED;;;;;AAKG;AACG,SAAU,2BAA2B,CAAC,aAAqB,EAAA;IAC/D,MAAM,cAAc,GAAW,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACzD,IAAA,IAAI,cAAc,GAAG,aAAa,EAAE;AAClC,QAAA,aAAa,CAAC,IAAI,CAChB,6DAA6D,cAAc,CAAA,CAAA,CAAG,CAC/E,CAAC;KACH;AACD,IAAA,OAAO,cAAc,CAAC;AACxB;;ACrEA;;;;;;;;;;;;;;;AAeG;MAsCU,KAAK,CAAA;AAYhB;;;;;;;AAOG;AACH,IAAA,WAAA,CACW,qBAA4C,EAC5C,IAAY,EACZ,MAAS,GAAA,KAAK,EACvB,gBAAyB,EAAA;QAHhB,IAAqB,CAAA,qBAAA,GAArB,qBAAqB,CAAuB;QAC5C,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QACZ,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;AAtBjB,QAAA,IAAA,CAAA,KAAK,GAAwC,CAAA,gCAAA;QAG7C,IAAgB,CAAA,gBAAA,GAA8B,EAAE,CAAC;QACzD,IAAQ,CAAA,QAAA,GAAsC,EAAE,CAAC;AACzC,QAAA,IAAA,CAAA,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;AACxB,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC;AAmBrD,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,IAAI,CAAC,cAAc,GAAG,CAAA,EAAG,uBAAuB,CAAI,CAAA,EAAA,IAAI,CAAC,QAAQ,CAAI,CAAA,EAAA,IAAI,CAAC,IAAI,EAAE,CAAC;AACjF,YAAA,IAAI,CAAC,aAAa,GAAG,CAAA,EAAG,sBAAsB,CAAI,CAAA,EAAA,IAAI,CAAC,QAAQ,CAAI,CAAA,EAAA,IAAI,CAAC,IAAI,EAAE,CAAC;AAC/E,YAAA,IAAI,CAAC,YAAY;gBACf,gBAAgB;oBAChB,CAAG,EAAA,oBAAoB,CAAI,CAAA,EAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAA,CAAE,CAAC;YAE1D,IAAI,gBAAgB,EAAE;;;gBAGpB,IAAI,CAAC,qBAAqB,EAAE,CAAC;aAC9B;SACF;KACF;AAED;;AAEG;IACH,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,KAAK,KAAA,CAAA,iCAA+B;YAC3C,MAAM,aAAa,CAAC,MAAM,CAAiC,eAAA,uCAAA;gBACzD,SAAS,EAAE,IAAI,CAAC,IAAI;AACrB,aAAA,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACnC,IAAI,CAAC,KAAK,GAAA,CAAA,0BAAsB;KACjC;AAED;;;AAGG;IACH,IAAI,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,KAAK,KAAA,CAAA,2BAAyB;YACrC,MAAM,aAAa,CAAC,MAAM,CAAiC,eAAA,uCAAA;gBACzD,SAAS,EAAE,IAAI,CAAC,IAAI;AACrB,aAAA,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,KAAK,GAAA,CAAA,6BAAyB;QACnC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAClC,QAAA,IAAI,CAAC,GAAG,CAAC,OAAO,CACd,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,aAAa,CACnB,CAAC;QACF,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,QAAQ,CAAC,IAAI,CAAC,CAAC;KAChB;AAED;;;;;;AAMG;AACH,IAAA,MAAM,CACJ,SAAiB,EACjB,QAAgB,EAChB,OAGC,EAAA;AAED,QAAA,IAAI,SAAS,IAAI,CAAC,EAAE;YAClB,MAAM,aAAa,CAAC,MAAM,CAAyC,6BAAA,+CAAA;gBACjE,SAAS,EAAE,IAAI,CAAC,IAAI;AACrB,aAAA,CAAC,CAAC;SACJ;AACD,QAAA,IAAI,QAAQ,IAAI,CAAC,EAAE;YACjB,MAAM,aAAa,CAAC,MAAM,CAAuC,4BAAA,6CAAA;gBAC/D,SAAS,EAAE,IAAI,CAAC,IAAI;AACrB,aAAA,CAAC,CAAC;SACJ;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;AAChD,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE;AACjC,YAAA,IAAI,CAAC,gBAAgB,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,OAAO,CAAC,UAAU,CAAE,CAAC;SACnD;AACD,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;AAC9B,YAAA,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACrD,gBAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;oBAC/C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,KAAK,CACpC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CACpC,CAAC;iBACH;aACF;SACF;QACD,QAAQ,CAAC,IAAI,CAAC,CAAC;KAChB;AAED;;;;;;AAMG;AACH,IAAA,eAAe,CAAC,OAAe,EAAE,YAAY,GAAG,CAAC,EAAA;QAC/C,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,SAAS,EAAE;AACxC,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;SACvC;aAAM;AACL,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC;SAChE;KACF;AAED;;;;;AAKG;IACH,SAAS,CAAC,OAAe,EAAE,YAAoB,EAAA;QAC7C,IAAI,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AACzC,YAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,2BAA2B,CAAC,YAAY,KAAA,IAAA,IAAZ,YAAY,KAAZ,KAAA,CAAA,GAAA,YAAY,GAAI,CAAC,CAAC,CAAC;SACzE;aAAM;YACL,MAAM,aAAa,CAAC,MAAM,CAAuC,4BAAA,6CAAA;AAC/D,gBAAA,gBAAgB,EAAE,OAAO;AAC1B,aAAA,CAAC,CAAC;SACJ;KACF;AAED;;;;AAIG;AACH,IAAA,SAAS,CAAC,OAAe,EAAA;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KACpC;AAED;;;;AAIG;IACH,YAAY,CAAC,IAAY,EAAE,KAAa,EAAA;AACtC,QAAA,MAAM,WAAW,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAC;AACrD,QAAA,MAAM,YAAY,GAAG,2BAA2B,CAAC,KAAK,CAAC,CAAC;AACxD,QAAA,IAAI,WAAW,IAAI,YAAY,EAAE;AAC/B,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YACpC,OAAO;SACR;;QAED,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,aAAa,CAAC,MAAM,CAAmC,wBAAA,yCAAA;AAC3D,gBAAA,aAAa,EAAE,IAAI;AACpB,aAAA,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,YAAY,EAAE;YACjB,MAAM,aAAa,CAAC,MAAM,CAAoC,yBAAA,0CAAA;AAC5D,gBAAA,cAAc,EAAE,KAAK;AACtB,aAAA,CAAC,CAAC;SACJ;KACF;AAED;;;AAGG;AACH,IAAA,YAAY,CAAC,IAAY,EAAA;AACvB,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;KACpC;AAED,IAAA,eAAe,CAAC,IAAY,EAAA;QAC1B,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;YAC7C,OAAO;SACR;AACD,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;KACpC;IAED,aAAa,GAAA;QACX,OAAY,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,gBAAgB,CAAG,CAAA;KACrC;AAEO,IAAA,YAAY,CAAC,SAAiB,EAAA;AACpC,QAAA,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;KAC9B;AAEO,IAAA,WAAW,CAAC,QAAgB,EAAA;AAClC,QAAA,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;KAC5B;AAED;;;AAGG;IACK,qBAAqB,GAAA;AAC3B,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACxE,MAAM,gBAAgB,GAAG,kBAAkB,IAAI,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACrE,IAAI,gBAAgB,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;YAC/D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAC3B,CAAC,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,IAAI,CAC/D,CAAC;SACH;KACF;AAED;;;;;AAKG;IACH,OAAO,cAAc,CACnB,qBAA4C,EAC5C,iBAAgD,EAChD,YAAgC,EAChC,eAAgC,EAChC,eAAwB,EAAA;QAExB,MAAM,KAAK,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAC;QACzC,IAAI,CAAC,KAAK,EAAE;YACV,OAAO;SACR;AACD,QAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,qBAAqB,EACrB,0BAA0B,GAAG,KAAK,EAClC,IAAI,CACL,CAAC;AACF,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC,CAAC;AAC1E,QAAA,KAAK,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;;AAGjC,QAAA,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,CAAC,CAAC,EAAE;AAC7C,YAAA,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC;AACpE,YAAA,KAAK,CAAC,SAAS,CACb,gBAAgB,EAChB,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC,CACvD,CAAC;AACF,YAAA,KAAK,CAAC,SAAS,CACb,0BAA0B,EAC1B,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,wBAAwB,GAAG,IAAI,CAAC,CACjE,CAAC;AACF,YAAA,KAAK,CAAC,SAAS,CACb,cAAc,EACd,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,CACrD,CAAC;SACH;QAED,MAAM,WAAW,GAAG,aAAa,CAAC;QAClC,MAAM,sBAAsB,GAAG,wBAAwB,CAAC;QACxD,IAAI,YAAY,EAAE;AAChB,YAAA,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAClC,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,WAAW,CAChD,CAAC;AACF,YAAA,IAAI,UAAU,IAAI,UAAU,CAAC,SAAS,EAAE;AACtC,gBAAA,KAAK,CAAC,SAAS,CACb,wBAAwB,EACxB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,CACxC,CAAC;aACH;AACD,YAAA,MAAM,oBAAoB,GAAG,YAAY,CAAC,IAAI,CAC5C,WAAW,IAAI,WAAW,CAAC,IAAI,KAAK,sBAAsB,CAC3D,CAAC;AACF,YAAA,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,SAAS,EAAE;AAC1D,gBAAA,KAAK,CAAC,SAAS,CACb,mCAAmC,EACnC,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,SAAS,GAAG,IAAI,CAAC,CAClD,CAAC;aACH;YAED,IAAI,eAAe,EAAE;AACnB,gBAAA,KAAK,CAAC,SAAS,CACb,8BAA8B,EAC9B,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,CACnC,CAAC;aACH;SACF;AAED,QAAA,IAAI,CAAC,iBAAiB,CACpB,KAAK,EACL,oCAAoC,EACpC,uCAAuC,EACvC,eAAe,CAAC,GAAG,CACpB,CAAC;AACF,QAAA,IAAI,CAAC,iBAAiB,CACpB,KAAK,EACL,mCAAmC,EACnC,sCAAsC,EACtC,eAAe,CAAC,GAAG,CACpB,CAAC;AACF,QAAA,IAAI,CAAC,iBAAiB,CACpB,KAAK,EACL,qCAAqC,EACrC,wCAAwC,EACxC,eAAe,CAAC,GAAG,CACpB,CAAC;;;QAIF,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChB,QAAA,SAAS,EAAE,CAAC;KACb;IAED,OAAO,iBAAiB,CACtB,KAAY,EACZ,SAAiB,EACjB,YAAoB,EACpB,MAAwB,EAAA;QAExB,IAAI,MAAM,EAAE;AACV,YAAA,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;AAC5D,YAAA,IAAI,MAAM,CAAC,kBAAkB,EAAE;gBAC7B,KAAK,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,kBAAkB,CAAC,CAAC;aAC7D;SACF;KACF;AAED,IAAA,OAAO,qBAAqB,CAC1B,qBAA4C,EAC5C,WAAmB,EAAA;AAEnB,QAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,qBAAqB,EACrB,WAAW,EACX,KAAK,EACL,WAAW,CACZ,CAAC;QACF,QAAQ,CAAC,KAAK,CAAC,CAAC;KACjB;AACF;;ACjZD;;;;;;;;;;;;;;;AAeG;AAiBH,IAAI,eAAe,GAAoB,EAAE,CAAC;AAC1C,IAAI,iBAAiB,GAAY,KAAK,CAAC;AACvC,IAAI,eAAmC,CAAC;AAElC,SAAU,iBAAiB,CAC/B,qBAA4C,EAAA;;AAG5C,IAAA,IAAI,CAAC,MAAM,EAAE,EAAE;QACb,OAAO;KACR;;;;IAID,UAAU,CAAC,MAAM,cAAc,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3D,UAAU,CAAC,MAAM,oBAAoB,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,UAAU,CAAC,MAAM,qBAAqB,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC;AACpE,CAAC;AAED,SAAS,oBAAoB,CAC3B,qBAA4C,EAAA;AAE5C,IAAA,MAAM,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;IAC9B,MAAM,SAAS,GAAG,GAAG,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;AACnD,IAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AAChC,QAAA,yBAAyB,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;KAC5D;AACD,IAAA,GAAG,CAAC,aAAa,CAAC,UAAU,EAAE,KAAK,IACjC,yBAAyB,CAAC,qBAAqB,EAAE,KAAK,CAAC,CACxD,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CAAC,qBAA4C,EAAA;AAClE,IAAA,MAAM,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;;AAE9B,IAAA,IAAI,YAAY,IAAI,MAAM,EAAE;AAC1B,QAAA,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE,MACxC,YAAY,CAAC,qBAAqB,CAAC,CACpC,CAAC;KACH;SAAM;AACL,QAAA,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MACtC,YAAY,CAAC,qBAAqB,CAAC,CACpC,CAAC;KACH;IACD,GAAG,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,MAAK;QACrD,IAAI,GAAG,CAAC,QAAQ,CAAC,eAAe,KAAK,QAAQ,EAAE;YAC7C,YAAY,CAAC,qBAAqB,CAAC,CAAC;SACrC;AACH,KAAC,CAAC,CAAC;AAEH,IAAA,IAAI,GAAG,CAAC,iBAAiB,EAAE;AACzB,QAAA,GAAG,CAAC,iBAAiB,CAAC,CAAC,GAAW,KAAI;YACpC,eAAe,GAAG,GAAG,CAAC;AACxB,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,GAAG,CAAC,KAAK,CAAC,CAAC,MAAgC,KAAI;;QAC7C,eAAe,CAAC,GAAG,GAAG;YACpB,KAAK,EAAE,MAAM,CAAC,KAAK;AACnB,YAAA,kBAAkB,EAAE,CAAA,EAAA,GAAA,MAAM,CAAC,WAAW,0CAAE,OAAO;SAChD,CAAC;AACJ,KAAC,CAAC,CAAC;AACH,IAAA,GAAG,CAAC,KAAK,CAAC,CAAC,MAAgC,KAAI;;QAC7C,eAAe,CAAC,GAAG,GAAG;YACpB,KAAK,EAAE,MAAM,CAAC,KAAK;AACnB,YAAA,kBAAkB,EAAE,CAAA,EAAA,GAAA,MAAM,CAAC,WAAW,0CAAE,kBAAkB;SAC3D,CAAC;AACJ,KAAC,CAAC,CAAC;AACH,IAAA,GAAG,CAAC,KAAK,CAAC,CAAC,MAAgC,KAAI;;QAC7C,eAAe,CAAC,GAAG,GAAG;YACpB,KAAK,EAAE,MAAM,CAAC,KAAK;AACnB,YAAA,kBAAkB,EAAE,CAAA,EAAA,GAAA,MAAM,CAAC,WAAW,0CAAE,iBAAiB;SAC1D,CAAC;AACJ,KAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,qBAAqB,CAC5B,qBAA4C,EAAA;AAE5C,IAAA,MAAM,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;;IAE9B,MAAM,QAAQ,GAAG,GAAG,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;AACjD,IAAA,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AAC9B,QAAA,qBAAqB,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC;KACvD;;AAED,IAAA,GAAG,CAAC,aAAa,CAAC,SAAS,EAAE,KAAK,IAChC,qBAAqB,CAAC,qBAAqB,EAAE,KAAK,CAAC,CACpD,CAAC;AACJ,CAAC;AAED,SAAS,qBAAqB,CAC5B,qBAA4C,EAC5C,OAAyB,EAAA;AAEzB,IAAA,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC;;;IAGjC,IACE,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,oBAAoB,CAAC,MAAM,CAAC;AACrD,QAAA,oBAAoB,EACpB;QACA,OAAO;KACR;AACD,IAAA,KAAK,CAAC,qBAAqB,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;AAClE,CAAC;AAED,SAAS,YAAY,CAAC,qBAA4C,EAAA;IAChE,IAAI,CAAC,iBAAiB,EAAE;QACtB,iBAAiB,GAAG,IAAI,CAAC;AACzB,QAAA,MAAM,GAAG,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;QAC9B,MAAM,iBAAiB,GAAG,GAAG,CAAC,gBAAgB,CAC5C,YAAY,CACoB,CAAC;QACnC,MAAM,YAAY,GAAG,GAAG,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;;;QAInD,UAAU,CAAC,MAAK;AACd,YAAA,KAAK,CAAC,cAAc,CAClB,qBAAqB,EACrB,iBAAiB,EACjB,YAAY,EACZ,eAAe,EACf,eAAe,CAChB,CAAC;SACH,EAAE,CAAC,CAAC,CAAC;KACP;AACH;;AChKA;;;;;;;;;;;;;;;AAeG;MAaU,qBAAqB,CAAA;IAGhC,WACW,CAAA,GAAgB,EAChB,aAA6C,EAAA;QAD7C,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAChB,IAAa,CAAA,aAAA,GAAb,aAAa,CAAgC;QAJhD,IAAW,CAAA,WAAA,GAAY,KAAK,CAAC;KAKjC;AAEJ;;;;;;;;AAQG;AACH,IAAA,KAAK,CAAC,QAA8B,EAAA;AAClC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,OAAO;SACR;QAED,IAAI,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAE,qBAAqB,MAAK,SAAS,EAAE;AACjD,YAAA,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC,qBAAqB,CAAC;SAC7D;QACD,IAAI,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAE,sBAAsB,MAAK,SAAS,EAAE;AAClD,YAAA,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC,sBAAsB,CAAC;SAC/D;QAED,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC,qBAAqB,EAAE,EAAE;AAC7C,YAAA,yBAAyB,EAAE;iBACxB,IAAI,CAAC,WAAW,IAAG;gBAClB,IAAI,WAAW,EAAE;AACf,oBAAA,qBAAqB,EAAE,CAAC;oBACxB,wBAAwB,CAAC,IAAI,CAAC,CAAC,IAAI,CACjC,MAAM,iBAAiB,CAAC,IAAI,CAAC,EAC7B,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAC9B,CAAC;AACF,oBAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;iBACzB;AACH,aAAC,CAAC;iBACD,KAAK,CAAC,KAAK,IAAG;AACb,gBAAA,aAAa,CAAC,IAAI,CAAC,0CAA0C,KAAK,CAAA,CAAE,CAAC,CAAC;AACxE,aAAC,CAAC,CAAC;SACN;aAAM;YACL,aAAa,CAAC,IAAI,CAChB,oEAAoE;AAClE,gBAAA,iDAAiD,CACpD,CAAC;SACH;KACF;IAED,IAAI,sBAAsB,CAAC,GAAY,EAAA;AACrC,QAAA,eAAe,CAAC,WAAW,EAAE,CAAC,sBAAsB,GAAG,GAAG,CAAC;KAC5D;AACD,IAAA,IAAI,sBAAsB,GAAA;AACxB,QAAA,OAAO,eAAe,CAAC,WAAW,EAAE,CAAC,sBAAsB,CAAC;KAC7D;IAED,IAAI,qBAAqB,CAAC,GAAY,EAAA;AACpC,QAAA,eAAe,CAAC,WAAW,EAAE,CAAC,qBAAqB,GAAG,GAAG,CAAC;KAC3D;AACD,IAAA,IAAI,qBAAqB,GAAA;AACvB,QAAA,OAAO,eAAe,CAAC,WAAW,EAAE,CAAC,qBAAqB,CAAC;KAC5D;AACF;;AC7FD;;;;;AAKG;AA6CH,MAAM,kBAAkB,GAAG,WAAW,CAAC;AAEvC;;;;AAIG;AACa,SAAA,cAAc,CAC5B,GAAA,GAAmB,MAAM,EAAE,EAAA;AAE3B,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;AAClD,IAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,EAA2B,CAAC;AACtE,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;;;AAKG;AACa,SAAA,qBAAqB,CACnC,GAAgB,EAChB,QAA8B,EAAA;AAE9B,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;;;AAIlD,IAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,QAAA,MAAM,gBAAgB,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;AACjD,QAAA,MAAM,eAAe,GAAG,QAAQ,CAAC,UAAU,EAAyB,CAAC;AACrE,QAAA,IAAI,SAAS,CAAC,eAAe,EAAE,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAA,KAAA,CAAA,GAAR,QAAQ,GAAI,EAAE,CAAC,EAAE;AAC9C,YAAA,OAAO,gBAAgB,CAAC;SACzB;aAAM;AACL,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,qBAAA,qCAA+B,CAAC;SAC3D;KACF;AAED,IAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC;AACvC,QAAA,OAAO,EAAE,QAAQ;AAClB,KAAA,CAA0B,CAAC;AAC5B,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;;;;AAKG;AACa,SAAA,KAAK,CACnB,WAAgC,EAChC,IAAY,EAAA;AAEZ,IAAA,WAAW,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC;AAC9C,IAAA,OAAO,IAAI,KAAK,CAAC,WAAoC,EAAE,IAAI,CAAC,CAAC;AAC/D,CAAC;AAED,MAAM,OAAO,GAAmC,CAC9C,SAA6B,EAC7B,EAAE,OAAO,EAAE,QAAQ,EAAqC,KACtD;;IAEF,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;IACxD,MAAM,aAAa,GAAG,SAAS;SAC5B,WAAW,CAAC,wBAAwB,CAAC;AACrC,SAAA,YAAY,EAAE,CAAC;AAElB,IAAA,IAAI,GAAG,CAAC,IAAI,KAAK,kBAAkB,EAAE;AACnC,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,gBAAA,gCAA0B,CAAC;KACtD;AACD,IAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACjC,QAAA,MAAM,aAAa,CAAC,MAAM,CAAA,WAAA,2BAAqB,CAAC;KACjD;IACD,QAAQ,CAAC,MAAM,CAAC,CAAC;IACjB,MAAM,YAAY,GAAG,IAAI,qBAAqB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;AACnE,IAAA,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAE7B,IAAA,OAAO,YAAY,CAAC;AACtB,CAAC,CAAC;AAEF,SAAS,mBAAmB,GAAA;IAC1B,kBAAkB,CAChB,IAAI,SAAS,CAAC,aAAa,EAAE,OAAO,EAAuB,QAAA,4BAAA,CAC5D,CAAC;AACF,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;;AAE/B,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAkB,CAAC,CAAC;AACrD,CAAC;AAED,mBAAmB,EAAE;;;;"}