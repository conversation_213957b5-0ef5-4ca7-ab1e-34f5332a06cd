import { StorageError } from './error';
/**
 * Function that is called once for each value in a stream of values.
 */
export type NextFn<T> = (value: T) => void;
/**
 * A function that is called with a `StorageError`
 * if the event stream ends due to an error.
 */
export type ErrorFn = (error: StorageError) => void;
/**
 * A function that is called if the event stream ends normally.
 */
export type CompleteFn = () => void;
/**
 * Unsubscribes from a stream.
 */
export type Unsubscribe = () => void;
/**
 * An observer identical to the `Observer` defined in packages/util except the
 * error passed into the ErrorFn is specifically a `StorageError`.
 */
export interface StorageObserver<T> {
    /**
     * Function that is called once for each value in the event stream.
     */
    next?: NextFn<T>;
    /**
     * A function that is called with a `StorageError`
     * if the event stream ends due to an error.
     */
    error?: ErrorFn;
    /**
     * A function that is called if the event stream ends normally.
     */
    complete?: CompleteFn;
}
/**
 * Subscribes to an event stream.
 */
export type Subscribe<T> = (next?: NextFn<T> | StorageObserver<T>, error?: ErrorFn, complete?: CompleteFn) => Unsubscribe;
export declare class Observer<T> implements StorageObserver<T> {
    next?: NextFn<T>;
    error?: ErrorFn;
    complete?: CompleteFn;
    constructor(nextOrObserver?: NextFn<T> | StorageObserver<T>, error?: ErrorFn, complete?: CompleteFn);
}
