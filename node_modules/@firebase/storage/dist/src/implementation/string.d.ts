/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * An enumeration of the possible string formats for upload.
 * @public
 */
export type StringFormat = (typeof StringFormat)[keyof typeof StringFormat];
/**
 * An enumeration of the possible string formats for upload.
 * @public
 */
export declare const StringFormat: {
    /**
     * Indicates the string should be interpreted "raw", that is, as normal text.
     * The string will be interpreted as UTF-16, then uploaded as a UTF-8 byte
     * sequence.
     * Example: The string 'Hello! \\ud83d\\ude0a' becomes the byte sequence
     * 48 65 6c 6c 6f 21 20 f0 9f 98 8a
     */
    readonly RAW: "raw";
    /**
     * Indicates the string should be interpreted as base64-encoded data.
     * Padding characters (trailing '='s) are optional.
     * Example: The string 'rWmO++E6t7/rlw==' becomes the byte sequence
     * ad 69 8e fb e1 3a b7 bf eb 97
     */
    readonly BASE64: "base64";
    /**
     * Indicates the string should be interpreted as base64url-encoded data.
     * Padding characters (trailing '='s) are optional.
     * Example: The string 'rWmO--E6t7_rlw==' becomes the byte sequence
     * ad 69 8e fb e1 3a b7 bf eb 97
     */
    readonly BASE64URL: "base64url";
    /**
     * Indicates the string is a data URL, such as one obtained from
     * canvas.toDataURL().
     * Example: the string 'data:application/octet-stream;base64,aaaa'
     * becomes the byte sequence
     * 69 a6 9a
     * (the content-type "application/octet-stream" is also applied, but can
     * be overridden in the metadata object).
     */
    readonly DATA_URL: "data_url";
};
export declare class StringData {
    data: Uint8Array;
    contentType: string | null;
    constructor(data: Uint8Array, contentType?: string | null);
}
/**
 * @internal
 */
export declare function dataFromString(format: StringFormat, stringData: string): StringData;
export declare function utf8Bytes_(value: string): Uint8Array;
export declare function percentEncodedBytes_(value: string): Uint8Array;
export declare function base64Bytes_(format: StringFormat, value: string): Uint8Array;
export declare function dataURLBytes_(dataUrl: string): Uint8Array;
export declare function dataURLContentType_(dataUrl: string): string | null;
