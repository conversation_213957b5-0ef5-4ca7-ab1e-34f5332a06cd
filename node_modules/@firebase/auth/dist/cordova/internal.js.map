{"version": 3, "file": "internal.js", "sources": ["../../src/platform_browser/persistence/cookie_storage.ts", "../../src/api/authentication/mfa.ts", "../../src/platform_browser/recaptcha/recaptcha_loader.ts", "../../src/platform_browser/recaptcha/recaptcha_verifier.ts", "../../src/platform_browser/strategies/phone.ts", "../../src/platform_browser/providers/phone.ts", "../../src/platform_browser/strategies/popup.ts", "../../src/core/util/validate_origin.ts", "../../src/platform_browser/iframe/gapi.ts", "../../src/platform_browser/iframe/iframe.ts", "../../src/platform_browser/util/popup.ts", "../../src/platform_browser/popup_redirect.ts", "../../src/mfa/mfa_assertion.ts", "../../src/platform_browser/mfa/assertions/phone.ts", "../../src/mfa/assertions/totp.ts", "../../src/platform_browser/index.ts", "../../internal/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Persistence } from '../../model/public_types';\nimport type { CookieChangeEvent } from 'cookie-store';\n\nconst POLLING_INTERVAL_MS = 1_000;\n\nimport {\n  PersistenceInternal,\n  PersistenceType,\n  PersistenceValue,\n  StorageEventListener\n} from '../../core/persistence';\n\n// Pull a cookie value from document.cookie\nfunction getDocumentCookie(name: string): string | null {\n  const escapedName = name.replace(/[\\\\^$.*+?()[\\]{}|]/g, '\\\\$&');\n  const matcher = RegExp(`${escapedName}=([^;]+)`);\n  return document.cookie.match(matcher)?.[1] ?? null;\n}\n\n// Produce a sanitized cookie name from the persistence key\nfunction getCookieName(key: string): string {\n  // __HOST- doesn't work in localhost https://issues.chromium.org/issues/40196122 but it has\n  // desirable security properties, so lets use a different cookie name while in dev-mode.\n  // Already checked isSecureContext in _isAvailable, so if it's http we're hitting local.\n  const isDevMode = window.location.protocol === 'http:';\n  return `${isDevMode ? '__dev_' : '__HOST-'}FIREBASE_${key.split(':')[3]}`;\n}\n\nexport class CookiePersistence implements PersistenceInternal {\n  static type: 'COOKIE' = 'COOKIE';\n  readonly type = PersistenceType.COOKIE;\n  listenerUnsubscribes: Map<StorageEventListener, () => void> = new Map();\n\n  // used to get the URL to the backend to proxy to\n  _getFinalTarget(originalUrl: string): URL | string {\n    if (typeof window === undefined) {\n      return originalUrl;\n    }\n    const url = new URL(`${window.location.origin}/__cookies__`);\n    url.searchParams.set('finalTarget', originalUrl);\n    return url;\n  }\n\n  // To be a usable persistence method in a chain browserCookiePersistence ensures that\n  // prerequisites have been met, namely that we're in a secureContext, navigator and document are\n  // available and cookies are enabled. Not all UAs support these method, so fallback accordingly.\n  async _isAvailable(): Promise<boolean> {\n    if (typeof isSecureContext === 'boolean' && !isSecureContext) {\n      return false;\n    }\n    if (typeof navigator === 'undefined' || typeof document === 'undefined') {\n      return false;\n    }\n    return navigator.cookieEnabled ?? true;\n  }\n\n  // Set should be a noop as we expect middleware to handle this\n  async _set(_key: string, _value: PersistenceValue): Promise<void> {\n    return;\n  }\n\n  // Attempt to get the cookie from cookieStore, fallback to document.cookie\n  async _get<T extends PersistenceValue>(key: string): Promise<T | null> {\n    if (!this._isAvailable()) {\n      return null;\n    }\n    const name = getCookieName(key);\n    if (window.cookieStore) {\n      const cookie = await window.cookieStore.get(name);\n      return cookie?.value as T;\n    }\n    return getDocumentCookie(name) as T;\n  }\n\n  // Log out by overriding the idToken with a sentinel value of \"\"\n  async _remove(key: string): Promise<void> {\n    if (!this._isAvailable()) {\n      return;\n    }\n    // To make sure we don't hit signout over and over again, only do this operation if we need to\n    // with the logout sentinel value of \"\" this can cause race conditions. Unnecessary set-cookie\n    // headers will reduce CDN hit rates too.\n    const existingValue = await this._get(key);\n    if (!existingValue) {\n      return;\n    }\n    const name = getCookieName(key);\n    document.cookie = `${name}=;Max-Age=34560000;Partitioned;Secure;SameSite=Strict;Path=/;Priority=High`;\n    await fetch(`/__cookies__`, { method: 'DELETE' }).catch(() => undefined);\n  }\n\n  // Listen for cookie changes, both cookieStore and fallback to polling document.cookie\n  _addListener(key: string, listener: StorageEventListener): void {\n    if (!this._isAvailable()) {\n      return;\n    }\n    const name = getCookieName(key);\n    if (window.cookieStore) {\n      const cb = ((event: CookieChangeEvent): void => {\n        const changedCookie = event.changed.find(\n          change => change.name === name\n        );\n        if (changedCookie) {\n          listener(changedCookie.value as PersistenceValue);\n        }\n        const deletedCookie = event.deleted.find(\n          change => change.name === name\n        );\n        if (deletedCookie) {\n          listener(null);\n        }\n      }) as EventListener;\n      const unsubscribe = (): void =>\n        window.cookieStore.removeEventListener('change', cb);\n      this.listenerUnsubscribes.set(listener, unsubscribe);\n      return window.cookieStore.addEventListener('change', cb as EventListener);\n    }\n    let lastValue = getDocumentCookie(name);\n    const interval = setInterval(() => {\n      const currentValue = getDocumentCookie(name);\n      if (currentValue !== lastValue) {\n        listener(currentValue as PersistenceValue | null);\n        lastValue = currentValue;\n      }\n    }, POLLING_INTERVAL_MS);\n    const unsubscribe = (): void => clearInterval(interval);\n    this.listenerUnsubscribes.set(listener, unsubscribe);\n  }\n\n  _removeListener(_key: string, listener: StorageEventListener): void {\n    const unsubscribe = this.listenerUnsubscribes.get(listener);\n    if (!unsubscribe) {\n      return;\n    }\n    unsubscribe();\n    this.listenerUnsubscribes.delete(listener);\n  }\n}\n\n/**\n * An implementation of {@link Persistence} of type `COOKIE`, for use on the client side in\n * applications leveraging hybrid rendering and middleware.\n *\n * @remarks This persistence method requires companion middleware to function, such as that provided\n * by {@link https://firebaseopensource.com/projects/firebaseextended/reactfire/ | ReactFire} for\n * NextJS.\n * @beta\n */\nexport const browserCookiePersistence: Persistence = CookiePersistence;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  _performApiRequest,\n  Endpoint,\n  HttpMethod,\n  RecaptchaClientType,\n  RecaptchaVersion,\n  _addTidIfNecessary\n} from '../index';\nimport { Auth } from '../../model/public_types';\nimport { IdTokenResponse } from '../../model/id_token';\nimport { MfaEnrollment } from '../account_management/mfa';\nimport { SignInWithIdpResponse } from './idp';\nimport {\n  SignInWithPhoneNumberRequest,\n  SignInWithPhoneNumberResponse\n} from './sms';\n\nexport interface FinalizeMfaResponse {\n  idToken: string;\n  refreshToken: string;\n}\n\n/**\n * @internal\n */\nexport interface IdTokenMfaResponse extends IdTokenResponse {\n  mfaPendingCredential?: string;\n  mfaInfo?: MfaEnrollment[];\n}\n\nexport interface StartPhoneMfaSignInRequest {\n  mfaPendingCredential: string;\n  mfaEnrollmentId: string;\n  phoneSignInInfo: {\n    // reCAPTCHA v2 token\n    recaptchaToken?: string;\n    // reCAPTCHA Enterprise token\n    captchaResponse?: string;\n    clientType?: RecaptchaClientType;\n    recaptchaVersion?: RecaptchaVersion;\n  };\n  tenantId?: string;\n}\n\nexport interface StartPhoneMfaSignInResponse {\n  phoneResponseInfo: {\n    sessionInfo: string;\n  };\n}\n\nexport function startSignInPhoneMfa(\n  auth: Auth,\n  request: StartPhoneMfaSignInRequest\n): Promise<StartPhoneMfaSignInResponse> {\n  return _performApiRequest<\n    StartPhoneMfaSignInRequest,\n    StartPhoneMfaSignInResponse\n  >(\n    auth,\n    HttpMethod.POST,\n    Endpoint.START_MFA_SIGN_IN,\n    _addTidIfNecessary(auth, request)\n  );\n}\n\nexport interface FinalizePhoneMfaSignInRequest {\n  mfaPendingCredential: string;\n  phoneVerificationInfo: SignInWithPhoneNumberRequest;\n  tenantId?: string;\n}\n\n// TOTP MFA Sign in only has a finalize phase. Phone MFA has a start phase to initiate sending an\n// SMS and a finalize phase to complete sign in. With TOTP, the user already has the OTP in the\n// TOTP/Authenticator app.\nexport interface FinalizeTotpMfaSignInRequest {\n  mfaPendingCredential: string;\n  totpVerificationInfo: { verificationCode: string };\n  tenantId?: string;\n  mfaEnrollmentId: string;\n}\n\nexport interface FinalizePhoneMfaSignInResponse extends FinalizeMfaResponse {}\n\nexport interface FinalizeTotpMfaSignInResponse extends FinalizeMfaResponse {}\n\nexport function finalizeSignInPhoneMfa(\n  auth: Auth,\n  request: FinalizePhoneMfaSignInRequest\n): Promise<FinalizePhoneMfaSignInResponse> {\n  return _performApiRequest<\n    FinalizePhoneMfaSignInRequest,\n    FinalizePhoneMfaSignInResponse\n  >(\n    auth,\n    HttpMethod.POST,\n    Endpoint.FINALIZE_MFA_SIGN_IN,\n    _addTidIfNecessary(auth, request)\n  );\n}\n\nexport function finalizeSignInTotpMfa(\n  auth: Auth,\n  request: FinalizeTotpMfaSignInRequest\n): Promise<FinalizeTotpMfaSignInResponse> {\n  return _performApiRequest<\n    FinalizeTotpMfaSignInRequest,\n    FinalizeTotpMfaSignInResponse\n  >(\n    auth,\n    HttpMethod.POST,\n    Endpoint.FINALIZE_MFA_SIGN_IN,\n    _addTidIfNecessary(auth, request)\n  );\n}\n\n/**\n * @internal\n */\nexport type PhoneOrOauthTokenResponse =\n  | SignInWithPhoneNumberResponse\n  | SignInWithIdpResponse\n  | IdTokenResponse;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { querystring } from '@firebase/util';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert, _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as jsHelpers from '../load_js';\nimport { Recaptcha, isV2 } from './recaptcha';\nimport { MockReCaptcha } from './recaptcha_mock';\n\n// ReCaptch<PERSON> will load using the same callback, so the callback function needs\n// to be kept around\nexport const _JSLOAD_CALLBACK = jsHelpers._generateCallbackName('rcb');\nconst NETWORK_TIMEOUT_DELAY = new Delay(30000, 60000);\n\n/**\n * We need to mark this interface as internal explicitly to exclude it in the public typings, because\n * it references AuthInternal which has a circular dependency with UserInternal.\n *\n * @internal\n */\nexport interface ReCaptchaLoader {\n  load(auth: AuthInternal, hl?: string): Promise<Recaptcha>;\n  clearedOneInstance(): void;\n}\n\n/**\n * Loader for the GReCaptcha library. There should only ever be one of this.\n */\nexport class ReCaptchaLoaderImpl implements ReCaptchaLoader {\n  private hostLanguage = '';\n  private counter = 0;\n  /**\n   * Check for `render()` method. `window.grecaptcha` will exist if the Enterprise\n   * version of the ReCAPTCHA script was loaded by someone else (e.g. App Check) but\n   * `window.grecaptcha.render()` will not. Another load will add it.\n   */\n  private readonly librarySeparatelyLoaded = !!_window().grecaptcha?.render;\n\n  load(auth: AuthInternal, hl = ''): Promise<Recaptcha> {\n    _assert(isHostLanguageValid(hl), auth, AuthErrorCode.ARGUMENT_ERROR);\n\n    if (this.shouldResolveImmediately(hl) && isV2(_window().grecaptcha)) {\n      return Promise.resolve(_window().grecaptcha! as Recaptcha);\n    }\n    return new Promise<Recaptcha>((resolve, reject) => {\n      const networkTimeout = _window().setTimeout(() => {\n        reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n      }, NETWORK_TIMEOUT_DELAY.get());\n\n      _window()[_JSLOAD_CALLBACK] = () => {\n        _window().clearTimeout(networkTimeout);\n        delete _window()[_JSLOAD_CALLBACK];\n\n        const recaptcha = _window().grecaptcha as Recaptcha;\n\n        if (!recaptcha || !isV2(recaptcha)) {\n          reject(_createError(auth, AuthErrorCode.INTERNAL_ERROR));\n          return;\n        }\n\n        // Wrap the recaptcha render function so that we know if the developer has\n        // called it separately\n        const render = recaptcha.render;\n        recaptcha.render = (container, params) => {\n          const widgetId = render(container, params);\n          this.counter++;\n          return widgetId;\n        };\n\n        this.hostLanguage = hl;\n        resolve(recaptcha);\n      };\n\n      const url = `${jsHelpers._recaptchaV2ScriptUrl()}?${querystring({\n        onload: _JSLOAD_CALLBACK,\n        render: 'explicit',\n        hl\n      })}`;\n\n      jsHelpers._loadJS(url).catch(() => {\n        clearTimeout(networkTimeout);\n        reject(_createError(auth, AuthErrorCode.INTERNAL_ERROR));\n      });\n    });\n  }\n\n  clearedOneInstance(): void {\n    this.counter--;\n  }\n\n  private shouldResolveImmediately(hl: string): boolean {\n    // We can resolve immediately if:\n    //   • grecaptcha is already defined AND (\n    //     1. the requested language codes are the same OR\n    //     2. there exists already a ReCaptcha on the page\n    //     3. the library was already loaded by the app\n    // In cases (2) and (3), we _can't_ reload as it would break the recaptchas\n    // that are already in the page\n    return (\n      !!_window().grecaptcha?.render &&\n      (hl === this.hostLanguage ||\n        this.counter > 0 ||\n        this.librarySeparatelyLoaded)\n    );\n  }\n}\n\nfunction isHostLanguageValid(hl: string): boolean {\n  return hl.length <= 6 && /^\\s*[a-zA-Z0-9\\-]*\\s*$/.test(hl);\n}\n\nexport class MockReCaptchaLoaderImpl implements ReCaptchaLoader {\n  async load(auth: AuthInternal): Promise<Recaptcha> {\n    return new MockReCaptcha(auth);\n  }\n\n  clearedOneInstance(): void {}\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Auth, RecaptchaParameters } from '../../model/public_types';\nimport { getRecaptchaParams } from '../../api/authentication/recaptcha';\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\nimport { _isHttpOrHttps } from '../../core/util/location';\nimport { ApplicationVerifierInternal } from '../../model/application_verifier';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport { _isWorker } from '../util/worker';\nimport { Recaptcha } from './recaptcha';\nimport {\n  MockReCaptchaLoaderImpl,\n  ReCaptchaLoader,\n  ReCaptchaLoaderImpl\n} from './recaptcha_loader';\n\nexport const RECAPTCHA_VERIFIER_TYPE = 'recaptcha';\n\nconst DEFAULT_PARAMS: RecaptchaParameters = {\n  theme: 'light',\n  type: 'image'\n};\n\ntype TokenCallback = (token: string) => void;\n\n/**\n * An {@link https://www.google.com/recaptcha/ | reCAPTCHA}-based application verifier.\n *\n * @remarks\n * `RecaptchaVerifier` does not work in a Node.js environment.\n *\n * @public\n */\nexport class RecaptchaVerifier implements ApplicationVerifierInternal {\n  /**\n   * The application verifier type.\n   *\n   * @remarks\n   * For a reCAPTCHA verifier, this is 'recaptcha'.\n   */\n  readonly type = RECAPTCHA_VERIFIER_TYPE;\n  private destroyed = false;\n  private widgetId: number | null = null;\n  private readonly container: HTMLElement;\n  private readonly isInvisible: boolean;\n  private readonly tokenChangeListeners = new Set<TokenCallback>();\n  private renderPromise: Promise<number> | null = null;\n  private readonly auth: AuthInternal;\n\n  /** @internal */\n  readonly _recaptchaLoader: ReCaptchaLoader;\n  private recaptcha: Recaptcha | null = null;\n\n  /**\n   * @param authExtern - The corresponding Firebase {@link Auth} instance.\n   *\n   * @param containerOrId - The reCAPTCHA container parameter.\n   *\n   * @remarks\n   * This has different meaning depending on whether the reCAPTCHA is hidden or visible. For a\n   * visible reCAPTCHA the container must be empty. If a string is used, it has to correspond to\n   * an element ID. The corresponding element must also must be in the DOM at the time of\n   * initialization.\n   *\n   * @param parameters - The optional reCAPTCHA parameters.\n   *\n   * @remarks\n   * Check the reCAPTCHA docs for a comprehensive list. All parameters are accepted except for\n   * the sitekey. Firebase Auth backend provisions a reCAPTCHA for each project and will\n   * configure this upon rendering. For an invisible reCAPTCHA, a size key must have the value\n   * 'invisible'.\n   */\n  constructor(\n    authExtern: Auth,\n    containerOrId: HTMLElement | string,\n    private readonly parameters: RecaptchaParameters = {\n      ...DEFAULT_PARAMS\n    }\n  ) {\n    this.auth = _castAuth(authExtern);\n    this.isInvisible = this.parameters.size === 'invisible';\n    _assert(\n      typeof document !== 'undefined',\n      this.auth,\n      AuthErrorCode.OPERATION_NOT_SUPPORTED\n    );\n    const container =\n      typeof containerOrId === 'string'\n        ? document.getElementById(containerOrId)\n        : containerOrId;\n    _assert(container, this.auth, AuthErrorCode.ARGUMENT_ERROR);\n\n    this.container = container;\n    this.parameters.callback = this.makeTokenCallback(this.parameters.callback);\n\n    this._recaptchaLoader = this.auth.settings.appVerificationDisabledForTesting\n      ? new MockReCaptchaLoaderImpl()\n      : new ReCaptchaLoaderImpl();\n\n    this.validateStartingState();\n    // TODO: Figure out if sdk version is needed\n  }\n\n  /**\n   * Waits for the user to solve the reCAPTCHA and resolves with the reCAPTCHA token.\n   *\n   * @returns A Promise for the reCAPTCHA token.\n   */\n  async verify(): Promise<string> {\n    this.assertNotDestroyed();\n    const id = await this.render();\n    const recaptcha = this.getAssertedRecaptcha();\n\n    const response = recaptcha.getResponse(id);\n    if (response) {\n      return response;\n    }\n\n    return new Promise<string>(resolve => {\n      const tokenChange = (token: string): void => {\n        if (!token) {\n          return; // Ignore token expirations.\n        }\n        this.tokenChangeListeners.delete(tokenChange);\n        resolve(token);\n      };\n\n      this.tokenChangeListeners.add(tokenChange);\n      if (this.isInvisible) {\n        recaptcha.execute(id);\n      }\n    });\n  }\n\n  /**\n   * Renders the reCAPTCHA widget on the page.\n   *\n   * @returns A Promise that resolves with the reCAPTCHA widget ID.\n   */\n  render(): Promise<number> {\n    try {\n      this.assertNotDestroyed();\n    } catch (e) {\n      // This method returns a promise. Since it's not async (we want to return the\n      // _same_ promise if rendering is still occurring), the API surface should\n      // reject with the error rather than just throw\n      return Promise.reject(e);\n    }\n\n    if (this.renderPromise) {\n      return this.renderPromise;\n    }\n\n    this.renderPromise = this.makeRenderPromise().catch(e => {\n      this.renderPromise = null;\n      throw e;\n    });\n\n    return this.renderPromise;\n  }\n\n  /** @internal */\n  _reset(): void {\n    this.assertNotDestroyed();\n    if (this.widgetId !== null) {\n      this.getAssertedRecaptcha().reset(this.widgetId);\n    }\n  }\n\n  /**\n   * Clears the reCAPTCHA widget from the page and destroys the instance.\n   */\n  clear(): void {\n    this.assertNotDestroyed();\n    this.destroyed = true;\n    this._recaptchaLoader.clearedOneInstance();\n    if (!this.isInvisible) {\n      this.container.childNodes.forEach(node => {\n        this.container.removeChild(node);\n      });\n    }\n  }\n\n  private validateStartingState(): void {\n    _assert(!this.parameters.sitekey, this.auth, AuthErrorCode.ARGUMENT_ERROR);\n    _assert(\n      this.isInvisible || !this.container.hasChildNodes(),\n      this.auth,\n      AuthErrorCode.ARGUMENT_ERROR\n    );\n    _assert(\n      typeof document !== 'undefined',\n      this.auth,\n      AuthErrorCode.OPERATION_NOT_SUPPORTED\n    );\n  }\n\n  private makeTokenCallback(\n    existing: TokenCallback | string | undefined\n  ): TokenCallback {\n    return token => {\n      this.tokenChangeListeners.forEach(listener => listener(token));\n      if (typeof existing === 'function') {\n        existing(token);\n      } else if (typeof existing === 'string') {\n        const globalFunc = _window()[existing];\n        if (typeof globalFunc === 'function') {\n          globalFunc(token);\n        }\n      }\n    };\n  }\n\n  private assertNotDestroyed(): void {\n    _assert(!this.destroyed, this.auth, AuthErrorCode.INTERNAL_ERROR);\n  }\n\n  private async makeRenderPromise(): Promise<number> {\n    await this.init();\n    if (!this.widgetId) {\n      let container = this.container;\n      if (!this.isInvisible) {\n        const guaranteedEmpty = document.createElement('div');\n        container.appendChild(guaranteedEmpty);\n        container = guaranteedEmpty;\n      }\n\n      this.widgetId = this.getAssertedRecaptcha().render(\n        container,\n        this.parameters\n      );\n    }\n\n    return this.widgetId;\n  }\n\n  private async init(): Promise<void> {\n    _assert(\n      _isHttpOrHttps() && !_isWorker(),\n      this.auth,\n      AuthErrorCode.INTERNAL_ERROR\n    );\n\n    await domReady();\n    this.recaptcha = await this._recaptchaLoader.load(\n      this.auth,\n      this.auth.languageCode || undefined\n    );\n\n    const siteKey = await getRecaptchaParams(this.auth);\n    _assert(siteKey, this.auth, AuthErrorCode.INTERNAL_ERROR);\n    this.parameters.sitekey = siteKey;\n  }\n\n  private getAssertedRecaptcha(): Recaptcha {\n    _assert(this.recaptcha, this.auth, AuthErrorCode.INTERNAL_ERROR);\n    return this.recaptcha;\n  }\n}\n\nfunction domReady(): Promise<void> {\n  let resolver: (() => void) | null = null;\n  return new Promise<void>(resolve => {\n    if (document.readyState === 'complete') {\n      resolve();\n      return;\n    }\n\n    // Document not ready, wait for load before resolving.\n    // Save resolver, so we can remove listener in case it was externally\n    // cancelled.\n    resolver = () => resolve();\n    window.addEventListener('load', resolver);\n  }).catch(e => {\n    if (resolver) {\n      window.removeEventListener('load', resolver);\n    }\n\n    throw e;\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  ApplicationVerifier,\n  Auth,\n  ConfirmationResult,\n  PhoneInfoOptions,\n  User,\n  UserCredential\n} from '../../model/public_types';\n\nimport {\n  startEnrollPhoneMfa,\n  StartPhoneMfaEnrollmentRequest,\n  StartPhoneMfaEnrollmentResponse\n} from '../../api/account_management/mfa';\nimport {\n  startSignInPhoneMfa,\n  StartPhoneMfaSignInRequest,\n  StartPhoneMfaSignInResponse\n} from '../../api/authentication/mfa';\nimport {\n  sendPhoneVerificationCode,\n  SendPhoneVerificationCodeRequest,\n  SendPhoneVerificationCodeResponse\n} from '../../api/authentication/sms';\nimport {\n  RecaptchaActionName,\n  RecaptchaClientType,\n  RecaptchaAuthProvider\n} from '../../api';\nimport { ApplicationVerifierInternal } from '../../model/application_verifier';\nimport { PhoneAuthCredential } from '../../core/credentials/phone';\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assertLinkedStatus, _link } from '../../core/user/link_unlink';\nimport {\n  _assert,\n  _serverAppCurrentUserOperationNotSupportedError\n} from '../../core/util/assert';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  linkWithCredential,\n  reauthenticateWithCredential,\n  signInWithCredential\n} from '../../core/strategies/credential';\nimport {\n  MultiFactorSessionImpl,\n  MultiFactorSessionType\n} from '../../mfa/mfa_session';\nimport { UserInternal } from '../../model/user';\nimport { RECAPTCHA_VERIFIER_TYPE } from '../recaptcha/recaptcha_verifier';\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { getModularInstance } from '@firebase/util';\nimport { ProviderId } from '../../model/enums';\nimport {\n  FAKE_TOKEN,\n  handleRecaptchaFlow,\n  _initializeRecaptchaConfig\n} from '../recaptcha/recaptcha_enterprise_verifier';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\ninterface OnConfirmationCallback {\n  (credential: PhoneAuthCredential): Promise<UserCredential>;\n}\n\nclass ConfirmationResultImpl implements ConfirmationResult {\n  constructor(\n    readonly verificationId: string,\n    private readonly onConfirmation: OnConfirmationCallback\n  ) {}\n\n  confirm(verificationCode: string): Promise<UserCredential> {\n    const authCredential = PhoneAuthCredential._fromVerification(\n      this.verificationId,\n      verificationCode\n    );\n    return this.onConfirmation(authCredential);\n  }\n}\n\n/**\n * Asynchronously signs in using a phone number.\n *\n * @remarks\n * This method sends a code via SMS to the given\n * phone number, and returns a {@link ConfirmationResult}. After the user\n * provides the code sent to their phone, call {@link ConfirmationResult.confirm}\n * with the code to sign the user in.\n *\n * For abuse prevention, this method requires a {@link ApplicationVerifier}.\n * This SDK includes an implementation based on reCAPTCHA v2, {@link RecaptchaVerifier}.\n * This function can work on other platforms that do not support the\n * {@link RecaptchaVerifier} (like React Native), but you need to use a\n * third-party {@link ApplicationVerifier} implementation.\n *\n * If you've enabled project-level reCAPTCHA Enterprise bot protection in\n * Enforce mode, you can omit the {@link ApplicationVerifier}.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // 'recaptcha-container' is the ID of an element in the DOM.\n * const applicationVerifier = new firebase.auth.RecaptchaVerifier('recaptcha-container');\n * const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\n * // Obtain a verificationCode from the user.\n * const credential = await confirmationResult.confirm(verificationCode);\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\n * @param appVerifier - The {@link ApplicationVerifier}.\n *\n * @public\n */\nexport async function signInWithPhoneNumber(\n  auth: Auth,\n  phoneNumber: string,\n  appVerifier?: ApplicationVerifier\n): Promise<ConfirmationResult> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(auth)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  const verificationId = await _verifyPhoneNumber(\n    authInternal,\n    phoneNumber,\n    getModularInstance(appVerifier as ApplicationVerifierInternal)\n  );\n  return new ConfirmationResultImpl(verificationId, cred =>\n    signInWithCredential(authInternal, cred)\n  );\n}\n\n/**\n * Links the user account with the given phone number.\n *\n * @remarks\n * This method does not work in a Node.js environment.\n *\n * @param user - The user.\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\n * @param appVerifier - The {@link ApplicationVerifier}.\n *\n * @public\n */\nexport async function linkWithPhoneNumber(\n  user: User,\n  phoneNumber: string,\n  appVerifier?: ApplicationVerifier\n): Promise<ConfirmationResult> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  await _assertLinkedStatus(false, userInternal, ProviderId.PHONE);\n  const verificationId = await _verifyPhoneNumber(\n    userInternal.auth,\n    phoneNumber,\n    getModularInstance(appVerifier as ApplicationVerifierInternal)\n  );\n  return new ConfirmationResultImpl(verificationId, cred =>\n    linkWithCredential(userInternal, cred)\n  );\n}\n\n/**\n * Re-authenticates a user using a fresh phone credential.\n *\n * @remarks\n * Use before operations such as {@link updatePassword} that require tokens from recent sign-in attempts.\n *\n * This method does not work in a Node.js environment or on any {@link User} signed in by\n * {@link Auth} instances created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @param user - The user.\n * @param phoneNumber - The user's phone number in E.164 format (e.g. +***********).\n * @param appVerifier - The {@link ApplicationVerifier}.\n *\n * @public\n */\nexport async function reauthenticateWithPhoneNumber(\n  user: User,\n  phoneNumber: string,\n  appVerifier?: ApplicationVerifier\n): Promise<ConfirmationResult> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(userInternal.auth)\n    );\n  }\n  const verificationId = await _verifyPhoneNumber(\n    userInternal.auth,\n    phoneNumber,\n    getModularInstance(appVerifier as ApplicationVerifierInternal)\n  );\n  return new ConfirmationResultImpl(verificationId, cred =>\n    reauthenticateWithCredential(userInternal, cred)\n  );\n}\n\ntype PhoneApiCaller<TRequest, TResponse> = (\n  auth: AuthInternal,\n  request: TRequest\n) => Promise<TResponse>;\n\n/**\n * Returns a verification ID to be used in conjunction with the SMS code that is sent.\n *\n */\nexport async function _verifyPhoneNumber(\n  auth: AuthInternal,\n  options: PhoneInfoOptions | string,\n  verifier?: ApplicationVerifierInternal\n): Promise<string> {\n  if (!auth._getRecaptchaConfig()) {\n    try {\n      await _initializeRecaptchaConfig(auth);\n    } catch (error) {\n      // If an error occurs while fetching the config, there is no way to know the enablement state\n      // of Phone provider, so we proceed with recaptcha V2 verification.\n      // The error is likely \"recaptchaKey undefined\", as reCAPTCHA Enterprise is not\n      // enabled for any provider.\n      console.log(\n        'Failed to initialize reCAPTCHA Enterprise config. Triggering the reCAPTCHA v2 verification.'\n      );\n    }\n  }\n\n  try {\n    let phoneInfoOptions: PhoneInfoOptions;\n\n    if (typeof options === 'string') {\n      phoneInfoOptions = {\n        phoneNumber: options\n      };\n    } else {\n      phoneInfoOptions = options;\n    }\n\n    if ('session' in phoneInfoOptions) {\n      const session = phoneInfoOptions.session as MultiFactorSessionImpl;\n\n      if ('phoneNumber' in phoneInfoOptions) {\n        _assert(\n          session.type === MultiFactorSessionType.ENROLL,\n          auth,\n          AuthErrorCode.INTERNAL_ERROR\n        );\n\n        const startPhoneMfaEnrollmentRequest: StartPhoneMfaEnrollmentRequest = {\n          idToken: session.credential,\n          phoneEnrollmentInfo: {\n            phoneNumber: phoneInfoOptions.phoneNumber,\n            clientType: RecaptchaClientType.WEB\n          }\n        };\n\n        const startEnrollPhoneMfaActionCallback: PhoneApiCaller<\n          StartPhoneMfaEnrollmentRequest,\n          StartPhoneMfaEnrollmentResponse\n        > = async (\n          authInstance: AuthInternal,\n          request: StartPhoneMfaEnrollmentRequest\n        ) => {\n          // If reCAPTCHA Enterprise token is FAKE_TOKEN, fetch reCAPTCHA v2 token and inject into request.\n          if (request.phoneEnrollmentInfo.captchaResponse === FAKE_TOKEN) {\n            _assert(\n              verifier?.type === RECAPTCHA_VERIFIER_TYPE,\n              authInstance,\n              AuthErrorCode.ARGUMENT_ERROR\n            );\n\n            const requestWithRecaptchaV2 = await injectRecaptchaV2Token(\n              authInstance,\n              request,\n              verifier\n            );\n            return startEnrollPhoneMfa(authInstance, requestWithRecaptchaV2);\n          }\n          return startEnrollPhoneMfa(authInstance, request);\n        };\n\n        const startPhoneMfaEnrollmentResponse: Promise<StartPhoneMfaEnrollmentResponse> =\n          handleRecaptchaFlow(\n            auth,\n            startPhoneMfaEnrollmentRequest,\n            RecaptchaActionName.MFA_SMS_ENROLLMENT,\n            startEnrollPhoneMfaActionCallback,\n            RecaptchaAuthProvider.PHONE_PROVIDER\n          );\n\n        const response = await startPhoneMfaEnrollmentResponse.catch(error => {\n          return Promise.reject(error);\n        });\n\n        return response.phoneSessionInfo.sessionInfo;\n      } else {\n        _assert(\n          session.type === MultiFactorSessionType.SIGN_IN,\n          auth,\n          AuthErrorCode.INTERNAL_ERROR\n        );\n        const mfaEnrollmentId =\n          phoneInfoOptions.multiFactorHint?.uid ||\n          phoneInfoOptions.multiFactorUid;\n        _assert(mfaEnrollmentId, auth, AuthErrorCode.MISSING_MFA_INFO);\n\n        const startPhoneMfaSignInRequest: StartPhoneMfaSignInRequest = {\n          mfaPendingCredential: session.credential,\n          mfaEnrollmentId,\n          phoneSignInInfo: {\n            clientType: RecaptchaClientType.WEB\n          }\n        };\n\n        const startSignInPhoneMfaActionCallback: PhoneApiCaller<\n          StartPhoneMfaSignInRequest,\n          StartPhoneMfaSignInResponse\n        > = async (\n          authInstance: AuthInternal,\n          request: StartPhoneMfaSignInRequest\n        ) => {\n          // If reCAPTCHA Enterprise token is FAKE_TOKEN, fetch reCAPTCHA v2 token and inject into request.\n          if (request.phoneSignInInfo.captchaResponse === FAKE_TOKEN) {\n            _assert(\n              verifier?.type === RECAPTCHA_VERIFIER_TYPE,\n              authInstance,\n              AuthErrorCode.ARGUMENT_ERROR\n            );\n\n            const requestWithRecaptchaV2 = await injectRecaptchaV2Token(\n              authInstance,\n              request,\n              verifier\n            );\n            return startSignInPhoneMfa(authInstance, requestWithRecaptchaV2);\n          }\n          return startSignInPhoneMfa(authInstance, request);\n        };\n\n        const startPhoneMfaSignInResponse: Promise<StartPhoneMfaSignInResponse> =\n          handleRecaptchaFlow(\n            auth,\n            startPhoneMfaSignInRequest,\n            RecaptchaActionName.MFA_SMS_SIGNIN,\n            startSignInPhoneMfaActionCallback,\n            RecaptchaAuthProvider.PHONE_PROVIDER\n          );\n\n        const response = await startPhoneMfaSignInResponse.catch(error => {\n          return Promise.reject(error);\n        });\n\n        return response.phoneResponseInfo.sessionInfo;\n      }\n    } else {\n      const sendPhoneVerificationCodeRequest: SendPhoneVerificationCodeRequest =\n        {\n          phoneNumber: phoneInfoOptions.phoneNumber,\n          clientType: RecaptchaClientType.WEB\n        };\n\n      const sendPhoneVerificationCodeActionCallback: PhoneApiCaller<\n        SendPhoneVerificationCodeRequest,\n        SendPhoneVerificationCodeResponse\n      > = async (\n        authInstance: AuthInternal,\n        request: SendPhoneVerificationCodeRequest\n      ) => {\n        // If reCAPTCHA Enterprise token is FAKE_TOKEN, fetch reCAPTCHA v2 token and inject into request.\n        if (request.captchaResponse === FAKE_TOKEN) {\n          _assert(\n            verifier?.type === RECAPTCHA_VERIFIER_TYPE,\n            authInstance,\n            AuthErrorCode.ARGUMENT_ERROR\n          );\n\n          const requestWithRecaptchaV2 = await injectRecaptchaV2Token(\n            authInstance,\n            request,\n            verifier\n          );\n          return sendPhoneVerificationCode(\n            authInstance,\n            requestWithRecaptchaV2\n          );\n        }\n        return sendPhoneVerificationCode(authInstance, request);\n      };\n\n      const sendPhoneVerificationCodeResponse: Promise<SendPhoneVerificationCodeResponse> =\n        handleRecaptchaFlow(\n          auth,\n          sendPhoneVerificationCodeRequest,\n          RecaptchaActionName.SEND_VERIFICATION_CODE,\n          sendPhoneVerificationCodeActionCallback,\n          RecaptchaAuthProvider.PHONE_PROVIDER\n        );\n\n      const response = await sendPhoneVerificationCodeResponse.catch(error => {\n        return Promise.reject(error);\n      });\n\n      return response.sessionInfo;\n    }\n  } finally {\n    verifier?._reset();\n  }\n}\n\n/**\n * Updates the user's phone number.\n *\n * @remarks\n * This method does not work in a Node.js environment or on any {@link User} signed in by\n * {@link Auth} instances created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```\n * // 'recaptcha-container' is the ID of an element in the DOM.\n * const applicationVerifier = new RecaptchaVerifier('recaptcha-container');\n * const provider = new PhoneAuthProvider(auth);\n * const verificationId = await provider.verifyPhoneNumber('+***********', applicationVerifier);\n * // Obtain the verificationCode from the user.\n * const phoneCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n * await updatePhoneNumber(user, phoneCredential);\n * ```\n *\n * @param user - The user.\n * @param credential - A credential authenticating the new phone number.\n *\n * @public\n */\nexport async function updatePhoneNumber(\n  user: User,\n  credential: PhoneAuthCredential\n): Promise<void> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _serverAppCurrentUserOperationNotSupportedError(userInternal.auth)\n    );\n  }\n  await _link(userInternal, credential);\n}\n\n// Helper function that fetches and injects a reCAPTCHA v2 token into the request.\nexport async function injectRecaptchaV2Token<T extends object>(\n  auth: AuthInternal,\n  request: T,\n  recaptchaV2Verifier: ApplicationVerifierInternal\n): Promise<T> {\n  _assert(\n    recaptchaV2Verifier.type === RECAPTCHA_VERIFIER_TYPE,\n    auth,\n    AuthErrorCode.ARGUMENT_ERROR\n  );\n\n  const recaptchaV2Token = await recaptchaV2Verifier.verify();\n\n  _assert(\n    typeof recaptchaV2Token === 'string',\n    auth,\n    AuthErrorCode.ARGUMENT_ERROR\n  );\n\n  const newRequest = { ...request };\n\n  if ('phoneEnrollmentInfo' in newRequest) {\n    const phoneNumber = (\n      newRequest as unknown as StartPhoneMfaEnrollmentRequest\n    ).phoneEnrollmentInfo.phoneNumber;\n    const captchaResponse = (\n      newRequest as unknown as StartPhoneMfaEnrollmentRequest\n    ).phoneEnrollmentInfo.captchaResponse;\n    const clientType = (newRequest as unknown as StartPhoneMfaEnrollmentRequest)\n      .phoneEnrollmentInfo.clientType;\n    const recaptchaVersion = (\n      newRequest as unknown as StartPhoneMfaEnrollmentRequest\n    ).phoneEnrollmentInfo.recaptchaVersion;\n\n    Object.assign(newRequest, {\n      'phoneEnrollmentInfo': {\n        phoneNumber,\n        recaptchaToken: recaptchaV2Token,\n        captchaResponse,\n        clientType,\n        recaptchaVersion\n      }\n    });\n\n    return newRequest;\n  } else if ('phoneSignInInfo' in newRequest) {\n    const captchaResponse = (\n      newRequest as unknown as StartPhoneMfaSignInRequest\n    ).phoneSignInInfo.captchaResponse;\n    const clientType = (newRequest as unknown as StartPhoneMfaSignInRequest)\n      .phoneSignInInfo.clientType;\n    const recaptchaVersion = (\n      newRequest as unknown as StartPhoneMfaSignInRequest\n    ).phoneSignInInfo.recaptchaVersion;\n\n    Object.assign(newRequest, {\n      'phoneSignInInfo': {\n        recaptchaToken: recaptchaV2Token,\n        captchaResponse,\n        clientType,\n        recaptchaVersion\n      }\n    });\n\n    return newRequest;\n  } else {\n    Object.assign(newRequest, { 'recaptchaToken': recaptchaV2Token });\n    return newRequest;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  PhoneInfoOptions,\n  ApplicationVerifier,\n  UserCredential\n} from '../../model/public_types';\n\nimport { SignInWithPhoneNumberResponse } from '../../api/authentication/sms';\nimport { ApplicationVerifierInternal as ApplicationVerifierInternal } from '../../model/application_verifier';\nimport { AuthInternal as AuthInternal } from '../../model/auth';\nimport { UserCredentialInternal as UserCredentialInternal } from '../../model/user';\nimport { PhoneAuthCredential } from '../../core/credentials/phone';\nimport { _verifyPhoneNumber } from '../strategies/phone';\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { AuthCredential } from '../../core';\nimport { FirebaseError, getModularInstance } from '@firebase/util';\nimport { TaggedWithTokenResponse } from '../../model/id_token';\nimport { ProviderId, SignInMethod } from '../../model/enums';\n\n/**\n * Provider for generating an {@link PhoneAuthCredential}.\n *\n * @remarks\n * `PhoneAuthProvider` does not work in a Node.js environment.\n *\n * @example\n * ```javascript\n * // 'recaptcha-container' is the ID of an element in the DOM.\n * const applicationVerifier = new RecaptchaVerifier('recaptcha-container');\n * const provider = new PhoneAuthProvider(auth);\n * const verificationId = await provider.verifyPhoneNumber('+***********', applicationVerifier);\n * // Obtain the verificationCode from the user.\n * const phoneCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n * const userCredential = await signInWithCredential(auth, phoneCredential);\n * ```\n *\n * @public\n */\nexport class PhoneAuthProvider {\n  /** Always set to {@link ProviderId}.PHONE. */\n  static readonly PROVIDER_ID: 'phone' = ProviderId.PHONE;\n  /** Always set to {@link SignInMethod}.PHONE. */\n  static readonly PHONE_SIGN_IN_METHOD: 'phone' = SignInMethod.PHONE;\n\n  /** Always set to {@link ProviderId}.PHONE. */\n  readonly providerId = PhoneAuthProvider.PROVIDER_ID;\n  private readonly auth: AuthInternal;\n\n  /**\n   * @param auth - The Firebase {@link Auth} instance in which sign-ins should occur.\n   *\n   */\n  constructor(auth: Auth) {\n    this.auth = _castAuth(auth);\n  }\n\n  /**\n   *\n   * Starts a phone number authentication flow by sending a verification code to the given phone\n   * number.\n   *\n   * @example\n   * ```javascript\n   * const provider = new PhoneAuthProvider(auth);\n   * const verificationId = await provider.verifyPhoneNumber(phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const authCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n   * const userCredential = await signInWithCredential(auth, authCredential);\n   * ```\n   *\n   * @example\n   * An alternative flow is provided using the `signInWithPhoneNumber` method.\n   * ```javascript\n   * const confirmationResult = signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const userCredential = confirmationResult.confirm(verificationCode);\n   * ```\n   *\n   * @param phoneInfoOptions - The user's {@link PhoneInfoOptions}. The phone number should be in\n   * E.164 format (e.g. +***********).\n   * @param applicationVerifier - An {@link ApplicationVerifier}, which prevents\n   * requests from unauthorized clients. This SDK includes an implementation\n   * based on reCAPTCHA v2, {@link RecaptchaVerifier}. If you've enabled\n   * reCAPTCHA Enterprise bot protection in Enforce mode, this parameter is\n   * optional; in all other configurations, the parameter is required.\n   *\n   * @returns A Promise for a verification ID that can be passed to\n   * {@link PhoneAuthProvider.credential} to identify this flow.\n   */\n  verifyPhoneNumber(\n    phoneOptions: PhoneInfoOptions | string,\n    applicationVerifier?: ApplicationVerifier\n  ): Promise<string> {\n    return _verifyPhoneNumber(\n      this.auth,\n      phoneOptions,\n      getModularInstance(applicationVerifier as ApplicationVerifierInternal)\n    );\n  }\n\n  /**\n   * Creates a phone auth credential, given the verification ID from\n   * {@link PhoneAuthProvider.verifyPhoneNumber} and the code that was sent to the user's\n   * mobile device.\n   *\n   * @example\n   * ```javascript\n   * const provider = new PhoneAuthProvider(auth);\n   * const verificationId = provider.verifyPhoneNumber(phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const authCredential = PhoneAuthProvider.credential(verificationId, verificationCode);\n   * const userCredential = signInWithCredential(auth, authCredential);\n   * ```\n   *\n   * @example\n   * An alternative flow is provided using the `signInWithPhoneNumber` method.\n   * ```javascript\n   * const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, applicationVerifier);\n   * // Obtain verificationCode from the user.\n   * const userCredential = await confirmationResult.confirm(verificationCode);\n   * ```\n   *\n   * @param verificationId - The verification ID returned from {@link PhoneAuthProvider.verifyPhoneNumber}.\n   * @param verificationCode - The verification code sent to the user's mobile device.\n   *\n   * @returns The auth provider credential.\n   */\n  static credential(\n    verificationId: string,\n    verificationCode: string\n  ): PhoneAuthCredential {\n    return PhoneAuthCredential._fromVerification(\n      verificationId,\n      verificationCode\n    );\n  }\n\n  /**\n   * Generates an {@link AuthCredential} from a {@link UserCredential}.\n   * @param userCredential - The user credential.\n   */\n  static credentialFromResult(\n    userCredential: UserCredential\n  ): AuthCredential | null {\n    const credential = userCredential as UserCredentialInternal;\n    return PhoneAuthProvider.credentialFromTaggedObject(credential);\n  }\n\n  /**\n   * Returns an {@link AuthCredential} when passed an error.\n   *\n   * @remarks\n   *\n   * This method works for errors like\n   * `auth/account-exists-with-different-credentials`. This is useful for\n   * recovering when attempting to set a user's phone number but the number\n   * in question is already tied to another account. For example, the following\n   * code tries to update the current user's phone number, and if that\n   * fails, links the user with the account associated with that number:\n   *\n   * ```js\n   * const provider = new PhoneAuthProvider(auth);\n   * const verificationId = await provider.verifyPhoneNumber(number, verifier);\n   * try {\n   *   const code = ''; // Prompt the user for the verification code\n   *   await updatePhoneNumber(\n   *       auth.currentUser,\n   *       PhoneAuthProvider.credential(verificationId, code));\n   * } catch (e) {\n   *   if ((e as FirebaseError)?.code === 'auth/account-exists-with-different-credential') {\n   *     const cred = PhoneAuthProvider.credentialFromError(e);\n   *     await linkWithCredential(auth.currentUser, cred);\n   *   }\n   * }\n   *\n   * // At this point, auth.currentUser.phoneNumber === number.\n   * ```\n   *\n   * @param error - The error to generate a credential from.\n   */\n  static credentialFromError(error: FirebaseError): AuthCredential | null {\n    return PhoneAuthProvider.credentialFromTaggedObject(\n      (error.customData || {}) as TaggedWithTokenResponse\n    );\n  }\n\n  private static credentialFromTaggedObject({\n    _tokenResponse: tokenResponse\n  }: TaggedWithTokenResponse): AuthCredential | null {\n    if (!tokenResponse) {\n      return null;\n    }\n    const { phoneNumber, temporaryProof } =\n      tokenResponse as SignInWithPhoneNumberResponse;\n    if (phoneNumber && temporaryProof) {\n      return PhoneAuthCredential._fromTokenResponse(\n        phoneNumber,\n        temporaryProof\n      );\n    }\n    return null;\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Auth,\n  AuthProvider,\n  PopupRedirectResolver,\n  User,\n  UserCredential\n} from '../../model/public_types';\n\nimport { _castAuth } from '../../core/auth/auth_impl';\nimport { AuthErrorCode } from '../../core/errors';\nimport {\n  _assert,\n  debugAssert,\n  _createError,\n  _assertInstanceOf\n} from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { _generateEventId } from '../../core/util/event_id';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  AuthEventType,\n  PopupRedirectResolverInternal\n} from '../../model/popup_redirect';\nimport { UserInternal } from '../../model/user';\nimport { _withDefaultResolver } from '../../core/util/resolver';\nimport { AuthPopup } from '../util/popup';\nimport { AbstractPopupRedirectOperation } from '../../core/strategies/abstract_popup_redirect_operation';\nimport { FederatedAuthProvider } from '../../core/providers/federated';\nimport { getModularInstance } from '@firebase/util';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\n/*\n * The event timeout is the same on mobile and desktop, no need for Delay. Set this to 8s since\n * blocking functions can take upto 7s to complete sign in, as documented in:\n * https://cloud.google.com/identity-platform/docs/blocking-functions#understanding_blocking_functions\n * https://firebase.google.com/docs/auth/extend-with-blocking-functions#understanding_blocking_functions\n */\nexport const enum _Timeout {\n  AUTH_EVENT = 8000\n}\nexport const _POLL_WINDOW_CLOSE_TIMEOUT = new Delay(2000, 10000);\n\n/**\n * Authenticates a Firebase client using a popup-based OAuth authentication flow.\n *\n * @remarks\n * If succeeds, returns the signed in user along with the provider's credential. If sign in was\n * unsuccessful, returns an error object containing additional information about the error.\n *\n * This method does not work in a Node.js environment or with {@link Auth} instances created with a\n * {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a popup.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithPopup(auth, provider);\n *\n * // The signed-in user info.\n * const user = result.user;\n * // This gives you a Facebook Access Token.\n * const credential = provider.credentialFromResult(auth, result);\n * const token = credential.accessToken;\n * ```\n *\n * @param auth - The {@link Auth} instance.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function signInWithPopup(\n  auth: Auth,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  if (_isFirebaseServerApp(auth.app)) {\n    return Promise.reject(\n      _createError(auth, AuthErrorCode.OPERATION_NOT_SUPPORTED)\n    );\n  }\n  const authInternal = _castAuth(auth);\n  _assertInstanceOf(auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(authInternal, resolver);\n  const action = new PopupOperation(\n    authInternal,\n    AuthEventType.SIGN_IN_VIA_POPUP,\n    provider,\n    resolverInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Reauthenticates the current user with the specified {@link OAuthProvider} using a pop-up based\n * OAuth flow.\n *\n * @remarks\n * If the reauthentication is successful, the returned result will contain the user and the\n * provider's credential.\n *\n * This method does not work in a Node.js environment or on any {@link User} signed in by\n * {@link Auth} instances created with a {@link @firebase/app#FirebaseServerApp}.\n *\n * @example\n * ```javascript\n * // Sign in using a popup.\n * const provider = new FacebookAuthProvider();\n * const result = await signInWithPopup(auth, provider);\n * // Reauthenticate using a popup.\n * await reauthenticateWithPopup(result.user, provider);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function reauthenticateWithPopup(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  if (_isFirebaseServerApp(userInternal.auth.app)) {\n    return Promise.reject(\n      _createError(userInternal.auth, AuthErrorCode.OPERATION_NOT_SUPPORTED)\n    );\n  }\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n  const action = new PopupOperation(\n    userInternal.auth,\n    AuthEventType.REAUTH_VIA_POPUP,\n    provider,\n    resolverInternal,\n    userInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Links the authenticated provider to the user account using a pop-up based OAuth flow.\n *\n * @remarks\n * If the linking is successful, the returned result will contain the user and the provider's credential.\n *\n * This method does not work in a Node.js environment.\n *\n * @example\n * ```javascript\n * // Sign in using some other provider.\n * const result = await signInWithEmailAndPassword(auth, email, password);\n * // Link using a popup.\n * const provider = new FacebookAuthProvider();\n * await linkWithPopup(result.user, provider);\n * ```\n *\n * @param user - The user.\n * @param provider - The provider to authenticate. The provider has to be an {@link OAuthProvider}.\n * Non-OAuth providers like {@link EmailAuthProvider} will throw an error.\n * @param resolver - An instance of {@link PopupRedirectResolver}, optional\n * if already supplied to {@link initializeAuth} or provided by {@link getAuth}.\n *\n * @public\n */\nexport async function linkWithPopup(\n  user: User,\n  provider: AuthProvider,\n  resolver?: PopupRedirectResolver\n): Promise<UserCredential> {\n  const userInternal = getModularInstance(user) as UserInternal;\n  _assertInstanceOf(userInternal.auth, provider, FederatedAuthProvider);\n  const resolverInternal = _withDefaultResolver(userInternal.auth, resolver);\n\n  const action = new PopupOperation(\n    userInternal.auth,\n    AuthEventType.LINK_VIA_POPUP,\n    provider,\n    resolverInternal,\n    userInternal\n  );\n  return action.executeNotNull();\n}\n\n/**\n * Popup event manager. Handles the popup's entire lifecycle; listens to auth\n * events\n *\n */\nclass PopupOperation extends AbstractPopupRedirectOperation {\n  // Only one popup is ever shown at once. The lifecycle of the current popup\n  // can be managed / cancelled by the constructor.\n  private static currentPopupAction: PopupOperation | null = null;\n  private authWindow: AuthPopup | null = null;\n  private pollId: number | null = null;\n\n  constructor(\n    auth: AuthInternal,\n    filter: AuthEventType,\n    private readonly provider: AuthProvider,\n    resolver: PopupRedirectResolverInternal,\n    user?: UserInternal\n  ) {\n    super(auth, filter, resolver, user);\n    if (PopupOperation.currentPopupAction) {\n      PopupOperation.currentPopupAction.cancel();\n    }\n\n    PopupOperation.currentPopupAction = this;\n  }\n\n  async executeNotNull(): Promise<UserCredential> {\n    const result = await this.execute();\n    _assert(result, this.auth, AuthErrorCode.INTERNAL_ERROR);\n    return result;\n  }\n\n  async onExecution(): Promise<void> {\n    debugAssert(\n      this.filter.length === 1,\n      'Popup operations only handle one event'\n    );\n    const eventId = _generateEventId();\n    this.authWindow = await this.resolver._openPopup(\n      this.auth,\n      this.provider,\n      this.filter[0], // There's always one, see constructor\n      eventId\n    );\n    this.authWindow.associatedEvent = eventId;\n\n    // Check for web storage support and origin validation _after_ the popup is\n    // loaded. These operations are slow (~1 second or so) Rather than\n    // waiting on them before opening the window, optimistically open the popup\n    // and check for storage support at the same time. If storage support is\n    // not available, this will cause the whole thing to reject properly. It\n    // will also close the popup, but since the promise has already rejected,\n    // the popup closed by user poll will reject into the void.\n    this.resolver._originValidation(this.auth).catch(e => {\n      this.reject(e);\n    });\n\n    this.resolver._isIframeWebStorageSupported(this.auth, isSupported => {\n      if (!isSupported) {\n        this.reject(\n          _createError(this.auth, AuthErrorCode.WEB_STORAGE_UNSUPPORTED)\n        );\n      }\n    });\n\n    // Handle user closure. Notice this does *not* use await\n    this.pollUserCancellation();\n  }\n\n  get eventId(): string | null {\n    return this.authWindow?.associatedEvent || null;\n  }\n\n  cancel(): void {\n    this.reject(_createError(this.auth, AuthErrorCode.EXPIRED_POPUP_REQUEST));\n  }\n\n  cleanUp(): void {\n    if (this.authWindow) {\n      this.authWindow.close();\n    }\n\n    if (this.pollId) {\n      window.clearTimeout(this.pollId);\n    }\n\n    this.authWindow = null;\n    this.pollId = null;\n    PopupOperation.currentPopupAction = null;\n  }\n\n  private pollUserCancellation(): void {\n    const poll = (): void => {\n      if (this.authWindow?.window?.closed) {\n        // Make sure that there is sufficient time for whatever action to\n        // complete. The window could have closed but the sign in network\n        // call could still be in flight. This is specifically true for\n        // Firefox or if the opener is in an iframe, in which case the oauth\n        // helper closes the popup.\n        this.pollId = window.setTimeout(() => {\n          this.pollId = null;\n          this.reject(\n            _createError(this.auth, AuthErrorCode.POPUP_CLOSED_BY_USER)\n          );\n        }, _Timeout.AUTH_EVENT);\n        return;\n      }\n\n      this.pollId = window.setTimeout(poll, _POLL_WINDOW_CLOSE_TIMEOUT.get());\n    };\n\n    poll();\n  }\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProjectConfig } from '../../api/project_config/get_project_config';\nimport { AuthInternal } from '../../model/auth';\nimport { AuthErrorCode } from '../errors';\nimport { _fail } from './assert';\nimport { _getCurrentUrl } from './location';\n\nconst IP_ADDRESS_REGEX = /^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$/;\nconst HTTP_REGEX = /^https?/;\n\nexport async function _validateOrigin(auth: AuthInternal): Promise<void> {\n  // Skip origin validation if we are in an emulated environment\n  if (auth.config.emulator) {\n    return;\n  }\n\n  const { authorizedDomains } = await _getProjectConfig(auth);\n\n  for (const domain of authorizedDomains) {\n    try {\n      if (matchDomain(domain)) {\n        return;\n      }\n    } catch {\n      // Do nothing if there's a URL error; just continue searching\n    }\n  }\n\n  // In the old SDK, this error also provides helpful messages.\n  _fail(auth, AuthErrorCode.INVALID_ORIGIN);\n}\n\nfunction matchDomain(expected: string): boolean {\n  const currentUrl = _getCurrentUrl();\n  const { protocol, hostname } = new URL(currentUrl);\n  if (expected.startsWith('chrome-extension://')) {\n    const ceUrl = new URL(expected);\n\n    if (ceUrl.hostname === '' && hostname === '') {\n      // For some reason we're not parsing chrome URLs properly\n      return (\n        protocol === 'chrome-extension:' &&\n        expected.replace('chrome-extension://', '') ===\n          currentUrl.replace('chrome-extension://', '')\n      );\n    }\n\n    return protocol === 'chrome-extension:' && ceUrl.hostname === hostname;\n  }\n\n  if (!HTTP_REGEX.test(protocol)) {\n    return false;\n  }\n\n  if (IP_ADDRESS_REGEX.test(expected)) {\n    // The domain has to be exactly equal to the pattern, as an IP domain will\n    // only contain the IP, no extra character.\n    return hostname === expected;\n  }\n\n  // Dots in pattern should be escaped.\n  const escapedDomainPattern = expected.replace(/\\./g, '\\\\.');\n  // Non ip address domains.\n  // domain.com = *.domain.com OR domain.com\n  const re = new RegExp(\n    '^(.+\\\\.' + escapedDomainPattern + '|' + escapedDomainPattern + ')$',\n    'i'\n  );\n  return re.test(hostname);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as js from '../load_js';\n\nconst NETWORK_TIMEOUT = new Delay(30000, 60000);\n\n/**\n * Reset unloaded GApi modules. If gapi.load fails due to a network error,\n * it will stop working after a retrial. This is a hack to fix this issue.\n */\nfunction resetUnloadedGapiModules(): void {\n  // Clear last failed gapi.load state to force next gapi.load to first\n  // load the failed gapi.iframes module.\n  // Get gapix.beacon context.\n  const beacon = _window().___jsl;\n  // Get current hint.\n  if (beacon?.H) {\n    // Get gapi hint.\n    for (const hint of Object.keys(beacon.H)) {\n      // Requested modules.\n      beacon.H[hint].r = beacon.H[hint].r || [];\n      // Loaded modules.\n      beacon.H[hint].L = beacon.H[hint].L || [];\n      // Set requested modules to a copy of the loaded modules.\n      beacon.H[hint].r = [...beacon.H[hint].L];\n      // Clear pending callbacks.\n      if (beacon.CP) {\n        for (let i = 0; i < beacon.CP.length; i++) {\n          // Remove all failed pending callbacks.\n          beacon.CP[i] = null;\n        }\n      }\n    }\n  }\n}\n\nfunction loadGapi(auth: AuthInternal): Promise<gapi.iframes.Context> {\n  return new Promise<gapi.iframes.Context>((resolve, reject) => {\n    // Function to run when gapi.load is ready.\n    function loadGapiIframe(): void {\n      // The developer may have tried to previously run gapi.load and failed.\n      // Run this to fix that.\n      resetUnloadedGapiModules();\n      gapi.load('gapi.iframes', {\n        callback: () => {\n          resolve(gapi.iframes.getContext());\n        },\n        ontimeout: () => {\n          // The above reset may be sufficient, but having this reset after\n          // failure ensures that if the developer calls gapi.load after the\n          // connection is re-established and before another attempt to embed\n          // the iframe, it would work and would not be broken because of our\n          // failed attempt.\n          // Timeout when gapi.iframes.Iframe not loaded.\n          resetUnloadedGapiModules();\n          reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n        },\n        timeout: NETWORK_TIMEOUT.get()\n      });\n    }\n\n    if (_window().gapi?.iframes?.Iframe) {\n      // If gapi.iframes.Iframe available, resolve.\n      resolve(gapi.iframes.getContext());\n    } else if (!!_window().gapi?.load) {\n      // Gapi loader ready, load gapi.iframes.\n      loadGapiIframe();\n    } else {\n      // Create a new iframe callback when this is called so as not to overwrite\n      // any previous defined callback. This happens if this method is called\n      // multiple times in parallel and could result in the later callback\n      // overwriting the previous one. This would end up with a iframe\n      // timeout.\n      const cbName = js._generateCallbackName('iframefcb');\n      // GApi loader not available, dynamically load platform.js.\n      _window()[cbName] = () => {\n        // GApi loader should be ready.\n        if (!!gapi.load) {\n          loadGapiIframe();\n        } else {\n          // Gapi loader failed, throw error.\n          reject(_createError(auth, AuthErrorCode.NETWORK_REQUEST_FAILED));\n        }\n      };\n      // Load GApi loader.\n      return js\n        ._loadJS(`${js._gapiScriptUrl()}?onload=${cbName}`)\n        .catch(e => reject(e));\n    }\n  }).catch(error => {\n    // Reset cached promise to allow for retrial.\n    cachedGApiLoader = null;\n    throw error;\n  });\n}\n\nlet cachedGApiLoader: Promise<gapi.iframes.Context> | null = null;\nexport function _loadGapi(auth: AuthInternal): Promise<gapi.iframes.Context> {\n  cachedGApiLoader = cachedGApiLoader || loadGapi(auth);\n  return cachedGApiLoader;\n}\n\nexport function _resetLoader(): void {\n  cachedGApiLoader = null;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SDK_VERSION } from '@firebase/app';\nimport { querystring } from '@firebase/util';\nimport { DefaultConfig } from '../../../internal';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert, _createError } from '../../core/util/assert';\nimport { Delay } from '../../core/util/delay';\nimport { _emulatorUrl } from '../../core/util/emulator';\nimport { AuthInternal } from '../../model/auth';\nimport { _window } from '../auth_window';\nimport * as gapiLoader from './gapi';\n\nconst PING_TIMEOUT = new Delay(5000, 15000);\nconst IFRAME_PATH = '__/auth/iframe';\nconst EMULATED_IFRAME_PATH = 'emulator/auth/iframe';\n\nconst IFRAME_ATTRIBUTES = {\n  style: {\n    position: 'absolute',\n    top: '-100px',\n    width: '1px',\n    height: '1px'\n  },\n  'aria-hidden': 'true',\n  tabindex: '-1'\n};\n\n// Map from apiHost to endpoint ID for passing into iframe. In current SDK, apiHost can be set to\n// anything (not from a list of endpoints with IDs as in legacy), so this is the closest we can get.\nconst EID_FROM_APIHOST = new Map([\n  [DefaultConfig.API_HOST, 'p'], // production\n  ['staging-identitytoolkit.sandbox.googleapis.com', 's'], // staging\n  ['test-identitytoolkit.sandbox.googleapis.com', 't'] // test\n]);\n\nfunction getIframeUrl(auth: AuthInternal): string {\n  const config = auth.config;\n  _assert(config.authDomain, auth, AuthErrorCode.MISSING_AUTH_DOMAIN);\n  const url = config.emulator\n    ? _emulatorUrl(config, EMULATED_IFRAME_PATH)\n    : `https://${auth.config.authDomain}/${IFRAME_PATH}`;\n\n  const params: Record<string, string> = {\n    apiKey: config.apiKey,\n    appName: auth.name,\n    v: SDK_VERSION\n  };\n  const eid = EID_FROM_APIHOST.get(auth.config.apiHost);\n  if (eid) {\n    params.eid = eid;\n  }\n  const frameworks = auth._getFrameworks();\n  if (frameworks.length) {\n    params.fw = frameworks.join(',');\n  }\n  return `${url}?${querystring(params).slice(1)}`;\n}\n\nexport async function _openIframe(\n  auth: AuthInternal\n): Promise<gapi.iframes.Iframe> {\n  const context = await gapiLoader._loadGapi(auth);\n  const gapi = _window().gapi;\n  _assert(gapi, auth, AuthErrorCode.INTERNAL_ERROR);\n  return context.open(\n    {\n      where: document.body,\n      url: getIframeUrl(auth),\n      messageHandlersFilter: gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER,\n      attributes: IFRAME_ATTRIBUTES,\n      dontclear: true\n    },\n    (iframe: gapi.iframes.Iframe) =>\n      new Promise(async (resolve, reject) => {\n        await iframe.restyle({\n          // Prevent iframe from closing on mouse out.\n          setHideOnLeave: false\n        });\n\n        const networkError = _createError(\n          auth,\n          AuthErrorCode.NETWORK_REQUEST_FAILED\n        );\n        // Confirm iframe is correctly loaded.\n        // To fallback on failure, set a timeout.\n        const networkErrorTimer = _window().setTimeout(() => {\n          reject(networkError);\n        }, PING_TIMEOUT.get());\n        // Clear timer and resolve pending iframe ready promise.\n        function clearTimerAndResolve(): void {\n          _window().clearTimeout(networkErrorTimer);\n          resolve(iframe);\n        }\n        // This returns an IThenable. However the reject part does not call\n        // when the iframe is not loaded.\n        iframe.ping(clearTimerAndResolve).then(clearTimerAndResolve, () => {\n          reject(networkError);\n        });\n      })\n  );\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getUA } from '@firebase/util';\n\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\nimport {\n  _isChromeIOS,\n  _isFirefox,\n  _isIOSStandalone\n} from '../../core/util/browser';\nimport { AuthInternal } from '../../model/auth';\n\nconst BASE_POPUP_OPTIONS = {\n  location: 'yes',\n  resizable: 'yes',\n  statusbar: 'yes',\n  toolbar: 'no'\n};\n\nconst DEFAULT_WIDTH = 500;\nconst DEFAULT_HEIGHT = 600;\nconst TARGET_BLANK = '_blank';\n\nconst FIREFOX_EMPTY_URL = 'http://localhost';\n\nexport class AuthPopup {\n  associatedEvent: string | null = null;\n\n  constructor(readonly window: Window | null) {}\n\n  close(): void {\n    if (this.window) {\n      try {\n        this.window.close();\n      } catch (e) {}\n    }\n  }\n}\n\nexport function _open(\n  auth: AuthInternal,\n  url?: string,\n  name?: string,\n  width = DEFAULT_WIDTH,\n  height = DEFAULT_HEIGHT\n): AuthPopup {\n  const top = Math.max((window.screen.availHeight - height) / 2, 0).toString();\n  const left = Math.max((window.screen.availWidth - width) / 2, 0).toString();\n  let target = '';\n\n  const options: { [key: string]: string } = {\n    ...BASE_POPUP_OPTIONS,\n    width: width.toString(),\n    height: height.toString(),\n    top,\n    left\n  };\n\n  // Chrome iOS 7 and 8 is returning an undefined popup win when target is\n  // specified, even though the popup is not necessarily blocked.\n  const ua = getUA().toLowerCase();\n\n  if (name) {\n    target = _isChromeIOS(ua) ? TARGET_BLANK : name;\n  }\n\n  if (_isFirefox(ua)) {\n    // Firefox complains when invalid URLs are popped out. Hacky way to bypass.\n    url = url || FIREFOX_EMPTY_URL;\n    // Firefox disables by default scrolling on popup windows, which can create\n    // issues when the user has many Google accounts, for instance.\n    options.scrollbars = 'yes';\n  }\n\n  const optionsString = Object.entries(options).reduce(\n    (accum, [key, value]) => `${accum}${key}=${value},`,\n    ''\n  );\n\n  if (_isIOSStandalone(ua) && target !== '_self') {\n    openAsNewWindowIOS(url || '', target);\n    return new AuthPopup(null);\n  }\n\n  // about:blank getting sanitized causing browsers like IE/Edge to display\n  // brief error message before redirecting to handler.\n  const newWin = window.open(url || '', target, optionsString);\n  _assert(newWin, auth, AuthErrorCode.POPUP_BLOCKED);\n\n  // Flaky on IE edge, encapsulate with a try and catch.\n  try {\n    newWin.focus();\n  } catch (e) {}\n\n  return new AuthPopup(newWin);\n}\n\nfunction openAsNewWindowIOS(url: string, target: string): void {\n  const el = document.createElement('a');\n  el.href = url;\n  el.target = target;\n  const click = document.createEvent('MouseEvent');\n  click.initMouseEvent(\n    'click',\n    true,\n    true,\n    window,\n    1,\n    0,\n    0,\n    0,\n    0,\n    false,\n    false,\n    false,\n    false,\n    1,\n    null\n  );\n  el.dispatchEvent(click);\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AuthProvider, PopupRedirectResolver } from '../model/public_types';\n\nimport { AuthEventManager } from '../core/auth/auth_event_manager';\nimport { AuthErrorCode } from '../core/errors';\nimport { _assert, debugAssert, _fail } from '../core/util/assert';\nimport { _generateEventId } from '../core/util/event_id';\nimport { _getCurrentUrl } from '../core/util/location';\nimport { _validateOrigin } from '../core/util/validate_origin';\nimport { AuthInternal } from '../model/auth';\nimport {\n  AuthEventType,\n  EventManager,\n  GapiAuthEvent,\n  GapiOutcome,\n  PopupRedirectResolverInternal\n} from '../model/popup_redirect';\nimport { _setWindowLocation } from './auth_window';\nimport { _openIframe } from './iframe/iframe';\nimport { browserSessionPersistence } from './persistence/session_storage';\nimport { _open, AuthPopup } from './util/popup';\nimport { _getRedirectResult } from './strategies/redirect';\nimport { _getRedirectUrl } from '../core/util/handler';\nimport { _isIOS, _isMobileBrowser, _isSafari } from '../core/util/browser';\nimport { _overrideRedirectResult } from '../core/strategies/redirect';\n\n/**\n * The special web storage event\n *\n */\nconst WEB_STORAGE_SUPPORT_KEY = 'webStorageSupport';\n\ninterface WebStorageSupportMessage extends gapi.iframes.Message {\n  [index: number]: Record<string, boolean>;\n}\n\ninterface ManagerOrPromise {\n  manager?: EventManager;\n  promise?: Promise<EventManager>;\n}\n\nclass BrowserPopupRedirectResolver implements PopupRedirectResolverInternal {\n  private readonly eventManagers: Record<string, ManagerOrPromise> = {};\n  private readonly iframes: Record<string, gapi.iframes.Iframe> = {};\n  private readonly originValidationPromises: Record<string, Promise<void>> = {};\n\n  readonly _redirectPersistence = browserSessionPersistence;\n\n  // Wrapping in async even though we don't await anywhere in order\n  // to make sure errors are raised as promise rejections\n  async _openPopup(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<AuthPopup> {\n    debugAssert(\n      this.eventManagers[auth._key()]?.manager,\n      '_initialize() not called before _openPopup()'\n    );\n\n    const url = await _getRedirectUrl(\n      auth,\n      provider,\n      authType,\n      _getCurrentUrl(),\n      eventId\n    );\n    return _open(auth, url, _generateEventId());\n  }\n\n  async _openRedirect(\n    auth: AuthInternal,\n    provider: AuthProvider,\n    authType: AuthEventType,\n    eventId?: string\n  ): Promise<never> {\n    await this._originValidation(auth);\n    const url = await _getRedirectUrl(\n      auth,\n      provider,\n      authType,\n      _getCurrentUrl(),\n      eventId\n    );\n    _setWindowLocation(url);\n    return new Promise(() => {});\n  }\n\n  _initialize(auth: AuthInternal): Promise<EventManager> {\n    const key = auth._key();\n    if (this.eventManagers[key]) {\n      const { manager, promise } = this.eventManagers[key];\n      if (manager) {\n        return Promise.resolve(manager);\n      } else {\n        debugAssert(promise, 'If manager is not set, promise should be');\n        return promise;\n      }\n    }\n\n    const promise = this.initAndGetManager(auth);\n    this.eventManagers[key] = { promise };\n\n    // If the promise is rejected, the key should be removed so that the\n    // operation can be retried later.\n    promise.catch(() => {\n      delete this.eventManagers[key];\n    });\n\n    return promise;\n  }\n\n  private async initAndGetManager(auth: AuthInternal): Promise<EventManager> {\n    const iframe = await _openIframe(auth);\n    const manager = new AuthEventManager(auth);\n    iframe.register<GapiAuthEvent>(\n      'authEvent',\n      (iframeEvent: GapiAuthEvent | null) => {\n        _assert(iframeEvent?.authEvent, auth, AuthErrorCode.INVALID_AUTH_EVENT);\n        // TODO: Consider splitting redirect and popup events earlier on\n\n        const handled = manager.onEvent(iframeEvent.authEvent);\n        return { status: handled ? GapiOutcome.ACK : GapiOutcome.ERROR };\n      },\n      gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER\n    );\n\n    this.eventManagers[auth._key()] = { manager };\n    this.iframes[auth._key()] = iframe;\n    return manager;\n  }\n\n  _isIframeWebStorageSupported(\n    auth: AuthInternal,\n    cb: (supported: boolean) => unknown\n  ): void {\n    const iframe = this.iframes[auth._key()];\n    iframe.send<gapi.iframes.Message, WebStorageSupportMessage>(\n      WEB_STORAGE_SUPPORT_KEY,\n      { type: WEB_STORAGE_SUPPORT_KEY },\n      result => {\n        const isSupported = result?.[0]?.[WEB_STORAGE_SUPPORT_KEY];\n        if (isSupported !== undefined) {\n          cb(!!isSupported);\n        }\n\n        _fail(auth, AuthErrorCode.INTERNAL_ERROR);\n      },\n      gapi.iframes.CROSS_ORIGIN_IFRAMES_FILTER\n    );\n  }\n\n  _originValidation(auth: AuthInternal): Promise<void> {\n    const key = auth._key();\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n\n    return this.originValidationPromises[key];\n  }\n\n  get _shouldInitProactively(): boolean {\n    // Mobile browsers and Safari need to optimistically initialize\n    return _isMobileBrowser() || _isSafari() || _isIOS();\n  }\n\n  _completeRedirectFn = _getRedirectResult;\n\n  _overrideRedirectResult = _overrideRedirectResult;\n}\n\n/**\n * An implementation of {@link PopupRedirectResolver} suitable for browser\n * based applications.\n *\n * @remarks\n * This method does not work in a Node.js environment.\n *\n * @public\n */\nexport const browserPopupRedirectResolver: PopupRedirectResolver =\n  BrowserPopupRedirectResolver;\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { FactorId, MultiFactorAssertion } from '../model/public_types';\nimport { debugFail } from '../core/util/assert';\nimport { MultiFactorSessionImpl, MultiFactorSessionType } from './mfa_session';\nimport { FinalizeMfaResponse } from '../api/authentication/mfa';\nimport { AuthInternal } from '../model/auth';\n\nexport abstract class MultiFactorAssertionImpl implements MultiFactorAssertion {\n  protected constructor(readonly factorId: FactorId) {}\n\n  _process(\n    auth: AuthInternal,\n    session: MultiFactorSessionImpl,\n    displayName?: string | null\n  ): Promise<FinalizeMfaResponse> {\n    switch (session.type) {\n      case MultiFactorSessionType.ENROLL:\n        return this._finalizeEnroll(auth, session.credential, displayName);\n      case MultiFactorSessionType.SIGN_IN:\n        return this._finalizeSignIn(auth, session.credential);\n      default:\n        return debugFail('unexpected MultiFactorSessionType');\n    }\n  }\n\n  abstract _finalizeEnroll(\n    auth: AuthInternal,\n    idToken: string,\n    displayName?: string | null\n  ): Promise<FinalizeMfaResponse>;\n  abstract _finalizeSignIn(\n    auth: AuthInternal,\n    mfaPendingCredential: string\n  ): Promise<FinalizeMfaResponse>;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  FactorId,\n  PhoneMultiFactorAssertion\n} from '../../../model/public_types';\n\nimport { MultiFactorAssertionImpl } from '../../../mfa/mfa_assertion';\nimport { AuthInternal } from '../../../model/auth';\nimport { finalizeEnrollPhoneMfa } from '../../../api/account_management/mfa';\nimport { PhoneAuthCredential } from '../../../core/credentials/phone';\nimport {\n  finalizeSignInPhoneMfa,\n  FinalizeMfaResponse\n} from '../../../api/authentication/mfa';\n\n/**\n * {@inheritdoc PhoneMultiFactorAssertion}\n *\n * @public\n */\nexport class PhoneMultiFactorAssertionImpl\n  extends MultiFactorAssertionImpl\n  implements PhoneMultiFactorAssertion\n{\n  private constructor(private readonly credential: PhoneAuthCredential) {\n    super(FactorId.PHONE);\n  }\n\n  /** @internal */\n  static _fromCredential(\n    credential: PhoneAuthCredential\n  ): PhoneMultiFactorAssertionImpl {\n    return new PhoneMultiFactorAssertionImpl(credential);\n  }\n\n  /** @internal */\n  _finalizeEnroll(\n    auth: AuthInternal,\n    idToken: string,\n    displayName?: string | null\n  ): Promise<FinalizeMfaResponse> {\n    return finalizeEnrollPhoneMfa(auth, {\n      idToken,\n      displayName,\n      phoneVerificationInfo: this.credential._makeVerificationRequest()\n    });\n  }\n\n  /** @internal */\n  _finalizeSignIn(\n    auth: AuthInternal,\n    mfaPendingCredential: string\n  ): Promise<FinalizeMfaResponse> {\n    return finalizeSignInPhoneMfa(auth, {\n      mfaPendingCredential,\n      phoneVerificationInfo: this.credential._makeVerificationRequest()\n    });\n  }\n}\n\n/**\n * Provider for generating a {@link PhoneMultiFactorAssertion}.\n *\n * @public\n */\nexport class PhoneMultiFactorGenerator {\n  private constructor() {}\n\n  /**\n   * Provides a {@link PhoneMultiFactorAssertion} to confirm ownership of the phone second factor.\n   *\n   * @remarks\n   * This method does not work in a Node.js environment.\n   *\n   * @param phoneAuthCredential - A credential provided by {@link PhoneAuthProvider.credential}.\n   * @returns A {@link PhoneMultiFactorAssertion} which can be used with\n   * {@link MultiFactorResolver.resolveSignIn}\n   */\n  static assertion(credential: PhoneAuthCredential): PhoneMultiFactorAssertion {\n    return PhoneMultiFactorAssertionImpl._fromCredential(credential);\n  }\n\n  /**\n   * The identifier of the phone second factor: `phone`.\n   */\n  static FACTOR_ID = 'phone';\n}\n", "/**\n * @license\n * Copyright 2022 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  TotpMultiFactorAssertion,\n  MultiFactorSession,\n  FactorId\n} from '../../model/public_types';\nimport { AuthInternal } from '../../model/auth';\nimport {\n  finalizeEnrollTotpMfa,\n  startEnrollTotpMfa,\n  StartTotpMfaEnrollmentResponse,\n  TotpVerificationInfo\n} from '../../api/account_management/mfa';\nimport {\n  FinalizeMfaResponse,\n  finalizeSignInTotpMfa\n} from '../../api/authentication/mfa';\nimport { MultiFactorAssertionImpl } from '../../mfa/mfa_assertion';\nimport { MultiFactorSessionImpl } from '../mfa_session';\nimport { AuthErrorCode } from '../../core/errors';\nimport { _assert } from '../../core/util/assert';\n\n/**\n * Provider for generating a {@link TotpMultiFactorAssertion}.\n *\n * @public\n */\nexport class TotpMultiFactorGenerator {\n  /**\n   * Provides a {@link TotpMultiFactorAssertion} to confirm ownership of\n   * the TOTP (time-based one-time password) second factor.\n   * This assertion is used to complete enrollment in TOTP second factor.\n   *\n   * @param secret A {@link TotpSecret} containing the shared secret key and other TOTP parameters.\n   * @param oneTimePassword One-time password from TOTP App.\n   * @returns A {@link TotpMultiFactorAssertion} which can be used with\n   * {@link MultiFactorUser.enroll}.\n   */\n  static assertionForEnrollment(\n    secret: TotpSecret,\n    oneTimePassword: string\n  ): TotpMultiFactorAssertion {\n    return TotpMultiFactorAssertionImpl._fromSecret(secret, oneTimePassword);\n  }\n\n  /**\n   * Provides a {@link TotpMultiFactorAssertion} to confirm ownership of the TOTP second factor.\n   * This assertion is used to complete signIn with TOTP as the second factor.\n   *\n   * @param enrollmentId identifies the enrolled TOTP second factor.\n   * @param oneTimePassword One-time password from TOTP App.\n   * @returns A {@link TotpMultiFactorAssertion} which can be used with\n   * {@link MultiFactorResolver.resolveSignIn}.\n   */\n  static assertionForSignIn(\n    enrollmentId: string,\n    oneTimePassword: string\n  ): TotpMultiFactorAssertion {\n    return TotpMultiFactorAssertionImpl._fromEnrollmentId(\n      enrollmentId,\n      oneTimePassword\n    );\n  }\n\n  /**\n   * Returns a promise to {@link TotpSecret} which contains the TOTP shared secret key and other parameters.\n   * Creates a TOTP secret as part of enrolling a TOTP second factor.\n   * Used for generating a QR code URL or inputting into a TOTP app.\n   * This method uses the auth instance corresponding to the user in the multiFactorSession.\n   *\n   * @param session The {@link MultiFactorSession} that the user is part of.\n   * @returns A promise to {@link TotpSecret}.\n   */\n  static async generateSecret(\n    session: MultiFactorSession\n  ): Promise<TotpSecret> {\n    const mfaSession = session as MultiFactorSessionImpl;\n    _assert(\n      typeof mfaSession.user?.auth !== 'undefined',\n      AuthErrorCode.INTERNAL_ERROR\n    );\n    const response = await startEnrollTotpMfa(mfaSession.user.auth, {\n      idToken: mfaSession.credential,\n      totpEnrollmentInfo: {}\n    });\n    return TotpSecret._fromStartTotpMfaEnrollmentResponse(\n      response,\n      mfaSession.user.auth\n    );\n  }\n\n  /**\n   * The identifier of the TOTP second factor: `totp`.\n   */\n  static FACTOR_ID: 'totp' = FactorId.TOTP;\n}\n\nexport class TotpMultiFactorAssertionImpl\n  extends MultiFactorAssertionImpl\n  implements TotpMultiFactorAssertion\n{\n  constructor(\n    readonly otp: string,\n    readonly enrollmentId?: string,\n    readonly secret?: TotpSecret\n  ) {\n    super(FactorId.TOTP);\n  }\n\n  /** @internal */\n  static _fromSecret(\n    secret: TotpSecret,\n    otp: string\n  ): TotpMultiFactorAssertionImpl {\n    return new TotpMultiFactorAssertionImpl(otp, undefined, secret);\n  }\n\n  /** @internal */\n  static _fromEnrollmentId(\n    enrollmentId: string,\n    otp: string\n  ): TotpMultiFactorAssertionImpl {\n    return new TotpMultiFactorAssertionImpl(otp, enrollmentId);\n  }\n\n  /** @internal */\n  async _finalizeEnroll(\n    auth: AuthInternal,\n    idToken: string,\n    displayName?: string | null\n  ): Promise<FinalizeMfaResponse> {\n    _assert(\n      typeof this.secret !== 'undefined',\n      auth,\n      AuthErrorCode.ARGUMENT_ERROR\n    );\n    return finalizeEnrollTotpMfa(auth, {\n      idToken,\n      displayName,\n      totpVerificationInfo: this.secret._makeTotpVerificationInfo(this.otp)\n    });\n  }\n\n  /** @internal */\n  async _finalizeSignIn(\n    auth: AuthInternal,\n    mfaPendingCredential: string\n  ): Promise<FinalizeMfaResponse> {\n    _assert(\n      this.enrollmentId !== undefined && this.otp !== undefined,\n      auth,\n      AuthErrorCode.ARGUMENT_ERROR\n    );\n    const totpVerificationInfo = { verificationCode: this.otp };\n    return finalizeSignInTotpMfa(auth, {\n      mfaPendingCredential,\n      mfaEnrollmentId: this.enrollmentId,\n      totpVerificationInfo\n    });\n  }\n}\n\n/**\n * Provider for generating a {@link TotpMultiFactorAssertion}.\n *\n * Stores the shared secret key and other parameters to generate time-based OTPs.\n * Implements methods to retrieve the shared secret key and generate a QR code URL.\n * @public\n */\nexport class TotpSecret {\n  /**\n   * Shared secret key/seed used for enrolling in TOTP MFA and generating OTPs.\n   */\n  readonly secretKey: string;\n  /**\n   * Hashing algorithm used.\n   */\n  readonly hashingAlgorithm: string;\n  /**\n   * Length of the one-time passwords to be generated.\n   */\n  readonly codeLength: number;\n  /**\n   * The interval (in seconds) when the OTP codes should change.\n   */\n  readonly codeIntervalSeconds: number;\n  /**\n   * The timestamp (UTC string) by which TOTP enrollment should be completed.\n   */\n  // This can be used by callers to show a countdown of when to enter OTP code by.\n  readonly enrollmentCompletionDeadline: string;\n\n  // The public members are declared outside the constructor so the docs can be generated.\n  private constructor(\n    secretKey: string,\n    hashingAlgorithm: string,\n    codeLength: number,\n    codeIntervalSeconds: number,\n    enrollmentCompletionDeadline: string,\n    private readonly sessionInfo: string,\n    private readonly auth: AuthInternal\n  ) {\n    this.secretKey = secretKey;\n    this.hashingAlgorithm = hashingAlgorithm;\n    this.codeLength = codeLength;\n    this.codeIntervalSeconds = codeIntervalSeconds;\n    this.enrollmentCompletionDeadline = enrollmentCompletionDeadline;\n  }\n\n  /** @internal */\n  static _fromStartTotpMfaEnrollmentResponse(\n    response: StartTotpMfaEnrollmentResponse,\n    auth: AuthInternal\n  ): TotpSecret {\n    return new TotpSecret(\n      response.totpSessionInfo.sharedSecretKey,\n      response.totpSessionInfo.hashingAlgorithm,\n      response.totpSessionInfo.verificationCodeLength,\n      response.totpSessionInfo.periodSec,\n      new Date(response.totpSessionInfo.finalizeEnrollmentTime).toUTCString(),\n      response.totpSessionInfo.sessionInfo,\n      auth\n    );\n  }\n\n  /** @internal */\n  _makeTotpVerificationInfo(otp: string): TotpVerificationInfo {\n    return { sessionInfo: this.sessionInfo, verificationCode: otp };\n  }\n\n  /**\n   * Returns a QR code URL as described in\n   * https://github.com/google/google-authenticator/wiki/Key-Uri-Format\n   * This can be displayed to the user as a QR code to be scanned into a TOTP app like Google Authenticator.\n   * If the optional parameters are unspecified, an accountName of <userEmail> and issuer of <firebaseAppName> are used.\n   *\n   * @param accountName the name of the account/app along with a user identifier.\n   * @param issuer issuer of the TOTP (likely the app name).\n   * @returns A QR code URL string.\n   */\n  generateQrCodeUrl(accountName?: string, issuer?: string): string {\n    let useDefaults = false;\n    if (_isEmptyString(accountName) || _isEmptyString(issuer)) {\n      useDefaults = true;\n    }\n    if (useDefaults) {\n      if (_isEmptyString(accountName)) {\n        accountName = this.auth.currentUser?.email || 'unknownuser';\n      }\n      if (_isEmptyString(issuer)) {\n        issuer = this.auth.name;\n      }\n    }\n    return `otpauth://totp/${issuer}:${accountName}?secret=${this.secretKey}&issuer=${issuer}&algorithm=${this.hashingAlgorithm}&digits=${this.codeLength}`;\n  }\n}\n\n/** @internal */\nfunction _isEmptyString(input?: string): boolean {\n  return typeof input === 'undefined' || input?.length === 0;\n}\n", "/**\n * @license\n * Copyright 2021 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\n\nimport {\n  initializeAuth,\n  beforeAuthStateChanged,\n  onIdTokenChanged,\n  connectAuthEmulator\n} from '..';\nimport { registerAuth } from '../core/auth/register';\nimport { ClientPlatform } from '../core/util/version';\nimport { browserLocalPersistence } from './persistence/local_storage';\nimport { browserSessionPersistence } from './persistence/session_storage';\nimport { indexedDBLocalPersistence } from './persistence/indexed_db';\nimport { browserPopupRedirectResolver } from './popup_redirect';\nimport { Auth, User } from '../model/public_types';\nimport { getDefaultEmulatorHost, getExperimentalSetting } from '@firebase/util';\nimport { _setExternalJSProvider } from './load_js';\nimport { _createError } from '../core/util/assert';\nimport { AuthErrorCode } from '../core/errors';\n\nconst DEFAULT_ID_TOKEN_MAX_AGE = 5 * 60;\nconst authIdTokenMaxAge =\n  getExperimentalSetting('authIdTokenMaxAge') || DEFAULT_ID_TOKEN_MAX_AGE;\n\nlet lastPostedIdToken: string | undefined | null = null;\n\nconst mintCookieFactory = (url: string) => async (user: User | null) => {\n  const idTokenResult = user && (await user.getIdTokenResult());\n  const idTokenAge =\n    idTokenResult &&\n    (new Date().getTime() - Date.parse(idTokenResult.issuedAtTime)) / 1_000;\n  if (idTokenAge && idTokenAge > authIdTokenMaxAge) {\n    return;\n  }\n  // Specifically trip null => undefined when logged out, to delete any existing cookie\n  const idToken = idTokenResult?.token;\n  if (lastPostedIdToken === idToken) {\n    return;\n  }\n  lastPostedIdToken = idToken;\n  await fetch(url, {\n    method: idToken ? 'POST' : 'DELETE',\n    headers: idToken\n      ? {\n          'Authorization': `Bearer ${idToken}`\n        }\n      : {}\n  });\n};\n\n/**\n * Returns the Auth instance associated with the provided {@link @firebase/app#FirebaseApp}.\n * If no instance exists, initializes an Auth instance with platform-specific default dependencies.\n *\n * @param app - The Firebase App.\n *\n * @public\n */\nexport function getAuth(app: FirebaseApp = getApp()): Auth {\n  const provider = _getProvider(app, 'auth');\n\n  if (provider.isInitialized()) {\n    return provider.getImmediate();\n  }\n\n  const auth = initializeAuth(app, {\n    popupRedirectResolver: browserPopupRedirectResolver,\n    persistence: [\n      indexedDBLocalPersistence,\n      browserLocalPersistence,\n      browserSessionPersistence\n    ]\n  });\n\n  const authTokenSyncPath = getExperimentalSetting('authTokenSyncURL');\n  // Only do the Cookie exchange in a secure context\n  if (\n    authTokenSyncPath &&\n    typeof isSecureContext === 'boolean' &&\n    isSecureContext\n  ) {\n    // Don't allow urls (XSS possibility), only paths on the same domain\n    const authTokenSyncUrl = new URL(authTokenSyncPath, location.origin);\n    if (location.origin === authTokenSyncUrl.origin) {\n      const mintCookie = mintCookieFactory(authTokenSyncUrl.toString());\n      beforeAuthStateChanged(auth, mintCookie, () =>\n        mintCookie(auth.currentUser)\n      );\n      onIdTokenChanged(auth, user => mintCookie(user));\n    }\n  }\n\n  const authEmulatorHost = getDefaultEmulatorHost('auth');\n  if (authEmulatorHost) {\n    connectAuthEmulator(auth, `http://${authEmulatorHost}`);\n  }\n\n  return auth;\n}\n\nfunction getScriptParentElement(): HTMLDocument | HTMLHeadElement {\n  return document.getElementsByTagName('head')?.[0] ?? document;\n}\n\n_setExternalJSProvider({\n  loadJS(url: string): Promise<Event> {\n    // TODO: consider adding timeout support & cancellation\n    return new Promise((resolve, reject) => {\n      const el = document.createElement('script');\n      el.setAttribute('src', url);\n      el.onload = resolve;\n      el.onerror = e => {\n        const error = _createError(AuthErrorCode.INTERNAL_ERROR);\n        error.customData = e as unknown as Record<string, unknown>;\n        reject(error);\n      };\n      el.type = 'text/javascript';\n      el.charset = 'UTF-8';\n      getScriptParentElement().appendChild(el);\n    });\n  },\n\n  gapiScript: 'https://apis.google.com/js/api.js',\n  recaptchaV2Script: 'https://www.google.com/recaptcha/api.js',\n  recaptchaEnterpriseScript:\n    'https://www.google.com/recaptcha/enterprise.js?render='\n});\n\nregisterAuth(ClientPlatform.BROWSER);\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _castAuth } from '../src/core/auth/auth_impl';\nimport { Auth } from '../src/model/public_types';\n\n/**\n * This interface is intended only for use by @firebase/auth-compat, do not use directly\n */\nexport * from '../index';\n\nexport { SignInWithIdpResponse } from '../src/api/authentication/idp';\nexport { AuthErrorCode } from '../src/core/errors';\nexport { PersistenceInternal } from '../src/core/persistence';\nexport { _persistenceKeyName } from '../src/core/persistence/persistence_user_manager';\nexport { UserImpl } from '../src/core/user/user_impl';\nexport { _getInstance } from '../src/core/util/instantiator';\nexport {\n  PopupRedirectResolverInternal,\n  EventManager,\n  AuthEventType\n} from '../src/model/popup_redirect';\nexport { UserCredentialInternal, UserParameters } from '../src/model/user';\nexport { AuthInternal, ConfigInternal } from '../src/model/auth';\nexport { DefaultConfig, AuthImpl, _castAuth } from '../src/core/auth/auth_impl';\n\nexport { ClientPlatform, _getClientVersion } from '../src/core/util/version';\n\nexport { _generateEventId } from '../src/core/util/event_id';\nexport { TaggedWithTokenResponse } from '../src/model/id_token';\nexport { _fail, _assert } from '../src/core/util/assert';\nexport { AuthPopup } from '../src/platform_browser/util/popup';\nexport { _getRedirectResult } from '../src/platform_browser/strategies/redirect';\nexport { _overrideRedirectResult } from '../src/core/strategies/redirect';\nexport { cordovaPopupRedirectResolver } from '../src/platform_cordova/popup_redirect/popup_redirect';\nexport { FetchProvider } from '../src/core/util/fetch_provider';\nexport { SAMLAuthCredential } from '../src/core/credentials/saml';\n\n// This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n// It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it out\n// of autogenerated documentation pages to reduce accidental misuse.\nexport function addFrameworkForLogging(auth: Auth, framework: string): void {\n  _castAuth(auth)._logFramework(framework);\n}\n"], "names": ["jsHelpers._generateCallbackName", "jsHelpers._recaptchaV2ScriptUrl", "jsHelpers._loadJS", "js._generateCallbackName", "js\n                ._loadJS", "js._gapiScriptUrl", "gapiLoader._loadGapi"], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAKH,MAAM,mBAAmB,GAAG,IAAK,CAAC;AASlC;AACA,SAAS,iBAAiB,CAAC,IAAY,EAAA;;IACrC,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;IAChE,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,WAAW,CAAA,QAAA,CAAU,CAAC,CAAC;AACjD,IAAA,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC;AACrD,CAAC;AAED;AACA,SAAS,aAAa,CAAC,GAAW,EAAA;;;;IAIhC,MAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,KAAK,OAAO,CAAC;IACvD,OAAO,CAAA,EAAG,SAAS,GAAG,QAAQ,GAAG,SAAS,YAAY,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA,CAAE,CAAC;AAC5E,CAAC;MAEY,iBAAiB,CAAA;AAA9B,IAAA,WAAA,GAAA;AAEW,QAAA,IAAA,CAAA,IAAI,GAA0B,QAAA,8BAAA;AACvC,QAAA,IAAA,CAAA,oBAAoB,GAA0C,IAAI,GAAG,EAAE,CAAC;KA0GzE;;AAvGC,IAAA,eAAe,CAAC,WAAmB,EAAA;AACjC,QAAA,IAAI,OAAO,MAAM,KAAK,SAAS,EAAE;AAC/B,YAAA,OAAO,WAAW,CAAC;SACpB;AACD,QAAA,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,CAAA,EAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAA,YAAA,CAAc,CAAC,CAAC;QAC7D,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;AACjD,QAAA,OAAO,GAAG,CAAC;KACZ;;;;AAKD,IAAA,MAAM,YAAY,GAAA;;QAChB,IAAI,OAAO,eAAe,KAAK,SAAS,IAAI,CAAC,eAAe,EAAE;AAC5D,YAAA,OAAO,KAAK,CAAC;SACd;QACD,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;AACvE,YAAA,OAAO,KAAK,CAAC;SACd;AACD,QAAA,OAAO,MAAA,SAAS,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,IAAI,CAAC;KACxC;;AAGD,IAAA,MAAM,IAAI,CAAC,IAAY,EAAE,MAAwB,EAAA;QAC/C,OAAO;KACR;;IAGD,MAAM,IAAI,CAA6B,GAAW,EAAA;AAChD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;AACxB,YAAA,OAAO,IAAI,CAAC;SACb;AACD,QAAA,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AAChC,QAAA,IAAI,MAAM,CAAC,WAAW,EAAE;YACtB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAClD,YAAA,OAAO,MAAM,KAAN,IAAA,IAAA,MAAM,uBAAN,MAAM,CAAE,KAAU,CAAC;SAC3B;AACD,QAAA,OAAO,iBAAiB,CAAC,IAAI,CAAM,CAAC;KACrC;;IAGD,MAAM,OAAO,CAAC,GAAW,EAAA;AACvB,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;YACxB,OAAO;SACR;;;;QAID,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,aAAa,EAAE;YAClB,OAAO;SACR;AACD,QAAA,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AAChC,QAAA,QAAQ,CAAC,MAAM,GAAG,CAAG,EAAA,IAAI,4EAA4E,CAAC;AACtG,QAAA,MAAM,KAAK,CAAC,CAAA,YAAA,CAAc,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,MAAM,SAAS,CAAC,CAAC;KAC1E;;IAGD,YAAY,CAAC,GAAW,EAAE,QAA8B,EAAA;AACtD,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE;YACxB,OAAO;SACR;AACD,QAAA,MAAM,IAAI,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;AAChC,QAAA,IAAI,MAAM,CAAC,WAAW,EAAE;AACtB,YAAA,MAAM,EAAE,IAAI,CAAC,KAAwB,KAAU;AAC7C,gBAAA,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CACtC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAC/B,CAAC;gBACF,IAAI,aAAa,EAAE;AACjB,oBAAA,QAAQ,CAAC,aAAa,CAAC,KAAyB,CAAC,CAAC;iBACnD;AACD,gBAAA,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CACtC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAC/B,CAAC;gBACF,IAAI,aAAa,EAAE;oBACjB,QAAQ,CAAC,IAAI,CAAC,CAAC;iBAChB;AACH,aAAC,CAAkB,CAAC;AACpB,YAAA,MAAM,WAAW,GAAG,MAClB,MAAM,CAAC,WAAW,CAAC,mBAAmB,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;YACvD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YACrD,OAAO,MAAM,CAAC,WAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE,EAAmB,CAAC,CAAC;SAC3E;AACD,QAAA,IAAI,SAAS,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACxC,QAAA,MAAM,QAAQ,GAAG,WAAW,CAAC,MAAK;AAChC,YAAA,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAC7C,YAAA,IAAI,YAAY,KAAK,SAAS,EAAE;gBAC9B,QAAQ,CAAC,YAAuC,CAAC,CAAC;gBAClD,SAAS,GAAG,YAAY,CAAC;aAC1B;SACF,EAAE,mBAAmB,CAAC,CAAC;QACxB,MAAM,WAAW,GAAG,MAAY,aAAa,CAAC,QAAQ,CAAC,CAAC;QACxD,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;KACtD;IAED,eAAe,CAAC,IAAY,EAAE,QAA8B,EAAA;QAC1D,MAAM,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,WAAW,EAAE;YAChB,OAAO;SACR;AACD,QAAA,WAAW,EAAE,CAAC;AACd,QAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;KAC5C;;AA3GM,iBAAI,CAAA,IAAA,GAAa,QAAb,CAAsB;AA8GnC;;;;;;;;AAQG;AACI,MAAM,wBAAwB,GAAgB;;ACrKrD;;;;;;;;;;;;;;;AAeG;AAoDa,SAAA,mBAAmB,CACjC,IAAU,EACV,OAAmC,EAAA;IAEnC,OAAO,kBAAkB,CAIvB,IAAI,EAGJ,MAAA,wBAAA,8BAAA,mCAAA,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAClC,CAAC;AACJ,CAAC;AAsBe,SAAA,sBAAsB,CACpC,IAAU,EACV,OAAsC,EAAA;IAEtC,OAAO,kBAAkB,CAIvB,IAAI,EAGJ,MAAA,wBAAA,iCAAA,sCAAA,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAClC,CAAC;AACJ,CAAC;AAEe,SAAA,qBAAqB,CACnC,IAAU,EACV,OAAqC,EAAA;IAErC,OAAO,kBAAkB,CAIvB,IAAI,EAGJ,MAAA,wBAAA,iCAAA,sCAAA,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAClC,CAAC;AACJ;;AClIA;;;;;;;;;;;;;;;AAeG;AAaH;AACA;AACO,MAAM,gBAAgB,GAAGA,qBAA+B,CAAC,KAAK,CAAC,CAAC;AACvE,MAAM,qBAAqB,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAatD;;AAEG;MACU,mBAAmB,CAAA;AAAhC,IAAA,WAAA,GAAA;;QACU,IAAY,CAAA,YAAA,GAAG,EAAE,CAAC;QAClB,IAAO,CAAA,OAAA,GAAG,CAAC,CAAC;AACpB;;;;AAIG;AACc,QAAA,IAAA,CAAA,uBAAuB,GAAG,CAAC,EAAC,CAAA,EAAA,GAAA,OAAO,EAAE,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAA,CAAC;KAqE3E;AAnEC,IAAA,IAAI,CAAC,IAAkB,EAAE,EAAE,GAAG,EAAE,EAAA;QAC9B,OAAO,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAE,IAAI,sDAA+B,CAAC;AAErE,QAAA,IAAI,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE;YACnE,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,UAAwB,CAAC,CAAC;SAC5D;QACD,OAAO,IAAI,OAAO,CAAY,CAAC,OAAO,EAAE,MAAM,KAAI;YAChD,MAAM,cAAc,GAAG,OAAO,EAAE,CAAC,UAAU,CAAC,MAAK;AAC/C,gBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;AACnE,aAAC,EAAE,qBAAqB,CAAC,GAAG,EAAE,CAAC,CAAC;AAEhC,YAAA,OAAO,EAAE,CAAC,gBAAgB,CAAC,GAAG,MAAK;AACjC,gBAAA,OAAO,EAAE,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;AACvC,gBAAA,OAAO,OAAO,EAAE,CAAC,gBAAgB,CAAC,CAAC;AAEnC,gBAAA,MAAM,SAAS,GAAG,OAAO,EAAE,CAAC,UAAuB,CAAC;gBAEpD,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;AAClC,oBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC,CAAC;oBACzD,OAAO;iBACR;;;AAID,gBAAA,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;gBAChC,SAAS,CAAC,MAAM,GAAG,CAAC,SAAS,EAAE,MAAM,KAAI;oBACvC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;oBAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,oBAAA,OAAO,QAAQ,CAAC;AAClB,iBAAC,CAAC;AAEF,gBAAA,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;gBACvB,OAAO,CAAC,SAAS,CAAC,CAAC;AACrB,aAAC,CAAC;YAEF,MAAM,GAAG,GAAG,CAAG,EAAAC,qBAA+B,EAAE,CAAI,CAAA,EAAA,WAAW,CAAC;AAC9D,gBAAA,MAAM,EAAE,gBAAgB;AACxB,gBAAA,MAAM,EAAE,UAAU;gBAClB,EAAE;AACH,aAAA,CAAC,EAAE,CAAC;YAELC,OAAiB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,MAAK;gBAChC,YAAY,CAAC,cAAc,CAAC,CAAC;AAC7B,gBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC,CAAC;AAC3D,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;IAED,kBAAkB,GAAA;QAChB,IAAI,CAAC,OAAO,EAAE,CAAC;KAChB;AAEO,IAAA,wBAAwB,CAAC,EAAU,EAAA;;;;;;;;;QAQzC,QACE,CAAC,EAAC,CAAA,EAAA,GAAA,OAAO,EAAE,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,CAAA;AAC9B,aAAC,EAAE,KAAK,IAAI,CAAC,YAAY;gBACvB,IAAI,CAAC,OAAO,GAAG,CAAC;AAChB,gBAAA,IAAI,CAAC,uBAAuB,CAAC,EAC/B;KACH;AACF,CAAA;AAED,SAAS,mBAAmB,CAAC,EAAU,EAAA;AACrC,IAAA,OAAO,EAAE,CAAC,MAAM,IAAI,CAAC,IAAI,wBAAwB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC7D,CAAC;MAEY,uBAAuB,CAAA;IAClC,MAAM,IAAI,CAAC,IAAkB,EAAA;AAC3B,QAAA,OAAO,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;KAChC;AAED,IAAA,kBAAkB,MAAW;AAC9B;;ACxID;;;;;;;;;;;;;;;AAeG;AAmBI,MAAM,uBAAuB,GAAG,WAAW,CAAC;AAEnD,MAAM,cAAc,GAAwB;AAC1C,IAAA,KAAK,EAAE,OAAO;AACd,IAAA,IAAI,EAAE,OAAO;CACd,CAAC;AAIF;;;;;;;AAOG;MACU,iBAAiB,CAAA;AAoB5B;;;;;;;;;;;;;;;;;;AAkBG;AACH,IAAA,WAAA,CACE,UAAgB,EAChB,aAAmC,EAClB,UAAA,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACZ,cAAc,CAClB,EAAA;QAFgB,IAAU,CAAA,UAAA,GAAV,UAAU,CAE1B;AA3CH;;;;;AAKG;QACM,IAAI,CAAA,IAAA,GAAG,uBAAuB,CAAC;QAChC,IAAS,CAAA,SAAA,GAAG,KAAK,CAAC;QAClB,IAAQ,CAAA,QAAA,GAAkB,IAAI,CAAC;AAGtB,QAAA,IAAA,CAAA,oBAAoB,GAAG,IAAI,GAAG,EAAiB,CAAC;QACzD,IAAa,CAAA,aAAA,GAA2B,IAAI,CAAC;QAK7C,IAAS,CAAA,SAAA,GAAqB,IAAI,CAAC;AA4BzC,QAAA,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,WAAW,CAAC;QACxD,OAAO,CACL,OAAO,QAAQ,KAAK,WAAW,EAC/B,IAAI,CAAC,IAAI,EAAA,6CAAA,6CAEV,CAAC;AACF,QAAA,MAAM,SAAS,GACb,OAAO,aAAa,KAAK,QAAQ;AAC/B,cAAE,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC;cACtC,aAAa,CAAC;AACpB,QAAA,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,sDAA+B,CAAC;AAE5D,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAE5E,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,iCAAiC;cACxE,IAAI,uBAAuB,EAAE;AAC/B,cAAE,IAAI,mBAAmB,EAAE,CAAC;QAE9B,IAAI,CAAC,qBAAqB,EAAE,CAAC;;KAE9B;AAED;;;;AAIG;AACH,IAAA,MAAM,MAAM,GAAA;QACV,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,MAAM,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AAC/B,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE9C,MAAM,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAC3C,IAAI,QAAQ,EAAE;AACZ,YAAA,OAAO,QAAQ,CAAC;SACjB;AAED,QAAA,OAAO,IAAI,OAAO,CAAS,OAAO,IAAG;AACnC,YAAA,MAAM,WAAW,GAAG,CAAC,KAAa,KAAU;gBAC1C,IAAI,CAAC,KAAK,EAAE;AACV,oBAAA,OAAO;iBACR;AACD,gBAAA,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;gBAC9C,OAAO,CAAC,KAAK,CAAC,CAAC;AACjB,aAAC,CAAC;AAEF,YAAA,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC3C,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;aACvB;AACH,SAAC,CAAC,CAAC;KACJ;AAED;;;;AAIG;IACH,MAAM,GAAA;AACJ,QAAA,IAAI;YACF,IAAI,CAAC,kBAAkB,EAAE,CAAC;SAC3B;QAAC,OAAO,CAAC,EAAE;;;;AAIV,YAAA,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC1B;AAED,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,OAAO,IAAI,CAAC,aAAa,CAAC;SAC3B;AAED,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,KAAK,CAAC,CAAC,IAAG;AACtD,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1B,YAAA,MAAM,CAAC,CAAC;AACV,SAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,aAAa,CAAC;KAC3B;;IAGD,MAAM,GAAA;QACJ,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;YAC1B,IAAI,CAAC,oBAAoB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAClD;KACF;AAED;;AAEG;IACH,KAAK,GAAA;QACH,IAAI,CAAC,kBAAkB,EAAE,CAAC;AAC1B,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACtB,QAAA,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;AAC3C,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAG;AACvC,gBAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACnC,aAAC,CAAC,CAAC;SACJ;KACF;IAEO,qBAAqB,GAAA;AAC3B,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;AAC3E,QAAA,OAAO,CACL,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,EAAE,EACnD,IAAI,CAAC,IAAI,sDAEV,CAAC;QACF,OAAO,CACL,OAAO,QAAQ,KAAK,WAAW,EAC/B,IAAI,CAAC,IAAI,EAAA,6CAAA,6CAEV,CAAC;KACH;AAEO,IAAA,iBAAiB,CACvB,QAA4C,EAAA;QAE5C,OAAO,KAAK,IAAG;AACb,YAAA,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/D,YAAA,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;gBAClC,QAAQ,CAAC,KAAK,CAAC,CAAC;aACjB;AAAM,iBAAA,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AACvC,gBAAA,MAAM,UAAU,GAAG,OAAO,EAAE,CAAC,QAAQ,CAAC,CAAC;AACvC,gBAAA,IAAI,OAAO,UAAU,KAAK,UAAU,EAAE;oBACpC,UAAU,CAAC,KAAK,CAAC,CAAC;iBACnB;aACF;AACH,SAAC,CAAC;KACH;IAEO,kBAAkB,GAAA;QACxB,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;KACnE;AAEO,IAAA,MAAM,iBAAiB,GAAA;AAC7B,QAAA,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;AAClB,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAC/B,YAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrB,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACtD,gBAAA,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;gBACvC,SAAS,GAAG,eAAe,CAAC;aAC7B;AAED,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC,MAAM,CAChD,SAAS,EACT,IAAI,CAAC,UAAU,CAChB,CAAC;SACH;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAEO,IAAA,MAAM,IAAI,GAAA;AAChB,QAAA,OAAO,CACL,cAAc,EAAE,IAAI,CAAC,SAAS,EAAE,EAChC,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAEV,CAAC;QAEF,MAAM,QAAQ,EAAE,CAAC;QACjB,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAC/C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,SAAS,CACpC,CAAC;QAEF,MAAM,OAAO,GAAG,MAAM,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpD,QAAA,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,sDAA+B,CAAC;AAC1D,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;KACnC;IAEO,oBAAoB,GAAA;QAC1B,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;QACjE,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AACF,CAAA;AAED,SAAS,QAAQ,GAAA;IACf,IAAI,QAAQ,GAAwB,IAAI,CAAC;AACzC,IAAA,OAAO,IAAI,OAAO,CAAO,OAAO,IAAG;AACjC,QAAA,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE;AACtC,YAAA,OAAO,EAAE,CAAC;YACV,OAAO;SACR;;;;AAKD,QAAA,QAAQ,GAAG,MAAM,OAAO,EAAE,CAAC;AAC3B,QAAA,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5C,KAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAG;QACX,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SAC9C;AAED,QAAA,MAAM,CAAC,CAAC;AACV,KAAC,CAAC,CAAC;AACL;;AC1SA;;;;;;;;;;;;;;;AAeG;AAiEH,MAAM,sBAAsB,CAAA;IAC1B,WACW,CAAA,cAAsB,EACd,cAAsC,EAAA;QAD9C,IAAc,CAAA,cAAA,GAAd,cAAc,CAAQ;QACd,IAAc,CAAA,cAAA,GAAd,cAAc,CAAwB;KACrD;AAEJ,IAAA,OAAO,CAAC,gBAAwB,EAAA;AAC9B,QAAA,MAAM,cAAc,GAAG,mBAAmB,CAAC,iBAAiB,CAC1D,IAAI,CAAC,cAAc,EACnB,gBAAgB,CACjB,CAAC;AACF,QAAA,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;KAC5C;AACF,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCG;AACI,eAAe,qBAAqB,CACzC,IAAU,EACV,WAAmB,EACnB,WAAiC,EAAA;AAEjC,IAAA,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAClC,OAAO,OAAO,CAAC,MAAM,CACnB,+CAA+C,CAAC,IAAI,CAAC,CACtD,CAAC;KACH;AACD,IAAA,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAC7C,YAAY,EACZ,WAAW,EACX,kBAAkB,CAAC,WAA0C,CAAC,CAC/D,CAAC;AACF,IAAA,OAAO,IAAI,sBAAsB,CAAC,cAAc,EAAE,IAAI,IACpD,oBAAoB,CAAC,YAAY,EAAE,IAAI,CAAC,CACzC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;AAWG;AACI,eAAe,mBAAmB,CACvC,IAAU,EACV,WAAmB,EACnB,WAAiC,EAAA;AAEjC,IAAA,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAiB,CAAC;AAC9D,IAAA,MAAM,mBAAmB,CAAC,KAAK,EAAE,YAAY,iCAAmB,CAAC;AACjE,IAAA,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAC7C,YAAY,CAAC,IAAI,EACjB,WAAW,EACX,kBAAkB,CAAC,WAA0C,CAAC,CAC/D,CAAC;AACF,IAAA,OAAO,IAAI,sBAAsB,CAAC,cAAc,EAAE,IAAI,IACpD,kBAAkB,CAAC,YAAY,EAAE,IAAI,CAAC,CACvC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;AAcG;AACI,eAAe,6BAA6B,CACjD,IAAU,EACV,WAAmB,EACnB,WAAiC,EAAA;AAEjC,IAAA,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAiB,CAAC;IAC9D,IAAI,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC/C,OAAO,OAAO,CAAC,MAAM,CACnB,+CAA+C,CAAC,YAAY,CAAC,IAAI,CAAC,CACnE,CAAC;KACH;AACD,IAAA,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAC7C,YAAY,CAAC,IAAI,EACjB,WAAW,EACX,kBAAkB,CAAC,WAA0C,CAAC,CAC/D,CAAC;AACF,IAAA,OAAO,IAAI,sBAAsB,CAAC,cAAc,EAAE,IAAI,IACpD,4BAA4B,CAAC,YAAY,EAAE,IAAI,CAAC,CACjD,CAAC;AACJ,CAAC;AAOD;;;AAGG;AACI,eAAe,kBAAkB,CACtC,IAAkB,EAClB,OAAkC,EAClC,QAAsC,EAAA;;AAEtC,IAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE;AAC/B,QAAA,IAAI;AACF,YAAA,MAAM,0BAA0B,CAAC,IAAI,CAAC,CAAC;SACxC;QAAC,OAAO,KAAK,EAAE;;;;;AAKd,YAAA,OAAO,CAAC,GAAG,CACT,6FAA6F,CAC9F,CAAC;SACH;KACF;AAED,IAAA,IAAI;AACF,QAAA,IAAI,gBAAkC,CAAC;AAEvC,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AAC/B,YAAA,gBAAgB,GAAG;AACjB,gBAAA,WAAW,EAAE,OAAO;aACrB,CAAC;SACH;aAAM;YACL,gBAAgB,GAAG,OAAO,CAAC;SAC5B;AAED,QAAA,IAAI,SAAS,IAAI,gBAAgB,EAAE;AACjC,YAAA,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAiC,CAAC;AAEnE,YAAA,IAAI,aAAa,IAAI,gBAAgB,EAAE;gBACrC,OAAO,CACL,OAAO,CAAC,IAAI,mDACZ,IAAI,sDAEL,CAAC;AAEF,gBAAA,MAAM,8BAA8B,GAAmC;oBACrE,OAAO,EAAE,OAAO,CAAC,UAAU;AAC3B,oBAAA,mBAAmB,EAAE;wBACnB,WAAW,EAAE,gBAAgB,CAAC,WAAW;AACzC,wBAAA,UAAU,EAAyB,iBAAA;AACpC,qBAAA;iBACF,CAAC;gBAEF,MAAM,iCAAiC,GAGnC,OACF,YAA0B,EAC1B,OAAuC,KACrC;;oBAEF,IAAI,OAAO,CAAC,mBAAmB,CAAC,eAAe,KAAK,UAAU,EAAE;AAC9D,wBAAA,OAAO,CACL,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,IAAI,MAAK,uBAAuB,EAC1C,YAAY,sDAEb,CAAC;wBAEF,MAAM,sBAAsB,GAAG,MAAM,sBAAsB,CACzD,YAAY,EACZ,OAAO,EACP,QAAQ,CACT,CAAC;AACF,wBAAA,OAAO,mBAAmB,CAAC,YAAY,EAAE,sBAAsB,CAAC,CAAC;qBAClE;AACD,oBAAA,OAAO,mBAAmB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AACpD,iBAAC,CAAC;gBAEF,MAAM,+BAA+B,GACnC,mBAAmB,CACjB,IAAI,EACJ,8BAA8B,EAAA,kBAAA,+CAE9B,iCAAiC,EAAA,gBAAA,4CAElC,CAAC;gBAEJ,MAAM,QAAQ,GAAG,MAAM,+BAA+B,CAAC,KAAK,CAAC,KAAK,IAAG;AACnE,oBAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B,iBAAC,CAAC,CAAC;AAEH,gBAAA,OAAO,QAAQ,CAAC,gBAAgB,CAAC,WAAW,CAAC;aAC9C;iBAAM;gBACL,OAAO,CACL,OAAO,CAAC,IAAI,oDACZ,IAAI,sDAEL,CAAC;gBACF,MAAM,eAAe,GACnB,CAAA,CAAA,EAAA,GAAA,gBAAgB,CAAC,eAAe,0CAAE,GAAG;oBACrC,gBAAgB,CAAC,cAAc,CAAC;AAClC,gBAAA,OAAO,CAAC,eAAe,EAAE,IAAI,mEAAiC,CAAC;AAE/D,gBAAA,MAAM,0BAA0B,GAA+B;oBAC7D,oBAAoB,EAAE,OAAO,CAAC,UAAU;oBACxC,eAAe;AACf,oBAAA,eAAe,EAAE;AACf,wBAAA,UAAU,EAAyB,iBAAA;AACpC,qBAAA;iBACF,CAAC;gBAEF,MAAM,iCAAiC,GAGnC,OACF,YAA0B,EAC1B,OAAmC,KACjC;;oBAEF,IAAI,OAAO,CAAC,eAAe,CAAC,eAAe,KAAK,UAAU,EAAE;AAC1D,wBAAA,OAAO,CACL,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,IAAI,MAAK,uBAAuB,EAC1C,YAAY,sDAEb,CAAC;wBAEF,MAAM,sBAAsB,GAAG,MAAM,sBAAsB,CACzD,YAAY,EACZ,OAAO,EACP,QAAQ,CACT,CAAC;AACF,wBAAA,OAAO,mBAAmB,CAAC,YAAY,EAAE,sBAAsB,CAAC,CAAC;qBAClE;AACD,oBAAA,OAAO,mBAAmB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AACpD,iBAAC,CAAC;gBAEF,MAAM,2BAA2B,GAC/B,mBAAmB,CACjB,IAAI,EACJ,0BAA0B,EAAA,cAAA,2CAE1B,iCAAiC,EAAA,gBAAA,4CAElC,CAAC;gBAEJ,MAAM,QAAQ,GAAG,MAAM,2BAA2B,CAAC,KAAK,CAAC,KAAK,IAAG;AAC/D,oBAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B,iBAAC,CAAC,CAAC;AAEH,gBAAA,OAAO,QAAQ,CAAC,iBAAiB,CAAC,WAAW,CAAC;aAC/C;SACF;aAAM;AACL,YAAA,MAAM,gCAAgC,GACpC;gBACE,WAAW,EAAE,gBAAgB,CAAC,WAAW;AACzC,gBAAA,UAAU,EAAyB,iBAAA;aACpC,CAAC;YAEJ,MAAM,uCAAuC,GAGzC,OACF,YAA0B,EAC1B,OAAyC,KACvC;;AAEF,gBAAA,IAAI,OAAO,CAAC,eAAe,KAAK,UAAU,EAAE;AAC1C,oBAAA,OAAO,CACL,CAAA,QAAQ,KAAA,IAAA,IAAR,QAAQ,KAAR,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,QAAQ,CAAE,IAAI,MAAK,uBAAuB,EAC1C,YAAY,sDAEb,CAAC;oBAEF,MAAM,sBAAsB,GAAG,MAAM,sBAAsB,CACzD,YAAY,EACZ,OAAO,EACP,QAAQ,CACT,CAAC;AACF,oBAAA,OAAO,yBAAyB,CAC9B,YAAY,EACZ,sBAAsB,CACvB,CAAC;iBACH;AACD,gBAAA,OAAO,yBAAyB,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;AAC1D,aAAC,CAAC;YAEF,MAAM,iCAAiC,GACrC,mBAAmB,CACjB,IAAI,EACJ,gCAAgC,EAAA,sBAAA,mDAEhC,uCAAuC,EAAA,gBAAA,4CAExC,CAAC;YAEJ,MAAM,QAAQ,GAAG,MAAM,iCAAiC,CAAC,KAAK,CAAC,KAAK,IAAG;AACrE,gBAAA,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/B,aAAC,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC,WAAW,CAAC;SAC7B;KACF;YAAS;AACR,QAAA,QAAQ,aAAR,QAAQ,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAR,QAAQ,CAAE,MAAM,EAAE,CAAC;KACpB;AACH,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;AAsBG;AACI,eAAe,iBAAiB,CACrC,IAAU,EACV,UAA+B,EAAA;AAE/B,IAAA,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAiB,CAAC;IAC9D,IAAI,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC/C,OAAO,OAAO,CAAC,MAAM,CACnB,+CAA+C,CAAC,YAAY,CAAC,IAAI,CAAC,CACnE,CAAC;KACH;AACD,IAAA,MAAM,KAAK,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;AACxC,CAAC;AAED;AACO,eAAe,sBAAsB,CAC1C,IAAkB,EAClB,OAAU,EACV,mBAAgD,EAAA;IAEhD,OAAO,CACL,mBAAmB,CAAC,IAAI,KAAK,uBAAuB,EACpD,IAAI,EAAA,gBAAA,oCAEL,CAAC;AAEF,IAAA,MAAM,gBAAgB,GAAG,MAAM,mBAAmB,CAAC,MAAM,EAAE,CAAC;IAE5D,OAAO,CACL,OAAO,gBAAgB,KAAK,QAAQ,EACpC,IAAI,sDAEL,CAAC;AAEF,IAAA,MAAM,UAAU,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,OAAO,CAAE,CAAC;AAElC,IAAA,IAAI,qBAAqB,IAAI,UAAU,EAAE;AACvC,QAAA,MAAM,WAAW,GACf,UACD,CAAC,mBAAmB,CAAC,WAAW,CAAC;AAClC,QAAA,MAAM,eAAe,GACnB,UACD,CAAC,mBAAmB,CAAC,eAAe,CAAC;QACtC,MAAM,UAAU,GAAI,UAAwD;aACzE,mBAAmB,CAAC,UAAU,CAAC;AAClC,QAAA,MAAM,gBAAgB,GACpB,UACD,CAAC,mBAAmB,CAAC,gBAAgB,CAAC;AAEvC,QAAA,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;AACxB,YAAA,qBAAqB,EAAE;gBACrB,WAAW;AACX,gBAAA,cAAc,EAAE,gBAAgB;gBAChC,eAAe;gBACf,UAAU;gBACV,gBAAgB;AACjB,aAAA;AACF,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,UAAU,CAAC;KACnB;AAAM,SAAA,IAAI,iBAAiB,IAAI,UAAU,EAAE;AAC1C,QAAA,MAAM,eAAe,GACnB,UACD,CAAC,eAAe,CAAC,eAAe,CAAC;QAClC,MAAM,UAAU,GAAI,UAAoD;aACrE,eAAe,CAAC,UAAU,CAAC;AAC9B,QAAA,MAAM,gBAAgB,GACpB,UACD,CAAC,eAAe,CAAC,gBAAgB,CAAC;AAEnC,QAAA,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;AACxB,YAAA,iBAAiB,EAAE;AACjB,gBAAA,cAAc,EAAE,gBAAgB;gBAChC,eAAe;gBACf,UAAU;gBACV,gBAAgB;AACjB,aAAA;AACF,SAAA,CAAC,CAAC;AAEH,QAAA,OAAO,UAAU,CAAC;KACnB;SAAM;QACL,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,CAAC,CAAC;AAClE,QAAA,OAAO,UAAU,CAAC;KACnB;AACH;;ACrhBA;;;;;;;;;;;;;;;AAeG;AAqBH;;;;;;;;;;;;;;;;;;AAkBG;MACU,iBAAiB,CAAA;AAU5B;;;AAGG;AACH,IAAA,WAAA,CAAY,IAAU,EAAA;;AAPb,QAAA,IAAA,CAAA,UAAU,GAAG,iBAAiB,CAAC,WAAW,CAAC;AAQlD,QAAA,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;KAC7B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCG;IACH,iBAAiB,CACf,YAAuC,EACvC,mBAAyC,EAAA;AAEzC,QAAA,OAAO,kBAAkB,CACvB,IAAI,CAAC,IAAI,EACT,YAAY,EACZ,kBAAkB,CAAC,mBAAkD,CAAC,CACvE,CAAC;KACH;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BG;AACH,IAAA,OAAO,UAAU,CACf,cAAsB,EACtB,gBAAwB,EAAA;QAExB,OAAO,mBAAmB,CAAC,iBAAiB,CAC1C,cAAc,EACd,gBAAgB,CACjB,CAAC;KACH;AAED;;;AAGG;IACH,OAAO,oBAAoB,CACzB,cAA8B,EAAA;QAE9B,MAAM,UAAU,GAAG,cAAwC,CAAC;AAC5D,QAAA,OAAO,iBAAiB,CAAC,0BAA0B,CAAC,UAAU,CAAC,CAAC;KACjE;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BG;IACH,OAAO,mBAAmB,CAAC,KAAoB,EAAA;AAC7C,QAAA,OAAO,iBAAiB,CAAC,0BAA0B,EAChD,KAAK,CAAC,UAAU,IAAI,EAAE,EACxB,CAAC;KACH;AAEO,IAAA,OAAO,0BAA0B,CAAC,EACxC,cAAc,EAAE,aAAa,EACL,EAAA;QACxB,IAAI,CAAC,aAAa,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC;SACb;AACD,QAAA,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,GACnC,aAA8C,CAAC;AACjD,QAAA,IAAI,WAAW,IAAI,cAAc,EAAE;YACjC,OAAO,mBAAmB,CAAC,kBAAkB,CAC3C,WAAW,EACX,cAAc,CACf,CAAC;SACH;AACD,QAAA,OAAO,IAAI,CAAC;KACb;;AAlKD;AACgB,iBAAA,CAAA,WAAW,kCAA6B;AACxD;AACgB,iBAAA,CAAA,oBAAoB;;AC3DtC;;;;;;;;;;;;;;;AAeG;AA0CI,MAAM,0BAA0B,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAEjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;AACI,eAAe,eAAe,CACnC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;AAEhC,IAAA,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAClC,OAAO,OAAO,CAAC,MAAM,CACnB,YAAY,CAAC,IAAI,EAAwC,6CAAA,6CAAA,CAC1D,CAAC;KACH;AACD,IAAA,MAAM,YAAY,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AACrC,IAAA,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;IACzD,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACtE,MAAM,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,EAAA,gBAAA,wCAEZ,QAAQ,EACR,gBAAgB,CACjB,CAAC;AACF,IAAA,OAAO,MAAM,CAAC,cAAc,EAAE,CAAC;AACjC,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;AACI,eAAe,uBAAuB,CAC3C,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;AAEhC,IAAA,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAiB,CAAC;IAC9D,IAAI,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QAC/C,OAAO,OAAO,CAAC,MAAM,CACnB,YAAY,CAAC,YAAY,CAAC,IAAI,EAAwC,6CAAA,6CAAA,CACvE,CAAC;KACH;IACD,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;IACtE,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC3E,IAAA,MAAM,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,CAAC,IAAI,EAAA,gBAAA,uCAEjB,QAAQ,EACR,gBAAgB,EAChB,YAAY,CACb,CAAC;AACF,IAAA,OAAO,MAAM,CAAC,cAAc,EAAE,CAAC;AACjC,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;AAwBG;AACI,eAAe,aAAa,CACjC,IAAU,EACV,QAAsB,EACtB,QAAgC,EAAA;AAEhC,IAAA,MAAM,YAAY,GAAG,kBAAkB,CAAC,IAAI,CAAiB,CAAC;IAC9D,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,EAAE,qBAAqB,CAAC,CAAC;IACtE,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAE3E,IAAA,MAAM,MAAM,GAAG,IAAI,cAAc,CAC/B,YAAY,CAAC,IAAI,EAAA,cAAA,qCAEjB,QAAQ,EACR,gBAAgB,EAChB,YAAY,CACb,CAAC;AACF,IAAA,OAAO,MAAM,CAAC,cAAc,EAAE,CAAC;AACjC,CAAC;AAED;;;;AAIG;AACH,MAAM,cAAe,SAAQ,8BAA8B,CAAA;IAOzD,WACE,CAAA,IAAkB,EAClB,MAAqB,EACJ,QAAsB,EACvC,QAAuC,EACvC,IAAmB,EAAA;QAEnB,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;QAJnB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAc;QANjC,IAAU,CAAA,UAAA,GAAqB,IAAI,CAAC;QACpC,IAAM,CAAA,MAAA,GAAkB,IAAI,CAAC;AAUnC,QAAA,IAAI,cAAc,CAAC,kBAAkB,EAAE;AACrC,YAAA,cAAc,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC;SAC5C;AAED,QAAA,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;KAC1C;AAED,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AACpC,QAAA,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,sDAA+B,CAAC;AACzD,QAAA,OAAO,MAAM,CAAC;KACf;AAED,IAAA,MAAM,WAAW,GAAA;QACf,WAAW,CACT,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EACxB,wCAAwC,CACzC,CAAC;AACF,QAAA,MAAM,OAAO,GAAG,gBAAgB,EAAE,CAAC;QACnC,IAAI,CAAC,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAC9C,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;AACd,QAAA,OAAO,CACR,CAAC;AACF,QAAA,IAAI,CAAC,UAAU,CAAC,eAAe,GAAG,OAAO,CAAC;;;;;;;;AAS1C,QAAA,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,IAAG;AACnD,YAAA,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjB,SAAC,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,IAAG;YAClE,IAAI,CAAC,WAAW,EAAE;gBAChB,IAAI,CAAC,MAAM,CACT,YAAY,CAAC,IAAI,CAAC,IAAI,EAAwC,yBAAA,6CAAA,CAC/D,CAAC;aACH;AACH,SAAC,CAAC,CAAC;;QAGH,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;AAED,IAAA,IAAI,OAAO,GAAA;;QACT,OAAO,CAAA,MAAA,IAAI,CAAC,UAAU,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAe,KAAI,IAAI,CAAC;KACjD;IAED,MAAM,GAAA;QACJ,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAsC,yBAAA,2CAAA,CAAC,CAAC;KAC3E;IAED,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;SACzB;AAED,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAClC;AAED,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AACvB,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACnB,QAAA,cAAc,CAAC,kBAAkB,GAAG,IAAI,CAAC;KAC1C;IAEO,oBAAoB,GAAA;QAC1B,MAAM,IAAI,GAAG,MAAW;;YACtB,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,EAAE;;;;;;gBAMnC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,MAAK;AACnC,oBAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;oBACnB,IAAI,CAAC,MAAM,CACT,YAAY,CAAC,IAAI,CAAC,IAAI,EAAqC,sBAAA,0CAAA,CAC5D,CAAC;AACJ,iBAAC,iCAAsB,CAAC;gBACxB,OAAO;aACR;AAED,YAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,0BAA0B,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1E,SAAC,CAAC;AAEF,QAAA,IAAI,EAAE,CAAC;KACR;;AA3GD;AACA;AACe,cAAkB,CAAA,kBAAA,GAA0B,IAA1B;;ACvNnC;;;;;;;;;;;;;;;AAeG;AAQH,MAAM,gBAAgB,GAAG,sCAAsC,CAAC;AAChE,MAAM,UAAU,GAAG,SAAS,CAAC;AAEtB,eAAe,eAAe,CAAC,IAAkB,EAAA;;AAEtD,IAAA,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;QACxB,OAAO;KACR;IAED,MAAM,EAAE,iBAAiB,EAAE,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAE5D,IAAA,KAAK,MAAM,MAAM,IAAI,iBAAiB,EAAE;AACtC,QAAA,IAAI;AACF,YAAA,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;gBACvB,OAAO;aACR;SACF;AAAC,QAAA,OAAA,EAAA,EAAM;;SAEP;KACF;;IAGD,KAAK,CAAC,IAAI,EAAA,qBAAA,oCAA+B,CAAC;AAC5C,CAAC;AAED,SAAS,WAAW,CAAC,QAAgB,EAAA;AACnC,IAAA,MAAM,UAAU,GAAG,cAAc,EAAE,CAAC;IACpC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;AACnD,IAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE;AAC9C,QAAA,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;QAEhC,IAAI,KAAK,CAAC,QAAQ,KAAK,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE;;YAE5C,QACE,QAAQ,KAAK,mBAAmB;AAChC,gBAAA,QAAQ,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;oBACzC,UAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC,EAC/C;SACH;QAED,OAAO,QAAQ,KAAK,mBAAmB,IAAI,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC;KACxE;IAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AAC9B,QAAA,OAAO,KAAK,CAAC;KACd;AAED,IAAA,IAAI,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;;;QAGnC,OAAO,QAAQ,KAAK,QAAQ,CAAC;KAC9B;;IAGD,MAAM,oBAAoB,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;;;AAG5D,IAAA,MAAM,EAAE,GAAG,IAAI,MAAM,CACnB,SAAS,GAAG,oBAAoB,GAAG,GAAG,GAAG,oBAAoB,GAAG,IAAI,EACpE,GAAG,CACJ,CAAC;AACF,IAAA,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC3B;;ACrFA;;;;;;;;;;;;;;;AAeG;AASH,MAAM,eAAe,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAEhD;;;AAGG;AACH,SAAS,wBAAwB,GAAA;;;;AAI/B,IAAA,MAAM,MAAM,GAAG,OAAO,EAAE,CAAC,MAAM,CAAC;;IAEhC,IAAI,MAAM,aAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,CAAC,EAAE;;AAEb,QAAA,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;;AAExC,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;AAE1C,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;AAE1C,YAAA,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEzC,YAAA,IAAI,MAAM,CAAC,EAAE,EAAE;AACb,gBAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;AAEzC,oBAAA,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;iBACrB;aACF;SACF;KACF;AACH,CAAC;AAED,SAAS,QAAQ,CAAC,IAAkB,EAAA;IAClC,OAAO,IAAI,OAAO,CAAuB,CAAC,OAAO,EAAE,MAAM,KAAI;;;AAE3D,QAAA,SAAS,cAAc,GAAA;;;AAGrB,YAAA,wBAAwB,EAAE,CAAC;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACxB,QAAQ,EAAE,MAAK;oBACb,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;iBACpC;gBACD,SAAS,EAAE,MAAK;;;;;;;AAOd,oBAAA,wBAAwB,EAAE,CAAC;AAC3B,oBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;iBAClE;AACD,gBAAA,OAAO,EAAE,eAAe,CAAC,GAAG,EAAE;AAC/B,aAAA,CAAC,CAAC;SACJ;QAED,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,OAAO,EAAE,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,EAAE;;YAEnC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;SACpC;AAAM,aAAA,IAAI,CAAC,EAAC,CAAA,EAAA,GAAA,OAAO,EAAE,CAAC,IAAI,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,CAAA,EAAE;;AAEjC,YAAA,cAAc,EAAE,CAAC;SAClB;aAAM;;;;;;YAML,MAAM,MAAM,GAAGC,qBAAwB,CAAC,WAAW,CAAC,CAAC;;AAErD,YAAA,OAAO,EAAE,CAAC,MAAM,CAAC,GAAG,MAAK;;AAEvB,gBAAA,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;AACf,oBAAA,cAAc,EAAE,CAAC;iBAClB;qBAAM;;AAEL,oBAAA,MAAM,CAAC,YAAY,CAAC,IAAI,EAAA,wBAAA,4CAAuC,CAAC,CAAC;iBAClE;AACH,aAAC,CAAC;;AAEF,YAAA,OAAOC,OACG,CAAC,GAAGC,cAAiB,EAAE,CAAA,QAAA,EAAW,MAAM,CAAA,CAAE,CAAC;iBAClD,KAAK,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;SAC1B;AACH,KAAC,CAAC,CAAC,KAAK,CAAC,KAAK,IAAG;;QAEf,gBAAgB,GAAG,IAAI,CAAC;AACxB,QAAA,MAAM,KAAK,CAAC;AACd,KAAC,CAAC,CAAC;AACL,CAAC;AAED,IAAI,gBAAgB,GAAyC,IAAI,CAAC;AAC5D,SAAU,SAAS,CAAC,IAAkB,EAAA;AAC1C,IAAA,gBAAgB,GAAG,gBAAgB,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACtD,IAAA,OAAO,gBAAgB,CAAC;AAC1B;;ACxHA;;;;;;;;;;;;;;;AAeG;AAcH,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC5C,MAAM,WAAW,GAAG,gBAAgB,CAAC;AACrC,MAAM,oBAAoB,GAAG,sBAAsB,CAAC;AAEpD,MAAM,iBAAiB,GAAG;AACxB,IAAA,KAAK,EAAE;AACL,QAAA,QAAQ,EAAE,UAAU;AACpB,QAAA,GAAG,EAAE,QAAQ;AACb,QAAA,KAAK,EAAE,KAAK;AACZ,QAAA,MAAM,EAAE,KAAK;AACd,KAAA;AACD,IAAA,aAAa,EAAE,MAAM;AACrB,IAAA,QAAQ,EAAE,IAAI;CACf,CAAC;AAEF;AACA;AACA,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC;IAC/B,CAAyB,gCAAA,+BAAA,GAAG,CAAC;AAC7B,IAAA,CAAC,gDAAgD,EAAE,GAAG,CAAC;AACvD,IAAA,CAAC,6CAA6C,EAAE,GAAG,CAAC;AACrD,CAAA,CAAC,CAAC;AAEH,SAAS,YAAY,CAAC,IAAkB,EAAA;AACtC,IAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,IAAA,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,wEAAoC,CAAC;AACpE,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ;AACzB,UAAE,YAAY,CAAC,MAAM,EAAE,oBAAoB,CAAC;UAC1C,CAAW,QAAA,EAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA,CAAA,EAAI,WAAW,CAAA,CAAE,CAAC;AAEvD,IAAA,MAAM,MAAM,GAA2B;QACrC,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,OAAO,EAAE,IAAI,CAAC,IAAI;AAClB,QAAA,CAAC,EAAE,WAAW;KACf,CAAC;AACF,IAAA,MAAM,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACtD,IAAI,GAAG,EAAE;AACP,QAAA,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC;KAClB;AACD,IAAA,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AACzC,IAAA,IAAI,UAAU,CAAC,MAAM,EAAE;QACrB,MAAM,CAAC,EAAE,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KAClC;AACD,IAAA,OAAO,CAAG,EAAA,GAAG,CAAI,CAAA,EAAA,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;AAClD,CAAC;AAEM,eAAe,WAAW,CAC/B,IAAkB,EAAA;IAElB,MAAM,OAAO,GAAG,MAAMC,SAAoB,CAAC,IAAI,CAAC,CAAC;AACjD,IAAA,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC,IAAI,CAAC;AAC5B,IAAA,OAAO,CAAC,IAAI,EAAE,IAAI,sDAA+B,CAAC;IAClD,OAAO,OAAO,CAAC,IAAI,CACjB;QACE,KAAK,EAAE,QAAQ,CAAC,IAAI;AACpB,QAAA,GAAG,EAAE,YAAY,CAAC,IAAI,CAAC;AACvB,QAAA,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,2BAA2B;AAC/D,QAAA,UAAU,EAAE,iBAAiB;AAC7B,QAAA,SAAS,EAAE,IAAI;AAChB,KAAA,EACD,CAAC,MAA2B,KAC1B,IAAI,OAAO,CAAC,OAAO,OAAO,EAAE,MAAM,KAAI;QACpC,MAAM,MAAM,CAAC,OAAO,CAAC;;AAEnB,YAAA,cAAc,EAAE,KAAK;AACtB,SAAA,CAAC,CAAC;AAEH,QAAA,MAAM,YAAY,GAAG,YAAY,CAC/B,IAAI,sEAEL,CAAC;;;QAGF,MAAM,iBAAiB,GAAG,OAAO,EAAE,CAAC,UAAU,CAAC,MAAK;YAClD,MAAM,CAAC,YAAY,CAAC,CAAC;AACvB,SAAC,EAAE,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;;AAEvB,QAAA,SAAS,oBAAoB,GAAA;AAC3B,YAAA,OAAO,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;YAC1C,OAAO,CAAC,MAAM,CAAC,CAAC;SACjB;;;QAGD,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,oBAAoB,EAAE,MAAK;YAChE,MAAM,CAAC,YAAY,CAAC,CAAC;AACvB,SAAC,CAAC,CAAC;KACJ,CAAC,CACL,CAAC;AACJ;;ACrHA;;;;;;;;;;;;;;;AAeG;AAaH,MAAM,kBAAkB,GAAG;AACzB,IAAA,QAAQ,EAAE,KAAK;AACf,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,SAAS,EAAE,KAAK;AAChB,IAAA,OAAO,EAAE,IAAI;CACd,CAAC;AAEF,MAAM,aAAa,GAAG,GAAG,CAAC;AAC1B,MAAM,cAAc,GAAG,GAAG,CAAC;AAC3B,MAAM,YAAY,GAAG,QAAQ,CAAC;AAE9B,MAAM,iBAAiB,GAAG,kBAAkB,CAAC;MAEhC,SAAS,CAAA;AAGpB,IAAA,WAAA,CAAqB,MAAqB,EAAA;QAArB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAe;QAF1C,IAAe,CAAA,eAAA,GAAkB,IAAI,CAAC;KAEQ;IAE9C,KAAK,GAAA;AACH,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,IAAI;AACF,gBAAA,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;aACrB;AAAC,YAAA,OAAO,CAAC,EAAE,GAAE;SACf;KACF;AACF,CAAA;AAEe,SAAA,KAAK,CACnB,IAAkB,EAClB,GAAY,EACZ,IAAa,EACb,KAAK,GAAG,aAAa,EACrB,MAAM,GAAG,cAAc,EAAA;IAEvB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC7E,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;IAC5E,IAAI,MAAM,GAAG,EAAE,CAAC;AAEhB,IAAA,MAAM,OAAO,GACR,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,kBAAkB,KACrB,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE,EACvB,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE,EACzB,GAAG;AACH,QAAA,IAAI,GACL,CAAC;;;AAIF,IAAA,MAAM,EAAE,GAAG,KAAK,EAAE,CAAC,WAAW,EAAE,CAAC;IAEjC,IAAI,IAAI,EAAE;AACR,QAAA,MAAM,GAAG,YAAY,CAAC,EAAE,CAAC,GAAG,YAAY,GAAG,IAAI,CAAC;KACjD;AAED,IAAA,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE;;AAElB,QAAA,GAAG,GAAG,GAAG,IAAI,iBAAiB,CAAC;;;AAG/B,QAAA,OAAO,CAAC,UAAU,GAAG,KAAK,CAAC;KAC5B;AAED,IAAA,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,CAClD,CAAC,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAA,EAAG,GAAG,CAAA,CAAA,EAAI,KAAK,CAAA,CAAA,CAAG,EACnD,EAAE,CACH,CAAC;IAEF,IAAI,gBAAgB,CAAC,EAAE,CAAC,IAAI,MAAM,KAAK,OAAO,EAAE;AAC9C,QAAA,kBAAkB,CAAC,GAAG,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC;AACtC,QAAA,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC;KAC5B;;;AAID,IAAA,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,EAAE,MAAM,EAAE,aAAa,CAAC,CAAC;AAC7D,IAAA,OAAO,CAAC,MAAM,EAAE,IAAI,oDAA8B,CAAC;;AAGnD,IAAA,IAAI;QACF,MAAM,CAAC,KAAK,EAAE,CAAC;KAChB;AAAC,IAAA,OAAO,CAAC,EAAE,GAAE;AAEd,IAAA,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW,EAAE,MAAc,EAAA;IACrD,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACvC,IAAA,EAAE,CAAC,IAAI,GAAG,GAAG,CAAC;AACd,IAAA,EAAE,CAAC,MAAM,GAAG,MAAM,CAAC;IACnB,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AACjD,IAAA,KAAK,CAAC,cAAc,CAClB,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,CAAC,EACD,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,CAAC,EACD,IAAI,CACL,CAAC;AACF,IAAA,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1B;;ACxIA;;;;;;;;;;;;;;;AAeG;AA2BH;;;AAGG;AACH,MAAM,uBAAuB,GAAG,mBAAmB,CAAC;AAWpD,MAAM,4BAA4B,CAAA;AAAlC,IAAA,WAAA,GAAA;QACmB,IAAa,CAAA,aAAA,GAAqC,EAAE,CAAC;QACrD,IAAO,CAAA,OAAA,GAAwC,EAAE,CAAC;QAClD,IAAwB,CAAA,wBAAA,GAAkC,EAAE,CAAC;QAErE,IAAoB,CAAA,oBAAA,GAAG,yBAAyB,CAAC;QAyH1D,IAAmB,CAAA,mBAAA,GAAG,kBAAkB,CAAC;QAEzC,IAAuB,CAAA,uBAAA,GAAG,uBAAuB,CAAC;KACnD;;;IAxHC,MAAM,UAAU,CACd,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;;AAEhB,QAAA,WAAW,CACT,CAAA,EAAA,GAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,EACxC,8CAA8C,CAC/C,CAAC;AAEF,QAAA,MAAM,GAAG,GAAG,MAAM,eAAe,CAC/B,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,cAAc,EAAE,EAChB,OAAO,CACR,CAAC;QACF,OAAO,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,gBAAgB,EAAE,CAAC,CAAC;KAC7C;IAED,MAAM,aAAa,CACjB,IAAkB,EAClB,QAAsB,EACtB,QAAuB,EACvB,OAAgB,EAAA;AAEhB,QAAA,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACnC,QAAA,MAAM,GAAG,GAAG,MAAM,eAAe,CAC/B,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,cAAc,EAAE,EAChB,OAAO,CACR,CAAC;QACF,kBAAkB,CAAC,GAAG,CAAC,CAAC;QACxB,OAAO,IAAI,OAAO,CAAC,MAAO,GAAC,CAAC,CAAC;KAC9B;AAED,IAAA,WAAW,CAAC,IAAkB,EAAA;AAC5B,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AACxB,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;AAC3B,YAAA,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YACrD,IAAI,OAAO,EAAE;AACX,gBAAA,OAAO,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aACjC;iBAAM;AACL,gBAAA,WAAW,CAAC,OAAO,EAAE,0CAA0C,CAAC,CAAC;AACjE,gBAAA,OAAO,OAAO,CAAC;aAChB;SACF;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC;;;AAItC,QAAA,OAAO,CAAC,KAAK,CAAC,MAAK;AACjB,YAAA,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AACjC,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,OAAO,CAAC;KAChB;IAEO,MAAM,iBAAiB,CAAC,IAAkB,EAAA;AAChD,QAAA,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,IAAI,CAAC,CAAC;AACvC,QAAA,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,CAAC,QAAQ,CACb,WAAW,EACX,CAAC,WAAiC,KAAI;YACpC,OAAO,CAAC,WAAW,KAAA,IAAA,IAAX,WAAW,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAX,WAAW,CAAE,SAAS,EAAE,IAAI,EAAA,oBAAA,wCAAmC,CAAC;;YAGxE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACvD,OAAO,EAAE,MAAM,EAAE,OAAO,GAAmB,KAAA,yBAAmB,OAAA,0BAAE,CAAC;AACnE,SAAC,EACD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CACzC,CAAC;AAEF,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC;AACnC,QAAA,OAAO,OAAO,CAAC;KAChB;IAED,4BAA4B,CAC1B,IAAkB,EAClB,EAAmC,EAAA;QAEnC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACzC,QAAA,MAAM,CAAC,IAAI,CACT,uBAAuB,EACvB,EAAE,IAAI,EAAE,uBAAuB,EAAE,EACjC,MAAM,IAAG;;AACP,YAAA,MAAM,WAAW,GAAG,CAAA,EAAA,GAAA,MAAM,aAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAG,CAAC,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,uBAAuB,CAAC,CAAC;AAC3D,YAAA,IAAI,WAAW,KAAK,SAAS,EAAE;AAC7B,gBAAA,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;aACnB;YAED,KAAK,CAAC,IAAI,EAAA,gBAAA,oCAA+B,CAAC;AAC5C,SAAC,EACD,IAAI,CAAC,OAAO,CAAC,2BAA2B,CACzC,CAAC;KACH;AAED,IAAA,iBAAiB,CAAC,IAAkB,EAAA;AAClC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;SAC5D;AAED,QAAA,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;KAC3C;AAED,IAAA,IAAI,sBAAsB,GAAA;;QAExB,OAAO,gBAAgB,EAAE,IAAI,SAAS,EAAE,IAAI,MAAM,EAAE,CAAC;KACtD;AAKF,CAAA;AAED;;;;;;;;AAQG;AACI,MAAM,4BAA4B,GACvC;;MChLoB,wBAAwB,CAAA;AAC5C,IAAA,WAAA,CAA+B,QAAkB,EAAA;QAAlB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAU;KAAI;AAErD,IAAA,QAAQ,CACN,IAAkB,EAClB,OAA+B,EAC/B,WAA2B,EAAA;AAE3B,QAAA,QAAQ,OAAO,CAAC,IAAI;AAClB,YAAA,KAAA,QAAA;AACE,gBAAA,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AACrE,YAAA,KAAA,QAAA;gBACE,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AACxD,YAAA;AACE,gBAAA,OAAO,SAAS,CAAC,mCAAmC,CAAC,CAAC;SACzD;KACF;AAWF;;ACnBD;;;;AAIG;AACG,MAAO,6BACX,SAAQ,wBAAwB,CAAA;AAGhC,IAAA,WAAA,CAAqC,UAA+B,EAAA;AAClE,QAAA,KAAK,8BAAgB,CAAC;QADa,IAAU,CAAA,UAAA,GAAV,UAAU,CAAqB;KAEnE;;IAGD,OAAO,eAAe,CACpB,UAA+B,EAAA;AAE/B,QAAA,OAAO,IAAI,6BAA6B,CAAC,UAAU,CAAC,CAAC;KACtD;;AAGD,IAAA,eAAe,CACb,IAAkB,EAClB,OAAe,EACf,WAA2B,EAAA;QAE3B,OAAO,sBAAsB,CAAC,IAAI,EAAE;YAClC,OAAO;YACP,WAAW;AACX,YAAA,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE;AAClE,SAAA,CAAC,CAAC;KACJ;;IAGD,eAAe,CACb,IAAkB,EAClB,oBAA4B,EAAA;QAE5B,OAAO,sBAAsB,CAAC,IAAI,EAAE;YAClC,oBAAoB;AACpB,YAAA,qBAAqB,EAAE,IAAI,CAAC,UAAU,CAAC,wBAAwB,EAAE;AAClE,SAAA,CAAC,CAAC;KACJ;AACF,CAAA;AAED;;;;AAIG;MACU,yBAAyB,CAAA;AACpC,IAAA,WAAA,GAAA,GAAwB;AAExB;;;;;;;;;AASG;IACH,OAAO,SAAS,CAAC,UAA+B,EAAA;AAC9C,QAAA,OAAO,6BAA6B,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;KAClE;;AAED;;AAEG;AACI,yBAAS,CAAA,SAAA,GAAG,OAAO;;AC/D5B;;;;AAIG;MACU,wBAAwB,CAAA;AACnC;;;;;;;;;AASG;AACH,IAAA,OAAO,sBAAsB,CAC3B,MAAkB,EAClB,eAAuB,EAAA;QAEvB,OAAO,4BAA4B,CAAC,WAAW,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;KAC1E;AAED;;;;;;;;AAQG;AACH,IAAA,OAAO,kBAAkB,CACvB,YAAoB,EACpB,eAAuB,EAAA;QAEvB,OAAO,4BAA4B,CAAC,iBAAiB,CACnD,YAAY,EACZ,eAAe,CAChB,CAAC;KACH;AAED;;;;;;;;AAQG;AACH,IAAA,aAAa,cAAc,CACzB,OAA2B,EAAA;;QAE3B,MAAM,UAAU,GAAG,OAAiC,CAAC;AACrD,QAAA,OAAO,CACL,QAAO,CAAA,EAAA,GAAA,UAAU,CAAC,IAAI,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,IAAI,CAAA,KAAK,WAAW,sDAE7C,CAAC;QACF,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE;YAC9D,OAAO,EAAE,UAAU,CAAC,UAAU;AAC9B,YAAA,kBAAkB,EAAE,EAAE;AACvB,SAAA,CAAC,CAAC;AACH,QAAA,OAAO,UAAU,CAAC,mCAAmC,CACnD,QAAQ,EACR,UAAU,CAAC,IAAI,CAAC,IAAI,CACrB,CAAC;KACH;;AAED;;AAEG;AACI,wBAAA,CAAA,SAAS,GAAyB,MAAA,qBAAA;AAGrC,MAAO,4BACX,SAAQ,wBAAwB,CAAA;AAGhC,IAAA,WAAA,CACW,GAAW,EACX,YAAqB,EACrB,MAAmB,EAAA;AAE5B,QAAA,KAAK,4BAAe,CAAC;QAJZ,IAAG,CAAA,GAAA,GAAH,GAAG,CAAQ;QACX,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAS;QACrB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAa;KAG7B;;AAGD,IAAA,OAAO,WAAW,CAChB,MAAkB,EAClB,GAAW,EAAA;QAEX,OAAO,IAAI,4BAA4B,CAAC,GAAG,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;KACjE;;AAGD,IAAA,OAAO,iBAAiB,CACtB,YAAoB,EACpB,GAAW,EAAA;AAEX,QAAA,OAAO,IAAI,4BAA4B,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;KAC5D;;AAGD,IAAA,MAAM,eAAe,CACnB,IAAkB,EAClB,OAAe,EACf,WAA2B,EAAA;QAE3B,OAAO,CACL,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW,EAClC,IAAI,EAAA,gBAAA,oCAEL,CAAC;QACF,OAAO,qBAAqB,CAAC,IAAI,EAAE;YACjC,OAAO;YACP,WAAW;YACX,oBAAoB,EAAE,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,GAAG,CAAC;AACtE,SAAA,CAAC,CAAC;KACJ;;AAGD,IAAA,MAAM,eAAe,CACnB,IAAkB,EAClB,oBAA4B,EAAA;AAE5B,QAAA,OAAO,CACL,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,EACzD,IAAI,sDAEL,CAAC;QACF,MAAM,oBAAoB,GAAG,EAAE,gBAAgB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5D,OAAO,qBAAqB,CAAC,IAAI,EAAE;YACjC,oBAAoB;YACpB,eAAe,EAAE,IAAI,CAAC,YAAY;YAClC,oBAAoB;AACrB,SAAA,CAAC,CAAC;KACJ;AACF,CAAA;AAED;;;;;;AAMG;MACU,UAAU,CAAA;;AAwBrB,IAAA,WAAA,CACE,SAAiB,EACjB,gBAAwB,EACxB,UAAkB,EAClB,mBAA2B,EAC3B,4BAAoC,EACnB,WAAmB,EACnB,IAAkB,EAAA;QADlB,IAAW,CAAA,WAAA,GAAX,WAAW,CAAQ;QACnB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAc;AAEnC,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC3B,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AAC7B,QAAA,IAAI,CAAC,mBAAmB,GAAG,mBAAmB,CAAC;AAC/C,QAAA,IAAI,CAAC,4BAA4B,GAAG,4BAA4B,CAAC;KAClE;;AAGD,IAAA,OAAO,mCAAmC,CACxC,QAAwC,EACxC,IAAkB,EAAA;QAElB,OAAO,IAAI,UAAU,CACnB,QAAQ,CAAC,eAAe,CAAC,eAAe,EACxC,QAAQ,CAAC,eAAe,CAAC,gBAAgB,EACzC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,EAC/C,QAAQ,CAAC,eAAe,CAAC,SAAS,EAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC,WAAW,EAAE,EACvE,QAAQ,CAAC,eAAe,CAAC,WAAW,EACpC,IAAI,CACL,CAAC;KACH;;AAGD,IAAA,yBAAyB,CAAC,GAAW,EAAA;QACnC,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,gBAAgB,EAAE,GAAG,EAAE,CAAC;KACjE;AAED;;;;;;;;;AASG;IACH,iBAAiB,CAAC,WAAoB,EAAE,MAAe,EAAA;;QACrD,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAI,cAAc,CAAC,WAAW,CAAC,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE;YACzD,WAAW,GAAG,IAAI,CAAC;SACpB;QACD,IAAI,WAAW,EAAE;AACf,YAAA,IAAI,cAAc,CAAC,WAAW,CAAC,EAAE;AAC/B,gBAAA,WAAW,GAAG,CAAA,CAAA,EAAA,GAAA,IAAI,CAAC,IAAI,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,KAAI,aAAa,CAAC;aAC7D;AACD,YAAA,IAAI,cAAc,CAAC,MAAM,CAAC,EAAE;AAC1B,gBAAA,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;aACzB;SACF;AACD,QAAA,OAAO,kBAAkB,MAAM,CAAA,CAAA,EAAI,WAAW,CAAW,QAAA,EAAA,IAAI,CAAC,SAAS,CAAA,QAAA,EAAW,MAAM,CAAc,WAAA,EAAA,IAAI,CAAC,gBAAgB,CAAA,QAAA,EAAW,IAAI,CAAC,UAAU,EAAE,CAAC;KACzJ;AACF,CAAA;AAED;AACA,SAAS,cAAc,CAAC,KAAc,EAAA;AACpC,IAAA,OAAO,OAAO,KAAK,KAAK,WAAW,IAAI,CAAA,KAAK,KAAL,IAAA,IAAA,KAAK,uBAAL,KAAK,CAAE,MAAM,MAAK,CAAC,CAAC;AAC7D;;ACnRA;;;;;;;;;;;;;;;AAeG;AAsBH,MAAM,wBAAwB,GAAG,CAAC,GAAG,EAAE,CAAC;AACxC,MAAM,iBAAiB,GACrB,sBAAsB,CAAC,mBAAmB,CAAC,IAAI,wBAAwB,CAAC;AAE1E,IAAI,iBAAiB,GAA8B,IAAI,CAAC;AAExD,MAAM,iBAAiB,GAAG,CAAC,GAAW,KAAK,OAAO,IAAiB,KAAI;IACrE,MAAM,aAAa,GAAG,IAAI,KAAK,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;IAC9D,MAAM,UAAU,GACd,aAAa;AACb,QAAA,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,IAAK,CAAC;AAC1E,IAAA,IAAI,UAAU,IAAI,UAAU,GAAG,iBAAiB,EAAE;QAChD,OAAO;KACR;;IAED,MAAM,OAAO,GAAG,aAAa,KAAA,IAAA,IAAb,aAAa,KAAb,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,aAAa,CAAE,KAAK,CAAC;AACrC,IAAA,IAAI,iBAAiB,KAAK,OAAO,EAAE;QACjC,OAAO;KACR;IACD,iBAAiB,GAAG,OAAO,CAAC;IAC5B,MAAM,KAAK,CAAC,GAAG,EAAE;QACf,MAAM,EAAE,OAAO,GAAG,MAAM,GAAG,QAAQ;AACnC,QAAA,OAAO,EAAE,OAAO;AACd,cAAE;gBACE,eAAe,EAAE,CAAU,OAAA,EAAA,OAAO,CAAE,CAAA;AACrC,aAAA;AACH,cAAE,EAAE;AACP,KAAA,CAAC,CAAC;AACL,CAAC,CAAC;AAEF;;;;;;;AAOG;AACa,SAAA,OAAO,CAAC,GAAA,GAAmB,MAAM,EAAE,EAAA;IACjD,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAE3C,IAAA,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE;AAC5B,QAAA,OAAO,QAAQ,CAAC,YAAY,EAAE,CAAC;KAChC;AAED,IAAA,MAAM,IAAI,GAAG,cAAc,CAAC,GAAG,EAAE;AAC/B,QAAA,qBAAqB,EAAE,4BAA4B;AACnD,QAAA,WAAW,EAAE;YACX,yBAAyB;YACzB,uBAAuB;YACvB,yBAAyB;AAC1B,SAAA;AACF,KAAA,CAAC,CAAC;AAEH,IAAA,MAAM,iBAAiB,GAAG,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;;AAErE,IAAA,IACE,iBAAiB;QACjB,OAAO,eAAe,KAAK,SAAS;AACpC,QAAA,eAAe,EACf;;QAEA,MAAM,gBAAgB,GAAG,IAAI,GAAG,CAAC,iBAAiB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QACrE,IAAI,QAAQ,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,EAAE;YAC/C,MAAM,UAAU,GAAG,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;AAClE,YAAA,sBAAsB,CAAC,IAAI,EAAE,UAAU,EAAE,MACvC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAC7B,CAAC;AACF,YAAA,gBAAgB,CAAC,IAAI,EAAE,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;SAClD;KACF;AAED,IAAA,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,MAAM,CAAC,CAAC;IACxD,IAAI,gBAAgB,EAAE;AACpB,QAAA,mBAAmB,CAAC,IAAI,EAAE,UAAU,gBAAgB,CAAA,CAAE,CAAC,CAAC;KACzD;AAED,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,sBAAsB,GAAA;;AAC7B,IAAA,OAAO,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,QAAQ,CAAC;AAChE,CAAC;AAED,sBAAsB,CAAC;AACrB,IAAA,MAAM,CAAC,GAAW,EAAA;;QAEhB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAI;YACrC,MAAM,EAAE,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC5C,YAAA,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AAC5B,YAAA,EAAE,CAAC,MAAM,GAAG,OAAO,CAAC;AACpB,YAAA,EAAE,CAAC,OAAO,GAAG,CAAC,IAAG;AACf,gBAAA,MAAM,KAAK,GAAG,YAAY,CAAA,gBAAA,oCAA8B,CAAC;AACzD,gBAAA,KAAK,CAAC,UAAU,GAAG,CAAuC,CAAC;gBAC3D,MAAM,CAAC,KAAK,CAAC,CAAC;AAChB,aAAC,CAAC;AACF,YAAA,EAAE,CAAC,IAAI,GAAG,iBAAiB,CAAC;AAC5B,YAAA,EAAE,CAAC,OAAO,GAAG,OAAO,CAAC;AACrB,YAAA,sBAAsB,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AAC3C,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,UAAU,EAAE,mCAAmC;AAC/C,IAAA,iBAAiB,EAAE,yCAAyC;AAC5D,IAAA,yBAAyB,EACvB,wDAAwD;AAC3D,CAAA,CAAC,CAAC;AAEH,YAAY,wCAAwB;;ACjJpC;;;;;;;;;;;;;;;AAeG;AAqCH;AACA;AACA;AACgB,SAAA,sBAAsB,CAAC,IAAU,EAAE,SAAiB,EAAA;IAClE,SAAS,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAC3C;;;;"}