/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * Duck-typed interface for objects that have an isEqual() method.
 *
 * Note: This is copied from src/util/misc.ts to avoid importing private types.
 */
export interface Equatable<T> {
    isEqual(other: T): boolean;
}
/**
 * Custom equals override for types that have a free-standing equals functions
 *  (such as `queryEquals()`).
 */
export interface CustomMatcher<T> {
    equalsFn: (left: T, right: T) => boolean;
    forType: Function;
}
export declare function addEqualityMatcher(...customMatchers: Array<CustomMatcher<any>>): void;
