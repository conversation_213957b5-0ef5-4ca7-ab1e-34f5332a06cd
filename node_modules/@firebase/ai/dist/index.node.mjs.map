{"version": 3, "file": "index.node.mjs", "sources": ["../src/constants.ts", "../src/types/enums.ts", "../src/types/schema.ts", "../src/types/imagen/requests.ts", "../src/public-types.ts", "../src/backend.ts", "../src/service.ts", "../src/errors.ts", "../src/helpers.ts", "../src/models/ai-model.ts", "../src/logger.ts", "../src/requests/request.ts", "../src/requests/response-helpers.ts", "../src/googleai-mappers.ts", "../src/requests/stream-reader.ts", "../src/methods/generate-content.ts", "../src/requests/request-helpers.ts", "../src/methods/chat-session-helpers.ts", "../src/methods/chat-session.ts", "../src/methods/count-tokens.ts", "../src/models/generative-model.ts", "../src/models/imagen-model.ts", "../src/requests/schema-builder.ts", "../src/requests/imagen-image-format.ts", "../src/api.ts", "../src/index.node.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../package.json';\n\nexport const AI_TYPE = 'AI';\n\nexport const DEFAULT_LOCATION = 'us-central1';\n\nexport const DEFAULT_BASE_URL = 'https://firebasevertexai.googleapis.com';\n\nexport const DEFAULT_API_VERSION = 'v1beta';\n\nexport const PACKAGE_VERSION = version;\n\nexport const LANGUAGE_TAG = 'gl-js';\n\nexport const DEFAULT_FETCH_TIMEOUT_MS = 180 * 1000;\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Role is the producer of the content.\n * @public\n */\nexport type Role = (typeof POSSIBLE_ROLES)[number];\n\n/**\n * Possible roles.\n * @public\n */\nexport const POSSIBLE_ROLES = ['user', 'model', 'function', 'system'] as const;\n\n/**\n * Harm categories that would cause prompts or candidates to be blocked.\n * @public\n */\nexport enum HarmCategory {\n  HARM_CATEGORY_HATE_SPEECH = 'HARM_CATEGORY_HATE_SPEECH',\n  HARM_CATEGORY_SEXUALLY_EXPLICIT = 'HARM_CATEGORY_SEXUALLY_EXPLICIT',\n  HARM_CATEGORY_HARASSMENT = 'HARM_CATEGORY_HARASSMENT',\n  HARM_CATEGORY_DANGEROUS_CONTENT = 'HARM_CATEGORY_DANGEROUS_CONTENT'\n}\n\n/**\n * Threshold above which a prompt or candidate will be blocked.\n * @public\n */\nexport enum HarmBlockThreshold {\n  /**\n   * Content with `NEGLIGIBLE` will be allowed.\n   */\n  BLOCK_LOW_AND_ABOVE = 'BLOCK_LOW_AND_ABOVE',\n  /**\n   * Content with `NEGLIGIBLE` and `LOW` will be allowed.\n   */\n  BLOCK_MEDIUM_AND_ABOVE = 'BLOCK_MEDIUM_AND_ABOVE',\n  /**\n   * Content with `NEGLIGIBLE`, `LOW`, and `MEDIUM` will be allowed.\n   */\n  BLOCK_ONLY_HIGH = 'BLOCK_ONLY_HIGH',\n  /**\n   * All content will be allowed.\n   */\n  BLOCK_NONE = 'BLOCK_NONE',\n  /**\n   * All content will be allowed. This is the same as `BLOCK_NONE`, but the metadata corresponding\n   * to the {@link HarmCategory} will not be present in the response.\n   */\n  OFF = 'OFF'\n}\n\n/**\n * This property is not supported in the Gemini Developer API ({@link GoogleAIBackend}).\n *\n * @public\n */\nexport enum HarmBlockMethod {\n  /**\n   * The harm block method uses both probability and severity scores.\n   */\n  SEVERITY = 'SEVERITY',\n  /**\n   * The harm block method uses the probability score.\n   */\n  PROBABILITY = 'PROBABILITY'\n}\n\n/**\n * Probability that a prompt or candidate matches a harm category.\n * @public\n */\nexport enum HarmProbability {\n  /**\n   * Content has a negligible chance of being unsafe.\n   */\n  NEGLIGIBLE = 'NEGLIGIBLE',\n  /**\n   * Content has a low chance of being unsafe.\n   */\n  LOW = 'LOW',\n  /**\n   * Content has a medium chance of being unsafe.\n   */\n  MEDIUM = 'MEDIUM',\n  /**\n   * Content has a high chance of being unsafe.\n   */\n  HIGH = 'HIGH'\n}\n\n/**\n * Harm severity levels.\n * @public\n */\nexport enum HarmSeverity {\n  /**\n   * Negligible level of harm severity.\n   */\n  HARM_SEVERITY_NEGLIGIBLE = 'HARM_SEVERITY_NEGLIGIBLE',\n  /**\n   * Low level of harm severity.\n   */\n  HARM_SEVERITY_LOW = 'HARM_SEVERITY_LOW',\n  /**\n   * Medium level of harm severity.\n   */\n  HARM_SEVERITY_MEDIUM = 'HARM_SEVERITY_MEDIUM',\n  /**\n   * High level of harm severity.\n   */\n  HARM_SEVERITY_HIGH = 'HARM_SEVERITY_HIGH',\n  /**\n   * Harm severity is not supported.\n   *\n   * @remarks\n   * The GoogleAI backend does not support `HarmSeverity`, so this value is used as a fallback.\n   */\n  HARM_SEVERITY_UNSUPPORTED = 'HARM_SEVERITY_UNSUPPORTED'\n}\n\n/**\n * Reason that a prompt was blocked.\n * @public\n */\nexport enum BlockReason {\n  /**\n   * Content was blocked by safety settings.\n   */\n  SAFETY = 'SAFETY',\n  /**\n   * Content was blocked, but the reason is uncategorized.\n   */\n  OTHER = 'OTHER',\n  /**\n   * Content was blocked because it contained terms from the terminology blocklist.\n   */\n  BLOCKLIST = 'BLOCKLIST',\n  /**\n   * Content was blocked due to prohibited content.\n   */\n  PROHIBITED_CONTENT = 'PROHIBITED_CONTENT'\n}\n\n/**\n * Reason that a candidate finished.\n * @public\n */\nexport enum FinishReason {\n  /**\n   * Natural stop point of the model or provided stop sequence.\n   */\n  STOP = 'STOP',\n  /**\n   * The maximum number of tokens as specified in the request was reached.\n   */\n  MAX_TOKENS = 'MAX_TOKENS',\n  /**\n   * The candidate content was flagged for safety reasons.\n   */\n  SAFETY = 'SAFETY',\n  /**\n   * The candidate content was flagged for recitation reasons.\n   */\n  RECITATION = 'RECITATION',\n  /**\n   * Unknown reason.\n   */\n  OTHER = 'OTHER',\n  /**\n   * The candidate content contained forbidden terms.\n   */\n  BLOCKLIST = 'BLOCKLIST',\n  /**\n   * The candidate content potentially contained prohibited content.\n   */\n  PROHIBITED_CONTENT = 'PROHIBITED_CONTENT',\n  /**\n   * The candidate content potentially contained Sensitive Personally Identifiable Information (SPII).\n   */\n  SPII = 'SPII',\n  /**\n   * The function call generated by the model was invalid.\n   */\n  MALFORMED_FUNCTION_CALL = 'MALFORMED_FUNCTION_CALL'\n}\n\n/**\n * @public\n */\nexport enum FunctionCallingMode {\n  /**\n   * Default model behavior; model decides to predict either a function call\n   * or a natural language response.\n   */\n  AUTO = 'AUTO',\n  /**\n   * Model is constrained to always predicting a function call only.\n   * If `allowed_function_names` is set, the predicted function call will be\n   * limited to any one of `allowed_function_names`, else the predicted\n   * function call will be any one of the provided `function_declarations`.\n   */\n  ANY = 'ANY',\n  /**\n   * Model will not predict any function call. Model behavior is same as when\n   * not passing any function declarations.\n   */\n  NONE = 'NONE'\n}\n\n/**\n * Content part modality.\n * @public\n */\nexport enum Modality {\n  /**\n   * Unspecified modality.\n   */\n  MODALITY_UNSPECIFIED = 'MODALITY_UNSPECIFIED',\n  /**\n   * Plain text.\n   */\n  TEXT = 'TEXT',\n  /**\n   * Image.\n   */\n  IMAGE = 'IMAGE',\n  /**\n   * Video.\n   */\n  VIDEO = 'VIDEO',\n  /**\n   * Audio.\n   */\n  AUDIO = 'AUDIO',\n  /**\n   * Document (for example, PDF).\n   */\n  DOCUMENT = 'DOCUMENT'\n}\n\n/**\n * Generation modalities to be returned in generation responses.\n *\n * @beta\n */\nexport const ResponseModality = {\n  /**\n   * Text.\n   * @beta\n   */\n  TEXT: 'TEXT',\n  /**\n   * Image.\n   * @beta\n   */\n  IMAGE: 'IMAGE'\n} as const;\n\n/**\n * Generation modalities to be returned in generation responses.\n *\n * @beta\n */\nexport type ResponseModality =\n  (typeof ResponseModality)[keyof typeof ResponseModality];\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Contains the list of OpenAPI data types\n * as defined by the\n * {@link https://swagger.io/docs/specification/data-models/data-types/ | OpenAPI specification}\n * @public\n */\nexport enum SchemaType {\n  /** String type. */\n  STRING = 'string',\n  /** Number type. */\n  NUMBER = 'number',\n  /** Integer type. */\n  INTEGER = 'integer',\n  /** Boolean type. */\n  BOOLEAN = 'boolean',\n  /** Array type. */\n  ARRAY = 'array',\n  /** Object type. */\n  OBJECT = 'object'\n}\n\n/**\n * Basic {@link Schema} properties shared across several Schema-related\n * types.\n * @public\n */\nexport interface SchemaShared<T> {\n  /** Optional. The format of the property.\n   * When using the Gemini Developer API ({@link GoogleAIBackend}), this must be either `'enum'` or\n   * `'date-time'`, otherwise requests will fail.\n   */\n  format?: string;\n  /** Optional. The description of the property. */\n  description?: string;\n  /**\n   * The title of the property. This helps document the schema's purpose but does not typically\n   * constrain the generated value. It can subtly guide the model by clarifying the intent of a\n   * field.\n   */\n  title?: string;\n  /** Optional. The items of the property. */\n  items?: T;\n  /** The minimum number of items (elements) in a schema of type {@link SchemaType.ARRAY}. */\n  minItems?: number;\n  /** The maximum number of items (elements) in a schema of type {@link SchemaType.ARRAY}. */\n  maxItems?: number;\n  /** Optional. Map of `Schema` objects. */\n  properties?: {\n    [k: string]: T;\n  };\n  /** A hint suggesting the order in which the keys should appear in the generated JSON string. */\n  propertyOrdering?: string[];\n  /** Optional. The enum of the property. */\n  enum?: string[];\n  /** Optional. The example of the property. */\n  example?: unknown;\n  /** Optional. Whether the property is nullable. */\n  nullable?: boolean;\n  /** The minimum value of a numeric type. */\n  minimum?: number;\n  /** The maximum value of a numeric type. */\n  maximum?: number;\n  [key: string]: unknown;\n}\n\n/**\n * Params passed to {@link Schema} static methods to create specific\n * {@link Schema} classes.\n * @public\n */\nexport interface SchemaParams extends SchemaShared<SchemaInterface> {}\n\n/**\n * Final format for {@link Schema} params passed to backend requests.\n * @public\n */\nexport interface SchemaRequest extends SchemaShared<SchemaRequest> {\n  /**\n   * The type of the property. {@link\n   * SchemaType}.\n   */\n  type: SchemaType;\n  /** Optional. Array of required property. */\n  required?: string[];\n}\n\n/**\n * Interface for {@link Schema} class.\n * @public\n */\nexport interface SchemaInterface extends SchemaShared<SchemaInterface> {\n  /**\n   * The type of the property. {@link\n   * SchemaType}.\n   */\n  type: SchemaType;\n}\n\n/**\n * Interface for {@link ObjectSchema} class.\n * @public\n */\nexport interface ObjectSchemaInterface extends SchemaInterface {\n  type: SchemaType.OBJECT;\n  optionalProperties?: string[];\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ImagenImageFormat } from '../../requests/imagen-image-format';\n\n/**\n * Parameters for configuring an {@link ImagenModel}.\n *\n * @beta\n */\nexport interface ImagenModelParams {\n  /**\n   * The Imagen model to use for generating images.\n   * For example: `imagen-3.0-generate-002`.\n   *\n   * Only Imagen 3 models (named `imagen-3.0-*`) are supported.\n   *\n   * See {@link https://firebase.google.com/docs/vertex-ai/models | model versions}\n   * for a full list of supported Imagen 3 models.\n   */\n  model: string;\n  /**\n   * Configuration options for generating images with Imagen.\n   */\n  generationConfig?: ImagenGenerationConfig;\n  /**\n   * Safety settings for filtering potentially inappropriate content.\n   */\n  safetySettings?: ImagenSafetySettings;\n}\n\n/**\n * Configuration options for generating images with Imagen.\n *\n * See the {@link http://firebase.google.com/docs/vertex-ai/generate-images-imagen | documentation} for\n * more details.\n *\n * @beta\n */\nexport interface ImagenGenerationConfig {\n  /**\n   * A description of what should be omitted from the generated images.\n   *\n   * Support for negative prompts depends on the Imagen model.\n   *\n   * See the {@link http://firebase.google.com/docs/vertex-ai/model-parameters#imagen | documentation} for more details.\n   *\n   * This is no longer supported in the Gemini Developer API ({@link GoogleAIBackend}) in versions\n   * greater than `imagen-3.0-generate-002`.\n   */\n  negativePrompt?: string;\n  /**\n   * The number of images to generate. The default value is 1.\n   *\n   * The number of sample images that may be generated in each request depends on the model\n   * (typically up to 4); see the <a href=\"http://firebase.google.com/docs/vertex-ai/model-parameters#imagen\">sampleCount</a>\n   * documentation for more details.\n   */\n  numberOfImages?: number;\n  /**\n   * The aspect ratio of the generated images. The default value is square 1:1.\n   * Supported aspect ratios depend on the Imagen model, see {@link ImagenAspectRatio}\n   * for more details.\n   */\n  aspectRatio?: ImagenAspectRatio;\n  /**\n   * The image format of the generated images. The default is PNG.\n   *\n   * See {@link ImagenImageFormat} for more details.\n   */\n  imageFormat?: ImagenImageFormat;\n  /**\n   * Whether to add an invisible watermark to generated images.\n   *\n   * If set to `true`, an invisible SynthID watermark is embedded in generated images to indicate\n   * that they are AI generated. If set to `false`, watermarking will be disabled.\n   *\n   * For Imagen 3 models, the default value is `true`; see the <a href=\"http://firebase.google.com/docs/vertex-ai/model-parameters#imagen\">addWatermark</a>\n   * documentation for more details.\n   *\n   * When using the Gemini Developer API ({@link GoogleAIBackend}), this will default to true,\n   * and cannot be turned off.\n   */\n  addWatermark?: boolean;\n}\n\n/**\n * A filter level controlling how aggressively to filter sensitive content.\n *\n * Text prompts provided as inputs and images (generated or uploaded) through Imagen on Vertex AI\n * are assessed against a list of safety filters, which include 'harmful categories' (for example,\n * `violence`, `sexual`, `derogatory`, and `toxic`). This filter level controls how aggressively to\n * filter out potentially harmful content from responses. See the {@link http://firebase.google.com/docs/vertex-ai/generate-images | documentation }\n * and the {@link https://cloud.google.com/vertex-ai/generative-ai/docs/image/responsible-ai-imagen#safety-filters | Responsible AI and usage guidelines}\n * for more details.\n *\n * @beta\n */\nexport enum ImagenSafetyFilterLevel {\n  /**\n   * The most aggressive filtering level; most strict blocking.\n   */\n  BLOCK_LOW_AND_ABOVE = 'block_low_and_above',\n  /**\n   * Blocks some sensitive prompts and responses.\n   */\n  BLOCK_MEDIUM_AND_ABOVE = 'block_medium_and_above',\n  /**\n   * Blocks few sensitive prompts and responses.\n   */\n  BLOCK_ONLY_HIGH = 'block_only_high',\n  /**\n   * The least aggressive filtering level; blocks very few sensitive prompts and responses.\n   *\n   * Access to this feature is restricted and may require your case to be reviewed and approved by\n   * Cloud support.\n   */\n  BLOCK_NONE = 'block_none'\n}\n\n/**\n * A filter level controlling whether generation of images containing people or faces is allowed.\n *\n * See the <a href=\"http://firebase.google.com/docs/vertex-ai/generate-images\">personGeneration</a>\n * documentation for more details.\n *\n * @beta\n */\nexport enum ImagenPersonFilterLevel {\n  /**\n   * Disallow generation of images containing people or faces; images of people are filtered out.\n   */\n  BLOCK_ALL = 'dont_allow',\n  /**\n   * Allow generation of images containing adults only; images of children are filtered out.\n   *\n   * Generation of images containing people or faces may require your use case to be\n   * reviewed and approved by Cloud support; see the {@link https://cloud.google.com/vertex-ai/generative-ai/docs/image/responsible-ai-imagen#person-face-gen | Responsible AI and usage guidelines}\n   * for more details.\n   */\n  ALLOW_ADULT = 'allow_adult',\n  /**\n   * Allow generation of images containing adults only; images of children are filtered out.\n   *\n   * Generation of images containing people or faces may require your use case to be\n   * reviewed and approved by Cloud support; see the {@link https://cloud.google.com/vertex-ai/generative-ai/docs/image/responsible-ai-imagen#person-face-gen | Responsible AI and usage guidelines}\n   * for more details.\n   */\n  ALLOW_ALL = 'allow_all'\n}\n\n/**\n * Settings for controlling the aggressiveness of filtering out sensitive content.\n *\n * See the {@link http://firebase.google.com/docs/vertex-ai/generate-images | documentation }\n * for more details.\n *\n * @beta\n */\nexport interface ImagenSafetySettings {\n  /**\n   * A filter level controlling how aggressive to filter out sensitive content from generated\n   * images.\n   */\n  safetyFilterLevel?: ImagenSafetyFilterLevel;\n  /**\n   * A filter level controlling whether generation of images containing people or faces is allowed.\n   */\n  personFilterLevel?: ImagenPersonFilterLevel;\n}\n\n/**\n * Aspect ratios for Imagen images.\n *\n * To specify an aspect ratio for generated images, set the `aspectRatio` property in your\n * {@link ImagenGenerationConfig}.\n *\n * See the the {@link http://firebase.google.com/docs/vertex-ai/generate-images | documentation }\n * for more details and examples of the supported aspect ratios.\n *\n * @beta\n */\nexport enum ImagenAspectRatio {\n  /**\n   * Square (1:1) aspect ratio.\n   */\n  SQUARE = '1:1',\n  /**\n   * Landscape (3:4) aspect ratio.\n   */\n  LANDSCAPE_3x4 = '3:4',\n  /**\n   * Portrait (4:3) aspect ratio.\n   */\n  PORTRAIT_4x3 = '4:3',\n  /**\n   * Landscape (16:9) aspect ratio.\n   */\n  LANDSCAPE_16x9 = '16:9',\n  /**\n   * Portrait (9:16) aspect ratio.\n   */\n  PORTRAIT_9x16 = '9:16'\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp } from '@firebase/app';\nimport { Backend } from './backend';\n\nexport * from './types';\n\n/**\n * @deprecated Use the new {@link AI | AI} instead. The Vertex AI in Firebase SDK has been\n * replaced with the Firebase AI SDK to accommodate the evolving set of supported features and\n * services. For migration details, see the {@link https://firebase.google.com/docs/vertex-ai/migrate-to-latest-sdk | migration guide}.\n *\n * An instance of the Firebase AI SDK.\n *\n * @public\n */\nexport type VertexAI = AI;\n\n/**\n * Options when initializing the Firebase AI SDK.\n *\n * @public\n */\nexport interface VertexAIOptions {\n  location?: string;\n}\n\n/**\n * An instance of the Firebase AI SDK.\n *\n * Do not create this instance directly. Instead, use {@link getAI | getAI()}.\n *\n * @public\n */\nexport interface AI {\n  /**\n   * The {@link @firebase/app#FirebaseApp} this {@link AI} instance is associated with.\n   */\n  app: FirebaseApp;\n  /**\n   * A {@link Backend} instance that specifies the configuration for the target backend,\n   * either the Gemini Developer API (using {@link GoogleAIBackend}) or the\n   * Vertex AI Gemini API (using {@link VertexAIBackend}).\n   */\n  backend: Backend;\n  /**\n   * @deprecated use `AI.backend.location` instead.\n   *\n   * The location configured for this AI service instance, relevant for Vertex AI backends.\n   */\n  location: string;\n}\n\n/**\n * An enum-like object containing constants that represent the supported backends\n * for the Firebase AI SDK.\n * This determines which backend service (Vertex AI Gemini API or Gemini Developer API)\n * the SDK will communicate with.\n *\n * These values are assigned to the `backendType` property within the specific backend\n * configuration objects ({@link GoogleAIBackend} or {@link VertexAIBackend}) to identify\n * which service to target.\n *\n * @public\n */\nexport const BackendType = {\n  /**\n   * Identifies the backend service for the Vertex AI Gemini API provided through Google Cloud.\n   * Use this constant when creating a {@link VertexAIBackend} configuration.\n   */\n  VERTEX_AI: 'VERTEX_AI',\n\n  /**\n   * Identifies the backend service for the Gemini Developer API ({@link https://ai.google/ | Google AI}).\n   * Use this constant when creating a {@link GoogleAIBackend} configuration.\n   */\n  GOOGLE_AI: 'GOOGLE_AI'\n} as const; // Using 'as const' makes the string values literal types\n\n/**\n * Type alias representing valid backend types.\n * It can be either `'VERTEX_AI'` or `'GOOGLE_AI'`.\n *\n * @public\n */\nexport type BackendType = (typeof BackendType)[keyof typeof BackendType];\n\n/**\n * Options for initializing the AI service using {@link getAI | getAI()}.\n * This allows specifying which backend to use (Vertex AI Gemini API or Gemini Developer API)\n * and configuring its specific options (like location for Vertex AI).\n *\n * @public\n */\nexport interface AIOptions {\n  /**\n   * The backend configuration to use for the AI service instance.\n   */\n  backend: Backend;\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DEFAULT_LOCATION } from './constants';\nimport { BackendType } from './public-types';\n\n/**\n * Abstract base class representing the configuration for an AI service backend.\n * This class should not be instantiated directly. Use its subclasses; {@link GoogleAIBackend} for\n * the Gemini Developer API (via {@link https://ai.google/ | Google AI}), and\n * {@link VertexAIBackend} for the Vertex AI Gemini API.\n *\n * @public\n */\nexport abstract class Backend {\n  /**\n   * Specifies the backend type.\n   */\n  readonly backendType: BackendType;\n\n  /**\n   * Protected constructor for use by subclasses.\n   * @param type - The backend type.\n   */\n  protected constructor(type: BackendType) {\n    this.backendType = type;\n  }\n}\n\n/**\n * Configuration class for the Gemini Developer API.\n *\n * Use this with {@link AIOptions} when initializing the AI service via\n * {@link getAI | getAI()} to specify the Gemini Developer API as the backend.\n *\n * @public\n */\nexport class GoogleAIBackend extends Backend {\n  /**\n   * Creates a configuration object for the Gemini Developer API backend.\n   */\n  constructor() {\n    super(BackendType.GOOGLE_AI);\n  }\n}\n\n/**\n * Configuration class for the Vertex AI Gemini API.\n *\n * Use this with {@link AIOptions} when initializing the AI service via\n * {@link getAI | getAI()} to specify the Vertex AI Gemini API as the backend.\n *\n * @public\n */\nexport class VertexAIBackend extends Backend {\n  /**\n   * The region identifier.\n   * See {@link https://firebase.google.com/docs/vertex-ai/locations#available-locations | Vertex AI locations}\n   * for a list of supported locations.\n   */\n  readonly location: string;\n\n  /**\n   * Creates a configuration object for the Vertex AI backend.\n   *\n   * @param location - The region identifier, defaulting to `us-central1`;\n   * see {@link https://firebase.google.com/docs/vertex-ai/locations#available-locations | Vertex AI locations}\n   * for a list of supported locations.\n   */\n  constructor(location: string = DEFAULT_LOCATION) {\n    super(BackendType.VERTEX_AI);\n    if (!location) {\n      this.location = DEFAULT_LOCATION;\n    } else {\n      this.location = location;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport { AI } from './public-types';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport { Provider } from '@firebase/component';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\nimport { Backend, VertexAIBackend } from './backend';\n\nexport class AIService implements AI, _FirebaseService {\n  auth: FirebaseAuthInternal | null;\n  appCheck: FirebaseAppCheckInternal | null;\n  location: string; // This is here for backwards-compatibility\n\n  constructor(\n    public app: FirebaseApp,\n    public backend: Backend,\n    authProvider?: Provider<FirebaseAuthInternalName>,\n    appCheckProvider?: Provider<AppCheckInternalComponentName>\n  ) {\n    const appCheck = appCheckProvider?.getImmediate({ optional: true });\n    const auth = authProvider?.getImmediate({ optional: true });\n    this.auth = auth || null;\n    this.appCheck = appCheck || null;\n\n    if (backend instanceof VertexAIBackend) {\n      this.location = backend.location;\n    } else {\n      this.location = '';\n    }\n  }\n\n  _delete(): Promise<void> {\n    return Promise.resolve();\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { AIErrorCode, CustomErrorData } from './types';\nimport { AI_TYPE } from './constants';\n\n/**\n * Error class for the Firebase AI SDK.\n *\n * @public\n */\nexport class AIError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `AIError` class.\n   *\n   * @param code - The error code from {@link AIErrorCode}.\n   * @param message - A human-readable message describing the error.\n   * @param customErrorData - Optional error data.\n   */\n  constructor(\n    readonly code: <PERSON><PERSON><PERSON>rC<PERSON>,\n    message: string,\n    readonly customErrorData?: CustomErrorData\n  ) {\n    // Match error format used by FirebaseError from ErrorFactory\n    const service = AI_TYPE;\n    const fullCode = `${service}/${code}`;\n    const fullMessage = `${service}: ${message} (${fullCode})`;\n    super(code, fullMessage);\n\n    // FirebaseError initializes a stack trace, but it assumes the error is created from the error\n    // factory. Since we break this assumption, we set the stack trace to be originating from this\n    // constructor.\n    // This is only supported in V8.\n    if (Error.captureStackTrace) {\n      // Allows us to initialize the stack trace without including the constructor itself at the\n      // top level of the stack trace.\n      Error.captureStackTrace(this, AIError);\n    }\n\n    // Allows instanceof AIError in ES5/ES6\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    // TODO(dlarocque): Replace this with `new.target`: https://www.typescriptlang.org/docs/handbook/release-notes/typescript-2-2.html#support-for-newtarget\n    //                   which we can now use since we no longer target ES5.\n    Object.setPrototypeOf(this, AIError.prototype);\n\n    // Since Error is an interface, we don't inherit toString and so we define it ourselves.\n    this.toString = () => fullMessage;\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AI_TYPE } from './constants';\nimport { AIError } from './errors';\nimport { AIErrorCode } from './types';\nimport { Backend, GoogleAIBackend, VertexAIBackend } from './backend';\n\n/**\n * Encodes a {@link Backend} into a string that will be used to uniquely identify {@link AI}\n * instances by backend type.\n *\n * @internal\n */\nexport function encodeInstanceIdentifier(backend: Backend): string {\n  if (backend instanceof GoogleAIBackend) {\n    return `${AI_TYPE}/googleai`;\n  } else if (backend instanceof VertexAIBackend) {\n    return `${AI_TYPE}/vertexai/${backend.location}`;\n  } else {\n    throw new AIError(\n      AIErrorCode.ERROR,\n      `Invalid backend: ${JSON.stringify(backend.backendType)}`\n    );\n  }\n}\n\n/**\n * Decodes an instance identifier string into a {@link Backend}.\n *\n * @internal\n */\nexport function decodeInstanceIdentifier(instanceIdentifier: string): Backend {\n  const identifierParts = instanceIdentifier.split('/');\n  if (identifierParts[0] !== AI_TYPE) {\n    throw new AIError(\n      AIErrorCode.ERROR,\n      `Invalid instance identifier, unknown prefix '${identifierParts[0]}'`\n    );\n  }\n  const backendType = identifierParts[1];\n  switch (backendType) {\n    case 'vertexai':\n      const location: string | undefined = identifierParts[2];\n      if (!location) {\n        throw new AIError(\n          AIErrorCode.ERROR,\n          `Invalid instance identifier, unknown location '${instanceIdentifier}'`\n        );\n      }\n      return new VertexAIBackend(location);\n    case 'googleai':\n      return new GoogleAIBackend();\n    default:\n      throw new AIError(\n        AIErrorCode.ERROR,\n        `Invalid instance identifier string: '${instanceIdentifier}'`\n      );\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AIError } from '../errors';\nimport { AIErrorCode, AI, BackendType } from '../public-types';\nimport { AIService } from '../service';\nimport { ApiSettings } from '../types/internal';\nimport { _isFirebaseServerApp } from '@firebase/app';\n\n/**\n * Base class for Firebase AI model APIs.\n *\n * Instances of this class are associated with a specific Firebase AI {@link Backend}\n * and provide methods for interacting with the configured generative model.\n *\n * @public\n */\nexport abstract class AIModel {\n  /**\n   * The fully qualified model resource name to use for generating images\n   * (for example, `publishers/google/models/imagen-3.0-generate-002`).\n   */\n  readonly model: string;\n\n  /**\n   * @internal\n   */\n  protected _apiSettings: ApiSettings;\n\n  /**\n   * Constructs a new instance of the {@link AIModel} class.\n   *\n   * This constructor should only be called from subclasses that provide\n   * a model API.\n   *\n   * @param ai - an {@link AI} instance.\n   * @param modelName - The name of the model being used. It can be in one of the following formats:\n   * - `my-model` (short name, will resolve to `publishers/google/models/my-model`)\n   * - `models/my-model` (will resolve to `publishers/google/models/my-model`)\n   * - `publishers/my-publisher/models/my-model` (fully qualified model name)\n   *\n   * @throws If the `apiKey` or `projectId` fields are missing in your\n   * Firebase config.\n   *\n   * @internal\n   */\n  protected constructor(ai: AI, modelName: string) {\n    if (!ai.app?.options?.apiKey) {\n      throw new AIError(\n        AIErrorCode.NO_API_KEY,\n        `The \"apiKey\" field is empty in the local Firebase config. Firebase AI requires this field to contain a valid API key.`\n      );\n    } else if (!ai.app?.options?.projectId) {\n      throw new AIError(\n        AIErrorCode.NO_PROJECT_ID,\n        `The \"projectId\" field is empty in the local Firebase config. Firebase AI requires this field to contain a valid project ID.`\n      );\n    } else if (!ai.app?.options?.appId) {\n      throw new AIError(\n        AIErrorCode.NO_APP_ID,\n        `The \"appId\" field is empty in the local Firebase config. Firebase AI requires this field to contain a valid app ID.`\n      );\n    } else {\n      this._apiSettings = {\n        apiKey: ai.app.options.apiKey,\n        project: ai.app.options.projectId,\n        appId: ai.app.options.appId,\n        automaticDataCollectionEnabled: ai.app.automaticDataCollectionEnabled,\n        location: ai.location,\n        backend: ai.backend\n      };\n\n      if (_isFirebaseServerApp(ai.app) && ai.app.settings.appCheckToken) {\n        const token = ai.app.settings.appCheckToken;\n        this._apiSettings.getAppCheckToken = () => {\n          return Promise.resolve({ token });\n        };\n      } else if ((ai as AIService).appCheck) {\n        this._apiSettings.getAppCheckToken = () =>\n          (ai as AIService).appCheck!.getToken();\n      }\n\n      if ((ai as AIService).auth) {\n        this._apiSettings.getAuthToken = () =>\n          (ai as AIService).auth!.getToken();\n      }\n\n      this.model = AIModel.normalizeModelName(\n        modelName,\n        this._apiSettings.backend.backendType\n      );\n    }\n  }\n\n  /**\n   * Normalizes the given model name to a fully qualified model resource name.\n   *\n   * @param modelName - The model name to normalize.\n   * @returns The fully qualified model resource name.\n   *\n   * @internal\n   */\n  static normalizeModelName(\n    modelName: string,\n    backendType: BackendType\n  ): string {\n    if (backendType === BackendType.GOOGLE_AI) {\n      return AIModel.normalizeGoogleAIModelName(modelName);\n    } else {\n      return AIModel.normalizeVertexAIModelName(modelName);\n    }\n  }\n\n  /**\n   * @internal\n   */\n  private static normalizeGoogleAIModelName(modelName: string): string {\n    return `models/${modelName}`;\n  }\n\n  /**\n   * @internal\n   */\n  private static normalizeVertexAIModelName(modelName: string): string {\n    let model: string;\n    if (modelName.includes('/')) {\n      if (modelName.startsWith('models/')) {\n        // Add 'publishers/google' if the user is only passing in 'models/model-name'.\n        model = `publishers/google/${modelName}`;\n      } else {\n        // Any other custom format (e.g. tuned models) must be passed in correctly.\n        model = modelName;\n      }\n    } else {\n      // If path is not included, assume it's a non-tuned model.\n      model = `publishers/google/models/${modelName}`;\n    }\n\n    return model;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Logger } from '@firebase/logger';\n\nexport const logger = new Logger('@firebase/vertexai');\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorDetails, RequestOptions, AIErrorCode } from '../types';\nimport { AIError } from '../errors';\nimport { ApiSettings } from '../types/internal';\nimport {\n  DEFAULT_API_VERSION,\n  DEFAULT_BASE_URL,\n  DEFAULT_FETCH_TIMEOUT_MS,\n  LANGUAGE_TAG,\n  PACKAGE_VERSION\n} from '../constants';\nimport { logger } from '../logger';\nimport { GoogleAIBackend, VertexAIBackend } from '../backend';\n\nexport enum Task {\n  GENERATE_CONTENT = 'generateContent',\n  STREAM_GENERATE_CONTENT = 'streamGenerateContent',\n  COUNT_TOKENS = 'countTokens',\n  PREDICT = 'predict'\n}\n\nexport class RequestUrl {\n  constructor(\n    public model: string,\n    public task: Task,\n    public apiSettings: ApiSettings,\n    public stream: boolean,\n    public requestOptions?: RequestOptions\n  ) {}\n  toString(): string {\n    const url = new URL(this.baseUrl); // Throws if the URL is invalid\n    url.pathname = `/${this.apiVersion}/${this.modelPath}:${this.task}`;\n    url.search = this.queryParams.toString();\n    return url.toString();\n  }\n\n  private get baseUrl(): string {\n    return this.requestOptions?.baseUrl || DEFAULT_BASE_URL;\n  }\n\n  private get apiVersion(): string {\n    return DEFAULT_API_VERSION; // TODO: allow user-set options if that feature becomes available\n  }\n\n  private get modelPath(): string {\n    if (this.apiSettings.backend instanceof GoogleAIBackend) {\n      return `projects/${this.apiSettings.project}/${this.model}`;\n    } else if (this.apiSettings.backend instanceof VertexAIBackend) {\n      return `projects/${this.apiSettings.project}/locations/${this.apiSettings.backend.location}/${this.model}`;\n    } else {\n      throw new AIError(\n        AIErrorCode.ERROR,\n        `Invalid backend: ${JSON.stringify(this.apiSettings.backend)}`\n      );\n    }\n  }\n\n  private get queryParams(): URLSearchParams {\n    const params = new URLSearchParams();\n    if (this.stream) {\n      params.set('alt', 'sse');\n    }\n\n    return params;\n  }\n}\n\n/**\n * Log language and \"fire/version\" to x-goog-api-client\n */\nfunction getClientHeaders(): string {\n  const loggingTags = [];\n  loggingTags.push(`${LANGUAGE_TAG}/${PACKAGE_VERSION}`);\n  loggingTags.push(`fire/${PACKAGE_VERSION}`);\n  return loggingTags.join(' ');\n}\n\nexport async function getHeaders(url: RequestUrl): Promise<Headers> {\n  const headers = new Headers();\n  headers.append('Content-Type', 'application/json');\n  headers.append('x-goog-api-client', getClientHeaders());\n  headers.append('x-goog-api-key', url.apiSettings.apiKey);\n  if (url.apiSettings.automaticDataCollectionEnabled) {\n    headers.append('X-Firebase-Appid', url.apiSettings.appId);\n  }\n  if (url.apiSettings.getAppCheckToken) {\n    const appCheckToken = await url.apiSettings.getAppCheckToken();\n    if (appCheckToken) {\n      headers.append('X-Firebase-AppCheck', appCheckToken.token);\n      if (appCheckToken.error) {\n        logger.warn(\n          `Unable to obtain a valid App Check token: ${appCheckToken.error.message}`\n        );\n      }\n    }\n  }\n\n  if (url.apiSettings.getAuthToken) {\n    const authToken = await url.apiSettings.getAuthToken();\n    if (authToken) {\n      headers.append('Authorization', `Firebase ${authToken.accessToken}`);\n    }\n  }\n\n  return headers;\n}\n\nexport async function constructRequest(\n  model: string,\n  task: Task,\n  apiSettings: ApiSettings,\n  stream: boolean,\n  body: string,\n  requestOptions?: RequestOptions\n): Promise<{ url: string; fetchOptions: RequestInit }> {\n  const url = new RequestUrl(model, task, apiSettings, stream, requestOptions);\n  return {\n    url: url.toString(),\n    fetchOptions: {\n      method: 'POST',\n      headers: await getHeaders(url),\n      body\n    }\n  };\n}\n\nexport async function makeRequest(\n  model: string,\n  task: Task,\n  apiSettings: ApiSettings,\n  stream: boolean,\n  body: string,\n  requestOptions?: RequestOptions\n): Promise<Response> {\n  const url = new RequestUrl(model, task, apiSettings, stream, requestOptions);\n  let response;\n  let fetchTimeoutId: string | number | NodeJS.Timeout | undefined;\n  try {\n    const request = await constructRequest(\n      model,\n      task,\n      apiSettings,\n      stream,\n      body,\n      requestOptions\n    );\n    // Timeout is 180s by default\n    const timeoutMillis =\n      requestOptions?.timeout != null && requestOptions.timeout >= 0\n        ? requestOptions.timeout\n        : DEFAULT_FETCH_TIMEOUT_MS;\n    const abortController = new AbortController();\n    fetchTimeoutId = setTimeout(() => abortController.abort(), timeoutMillis);\n    request.fetchOptions.signal = abortController.signal;\n\n    response = await fetch(request.url, request.fetchOptions);\n    if (!response.ok) {\n      let message = '';\n      let errorDetails;\n      try {\n        const json = await response.json();\n        message = json.error.message;\n        if (json.error.details) {\n          message += ` ${JSON.stringify(json.error.details)}`;\n          errorDetails = json.error.details;\n        }\n      } catch (e) {\n        // ignored\n      }\n      if (\n        response.status === 403 &&\n        errorDetails.some(\n          (detail: ErrorDetails) => detail.reason === 'SERVICE_DISABLED'\n        ) &&\n        errorDetails.some((detail: ErrorDetails) =>\n          (\n            detail.links as Array<Record<string, string>>\n          )?.[0]?.description.includes(\n            'Google developers console API activation'\n          )\n        )\n      ) {\n        throw new AIError(\n          AIErrorCode.API_NOT_ENABLED,\n          `The Firebase AI SDK requires the Firebase AI ` +\n            `API ('firebasevertexai.googleapis.com') to be enabled in your ` +\n            `Firebase project. Enable this API by visiting the Firebase Console ` +\n            `at https://console.firebase.google.com/project/${url.apiSettings.project}/genai/ ` +\n            `and clicking \"Get started\". If you enabled this API recently, ` +\n            `wait a few minutes for the action to propagate to our systems and ` +\n            `then retry.`,\n          {\n            status: response.status,\n            statusText: response.statusText,\n            errorDetails\n          }\n        );\n      }\n      throw new AIError(\n        AIErrorCode.FETCH_ERROR,\n        `Error fetching from ${url}: [${response.status} ${response.statusText}] ${message}`,\n        {\n          status: response.status,\n          statusText: response.statusText,\n          errorDetails\n        }\n      );\n    }\n  } catch (e) {\n    let err = e as Error;\n    if (\n      (e as AIError).code !== AIErrorCode.FETCH_ERROR &&\n      (e as AIError).code !== AIErrorCode.API_NOT_ENABLED &&\n      e instanceof Error\n    ) {\n      err = new AIError(\n        AIErrorCode.ERROR,\n        `Error fetching from ${url.toString()}: ${e.message}`\n      );\n      err.stack = e.stack;\n    }\n\n    throw err;\n  } finally {\n    if (fetchTimeoutId) {\n      clearTimeout(fetchTimeoutId);\n    }\n  }\n  return response;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  EnhancedGenerateContentResponse,\n  FinishReason,\n  FunctionCall,\n  GenerateContentCandidate,\n  GenerateContentResponse,\n  ImagenGCSImage,\n  ImagenInlineImage,\n  AIErrorCode,\n  InlineDataPart\n} from '../types';\nimport { AIError } from '../errors';\nimport { logger } from '../logger';\nimport { ImagenResponseInternal } from '../types/internal';\n\n/**\n * Creates an EnhancedGenerateContentResponse object that has helper functions and\n * other modifications that improve usability.\n */\nexport function createEnhancedContentResponse(\n  response: GenerateContentResponse\n): EnhancedGenerateContentResponse {\n  /**\n   * The Vertex AI backend omits default values.\n   * This causes the `index` property to be omitted from the first candidate in the\n   * response, since it has index 0, and 0 is a default value.\n   * See: https://github.com/firebase/firebase-js-sdk/issues/8566\n   */\n  if (response.candidates && !response.candidates[0].hasOwnProperty('index')) {\n    response.candidates[0].index = 0;\n  }\n\n  const responseWithHelpers = addHelpers(response);\n  return responseWithHelpers;\n}\n\n/**\n * Adds convenience helper methods to a response object, including stream\n * chunks (as long as each chunk is a complete GenerateContentResponse JSON).\n */\nexport function addHelpers(\n  response: GenerateContentResponse\n): EnhancedGenerateContentResponse {\n  (response as EnhancedGenerateContentResponse).text = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        logger.warn(\n          `This response had ${response.candidates.length} ` +\n            `candidates. Returning text from the first candidate only. ` +\n            `Access response.candidates directly to use the other candidates.`\n        );\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new AIError(\n          AIErrorCode.RESPONSE_ERROR,\n          `Response error: ${formatBlockErrorMessage(\n            response\n          )}. Response body stored in error.response`,\n          {\n            response\n          }\n        );\n      }\n      return getText(response);\n    } else if (response.promptFeedback) {\n      throw new AIError(\n        AIErrorCode.RESPONSE_ERROR,\n        `Text not available. ${formatBlockErrorMessage(response)}`,\n        {\n          response\n        }\n      );\n    }\n    return '';\n  };\n  (response as EnhancedGenerateContentResponse).inlineDataParts = ():\n    | InlineDataPart[]\n    | undefined => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        logger.warn(\n          `This response had ${response.candidates.length} ` +\n            `candidates. Returning data from the first candidate only. ` +\n            `Access response.candidates directly to use the other candidates.`\n        );\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new AIError(\n          AIErrorCode.RESPONSE_ERROR,\n          `Response error: ${formatBlockErrorMessage(\n            response\n          )}. Response body stored in error.response`,\n          {\n            response\n          }\n        );\n      }\n      return getInlineDataParts(response);\n    } else if (response.promptFeedback) {\n      throw new AIError(\n        AIErrorCode.RESPONSE_ERROR,\n        `Data not available. ${formatBlockErrorMessage(response)}`,\n        {\n          response\n        }\n      );\n    }\n    return undefined;\n  };\n  (response as EnhancedGenerateContentResponse).functionCalls = () => {\n    if (response.candidates && response.candidates.length > 0) {\n      if (response.candidates.length > 1) {\n        logger.warn(\n          `This response had ${response.candidates.length} ` +\n            `candidates. Returning function calls from the first candidate only. ` +\n            `Access response.candidates directly to use the other candidates.`\n        );\n      }\n      if (hadBadFinishReason(response.candidates[0])) {\n        throw new AIError(\n          AIErrorCode.RESPONSE_ERROR,\n          `Response error: ${formatBlockErrorMessage(\n            response\n          )}. Response body stored in error.response`,\n          {\n            response\n          }\n        );\n      }\n      return getFunctionCalls(response);\n    } else if (response.promptFeedback) {\n      throw new AIError(\n        AIErrorCode.RESPONSE_ERROR,\n        `Function call not available. ${formatBlockErrorMessage(response)}`,\n        {\n          response\n        }\n      );\n    }\n    return undefined;\n  };\n  return response as EnhancedGenerateContentResponse;\n}\n\n/**\n * Returns all text found in all parts of first candidate.\n */\nexport function getText(response: GenerateContentResponse): string {\n  const textStrings = [];\n  if (response.candidates?.[0].content?.parts) {\n    for (const part of response.candidates?.[0].content?.parts) {\n      if (part.text) {\n        textStrings.push(part.text);\n      }\n    }\n  }\n  if (textStrings.length > 0) {\n    return textStrings.join('');\n  } else {\n    return '';\n  }\n}\n\n/**\n * Returns {@link FunctionCall}s associated with first candidate.\n */\nexport function getFunctionCalls(\n  response: GenerateContentResponse\n): FunctionCall[] | undefined {\n  const functionCalls: FunctionCall[] = [];\n  if (response.candidates?.[0].content?.parts) {\n    for (const part of response.candidates?.[0].content?.parts) {\n      if (part.functionCall) {\n        functionCalls.push(part.functionCall);\n      }\n    }\n  }\n  if (functionCalls.length > 0) {\n    return functionCalls;\n  } else {\n    return undefined;\n  }\n}\n\n/**\n * Returns {@link InlineDataPart}s in the first candidate if present.\n *\n * @internal\n */\nexport function getInlineDataParts(\n  response: GenerateContentResponse\n): InlineDataPart[] | undefined {\n  const data: InlineDataPart[] = [];\n\n  if (response.candidates?.[0].content?.parts) {\n    for (const part of response.candidates?.[0].content?.parts) {\n      if (part.inlineData) {\n        data.push(part);\n      }\n    }\n  }\n\n  if (data.length > 0) {\n    return data;\n  } else {\n    return undefined;\n  }\n}\n\nconst badFinishReasons = [FinishReason.RECITATION, FinishReason.SAFETY];\n\nfunction hadBadFinishReason(candidate: GenerateContentCandidate): boolean {\n  return (\n    !!candidate.finishReason &&\n    badFinishReasons.includes(candidate.finishReason)\n  );\n}\n\nexport function formatBlockErrorMessage(\n  response: GenerateContentResponse\n): string {\n  let message = '';\n  if (\n    (!response.candidates || response.candidates.length === 0) &&\n    response.promptFeedback\n  ) {\n    message += 'Response was blocked';\n    if (response.promptFeedback?.blockReason) {\n      message += ` due to ${response.promptFeedback.blockReason}`;\n    }\n    if (response.promptFeedback?.blockReasonMessage) {\n      message += `: ${response.promptFeedback.blockReasonMessage}`;\n    }\n  } else if (response.candidates?.[0]) {\n    const firstCandidate = response.candidates[0];\n    if (hadBadFinishReason(firstCandidate)) {\n      message += `Candidate was blocked due to ${firstCandidate.finishReason}`;\n      if (firstCandidate.finishMessage) {\n        message += `: ${firstCandidate.finishMessage}`;\n      }\n    }\n  }\n  return message;\n}\n\n/**\n * Convert a generic successful fetch response body to an Imagen response object\n * that can be returned to the user. This converts the REST APIs response format to our\n * APIs representation of a response.\n *\n * @internal\n */\nexport async function handlePredictResponse<\n  T extends ImagenInlineImage | ImagenGCSImage\n>(response: Response): Promise<{ images: T[]; filteredReason?: string }> {\n  const responseJson: ImagenResponseInternal = await response.json();\n\n  const images: T[] = [];\n  let filteredReason: string | undefined = undefined;\n\n  // The backend should always send a non-empty array of predictions if the response was successful.\n  if (!responseJson.predictions || responseJson.predictions?.length === 0) {\n    throw new AIError(\n      AIErrorCode.RESPONSE_ERROR,\n      'No predictions or filtered reason received from Vertex AI. Please report this issue with the full error details at https://github.com/firebase/firebase-js-sdk/issues.'\n    );\n  }\n\n  for (const prediction of responseJson.predictions) {\n    if (prediction.raiFilteredReason) {\n      filteredReason = prediction.raiFilteredReason;\n    } else if (prediction.mimeType && prediction.bytesBase64Encoded) {\n      images.push({\n        mimeType: prediction.mimeType,\n        bytesBase64Encoded: prediction.bytesBase64Encoded\n      } as T);\n    } else if (prediction.mimeType && prediction.gcsUri) {\n      images.push({\n        mimeType: prediction.mimeType,\n        gcsURI: prediction.gcsUri\n      } as T);\n    } else {\n      throw new AIError(\n        AIErrorCode.RESPONSE_ERROR,\n        `Predictions array in response has missing properties. Response: ${JSON.stringify(\n          responseJson\n        )}`\n      );\n    }\n  }\n\n  return { images, filteredReason };\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AIError } from './errors';\nimport { logger } from './logger';\nimport {\n  CitationMetadata,\n  CountTokensRequest,\n  GenerateContentCandidate,\n  GenerateContentRequest,\n  GenerateContentResponse,\n  HarmSeverity,\n  InlineDataPart,\n  PromptFeedback,\n  SafetyRating,\n  AIErrorCode\n} from './types';\nimport {\n  GoogleAIGenerateContentResponse,\n  GoogleAIGenerateContentCandidate,\n  GoogleAICountTokensRequest\n} from './types/googleai';\n\n/**\n * This SDK supports both the Vertex AI Gemini API and the Gemini Developer API (using Google AI).\n * The public API prioritizes the format used by the Vertex AI Gemini API.\n * We avoid having two sets of types by translating requests and responses between the two API formats.\n * This translation allows developers to switch between the Vertex AI Gemini API and the Gemini Developer API\n * with minimal code changes.\n *\n * In here are functions that map requests and responses between the two API formats.\n * Requests in the Vertex AI format are mapped to the Google AI format before being sent.\n * Responses from the Google AI backend are mapped back to the Vertex AI format before being returned to the user.\n */\n\n/**\n * Maps a Vertex AI {@link GenerateContentRequest} to a format that can be sent to Google AI.\n *\n * @param generateContentRequest The {@link GenerateContentRequest} to map.\n * @returns A {@link GenerateContentResponse} that conforms to the Google AI format.\n *\n * @throws If the request contains properties that are unsupported by Google AI.\n *\n * @internal\n */\nexport function mapGenerateContentRequest(\n  generateContentRequest: GenerateContentRequest\n): GenerateContentRequest {\n  generateContentRequest.safetySettings?.forEach(safetySetting => {\n    if (safetySetting.method) {\n      throw new AIError(\n        AIErrorCode.UNSUPPORTED,\n        'SafetySetting.method is not supported in the the Gemini Developer API. Please remove this property.'\n      );\n    }\n  });\n\n  if (generateContentRequest.generationConfig?.topK) {\n    const roundedTopK = Math.round(\n      generateContentRequest.generationConfig.topK\n    );\n\n    if (roundedTopK !== generateContentRequest.generationConfig.topK) {\n      logger.warn(\n        'topK in GenerationConfig has been rounded to the nearest integer to match the format for requests to the Gemini Developer API.'\n      );\n      generateContentRequest.generationConfig.topK = roundedTopK;\n    }\n  }\n\n  return generateContentRequest;\n}\n\n/**\n * Maps a {@link GenerateContentResponse} from Google AI to the format of the\n * {@link GenerateContentResponse} that we get from VertexAI that is exposed in the public API.\n *\n * @param googleAIResponse The {@link GenerateContentResponse} from Google AI.\n * @returns A {@link GenerateContentResponse} that conforms to the public API's format.\n *\n * @internal\n */\nexport function mapGenerateContentResponse(\n  googleAIResponse: GoogleAIGenerateContentResponse\n): GenerateContentResponse {\n  const generateContentResponse = {\n    candidates: googleAIResponse.candidates\n      ? mapGenerateContentCandidates(googleAIResponse.candidates)\n      : undefined,\n    prompt: googleAIResponse.promptFeedback\n      ? mapPromptFeedback(googleAIResponse.promptFeedback)\n      : undefined,\n    usageMetadata: googleAIResponse.usageMetadata\n  };\n\n  return generateContentResponse;\n}\n\n/**\n * Maps a Vertex AI {@link CountTokensRequest} to a format that can be sent to Google AI.\n *\n * @param countTokensRequest The {@link CountTokensRequest} to map.\n * @param model The model to count tokens with.\n * @returns A {@link CountTokensRequest} that conforms to the Google AI format.\n *\n * @internal\n */\nexport function mapCountTokensRequest(\n  countTokensRequest: CountTokensRequest,\n  model: string\n): GoogleAICountTokensRequest {\n  const mappedCountTokensRequest: GoogleAICountTokensRequest = {\n    generateContentRequest: {\n      model,\n      ...countTokensRequest\n    }\n  };\n\n  return mappedCountTokensRequest;\n}\n\n/**\n * Maps a Google AI {@link GoogleAIGenerateContentCandidate} to a format that conforms\n * to the Vertex AI API format.\n *\n * @param candidates The {@link GoogleAIGenerateContentCandidate} to map.\n * @returns A {@link GenerateContentCandidate} that conforms to the Vertex AI format.\n *\n * @throws If any {@link Part} in the candidates has a `videoMetadata` property.\n *\n * @internal\n */\nexport function mapGenerateContentCandidates(\n  candidates: GoogleAIGenerateContentCandidate[]\n): GenerateContentCandidate[] {\n  const mappedCandidates: GenerateContentCandidate[] = [];\n  let mappedSafetyRatings: SafetyRating[];\n  if (mappedCandidates) {\n    candidates.forEach(candidate => {\n      // Map citationSources to citations.\n      let citationMetadata: CitationMetadata | undefined;\n      if (candidate.citationMetadata) {\n        citationMetadata = {\n          citations: candidate.citationMetadata.citationSources\n        };\n      }\n\n      // Assign missing candidate SafetyRatings properties to their defaults if undefined.\n      if (candidate.safetyRatings) {\n        mappedSafetyRatings = candidate.safetyRatings.map(safetyRating => {\n          return {\n            ...safetyRating,\n            severity:\n              safetyRating.severity ?? HarmSeverity.HARM_SEVERITY_UNSUPPORTED,\n            probabilityScore: safetyRating.probabilityScore ?? 0,\n            severityScore: safetyRating.severityScore ?? 0\n          };\n        });\n      }\n\n      // videoMetadata is not supported.\n      // Throw early since developers may send a long video as input and only expect to pay\n      // for inference on a small portion of the video.\n      if (\n        candidate.content?.parts.some(\n          part => (part as InlineDataPart)?.videoMetadata\n        )\n      ) {\n        throw new AIError(\n          AIErrorCode.UNSUPPORTED,\n          'Part.videoMetadata is not supported in the Gemini Developer API. Please remove this property.'\n        );\n      }\n\n      const mappedCandidate = {\n        index: candidate.index,\n        content: candidate.content,\n        finishReason: candidate.finishReason,\n        finishMessage: candidate.finishMessage,\n        safetyRatings: mappedSafetyRatings,\n        citationMetadata,\n        groundingMetadata: candidate.groundingMetadata\n      };\n      mappedCandidates.push(mappedCandidate);\n    });\n  }\n\n  return mappedCandidates;\n}\n\nexport function mapPromptFeedback(\n  promptFeedback: PromptFeedback\n): PromptFeedback {\n  // Assign missing SafetyRating properties to their defaults if undefined.\n  const mappedSafetyRatings: SafetyRating[] = [];\n  promptFeedback.safetyRatings.forEach(safetyRating => {\n    mappedSafetyRatings.push({\n      category: safetyRating.category,\n      probability: safetyRating.probability,\n      severity: safetyRating.severity ?? HarmSeverity.HARM_SEVERITY_UNSUPPORTED,\n      probabilityScore: safetyRating.probabilityScore ?? 0,\n      severityScore: safetyRating.severityScore ?? 0,\n      blocked: safetyRating.blocked\n    });\n  });\n\n  const mappedPromptFeedback: PromptFeedback = {\n    blockReason: promptFeedback.blockReason,\n    safetyRatings: mappedSafetyRatings,\n    blockReasonMessage: promptFeedback.blockReasonMessage\n  };\n  return mappedPromptFeedback;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  EnhancedGenerateContentResponse,\n  GenerateContentCandidate,\n  GenerateContentResponse,\n  GenerateContentStreamResult,\n  Part,\n  AIErrorCode\n} from '../types';\nimport { AIError } from '../errors';\nimport { createEnhancedContentResponse } from './response-helpers';\nimport * as GoogleAIMapper from '../googleai-mappers';\nimport { GoogleAIGenerateContentResponse } from '../types/googleai';\nimport { ApiSettings } from '../types/internal';\nimport { BackendType } from '../public-types';\n\nconst responseLineRE = /^data\\: (.*)(?:\\n\\n|\\r\\r|\\r\\n\\r\\n)/;\n\n/**\n * Process a response.body stream from the backend and return an\n * iterator that provides one complete GenerateContentResponse at a time\n * and a promise that resolves with a single aggregated\n * GenerateContentResponse.\n *\n * @param response - Response from a fetch call\n */\nexport function processStream(\n  response: Response,\n  apiSettings: ApiSettings\n): GenerateContentStreamResult {\n  const inputStream = response.body!.pipeThrough(\n    new TextDecoderStream('utf8', { fatal: true })\n  );\n  const responseStream =\n    getResponseStream<GenerateContentResponse>(inputStream);\n  const [stream1, stream2] = responseStream.tee();\n  return {\n    stream: generateResponseSequence(stream1, apiSettings),\n    response: getResponsePromise(stream2, apiSettings)\n  };\n}\n\nasync function getResponsePromise(\n  stream: ReadableStream<GenerateContentResponse>,\n  apiSettings: ApiSettings\n): Promise<EnhancedGenerateContentResponse> {\n  const allResponses: GenerateContentResponse[] = [];\n  const reader = stream.getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      let generateContentResponse = aggregateResponses(allResponses);\n      if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n        generateContentResponse = GoogleAIMapper.mapGenerateContentResponse(\n          generateContentResponse as GoogleAIGenerateContentResponse\n        );\n      }\n      return createEnhancedContentResponse(generateContentResponse);\n    }\n\n    allResponses.push(value);\n  }\n}\n\nasync function* generateResponseSequence(\n  stream: ReadableStream<GenerateContentResponse>,\n  apiSettings: ApiSettings\n): AsyncGenerator<EnhancedGenerateContentResponse> {\n  const reader = stream.getReader();\n  while (true) {\n    const { value, done } = await reader.read();\n    if (done) {\n      break;\n    }\n\n    let enhancedResponse: EnhancedGenerateContentResponse;\n    if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n      enhancedResponse = createEnhancedContentResponse(\n        GoogleAIMapper.mapGenerateContentResponse(\n          value as GoogleAIGenerateContentResponse\n        )\n      );\n    } else {\n      enhancedResponse = createEnhancedContentResponse(value);\n    }\n\n    yield enhancedResponse;\n  }\n}\n\n/**\n * Reads a raw stream from the fetch response and join incomplete\n * chunks, returning a new stream that provides a single complete\n * GenerateContentResponse in each iteration.\n */\nexport function getResponseStream<T>(\n  inputStream: ReadableStream<string>\n): ReadableStream<T> {\n  const reader = inputStream.getReader();\n  const stream = new ReadableStream<T>({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      function pump(): Promise<(() => Promise<void>) | undefined> {\n        return reader.read().then(({ value, done }) => {\n          if (done) {\n            if (currentText.trim()) {\n              controller.error(\n                new AIError(AIErrorCode.PARSE_FAILED, 'Failed to parse stream')\n              );\n              return;\n            }\n            controller.close();\n            return;\n          }\n\n          currentText += value;\n          let match = currentText.match(responseLineRE);\n          let parsedResponse: T;\n          while (match) {\n            try {\n              parsedResponse = JSON.parse(match[1]);\n            } catch (e) {\n              controller.error(\n                new AIError(\n                  AIErrorCode.PARSE_FAILED,\n                  `Error parsing JSON response: \"${match[1]}`\n                )\n              );\n              return;\n            }\n            controller.enqueue(parsedResponse);\n            currentText = currentText.substring(match[0].length);\n            match = currentText.match(responseLineRE);\n          }\n          return pump();\n        });\n      }\n    }\n  });\n  return stream;\n}\n\n/**\n * Aggregates an array of `GenerateContentResponse`s into a single\n * GenerateContentResponse.\n */\nexport function aggregateResponses(\n  responses: GenerateContentResponse[]\n): GenerateContentResponse {\n  const lastResponse = responses[responses.length - 1];\n  const aggregatedResponse: GenerateContentResponse = {\n    promptFeedback: lastResponse?.promptFeedback\n  };\n  for (const response of responses) {\n    if (response.candidates) {\n      for (const candidate of response.candidates) {\n        // Index will be undefined if it's the first index (0), so we should use 0 if it's undefined.\n        // See: https://github.com/firebase/firebase-js-sdk/issues/8566\n        const i = candidate.index || 0;\n        if (!aggregatedResponse.candidates) {\n          aggregatedResponse.candidates = [];\n        }\n        if (!aggregatedResponse.candidates[i]) {\n          aggregatedResponse.candidates[i] = {\n            index: candidate.index\n          } as GenerateContentCandidate;\n        }\n        // Keep overwriting, the last one will be final\n        aggregatedResponse.candidates[i].citationMetadata =\n          candidate.citationMetadata;\n        aggregatedResponse.candidates[i].finishReason = candidate.finishReason;\n        aggregatedResponse.candidates[i].finishMessage =\n          candidate.finishMessage;\n        aggregatedResponse.candidates[i].safetyRatings =\n          candidate.safetyRatings;\n\n        /**\n         * Candidates should always have content and parts, but this handles\n         * possible malformed responses.\n         */\n        if (candidate.content && candidate.content.parts) {\n          if (!aggregatedResponse.candidates[i].content) {\n            aggregatedResponse.candidates[i].content = {\n              role: candidate.content.role || 'user',\n              parts: []\n            };\n          }\n          const newPart: Partial<Part> = {};\n          for (const part of candidate.content.parts) {\n            if (part.text !== undefined) {\n              // The backend can send empty text parts. If these are sent back\n              // (e.g. in chat history), the backend will respond with an error.\n              // To prevent this, ignore empty text parts.\n              if (part.text === '') {\n                continue;\n              }\n              newPart.text = part.text;\n            }\n            if (part.functionCall) {\n              newPart.functionCall = part.functionCall;\n            }\n            if (Object.keys(newPart).length === 0) {\n              throw new AIError(\n                AIErrorCode.INVALID_CONTENT,\n                'Part should have at least one property, but there are none. This is likely caused ' +\n                  'by a malformed response from the backend.'\n              );\n            }\n            aggregatedResponse.candidates[i].content.parts.push(\n              newPart as Part\n            );\n          }\n        }\n      }\n    }\n  }\n  return aggregatedResponse;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  GenerateContentRequest,\n  GenerateContentResponse,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  RequestOptions\n} from '../types';\nimport { Task, makeRequest } from '../requests/request';\nimport { createEnhancedContentResponse } from '../requests/response-helpers';\nimport { processStream } from '../requests/stream-reader';\nimport { ApiSettings } from '../types/internal';\nimport * as GoogleAIMapper from '../googleai-mappers';\nimport { BackendType } from '../public-types';\n\nexport async function generateContentStream(\n  apiSettings: ApiSettings,\n  model: string,\n  params: GenerateContentRequest,\n  requestOptions?: RequestOptions\n): Promise<GenerateContentStreamResult> {\n  if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n    params = GoogleAIMapper.mapGenerateContentRequest(params);\n  }\n  const response = await makeRequest(\n    model,\n    Task.STREAM_GENERATE_CONTENT,\n    apiSettings,\n    /* stream */ true,\n    JSON.stringify(params),\n    requestOptions\n  );\n  return processStream(response, apiSettings); // TODO: Map streaming responses\n}\n\nexport async function generateContent(\n  apiSettings: ApiSettings,\n  model: string,\n  params: GenerateContentRequest,\n  requestOptions?: RequestOptions\n): Promise<GenerateContentResult> {\n  if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n    params = GoogleAIMapper.mapGenerateContentRequest(params);\n  }\n  const response = await makeRequest(\n    model,\n    Task.GENERATE_CONTENT,\n    apiSettings,\n    /* stream */ false,\n    JSON.stringify(params),\n    requestOptions\n  );\n  const generateContentResponse = await processGenerateContentResponse(\n    response,\n    apiSettings\n  );\n  const enhancedResponse = createEnhancedContentResponse(\n    generateContentResponse\n  );\n  return {\n    response: enhancedResponse\n  };\n}\n\nasync function processGenerateContentResponse(\n  response: Response,\n  apiSettings: ApiSettings\n): Promise<GenerateContentResponse> {\n  const responseJson = await response.json();\n  if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n    return GoogleAIMapper.mapGenerateContentResponse(responseJson);\n  } else {\n    return responseJson;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Content, GenerateContentRequest, Part, AIErrorCode } from '../types';\nimport { AIError } from '../errors';\nimport { ImagenGenerationParams, PredictRequestBody } from '../types/internal';\n\nexport function formatSystemInstruction(\n  input?: string | Part | Content\n): Content | undefined {\n  // null or undefined\n  if (input == null) {\n    return undefined;\n  } else if (typeof input === 'string') {\n    return { role: 'system', parts: [{ text: input }] } as Content;\n  } else if ((input as Part).text) {\n    return { role: 'system', parts: [input as Part] };\n  } else if ((input as Content).parts) {\n    if (!(input as Content).role) {\n      return { role: 'system', parts: (input as Content).parts };\n    } else {\n      return input as Content;\n    }\n  }\n}\n\nexport function formatNewContent(\n  request: string | Array<string | Part>\n): Content {\n  let newParts: Part[] = [];\n  if (typeof request === 'string') {\n    newParts = [{ text: request }];\n  } else {\n    for (const partOrString of request) {\n      if (typeof partOrString === 'string') {\n        newParts.push({ text: partOrString });\n      } else {\n        newParts.push(partOrString);\n      }\n    }\n  }\n  return assignRoleToPartsAndValidateSendMessageRequest(newParts);\n}\n\n/**\n * When multiple Part types (i.e. FunctionResponsePart and TextPart) are\n * passed in a single Part array, we may need to assign different roles to each\n * part. Currently only FunctionResponsePart requires a role other than 'user'.\n * @private\n * @param parts Array of parts to pass to the model\n * @returns Array of content items\n */\nfunction assignRoleToPartsAndValidateSendMessageRequest(\n  parts: Part[]\n): Content {\n  const userContent: Content = { role: 'user', parts: [] };\n  const functionContent: Content = { role: 'function', parts: [] };\n  let hasUserContent = false;\n  let hasFunctionContent = false;\n  for (const part of parts) {\n    if ('functionResponse' in part) {\n      functionContent.parts.push(part);\n      hasFunctionContent = true;\n    } else {\n      userContent.parts.push(part);\n      hasUserContent = true;\n    }\n  }\n\n  if (hasUserContent && hasFunctionContent) {\n    throw new AIError(\n      AIErrorCode.INVALID_CONTENT,\n      'Within a single message, FunctionResponse cannot be mixed with other type of Part in the request for sending chat message.'\n    );\n  }\n\n  if (!hasUserContent && !hasFunctionContent) {\n    throw new AIError(\n      AIErrorCode.INVALID_CONTENT,\n      'No Content is provided for sending chat message.'\n    );\n  }\n\n  if (hasUserContent) {\n    return userContent;\n  }\n\n  return functionContent;\n}\n\nexport function formatGenerateContentInput(\n  params: GenerateContentRequest | string | Array<string | Part>\n): GenerateContentRequest {\n  let formattedRequest: GenerateContentRequest;\n  if ((params as GenerateContentRequest).contents) {\n    formattedRequest = params as GenerateContentRequest;\n  } else {\n    // Array or string\n    const content = formatNewContent(params as string | Array<string | Part>);\n    formattedRequest = { contents: [content] };\n  }\n  if ((params as GenerateContentRequest).systemInstruction) {\n    formattedRequest.systemInstruction = formatSystemInstruction(\n      (params as GenerateContentRequest).systemInstruction\n    );\n  }\n  return formattedRequest;\n}\n\n/**\n * Convert the user-defined parameters in {@link ImagenGenerationParams} to the format\n * that is expected from the REST API.\n *\n * @internal\n */\nexport function createPredictRequestBody(\n  prompt: string,\n  {\n    gcsURI,\n    imageFormat,\n    addWatermark,\n    numberOfImages = 1,\n    negativePrompt,\n    aspectRatio,\n    safetyFilterLevel,\n    personFilterLevel\n  }: ImagenGenerationParams\n): PredictRequestBody {\n  // Properties that are undefined will be omitted from the JSON string that is sent in the request.\n  const body: PredictRequestBody = {\n    instances: [\n      {\n        prompt\n      }\n    ],\n    parameters: {\n      storageUri: gcsURI,\n      negativePrompt,\n      sampleCount: numberOfImages,\n      aspectRatio,\n      outputOptions: imageFormat,\n      addWatermark,\n      safetyFilterLevel,\n      personGeneration: personFilterLevel,\n      includeRaiReason: true\n    }\n  };\n  return body;\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Content, POSSIBLE_ROLES, Part, Role, AIErrorCode } from '../types';\nimport { AIError } from '../errors';\n\n// https://ai.google.dev/api/rest/v1beta/Content#part\n\nconst VALID_PART_FIELDS: Array<keyof Part> = [\n  'text',\n  'inlineData',\n  'functionCall',\n  'functionResponse'\n];\n\nconst VALID_PARTS_PER_ROLE: { [key in Role]: Array<keyof Part> } = {\n  user: ['text', 'inlineData'],\n  function: ['functionResponse'],\n  model: ['text', 'functionCall'],\n  // System instructions shouldn't be in history anyway.\n  system: ['text']\n};\n\nconst VALID_PREVIOUS_CONTENT_ROLES: { [key in Role]: Role[] } = {\n  user: ['model'],\n  function: ['model'],\n  model: ['user', 'function'],\n  // System instructions shouldn't be in history.\n  system: []\n};\n\nexport function validateChatHistory(history: Content[]): void {\n  let prevContent: Content | null = null;\n  for (const currContent of history) {\n    const { role, parts } = currContent;\n    if (!prevContent && role !== 'user') {\n      throw new AIError(\n        AIErrorCode.INVALID_CONTENT,\n        `First Content should be with role 'user', got ${role}`\n      );\n    }\n    if (!POSSIBLE_ROLES.includes(role)) {\n      throw new AIError(\n        AIErrorCode.INVALID_CONTENT,\n        `Each item should include role field. Got ${role} but valid roles are: ${JSON.stringify(\n          POSSIBLE_ROLES\n        )}`\n      );\n    }\n\n    if (!Array.isArray(parts)) {\n      throw new AIError(\n        AIErrorCode.INVALID_CONTENT,\n        `Content should have 'parts' but property with an array of Parts`\n      );\n    }\n\n    if (parts.length === 0) {\n      throw new AIError(\n        AIErrorCode.INVALID_CONTENT,\n        `Each Content should have at least one part`\n      );\n    }\n\n    const countFields: Record<keyof Part, number> = {\n      text: 0,\n      inlineData: 0,\n      functionCall: 0,\n      functionResponse: 0\n    };\n\n    for (const part of parts) {\n      for (const key of VALID_PART_FIELDS) {\n        if (key in part) {\n          countFields[key] += 1;\n        }\n      }\n    }\n    const validParts = VALID_PARTS_PER_ROLE[role];\n    for (const key of VALID_PART_FIELDS) {\n      if (!validParts.includes(key) && countFields[key] > 0) {\n        throw new AIError(\n          AIErrorCode.INVALID_CONTENT,\n          `Content with role '${role}' can't contain '${key}' part`\n        );\n      }\n    }\n\n    if (prevContent) {\n      const validPreviousContentRoles = VALID_PREVIOUS_CONTENT_ROLES[role];\n      if (!validPreviousContentRoles.includes(prevContent.role)) {\n        throw new AIError(\n          AIErrorCode.INVALID_CONTENT,\n          `Content with role '${role}' can't follow '${\n            prevContent.role\n          }'. Valid previous roles: ${JSON.stringify(\n            VALID_PREVIOUS_CONTENT_ROLES\n          )}`\n        );\n      }\n    }\n    prevContent = currContent;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Content,\n  GenerateContentRequest,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  Part,\n  RequestOptions,\n  StartChatParams\n} from '../types';\nimport { formatNewContent } from '../requests/request-helpers';\nimport { formatBlockErrorMessage } from '../requests/response-helpers';\nimport { validateChatHistory } from './chat-session-helpers';\nimport { generateContent, generateContentStream } from './generate-content';\nimport { ApiSettings } from '../types/internal';\nimport { logger } from '../logger';\n\n/**\n * Do not log a message for this error.\n */\nconst SILENT_ERROR = 'SILENT_ERROR';\n\n/**\n * ChatSession class that enables sending chat messages and stores\n * history of sent and received messages so far.\n *\n * @public\n */\nexport class ChatSession {\n  private _apiSettings: ApiSettings;\n  private _history: Content[] = [];\n  private _sendPromise: Promise<void> = Promise.resolve();\n\n  constructor(\n    apiSettings: ApiSettings,\n    public model: string,\n    public params?: StartChatParams,\n    public requestOptions?: RequestOptions\n  ) {\n    this._apiSettings = apiSettings;\n    if (params?.history) {\n      validateChatHistory(params.history);\n      this._history = params.history;\n    }\n  }\n\n  /**\n   * Gets the chat history so far. Blocked prompts are not added to history.\n   * Neither blocked candidates nor the prompts that generated them are added\n   * to history.\n   */\n  async getHistory(): Promise<Content[]> {\n    await this._sendPromise;\n    return this._history;\n  }\n\n  /**\n   * Sends a chat message and receives a non-streaming\n   * {@link GenerateContentResult}\n   */\n  async sendMessage(\n    request: string | Array<string | Part>\n  ): Promise<GenerateContentResult> {\n    await this._sendPromise;\n    const newContent = formatNewContent(request);\n    const generateContentRequest: GenerateContentRequest = {\n      safetySettings: this.params?.safetySettings,\n      generationConfig: this.params?.generationConfig,\n      tools: this.params?.tools,\n      toolConfig: this.params?.toolConfig,\n      systemInstruction: this.params?.systemInstruction,\n      contents: [...this._history, newContent]\n    };\n    let finalResult = {} as GenerateContentResult;\n    // Add onto the chain.\n    this._sendPromise = this._sendPromise\n      .then(() =>\n        generateContent(\n          this._apiSettings,\n          this.model,\n          generateContentRequest,\n          this.requestOptions\n        )\n      )\n      .then(result => {\n        if (\n          result.response.candidates &&\n          result.response.candidates.length > 0\n        ) {\n          this._history.push(newContent);\n          const responseContent: Content = {\n            parts: result.response.candidates?.[0].content.parts || [],\n            // Response seems to come back without a role set.\n            role: result.response.candidates?.[0].content.role || 'model'\n          };\n          this._history.push(responseContent);\n        } else {\n          const blockErrorMessage = formatBlockErrorMessage(result.response);\n          if (blockErrorMessage) {\n            logger.warn(\n              `sendMessage() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`\n            );\n          }\n        }\n        finalResult = result;\n      });\n    await this._sendPromise;\n    return finalResult;\n  }\n\n  /**\n   * Sends a chat message and receives the response as a\n   * {@link GenerateContentStreamResult} containing an iterable stream\n   * and a response promise.\n   */\n  async sendMessageStream(\n    request: string | Array<string | Part>\n  ): Promise<GenerateContentStreamResult> {\n    await this._sendPromise;\n    const newContent = formatNewContent(request);\n    const generateContentRequest: GenerateContentRequest = {\n      safetySettings: this.params?.safetySettings,\n      generationConfig: this.params?.generationConfig,\n      tools: this.params?.tools,\n      toolConfig: this.params?.toolConfig,\n      systemInstruction: this.params?.systemInstruction,\n      contents: [...this._history, newContent]\n    };\n    const streamPromise = generateContentStream(\n      this._apiSettings,\n      this.model,\n      generateContentRequest,\n      this.requestOptions\n    );\n\n    // Add onto the chain.\n    this._sendPromise = this._sendPromise\n      .then(() => streamPromise)\n      // This must be handled to avoid unhandled rejection, but jump\n      // to the final catch block with a label to not log this error.\n      .catch(_ignored => {\n        throw new Error(SILENT_ERROR);\n      })\n      .then(streamResult => streamResult.response)\n      .then(response => {\n        if (response.candidates && response.candidates.length > 0) {\n          this._history.push(newContent);\n          const responseContent = { ...response.candidates[0].content };\n          // Response seems to come back without a role set.\n          if (!responseContent.role) {\n            responseContent.role = 'model';\n          }\n          this._history.push(responseContent);\n        } else {\n          const blockErrorMessage = formatBlockErrorMessage(response);\n          if (blockErrorMessage) {\n            logger.warn(\n              `sendMessageStream() was unsuccessful. ${blockErrorMessage}. Inspect response object for details.`\n            );\n          }\n        }\n      })\n      .catch(e => {\n        // Errors in streamPromise are already catchable by the user as\n        // streamPromise is returned.\n        // Avoid duplicating the error message in logs.\n        if (e.message !== SILENT_ERROR) {\n          // Users do not have access to _sendPromise to catch errors\n          // downstream from streamPromise, so they should not throw.\n          logger.error(e);\n        }\n      });\n    return streamPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  CountTokensRequest,\n  CountTokensResponse,\n  RequestOptions\n} from '../types';\nimport { Task, makeRequest } from '../requests/request';\nimport { ApiSettings } from '../types/internal';\nimport * as GoogleAIMapper from '../googleai-mappers';\nimport { BackendType } from '../public-types';\n\nexport async function countTokens(\n  apiSettings: ApiSettings,\n  model: string,\n  params: CountTokensRequest,\n  requestOptions?: RequestOptions\n): Promise<CountTokensResponse> {\n  let body: string = '';\n  if (apiSettings.backend.backendType === BackendType.GOOGLE_AI) {\n    const mappedParams = GoogleAIMapper.mapCountTokensRequest(params, model);\n    body = JSON.stringify(mappedParams);\n  } else {\n    body = JSON.stringify(params);\n  }\n  const response = await makeRequest(\n    model,\n    Task.COUNT_TOKENS,\n    apiSettings,\n    false,\n    body,\n    requestOptions\n  );\n  return response.json();\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  generateContent,\n  generateContentStream\n} from '../methods/generate-content';\nimport {\n  Content,\n  CountTokensRequest,\n  CountTokensResponse,\n  GenerateContentRequest,\n  GenerateContentResult,\n  GenerateContentStreamResult,\n  GenerationConfig,\n  ModelParams,\n  Part,\n  RequestOptions,\n  SafetySetting,\n  StartChatParams,\n  Tool,\n  ToolConfig\n} from '../types';\nimport { ChatSession } from '../methods/chat-session';\nimport { countTokens } from '../methods/count-tokens';\nimport {\n  formatGenerateContentInput,\n  formatSystemInstruction\n} from '../requests/request-helpers';\nimport { AI } from '../public-types';\nimport { AIModel } from './ai-model';\n\n/**\n * Class for generative model APIs.\n * @public\n */\nexport class GenerativeModel extends AIModel {\n  generationConfig: GenerationConfig;\n  safetySettings: SafetySetting[];\n  requestOptions?: RequestOptions;\n  tools?: Tool[];\n  toolConfig?: ToolConfig;\n  systemInstruction?: Content;\n\n  constructor(\n    ai: AI,\n    modelParams: ModelParams,\n    requestOptions?: RequestOptions\n  ) {\n    super(ai, modelParams.model);\n    this.generationConfig = modelParams.generationConfig || {};\n    this.safetySettings = modelParams.safetySettings || [];\n    this.tools = modelParams.tools;\n    this.toolConfig = modelParams.toolConfig;\n    this.systemInstruction = formatSystemInstruction(\n      modelParams.systemInstruction\n    );\n    this.requestOptions = requestOptions || {};\n  }\n\n  /**\n   * Makes a single non-streaming call to the model\n   * and returns an object containing a single {@link GenerateContentResponse}.\n   */\n  async generateContent(\n    request: GenerateContentRequest | string | Array<string | Part>\n  ): Promise<GenerateContentResult> {\n    const formattedParams = formatGenerateContentInput(request);\n    return generateContent(\n      this._apiSettings,\n      this.model,\n      {\n        generationConfig: this.generationConfig,\n        safetySettings: this.safetySettings,\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        ...formattedParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Makes a single streaming call to the model\n   * and returns an object containing an iterable stream that iterates\n   * over all chunks in the streaming response as well as\n   * a promise that returns the final aggregated response.\n   */\n  async generateContentStream(\n    request: GenerateContentRequest | string | Array<string | Part>\n  ): Promise<GenerateContentStreamResult> {\n    const formattedParams = formatGenerateContentInput(request);\n    return generateContentStream(\n      this._apiSettings,\n      this.model,\n      {\n        generationConfig: this.generationConfig,\n        safetySettings: this.safetySettings,\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        ...formattedParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Gets a new {@link ChatSession} instance which can be used for\n   * multi-turn chats.\n   */\n  startChat(startChatParams?: StartChatParams): ChatSession {\n    return new ChatSession(\n      this._apiSettings,\n      this.model,\n      {\n        tools: this.tools,\n        toolConfig: this.toolConfig,\n        systemInstruction: this.systemInstruction,\n        generationConfig: this.generationConfig,\n        safetySettings: this.safetySettings,\n        /**\n         * Overrides params inherited from GenerativeModel with those explicitly set in the\n         * StartChatParams. For example, if startChatParams.generationConfig is set, it'll override\n         * this.generationConfig.\n         */\n        ...startChatParams\n      },\n      this.requestOptions\n    );\n  }\n\n  /**\n   * Counts the tokens in the provided request.\n   */\n  async countTokens(\n    request: CountTokensRequest | string | Array<string | Part>\n  ): Promise<CountTokensResponse> {\n    const formattedParams = formatGenerateContentInput(request);\n    return countTokens(this._apiSettings, this.model, formattedParams);\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AI } from '../public-types';\nimport { Task, makeRequest } from '../requests/request';\nimport { createPredictRequestBody } from '../requests/request-helpers';\nimport { handlePredictResponse } from '../requests/response-helpers';\nimport {\n  ImagenGCSImage,\n  ImagenGenerationConfig,\n  ImagenInlineImage,\n  RequestOptions,\n  ImagenModelParams,\n  ImagenGenerationResponse,\n  ImagenSafetySettings\n} from '../types';\nimport { AIModel } from './ai-model';\n\n/**\n * Class for Imagen model APIs.\n *\n * This class provides methods for generating images using the Imagen model.\n *\n * @example\n * ```javascript\n * const imagen = new ImagenModel(\n *   ai,\n *   {\n *     model: 'imagen-3.0-generate-002'\n *   }\n * );\n *\n * const response = await imagen.generateImages('A photo of a cat');\n * if (response.images.length > 0) {\n *   console.log(response.images[0].bytesBase64Encoded);\n * }\n * ```\n *\n * @beta\n */\nexport class ImagenModel extends AIModel {\n  /**\n   * The Imagen generation configuration.\n   */\n  generationConfig?: ImagenGenerationConfig;\n  /**\n   * Safety settings for filtering inappropriate content.\n   */\n  safetySettings?: ImagenSafetySettings;\n\n  /**\n   * Constructs a new instance of the {@link ImagenModel} class.\n   *\n   * @param ai - an {@link AI} instance.\n   * @param modelParams - Parameters to use when making requests to Imagen.\n   * @param requestOptions - Additional options to use when making requests.\n   *\n   * @throws If the `apiKey` or `projectId` fields are missing in your\n   * Firebase config.\n   */\n  constructor(\n    ai: AI,\n    modelParams: ImagenModelParams,\n    public requestOptions?: RequestOptions\n  ) {\n    const { model, generationConfig, safetySettings } = modelParams;\n    super(ai, model);\n    this.generationConfig = generationConfig;\n    this.safetySettings = safetySettings;\n  }\n\n  /**\n   * Generates images using the Imagen model and returns them as\n   * base64-encoded strings.\n   *\n   * @param prompt - A text prompt describing the image(s) to generate.\n   * @returns A promise that resolves to an {@link ImagenGenerationResponse}\n   * object containing the generated images.\n   *\n   * @throws If the request to generate images fails. This happens if the\n   * prompt is blocked.\n   *\n   * @remarks\n   * If the prompt was not blocked, but one or more of the generated images were filtered, the\n   * returned object will have a `filteredReason` property.\n   * If all images are filtered, the `images` array will be empty.\n   *\n   * @beta\n   */\n  async generateImages(\n    prompt: string\n  ): Promise<ImagenGenerationResponse<ImagenInlineImage>> {\n    const body = createPredictRequestBody(prompt, {\n      ...this.generationConfig,\n      ...this.safetySettings\n    });\n    const response = await makeRequest(\n      this.model,\n      Task.PREDICT,\n      this._apiSettings,\n      /* stream */ false,\n      JSON.stringify(body),\n      this.requestOptions\n    );\n    return handlePredictResponse<ImagenInlineImage>(response);\n  }\n\n  /**\n   * Generates images to Cloud Storage for Firebase using the Imagen model.\n   *\n   * @internal This method is temporarily internal.\n   *\n   * @param prompt - A text prompt describing the image(s) to generate.\n   * @param gcsURI - The URI of file stored in a Cloud Storage for Firebase bucket.\n   * This should be a directory. For example, `gs://my-bucket/my-directory/`.\n   * @returns A promise that resolves to an {@link ImagenGenerationResponse}\n   * object containing the URLs of the generated images.\n   *\n   * @throws If the request fails to generate images fails. This happens if\n   * the prompt is blocked.\n   *\n   * @remarks\n   * If the prompt was not blocked, but one or more of the generated images were filtered, the\n   * returned object will have a `filteredReason` property.\n   * If all images are filtered, the `images` array will be empty.\n   */\n  async generateImagesGCS(\n    prompt: string,\n    gcsURI: string\n  ): Promise<ImagenGenerationResponse<ImagenGCSImage>> {\n    const body = createPredictRequestBody(prompt, {\n      gcsURI,\n      ...this.generationConfig,\n      ...this.safetySettings\n    });\n    const response = await makeRequest(\n      this.model,\n      Task.PREDICT,\n      this._apiSettings,\n      /* stream */ false,\n      JSON.stringify(body),\n      this.requestOptions\n    );\n    return handlePredictResponse<ImagenGCSImage>(response);\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AIError } from '../errors';\nimport { AIErrorCode } from '../types';\nimport {\n  SchemaInterface,\n  SchemaType,\n  SchemaParams,\n  SchemaRequest,\n  ObjectSchemaInterface\n} from '../types/schema';\n\n/**\n * Parent class encompassing all Schema types, with static methods that\n * allow building specific Schema types. This class can be converted with\n * `JSON.stringify()` into a JSON string accepted by Vertex AI REST endpoints.\n * (This string conversion is automatically done when calling SDK methods.)\n * @public\n */\nexport abstract class Schema implements SchemaInterface {\n  /**\n   * Optional. The type of the property. {@link\n   * SchemaType}.\n   */\n  type: SchemaType;\n  /** Optional. The format of the property.\n   * Supported formats:<br/>\n   * <ul>\n   *  <li>for NUMBER type: \"float\", \"double\"</li>\n   *  <li>for INTEGER type: \"int32\", \"int64\"</li>\n   *  <li>for STRING type: \"email\", \"byte\", etc</li>\n   * </ul>\n   */\n  format?: string;\n  /** Optional. The description of the property. */\n  description?: string;\n  /** Optional. The items of the property. */\n  items?: SchemaInterface;\n  /** The minimum number of items (elements) in a schema of type {@link SchemaType.ARRAY}. */\n  minItems?: number;\n  /** The maximum number of items (elements) in a schema of type {@link SchemaType.ARRAY}. */\n  maxItems?: number;\n  /** Optional. Whether the property is nullable. Defaults to false. */\n  nullable: boolean;\n  /** Optional. The example of the property. */\n  example?: unknown;\n  /**\n   * Allows user to add other schema properties that have not yet\n   * been officially added to the SDK.\n   */\n  [key: string]: unknown;\n\n  constructor(schemaParams: SchemaInterface) {\n    // eslint-disable-next-line guard-for-in\n    for (const paramKey in schemaParams) {\n      this[paramKey] = schemaParams[paramKey];\n    }\n    // Ensure these are explicitly set to avoid TS errors.\n    this.type = schemaParams.type;\n    this.nullable = schemaParams.hasOwnProperty('nullable')\n      ? !!schemaParams.nullable\n      : false;\n  }\n\n  /**\n   * Defines how this Schema should be serialized as JSON.\n   * See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/JSON/stringify#tojson_behavior\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj: { type: SchemaType; [key: string]: unknown } = {\n      type: this.type\n    };\n    for (const prop in this) {\n      if (this.hasOwnProperty(prop) && this[prop] !== undefined) {\n        if (prop !== 'required' || this.type === SchemaType.OBJECT) {\n          obj[prop] = this[prop];\n        }\n      }\n    }\n    return obj as SchemaRequest;\n  }\n\n  static array(arrayParams: SchemaParams & { items: Schema }): ArraySchema {\n    return new ArraySchema(arrayParams, arrayParams.items);\n  }\n\n  static object(\n    objectParams: SchemaParams & {\n      properties: {\n        [k: string]: Schema;\n      };\n      optionalProperties?: string[];\n    }\n  ): ObjectSchema {\n    return new ObjectSchema(\n      objectParams,\n      objectParams.properties,\n      objectParams.optionalProperties\n    );\n  }\n\n  // eslint-disable-next-line id-blacklist\n  static string(stringParams?: SchemaParams): StringSchema {\n    return new StringSchema(stringParams);\n  }\n\n  static enumString(\n    stringParams: SchemaParams & { enum: string[] }\n  ): StringSchema {\n    return new StringSchema(stringParams, stringParams.enum);\n  }\n\n  static integer(integerParams?: SchemaParams): IntegerSchema {\n    return new IntegerSchema(integerParams);\n  }\n\n  // eslint-disable-next-line id-blacklist\n  static number(numberParams?: SchemaParams): NumberSchema {\n    return new NumberSchema(numberParams);\n  }\n\n  // eslint-disable-next-line id-blacklist\n  static boolean(booleanParams?: SchemaParams): BooleanSchema {\n    return new BooleanSchema(booleanParams);\n  }\n}\n\n/**\n * A type that includes all specific Schema types.\n * @public\n */\nexport type TypedSchema =\n  | IntegerSchema\n  | NumberSchema\n  | StringSchema\n  | BooleanSchema\n  | ObjectSchema\n  | ArraySchema;\n\n/**\n * Schema class for \"integer\" types.\n * @public\n */\nexport class IntegerSchema extends Schema {\n  constructor(schemaParams?: SchemaParams) {\n    super({\n      type: SchemaType.INTEGER,\n      ...schemaParams\n    });\n  }\n}\n\n/**\n * Schema class for \"number\" types.\n * @public\n */\nexport class NumberSchema extends Schema {\n  constructor(schemaParams?: SchemaParams) {\n    super({\n      type: SchemaType.NUMBER,\n      ...schemaParams\n    });\n  }\n}\n\n/**\n * Schema class for \"boolean\" types.\n * @public\n */\nexport class BooleanSchema extends Schema {\n  constructor(schemaParams?: SchemaParams) {\n    super({\n      type: SchemaType.BOOLEAN,\n      ...schemaParams\n    });\n  }\n}\n\n/**\n * Schema class for \"string\" types. Can be used with or without\n * enum values.\n * @public\n */\nexport class StringSchema extends Schema {\n  enum?: string[];\n  constructor(schemaParams?: SchemaParams, enumValues?: string[]) {\n    super({\n      type: SchemaType.STRING,\n      ...schemaParams\n    });\n    this.enum = enumValues;\n  }\n\n  /**\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj = super.toJSON();\n    if (this.enum) {\n      obj['enum'] = this.enum;\n    }\n    return obj as SchemaRequest;\n  }\n}\n\n/**\n * Schema class for \"array\" types.\n * The `items` param should refer to the type of item that can be a member\n * of the array.\n * @public\n */\nexport class ArraySchema extends Schema {\n  constructor(schemaParams: SchemaParams, public items: TypedSchema) {\n    super({\n      type: SchemaType.ARRAY,\n      ...schemaParams\n    });\n  }\n\n  /**\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj = super.toJSON();\n    obj.items = this.items.toJSON();\n    return obj;\n  }\n}\n\n/**\n * Schema class for \"object\" types.\n * The `properties` param must be a map of `Schema` objects.\n * @public\n */\nexport class ObjectSchema extends Schema {\n  constructor(\n    schemaParams: SchemaParams,\n    public properties: {\n      [k: string]: TypedSchema;\n    },\n    public optionalProperties: string[] = []\n  ) {\n    super({\n      type: SchemaType.OBJECT,\n      ...schemaParams\n    });\n  }\n\n  /**\n   * @internal\n   */\n  toJSON(): SchemaRequest {\n    const obj = super.toJSON();\n    obj.properties = { ...this.properties };\n    const required = [];\n    if (this.optionalProperties) {\n      for (const propertyKey of this.optionalProperties) {\n        if (!this.properties.hasOwnProperty(propertyKey)) {\n          throw new AIError(\n            AIErrorCode.INVALID_SCHEMA,\n            `Property \"${propertyKey}\" specified in \"optionalProperties\" does not exist.`\n          );\n        }\n      }\n    }\n    for (const propertyKey in this.properties) {\n      if (this.properties.hasOwnProperty(propertyKey)) {\n        obj.properties[propertyKey] = this.properties[\n          propertyKey\n        ].toJSON() as SchemaRequest;\n        if (!this.optionalProperties.includes(propertyKey)) {\n          required.push(propertyKey);\n        }\n      }\n    }\n    if (required.length > 0) {\n      obj.required = required;\n    }\n    delete (obj as ObjectSchemaInterface).optionalProperties;\n    return obj as SchemaRequest;\n  }\n}\n", "/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { logger } from '../logger';\n\n/**\n * Defines the image format for images generated by Imagen.\n *\n * Use this class to specify the desired format (JPEG or PNG) and compression quality\n * for images generated by Imagen. This is typically included as part of\n * {@link ImagenModelParams}.\n *\n * @example\n * ```javascript\n * const imagenModelParams = {\n *   // ... other ImagenModelParams\n *   imageFormat: ImagenImageFormat.jpeg(75) // JPEG with a compression level of 75.\n * }\n * ```\n *\n * @beta\n */\nexport class ImagenImageFormat {\n  /**\n   * The MIME type.\n   */\n  mimeType: string;\n  /**\n   * The level of compression (a number between 0 and 100).\n   */\n  compressionQuality?: number;\n\n  private constructor() {\n    this.mimeType = 'image/png';\n  }\n\n  /**\n   * Creates an {@link ImagenImageFormat} for a JPEG image.\n   *\n   * @param compressionQuality - The level of compression (a number between 0 and 100).\n   * @returns An {@link ImagenImageFormat} object for a JPEG image.\n   *\n   * @beta\n   */\n  static jpeg(compressionQuality?: number): ImagenImageFormat {\n    if (\n      compressionQuality &&\n      (compressionQuality < 0 || compressionQuality > 100)\n    ) {\n      logger.warn(\n        `Invalid JPEG compression quality of ${compressionQuality} specified; the supported range is [0, 100].`\n      );\n    }\n    return { mimeType: 'image/jpeg', compressionQuality };\n  }\n\n  /**\n   * Creates an {@link ImagenImageFormat} for a PNG image.\n   *\n   * @returns An {@link ImagenImageFormat} object for a PNG image.\n   *\n   * @beta\n   */\n  static png(): ImagenImageFormat {\n    return { mimeType: 'image/png' };\n  }\n}\n", "/**\n * @license\n * Copyright 2024 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Provider } from '@firebase/component';\nimport { getModularInstance } from '@firebase/util';\nimport { AI_TYPE } from './constants';\nimport { AIService } from './service';\nimport { AI, AIOptions, VertexAI, VertexAIOptions } from './public-types';\nimport {\n  ImagenModelParams,\n  ModelParams,\n  RequestOptions,\n  AIErrorCode\n} from './types';\nimport { AIError } from './errors';\nimport { AIModel, GenerativeModel, ImagenModel } from './models';\nimport { encodeInstanceIdentifier } from './helpers';\nimport { GoogleAIBackend, VertexAIBackend } from './backend';\n\nexport { ChatSession } from './methods/chat-session';\nexport * from './requests/schema-builder';\nexport { ImagenImageFormat } from './requests/imagen-image-format';\nexport { AIModel, GenerativeModel, ImagenModel, AIError };\nexport { Backend, VertexAIBackend, GoogleAIBackend } from './backend';\n\nexport { AIErrorCode as VertexAIErrorCode };\n\n/**\n * @deprecated Use the new {@link AIModel} instead. The Vertex AI in Firebase SDK has been\n * replaced with the Firebase AI SDK to accommodate the evolving set of supported features and\n * services. For migration details, see the {@link https://firebase.google.com/docs/vertex-ai/migrate-to-latest-sdk | migration guide}.\n *\n * Base class for Firebase AI model APIs.\n *\n * @public\n */\nexport const VertexAIModel = AIModel;\n\n/**\n * @deprecated Use the new {@link AIError} instead. The Vertex AI in Firebase SDK has been\n * replaced with the Firebase AI SDK to accommodate the evolving set of supported features and\n * services. For migration details, see the {@link https://firebase.google.com/docs/vertex-ai/migrate-to-latest-sdk | migration guide}.\n *\n * Error class for the Firebase AI SDK.\n *\n * @public\n */\nexport const VertexAIError = AIError;\n\ndeclare module '@firebase/component' {\n  interface NameServiceMapping {\n    [AI_TYPE]: AIService;\n  }\n}\n\n/**\n * @deprecated Use the new {@link getAI | getAI()} instead. The Vertex AI in Firebase SDK has been\n * replaced with the Firebase AI SDK to accommodate the evolving set of supported features and\n * services. For migration details, see the {@link https://firebase.google.com/docs/vertex-ai/migrate-to-latest-sdk | migration guide}.\n *\n * Returns a {@link VertexAI} instance for the given app, configured to use the\n * Vertex AI Gemini API. This instance will be\n * configured to use the Vertex AI Gemini API.\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param options - Options to configure the Vertex AI instance, including the location.\n *\n * @public\n */\nexport function getVertexAI(\n  app: FirebaseApp = getApp(),\n  options?: VertexAIOptions\n): VertexAI {\n  app = getModularInstance(app);\n  // Dependencies\n  const AIProvider: Provider<'AI'> = _getProvider(app, AI_TYPE);\n\n  const backend = new VertexAIBackend(options?.location);\n  const identifier = encodeInstanceIdentifier(backend);\n  return AIProvider.getImmediate({\n    identifier\n  });\n}\n\n/**\n * Returns the default {@link AI} instance that is associated with the provided\n * {@link @firebase/app#FirebaseApp}. If no instance exists, initializes a new instance with the\n * default settings.\n *\n * @example\n * ```javascript\n * const ai = getAI(app);\n * ```\n *\n * @example\n * ```javascript\n * // Get an AI instance configured to use the Gemini Developer API (via Google AI).\n * const ai = getAI(app, { backend: new GoogleAIBackend() });\n * ```\n *\n * @example\n * ```javascript\n * // Get an AI instance configured to use the Vertex AI Gemini API.\n * const ai = getAI(app, { backend: new VertexAIBackend() });\n * ```\n *\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param options - {@link AIOptions} that configure the AI instance.\n * @returns The default {@link AI} instance for the given {@link @firebase/app#FirebaseApp}.\n *\n * @public\n */\nexport function getAI(\n  app: FirebaseApp = getApp(),\n  options: AIOptions = { backend: new GoogleAIBackend() }\n): AI {\n  app = getModularInstance(app);\n  // Dependencies\n  const AIProvider: Provider<'AI'> = _getProvider(app, AI_TYPE);\n\n  const identifier = encodeInstanceIdentifier(options.backend);\n  return AIProvider.getImmediate({\n    identifier\n  });\n}\n\n/**\n * Returns a {@link GenerativeModel} class with methods for inference\n * and other functionality.\n *\n * @public\n */\nexport function getGenerativeModel(\n  ai: AI,\n  modelParams: ModelParams,\n  requestOptions?: RequestOptions\n): GenerativeModel {\n  if (!modelParams.model) {\n    throw new AIError(\n      AIErrorCode.NO_MODEL,\n      `Must provide a model name. Example: getGenerativeModel({ model: 'my-model-name' })`\n    );\n  }\n  return new GenerativeModel(ai, modelParams, requestOptions);\n}\n\n/**\n * Returns an {@link ImagenModel} class with methods for using Imagen.\n *\n * Only Imagen 3 models (named `imagen-3.0-*`) are supported.\n *\n * @param ai - An {@link AI} instance.\n * @param modelParams - Parameters to use when making Imagen requests.\n * @param requestOptions - Additional options to use when making requests.\n *\n * @throws If the `apiKey` or `projectId` fields are missing in your\n * Firebase config.\n *\n * @beta\n */\nexport function getImagenModel(\n  ai: AI,\n  modelParams: ImagenModelParams,\n  requestOptions?: RequestOptions\n): ImagenModel {\n  if (!modelParams.model) {\n    throw new AIError(\n      AIErrorCode.NO_MODEL,\n      `Must provide a model name. Example: getImagenModel({ model: 'my-model-name' })`\n    );\n  }\n  return new ImagenModel(ai, modelParams, requestOptions);\n}\n", "/**\n * The Firebase AI Web SDK.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2025 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerVersion, _registerComponent } from '@firebase/app';\nimport { AIService } from './service';\nimport { AI_TYPE } from './constants';\nimport { Component, ComponentType } from '@firebase/component';\nimport { name, version } from '../package.json';\nimport { decodeInstanceIdentifier } from './helpers';\nimport { AIError } from './errors';\nimport { AIErrorCode } from './public-types';\n\nfunction registerAI(): void {\n  _registerComponent(\n    new Component(\n      AI_TYPE,\n      (container, { instanceIdentifier }) => {\n        if (!instanceIdentifier) {\n          throw new AIError(\n            AIErrorCode.ERROR,\n            'AIService instance identifier is undefined.'\n          );\n        }\n\n        const backend = decodeInstanceIdentifier(instanceIdentifier);\n\n        // getImmediate for FirebaseApp will always succeed\n        const app = container.getProvider('app').getImmediate();\n        const auth = container.getProvider('auth-internal');\n        const appCheckProvider = container.getProvider('app-check-internal');\n        return new AIService(app, backend, auth, appCheckProvider);\n      },\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, 'node');\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n\nregisterAI();\n\nexport * from './api';\nexport * from './public-types';\n"], "names": ["GoogleAIMapper.mapGenerateContentResponse", "GoogleAIMapper.mapGenerateContentRequest", "GoogleAIMapper.mapCountTokensRequest"], "mappings": ";;;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAII,MAAM,OAAO,GAAG,IAAI,CAAC;AAErB,MAAM,gBAAgB,GAAG,aAAa,CAAC;AAEvC,MAAM,gBAAgB,GAAG,yCAAyC,CAAC;AAEnE,MAAM,mBAAmB,GAAG,QAAQ,CAAC;AAErC,MAAM,eAAe,GAAG,OAAO,CAAC;AAEhC,MAAM,YAAY,GAAG,OAAO,CAAC;AAE7B,MAAM,wBAAwB,GAAG,GAAG,GAAG,IAAI;;AC/BlD;;;;;;;;;;;;;;;AAeG;AAQH;;;AAGG;AACI,MAAM,cAAc,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAW;AAE/E;;;AAGG;IACS,aAKX;AALD,CAAA,UAAY,YAAY,EAAA;AACtB,IAAA,YAAA,CAAA,2BAAA,CAAA,GAAA,2BAAuD,CAAA;AACvD,IAAA,YAAA,CAAA,iCAAA,CAAA,GAAA,iCAAmE,CAAA;AACnE,IAAA,YAAA,CAAA,0BAAA,CAAA,GAAA,0BAAqD,CAAA;AACrD,IAAA,YAAA,CAAA,iCAAA,CAAA,GAAA,iCAAmE,CAAA;AACrE,CAAC,EALW,YAAY,KAAZ,YAAY,GAKvB,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,mBAsBX;AAtBD,CAAA,UAAY,kBAAkB,EAAA;AAC5B;;AAEG;AACH,IAAA,kBAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C,CAAA;AAC3C;;AAEG;AACH,IAAA,kBAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD,CAAA;AACjD;;AAEG;AACH,IAAA,kBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;AACnC;;AAEG;AACH,IAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB;;;AAGG;AACH,IAAA,kBAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACb,CAAC,EAtBW,kBAAkB,KAAlB,kBAAkB,GAsB7B,EAAA,CAAA,CAAA,CAAA;AAED;;;;AAIG;IACS,gBASX;AATD,CAAA,UAAY,eAAe,EAAA;AACzB;;AAEG;AACH,IAAA,eAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACrB;;AAEG;AACH,IAAA,eAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC7B,CAAC,EATW,eAAe,KAAf,eAAe,GAS1B,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,gBAiBX;AAjBD,CAAA,UAAY,eAAe,EAAA;AACzB;;AAEG;AACH,IAAA,eAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB;;AAEG;AACH,IAAA,eAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX;;AAEG;AACH,IAAA,eAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB;;AAEG;AACH,IAAA,eAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACf,CAAC,EAjBW,eAAe,KAAf,eAAe,GAiB1B,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,aAwBX;AAxBD,CAAA,UAAY,YAAY,EAAA;AACtB;;AAEG;AACH,IAAA,YAAA,CAAA,0BAAA,CAAA,GAAA,0BAAqD,CAAA;AACrD;;AAEG;AACH,IAAA,YAAA,CAAA,mBAAA,CAAA,GAAA,mBAAuC,CAAA;AACvC;;AAEG;AACH,IAAA,YAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C,CAAA;AAC7C;;AAEG;AACH,IAAA,YAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;AACzC;;;;;AAKG;AACH,IAAA,YAAA,CAAA,2BAAA,CAAA,GAAA,2BAAuD,CAAA;AACzD,CAAC,EAxBW,YAAY,KAAZ,YAAY,GAwBvB,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,YAiBX;AAjBD,CAAA,UAAY,WAAW,EAAA;AACrB;;AAEG;AACH,IAAA,WAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB;;AAEG;AACH,IAAA,WAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf;;AAEG;AACH,IAAA,WAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB;;AAEG;AACH,IAAA,WAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;AAC3C,CAAC,EAjBW,WAAW,KAAX,WAAW,GAiBtB,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,aAqCX;AArCD,CAAA,UAAY,YAAY,EAAA;AACtB;;AAEG;AACH,IAAA,YAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb;;AAEG;AACH,IAAA,YAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB;;AAEG;AACH,IAAA,YAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACjB;;AAEG;AACH,IAAA,YAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AACzB;;AAEG;AACH,IAAA,YAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf;;AAEG;AACH,IAAA,YAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACvB;;AAEG;AACH,IAAA,YAAA,CAAA,oBAAA,CAAA,GAAA,oBAAyC,CAAA;AACzC;;AAEG;AACH,IAAA,YAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb;;AAEG;AACH,IAAA,YAAA,CAAA,yBAAA,CAAA,GAAA,yBAAmD,CAAA;AACrD,CAAC,EArCW,YAAY,KAAZ,YAAY,GAqCvB,EAAA,CAAA,CAAA,CAAA;AAED;;AAEG;IACS,oBAkBX;AAlBD,CAAA,UAAY,mBAAmB,EAAA;AAC7B;;;AAGG;AACH,IAAA,mBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb;;;;;AAKG;AACH,IAAA,mBAAA,CAAA,KAAA,CAAA,GAAA,KAAW,CAAA;AACX;;;AAGG;AACH,IAAA,mBAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACf,CAAC,EAlBW,mBAAmB,KAAnB,mBAAmB,GAkB9B,EAAA,CAAA,CAAA,CAAA;AAED;;;AAGG;IACS,SAyBX;AAzBD,CAAA,UAAY,QAAQ,EAAA;AAClB;;AAEG;AACH,IAAA,QAAA,CAAA,sBAAA,CAAA,GAAA,sBAA6C,CAAA;AAC7C;;AAEG;AACH,IAAA,QAAA,CAAA,MAAA,CAAA,GAAA,MAAa,CAAA;AACb;;AAEG;AACH,IAAA,QAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf;;AAEG;AACH,IAAA,QAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf;;AAEG;AACH,IAAA,QAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;AACf;;AAEG;AACH,IAAA,QAAA,CAAA,UAAA,CAAA,GAAA,UAAqB,CAAA;AACvB,CAAC,EAzBW,QAAQ,KAAR,QAAQ,GAyBnB,EAAA,CAAA,CAAA,CAAA;AAED;;;;AAIG;AACU,MAAA,gBAAgB,GAAG;AAC9B;;;AAGG;AACH,IAAA,IAAI,EAAE,MAAM;AACZ;;;AAGG;AACH,IAAA,KAAK,EAAE,OAAO;;;AChRhB;;;;;;;;;;;;;;;AAeG;AAEH;;;;;AAKG;IACS,WAaX;AAbD,CAAA,UAAY,UAAU,EAAA;;AAEpB,IAAA,UAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;;AAEjB,IAAA,UAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;;AAEjB,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;;AAEnB,IAAA,UAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;;AAEnB,IAAA,UAAA,CAAA,OAAA,CAAA,GAAA,OAAe,CAAA;;AAEf,IAAA,UAAA,CAAA,QAAA,CAAA,GAAA,QAAiB,CAAA;AACnB,CAAC,EAbW,UAAU,KAAV,UAAU,GAarB,EAAA,CAAA,CAAA;;ACpCD;;;;;;;;;;;;;;;AAeG;AAqFH;;;;;;;;;;;AAWG;IACS,wBAoBX;AApBD,CAAA,UAAY,uBAAuB,EAAA;AACjC;;AAEG;AACH,IAAA,uBAAA,CAAA,qBAAA,CAAA,GAAA,qBAA2C,CAAA;AAC3C;;AAEG;AACH,IAAA,uBAAA,CAAA,wBAAA,CAAA,GAAA,wBAAiD,CAAA;AACjD;;AAEG;AACH,IAAA,uBAAA,CAAA,iBAAA,CAAA,GAAA,iBAAmC,CAAA;AACnC;;;;;AAKG;AACH,IAAA,uBAAA,CAAA,YAAA,CAAA,GAAA,YAAyB,CAAA;AAC3B,CAAC,EApBW,uBAAuB,KAAvB,uBAAuB,GAoBlC,EAAA,CAAA,CAAA,CAAA;AAED;;;;;;;AAOG;IACS,wBAqBX;AArBD,CAAA,UAAY,uBAAuB,EAAA;AACjC;;AAEG;AACH,IAAA,uBAAA,CAAA,WAAA,CAAA,GAAA,YAAwB,CAAA;AACxB;;;;;;AAMG;AACH,IAAA,uBAAA,CAAA,aAAA,CAAA,GAAA,aAA2B,CAAA;AAC3B;;;;;;AAMG;AACH,IAAA,uBAAA,CAAA,WAAA,CAAA,GAAA,WAAuB,CAAA;AACzB,CAAC,EArBW,uBAAuB,KAAvB,uBAAuB,GAqBlC,EAAA,CAAA,CAAA,CAAA;AAsBD;;;;;;;;;;AAUG;IACS,kBAqBX;AArBD,CAAA,UAAY,iBAAiB,EAAA;AAC3B;;AAEG;AACH,IAAA,iBAAA,CAAA,QAAA,CAAA,GAAA,KAAc,CAAA;AACd;;AAEG;AACH,IAAA,iBAAA,CAAA,eAAA,CAAA,GAAA,KAAqB,CAAA;AACrB;;AAEG;AACH,IAAA,iBAAA,CAAA,cAAA,CAAA,GAAA,KAAoB,CAAA;AACpB;;AAEG;AACH,IAAA,iBAAA,CAAA,gBAAA,CAAA,GAAA,MAAuB,CAAA;AACvB;;AAEG;AACH,IAAA,iBAAA,CAAA,eAAA,CAAA,GAAA,MAAsB,CAAA;AACxB,CAAC,EArBW,iBAAiB,KAAjB,iBAAiB,GAqB5B,EAAA,CAAA,CAAA;;ACzND;;;;;;;;;;;;;;;AAeG;AAqDH;;;;;;;;;;;AAWG;AACU,MAAA,WAAW,GAAG;AACzB;;;AAGG;AACH,IAAA,SAAS,EAAE,WAAW;AAEtB;;;AAGG;AACH,IAAA,SAAS,EAAE,WAAW;AACd,EAAC;;AC5FX;;;;;;;;;;;;;;;AAeG;AAKH;;;;;;;AAOG;MACmB,OAAO,CAAA;AAM3B;;;AAGG;AACH,IAAA,WAAA,CAAsB,IAAiB,EAAA;AACrC,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;KACzB;AACF,CAAA;AAED;;;;;;;AAOG;AACG,MAAO,eAAgB,SAAQ,OAAO,CAAA;AAC1C;;AAEG;AACH,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;KAC9B;AACF,CAAA;AAED;;;;;;;AAOG;AACG,MAAO,eAAgB,SAAQ,OAAO,CAAA;AAQ1C;;;;;;AAMG;AACH,IAAA,WAAA,CAAY,WAAmB,gBAAgB,EAAA;AAC7C,QAAA,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC;SAClC;aAAM;AACL,YAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;SAC1B;KACF;AACF;;AC3FD;;;;;;;;;;;;;;;AAeG;MAeU,SAAS,CAAA;AAKpB,IAAA,WAAA,CACS,GAAgB,EAChB,OAAgB,EACvB,YAAiD,EACjD,gBAA0D,EAAA;QAHnD,IAAG,CAAA,GAAA,GAAH,GAAG,CAAa;QAChB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;AAIvB,QAAA,MAAM,QAAQ,GAAG,gBAAgB,KAAhB,IAAA,IAAA,gBAAgB,uBAAhB,gBAAgB,CAAE,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AACpE,QAAA,MAAM,IAAI,GAAG,YAAY,KAAZ,IAAA,IAAA,YAAY,uBAAZ,YAAY,CAAE,YAAY,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AAC5D,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC;AAEjC,QAAA,IAAI,OAAO,YAAY,eAAe,EAAE;AACtC,YAAA,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SAClC;aAAM;AACL,YAAA,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;SACpB;KACF;IAED,OAAO,GAAA;AACL,QAAA,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;KAC1B;AACF;;ACxDD;;;;;;;;;;;;;;;AAeG;AAMH;;;;AAIG;AACG,MAAO,OAAQ,SAAQ,aAAa,CAAA;AACxC;;;;;;AAMG;AACH,IAAA,WAAA,CACW,IAAiB,EAC1B,OAAe,EACN,eAAiC,EAAA;;QAG1C,MAAM,OAAO,GAAG,OAAO,CAAC;AACxB,QAAA,MAAM,QAAQ,GAAG,CAAA,EAAG,OAAO,CAAI,CAAA,EAAA,IAAI,EAAE,CAAC;QACtC,MAAM,WAAW,GAAG,CAAG,EAAA,OAAO,KAAK,OAAO,CAAA,EAAA,EAAK,QAAQ,CAAA,CAAA,CAAG,CAAC;AAC3D,QAAA,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QARhB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAa;QAEjB,IAAe,CAAA,eAAA,GAAf,eAAe,CAAkB;;;;;AAY1C,QAAA,IAAI,KAAK,CAAC,iBAAiB,EAAE;;;AAG3B,YAAA,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;SACxC;;;;;QAMD,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;;AAG/C,QAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,WAAW,CAAC;KACnC;AACF;;AChED;;;;;;;;;;;;;;;AAeG;AAOH;;;;;AAKG;AACG,SAAU,wBAAwB,CAAC,OAAgB,EAAA;AACvD,IAAA,IAAI,OAAO,YAAY,eAAe,EAAE;QACtC,OAAO,CAAA,EAAG,OAAO,CAAA,SAAA,CAAW,CAAC;KAC9B;AAAM,SAAA,IAAI,OAAO,YAAY,eAAe,EAAE;AAC7C,QAAA,OAAO,GAAG,OAAO,CAAA,UAAA,EAAa,OAAO,CAAC,QAAQ,EAAE,CAAC;KAClD;SAAM;AACL,QAAA,MAAM,IAAI,OAAO,CAEf,OAAA,0BAAA,CAAA,iBAAA,EAAoB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA,CAAE,CAC1D,CAAC;KACH;AACH,CAAC;AAED;;;;AAIG;AACG,SAAU,wBAAwB,CAAC,kBAA0B,EAAA;IACjE,MAAM,eAAe,GAAG,kBAAkB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACtD,IAAA,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;QAClC,MAAM,IAAI,OAAO,CAAA,OAAA,0BAEf,CAAgD,6CAAA,EAAA,eAAe,CAAC,CAAC,CAAC,CAAG,CAAA,CAAA,CACtE,CAAC;KACH;AACD,IAAA,MAAM,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;IACvC,QAAQ,WAAW;AACjB,QAAA,KAAK,UAAU;AACb,YAAA,MAAM,QAAQ,GAAuB,eAAe,CAAC,CAAC,CAAC,CAAC;YACxD,IAAI,CAAC,QAAQ,EAAE;AACb,gBAAA,MAAM,IAAI,OAAO,CAAA,OAAA,0BAEf,kDAAkD,kBAAkB,CAAA,CAAA,CAAG,CACxE,CAAC;aACH;AACD,YAAA,OAAO,IAAI,eAAe,CAAC,QAAQ,CAAC,CAAC;AACvC,QAAA,KAAK,UAAU;YACb,OAAO,IAAI,eAAe,EAAE,CAAC;AAC/B,QAAA;AACE,YAAA,MAAM,IAAI,OAAO,CAAA,OAAA,0BAEf,wCAAwC,kBAAkB,CAAA,CAAA,CAAG,CAC9D,CAAC;KACL;AACH;;ACzEA;;;;;;;;;;;;;;;AAeG;AAQH;;;;;;;AAOG;MACmB,OAAO,CAAA;AAY3B;;;;;;;;;;;;;;;;AAgBG;IACH,WAAsB,CAAA,EAAM,EAAE,SAAiB,EAAA;;AAC7C,QAAA,IAAI,EAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,EAAE,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,MAAM,CAAA,EAAE;AAC5B,YAAA,MAAM,IAAI,OAAO,CAEf,YAAA,+BAAA,CAAA,qHAAA,CAAuH,CACxH,CAAC;SACH;AAAM,aAAA,IAAI,EAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,EAAE,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,SAAS,CAAA,EAAE;AACtC,YAAA,MAAM,IAAI,OAAO,CAEf,eAAA,kCAAA,CAAA,2HAAA,CAA6H,CAC9H,CAAC;SACH;AAAM,aAAA,IAAI,EAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,EAAE,CAAC,GAAG,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,CAAA,EAAE;AAClC,YAAA,MAAM,IAAI,OAAO,CAEf,WAAA,8BAAA,CAAA,mHAAA,CAAqH,CACtH,CAAC;SACH;aAAM;YACL,IAAI,CAAC,YAAY,GAAG;AAClB,gBAAA,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM;AAC7B,gBAAA,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS;AACjC,gBAAA,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK;AAC3B,gBAAA,8BAA8B,EAAE,EAAE,CAAC,GAAG,CAAC,8BAA8B;gBACrE,QAAQ,EAAE,EAAE,CAAC,QAAQ;gBACrB,OAAO,EAAE,EAAE,CAAC,OAAO;aACpB,CAAC;AAEF,YAAA,IAAI,oBAAoB,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,EAAE;gBACjE,MAAM,KAAK,GAAG,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC;AAC5C,gBAAA,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,MAAK;oBACxC,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AACpC,iBAAC,CAAC;aACH;AAAM,iBAAA,IAAK,EAAgB,CAAC,QAAQ,EAAE;AACrC,gBAAA,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,MAClC,EAAgB,CAAC,QAAS,CAAC,QAAQ,EAAE,CAAC;aAC1C;AAED,YAAA,IAAK,EAAgB,CAAC,IAAI,EAAE;AAC1B,gBAAA,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,MAC9B,EAAgB,CAAC,IAAK,CAAC,QAAQ,EAAE,CAAC;aACtC;AAED,YAAA,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,kBAAkB,CACrC,SAAS,EACT,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,WAAW,CACtC,CAAC;SACH;KACF;AAED;;;;;;;AAOG;AACH,IAAA,OAAO,kBAAkB,CACvB,SAAiB,EACjB,WAAwB,EAAA;AAExB,QAAA,IAAI,WAAW,KAAK,WAAW,CAAC,SAAS,EAAE;AACzC,YAAA,OAAO,OAAO,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAC;SACtD;aAAM;AACL,YAAA,OAAO,OAAO,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAC;SACtD;KACF;AAED;;AAEG;IACK,OAAO,0BAA0B,CAAC,SAAiB,EAAA;QACzD,OAAO,CAAA,OAAA,EAAU,SAAS,CAAA,CAAE,CAAC;KAC9B;AAED;;AAEG;IACK,OAAO,0BAA0B,CAAC,SAAiB,EAAA;AACzD,QAAA,IAAI,KAAa,CAAC;AAClB,QAAA,IAAI,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC3B,YAAA,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;;AAEnC,gBAAA,KAAK,GAAG,CAAA,kBAAA,EAAqB,SAAS,CAAA,CAAE,CAAC;aAC1C;iBAAM;;gBAEL,KAAK,GAAG,SAAS,CAAC;aACnB;SACF;aAAM;;AAEL,YAAA,KAAK,GAAG,CAAA,yBAAA,EAA4B,SAAS,CAAA,CAAE,CAAC;SACjD;AAED,QAAA,OAAO,KAAK,CAAC;KACd;AACF;;AC1JD;;;;;;;;;;;;;;;AAeG;AAII,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,oBAAoB,CAAC;;ACnBtD;;;;;;;;;;;;;;;AAeG;AAeH,IAAY,IAKX,CAAA;AALD,CAAA,UAAY,IAAI,EAAA;AACd,IAAA,IAAA,CAAA,kBAAA,CAAA,GAAA,iBAAoC,CAAA;AACpC,IAAA,IAAA,CAAA,yBAAA,CAAA,GAAA,uBAAiD,CAAA;AACjD,IAAA,IAAA,CAAA,cAAA,CAAA,GAAA,aAA4B,CAAA;AAC5B,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,SAAmB,CAAA;AACrB,CAAC,EALW,IAAI,KAAJ,IAAI,GAKf,EAAA,CAAA,CAAA,CAAA;MAEY,UAAU,CAAA;IACrB,WACS,CAAA,KAAa,EACb,IAAU,EACV,WAAwB,EACxB,MAAe,EACf,cAA+B,EAAA;QAJ/B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QACb,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAM;QACV,IAAW,CAAA,WAAA,GAAX,WAAW,CAAa;QACxB,IAAM,CAAA,MAAA,GAAN,MAAM,CAAS;QACf,IAAc,CAAA,cAAA,GAAd,cAAc,CAAiB;KACpC;IACJ,QAAQ,GAAA;QACN,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAClC,QAAA,GAAG,CAAC,QAAQ,GAAG,CAAI,CAAA,EAAA,IAAI,CAAC,UAAU,CAAA,CAAA,EAAI,IAAI,CAAC,SAAS,CAAI,CAAA,EAAA,IAAI,CAAC,IAAI,EAAE,CAAC;QACpE,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;AACzC,QAAA,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;KACvB;AAED,IAAA,IAAY,OAAO,GAAA;;QACjB,OAAO,CAAA,MAAA,IAAI,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAO,KAAI,gBAAgB,CAAC;KACzD;AAED,IAAA,IAAY,UAAU,GAAA;QACpB,OAAO,mBAAmB,CAAC;KAC5B;AAED,IAAA,IAAY,SAAS,GAAA;QACnB,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,YAAY,eAAe,EAAE;YACvD,OAAO,CAAA,SAAA,EAAY,IAAI,CAAC,WAAW,CAAC,OAAO,CAAA,CAAA,EAAI,IAAI,CAAC,KAAK,CAAA,CAAE,CAAC;SAC7D;aAAM,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,YAAY,eAAe,EAAE;AAC9D,YAAA,OAAO,YAAY,IAAI,CAAC,WAAW,CAAC,OAAO,cAAc,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAA,CAAA,EAAI,IAAI,CAAC,KAAK,EAAE,CAAC;SAC5G;aAAM;AACL,YAAA,MAAM,IAAI,OAAO,CAAA,OAAA,0BAEf,CAAoB,iBAAA,EAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA,CAAE,CAC/D,CAAC;SACH;KACF;AAED,IAAA,IAAY,WAAW,GAAA;AACrB,QAAA,MAAM,MAAM,GAAG,IAAI,eAAe,EAAE,CAAC;AACrC,QAAA,IAAI,IAAI,CAAC,MAAM,EAAE;AACf,YAAA,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAC1B;AAED,QAAA,OAAO,MAAM,CAAC;KACf;AACF,CAAA;AAED;;AAEG;AACH,SAAS,gBAAgB,GAAA;IACvB,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,WAAW,CAAC,IAAI,CAAC,CAAA,EAAG,YAAY,CAAI,CAAA,EAAA,eAAe,CAAE,CAAA,CAAC,CAAC;AACvD,IAAA,WAAW,CAAC,IAAI,CAAC,QAAQ,eAAe,CAAA,CAAE,CAAC,CAAC;AAC5C,IAAA,OAAO,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/B,CAAC;AAEM,eAAe,UAAU,CAAC,GAAe,EAAA;AAC9C,IAAA,MAAM,OAAO,GAAG,IAAI,OAAO,EAAE,CAAC;AAC9B,IAAA,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;IACnD,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,CAAC,CAAC;IACxD,OAAO,CAAC,MAAM,CAAC,gBAAgB,EAAE,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACzD,IAAA,IAAI,GAAG,CAAC,WAAW,CAAC,8BAA8B,EAAE;QAClD,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KAC3D;AACD,IAAA,IAAI,GAAG,CAAC,WAAW,CAAC,gBAAgB,EAAE;QACpC,MAAM,aAAa,GAAG,MAAM,GAAG,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC;QAC/D,IAAI,aAAa,EAAE;YACjB,OAAO,CAAC,MAAM,CAAC,qBAAqB,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3D,YAAA,IAAI,aAAa,CAAC,KAAK,EAAE;gBACvB,MAAM,CAAC,IAAI,CACT,CAA6C,0CAAA,EAAA,aAAa,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA,CAC3E,CAAC;aACH;SACF;KACF;AAED,IAAA,IAAI,GAAG,CAAC,WAAW,CAAC,YAAY,EAAE;QAChC,MAAM,SAAS,GAAG,MAAM,GAAG,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;QACvD,IAAI,SAAS,EAAE;YACb,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,CAAY,SAAA,EAAA,SAAS,CAAC,WAAW,CAAE,CAAA,CAAC,CAAC;SACtE;KACF;AAED,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAEM,eAAe,gBAAgB,CACpC,KAAa,EACb,IAAU,EACV,WAAwB,EACxB,MAAe,EACf,IAAY,EACZ,cAA+B,EAAA;AAE/B,IAAA,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;IAC7E,OAAO;AACL,QAAA,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAE;AACnB,QAAA,YAAY,EAAE;AACZ,YAAA,MAAM,EAAE,MAAM;AACd,YAAA,OAAO,EAAE,MAAM,UAAU,CAAC,GAAG,CAAC;YAC9B,IAAI;AACL,SAAA;KACF,CAAC;AACJ,CAAC;AAEM,eAAe,WAAW,CAC/B,KAAa,EACb,IAAU,EACV,WAAwB,EACxB,MAAe,EACf,IAAY,EACZ,cAA+B,EAAA;AAE/B,IAAA,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;AAC7E,IAAA,IAAI,QAAQ,CAAC;AACb,IAAA,IAAI,cAA4D,CAAC;AACjE,IAAA,IAAI;AACF,QAAA,MAAM,OAAO,GAAG,MAAM,gBAAgB,CACpC,KAAK,EACL,IAAI,EACJ,WAAW,EACX,MAAM,EACN,IAAI,EACJ,cAAc,CACf,CAAC;;AAEF,QAAA,MAAM,aAAa,GACjB,CAAA,cAAc,KAAA,IAAA,IAAd,cAAc,KAAd,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,cAAc,CAAE,OAAO,KAAI,IAAI,IAAI,cAAc,CAAC,OAAO,IAAI,CAAC;cAC1D,cAAc,CAAC,OAAO;cACtB,wBAAwB,CAAC;AAC/B,QAAA,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;AAC9C,QAAA,cAAc,GAAG,UAAU,CAAC,MAAM,eAAe,CAAC,KAAK,EAAE,EAAE,aAAa,CAAC,CAAC;QAC1E,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;AAErD,QAAA,QAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,YAAY,CAAC,CAAC;AAC1D,QAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;YAChB,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB,YAAA,IAAI,YAAY,CAAC;AACjB,YAAA,IAAI;AACF,gBAAA,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACnC,gBAAA,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAC7B,gBAAA,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AACtB,oBAAA,OAAO,IAAI,CAAA,CAAA,EAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;AACpD,oBAAA,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;iBACnC;aACF;YAAC,OAAO,CAAC,EAAE;;aAEX;AACD,YAAA,IACE,QAAQ,CAAC,MAAM,KAAK,GAAG;AACvB,gBAAA,YAAY,CAAC,IAAI,CACf,CAAC,MAAoB,KAAK,MAAM,CAAC,MAAM,KAAK,kBAAkB,CAC/D;AACD,gBAAA,YAAY,CAAC,IAAI,CAAC,CAAC,MAAoB,KAAI;;AACzC,oBAAA,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GACE,MAAM,CAAC,KACR,MAAG,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAC,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,WAAW,CAAC,QAAQ,CAC1B,0CAA0C,CAC3C,CAAA;AAAA,iBAAA,CACF,EACD;gBACA,MAAM,IAAI,OAAO,CAAA,iBAAA,oCAEf,CAA+C,6CAAA,CAAA;oBAC7C,CAAgE,8DAAA,CAAA;oBAChE,CAAqE,mEAAA,CAAA;AACrE,oBAAA,CAAA,+CAAA,EAAkD,GAAG,CAAC,WAAW,CAAC,OAAO,CAAU,QAAA,CAAA;oBACnF,CAAgE,8DAAA,CAAA;oBAChE,CAAoE,kEAAA,CAAA;AACpE,oBAAA,CAAA,WAAA,CAAa,EACf;oBACE,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,YAAY;AACb,iBAAA,CACF,CAAC;aACH;AACD,YAAA,MAAM,IAAI,OAAO,CAAA,aAAA,gCAEf,CAAuB,oBAAA,EAAA,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAA,CAAA,EAAI,QAAQ,CAAC,UAAU,CAAK,EAAA,EAAA,OAAO,EAAE,EACpF;gBACE,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;gBAC/B,YAAY;AACb,aAAA,CACF,CAAC;SACH;KACF;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,GAAG,GAAG,CAAU,CAAC;QACrB,IACG,CAAa,CAAC,IAAI,KAA4B,aAAA;YAC9C,CAAa,CAAC,IAAI,KAAgC,iBAAA;YACnD,CAAC,YAAY,KAAK,EAClB;AACA,YAAA,GAAG,GAAG,IAAI,OAAO,CAAA,OAAA,0BAEf,uBAAuB,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,OAAO,CAAA,CAAE,CACtD,CAAC;AACF,YAAA,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;SACrB;AAED,QAAA,MAAM,GAAG,CAAC;KACX;YAAS;QACR,IAAI,cAAc,EAAE;YAClB,YAAY,CAAC,cAAc,CAAC,CAAC;SAC9B;KACF;AACD,IAAA,OAAO,QAAQ,CAAC;AAClB;;ACrPA;;;;;;;;;;;;;;;AAeG;AAiBH;;;AAGG;AACG,SAAU,6BAA6B,CAC3C,QAAiC,EAAA;AAEjC;;;;;AAKG;AACH,IAAA,IAAI,QAAQ,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;QAC1E,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;KAClC;AAED,IAAA,MAAM,mBAAmB,GAAG,UAAU,CAAC,QAAQ,CAAC,CAAC;AACjD,IAAA,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAED;;;AAGG;AACG,SAAU,UAAU,CACxB,QAAiC,EAAA;AAEhC,IAAA,QAA4C,CAAC,IAAI,GAAG,MAAK;AACxD,QAAA,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClC,MAAM,CAAC,IAAI,CACT,CAAA,kBAAA,EAAqB,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAG,CAAA,CAAA;oBAChD,CAA4D,0DAAA,CAAA;AAC5D,oBAAA,CAAA,gEAAA,CAAkE,CACrE,CAAC;aACH;YACD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC9C,MAAM,IAAI,OAAO,CAEf,gBAAA,mCAAA,CAAA,gBAAA,EAAmB,uBAAuB,CACxC,QAAQ,CACT,CAAA,wCAAA,CAA0C,EAC3C;oBACE,QAAQ;AACT,iBAAA,CACF,CAAC;aACH;AACD,YAAA,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC1B;AAAM,aAAA,IAAI,QAAQ,CAAC,cAAc,EAAE;YAClC,MAAM,IAAI,OAAO,CAEf,gBAAA,mCAAA,CAAA,oBAAA,EAAuB,uBAAuB,CAAC,QAAQ,CAAC,CAAA,CAAE,EAC1D;gBACE,QAAQ;AACT,aAAA,CACF,CAAC;SACH;AACD,QAAA,OAAO,EAAE,CAAC;AACZ,KAAC,CAAC;AACD,IAAA,QAA4C,CAAC,eAAe,GAAG,MAEhD;AACd,QAAA,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClC,MAAM,CAAC,IAAI,CACT,CAAA,kBAAA,EAAqB,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAG,CAAA,CAAA;oBAChD,CAA4D,0DAAA,CAAA;AAC5D,oBAAA,CAAA,gEAAA,CAAkE,CACrE,CAAC;aACH;YACD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC9C,MAAM,IAAI,OAAO,CAEf,gBAAA,mCAAA,CAAA,gBAAA,EAAmB,uBAAuB,CACxC,QAAQ,CACT,CAAA,wCAAA,CAA0C,EAC3C;oBACE,QAAQ;AACT,iBAAA,CACF,CAAC;aACH;AACD,YAAA,OAAO,kBAAkB,CAAC,QAAQ,CAAC,CAAC;SACrC;AAAM,aAAA,IAAI,QAAQ,CAAC,cAAc,EAAE;YAClC,MAAM,IAAI,OAAO,CAEf,gBAAA,mCAAA,CAAA,oBAAA,EAAuB,uBAAuB,CAAC,QAAQ,CAAC,CAAA,CAAE,EAC1D;gBACE,QAAQ;AACT,aAAA,CACF,CAAC;SACH;AACD,QAAA,OAAO,SAAS,CAAC;AACnB,KAAC,CAAC;AACD,IAAA,QAA4C,CAAC,aAAa,GAAG,MAAK;AACjE,QAAA,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;gBAClC,MAAM,CAAC,IAAI,CACT,CAAA,kBAAA,EAAqB,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAG,CAAA,CAAA;oBAChD,CAAsE,oEAAA,CAAA;AACtE,oBAAA,CAAA,gEAAA,CAAkE,CACrE,CAAC;aACH;YACD,IAAI,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;gBAC9C,MAAM,IAAI,OAAO,CAEf,gBAAA,mCAAA,CAAA,gBAAA,EAAmB,uBAAuB,CACxC,QAAQ,CACT,CAAA,wCAAA,CAA0C,EAC3C;oBACE,QAAQ;AACT,iBAAA,CACF,CAAC;aACH;AACD,YAAA,OAAO,gBAAgB,CAAC,QAAQ,CAAC,CAAC;SACnC;AAAM,aAAA,IAAI,QAAQ,CAAC,cAAc,EAAE;YAClC,MAAM,IAAI,OAAO,CAEf,gBAAA,mCAAA,CAAA,6BAAA,EAAgC,uBAAuB,CAAC,QAAQ,CAAC,CAAA,CAAE,EACnE;gBACE,QAAQ;AACT,aAAA,CACF,CAAC;SACH;AACD,QAAA,OAAO,SAAS,CAAC;AACnB,KAAC,CAAC;AACF,IAAA,OAAO,QAA2C,CAAC;AACrD,CAAC;AAED;;AAEG;AACG,SAAU,OAAO,CAAC,QAAiC,EAAA;;IACvD,MAAM,WAAW,GAAG,EAAE,CAAC;AACvB,IAAA,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,EAAE;AAC3C,QAAA,KAAK,MAAM,IAAI,IAAI,CAAA,EAAA,GAAA,MAAA,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAE,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,EAAE;AAC1D,YAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,gBAAA,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC7B;SACF;KACF;AACD,IAAA,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1B,QAAA,OAAO,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KAC7B;SAAM;AACL,QAAA,OAAO,EAAE,CAAC;KACX;AACH,CAAC;AAED;;AAEG;AACG,SAAU,gBAAgB,CAC9B,QAAiC,EAAA;;IAEjC,MAAM,aAAa,GAAmB,EAAE,CAAC;AACzC,IAAA,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,EAAE;AAC3C,QAAA,KAAK,MAAM,IAAI,IAAI,CAAA,EAAA,GAAA,MAAA,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAE,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,EAAE;AAC1D,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACvC;SACF;KACF;AACD,IAAA,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,QAAA,OAAO,aAAa,CAAC;KACtB;SAAM;AACL,QAAA,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED;;;;AAIG;AACG,SAAU,kBAAkB,CAChC,QAAiC,EAAA;;IAEjC,MAAM,IAAI,GAAqB,EAAE,CAAC;AAElC,IAAA,IAAI,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAA,CAAE,OAAO,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,KAAK,EAAE;AAC3C,QAAA,KAAK,MAAM,IAAI,IAAI,CAAA,EAAA,GAAA,MAAA,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAE,CAAA,OAAO,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAK,EAAE;AAC1D,YAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,gBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACjB;SACF;KACF;AAED,IAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACnB,QAAA,OAAO,IAAI,CAAC;KACb;SAAM;AACL,QAAA,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED,MAAM,gBAAgB,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;AAExE,SAAS,kBAAkB,CAAC,SAAmC,EAAA;AAC7D,IAAA,QACE,CAAC,CAAC,SAAS,CAAC,YAAY;QACxB,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,EACjD;AACJ,CAAC;AAEK,SAAU,uBAAuB,CACrC,QAAiC,EAAA;;IAEjC,IAAI,OAAO,GAAG,EAAE,CAAC;AACjB,IAAA,IACE,CAAC,CAAC,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC;QACzD,QAAQ,CAAC,cAAc,EACvB;QACA,OAAO,IAAI,sBAAsB,CAAC;AAClC,QAAA,IAAI,MAAA,QAAQ,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAW,EAAE;YACxC,OAAO,IAAI,WAAW,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC;SAC7D;AACD,QAAA,IAAI,MAAA,QAAQ,CAAC,cAAc,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,kBAAkB,EAAE;YAC/C,OAAO,IAAI,KAAK,QAAQ,CAAC,cAAc,CAAC,kBAAkB,EAAE,CAAC;SAC9D;KACF;SAAM,IAAI,CAAA,EAAA,GAAA,QAAQ,CAAC,UAAU,0CAAG,CAAC,CAAC,EAAE;QACnC,MAAM,cAAc,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC9C,QAAA,IAAI,kBAAkB,CAAC,cAAc,CAAC,EAAE;AACtC,YAAA,OAAO,IAAI,CAAgC,6BAAA,EAAA,cAAc,CAAC,YAAY,EAAE,CAAC;AACzE,YAAA,IAAI,cAAc,CAAC,aAAa,EAAE;AAChC,gBAAA,OAAO,IAAI,CAAK,EAAA,EAAA,cAAc,CAAC,aAAa,EAAE,CAAC;aAChD;SACF;KACF;AACD,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;AAMG;AACI,eAAe,qBAAqB,CAEzC,QAAkB,EAAA;;AAClB,IAAA,MAAM,YAAY,GAA2B,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAEnE,MAAM,MAAM,GAAQ,EAAE,CAAC;IACvB,IAAI,cAAc,GAAuB,SAAS,CAAC;;AAGnD,IAAA,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI,CAAA,CAAA,EAAA,GAAA,YAAY,CAAC,WAAW,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAM,MAAK,CAAC,EAAE;AACvE,QAAA,MAAM,IAAI,OAAO,CAEf,gBAAA,mCAAA,wKAAwK,CACzK,CAAC;KACH;AAED,IAAA,KAAK,MAAM,UAAU,IAAI,YAAY,CAAC,WAAW,EAAE;AACjD,QAAA,IAAI,UAAU,CAAC,iBAAiB,EAAE;AAChC,YAAA,cAAc,GAAG,UAAU,CAAC,iBAAiB,CAAC;SAC/C;aAAM,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,kBAAkB,EAAE;YAC/D,MAAM,CAAC,IAAI,CAAC;gBACV,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,kBAAkB,EAAE,UAAU,CAAC,kBAAkB;AAC7C,aAAA,CAAC,CAAC;SACT;aAAM,IAAI,UAAU,CAAC,QAAQ,IAAI,UAAU,CAAC,MAAM,EAAE;YACnD,MAAM,CAAC,IAAI,CAAC;gBACV,QAAQ,EAAE,UAAU,CAAC,QAAQ;gBAC7B,MAAM,EAAE,UAAU,CAAC,MAAM;AACrB,aAAA,CAAC,CAAC;SACT;aAAM;AACL,YAAA,MAAM,IAAI,OAAO,CAEf,gBAAA,mCAAA,CAAA,gEAAA,EAAmE,IAAI,CAAC,SAAS,CAC/E,YAAY,CACb,CAAE,CAAA,CACJ,CAAC;SACH;KACF;AAED,IAAA,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,CAAC;AACpC;;ACrTA;;;;;;;;;;;;;;;AAeG;AAsBH;;;;;;;;;;AAUG;AAEH;;;;;;;;;AASG;AACG,SAAU,yBAAyB,CACvC,sBAA8C,EAAA;;IAE9C,CAAA,EAAA,GAAA,sBAAsB,CAAC,cAAc,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAE,OAAO,CAAC,aAAa,IAAG;AAC7D,QAAA,IAAI,aAAa,CAAC,MAAM,EAAE;AACxB,YAAA,MAAM,IAAI,OAAO,CAEf,aAAA,gCAAA,qGAAqG,CACtG,CAAC;SACH;AACH,KAAC,CAAC,CAAC;AAEH,IAAA,IAAI,MAAA,sBAAsB,CAAC,gBAAgB,MAAE,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAI,EAAE;AACjD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAC5B,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,CAC7C,CAAC;QAEF,IAAI,WAAW,KAAK,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,EAAE;AAChE,YAAA,MAAM,CAAC,IAAI,CACT,gIAAgI,CACjI,CAAC;AACF,YAAA,sBAAsB,CAAC,gBAAgB,CAAC,IAAI,GAAG,WAAW,CAAC;SAC5D;KACF;AAED,IAAA,OAAO,sBAAsB,CAAC;AAChC,CAAC;AAED;;;;;;;;AAQG;AACG,SAAU,0BAA0B,CACxC,gBAAiD,EAAA;AAEjD,IAAA,MAAM,uBAAuB,GAAG;QAC9B,UAAU,EAAE,gBAAgB,CAAC,UAAU;AACrC,cAAE,4BAA4B,CAAC,gBAAgB,CAAC,UAAU,CAAC;AAC3D,cAAE,SAAS;QACb,MAAM,EAAE,gBAAgB,CAAC,cAAc;AACrC,cAAE,iBAAiB,CAAC,gBAAgB,CAAC,cAAc,CAAC;AACpD,cAAE,SAAS;QACb,aAAa,EAAE,gBAAgB,CAAC,aAAa;KAC9C,CAAC;AAEF,IAAA,OAAO,uBAAuB,CAAC;AACjC,CAAC;AAED;;;;;;;;AAQG;AACa,SAAA,qBAAqB,CACnC,kBAAsC,EACtC,KAAa,EAAA;AAEb,IAAA,MAAM,wBAAwB,GAA+B;AAC3D,QAAA,sBAAsB,EACpB,MAAA,CAAA,MAAA,CAAA,EAAA,KAAK,EACF,EAAA,kBAAkB,CACtB;KACF,CAAC;AAEF,IAAA,OAAO,wBAAwB,CAAC;AAClC,CAAC;AAED;;;;;;;;;;AAUG;AACG,SAAU,4BAA4B,CAC1C,UAA8C,EAAA;IAE9C,MAAM,gBAAgB,GAA+B,EAAE,CAAC;AACxD,IAAA,IAAI,mBAAmC,CAAC;IACxC,IAAI,gBAAgB,EAAE;AACpB,QAAA,UAAU,CAAC,OAAO,CAAC,SAAS,IAAG;;;AAE7B,YAAA,IAAI,gBAA8C,CAAC;AACnD,YAAA,IAAI,SAAS,CAAC,gBAAgB,EAAE;AAC9B,gBAAA,gBAAgB,GAAG;AACjB,oBAAA,SAAS,EAAE,SAAS,CAAC,gBAAgB,CAAC,eAAe;iBACtD,CAAC;aACH;;AAGD,YAAA,IAAI,SAAS,CAAC,aAAa,EAAE;gBAC3B,mBAAmB,GAAG,SAAS,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,IAAG;;AAC/D,oBAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,YAAY,CAAA,EAAA,EACf,QAAQ,EACN,CAAA,EAAA,GAAA,YAAY,CAAC,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,YAAY,CAAC,yBAAyB,EACjE,gBAAgB,EAAE,CAAA,EAAA,GAAA,YAAY,CAAC,gBAAgB,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,CAAC,EACpD,aAAa,EAAE,CAAA,EAAA,GAAA,YAAY,CAAC,aAAa,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAI,CAAC,EAC9C,CAAA,CAAA;AACJ,iBAAC,CAAC,CAAC;aACJ;;;;YAKD,IACE,CAAA,EAAA,GAAA,SAAS,CAAC,OAAO,0CAAE,KAAK,CAAC,IAAI,CAC3B,IAAI,IAAK,IAAuB,aAAvB,IAAI,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAJ,IAAI,CAAqB,aAAa,CAChD,EACD;AACA,gBAAA,MAAM,IAAI,OAAO,CAEf,aAAA,gCAAA,+FAA+F,CAChG,CAAC;aACH;AAED,YAAA,MAAM,eAAe,GAAG;gBACtB,KAAK,EAAE,SAAS,CAAC,KAAK;gBACtB,OAAO,EAAE,SAAS,CAAC,OAAO;gBAC1B,YAAY,EAAE,SAAS,CAAC,YAAY;gBACpC,aAAa,EAAE,SAAS,CAAC,aAAa;AACtC,gBAAA,aAAa,EAAE,mBAAmB;gBAClC,gBAAgB;gBAChB,iBAAiB,EAAE,SAAS,CAAC,iBAAiB;aAC/C,CAAC;AACF,YAAA,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;AACzC,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAEK,SAAU,iBAAiB,CAC/B,cAA8B,EAAA;;IAG9B,MAAM,mBAAmB,GAAmB,EAAE,CAAC;AAC/C,IAAA,cAAc,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,IAAG;;QAClD,mBAAmB,CAAC,IAAI,CAAC;YACvB,QAAQ,EAAE,YAAY,CAAC,QAAQ;YAC/B,WAAW,EAAE,YAAY,CAAC,WAAW;YACrC,QAAQ,EAAE,MAAA,YAAY,CAAC,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,YAAY,CAAC,yBAAyB;AACzE,YAAA,gBAAgB,EAAE,CAAA,EAAA,GAAA,YAAY,CAAC,gBAAgB,mCAAI,CAAC;AACpD,YAAA,aAAa,EAAE,CAAA,EAAA,GAAA,YAAY,CAAC,aAAa,mCAAI,CAAC;YAC9C,OAAO,EAAE,YAAY,CAAC,OAAO;AAC9B,SAAA,CAAC,CAAC;AACL,KAAC,CAAC,CAAC;AAEH,IAAA,MAAM,oBAAoB,GAAmB;QAC3C,WAAW,EAAE,cAAc,CAAC,WAAW;AACvC,QAAA,aAAa,EAAE,mBAAmB;QAClC,kBAAkB,EAAE,cAAc,CAAC,kBAAkB;KACtD,CAAC;AACF,IAAA,OAAO,oBAAoB,CAAC;AAC9B;;AClOA;;;;;;;;;;;;;;;AAeG;AAiBH,MAAM,cAAc,GAAG,oCAAoC,CAAC;AAE5D;;;;;;;AAOG;AACa,SAAA,aAAa,CAC3B,QAAkB,EAClB,WAAwB,EAAA;IAExB,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAK,CAAC,WAAW,CAC5C,IAAI,iBAAiB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAC/C,CAAC;AACF,IAAA,MAAM,cAAc,GAClB,iBAAiB,CAA0B,WAAW,CAAC,CAAC;IAC1D,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,cAAc,CAAC,GAAG,EAAE,CAAC;IAChD,OAAO;AACL,QAAA,MAAM,EAAE,wBAAwB,CAAC,OAAO,EAAE,WAAW,CAAC;AACtD,QAAA,QAAQ,EAAE,kBAAkB,CAAC,OAAO,EAAE,WAAW,CAAC;KACnD,CAAC;AACJ,CAAC;AAED,eAAe,kBAAkB,CAC/B,MAA+C,EAC/C,WAAwB,EAAA;IAExB,MAAM,YAAY,GAA8B,EAAE,CAAC;AACnD,IAAA,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IAClC,OAAO,IAAI,EAAE;QACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QAC5C,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,uBAAuB,GAAG,kBAAkB,CAAC,YAAY,CAAC,CAAC;YAC/D,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,SAAS,EAAE;AAC7D,gBAAA,uBAAuB,GAAGA,0BAAyC,CACjE,uBAA0D,CAC3D,CAAC;aACH;AACD,YAAA,OAAO,6BAA6B,CAAC,uBAAuB,CAAC,CAAC;SAC/D;AAED,QAAA,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC1B;AACH,CAAC;AAED,SAAgB,wBAAwB,CACtC,MAA+C,EAC/C,WAAwB,EAAA;;AAExB,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QAClC,OAAO,IAAI,EAAE;AACX,YAAA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,OAAA,CAAA,MAAM,CAAC,IAAI,EAAE,CAAA,CAAC;YAC5C,IAAI,IAAI,EAAE;gBACR,MAAM;aACP;AAED,YAAA,IAAI,gBAAiD,CAAC;YACtD,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,SAAS,EAAE;gBAC7D,gBAAgB,GAAG,6BAA6B,CAC9CA,0BAAyC,CACvC,KAAwC,CACzC,CACF,CAAC;aACH;iBAAM;AACL,gBAAA,gBAAgB,GAAG,6BAA6B,CAAC,KAAK,CAAC,CAAC;aACzD;YAED,MAAM,MAAA,OAAA,CAAA,gBAAgB,CAAA,CAAC;SACxB;KACF,CAAA,CAAA;AAAA,CAAA;AAED;;;;AAIG;AACG,SAAU,iBAAiB,CAC/B,WAAmC,EAAA;AAEnC,IAAA,MAAM,MAAM,GAAG,WAAW,CAAC,SAAS,EAAE,CAAC;AACvC,IAAA,MAAM,MAAM,GAAG,IAAI,cAAc,CAAI;AACnC,QAAA,KAAK,CAAC,UAAU,EAAA;YACd,IAAI,WAAW,GAAG,EAAE,CAAC;YACrB,OAAO,IAAI,EAAE,CAAC;AACd,YAAA,SAAS,IAAI,GAAA;AACX,gBAAA,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,KAAI;oBAC5C,IAAI,IAAI,EAAE;AACR,wBAAA,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE;4BACtB,UAAU,CAAC,KAAK,CACd,IAAI,OAAO,CAA2B,cAAA,iCAAA,wBAAwB,CAAC,CAChE,CAAC;4BACF,OAAO;yBACR;wBACD,UAAU,CAAC,KAAK,EAAE,CAAC;wBACnB,OAAO;qBACR;oBAED,WAAW,IAAI,KAAK,CAAC;oBACrB,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC9C,oBAAA,IAAI,cAAiB,CAAC;oBACtB,OAAO,KAAK,EAAE;AACZ,wBAAA,IAAI;4BACF,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;yBACvC;wBAAC,OAAO,CAAC,EAAE;AACV,4BAAA,UAAU,CAAC,KAAK,CACd,IAAI,OAAO,CAET,cAAA,iCAAA,CAAA,8BAAA,EAAiC,KAAK,CAAC,CAAC,CAAC,CAAE,CAAA,CAC5C,CACF,CAAC;4BACF,OAAO;yBACR;AACD,wBAAA,UAAU,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACnC,wBAAA,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;AACrD,wBAAA,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;qBAC3C;oBACD,OAAO,IAAI,EAAE,CAAC;AAChB,iBAAC,CAAC,CAAC;aACJ;SACF;AACF,KAAA,CAAC,CAAC;AACH,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;AAGG;AACG,SAAU,kBAAkB,CAChC,SAAoC,EAAA;IAEpC,MAAM,YAAY,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACrD,IAAA,MAAM,kBAAkB,GAA4B;AAClD,QAAA,cAAc,EAAE,YAAY,KAAA,IAAA,IAAZ,YAAY,KAAZ,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,YAAY,CAAE,cAAc;KAC7C,CAAC;AACF,IAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AAChC,QAAA,IAAI,QAAQ,CAAC,UAAU,EAAE;AACvB,YAAA,KAAK,MAAM,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;;;AAG3C,gBAAA,MAAM,CAAC,GAAG,SAAS,CAAC,KAAK,IAAI,CAAC,CAAC;AAC/B,gBAAA,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE;AAClC,oBAAA,kBAAkB,CAAC,UAAU,GAAG,EAAE,CAAC;iBACpC;gBACD,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;AACrC,oBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG;wBACjC,KAAK,EAAE,SAAS,CAAC,KAAK;qBACK,CAAC;iBAC/B;;AAED,gBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,gBAAgB;oBAC/C,SAAS,CAAC,gBAAgB,CAAC;gBAC7B,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;AACvE,gBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa;oBAC5C,SAAS,CAAC,aAAa,CAAC;AAC1B,gBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,aAAa;oBAC5C,SAAS,CAAC,aAAa,CAAC;AAE1B;;;AAGG;gBACH,IAAI,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE;oBAChD,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;AAC7C,wBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG;AACzC,4BAAA,IAAI,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,IAAI,MAAM;AACtC,4BAAA,KAAK,EAAE,EAAE;yBACV,CAAC;qBACH;oBACD,MAAM,OAAO,GAAkB,EAAE,CAAC;oBAClC,KAAK,MAAM,IAAI,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE;AAC1C,wBAAA,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE;;;;AAI3B,4BAAA,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,EAAE;gCACpB,SAAS;6BACV;AACD,4BAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;yBAC1B;AACD,wBAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,4BAAA,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;yBAC1C;wBACD,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;4BACrC,MAAM,IAAI,OAAO,CAAA,iBAAA,oCAEf,oFAAoF;AAClF,gCAAA,2CAA2C,CAC9C,CAAC;yBACH;AACD,wBAAA,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CACjD,OAAe,CAChB,CAAC;qBACH;iBACF;aACF;SACF;KACF;AACD,IAAA,OAAO,kBAAkB,CAAC;AAC5B;;AC1OA;;;;;;;;;;;;;;;AAeG;AAgBI,eAAe,qBAAqB,CACzC,WAAwB,EACxB,KAAa,EACb,MAA8B,EAC9B,cAA+B,EAAA;IAE/B,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,SAAS,EAAE;AAC7D,QAAA,MAAM,GAAGC,yBAAwC,CAAC,MAAM,CAAC,CAAC;KAC3D;IACD,MAAM,QAAQ,GAAG,MAAM,WAAW,CAChC,KAAK,EACL,IAAI,CAAC,uBAAuB,EAC5B,WAAW;AACX,iBAAa,IAAI,EACjB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EACtB,cAAc,CACf,CAAC;IACF,OAAO,aAAa,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;AAC9C,CAAC;AAEM,eAAe,eAAe,CACnC,WAAwB,EACxB,KAAa,EACb,MAA8B,EAC9B,cAA+B,EAAA;IAE/B,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,SAAS,EAAE;AAC7D,QAAA,MAAM,GAAGA,yBAAwC,CAAC,MAAM,CAAC,CAAC;KAC3D;IACD,MAAM,QAAQ,GAAG,MAAM,WAAW,CAChC,KAAK,EACL,IAAI,CAAC,gBAAgB,EACrB,WAAW;AACX,iBAAa,KAAK,EAClB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EACtB,cAAc,CACf,CAAC;IACF,MAAM,uBAAuB,GAAG,MAAM,8BAA8B,CAClE,QAAQ,EACR,WAAW,CACZ,CAAC;AACF,IAAA,MAAM,gBAAgB,GAAG,6BAA6B,CACpD,uBAAuB,CACxB,CAAC;IACF,OAAO;AACL,QAAA,QAAQ,EAAE,gBAAgB;KAC3B,CAAC;AACJ,CAAC;AAED,eAAe,8BAA8B,CAC3C,QAAkB,EAClB,WAAwB,EAAA;AAExB,IAAA,MAAM,YAAY,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;IAC3C,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,SAAS,EAAE;AAC7D,QAAA,OAAOD,0BAAyC,CAAC,YAAY,CAAC,CAAC;KAChE;SAAM;AACL,QAAA,OAAO,YAAY,CAAC;KACrB;AACH;;AC1FA;;;;;;;;;;;;;;;AAeG;AAMG,SAAU,uBAAuB,CACrC,KAA+B,EAAA;;AAG/B,IAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,QAAA,OAAO,SAAS,CAAC;KAClB;AAAM,SAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACpC,QAAA,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAa,CAAC;KAChE;AAAM,SAAA,IAAK,KAAc,CAAC,IAAI,EAAE;QAC/B,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,KAAa,CAAC,EAAE,CAAC;KACnD;AAAM,SAAA,IAAK,KAAiB,CAAC,KAAK,EAAE;AACnC,QAAA,IAAI,CAAE,KAAiB,CAAC,IAAI,EAAE;YAC5B,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAG,KAAiB,CAAC,KAAK,EAAE,CAAC;SAC5D;aAAM;AACL,YAAA,OAAO,KAAgB,CAAC;SACzB;KACF;AACH,CAAC;AAEK,SAAU,gBAAgB,CAC9B,OAAsC,EAAA;IAEtC,IAAI,QAAQ,GAAW,EAAE,CAAC;AAC1B,IAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,QAAQ,GAAG,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;KAChC;SAAM;AACL,QAAA,KAAK,MAAM,YAAY,IAAI,OAAO,EAAE;AAClC,YAAA,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;gBACpC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;aACvC;iBAAM;AACL,gBAAA,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAC7B;SACF;KACF;AACD,IAAA,OAAO,8CAA8C,CAAC,QAAQ,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;;AAOG;AACH,SAAS,8CAA8C,CACrD,KAAa,EAAA;IAEb,MAAM,WAAW,GAAY,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IACzD,MAAM,eAAe,GAAY,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IACjE,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,IAAI,kBAAkB,GAAG,KAAK,CAAC;AAC/B,IAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,QAAA,IAAI,kBAAkB,IAAI,IAAI,EAAE;AAC9B,YAAA,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjC,kBAAkB,GAAG,IAAI,CAAC;SAC3B;aAAM;AACL,YAAA,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC7B,cAAc,GAAG,IAAI,CAAC;SACvB;KACF;AAED,IAAA,IAAI,cAAc,IAAI,kBAAkB,EAAE;AACxC,QAAA,MAAM,IAAI,OAAO,CAEf,iBAAA,oCAAA,4HAA4H,CAC7H,CAAC;KACH;AAED,IAAA,IAAI,CAAC,cAAc,IAAI,CAAC,kBAAkB,EAAE;AAC1C,QAAA,MAAM,IAAI,OAAO,CAEf,iBAAA,oCAAA,kDAAkD,CACnD,CAAC;KACH;IAED,IAAI,cAAc,EAAE;AAClB,QAAA,OAAO,WAAW,CAAC;KACpB;AAED,IAAA,OAAO,eAAe,CAAC;AACzB,CAAC;AAEK,SAAU,0BAA0B,CACxC,MAA8D,EAAA;AAE9D,IAAA,IAAI,gBAAwC,CAAC;AAC7C,IAAA,IAAK,MAAiC,CAAC,QAAQ,EAAE;QAC/C,gBAAgB,GAAG,MAAgC,CAAC;KACrD;SAAM;;AAEL,QAAA,MAAM,OAAO,GAAG,gBAAgB,CAAC,MAAuC,CAAC,CAAC;QAC1E,gBAAgB,GAAG,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;KAC5C;AACD,IAAA,IAAK,MAAiC,CAAC,iBAAiB,EAAE;QACxD,gBAAgB,CAAC,iBAAiB,GAAG,uBAAuB,CACzD,MAAiC,CAAC,iBAAiB,CACrD,CAAC;KACH;AACD,IAAA,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;;;;AAKG;AACG,SAAU,wBAAwB,CACtC,MAAc,EACd,EACE,MAAM,EACN,WAAW,EACX,YAAY,EACZ,cAAc,GAAG,CAAC,EAClB,cAAc,EACd,WAAW,EACX,iBAAiB,EACjB,iBAAiB,EACM,EAAA;;AAGzB,IAAA,MAAM,IAAI,GAAuB;AAC/B,QAAA,SAAS,EAAE;AACT,YAAA;gBACE,MAAM;AACP,aAAA;AACF,SAAA;AACD,QAAA,UAAU,EAAE;AACV,YAAA,UAAU,EAAE,MAAM;YAClB,cAAc;AACd,YAAA,WAAW,EAAE,cAAc;YAC3B,WAAW;AACX,YAAA,aAAa,EAAE,WAAW;YAC1B,YAAY;YACZ,iBAAiB;AACjB,YAAA,gBAAgB,EAAE,iBAAiB;AACnC,YAAA,gBAAgB,EAAE,IAAI;AACvB,SAAA;KACF,CAAC;AACF,IAAA,OAAO,IAAI,CAAC;AACd;;AClKA;;;;;;;;;;;;;;;AAeG;AAKH;AAEA,MAAM,iBAAiB,GAAsB;IAC3C,MAAM;IACN,YAAY;IACZ,cAAc;IACd,kBAAkB;CACnB,CAAC;AAEF,MAAM,oBAAoB,GAAyC;AACjE,IAAA,IAAI,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;IAC5B,QAAQ,EAAE,CAAC,kBAAkB,CAAC;AAC9B,IAAA,KAAK,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;;IAE/B,MAAM,EAAE,CAAC,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,4BAA4B,GAA8B;IAC9D,IAAI,EAAE,CAAC,OAAO,CAAC;IACf,QAAQ,EAAE,CAAC,OAAO,CAAC;AACnB,IAAA,KAAK,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC;;AAE3B,IAAA,MAAM,EAAE,EAAE;CACX,CAAC;AAEI,SAAU,mBAAmB,CAAC,OAAkB,EAAA;IACpD,IAAI,WAAW,GAAmB,IAAI,CAAC;AACvC,IAAA,KAAK,MAAM,WAAW,IAAI,OAAO,EAAE;AACjC,QAAA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC;AACpC,QAAA,IAAI,CAAC,WAAW,IAAI,IAAI,KAAK,MAAM,EAAE;AACnC,YAAA,MAAM,IAAI,OAAO,CAAA,iBAAA,oCAEf,iDAAiD,IAAI,CAAA,CAAE,CACxD,CAAC;SACH;QACD,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAClC,YAAA,MAAM,IAAI,OAAO,CAEf,iBAAA,oCAAA,CAAA,yCAAA,EAA4C,IAAI,CAAyB,sBAAA,EAAA,IAAI,CAAC,SAAS,CACrF,cAAc,CACf,CAAA,CAAE,CACJ,CAAC;SACH;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACzB,YAAA,MAAM,IAAI,OAAO,CAEf,iBAAA,oCAAA,CAAA,+DAAA,CAAiE,CAClE,CAAC;SACH;AAED,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACtB,YAAA,MAAM,IAAI,OAAO,CAEf,iBAAA,oCAAA,CAAA,0CAAA,CAA4C,CAC7C,CAAC;SACH;AAED,QAAA,MAAM,WAAW,GAA+B;AAC9C,YAAA,IAAI,EAAE,CAAC;AACP,YAAA,UAAU,EAAE,CAAC;AACb,YAAA,YAAY,EAAE,CAAC;AACf,YAAA,gBAAgB,EAAE,CAAC;SACpB,CAAC;AAEF,QAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AACxB,YAAA,KAAK,MAAM,GAAG,IAAI,iBAAiB,EAAE;AACnC,gBAAA,IAAI,GAAG,IAAI,IAAI,EAAE;AACf,oBAAA,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;iBACvB;aACF;SACF;AACD,QAAA,MAAM,UAAU,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;AAC9C,QAAA,KAAK,MAAM,GAAG,IAAI,iBAAiB,EAAE;AACnC,YAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACrD,MAAM,IAAI,OAAO,CAEf,iBAAA,oCAAA,CAAA,mBAAA,EAAsB,IAAI,CAAoB,iBAAA,EAAA,GAAG,CAAQ,MAAA,CAAA,CAC1D,CAAC;aACH;SACF;QAED,IAAI,WAAW,EAAE;AACf,YAAA,MAAM,yBAAyB,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAC;YACrE,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;AACzD,gBAAA,MAAM,IAAI,OAAO,CAAA,iBAAA,oCAEf,sBAAsB,IAAI,CAAA,gBAAA,EACxB,WAAW,CAAC,IACd,CAA4B,yBAAA,EAAA,IAAI,CAAC,SAAS,CACxC,4BAA4B,CAC7B,CAAA,CAAE,CACJ,CAAC;aACH;SACF;QACD,WAAW,GAAG,WAAW,CAAC;KAC3B;AACH;;ACrHA;;;;;;;;;;;;;;;AAeG;AAkBH;;AAEG;AACH,MAAM,YAAY,GAAG,cAAc,CAAC;AAEpC;;;;;AAKG;MACU,WAAW,CAAA;AAKtB,IAAA,WAAA,CACE,WAAwB,EACjB,KAAa,EACb,MAAwB,EACxB,cAA+B,EAAA;QAF/B,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QACb,IAAM,CAAA,MAAA,GAAN,MAAM,CAAkB;QACxB,IAAc,CAAA,cAAA,GAAd,cAAc,CAAiB;QAPhC,IAAQ,CAAA,QAAA,GAAc,EAAE,CAAC;AACzB,QAAA,IAAA,CAAA,YAAY,GAAkB,OAAO,CAAC,OAAO,EAAE,CAAC;AAQtD,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,MAAM,aAAN,MAAM,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAN,MAAM,CAAE,OAAO,EAAE;AACnB,YAAA,mBAAmB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC;SAChC;KACF;AAED;;;;AAIG;AACH,IAAA,MAAM,UAAU,GAAA;QACd,MAAM,IAAI,CAAC,YAAY,CAAC;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAED;;;AAGG;IACH,MAAM,WAAW,CACf,OAAsC,EAAA;;QAEtC,MAAM,IAAI,CAAC,YAAY,CAAC;AACxB,QAAA,MAAM,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAA,MAAM,sBAAsB,GAA2B;AACrD,YAAA,cAAc,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,cAAc;AAC3C,YAAA,gBAAgB,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,gBAAgB;AAC/C,YAAA,KAAK,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,KAAK;AACzB,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,UAAU;AACnC,YAAA,iBAAiB,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,iBAAiB;YACjD,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC;SACzC,CAAC;QACF,IAAI,WAAW,GAAG,EAA2B,CAAC;;AAE9C,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY;aAClC,IAAI,CAAC,MACJ,eAAe,CACb,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,EACV,sBAAsB,EACtB,IAAI,CAAC,cAAc,CACpB,CACF;aACA,IAAI,CAAC,MAAM,IAAG;;AACb,YAAA,IACE,MAAM,CAAC,QAAQ,CAAC,UAAU;gBAC1B,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EACrC;AACA,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/B,gBAAA,MAAM,eAAe,GAAY;AAC/B,oBAAA,KAAK,EAAE,CAAA,CAAA,EAAA,GAAA,MAAM,CAAC,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAE,CAAA,OAAO,CAAC,KAAK,KAAI,EAAE;;AAE1D,oBAAA,IAAI,EAAE,CAAA,CAAA,EAAA,GAAA,MAAM,CAAC,QAAQ,CAAC,UAAU,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAG,CAAC,CAAE,CAAA,OAAO,CAAC,IAAI,KAAI,OAAO;iBAC9D,CAAC;AACF,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACrC;iBAAM;gBACL,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACnE,IAAI,iBAAiB,EAAE;AACrB,oBAAA,MAAM,CAAC,IAAI,CACT,mCAAmC,iBAAiB,CAAA,sCAAA,CAAwC,CAC7F,CAAC;iBACH;aACF;YACD,WAAW,GAAG,MAAM,CAAC;AACvB,SAAC,CAAC,CAAC;QACL,MAAM,IAAI,CAAC,YAAY,CAAC;AACxB,QAAA,OAAO,WAAW,CAAC;KACpB;AAED;;;;AAIG;IACH,MAAM,iBAAiB,CACrB,OAAsC,EAAA;;QAEtC,MAAM,IAAI,CAAC,YAAY,CAAC;AACxB,QAAA,MAAM,UAAU,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAA,MAAM,sBAAsB,GAA2B;AACrD,YAAA,cAAc,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,cAAc;AAC3C,YAAA,gBAAgB,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,gBAAgB;AAC/C,YAAA,KAAK,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,KAAK;AACzB,YAAA,UAAU,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,UAAU;AACnC,YAAA,iBAAiB,EAAE,CAAA,EAAA,GAAA,IAAI,CAAC,MAAM,0CAAE,iBAAiB;YACjD,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC;SACzC,CAAC;AACF,QAAA,MAAM,aAAa,GAAG,qBAAqB,CACzC,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,EACV,sBAAsB,EACtB,IAAI,CAAC,cAAc,CACpB,CAAC;;AAGF,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY;AAClC,aAAA,IAAI,CAAC,MAAM,aAAa,CAAC;;;aAGzB,KAAK,CAAC,QAAQ,IAAG;AAChB,YAAA,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AAChC,SAAC,CAAC;aACD,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC,QAAQ,CAAC;aAC3C,IAAI,CAAC,QAAQ,IAAG;AACf,YAAA,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AACzD,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC/B,MAAM,eAAe,GAAQ,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAE,CAAC;;AAE9D,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;AACzB,oBAAA,eAAe,CAAC,IAAI,GAAG,OAAO,CAAC;iBAChC;AACD,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;aACrC;iBAAM;AACL,gBAAA,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,QAAQ,CAAC,CAAC;gBAC5D,IAAI,iBAAiB,EAAE;AACrB,oBAAA,MAAM,CAAC,IAAI,CACT,yCAAyC,iBAAiB,CAAA,sCAAA,CAAwC,CACnG,CAAC;iBACH;aACF;AACH,SAAC,CAAC;aACD,KAAK,CAAC,CAAC,IAAG;;;;AAIT,YAAA,IAAI,CAAC,CAAC,OAAO,KAAK,YAAY,EAAE;;;AAG9B,gBAAA,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACjB;AACH,SAAC,CAAC,CAAC;AACL,QAAA,OAAO,aAAa,CAAC;KACtB;AACF;;AC9LD;;;;;;;;;;;;;;;AAeG;AAYI,eAAe,WAAW,CAC/B,WAAwB,EACxB,KAAa,EACb,MAA0B,EAC1B,cAA+B,EAAA;IAE/B,IAAI,IAAI,GAAW,EAAE,CAAC;IACtB,IAAI,WAAW,CAAC,OAAO,CAAC,WAAW,KAAK,WAAW,CAAC,SAAS,EAAE;QAC7D,MAAM,YAAY,GAAGE,qBAAoC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzE,QAAA,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;KACrC;SAAM;AACL,QAAA,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KAC/B;AACD,IAAA,MAAM,QAAQ,GAAG,MAAM,WAAW,CAChC,KAAK,EACL,IAAI,CAAC,YAAY,EACjB,WAAW,EACX,KAAK,EACL,IAAI,EACJ,cAAc,CACf,CAAC;AACF,IAAA,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzB;;ACjDA;;;;;;;;;;;;;;;AAeG;AA+BH;;;AAGG;AACG,MAAO,eAAgB,SAAQ,OAAO,CAAA;AAQ1C,IAAA,WAAA,CACE,EAAM,EACN,WAAwB,EACxB,cAA+B,EAAA;AAE/B,QAAA,KAAK,CAAC,EAAE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;QAC7B,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,IAAI,EAAE,CAAC;QAC3D,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,cAAc,IAAI,EAAE,CAAC;AACvD,QAAA,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;AAC/B,QAAA,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC,iBAAiB,GAAG,uBAAuB,CAC9C,WAAW,CAAC,iBAAiB,CAC9B,CAAC;AACF,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI,EAAE,CAAC;KAC5C;AAED;;;AAGG;IACH,MAAM,eAAe,CACnB,OAA+D,EAAA;AAE/D,QAAA,MAAM,eAAe,GAAG,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAC5D,OAAO,eAAe,CACpB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,EAAA,MAAA,CAAA,MAAA,CAAA,EAER,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,UAAU,EAAE,IAAI,CAAC,UAAU,EAC3B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EACtC,EAAA,eAAe,GAEpB,IAAI,CAAC,cAAc,CACpB,CAAC;KACH;AAED;;;;;AAKG;IACH,MAAM,qBAAqB,CACzB,OAA+D,EAAA;AAE/D,QAAA,MAAM,eAAe,GAAG,0BAA0B,CAAC,OAAO,CAAC,CAAC;QAC5D,OAAO,qBAAqB,CAC1B,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,EAAA,MAAA,CAAA,MAAA,CAAA,EAER,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,cAAc,EAAE,IAAI,CAAC,cAAc,EACnC,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,UAAU,EAAE,IAAI,CAAC,UAAU,EAC3B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EACtC,EAAA,eAAe,GAEpB,IAAI,CAAC,cAAc,CACpB,CAAC;KACH;AAED;;;AAGG;AACH,IAAA,SAAS,CAAC,eAAiC,EAAA;QACzC,OAAO,IAAI,WAAW,CACpB,IAAI,CAAC,YAAY,EACjB,IAAI,CAAC,KAAK,kBAER,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,UAAU,EAAE,IAAI,CAAC,UAAU,EAC3B,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,EACzC,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EACvC,cAAc,EAAE,IAAI,CAAC,cAAc,EAMhC,EAAA,eAAe,GAEpB,IAAI,CAAC,cAAc,CACpB,CAAC;KACH;AAED;;AAEG;IACH,MAAM,WAAW,CACf,OAA2D,EAAA;AAE3D,QAAA,MAAM,eAAe,GAAG,0BAA0B,CAAC,OAAO,CAAC,CAAC;AAC5D,QAAA,OAAO,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;KACpE;AACF;;AC5JD;;;;;;;;;;;;;;;AAeG;AAiBH;;;;;;;;;;;;;;;;;;;;;AAqBG;AACG,MAAO,WAAY,SAAQ,OAAO,CAAA;AAUtC;;;;;;;;;AASG;AACH,IAAA,WAAA,CACE,EAAM,EACN,WAA8B,EACvB,cAA+B,EAAA;QAEtC,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE,GAAG,WAAW,CAAC;AAChE,QAAA,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QAHV,IAAc,CAAA,cAAA,GAAd,cAAc,CAAiB;AAItC,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;AACzC,QAAA,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;KACtC;AAED;;;;;;;;;;;;;;;;;AAiBG;IACH,MAAM,cAAc,CAClB,MAAc,EAAA;AAEd,QAAA,MAAM,IAAI,GAAG,wBAAwB,CAAC,MAAM,EACvC,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,IAAI,CAAC,gBAAgB,CACrB,EAAA,IAAI,CAAC,cAAc,EACtB,CAAC;AACH,QAAA,MAAM,QAAQ,GAAG,MAAM,WAAW,CAChC,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,YAAY;AACjB,qBAAa,KAAK,EAClB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EACpB,IAAI,CAAC,cAAc,CACpB,CAAC;AACF,QAAA,OAAO,qBAAqB,CAAoB,QAAQ,CAAC,CAAC;KAC3D;AAED;;;;;;;;;;;;;;;;;;AAkBG;AACH,IAAA,MAAM,iBAAiB,CACrB,MAAc,EACd,MAAc,EAAA;AAEd,QAAA,MAAM,IAAI,GAAG,wBAAwB,CAAC,MAAM,gCAC1C,MAAM,EAAA,EACH,IAAI,CAAC,gBAAgB,CACrB,EAAA,IAAI,CAAC,cAAc,EACtB,CAAC;AACH,QAAA,MAAM,QAAQ,GAAG,MAAM,WAAW,CAChC,IAAI,CAAC,KAAK,EACV,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,YAAY;AACjB,qBAAa,KAAK,EAClB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EACpB,IAAI,CAAC,cAAc,CACpB,CAAC;AACF,QAAA,OAAO,qBAAqB,CAAiB,QAAQ,CAAC,CAAC;KACxD;AACF;;AC/JD;;;;;;;;;;;;;;;AAeG;AAYH;;;;;;AAMG;MACmB,MAAM,CAAA;AAiC1B,IAAA,WAAA,CAAY,YAA6B,EAAA;;AAEvC,QAAA,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE;YACnC,IAAI,CAAC,QAAQ,CAAC,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;SACzC;;AAED,QAAA,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,cAAc,CAAC,UAAU,CAAC;AACrD,cAAE,CAAC,CAAC,YAAY,CAAC,QAAQ;cACvB,KAAK,CAAC;KACX;AAED;;;;AAIG;IACH,MAAM,GAAA;AACJ,QAAA,MAAM,GAAG,GAAiD;YACxD,IAAI,EAAE,IAAI,CAAC,IAAI;SAChB,CAAC;AACF,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE;AACvB,YAAA,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,SAAS,EAAE;AACzD,gBAAA,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,MAAM,EAAE;oBAC1D,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;iBACxB;aACF;SACF;AACD,QAAA,OAAO,GAAoB,CAAC;KAC7B;IAED,OAAO,KAAK,CAAC,WAA6C,EAAA;QACxD,OAAO,IAAI,WAAW,CAAC,WAAW,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;KACxD;IAED,OAAO,MAAM,CACX,YAKC,EAAA;AAED,QAAA,OAAO,IAAI,YAAY,CACrB,YAAY,EACZ,YAAY,CAAC,UAAU,EACvB,YAAY,CAAC,kBAAkB,CAChC,CAAC;KACH;;IAGD,OAAO,MAAM,CAAC,YAA2B,EAAA;AACvC,QAAA,OAAO,IAAI,YAAY,CAAC,YAAY,CAAC,CAAC;KACvC;IAED,OAAO,UAAU,CACf,YAA+C,EAAA;QAE/C,OAAO,IAAI,YAAY,CAAC,YAAY,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;KAC1D;IAED,OAAO,OAAO,CAAC,aAA4B,EAAA;AACzC,QAAA,OAAO,IAAI,aAAa,CAAC,aAAa,CAAC,CAAC;KACzC;;IAGD,OAAO,MAAM,CAAC,YAA2B,EAAA;AACvC,QAAA,OAAO,IAAI,YAAY,CAAC,YAAY,CAAC,CAAC;KACvC;;IAGD,OAAO,OAAO,CAAC,aAA4B,EAAA;AACzC,QAAA,OAAO,IAAI,aAAa,CAAC,aAAa,CAAC,CAAC;KACzC;AACF,CAAA;AAcD;;;AAGG;AACG,MAAO,aAAc,SAAQ,MAAM,CAAA;AACvC,IAAA,WAAA,CAAY,YAA2B,EAAA;QACrC,KAAK,CAAA,MAAA,CAAA,MAAA,CAAA,EACH,IAAI,EAAE,UAAU,CAAC,OAAO,EAAA,EACrB,YAAY,CAAA,CACf,CAAC;KACJ;AACF,CAAA;AAED;;;AAGG;AACG,MAAO,YAAa,SAAQ,MAAM,CAAA;AACtC,IAAA,WAAA,CAAY,YAA2B,EAAA;QACrC,KAAK,CAAA,MAAA,CAAA,MAAA,CAAA,EACH,IAAI,EAAE,UAAU,CAAC,MAAM,EAAA,EACpB,YAAY,CAAA,CACf,CAAC;KACJ;AACF,CAAA;AAED;;;AAGG;AACG,MAAO,aAAc,SAAQ,MAAM,CAAA;AACvC,IAAA,WAAA,CAAY,YAA2B,EAAA;QACrC,KAAK,CAAA,MAAA,CAAA,MAAA,CAAA,EACH,IAAI,EAAE,UAAU,CAAC,OAAO,EAAA,EACrB,YAAY,CAAA,CACf,CAAC;KACJ;AACF,CAAA;AAED;;;;AAIG;AACG,MAAO,YAAa,SAAQ,MAAM,CAAA;IAEtC,WAAY,CAAA,YAA2B,EAAE,UAAqB,EAAA;QAC5D,KAAK,CAAA,MAAA,CAAA,MAAA,CAAA,EACH,IAAI,EAAE,UAAU,CAAC,MAAM,EAAA,EACpB,YAAY,CAAA,CACf,CAAC;AACH,QAAA,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;KACxB;AAED;;AAEG;IACH,MAAM,GAAA;AACJ,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;AAC3B,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;SACzB;AACD,QAAA,OAAO,GAAoB,CAAC;KAC7B;AACF,CAAA;AAED;;;;;AAKG;AACG,MAAO,WAAY,SAAQ,MAAM,CAAA;IACrC,WAAY,CAAA,YAA0B,EAAS,KAAkB,EAAA;QAC/D,KAAK,CAAA,MAAA,CAAA,MAAA,CAAA,EACH,IAAI,EAAE,UAAU,CAAC,KAAK,EAAA,EACnB,YAAY,CAAA,CACf,CAAC;QAJ0C,IAAK,CAAA,KAAA,GAAL,KAAK,CAAa;KAKhE;AAED;;AAEG;IACH,MAAM,GAAA;AACJ,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QAC3B,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;AAChC,QAAA,OAAO,GAAG,CAAC;KACZ;AACF,CAAA;AAED;;;;AAIG;AACG,MAAO,YAAa,SAAQ,MAAM,CAAA;AACtC,IAAA,WAAA,CACE,YAA0B,EACnB,UAEN,EACM,qBAA+B,EAAE,EAAA;QAExC,KAAK,CAAA,MAAA,CAAA,MAAA,CAAA,EACH,IAAI,EAAE,UAAU,CAAC,MAAM,EAAA,EACpB,YAAY,CAAA,CACf,CAAC;QARI,IAAU,CAAA,UAAA,GAAV,UAAU,CAEhB;QACM,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB,CAAe;KAMzC;AAED;;AAEG;IACH,MAAM,GAAA;AACJ,QAAA,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;AAC3B,QAAA,GAAG,CAAC,UAAU,GAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAQ,IAAI,CAAC,UAAU,CAAE,CAAC;QACxC,MAAM,QAAQ,GAAG,EAAE,CAAC;AACpB,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACjD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;AAChD,oBAAA,MAAM,IAAI,OAAO,CAAA,gBAAA,mCAEf,aAAa,WAAW,CAAA,mDAAA,CAAqD,CAC9E,CAAC;iBACH;aACF;SACF;AACD,QAAA,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE;YACzC,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;AAC/C,gBAAA,GAAG,CAAC,UAAU,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,UAAU,CAC3C,WAAW,CACZ,CAAC,MAAM,EAAmB,CAAC;gBAC5B,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAClD,oBAAA,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAC5B;aACF;SACF;AACD,QAAA,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACvB,YAAA,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;SACzB;QACD,OAAQ,GAA6B,CAAC,kBAAkB,CAAC;AACzD,QAAA,OAAO,GAAoB,CAAC;KAC7B;AACF;;ACzSD;;;;;;;;;;;;;;;AAeG;AAIH;;;;;;;;;;;;;;;;AAgBG;MACU,iBAAiB,CAAA;AAU5B,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;KAC7B;AAED;;;;;;;AAOG;IACH,OAAO,IAAI,CAAC,kBAA2B,EAAA;AACrC,QAAA,IACE,kBAAkB;aACjB,kBAAkB,GAAG,CAAC,IAAI,kBAAkB,GAAG,GAAG,CAAC,EACpD;AACA,YAAA,MAAM,CAAC,IAAI,CACT,uCAAuC,kBAAkB,CAAA,4CAAA,CAA8C,CACxG,CAAC;SACH;AACD,QAAA,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,kBAAkB,EAAE,CAAC;KACvD;AAED;;;;;;AAMG;AACH,IAAA,OAAO,GAAG,GAAA;AACR,QAAA,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC;KAClC;AACF;;AChFD;;;;;;;;;;;;;;;AAeG;AA2BH;;;;;;;;AAQG;AACI,MAAM,aAAa,GAAG,QAAQ;AAErC;;;;;;;;AAQG;AACI,MAAM,aAAa,GAAG,QAAQ;AAQrC;;;;;;;;;;;;;AAaG;SACa,WAAW,CACzB,MAAmB,MAAM,EAAE,EAC3B,OAAyB,EAAA;AAEzB,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;;IAE9B,MAAM,UAAU,GAAmB,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAE9D,IAAA,MAAM,OAAO,GAAG,IAAI,eAAe,CAAC,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,CAAC,CAAC;AACvD,IAAA,MAAM,UAAU,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IACrD,OAAO,UAAU,CAAC,YAAY,CAAC;QAC7B,UAAU;AACX,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BG;AACa,SAAA,KAAK,CACnB,GAAA,GAAmB,MAAM,EAAE,EAC3B,OAAqB,GAAA,EAAE,OAAO,EAAE,IAAI,eAAe,EAAE,EAAE,EAAA;AAEvD,IAAA,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;;IAE9B,MAAM,UAAU,GAAmB,YAAY,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAE9D,MAAM,UAAU,GAAG,wBAAwB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC7D,OAAO,UAAU,CAAC,YAAY,CAAC;QAC7B,UAAU;AACX,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;AAKG;SACa,kBAAkB,CAChC,EAAM,EACN,WAAwB,EACxB,cAA+B,EAAA;AAE/B,IAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;AACtB,QAAA,MAAM,IAAI,OAAO,CAEf,UAAA,6BAAA,CAAA,kFAAA,CAAoF,CACrF,CAAC;KACH;IACD,OAAO,IAAI,eAAe,CAAC,EAAE,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AAC9D,CAAC;AAED;;;;;;;;;;;;;AAaG;SACa,cAAc,CAC5B,EAAM,EACN,WAA8B,EAC9B,cAA+B,EAAA;AAE/B,IAAA,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE;AACtB,QAAA,MAAM,IAAI,OAAO,CAEf,UAAA,6BAAA,CAAA,8EAAA,CAAgF,CACjF,CAAC;KACH;IACD,OAAO,IAAI,WAAW,CAAC,EAAE,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC;AAC1D;;AC3LA;;;;AAIG;AA4BH,SAAS,UAAU,GAAA;AACjB,IAAA,kBAAkB,CAChB,IAAI,SAAS,CACX,OAAO,EACP,CAAC,SAAS,EAAE,EAAE,kBAAkB,EAAE,KAAI;QACpC,IAAI,CAAC,kBAAkB,EAAE;AACvB,YAAA,MAAM,IAAI,OAAO,CAEf,OAAA,0BAAA,6CAA6C,CAC9C,CAAC;SACH;AAED,QAAA,MAAM,OAAO,GAAG,wBAAwB,CAAC,kBAAkB,CAAC,CAAC;;QAG7D,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;QACxD,MAAM,IAAI,GAAG,SAAS,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QACpD,MAAM,gBAAgB,GAAG,SAAS,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;QACrE,OAAO,IAAI,SAAS,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;AAC7D,KAAC,sCAEF,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAC7B,CAAC;AAEF,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;;AAEvC,IAAA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAkB,CAAC,CAAC;AACrD,CAAC;AAED,UAAU,EAAE;;;;"}