{"version": 3, "file": "index.esm2017.js", "sources": ["../../src/util/constants.ts", "../../src/util/errors.ts", "../../src/functions/common.ts", "../../src/functions/create-installation-request.ts", "../../src/util/sleep.ts", "../../src/helpers/buffer-to-base64-url-safe.ts", "../../src/helpers/generate-fid.ts", "../../src/util/get-key.ts", "../../src/helpers/fid-changed.ts", "../../src/helpers/idb-manager.ts", "../../src/helpers/get-installation-entry.ts", "../../src/functions/generate-auth-token-request.ts", "../../src/helpers/refresh-auth-token.ts", "../../src/api/get-id.ts", "../../src/api/get-token.ts", "../../src/functions/delete-installation-request.ts", "../../src/api/delete-installations.ts", "../../src/api/on-id-change.ts", "../../src/api/get-installations.ts", "../../src/helpers/extract-app-config.ts", "../../src/functions/config.ts", "../../src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { version } from '../../package.json';\n\nexport const PENDING_TIMEOUT_MS = 10000;\n\nexport const PACKAGE_VERSION = `w:${version}`;\nexport const INTERNAL_AUTH_VERSION = 'FIS_v2';\n\nexport const INSTALLATIONS_API_URL =\n  'https://firebaseinstallations.googleapis.com/v1';\n\nexport const TOKEN_EXPIRATION_BUFFER = 60 * 60 * 1000; // One hour\n\nexport const SERVICE = 'installations';\nexport const SERVICE_NAME = 'Installations';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ErrorFactory, FirebaseError } from '@firebase/util';\nimport { SERVICE, SERVICE_NAME } from './constants';\n\nexport const enum ErrorCode {\n  MISSING_APP_CONFIG_VALUES = 'missing-app-config-values',\n  NOT_REGISTERED = 'not-registered',\n  INSTALLATION_NOT_FOUND = 'installation-not-found',\n  REQUEST_FAILED = 'request-failed',\n  APP_OFFLINE = 'app-offline',\n  DELETE_PENDING_REGISTRATION = 'delete-pending-registration'\n}\n\nconst ERROR_DESCRIPTION_MAP: { readonly [key in ErrorCode]: string } = {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]:\n    'Missing App configuration value: \"{$valueName}\"',\n  [ErrorCode.NOT_REGISTERED]: 'Firebase Installation is not registered.',\n  [ErrorCode.INSTALLATION_NOT_FOUND]: 'Firebase Installation not found.',\n  [ErrorCode.REQUEST_FAILED]:\n    '{$requestName} request failed with error \"{$serverCode} {$serverStatus}: {$serverMessage}\"',\n  [ErrorCode.APP_OFFLINE]: 'Could not process request. Application offline.',\n  [ErrorCode.DELETE_PENDING_REGISTRATION]:\n    \"Can't delete installation while there is a pending registration request.\"\n};\n\ninterface ErrorParams {\n  [ErrorCode.MISSING_APP_CONFIG_VALUES]: {\n    valueName: string;\n  };\n  [ErrorCode.REQUEST_FAILED]: {\n    requestName: string;\n    [index: string]: string | number; // to make TypeScript 3.8 happy\n  } & ServerErrorData;\n}\n\nexport const ERROR_FACTORY = new ErrorFactory<ErrorCode, ErrorParams>(\n  SERVICE,\n  SERVICE_NAME,\n  ERROR_DESCRIPTION_MAP\n);\n\nexport interface ServerErrorData {\n  serverCode: number;\n  serverMessage: string;\n  serverStatus: string;\n}\n\nexport type ServerError = FirebaseError & { customData: ServerErrorData };\n\n/** Returns true if error is a FirebaseError that is based on an error from the server. */\nexport function isServerError(error: unknown): error is ServerError {\n  return (\n    error instanceof FirebaseError &&\n    error.code.includes(ErrorCode.REQUEST_FAILED)\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseError } from '@firebase/util';\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport {\n  INSTALLATIONS_API_URL,\n  INTERNAL_AUTH_VERSION\n} from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { AppConfig } from '../interfaces/installation-impl';\n\nexport function getInstallationsEndpoint({ projectId }: AppConfig): string {\n  return `${INSTALLATIONS_API_URL}/projects/${projectId}/installations`;\n}\n\nexport function extractAuthTokenInfoFromResponse(\n  response: GenerateAuthTokenResponse\n): CompletedAuthToken {\n  return {\n    token: response.token,\n    requestStatus: RequestStatus.COMPLETED,\n    expiresIn: getExpiresInFromResponseExpiresIn(response.expiresIn),\n    creationTime: Date.now()\n  };\n}\n\nexport async function getErrorFromResponse(\n  requestName: string,\n  response: Response\n): Promise<FirebaseError> {\n  const responseJson: ErrorResponse = await response.json();\n  const errorData = responseJson.error;\n  return ERROR_FACTORY.create(ErrorCode.REQUEST_FAILED, {\n    requestName,\n    serverCode: errorData.code,\n    serverMessage: errorData.message,\n    serverStatus: errorData.status\n  });\n}\n\nexport function getHeaders({ apiKey }: AppConfig): Headers {\n  return new Headers({\n    'Content-Type': 'application/json',\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n\nexport function getHeadersWithAuth(\n  appConfig: AppConfig,\n  { refreshToken }: RegisteredInstallationEntry\n): Headers {\n  const headers = getHeaders(appConfig);\n  headers.append('Authorization', getAuthorizationHeader(refreshToken));\n  return headers;\n}\n\nexport interface ErrorResponse {\n  error: {\n    code: number;\n    message: string;\n    status: string;\n  };\n}\n\n/**\n * Calls the passed in fetch wrapper and returns the response.\n * If the returned response has a status of 5xx, re-runs the function once and\n * returns the response.\n */\nexport async function retryIfServerError(\n  fn: () => Promise<Response>\n): Promise<Response> {\n  const result = await fn();\n\n  if (result.status >= 500 && result.status < 600) {\n    // Internal Server Error. Retry request.\n    return fn();\n  }\n\n  return result;\n}\n\nfunction getExpiresInFromResponseExpiresIn(responseExpiresIn: string): number {\n  // This works because the server will never respond with fractions of a second.\n  return Number(responseExpiresIn.replace('s', '000'));\n}\n\nfunction getAuthorizationHeader(refreshToken: string): string {\n  return `${INTERNAL_AUTH_VERSION} ${refreshToken}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { CreateInstallationResponse } from '../interfaces/api-response';\nimport {\n  InProgressInstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { INTERNAL_AUTH_VERSION, PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeaders,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\n\nexport async function createInstallationRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  { fid }: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  const endpoint = getInstallationsEndpoint(appConfig);\n\n  const headers = getHeaders(appConfig);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    fid,\n    authVersion: INTERNAL_AUTH_VERSION,\n    appId: appConfig.appId,\n    sdkVersion: PACKAGE_VERSION\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: CreateInstallationResponse = await response.json();\n    const registeredInstallationEntry: RegisteredInstallationEntry = {\n      fid: responseValue.fid || fid,\n      registrationStatus: RequestStatus.COMPLETED,\n      refreshToken: responseValue.refreshToken,\n      authToken: extractAuthTokenInfoFromResponse(responseValue.authToken)\n    };\n    return registeredInstallationEntry;\n  } else {\n    throw await getErrorFromResponse('Create Installation', response);\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/** Returns a promise that resolves after given time passes. */\nexport function sleep(ms: number): Promise<void> {\n  return new Promise<void>(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport function bufferToBase64UrlSafe(array: Uint8Array): string {\n  const b64 = btoa(String.fromCharCode(...array));\n  return b64.replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { bufferToBase64UrlSafe } from './buffer-to-base64-url-safe';\n\nexport const VALID_FID_PATTERN = /^[cdef][\\w-]{21}$/;\nexport const INVALID_FID = '';\n\n/**\n * Generates a new FID using random values from Web Crypto API.\n * Returns an empty string if FID generation fails for any reason.\n */\nexport function generateFid(): string {\n  try {\n    // A valid FID has exactly 22 base64 characters, which is 132 bits, or 16.5\n    // bytes. our implementation generates a 17 byte array instead.\n    const fidByteArray = new Uint8Array(17);\n    const crypto =\n      self.crypto || (self as unknown as { msCrypto: Crypto }).msCrypto;\n    crypto.getRandomValues(fidByteArray);\n\n    // Replace the first 4 random bits with the constant FID header of 0b0111.\n    fidByteArray[0] = 0b01110000 + (fidByteArray[0] % 0b00010000);\n\n    const fid = encode(fidByteArray);\n\n    return VALID_FID_PATTERN.test(fid) ? fid : INVALID_FID;\n  } catch {\n    // FID generation errored\n    return INVALID_FID;\n  }\n}\n\n/** Converts a FID Uint8Array to a base64 string representation. */\nfunction encode(fidByteArray: Uint8Array): string {\n  const b64String = bufferToBase64UrlSafe(fidByteArray);\n\n  // Remove the 23rd character that was added because of the extra 4 bits at the\n  // end of our 17 byte array, and the '=' padding.\n  return b64String.substr(0, 22);\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\n\n/** Returns a string key that can be used to identify the app. */\nexport function getKey(appConfig: AppConfig): string {\n  return `${appConfig.appName}!${appConfig.appId}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getKey } from '../util/get-key';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { IdChangeCallbackFn } from '../api';\n\nconst fidChangeCallbacks: Map<string, Set<IdChangeCallbackFn>> = new Map();\n\n/**\n * Calls the onIdChange callbacks with the new FID value, and broadcasts the\n * change to other tabs.\n */\nexport function fidChanged(appConfig: AppConfig, fid: string): void {\n  const key = getKey(appConfig);\n\n  callFidChangeCallbacks(key, fid);\n  broadcastFidChange(key, fid);\n}\n\nexport function addCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  // Open the broadcast channel if it's not already open,\n  // to be able to listen to change events from other tabs.\n  getBroadcastChannel();\n\n  const key = getKey(appConfig);\n\n  let callbackSet = fidChangeCallbacks.get(key);\n  if (!callbackSet) {\n    callbackSet = new Set();\n    fidChangeCallbacks.set(key, callbackSet);\n  }\n  callbackSet.add(callback);\n}\n\nexport function removeCallback(\n  appConfig: AppConfig,\n  callback: IdChangeCallbackFn\n): void {\n  const key = getKey(appConfig);\n\n  const callbackSet = fidChangeCallbacks.get(key);\n\n  if (!callbackSet) {\n    return;\n  }\n\n  callbackSet.delete(callback);\n  if (callbackSet.size === 0) {\n    fidChangeCallbacks.delete(key);\n  }\n\n  // Close broadcast channel if there are no more callbacks.\n  closeBroadcastChannel();\n}\n\nfunction callFidChangeCallbacks(key: string, fid: string): void {\n  const callbacks = fidChangeCallbacks.get(key);\n  if (!callbacks) {\n    return;\n  }\n\n  for (const callback of callbacks) {\n    callback(fid);\n  }\n}\n\nfunction broadcastFidChange(key: string, fid: string): void {\n  const channel = getBroadcastChannel();\n  if (channel) {\n    channel.postMessage({ key, fid });\n  }\n  closeBroadcastChannel();\n}\n\nlet broadcastChannel: BroadcastChannel | null = null;\n/** Opens and returns a BroadcastChannel if it is supported by the browser. */\nfunction getBroadcastChannel(): BroadcastChannel | null {\n  if (!broadcastChannel && 'BroadcastChannel' in self) {\n    broadcastChannel = new BroadcastChannel('[Firebase] FID Change');\n    broadcastChannel.onmessage = e => {\n      callFidChangeCallbacks(e.data.key, e.data.fid);\n    };\n  }\n  return broadcastChannel;\n}\n\nfunction closeBroadcastChannel(): void {\n  if (fidChangeCallbacks.size === 0 && broadcastChannel) {\n    broadcastChannel.close();\n    broadcastChannel = null;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DBSchema, IDBPDatabase, openDB } from 'idb';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { InstallationEntry } from '../interfaces/installation-entry';\nimport { getKey } from '../util/get-key';\nimport { fidChanged } from './fid-changed';\n\nconst DATABASE_NAME = 'firebase-installations-database';\nconst DATABASE_VERSION = 1;\nconst OBJECT_STORE_NAME = 'firebase-installations-store';\n\ninterface InstallationsDB extends DBSchema {\n  'firebase-installations-store': {\n    key: string;\n    value: InstallationEntry | undefined;\n  };\n}\n\nlet dbPromise: Promise<IDBPDatabase<InstallationsDB>> | null = null;\nfunction getDbPromise(): Promise<IDBPDatabase<InstallationsDB>> {\n  if (!dbPromise) {\n    dbPromise = openDB(DATABASE_NAME, DATABASE_VERSION, {\n      upgrade: (db, oldVersion) => {\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (oldVersion) {\n          case 0:\n            db.createObjectStore(OBJECT_STORE_NAME);\n        }\n      }\n    });\n  }\n  return dbPromise;\n}\n\n/** Gets record(s) from the objectStore that match the given key. */\nexport async function get(\n  appConfig: AppConfig\n): Promise<InstallationEntry | undefined> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  return db\n    .transaction(OBJECT_STORE_NAME)\n    .objectStore(OBJECT_STORE_NAME)\n    .get(key) as Promise<InstallationEntry>;\n}\n\n/** Assigns or overwrites the record for the given key with the given value. */\nexport async function set<ValueType extends InstallationEntry>(\n  appConfig: AppConfig,\n  value: ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const objectStore = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue = (await objectStore.get(key)) as InstallationEntry;\n  await objectStore.put(value, key);\n  await tx.done;\n\n  if (!oldValue || oldValue.fid !== value.fid) {\n    fidChanged(appConfig, value.fid);\n  }\n\n  return value;\n}\n\n/** Removes record(s) from the objectStore that match the given key. */\nexport async function remove(appConfig: AppConfig): Promise<void> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).delete(key);\n  await tx.done;\n}\n\n/**\n * Atomically updates a record with the result of updateFn, which gets\n * called with the current value. If newValue is undefined, the record is\n * deleted instead.\n * @return Updated value\n */\nexport async function update<ValueType extends InstallationEntry | undefined>(\n  appConfig: AppConfig,\n  updateFn: (previousValue: InstallationEntry | undefined) => ValueType\n): Promise<ValueType> {\n  const key = getKey(appConfig);\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  const store = tx.objectStore(OBJECT_STORE_NAME);\n  const oldValue: InstallationEntry | undefined = (await store.get(\n    key\n  )) as InstallationEntry;\n  const newValue = updateFn(oldValue);\n\n  if (newValue === undefined) {\n    await store.delete(key);\n  } else {\n    await store.put(newValue, key);\n  }\n  await tx.done;\n\n  if (newValue && (!oldValue || oldValue.fid !== newValue.fid)) {\n    fidChanged(appConfig, newValue.fid);\n  }\n\n  return newValue;\n}\n\nexport async function clear(): Promise<void> {\n  const db = await getDbPromise();\n  const tx = db.transaction(OBJECT_STORE_NAME, 'readwrite');\n  await tx.objectStore(OBJECT_STORE_NAME).clear();\n  await tx.done;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { createInstallationRequest } from '../functions/create-installation-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  InProgressInstallationEntry,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { generateFid, INVALID_FID } from './generate-fid';\nimport { remove, set, update } from './idb-manager';\n\nexport interface InstallationEntryWithRegistrationPromise {\n  installationEntry: InstallationEntry;\n  /** Exist iff the installationEntry is not registered. */\n  registrationPromise?: Promise<RegisteredInstallationEntry>;\n}\n\n/**\n * Updates and returns the InstallationEntry from the database.\n * Also triggers a registration request if it is necessary and possible.\n */\nexport async function getInstallationEntry(\n  installations: FirebaseInstallationsImpl\n): Promise<InstallationEntryWithRegistrationPromise> {\n  let registrationPromise: Promise<RegisteredInstallationEntry> | undefined;\n\n  const installationEntry = await update(installations.appConfig, oldEntry => {\n    const installationEntry = updateOrCreateInstallationEntry(oldEntry);\n    const entryWithPromise = triggerRegistrationIfNecessary(\n      installations,\n      installationEntry\n    );\n    registrationPromise = entryWithPromise.registrationPromise;\n    return entryWithPromise.installationEntry;\n  });\n\n  if (installationEntry.fid === INVALID_FID) {\n    // FID generation failed. Waiting for the FID from the server.\n    return { installationEntry: await registrationPromise! };\n  }\n\n  return {\n    installationEntry,\n    registrationPromise\n  };\n}\n\n/**\n * Creates a new Installation Entry if one does not exist.\n * Also clears timed out pending requests.\n */\nfunction updateOrCreateInstallationEntry(\n  oldEntry: InstallationEntry | undefined\n): InstallationEntry {\n  const entry: InstallationEntry = oldEntry || {\n    fid: generateFid(),\n    registrationStatus: RequestStatus.NOT_STARTED\n  };\n\n  return clearTimedOutRequest(entry);\n}\n\n/**\n * If the Firebase Installation is not registered yet, this will trigger the\n * registration and return an InProgressInstallationEntry.\n *\n * If registrationPromise does not exist, the installationEntry is guaranteed\n * to be registered.\n */\nfunction triggerRegistrationIfNecessary(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InstallationEntry\n): InstallationEntryWithRegistrationPromise {\n  if (installationEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n    if (!navigator.onLine) {\n      // Registration required but app is offline.\n      const registrationPromiseWithError = Promise.reject(\n        ERROR_FACTORY.create(ErrorCode.APP_OFFLINE)\n      );\n      return {\n        installationEntry,\n        registrationPromise: registrationPromiseWithError\n      };\n    }\n\n    // Try registering. Change status to IN_PROGRESS.\n    const inProgressEntry: InProgressInstallationEntry = {\n      fid: installationEntry.fid,\n      registrationStatus: RequestStatus.IN_PROGRESS,\n      registrationTime: Date.now()\n    };\n    const registrationPromise = registerInstallation(\n      installations,\n      inProgressEntry\n    );\n    return { installationEntry: inProgressEntry, registrationPromise };\n  } else if (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS\n  ) {\n    return {\n      installationEntry,\n      registrationPromise: waitUntilFidRegistration(installations)\n    };\n  } else {\n    return { installationEntry };\n  }\n}\n\n/** This will be executed only once for each new Firebase Installation. */\nasync function registerInstallation(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: InProgressInstallationEntry\n): Promise<RegisteredInstallationEntry> {\n  try {\n    const registeredInstallationEntry = await createInstallationRequest(\n      installations,\n      installationEntry\n    );\n    return set(installations.appConfig, registeredInstallationEntry);\n  } catch (e) {\n    if (isServerError(e) && e.customData.serverCode === 409) {\n      // Server returned a \"FID cannot be used\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      // Registration failed. Set FID as not registered.\n      await set(installations.appConfig, {\n        fid: installationEntry.fid,\n        registrationStatus: RequestStatus.NOT_STARTED\n      });\n    }\n    throw e;\n  }\n}\n\n/** Call if FID registration is pending in another request. */\nasync function waitUntilFidRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<RegisteredInstallationEntry> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry: InstallationEntry = await updateInstallationRequest(\n    installations.appConfig\n  );\n  while (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n    // createInstallation request still in progress.\n    await sleep(100);\n\n    entry = await updateInstallationRequest(installations.appConfig);\n  }\n\n  if (entry.registrationStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    const { installationEntry, registrationPromise } =\n      await getInstallationEntry(installations);\n\n    if (registrationPromise) {\n      return registrationPromise;\n    } else {\n      // if there is no registrationPromise, entry is registered.\n      return installationEntry as RegisteredInstallationEntry;\n    }\n  }\n\n  return entry;\n}\n\n/**\n * Called only if there is a CreateInstallation request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * CreateInstallation request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateInstallationRequest(\n  appConfig: AppConfig\n): Promise<InstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!oldEntry) {\n      throw ERROR_FACTORY.create(ErrorCode.INSTALLATION_NOT_FOUND);\n    }\n    return clearTimedOutRequest(oldEntry);\n  });\n}\n\nfunction clearTimedOutRequest(entry: InstallationEntry): InstallationEntry {\n  if (hasInstallationRequestTimedOut(entry)) {\n    return {\n      fid: entry.fid,\n      registrationStatus: RequestStatus.NOT_STARTED\n    };\n  }\n\n  return entry;\n}\n\nfunction hasInstallationRequestTimedOut(\n  installationEntry: InstallationEntry\n): boolean {\n  return (\n    installationEntry.registrationStatus === RequestStatus.IN_PROGRESS &&\n    installationEntry.registrationTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { GenerateAuthTokenResponse } from '../interfaces/api-response';\nimport {\n  CompletedAuthToken,\n  RegisteredInstallationEntry\n} from '../interfaces/installation-entry';\nimport { PACKAGE_VERSION } from '../util/constants';\nimport {\n  extractAuthTokenInfoFromResponse,\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\nimport {\n  FirebaseInstallationsImpl,\n  AppConfig\n} from '../interfaces/installation-impl';\n\nexport async function generateAuthTokenRequest(\n  { appConfig, heartbeatServiceProvider }: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  const endpoint = getGenerateAuthTokenEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n\n  // If heartbeat service exists, add the heartbeat string to the header.\n  const heartbeatService = heartbeatServiceProvider.getImmediate({\n    optional: true\n  });\n  if (heartbeatService) {\n    const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\n    if (heartbeatsHeader) {\n      headers.append('x-firebase-client', heartbeatsHeader);\n    }\n  }\n\n  const body = {\n    installation: {\n      sdkVersion: PACKAGE_VERSION,\n      appId: appConfig.appId\n    }\n  };\n\n  const request: RequestInit = {\n    method: 'POST',\n    headers,\n    body: JSON.stringify(body)\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (response.ok) {\n    const responseValue: GenerateAuthTokenResponse = await response.json();\n    const completedAuthToken: CompletedAuthToken =\n      extractAuthTokenInfoFromResponse(responseValue);\n    return completedAuthToken;\n  } else {\n    throw await getErrorFromResponse('Generate Auth Token', response);\n  }\n}\n\nfunction getGenerateAuthTokenEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}/authTokens:generate`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { generateAuthTokenRequest } from '../functions/generate-auth-token-request';\nimport {\n  AppConfig,\n  FirebaseInstallationsImpl\n} from '../interfaces/installation-impl';\nimport {\n  AuthToken,\n  CompletedAuthToken,\n  InProgressAuthToken,\n  InstallationEntry,\n  RegisteredInstallationEntry,\n  RequestStatus\n} from '../interfaces/installation-entry';\nimport { PENDING_TIMEOUT_MS, TOKEN_EXPIRATION_BUFFER } from '../util/constants';\nimport { ERROR_FACTORY, ErrorCode, isServerError } from '../util/errors';\nimport { sleep } from '../util/sleep';\nimport { remove, set, update } from './idb-manager';\n\n/**\n * Returns a valid authentication token for the installation. Generates a new\n * token if one doesn't exist, is expired or about to expire.\n *\n * Should only be called if the Firebase Installation is registered.\n */\nexport async function refreshAuthToken(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh = false\n): Promise<CompletedAuthToken> {\n  let tokenPromise: Promise<CompletedAuthToken> | undefined;\n  const entry = await update(installations.appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (!forceRefresh && isAuthTokenValid(oldAuthToken)) {\n      // There is a valid token in the DB.\n      return oldEntry;\n    } else if (oldAuthToken.requestStatus === RequestStatus.IN_PROGRESS) {\n      // There already is a token request in progress.\n      tokenPromise = waitUntilAuthTokenRequest(installations, forceRefresh);\n      return oldEntry;\n    } else {\n      // No token or token expired.\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      }\n\n      const inProgressEntry = makeAuthTokenRequestInProgressEntry(oldEntry);\n      tokenPromise = fetchAuthTokenFromServer(installations, inProgressEntry);\n      return inProgressEntry;\n    }\n  });\n\n  const authToken = tokenPromise\n    ? await tokenPromise\n    : (entry.authToken as CompletedAuthToken);\n  return authToken;\n}\n\n/**\n * Call only if FID is registered and Auth Token request is in progress.\n *\n * Waits until the current pending request finishes. If the request times out,\n * tries once in this thread as well.\n */\nasync function waitUntilAuthTokenRequest(\n  installations: FirebaseInstallationsImpl,\n  forceRefresh: boolean\n): Promise<CompletedAuthToken> {\n  // Unfortunately, there is no way of reliably observing when a value in\n  // IndexedDB changes (yet, see https://github.com/WICG/indexed-db-observers),\n  // so we need to poll.\n\n  let entry = await updateAuthTokenRequest(installations.appConfig);\n  while (entry.authToken.requestStatus === RequestStatus.IN_PROGRESS) {\n    // generateAuthToken still in progress.\n    await sleep(100);\n\n    entry = await updateAuthTokenRequest(installations.appConfig);\n  }\n\n  const authToken = entry.authToken;\n  if (authToken.requestStatus === RequestStatus.NOT_STARTED) {\n    // The request timed out or failed in a different call. Try again.\n    return refreshAuthToken(installations, forceRefresh);\n  } else {\n    return authToken;\n  }\n}\n\n/**\n * Called only if there is a GenerateAuthToken request in progress.\n *\n * Updates the InstallationEntry in the DB based on the status of the\n * GenerateAuthToken request.\n *\n * Returns the updated InstallationEntry.\n */\nfunction updateAuthTokenRequest(\n  appConfig: AppConfig\n): Promise<RegisteredInstallationEntry> {\n  return update(appConfig, oldEntry => {\n    if (!isEntryRegistered(oldEntry)) {\n      throw ERROR_FACTORY.create(ErrorCode.NOT_REGISTERED);\n    }\n\n    const oldAuthToken = oldEntry.authToken;\n    if (hasAuthTokenRequestTimedOut(oldAuthToken)) {\n      return {\n        ...oldEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n    }\n\n    return oldEntry;\n  });\n}\n\nasync function fetchAuthTokenFromServer(\n  installations: FirebaseInstallationsImpl,\n  installationEntry: RegisteredInstallationEntry\n): Promise<CompletedAuthToken> {\n  try {\n    const authToken = await generateAuthTokenRequest(\n      installations,\n      installationEntry\n    );\n    const updatedInstallationEntry: RegisteredInstallationEntry = {\n      ...installationEntry,\n      authToken\n    };\n    await set(installations.appConfig, updatedInstallationEntry);\n    return authToken;\n  } catch (e) {\n    if (\n      isServerError(e) &&\n      (e.customData.serverCode === 401 || e.customData.serverCode === 404)\n    ) {\n      // Server returned a \"FID not found\" or a \"Invalid authentication\" error.\n      // Generate a new ID next time.\n      await remove(installations.appConfig);\n    } else {\n      const updatedInstallationEntry: RegisteredInstallationEntry = {\n        ...installationEntry,\n        authToken: { requestStatus: RequestStatus.NOT_STARTED }\n      };\n      await set(installations.appConfig, updatedInstallationEntry);\n    }\n    throw e;\n  }\n}\n\nfunction isEntryRegistered(\n  installationEntry: InstallationEntry | undefined\n): installationEntry is RegisteredInstallationEntry {\n  return (\n    installationEntry !== undefined &&\n    installationEntry.registrationStatus === RequestStatus.COMPLETED\n  );\n}\n\nfunction isAuthTokenValid(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.COMPLETED &&\n    !isAuthTokenExpired(authToken)\n  );\n}\n\nfunction isAuthTokenExpired(authToken: CompletedAuthToken): boolean {\n  const now = Date.now();\n  return (\n    now < authToken.creationTime ||\n    authToken.creationTime + authToken.expiresIn < now + TOKEN_EXPIRATION_BUFFER\n  );\n}\n\n/** Returns an updated InstallationEntry with an InProgressAuthToken. */\nfunction makeAuthTokenRequestInProgressEntry(\n  oldEntry: RegisteredInstallationEntry\n): RegisteredInstallationEntry {\n  const inProgressAuthToken: InProgressAuthToken = {\n    requestStatus: RequestStatus.IN_PROGRESS,\n    requestTime: Date.now()\n  };\n  return {\n    ...oldEntry,\n    authToken: inProgressAuthToken\n  };\n}\n\nfunction hasAuthTokenRequestTimedOut(authToken: AuthToken): boolean {\n  return (\n    authToken.requestStatus === RequestStatus.IN_PROGRESS &&\n    authToken.requestTime + PENDING_TIMEOUT_MS < Date.now()\n  );\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Creates a Firebase Installation if there isn't one for the app and\n * returns the Installation ID.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function getId(installations: Installations): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  const { installationEntry, registrationPromise } = await getInstallationEntry(\n    installationsImpl\n  );\n\n  if (registrationPromise) {\n    registrationPromise.catch(console.error);\n  } else {\n    // If the installation is already registered, update the authentication\n    // token if needed.\n    refreshAuthToken(installationsImpl).catch(console.error);\n  }\n\n  return installationEntry.fid;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { getInstallationEntry } from '../helpers/get-installation-entry';\nimport { refreshAuthToken } from '../helpers/refresh-auth-token';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns a Firebase Installations auth token, identifying the current\n * Firebase Installation.\n * @param installations - The `Installations` instance.\n * @param forceRefresh - Force refresh regardless of token expiration.\n *\n * @public\n */\nexport async function getToken(\n  installations: Installations,\n  forceRefresh = false\n): Promise<string> {\n  const installationsImpl = installations as FirebaseInstallationsImpl;\n  await completeInstallationRegistration(installationsImpl);\n\n  // At this point we either have a Registered Installation in the DB, or we've\n  // already thrown an error.\n  const authToken = await refreshAuthToken(installationsImpl, forceRefresh);\n  return authToken.token;\n}\n\nasync function completeInstallationRegistration(\n  installations: FirebaseInstallationsImpl\n): Promise<void> {\n  const { registrationPromise } = await getInstallationEntry(installations);\n\n  if (registrationPromise) {\n    // A createInstallation request is in progress. Wait until it finishes.\n    await registrationPromise;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { RegisteredInstallationEntry } from '../interfaces/installation-entry';\nimport {\n  getErrorFromResponse,\n  getHeadersWithAuth,\n  getInstallationsEndpoint,\n  retryIfServerError\n} from './common';\n\nexport async function deleteInstallationRequest(\n  appConfig: AppConfig,\n  installationEntry: RegisteredInstallationEntry\n): Promise<void> {\n  const endpoint = getDeleteEndpoint(appConfig, installationEntry);\n\n  const headers = getHeadersWithAuth(appConfig, installationEntry);\n  const request: RequestInit = {\n    method: 'DELETE',\n    headers\n  };\n\n  const response = await retryIfServerError(() => fetch(endpoint, request));\n  if (!response.ok) {\n    throw await getErrorFromResponse('Delete Installation', response);\n  }\n}\n\nfunction getDeleteEndpoint(\n  appConfig: AppConfig,\n  { fid }: RegisteredInstallationEntry\n): string {\n  return `${getInstallationsEndpoint(appConfig)}/${fid}`;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { deleteInstallationRequest } from '../functions/delete-installation-request';\nimport { remove, update } from '../helpers/idb-manager';\nimport { RequestStatus } from '../interfaces/installation-entry';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Deletes the Firebase Installation and all associated data.\n * @param installations - The `Installations` instance.\n *\n * @public\n */\nexport async function deleteInstallations(\n  installations: Installations\n): Promise<void> {\n  const { appConfig } = installations as FirebaseInstallationsImpl;\n\n  const entry = await update(appConfig, oldEntry => {\n    if (oldEntry && oldEntry.registrationStatus === RequestStatus.NOT_STARTED) {\n      // Delete the unregistered entry without sending a deleteInstallation request.\n      return undefined;\n    }\n    return oldEntry;\n  });\n\n  if (entry) {\n    if (entry.registrationStatus === RequestStatus.IN_PROGRESS) {\n      // Can't delete while trying to register.\n      throw ERROR_FACTORY.create(ErrorCode.DELETE_PENDING_REGISTRATION);\n    } else if (entry.registrationStatus === RequestStatus.COMPLETED) {\n      if (!navigator.onLine) {\n        throw ERROR_FACTORY.create(ErrorCode.APP_OFFLINE);\n      } else {\n        await deleteInstallationRequest(appConfig, entry);\n        await remove(appConfig);\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { addCallback, removeCallback } from '../helpers/fid-changed';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * An user defined callback function that gets called when Installations ID changes.\n *\n * @public\n */\nexport type IdChangeCallbackFn = (installationId: string) => void;\n/**\n * Unsubscribe a callback function previously added via {@link IdChangeCallbackFn}.\n *\n * @public\n */\nexport type IdChangeUnsubscribeFn = () => void;\n\n/**\n * Sets a new callback that will get called when Installation ID changes.\n * Returns an unsubscribe function that will remove the callback when called.\n * @param installations - The `Installations` instance.\n * @param callback - The callback function that is invoked when FID changes.\n * @returns A function that can be called to unsubscribe.\n *\n * @public\n */\nexport function onIdChange(\n  installations: Installations,\n  callback: IdChangeCallbackFn\n): IdChangeUnsubscribeFn {\n  const { appConfig } = installations as FirebaseInstallationsImpl;\n\n  addCallback(appConfig, callback);\n  return () => {\n    removeCallback(appConfig, callback);\n  };\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, getApp, _getProvider } from '@firebase/app';\nimport { Installations } from '../interfaces/public-types';\n\n/**\n * Returns an instance of {@link Installations} associated with the given\n * {@link @firebase/app#FirebaseApp} instance.\n * @param app - The {@link @firebase/app#FirebaseApp} instance.\n *\n * @public\n */\nexport function getInstallations(app: FirebaseApp = getApp()): Installations {\n  const installationsImpl = _getProvider(app, 'installations').getImmediate();\n  return installationsImpl;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, FirebaseOptions } from '@firebase/app';\nimport { FirebaseError } from '@firebase/util';\nimport { AppConfig } from '../interfaces/installation-impl';\nimport { ERROR_FACTORY, ErrorCode } from '../util/errors';\n\nexport function extractAppConfig(app: FirebaseApp): AppConfig {\n  if (!app || !app.options) {\n    throw getMissingValueError('App Configuration');\n  }\n\n  if (!app.name) {\n    throw getMissingValueError('App Name');\n  }\n\n  // Required app config keys\n  const configKeys: Array<keyof FirebaseOptions> = [\n    'projectId',\n    'apiKey',\n    'appId'\n  ];\n\n  for (const keyName of configKeys) {\n    if (!app.options[keyName]) {\n      throw getMissingValueError(keyName);\n    }\n  }\n\n  return {\n    appName: app.name,\n    projectId: app.options.projectId!,\n    apiKey: app.options.apiKey!,\n    appId: app.options.appId!\n  };\n}\n\nfunction getMissingValueError(valueName: string): FirebaseError {\n  return ERROR_FACTORY.create(ErrorCode.MISSING_APP_CONFIG_VALUES, {\n    valueName\n  });\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, _getProvider } from '@firebase/app';\nimport {\n  Component,\n  ComponentType,\n  InstanceFactory,\n  ComponentContainer\n} from '@firebase/component';\nimport { getId, getToken } from '../api/index';\nimport { _FirebaseInstallationsInternal } from '../interfaces/public-types';\nimport { FirebaseInstallationsImpl } from '../interfaces/installation-impl';\nimport { extractAppConfig } from '../helpers/extract-app-config';\n\nconst INSTALLATIONS_NAME = 'installations';\nconst INSTALLATIONS_NAME_INTERNAL = 'installations-internal';\n\nconst publicFactory: InstanceFactory<'installations'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Throws if app isn't configured properly.\n  const appConfig = extractAppConfig(app);\n  const heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n\n  const installationsImpl: FirebaseInstallationsImpl = {\n    app,\n    appConfig,\n    heartbeatServiceProvider,\n    _delete: () => Promise.resolve()\n  };\n  return installationsImpl;\n};\n\nconst internalFactory: InstanceFactory<'installations-internal'> = (\n  container: ComponentContainer\n) => {\n  const app = container.getProvider('app').getImmediate();\n  // Internal FIS instance relies on public FIS instance.\n  const installations = _getProvider(app, INSTALLATIONS_NAME).getImmediate();\n\n  const installationsInternal: _FirebaseInstallationsInternal = {\n    getId: () => getId(installations),\n    getToken: (forceRefresh?: boolean) => getToken(installations, forceRefresh)\n  };\n  return installationsInternal;\n};\n\nexport function registerInstallations(): void {\n  _registerComponent(\n    new Component(INSTALLATIONS_NAME, publicFactory, ComponentType.PUBLIC)\n  );\n  _registerComponent(\n    new Component(\n      INSTALLATIONS_NAME_INTERNAL,\n      internalFactory,\n      ComponentType.PRIVATE\n    )\n  );\n}\n", "/**\n * The Firebase Installations Web SDK.\n * This SDK does not work in a Node.js environment.\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { registerInstallations } from './functions/config';\nimport { registerVersion } from '@firebase/app';\nimport { name, version } from '../package.json';\n\nexport * from './api';\nexport * from './interfaces/public-types';\n\nregisterInstallations();\nregisterVersion(name, version);\n// BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\nregisterVersion(name, version, '__BUILD_TARGET__');\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;;;;;;AAeG;AAII,MAAM,kBAAkB,GAAG,KAAK,CAAC;AAEjC,MAAM,eAAe,GAAG,CAAK,EAAA,EAAA,OAAO,EAAE,CAAC;AACvC,MAAM,qBAAqB,GAAG,QAAQ,CAAC;AAEvC,MAAM,qBAAqB,GAChC,iDAAiD,CAAC;AAE7C,MAAM,uBAAuB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;AAE/C,MAAM,OAAO,GAAG,eAAe,CAAC;AAChC,MAAM,YAAY,GAAG,eAAe;;AC9B3C;;;;;;;;;;;;;;;AAeG;AAcH,MAAM,qBAAqB,GAA4C;AACrE,IAAA,CAAA,2BAAA,6CACE,iDAAiD;AACnD,IAAA,CAAA,gBAAA,kCAA4B,0CAA0C;AACtE,IAAA,CAAA,wBAAA,0CAAoC,kCAAkC;AACtE,IAAA,CAAA,gBAAA,kCACE,4FAA4F;AAC9F,IAAA,CAAA,aAAA,+BAAyB,iDAAiD;AAC1E,IAAA,CAAA,6BAAA,+CACE,0EAA0E;CAC7E,CAAC;AAYK,MAAM,aAAa,GAAG,IAAI,YAAY,CAC3C,OAAO,EACP,YAAY,EACZ,qBAAqB,CACtB,CAAC;AAUF;AACM,SAAU,aAAa,CAAC,KAAc,EAAA;IAC1C,QACE,KAAK,YAAY,aAAa;AAC9B,QAAA,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAA,gBAAA,gCAA0B,EAC7C;AACJ;;ACvEA;;;;;;;;;;;;;;;AAeG;AAgBa,SAAA,wBAAwB,CAAC,EAAE,SAAS,EAAa,EAAA;AAC/D,IAAA,OAAO,CAAG,EAAA,qBAAqB,CAAa,UAAA,EAAA,SAAS,gBAAgB,CAAC;AACxE,CAAC;AAEK,SAAU,gCAAgC,CAC9C,QAAmC,EAAA;IAEnC,OAAO;QACL,KAAK,EAAE,QAAQ,CAAC,KAAK;AACrB,QAAA,aAAa,EAAyB,CAAA;AACtC,QAAA,SAAS,EAAE,iCAAiC,CAAC,QAAQ,CAAC,SAAS,CAAC;AAChE,QAAA,YAAY,EAAE,IAAI,CAAC,GAAG,EAAE;KACzB,CAAC;AACJ,CAAC;AAEM,eAAe,oBAAoB,CACxC,WAAmB,EACnB,QAAkB,EAAA;AAElB,IAAA,MAAM,YAAY,GAAkB,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AAC1D,IAAA,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC;IACrC,OAAO,aAAa,CAAC,MAAM,CAA2B,gBAAA,iCAAA;QACpD,WAAW;QACX,UAAU,EAAE,SAAS,CAAC,IAAI;QAC1B,aAAa,EAAE,SAAS,CAAC,OAAO;QAChC,YAAY,EAAE,SAAS,CAAC,MAAM;AAC/B,KAAA,CAAC,CAAC;AACL,CAAC;AAEe,SAAA,UAAU,CAAC,EAAE,MAAM,EAAa,EAAA;IAC9C,OAAO,IAAI,OAAO,CAAC;AACjB,QAAA,cAAc,EAAE,kBAAkB;AAClC,QAAA,MAAM,EAAE,kBAAkB;AAC1B,QAAA,gBAAgB,EAAE,MAAM;AACzB,KAAA,CAAC,CAAC;AACL,CAAC;SAEe,kBAAkB,CAChC,SAAoB,EACpB,EAAE,YAAY,EAA+B,EAAA;AAE7C,IAAA,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;IACtC,OAAO,CAAC,MAAM,CAAC,eAAe,EAAE,sBAAsB,CAAC,YAAY,CAAC,CAAC,CAAC;AACtE,IAAA,OAAO,OAAO,CAAC;AACjB,CAAC;AAUD;;;;AAIG;AACI,eAAe,kBAAkB,CACtC,EAA2B,EAAA;AAE3B,IAAA,MAAM,MAAM,GAAG,MAAM,EAAE,EAAE,CAAC;AAE1B,IAAA,IAAI,MAAM,CAAC,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE;;QAE/C,OAAO,EAAE,EAAE,CAAC;KACb;AAED,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,iCAAiC,CAAC,iBAAyB,EAAA;;IAElE,OAAO,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AACvD,CAAC;AAED,SAAS,sBAAsB,CAAC,YAAoB,EAAA;AAClD,IAAA,OAAO,CAAG,EAAA,qBAAqB,CAAI,CAAA,EAAA,YAAY,EAAE,CAAC;AACpD;;AC9GA;;;;;;;;;;;;;;;AAeG;AAkBI,eAAe,yBAAyB,CAC7C,EAAE,SAAS,EAAE,wBAAwB,EAA6B,EAClE,EAAE,GAAG,EAA+B,EAAA;AAEpC,IAAA,MAAM,QAAQ,GAAG,wBAAwB,CAAC,SAAS,CAAC,CAAC;AAErD,IAAA,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC;;AAGtC,IAAA,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,YAAY,CAAC;AAC7D,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC,CAAC;IACH,IAAI,gBAAgB,EAAE;AACpB,QAAA,MAAM,gBAAgB,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,EAAE,CAAC;QACtE,IAAI,gBAAgB,EAAE;AACpB,YAAA,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;SACvD;KACF;AAED,IAAA,MAAM,IAAI,GAAG;QACX,GAAG;AACH,QAAA,WAAW,EAAE,qBAAqB;QAClC,KAAK,EAAE,SAAS,CAAC,KAAK;AACtB,QAAA,UAAU,EAAE,eAAe;KAC5B,CAAC;AAEF,IAAA,MAAM,OAAO,GAAgB;AAC3B,QAAA,MAAM,EAAE,MAAM;QACd,OAAO;AACP,QAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC3B,CAAC;AAEF,IAAA,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,MAAM,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AAC1E,IAAA,IAAI,QAAQ,CAAC,EAAE,EAAE;AACf,QAAA,MAAM,aAAa,GAA+B,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACxE,QAAA,MAAM,2BAA2B,GAAgC;AAC/D,YAAA,GAAG,EAAE,aAAa,CAAC,GAAG,IAAI,GAAG;AAC7B,YAAA,kBAAkB,EAAyB,CAAA;YAC3C,YAAY,EAAE,aAAa,CAAC,YAAY;AACxC,YAAA,SAAS,EAAE,gCAAgC,CAAC,aAAa,CAAC,SAAS,CAAC;SACrE,CAAC;AACF,QAAA,OAAO,2BAA2B,CAAC;KACpC;SAAM;AACL,QAAA,MAAM,MAAM,oBAAoB,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;KACnE;AACH;;AC9EA;;;;;;;;;;;;;;;AAeG;AAEH;AACM,SAAU,KAAK,CAAC,EAAU,EAAA;AAC9B,IAAA,OAAO,IAAI,OAAO,CAAO,OAAO,IAAG;AACjC,QAAA,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAC1B,KAAC,CAAC,CAAC;AACL;;ACtBA;;;;;;;;;;;;;;;AAeG;AAEG,SAAU,qBAAqB,CAAC,KAAiB,EAAA;AACrD,IAAA,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAChD,IAAA,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACrD;;ACpBA;;;;;;;;;;;;;;;AAeG;AAII,MAAM,iBAAiB,GAAG,mBAAmB,CAAC;AAC9C,MAAM,WAAW,GAAG,EAAE,CAAC;AAE9B;;;AAGG;SACa,WAAW,GAAA;AACzB,IAAA,IAAI;;;AAGF,QAAA,MAAM,YAAY,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QACxC,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,IAAK,IAAwC,CAAC,QAAQ,CAAC;AACpE,QAAA,MAAM,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;;AAGrC,QAAA,YAAY,CAAC,CAAC,CAAC,GAAG,UAAU,IAAI,YAAY,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AAE9D,QAAA,MAAM,GAAG,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC;AAEjC,QAAA,OAAO,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,WAAW,CAAC;KACxD;AAAC,IAAA,OAAA,EAAA,EAAM;;AAEN,QAAA,OAAO,WAAW,CAAC;KACpB;AACH,CAAC;AAED;AACA,SAAS,MAAM,CAAC,YAAwB,EAAA;AACtC,IAAA,MAAM,SAAS,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;;;IAItD,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACjC;;ACtDA;;;;;;;;;;;;;;;AAeG;AAIH;AACM,SAAU,MAAM,CAAC,SAAoB,EAAA;IACzC,OAAO,CAAA,EAAG,SAAS,CAAC,OAAO,IAAI,SAAS,CAAC,KAAK,CAAA,CAAE,CAAC;AACnD;;ACtBA;;;;;;;;;;;;;;;AAeG;AAMH,MAAM,kBAAkB,GAAyC,IAAI,GAAG,EAAE,CAAC;AAE3E;;;AAGG;AACa,SAAA,UAAU,CAAC,SAAoB,EAAE,GAAW,EAAA;AAC1D,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAE9B,IAAA,sBAAsB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACjC,IAAA,kBAAkB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AAC/B,CAAC;AAEe,SAAA,WAAW,CACzB,SAAoB,EACpB,QAA4B,EAAA;;;AAI5B,IAAA,mBAAmB,EAAE,CAAC;AAEtB,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IAE9B,IAAI,WAAW,GAAG,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9C,IAAI,CAAC,WAAW,EAAE;AAChB,QAAA,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;AACxB,QAAA,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;KAC1C;AACD,IAAA,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC5B,CAAC;AAEe,SAAA,cAAc,CAC5B,SAAoB,EACpB,QAA4B,EAAA;AAE5B,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IAE9B,MAAM,WAAW,GAAG,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAEhD,IAAI,CAAC,WAAW,EAAE;QAChB,OAAO;KACR;AAED,IAAA,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAC7B,IAAA,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC,EAAE;AAC1B,QAAA,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KAChC;;AAGD,IAAA,qBAAqB,EAAE,CAAC;AAC1B,CAAC;AAED,SAAS,sBAAsB,CAAC,GAAW,EAAE,GAAW,EAAA;IACtD,MAAM,SAAS,GAAG,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC9C,IAAI,CAAC,SAAS,EAAE;QACd,OAAO;KACR;AAED,IAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;QAChC,QAAQ,CAAC,GAAG,CAAC,CAAC;KACf;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW,EAAE,GAAW,EAAA;AAClD,IAAA,MAAM,OAAO,GAAG,mBAAmB,EAAE,CAAC;IACtC,IAAI,OAAO,EAAE;QACX,OAAO,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;KACnC;AACD,IAAA,qBAAqB,EAAE,CAAC;AAC1B,CAAC;AAED,IAAI,gBAAgB,GAA4B,IAAI,CAAC;AACrD;AACA,SAAS,mBAAmB,GAAA;AAC1B,IAAA,IAAI,CAAC,gBAAgB,IAAI,kBAAkB,IAAI,IAAI,EAAE;AACnD,QAAA,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;AACjE,QAAA,gBAAgB,CAAC,SAAS,GAAG,CAAC,IAAG;AAC/B,YAAA,sBAAsB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACjD,SAAC,CAAC;KACH;AACD,IAAA,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED,SAAS,qBAAqB,GAAA;IAC5B,IAAI,kBAAkB,CAAC,IAAI,KAAK,CAAC,IAAI,gBAAgB,EAAE;QACrD,gBAAgB,CAAC,KAAK,EAAE,CAAC;QACzB,gBAAgB,GAAG,IAAI,CAAC;KACzB;AACH;;AC7GA;;;;;;;;;;;;;;;AAeG;AAQH,MAAM,aAAa,GAAG,iCAAiC,CAAC;AACxD,MAAM,gBAAgB,GAAG,CAAC,CAAC;AAC3B,MAAM,iBAAiB,GAAG,8BAA8B,CAAC;AASzD,IAAI,SAAS,GAAkD,IAAI,CAAC;AACpE,SAAS,YAAY,GAAA;IACnB,IAAI,CAAC,SAAS,EAAE;AACd,QAAA,SAAS,GAAG,MAAM,CAAC,aAAa,EAAE,gBAAgB,EAAE;AAClD,YAAA,OAAO,EAAE,CAAC,EAAE,EAAE,UAAU,KAAI;;;;;;gBAM1B,QAAQ,UAAU;AAChB,oBAAA,KAAK,CAAC;AACJ,wBAAA,EAAE,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;iBAC3C;aACF;AACF,SAAA,CAAC,CAAC;KACJ;AACD,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAcD;AACO,eAAe,GAAG,CACvB,SAAoB,EACpB,KAAgB,EAAA;AAEhB,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAC9B,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;IAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;IAC1D,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;IACtD,MAAM,QAAQ,IAAI,MAAM,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAsB,CAAC;IACnE,MAAM,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,MAAM,EAAE,CAAC,IAAI,CAAC;IAEd,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,EAAE;AAC3C,QAAA,UAAU,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;KAClC;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;AACO,eAAe,MAAM,CAAC,SAAoB,EAAA;AAC/C,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAC9B,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;IAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;IAC1D,MAAM,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACpD,MAAM,EAAE,CAAC,IAAI,CAAC;AAChB,CAAC;AAED;;;;;AAKG;AACI,eAAe,MAAM,CAC1B,SAAoB,EACpB,QAAqE,EAAA;AAErE,IAAA,MAAM,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;AAC9B,IAAA,MAAM,EAAE,GAAG,MAAM,YAAY,EAAE,CAAC;IAChC,MAAM,EAAE,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;IAC1D,MAAM,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;IAChD,MAAM,QAAQ,IAAmC,MAAM,KAAK,CAAC,GAAG,CAC9D,GAAG,CACJ,CAAsB,CAAC;AACxB,IAAA,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAEpC,IAAA,IAAI,QAAQ,KAAK,SAAS,EAAE;AAC1B,QAAA,MAAM,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;KACzB;SAAM;QACL,MAAM,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;KAChC;IACD,MAAM,EAAE,CAAC,IAAI,CAAC;AAEd,IAAA,IAAI,QAAQ,KAAK,CAAC,QAAQ,IAAI,QAAQ,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC5D,QAAA,UAAU,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;KACrC;AAED,IAAA,OAAO,QAAQ,CAAC;AAClB;;AC9HA;;;;;;;;;;;;;;;AAeG;AAyBH;;;AAGG;AACI,eAAe,oBAAoB,CACxC,aAAwC,EAAA;AAExC,IAAA,IAAI,mBAAqE,CAAC;IAE1E,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAG;AACzE,QAAA,MAAM,iBAAiB,GAAG,+BAA+B,CAAC,QAAQ,CAAC,CAAC;QACpE,MAAM,gBAAgB,GAAG,8BAA8B,CACrD,aAAa,EACb,iBAAiB,CAClB,CAAC;AACF,QAAA,mBAAmB,GAAG,gBAAgB,CAAC,mBAAmB,CAAC;QAC3D,OAAO,gBAAgB,CAAC,iBAAiB,CAAC;AAC5C,KAAC,CAAC,CAAC;AAEH,IAAA,IAAI,iBAAiB,CAAC,GAAG,KAAK,WAAW,EAAE;;AAEzC,QAAA,OAAO,EAAE,iBAAiB,EAAE,MAAM,mBAAoB,EAAE,CAAC;KAC1D;IAED,OAAO;QACL,iBAAiB;QACjB,mBAAmB;KACpB,CAAC;AACJ,CAAC;AAED;;;AAGG;AACH,SAAS,+BAA+B,CACtC,QAAuC,EAAA;IAEvC,MAAM,KAAK,GAAsB,QAAQ,IAAI;QAC3C,GAAG,EAAE,WAAW,EAAE;AAClB,QAAA,kBAAkB,EAA2B,CAAA;KAC9C,CAAC;AAEF,IAAA,OAAO,oBAAoB,CAAC,KAAK,CAAC,CAAC;AACrC,CAAC;AAED;;;;;;AAMG;AACH,SAAS,8BAA8B,CACrC,aAAwC,EACxC,iBAAoC,EAAA;AAEpC,IAAA,IAAI,iBAAiB,CAAC,kBAAkB,KAAA,CAAA,kCAAgC;AACtE,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;;YAErB,MAAM,4BAA4B,GAAG,OAAO,CAAC,MAAM,CACjD,aAAa,CAAC,MAAM,CAAuB,aAAA,6BAAA,CAC5C,CAAC;YACF,OAAO;gBACL,iBAAiB;AACjB,gBAAA,mBAAmB,EAAE,4BAA4B;aAClD,CAAC;SACH;;AAGD,QAAA,MAAM,eAAe,GAAgC;YACnD,GAAG,EAAE,iBAAiB,CAAC,GAAG;AAC1B,YAAA,kBAAkB,EAA2B,CAAA;AAC7C,YAAA,gBAAgB,EAAE,IAAI,CAAC,GAAG,EAAE;SAC7B,CAAC;QACF,MAAM,mBAAmB,GAAG,oBAAoB,CAC9C,aAAa,EACb,eAAe,CAChB,CAAC;AACF,QAAA,OAAO,EAAE,iBAAiB,EAAE,eAAe,EAAE,mBAAmB,EAAE,CAAC;KACpE;AAAM,SAAA,IACL,iBAAiB,CAAC,kBAAkB,KAAA,CAAA,kCACpC;QACA,OAAO;YACL,iBAAiB;AACjB,YAAA,mBAAmB,EAAE,wBAAwB,CAAC,aAAa,CAAC;SAC7D,CAAC;KACH;SAAM;QACL,OAAO,EAAE,iBAAiB,EAAE,CAAC;KAC9B;AACH,CAAC;AAED;AACA,eAAe,oBAAoB,CACjC,aAAwC,EACxC,iBAA8C,EAAA;AAE9C,IAAA,IAAI;QACF,MAAM,2BAA2B,GAAG,MAAM,yBAAyB,CACjE,aAAa,EACb,iBAAiB,CAClB,CAAC;QACF,OAAO,GAAG,CAAC,aAAa,CAAC,SAAS,EAAE,2BAA2B,CAAC,CAAC;KAClE;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,KAAK,GAAG,EAAE;;;AAGvD,YAAA,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;SACvC;aAAM;;AAEL,YAAA,MAAM,GAAG,CAAC,aAAa,CAAC,SAAS,EAAE;gBACjC,GAAG,EAAE,iBAAiB,CAAC,GAAG;AAC1B,gBAAA,kBAAkB,EAA2B,CAAA;AAC9C,aAAA,CAAC,CAAC;SACJ;AACD,QAAA,MAAM,CAAC,CAAC;KACT;AACH,CAAC;AAED;AACA,eAAe,wBAAwB,CACrC,aAAwC,EAAA;;;;IAMxC,IAAI,KAAK,GAAsB,MAAM,yBAAyB,CAC5D,aAAa,CAAC,SAAS,CACxB,CAAC;AACF,IAAA,OAAO,KAAK,CAAC,kBAAkB,KAAA,CAAA,kCAAgC;;AAE7D,QAAA,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;QAEjB,KAAK,GAAG,MAAM,yBAAyB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;KAClE;AAED,IAAA,IAAI,KAAK,CAAC,kBAAkB,KAAA,CAAA,kCAAgC;;QAE1D,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,GAC9C,MAAM,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAE5C,IAAI,mBAAmB,EAAE;AACvB,YAAA,OAAO,mBAAmB,CAAC;SAC5B;aAAM;;AAEL,YAAA,OAAO,iBAAgD,CAAC;SACzD;KACF;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;AAOG;AACH,SAAS,yBAAyB,CAChC,SAAoB,EAAA;AAEpB,IAAA,OAAO,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAG;QAClC,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,wBAAA,wCAAkC,CAAC;SAC9D;AACD,QAAA,OAAO,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AACxC,KAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,oBAAoB,CAAC,KAAwB,EAAA;AACpD,IAAA,IAAI,8BAA8B,CAAC,KAAK,CAAC,EAAE;QACzC,OAAO;YACL,GAAG,EAAE,KAAK,CAAC,GAAG;AACd,YAAA,kBAAkB,EAA2B,CAAA;SAC9C,CAAC;KACH;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,8BAA8B,CACrC,iBAAoC,EAAA;AAEpC,IAAA,QACE,iBAAiB,CAAC,kBAAkB,KAA8B,CAAA;QAClE,iBAAiB,CAAC,gBAAgB,GAAG,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,EACpE;AACJ;;ACrOA;;;;;;;;;;;;;;;AAeG;AAoBI,eAAe,wBAAwB,CAC5C,EAAE,SAAS,EAAE,wBAAwB,EAA6B,EAClE,iBAA8C,EAAA;IAE9C,MAAM,QAAQ,GAAG,4BAA4B,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IAE5E,MAAM,OAAO,GAAG,kBAAkB,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;;AAGjE,IAAA,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,YAAY,CAAC;AAC7D,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC,CAAC;IACH,IAAI,gBAAgB,EAAE;AACpB,QAAA,MAAM,gBAAgB,GAAG,MAAM,gBAAgB,CAAC,mBAAmB,EAAE,CAAC;QACtE,IAAI,gBAAgB,EAAE;AACpB,YAAA,OAAO,CAAC,MAAM,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,CAAC;SACvD;KACF;AAED,IAAA,MAAM,IAAI,GAAG;AACX,QAAA,YAAY,EAAE;AACZ,YAAA,UAAU,EAAE,eAAe;YAC3B,KAAK,EAAE,SAAS,CAAC,KAAK;AACvB,SAAA;KACF,CAAC;AAEF,IAAA,MAAM,OAAO,GAAgB;AAC3B,QAAA,MAAM,EAAE,MAAM;QACd,OAAO;AACP,QAAA,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;KAC3B,CAAC;AAEF,IAAA,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,MAAM,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AAC1E,IAAA,IAAI,QAAQ,CAAC,EAAE,EAAE;AACf,QAAA,MAAM,aAAa,GAA8B,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACvE,QAAA,MAAM,kBAAkB,GACtB,gCAAgC,CAAC,aAAa,CAAC,CAAC;AAClD,QAAA,OAAO,kBAAkB,CAAC;KAC3B;SAAM;AACL,QAAA,MAAM,MAAM,oBAAoB,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;KACnE;AACH,CAAC;AAED,SAAS,4BAA4B,CACnC,SAAoB,EACpB,EAAE,GAAG,EAA+B,EAAA;IAEpC,OAAO,CAAA,EAAG,wBAAwB,CAAC,SAAS,CAAC,CAAI,CAAA,EAAA,GAAG,sBAAsB,CAAC;AAC7E;;ACnFA;;;;;;;;;;;;;;;AAeG;AAoBH;;;;;AAKG;AACI,eAAe,gBAAgB,CACpC,aAAwC,EACxC,YAAY,GAAG,KAAK,EAAA;AAEpB,IAAA,IAAI,YAAqD,CAAC;IAC1D,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,IAAG;AAC7D,QAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE;AAChC,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,gBAAA,gCAA0B,CAAC;SACtD;AAED,QAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC;QACxC,IAAI,CAAC,YAAY,IAAI,gBAAgB,CAAC,YAAY,CAAC,EAAE;;AAEnD,YAAA,OAAO,QAAQ,CAAC;SACjB;AAAM,aAAA,IAAI,YAAY,CAAC,aAAa,KAAA,CAAA,kCAAgC;;AAEnE,YAAA,YAAY,GAAG,yBAAyB,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;AACtE,YAAA,OAAO,QAAQ,CAAC;SACjB;aAAM;;AAEL,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACrB,gBAAA,MAAM,aAAa,CAAC,MAAM,CAAA,aAAA,6BAAuB,CAAC;aACnD;AAED,YAAA,MAAM,eAAe,GAAG,mCAAmC,CAAC,QAAQ,CAAC,CAAC;AACtE,YAAA,YAAY,GAAG,wBAAwB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC;AACxE,YAAA,OAAO,eAAe,CAAC;SACxB;AACH,KAAC,CAAC,CAAC;IAEH,MAAM,SAAS,GAAG,YAAY;UAC1B,MAAM,YAAY;AACpB,UAAG,KAAK,CAAC,SAAgC,CAAC;AAC5C,IAAA,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;;;AAKG;AACH,eAAe,yBAAyB,CACtC,aAAwC,EACxC,YAAqB,EAAA;;;;IAMrB,IAAI,KAAK,GAAG,MAAM,sBAAsB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAClE,IAAA,OAAO,KAAK,CAAC,SAAS,CAAC,aAAa,KAAA,CAAA,kCAAgC;;AAElE,QAAA,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC;QAEjB,KAAK,GAAG,MAAM,sBAAsB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;KAC/D;AAED,IAAA,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;AAClC,IAAA,IAAI,SAAS,CAAC,aAAa,KAAA,CAAA,kCAAgC;;AAEzD,QAAA,OAAO,gBAAgB,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;KACtD;SAAM;AACL,QAAA,OAAO,SAAS,CAAC;KAClB;AACH,CAAC;AAED;;;;;;;AAOG;AACH,SAAS,sBAAsB,CAC7B,SAAoB,EAAA;AAEpB,IAAA,OAAO,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAG;AAClC,QAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE;AAChC,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,gBAAA,gCAA0B,CAAC;SACtD;AAED,QAAA,MAAM,YAAY,GAAG,QAAQ,CAAC,SAAS,CAAC;AACxC,QAAA,IAAI,2BAA2B,CAAC,YAAY,CAAC,EAAE;YAC7C,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,QAAQ,KACX,SAAS,EAAE,EAAE,aAAa,EAAA,CAAA,kCAA6B,EACvD,CAAA,CAAA;SACH;AAED,QAAA,OAAO,QAAQ,CAAC;AAClB,KAAC,CAAC,CAAC;AACL,CAAC;AAED,eAAe,wBAAwB,CACrC,aAAwC,EACxC,iBAA8C,EAAA;AAE9C,IAAA,IAAI;QACF,MAAM,SAAS,GAAG,MAAM,wBAAwB,CAC9C,aAAa,EACb,iBAAiB,CAClB,CAAC;AACF,QAAA,MAAM,wBAAwB,GACzB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,iBAAiB,CACpB,EAAA,EAAA,SAAS,GACV,CAAC;QACF,MAAM,GAAG,CAAC,aAAa,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;AAC7D,QAAA,OAAO,SAAS,CAAC;KAClB;IAAC,OAAO,CAAC,EAAE;QACV,IACE,aAAa,CAAC,CAAC,CAAC;AAChB,aAAC,CAAC,CAAC,UAAU,CAAC,UAAU,KAAK,GAAG,IAAI,CAAC,CAAC,UAAU,CAAC,UAAU,KAAK,GAAG,CAAC,EACpE;;;AAGA,YAAA,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;SACvC;aAAM;YACL,MAAM,wBAAwB,GACzB,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,iBAAiB,CACpB,EAAA,EAAA,SAAS,EAAE,EAAE,aAAa,EAAA,CAAA,kCAA6B,EAAA,CACxD,CAAC;YACF,MAAM,GAAG,CAAC,aAAa,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;SAC9D;AACD,QAAA,MAAM,CAAC,CAAC;KACT;AACH,CAAC;AAED,SAAS,iBAAiB,CACxB,iBAAgD,EAAA;IAEhD,QACE,iBAAiB,KAAK,SAAS;AAC/B,QAAA,iBAAiB,CAAC,kBAAkB,KAA4B,CAAA,gCAChE;AACJ,CAAC;AAED,SAAS,gBAAgB,CAAC,SAAoB,EAAA;AAC5C,IAAA,QACE,SAAS,CAAC,aAAa,KAA4B,CAAA;AACnD,QAAA,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAC9B;AACJ,CAAC;AAED,SAAS,kBAAkB,CAAC,SAA6B,EAAA;AACvD,IAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;AACvB,IAAA,QACE,GAAG,GAAG,SAAS,CAAC,YAAY;QAC5B,SAAS,CAAC,YAAY,GAAG,SAAS,CAAC,SAAS,GAAG,GAAG,GAAG,uBAAuB,EAC5E;AACJ,CAAC;AAED;AACA,SAAS,mCAAmC,CAC1C,QAAqC,EAAA;AAErC,IAAA,MAAM,mBAAmB,GAAwB;AAC/C,QAAA,aAAa,EAA2B,CAAA;AACxC,QAAA,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;KACxB,CAAC;AACF,IAAA,OAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EACK,QAAQ,CAAA,EAAA,EACX,SAAS,EAAE,mBAAmB,EAC9B,CAAA,CAAA;AACJ,CAAC;AAED,SAAS,2BAA2B,CAAC,SAAoB,EAAA;AACvD,IAAA,QACE,SAAS,CAAC,aAAa,KAA8B,CAAA;QACrD,SAAS,CAAC,WAAW,GAAG,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,EACvD;AACJ;;ACrNA;;;;;;;;;;;;;;;AAeG;AAOH;;;;;;AAMG;AACI,eAAe,KAAK,CAAC,aAA4B,EAAA;IACtD,MAAM,iBAAiB,GAAG,aAA0C,CAAC;IACrE,MAAM,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,GAAG,MAAM,oBAAoB,CAC3E,iBAAiB,CAClB,CAAC;IAEF,IAAI,mBAAmB,EAAE;AACvB,QAAA,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC1C;SAAM;;;QAGL,gBAAgB,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC1D;IAED,OAAO,iBAAiB,CAAC,GAAG,CAAC;AAC/B;;AC5CA;;;;;;;;;;;;;;;AAeG;AAOH;;;;;;;AAOG;AACI,eAAe,QAAQ,CAC5B,aAA4B,EAC5B,YAAY,GAAG,KAAK,EAAA;IAEpB,MAAM,iBAAiB,GAAG,aAA0C,CAAC;AACrE,IAAA,MAAM,gCAAgC,CAAC,iBAAiB,CAAC,CAAC;;;IAI1D,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,iBAAiB,EAAE,YAAY,CAAC,CAAC;IAC1E,OAAO,SAAS,CAAC,KAAK,CAAC;AACzB,CAAC;AAED,eAAe,gCAAgC,CAC7C,aAAwC,EAAA;IAExC,MAAM,EAAE,mBAAmB,EAAE,GAAG,MAAM,oBAAoB,CAAC,aAAa,CAAC,CAAC;IAE1E,IAAI,mBAAmB,EAAE;;AAEvB,QAAA,MAAM,mBAAmB,CAAC;KAC3B;AACH;;ACpDA;;;;;;;;;;;;;;;AAeG;AAWI,eAAe,yBAAyB,CAC7C,SAAoB,EACpB,iBAA8C,EAAA;IAE9C,MAAM,QAAQ,GAAG,iBAAiB,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;IAEjE,MAAM,OAAO,GAAG,kBAAkB,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;AACjE,IAAA,MAAM,OAAO,GAAgB;AAC3B,QAAA,MAAM,EAAE,QAAQ;QAChB,OAAO;KACR,CAAC;AAEF,IAAA,MAAM,QAAQ,GAAG,MAAM,kBAAkB,CAAC,MAAM,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;AAC1E,IAAA,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AAChB,QAAA,MAAM,MAAM,oBAAoB,CAAC,qBAAqB,EAAE,QAAQ,CAAC,CAAC;KACnE;AACH,CAAC;AAED,SAAS,iBAAiB,CACxB,SAAoB,EACpB,EAAE,GAAG,EAA+B,EAAA;IAEpC,OAAO,CAAA,EAAG,wBAAwB,CAAC,SAAS,CAAC,CAAI,CAAA,EAAA,GAAG,EAAE,CAAC;AACzD;;ACjDA;;;;;;;;;;;;;;;AAeG;AASH;;;;;AAKG;AACI,eAAe,mBAAmB,CACvC,aAA4B,EAAA;AAE5B,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,aAA0C,CAAC;IAEjE,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,EAAE,QAAQ,IAAG;AAC/C,QAAA,IAAI,QAAQ,IAAI,QAAQ,CAAC,kBAAkB,KAAA,CAAA,kCAAgC;;AAEzE,YAAA,OAAO,SAAS,CAAC;SAClB;AACD,QAAA,OAAO,QAAQ,CAAC;AAClB,KAAC,CAAC,CAAC;IAEH,IAAI,KAAK,EAAE;AACT,QAAA,IAAI,KAAK,CAAC,kBAAkB,KAAA,CAAA,kCAAgC;;AAE1D,YAAA,MAAM,aAAa,CAAC,MAAM,CAAA,6BAAA,6CAAuC,CAAC;SACnE;AAAM,aAAA,IAAI,KAAK,CAAC,kBAAkB,KAAA,CAAA,gCAA8B;AAC/D,YAAA,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACrB,gBAAA,MAAM,aAAa,CAAC,MAAM,CAAA,aAAA,6BAAuB,CAAC;aACnD;iBAAM;AACL,gBAAA,MAAM,yBAAyB,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAClD,gBAAA,MAAM,MAAM,CAAC,SAAS,CAAC,CAAC;aACzB;SACF;KACF;AACH;;ACxDA;;;;;;;;;;;;;;;AAeG;AAmBH;;;;;;;;AAQG;AACa,SAAA,UAAU,CACxB,aAA4B,EAC5B,QAA4B,EAAA;AAE5B,IAAA,MAAM,EAAE,SAAS,EAAE,GAAG,aAA0C,CAAC;AAEjE,IAAA,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACjC,IAAA,OAAO,MAAK;AACV,QAAA,cAAc,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AACtC,KAAC,CAAC;AACJ;;ACrDA;;;;;;;;;;;;;;;AAeG;AAKH;;;;;;AAMG;AACa,SAAA,gBAAgB,CAAC,GAAA,GAAmB,MAAM,EAAE,EAAA;IAC1D,MAAM,iBAAiB,GAAG,YAAY,CAAC,GAAG,EAAE,eAAe,CAAC,CAAC,YAAY,EAAE,CAAC;AAC5E,IAAA,OAAO,iBAAiB,CAAC;AAC3B;;AC9BA;;;;;;;;;;;;;;;AAeG;AAOG,SAAU,gBAAgB,CAAC,GAAgB,EAAA;IAC/C,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;AACxB,QAAA,MAAM,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;KACjD;AAED,IAAA,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;AACb,QAAA,MAAM,oBAAoB,CAAC,UAAU,CAAC,CAAC;KACxC;;AAGD,IAAA,MAAM,UAAU,GAAiC;QAC/C,WAAW;QACX,QAAQ;QACR,OAAO;KACR,CAAC;AAEF,IAAA,KAAK,MAAM,OAAO,IAAI,UAAU,EAAE;QAChC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AACzB,YAAA,MAAM,oBAAoB,CAAC,OAAO,CAAC,CAAC;SACrC;KACF;IAED,OAAO;QACL,OAAO,EAAE,GAAG,CAAC,IAAI;AACjB,QAAA,SAAS,EAAE,GAAG,CAAC,OAAO,CAAC,SAAU;AACjC,QAAA,MAAM,EAAE,GAAG,CAAC,OAAO,CAAC,MAAO;AAC3B,QAAA,KAAK,EAAE,GAAG,CAAC,OAAO,CAAC,KAAM;KAC1B,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAAC,SAAiB,EAAA;IAC7C,OAAO,aAAa,CAAC,MAAM,CAAsC,2BAAA,4CAAA;QAC/D,SAAS;AACV,KAAA,CAAC,CAAC;AACL;;ACxDA;;;;;;;;;;;;;;;AAeG;AAcH,MAAM,kBAAkB,GAAG,eAAe,CAAC;AAC3C,MAAM,2BAA2B,GAAG,wBAAwB,CAAC;AAE7D,MAAM,aAAa,GAAqC,CACtD,SAA6B,KAC3B;IACF,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;;AAExD,IAAA,MAAM,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,wBAAwB,GAAG,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;AAEhE,IAAA,MAAM,iBAAiB,GAA8B;QACnD,GAAG;QACH,SAAS;QACT,wBAAwB;AACxB,QAAA,OAAO,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE;KACjC,CAAC;AACF,IAAA,OAAO,iBAAiB,CAAC;AAC3B,CAAC,CAAC;AAEF,MAAM,eAAe,GAA8C,CACjE,SAA6B,KAC3B;IACF,MAAM,GAAG,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,YAAY,EAAE,CAAC;;IAExD,MAAM,aAAa,GAAG,YAAY,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC,YAAY,EAAE,CAAC;AAE3E,IAAA,MAAM,qBAAqB,GAAmC;AAC5D,QAAA,KAAK,EAAE,MAAM,KAAK,CAAC,aAAa,CAAC;QACjC,QAAQ,EAAE,CAAC,YAAsB,KAAK,QAAQ,CAAC,aAAa,EAAE,YAAY,CAAC;KAC5E,CAAC;AACF,IAAA,OAAO,qBAAqB,CAAC;AAC/B,CAAC,CAAC;SAEc,qBAAqB,GAAA;IACnC,kBAAkB,CAChB,IAAI,SAAS,CAAC,kBAAkB,EAAE,aAAa,EAAuB,QAAA,4BAAA,CACvE,CAAC;IACF,kBAAkB,CAChB,IAAI,SAAS,CACX,2BAA2B,EAC3B,eAAe,EAEhB,SAAA,6BAAA,CACF,CAAC;AACJ;;AC1EA;;;;;AAKG;AA0BH,qBAAqB,EAAE,CAAC;AACxB,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC/B;AACA,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,SAAkB,CAAC;;;;"}