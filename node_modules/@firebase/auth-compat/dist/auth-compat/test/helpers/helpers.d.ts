/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import '@firebase/auth-compat';
import { Provider } from '@firebase/component';
import '../..';
export declare const FAKE_HEARTBEAT_CONTROLLER_PROVIDER: Provider<"heartbeat">;
export declare const FAKE_APP_CHECK_CONTROLLER_PROVIDER: Provider<"app-check-internal">;
export declare function initializeTestInstance(): void;
export declare function cleanUpTestInstance(): Promise<void>;
export declare function randomEmail(): string;
