<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is on -->
    <style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar.Fullscreen">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
        <!-- Aggressive settings to eliminate app icon flash -->
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <!-- Prevent any preview or animation delays -->
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowAnimationStyle">@null</item>
        <!-- Force immediate display -->
        <item name="android:windowShowWallpaper">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <!-- Set background color to match splash -->
        <item name="android:colorPrimary">#6A1B9A</item>
        <item name="android:colorPrimaryDark">#4A148C</item>
        <item name="android:statusBarColor">#6A1B9A</item>
        <item name="android:navigationBarColor">#4A148C</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>
